# BUYHOO收银系统 - 外部API接口文档

本文档梳理了BUYHOO收银系统中使用的所有外部API接口，按照域名进行组织分类。

## 目录
- [域名配置](#域名配置)
- [buyhoo.cc - 主服务器](#buyhoocc---主服务器)
  - [认证与系统接口](#认证与系统接口) (2个)
  - [商品管理接口](#商品管理接口) (10个)
  - [支付处理接口](#支付处理接口) (9个)
  - [会员管理接口](#会员管理接口) (6个)
  - [订单管理接口](#订单管理接口) (7个)
  - [配送管理接口](#配送管理接口) (2个)
  - [供应商管理接口](#供应商管理接口) (1个)
  - [店铺管理接口](#店铺管理接口) (1个)
  - [系统管理接口](#系统管理接口) (1个)
  - [数据统计分析接口](#数据统计分析接口) (9个)
  - [文件下载接口](#文件下载接口) (1个)
- [face.buyhoo.cc - 人脸识别服务器](#facebuyhoocc---人脸识别服务器) (1个)
- [update.allscm.top - 升级服务器](#updateallscmtop---升级服务器) (1个)
- [platform.allscm.net - 新版升级服务器](#platformallscmnet---新版升级服务器) (2个)
- [www.baidu.com - 网络状态检测](#wwwbaiducom---网络状态检测) (1个)
- [MQTT服务器](#mqtt服务器) (1个)
- [接口统计汇总](#接口统计汇总)
- [注意事项](#注意事项)
- [文档优化建议](#文档优化建议)

---

## 快速参考

### 核心业务接口速查
| 功能 | 接口编号 | URL路径 | 说明 |
|------|---------|---------|------|
| 用户登录 | #1 | `/shopmanager/app/shop/staffLogin.do` | 账号密码登录验证 |
| 商品同步 | #3 | `/shopmanager/pc/pcGoods.do` | 从服务器同步商品数据 |
| 订单支付 | #14 | `/harricane/payOnline/v2/cashierPay.do` | 上传订单并处理支付 |
| 会员查询 | #22 | `/shopmanager/customer/queryShopCusList.do` | 查询店铺会员列表 |
| 销售记录 | #28 | `/shopmanager/pc/queryTodayLists.do` | 查询销售记录 |
| 人脸识别 | #50 | `/shopmanager/app/shop/searchCustomerMsg.do` | 人脸识别查询会员 |

### 常用统计接口速查
| 功能 | 接口编号 | URL路径 | 说明 |
|------|---------|---------|------|
| 主页概览 | #40 | `/shopmanager/pc/mainMessageForPC.do` | 主页数据概览 |
| 24H营业额 | #42 | `/shopmanager/pc/querySaleTotalByHour.do` | 24小时营业额分布 |
| 库存概览 | #43 | `/shopmanager/pc/overviewOfStock.do` | 库存指标概览 |
| 销售走势 | #45 | `/shopmanager/pc/salesTrend.do` | 销售额走势图 |

---

## 域名配置

系统中的API域名通过配置文件进行管理，主要配置如下：

```cpp
// CPP/NetModule/NetGlobal.h
extern QString req_host_prefix;               // 主服务器: http://buyhoo.cc/
extern QString req_host_prefix_mini_program;  // 小程序服务器: http://buyhoo.cc/
extern QString req_host_prefix_face;          // 人脸识别服务器: http://face.buyhoo.cc/
extern QString req_host_prefix_takeaway;      // 外卖服务器: http://buyhoo.cc/shop/
extern QString req_host_prefix__cash_upgrade; // 升级服务器: http://update.allscm.top/cashUpgrade/
```

---

## buyhoo.cc - 主服务器

### 认证与系统接口

#### 1. 用户登录认证
- **URL**: `POST http://buyhoo.cc/shopmanager/app/shop/staffLogin.do`
- **实现**: [`CPP/ControlModule/ShopControl.cpp:407`](../CPP/ControlModule/ShopControl.cpp#L407)
- **功能**: 用户账号密码登录验证
- **请求参数**:
  ```json
  {
    "staffAccount": "用户账号",
    "staffPwd": "用户密码",
    "versionNumber": "版本号",
    "machineTime": "机器时间",
    "macId": "MAC地址"
  }
  ```

#### 2. 服务器时间同步
- **URL**: `POST http://buyhoo.cc/shopmanager/app/shop/getNowTime.do`
- **实现**: [`CPP/main.cpp:48`](../CPP/main.cpp#L48)
- **功能**: 系统启动时同步服务器时间

### 商品管理接口

#### 3. 商品数据同步
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/pcGoods.do`
- **实现**: [`CPP/ControlModule/GoodsControl.cpp:75`](../CPP/ControlModule/GoodsControl.cpp#L75)
- **功能**: 从服务器同步商品数据到本地
- **请求参数**:
  ```
  shop_unique: 店铺ID
  today: 0
  isCache: 2
  ```

#### 4. 商品上传(旧版)
- **URL**: `POST http://buyhoo.cc/shopmanager/app/shop/appNewGoods.do`
- **实现**: [`CPP/ControlModule/GoodsControl.cpp:670`](../CPP/ControlModule/GoodsControl.cpp#L670)
- **功能**: 上传新商品信息到服务器

#### 5. 商品上传(新版)
- **URL**: `POST http://buyhoo.cc/shopUpdate/goods/v2/addGoods.do`
- **实现**: [`CPP/ControlModule/GoodsControl.cpp:785`](../CPP/ControlModule/GoodsControl.cpp#L785)
- **功能**: 新版商品上传接口

#### 6. 商品捆绑查询
- **URL**: `POST http://buyhoo.cc/shopmanager/app/shop/pcSaleGoodsSearch.do`
- **实现**: [`CPP/ControlModule/GoodsPromotionCtrl.cpp:918`](../CPP/ControlModule/GoodsPromotionCtrl.cpp#L918)
- **功能**: 查询商品捆绑销售信息

#### 7. 商品促销查询
- **URL**: `POST http://buyhoo.cc/harricane/payOnline/queryAllGoodsPromotion.do`
- **实现**: [`CPP/ControlModule/GoodsPromotionCtrl.cpp:647`](../CPP/ControlModule/GoodsPromotionCtrl.cpp#L647)
- **功能**: 查询商品优惠满赠信息

#### 8. 在线商品信息查询
- **URL**: `POST http://buyhoo.cc/shopmanager/goodsDict/queryGoodsDictMsg.do`
- **实现**: [`CPP/ControlModule/GoodsControl.cpp:2286`](../CPP/ControlModule/GoodsControl.cpp#L2286)
- **功能**: 根据条码查询在线商品信息

#### 9. 虚拟商品分类查询
- **URL**: `POST http://buyhoo.cc/shopmanager/goodsKindInvented/queryGoodsInventedList.do`
- **实现**: [`CPP/ControlModule/GoodsControl.cpp:2670`](../CPP/ControlModule/GoodsControl.cpp#L2670)
- **功能**: 查询虚拟商品分类列表

#### 10. 商品详情查询
- **URL**: `POST http://buyhoo.cc/shopmanager/app/shop/appQueryGoodsDetail.do`
- **实现**: [`CPP/ControlModule/GoodsControl.cpp:2192`](../CPP/ControlModule/GoodsControl.cpp#L2192)
- **功能**: 查询商品详细信息

#### 11. 单品促销查询
- **URL**: `POST http://buyhoo.cc/harricane/payOnline/queryPromotionGoodsSingle.do`
- **实现**: [`CPP/ControlModule/GoodsPromotionCtrl.cpp:1041`](../CPP/ControlModule/GoodsPromotionCtrl.cpp#L1041)
- **功能**: 查询单品促销信息

#### 12. 订单促销查询
- **URL**: `POST http://buyhoo.cc/harricane/payOnline/queryOrderPromotion.do`
- **实现**: [`CPP/ControlModule/GoodsPromotionCtrl.cpp:1590`](../CPP/ControlModule/GoodsPromotionCtrl.cpp#L1590)
- **功能**: 查询订单级别的促销信息

### 支付处理接口

#### 13. 在线会员支付
- **URL**: `POST http://buyhoo.cc/goBuy/cart/collectMoneyPay.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:792`](../CPP/ControlModule/PayMethodControl.cpp#L792)
- **功能**: 处理在线会员支付

#### 14. 订单上传/在线支付
- **URL**: `POST http://buyhoo.cc/harricane/payOnline/v2/cashierPay.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:3415`](../CPP/ControlModule/PayMethodControl.cpp#L3415)
- **功能**: 上传订单并处理在线支付

#### 15. 宁宇订单上传
- **URL**: `POST http://buyhoo.cc/payOnline/v2/cashierPay_ny.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:3534`](../CPP/ControlModule/PayMethodControl.cpp#L3534)
- **功能**: 宁宇特定的订单上传接口

#### 16. 支付状态查询
- **URL**: `POST http://buyhoo.cc/harricane/payOnline/queryOrderYT.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:3725`](../CPP/ControlModule/PayMethodControl.cpp#L3725)
- **功能**: 查询在线支付状态

#### 17. 会员充值
- **URL**: `POST http://buyhoo.cc/harricane/cuscheckout/recharge.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:1863`](../CPP/ControlModule/PayMethodControl.cpp#L1863)
- **功能**: 处理会员账户充值

#### 18. 小程序支付
- **URL**: `POST http://buyhoo.cc/goBuy/cart/v2/queryPlatformCusInfo.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:2390`](../CPP/ControlModule/PayMethodControl.cpp#L2390)
- **功能**: 小程序支付处理

#### 19. 小程序支付状态查询
- **URL**: `POST http://buyhoo.cc/goBuy/my/querySaleListDetailStatus.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:2620`](../CPP/ControlModule/PayMethodControl.cpp#L2620)
- **功能**: 查询小程序支付状态

#### 20. 小程序订单详情查询
- **URL**: `POST http://buyhoo.cc/goBuy/my/querySaleListDetail.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:2536`](../CPP/ControlModule/PayMethodControl.cpp#L2536)
- **功能**: 查询小程序订单详细信息

#### 21. 易通支付状态查询
- **URL**: `POST http://buyhoo.cc/harricane/payOnline/yiTongPaySaleStatus.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:4209`](../CPP/ControlModule/PayMethodControl.cpp#L4209)
- **功能**: 查询易通支付状态(组合支付)

### 会员管理接口

#### 22. 会员列表查询
- **URL**: `POST http://buyhoo.cc/shopmanager/customer/queryShopCusList.do`
- **实现**: [`CPP/ControlModule/MemberControl.cpp:21`](../CPP/ControlModule/MemberControl.cpp#L21)
- **功能**: 查询店铺会员列表

#### 23. 会员信息查询
- **URL**: `POST http://buyhoo.cc/shopUpdate/cuscheckout/findCusById.do`
- **实现**: [`CPP/ControlModule/MemberControl.cpp:256`](../CPP/ControlModule/MemberControl.cpp#L256)
- **功能**: 根据会员ID查询详细信息

#### 24. 会员注册/添加
- **URL**: `POST http://buyhoo.cc/harricane/cuscheckout/addCus.do`
- **实现**: [`CPP/ControlModule/MemberControl.cpp:574`](../CPP/ControlModule/MemberControl.cpp#L574)
- **功能**: 注册新会员或添加会员

#### 25. 会员删除
- **URL**: `POST http://buyhoo.cc/harricane/cuscheckout/deleteCus.do`
- **实现**: [`CPP/ControlModule/MemberControl.cpp:652`](../CPP/ControlModule/MemberControl.cpp#L652)
- **功能**: 删除会员账户

#### 26. 会员信息编辑
- **URL**: `POST http://buyhoo.cc/shopUpdate/cuscheckout/editCus.do`
- **实现**: [`CPP/ControlModule/MemberControl.cpp:794`](../CPP/ControlModule/MemberControl.cpp#L794)
- **功能**: 编辑会员信息

#### 27. 会员统计查询
- **URL**: `POST http://buyhoo.cc/shopmanager/customer/queryCustomerStat.do`
- **实现**: [`CPP/ControlModule/MemberControl.cpp:418`](../CPP/ControlModule/MemberControl.cpp#L418)
- **功能**: 查询会员统计数据

### 订单管理接口

#### 28. 销售记录查询
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/queryTodayLists.do`
- **实现**: [`CPP/ControlModule/OrderControl.cpp:296`](../CPP/ControlModule/OrderControl.cpp#L296)
- **功能**: 查询指定时间段的销售记录

#### 29. 网络订单查询
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/pcQuerySaleList.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:20`](../CPP/ControlModule/NetOrderControl.cpp#L20)
- **功能**: 查询网络订单记录

#### 30. 订单状态统计(新版)
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/selectOnlineOrderCountByStatus.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:76`](../CPP/ControlModule/NetOrderControl.cpp#L76)
- **功能**: 按状态统计订单数量

#### 31. 订单状态统计(旧版)
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/getListCountByStatus.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:145`](../CPP/ControlModule/NetOrderControl.cpp#L145)
- **功能**: 按状态统计订单数量(旧版)

#### 32. 订单详情查询
- **URL**: `POST http://buyhoo.cc/shopmanager/app/shop/appQuerySaleListDetail.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:391`](../CPP/ControlModule/NetOrderControl.cpp#L391)
- **功能**: 查询订单详细信息

#### 33. 商品销售信息查询
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/queryTodayGoodsSaleMsg.do`
- **实现**: [`CPP/ControlModule/OrderControl.cpp:514`](../CPP/ControlModule/OrderControl.cpp#L514)
- **功能**: 查询商品销售统计信息

#### 34. 退货记录添加
- **URL**: `POST http://buyhoo.cc/shopmanager/ret/addNewRetRecord.do`
- **实现**: [`CPP/ControlModule/OrderControl.cpp:682`](../CPP/ControlModule/OrderControl.cpp#L682)
- **功能**: 添加退货记录

### 配送管理接口

#### 35. 创建配送订单
- **URL**: `POST http://buyhoo.cc/shop/peisong/createOrder.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:492`](../CPP/ControlModule/NetOrderControl.cpp#L492)
- **功能**: 创建配送订单
- **请求参数**:
  ```
  delivery_type: 配送方式 (-1:到店自提, 0:自配送, 1:美团配送, 2:一刻钟配送)
  shop_courier_id: 自配送商家快递员ID
  courier_name: 配送员姓名
  goods_weight: 订单商品重量(kg)
  sale_list_unique: 订单编号
  ```

#### 36. 获取配送员列表
- **URL**: `POST http://buyhoo.cc/shop/peisong/order/shopCourierList.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:442`](../CPP/ControlModule/NetOrderControl.cpp#L442)
- **功能**: 获取店铺配送员列表

### 供应商管理接口

#### 37. 供应商列表查询
- **URL**: `POST http://buyhoo.cc/harricane/html/supplier/querySupplierList.do`
- **实现**: [`CPP/ControlModule/SupplierControl.cpp:365`](../CPP/ControlModule/SupplierControl.cpp#L365)
- **功能**: 查询供应商列表

### 店铺管理接口

#### 38. 店铺积分查询
- **URL**: `POST http://buyhoo.cc/harricane/payOnline/queryShopBeans.do`
- **实现**: [`CPP/ControlModule/ShopControl.cpp:1226`](../CPP/ControlModule/ShopControl.cpp#L1226)
- **功能**: 查询店铺积分余额

### 系统管理接口

#### 39. 日志上传
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/uploadLog.do`
- **实现**: [`CPP/ControlModule/LogCtrl.cpp:30`](../CPP/ControlModule/LogCtrl.cpp#L30)
- **功能**: 上传系统日志文件(ZIP格式)

### 数据统计分析接口

#### 40. 主页数据概览
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/mainMessageForPC.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:34`](../CPP/CheckDataModule/checkData.cpp#L34)
- **功能**: 获取主页数据概览信息

#### 41. 销售数据周期占比
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/saleStatisticsCycleRatio.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:81`](../CPP/CheckDataModule/checkData.cpp#L81)
- **功能**: 查询销售数据周期占比统计
- **请求参数**: `dateType` (日期类型)

#### 42. 营业额24小时分布
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/querySaleTotalByHour.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:130`](../CPP/CheckDataModule/checkData.cpp#L130)
- **功能**: 查询24小时营业额分布图数据

#### 43. 库存指标概览
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/overviewOfStock.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:178`](../CPP/CheckDataModule/checkData.cpp#L178)
- **功能**: 获取库存指标概览数据

#### 44. 销售品类占比
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/salesCategoryRatio.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:226`](../CPP/CheckDataModule/checkData.cpp#L226)
- **功能**: 查询销售品类占比统计
- **请求参数**: `dateType` (日期类型)

#### 45. 销售额走势图
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/salesTrend.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:324`](../CPP/CheckDataModule/checkData.cpp#L324)
- **功能**: 查询销售额走势图数据
- **请求参数**: `startTime`, `endTime`

#### 46. 滞销商品TOP5
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/queryUnmarketableTop5.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:502`](../CPP/CheckDataModule/checkData.cpp#L502)
- **功能**: 查询滞销商品TOP5统计

#### 47. 商品销售统计
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/queryGoodsSaleStatistics.do`
- **实现**: [`CPP/ControlModule/OrderControl.cpp:454`](../CPP/ControlModule/OrderControl.cpp#L454)
- **功能**: 查询商品销售统计数据

#### 48. 交班记录
- **URL**: `POST http://buyhoo.cc/shopmanager/pc/pcStaffSignOutForBoss.do`
- **实现**: [`CPP/ControlModule/OrderControl.cpp:573`](../CPP/ControlModule/OrderControl.cpp#L573)
- **功能**: 记录员工交班信息

---

### 文件下载接口

#### 49. 文件下载服务
- **URL**: `GET {动态URL}` (通过升级服务器获取)
- **实现**: [`CPP/ControlModule/FileDownloadWorker.cpp:36`](../CPP/ControlModule/FileDownloadWorker.cpp#L36)
- **功能**: 下载升级文件和其他资源文件
- **特点**: 支持断点续传、进度监控、批量下载

---

## face.buyhoo.cc - 人脸识别服务器

#### 50. 人脸识别查询会员
- **URL**: `POST http://face.buyhoo.cc/shopmanager/app/shop/searchCustomerMsg.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:693`](../CPP/ControlModule/PayMethodControl.cpp#L693)
- **功能**: 通过人脸识别查询会员信息
- **请求参数**:
  ```json
  {
    "shopUnique": "店铺ID",
    "imageMsg": "Base64编码的人脸图片",
    "imgFormat": "jpg",
    "onlineType": "1",
    "saleListTotal": "订单总金额"
  }
  ```

---

## update.allscm.top - 升级服务器

#### 51. 版本检查(旧版)
- **URL**: `POST http://update.allscm.top/cashUpgrade/upgrade/pc/checkVersion`
- **实现**: [`CPP/ControlModule/UpdateCtrl.cpp:197`](../CPP/ControlModule/UpdateCtrl.cpp#L197)
- **功能**: 检查软件版本更新

---

## platform.allscm.net - 新版升级服务器

#### 52. 版本检查(新版)
- **URL**: `POST https://platform.allscm.net/gw/platformBaseWeb/noAuth/pc/checkVersion`
- **实现**: [`CPP/ControlModule/UpdateCtrl.cpp:75`](../CPP/ControlModule/UpdateCtrl.cpp#L75)
- **功能**: 新版本检查接口
- **请求参数**:
  ```json
  {
    "sign": "签名",
    "shopUnique": "店铺ID",
    "appCode": "NEW_PC_CASHIER",
    "currentVersion": "当前版本号"
  }
  ```

#### 53. 升级完成通知
- **URL**: `POST https://platform.allscm.net/gw/platformBaseWeb/noAuth/pc/upgradeFinish`
- **实现**: [`CPP/ControlModule/UpdateCtrl.cpp:416`](../CPP/ControlModule/UpdateCtrl.cpp#L416)
- **功能**: 通知服务器升级完成
- **请求参数**:
  ```json
  {
    "shopUnique": "店铺ID",
    "sign": "签名",
    "logId": "日志ID"
  }
  ```

---

## www.baidu.com - 网络状态检测

#### 54. 网络连通性检测
- **URL**: `ping www.baidu.com`
- **实现**: [`CPP/WorkerModule/NetStatusWorker.cpp:21`](../CPP/WorkerModule/NetStatusWorker.cpp#L21)
- **功能**: 检测网络连接状态
- **检测方式**: 使用ping命令检测延迟，延迟在0-2000ms之间视为在线

---

## MQTT服务器

### 服务器配置
- **服务器地址**: `120.24.220.116`
- **端口**: `1883`
- **实现**: [`CPP/ControlModule/MqttControl.cpp`](../CPP/ControlModule/MqttControl.cpp)

### 连接参数
- **客户端ID**: `Market + MAC地址`
- **主题**: `win_qt_cash_[MAC地址]` (动态生成)
- **QoS**: `2`
- **保活时间**: `3秒`
- **重连间隔**: `13秒`
- **遗嘱主题**: `win_qt_cash_1.0`
- **清理会话**: `true` (连接时), `false` (重连时)

### 消息类型

#### 连接初始化消息
```json
{
  "ctrl": "msg_init",
  "ID": "设备MAC地址",
  "status": 200,
  "errcode": 0,
  "msg": "success",
  "count": 1,
  "data": {
    "shop_unique": "店铺ID"
  }
}
```

#### 设备关闭消息
```json
{
  "ctrl": "msg_shutdown",
  "ID": "设备MAC地址",
  "version": "版本号"
}
```

#### 业务消息类型
- `msg_net_new`: 新订单通知 → 语音提醒 + 自动打印
- `msg_goods_update`: 商品更新 → 刷新商品数据 + 同步到条码秤
- `msg_goods_kind_update`: 商品分类更新
- `msg_goods_add`: 商品添加
- `msg_goods_del`: 商品删除

---

## 接口统计汇总

### 按域名分类统计
- **buyhoo.cc**: 48个接口 (主要业务接口)
- **face.buyhoo.cc**: 1个接口 (人脸识别)
- **update.allscm.top**: 1个接口 (旧版升级)
- **platform.allscm.net**: 2个接口 (新版升级)
- **www.baidu.com**: 1个接口 (网络检测)
- **文件下载服务**: 1个服务 (动态URL)
- **MQTT服务器**: 1个服务 (实时通信)

### 按功能分类统计
- **认证与系统**: 2个接口
- **商品管理**: 10个接口
- **支付处理**: 9个接口
- **会员管理**: 6个接口
- **订单管理**: 7个接口
- **配送管理**: 2个接口
- **供应商管理**: 1个接口
- **店铺管理**: 1个接口
- **系统管理**: 1个接口
- **数据统计分析**: 9个接口
- **文件下载**: 1个服务
- **人脸识别**: 1个接口
- **系统升级**: 3个接口
- **网络检测**: 1个接口
- **实时通信**: 1个MQTT服务

### 通信协议
- **HTTP/HTTPS**: 53个接口
- **MQTT**: 1个实时通信服务
- **ICMP**: 1个网络检测服务

### 数据格式
- **JSON**: 主要用于新版接口和复杂数据传输
- **Form-urlencoded**: 主要用于传统接口和简单参数传输
- **Base64**: 用于图片数据传输(人脸识别)

---

## 注意事项

1. **域名配置**: 所有API域名都通过配置文件管理，支持动态切换环境
2. **认证机制**: 大部分接口需要店铺ID(shopUnique)和用户ID认证
3. **超时设置**: HTTP请求默认超时时间为3秒
4. **重试机制**: 支持自动重试机制
5. **日志记录**: 所有网络请求都有详细的日志记录
6. **多语言支持**: HTTP请求头包含语言信息(zh_CN, en_US, vie_VN, th_TH, ru_RU)
7. **版本兼容**: 部分接口有新旧版本，系统会根据配置选择合适的版本

## 文档优化建议

### 已发现并修正的问题
1. ✅ **遗漏接口补充**: 新增了12个遗漏的API接口
   - 商品详情查询、单品促销查询、订单促销查询
   - 小程序订单详情查询、易通支付状态查询
   - 店铺积分查询、日志上传接口
   - 主页数据概览、销售数据周期占比、营业额24小时分布
   - 销售品类占比、销售额走势图、文件下载服务

2. ✅ **接口编号重新整理**: 统一了接口编号(1-54)，避免重复和混乱

3. ✅ **分类优化**: 新增了"店铺管理"、"系统管理"和"文件下载"分类

4. ✅ **统计数据更新**: 更新了接口数量统计，总计53个HTTP接口

5. ✅ **数据统计接口完善**: 补充了完整的数据分析相关接口，从4个增加到9个

### 文档使用建议
1. **快速查找**: 使用目录链接快速定位到特定域名或功能分类
2. **源码追踪**: 点击实现位置链接可直接跳转到对应源码文件
3. **接口测试**: 建议结合Postman等工具进行接口测试
4. **环境切换**: 注意不同环境下的域名配置差异
5. **错误处理**: 参考源码中的错误处理逻辑进行客户端开发

### 维护建议
1. **定期更新**: 建议每次版本发布后更新此文档
2. **接口变更**: 新增或修改接口时及时更新文档
3. **示例补充**: 可考虑为重要接口添加请求/响应示例
4. **状态码说明**: 可补充常见HTTP状态码和业务状态码说明

---

*文档生成时间: 2025-06-24*
*基于BUYHOO收银系统源码分析生成*
*最后更新: 新增12个遗漏接口，完善数据统计分析模块，总计54个接口*
