# BUYHOO 收银系统业务流程与通信接口文档

## 目录

- [概述](#概述)
- [系统架构](#系统架构)
- [核心业务流程](#核心业务流程)
  - [1. 用户登录认证流程](#1-用户登录认证流程)
  - [2. 商品管理流程](#2-商品管理流程)
  - [3. 支付处理流程](#3-支付处理流程)
  - [4. 会员管理流程](#4-会员管理流程)
  - [5. 订单管理流程](#5-订单管理流程)
  - [6. 配送管理流程](#6-配送管理流程)
- [MQTT实时通信](#mqtt实时通信)
- [硬件设备通信](#硬件设备通信)
- [数据统计分析接口](#数据统计分析接口)
- [网络状态监控](#网络状态监控)
- [HTTP客户端架构](#http客户端架构)
- [业务流程时序图](#业务流程时序图)
- [错误处理机制](#错误处理机制)
- [安全机制](#安全机制)
- [配置管理](#配置管理)
- [日志系统](#日志系统)
- [API接口汇总表](#api接口汇总表)
- [通信协议汇总](#通信协议汇总)
- [总结](#总结)

## 概述

BUYHOO是一个基于Qt/QML开发的智能收银系统，支持多种支付方式、会员管理、商品管理等功能。本文档详细描述了系统中涉及的HTTP接口、MQTT通信、串口通信等与外部系统的交互流程。

## 系统架构

### 网络通信架构
- **HTTP客户端**: 基于Qt的HttpClient，支持GET/POST请求
- **MQTT客户端**: 用于实时消息推送和设备状态同步
- **串口通信**: 支持电子秤、打印机等硬件设备通信
- **网络状态监控**: 实时监控网络连接状态

### 服务器端点配置
```cpp
// 主要服务器地址配置 - CPP/NetModule/NetGlobal.h
extern QString req_host_prefix;               // 主服务器: http://buyhoo.cc/
extern QString req_host_prefix_mini_program;  // 小程序服务器: http://buyhoo.cc/
extern QString req_host_prefix_face;          // 人脸识别服务器: http://face.buyhoo.cc/
extern QString req_host_prefix_takeaway;      // 外卖服务器: http://buyhoo.cc/shop/
extern QString req_host_prefix__cash_upgrade; // 升级服务器: http://update.allscm.top/cashUpgrade/
```

## 核心业务流程

### 1. 用户登录认证流程

#### 1.1 登录接口
- **接口**: `POST /shopmanager/app/shop/staffLogin.do`
- **实现**: [`CPP/ControlModule/ShopControl.cpp:407`](../CPP/ControlModule/ShopControl.cpp#L407)
- **请求参数**:
  ```json
  {
    "staffAccount": "用户账号",
    "staffPwd": "用户密码",
    "versionNumber": "版本号",
    "machineTime": "机器时间",
    "macId": "MAC地址"
  }
  ```

#### 1.2 时间同步
- **接口**: `POST /shopmanager/app/shop/getNowTime.do`
- **实现**: [`CPP/main.cpp:48`](../CPP/main.cpp#L48)
- **用途**: 系统启动时同步服务器时间

### 2. 商品管理流程

#### 2.1 商品数据同步
- **接口**: `POST /shopmanager/pc/pcGoods.do`
- **实现**: [`CPP/ControlModule/GoodsControl.cpp:75`](../CPP/ControlModule/GoodsControl.cpp#L75)
- **请求参数**:
  ```
  shop_unique: 店铺ID
  today: 0
  isCache: 2
  ```

#### 2.2 商品上传
- **接口**: `POST /shopmanager/app/shop/appNewGoods.do`
- **实现**: [`CPP/ControlModule/GoodsControl.cpp:670`](../CPP/ControlModule/GoodsControl.cpp#L670)
- **接口**: `POST /shopUpdate/goods/v2/addGoods.do`
- **实现**: [`CPP/ControlModule/GoodsControl.cpp:785`](../CPP/ControlModule/GoodsControl.cpp#L785)

#### 2.3 商品捆绑查询
- **接口**: `POST /shopmanager/app/shop/pcSaleGoodsSearch.do`
- **实现**: [`CPP/ControlModule/GoodsPromotionCtrl.cpp:918`](../CPP/ControlModule/GoodsPromotionCtrl.cpp#L918)

#### 2.4 商品促销查询
- **接口**: `POST /harricane/payOnline/queryAllGoodsPromotion.do`
- **实现**: [`CPP/ControlModule/GoodsPromotionCtrl.cpp:647`](../CPP/ControlModule/GoodsPromotionCtrl.cpp#L647)
- **功能**: 查询商品优惠满赠信息

#### 2.5 订单促销查询
- **接口**: `POST /harricane/payOnline/queryOrderPromotion.do`
- **实现**: [`CPP/ControlModule/GoodsPromotionCtrl.cpp:1590`](../CPP/ControlModule/GoodsPromotionCtrl.cpp#L1590)
- **功能**: 查询订单级别的促销活动

### 3. 支付处理流程

#### 3.1 在线会员支付
- **接口**: `POST /goBuy/cart/collectMoneyPay.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:792`](../CPP/ControlModule/PayMethodControl.cpp#L792)
- **支付方式**: 102 (在线会员支付)

#### 3.2 订单上传
- **接口**: `POST /harricane/payOnline/v2/cashierPay.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:3415`](../CPP/ControlModule/PayMethodControl.cpp#L3415)
- **宁宇专用**: `POST /payOnline/v2/cashierPay_ny.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:3534`](../CPP/ControlModule/PayMethodControl.cpp#L3534)

#### 3.3 支付状态查询
- **接口**: `POST /harricane/payOnline/queryOrderYT.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:3725`](../CPP/ControlModule/PayMethodControl.cpp#L3725)

#### 3.4 会员充值
- **接口**: `POST /harricane/cuscheckout/recharge.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:1863`](../CPP/ControlModule/PayMethodControl.cpp#L1863)

#### 3.5 小程序支付
- **接口**: `POST /goBuy/cart/v2/queryPlatformCusInfo.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:2390`](../CPP/ControlModule/PayMethodControl.cpp#L2390)
- **支付方式**: 101 (小程序支付)
- **支付码识别**: 以"36"开头的16-24位支付码

#### 3.6 小程序支付状态查询
- **接口**: `POST /goBuy/my/querySaleListDetailStatus.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:2620`](../CPP/ControlModule/PayMethodControl.cpp#L2620)

#### 3.7 人脸支付
- **接口**: `POST /shopmanager/app/shop/searchCustomerMsg.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:693`](../CPP/ControlModule/PayMethodControl.cpp#L693)
- **服务器**: `http://face.buyhoo.cc/`
- **请求参数**:
  ```json
  {
    "shopUnique": "店铺ID",
    "imageMsg": "Base64编码的人脸图片",
    "imgFormat": "jpg",
    "onlineType": "1",
    "saleListTotal": "订单总金额"
  }
  ```

#### 3.8 在线支付(扫码支付)
- **接口**: `POST /harricane/payOnline/v2/cashierPay.do`
- **实现**: [`CPP/ControlModule/PayMethodControl.cpp:3616`](../CPP/ControlModule/PayMethodControl.cpp#L3616)
- **支持的支付方式**:
  - 支付宝在线支付 (10-15开头)
  - 微信在线支付 (25-30开头)
  - 易通支付 (62开头)
  - 银联支付

### 4. 会员管理流程

#### 4.1 会员信息查询
- **接口**: `POST /shopUpdate/cuscheckout/findCusById.do`
- **实现**: [`CPP/ControlModule/MemberControl.cpp:256`](../CPP/ControlModule/MemberControl.cpp#L256)

#### 4.2 会员注册/添加
- **接口**: `POST /harricane/cuscheckout/addCus.do`
- **实现**: [`CPP/ControlModule/MemberControl.cpp:574`](../CPP/ControlModule/MemberControl.cpp#L574)
- **实现**: [`CPP/ControlModule/MemberControl.cpp:886`](../CPP/ControlModule/MemberControl.cpp#L886)

#### 4.3 会员删除
- **接口**: `POST /harricane/cuscheckout/deleteCus.do`
- **实现**: [`CPP/ControlModule/MemberControl.cpp:652`](../CPP/ControlModule/MemberControl.cpp#L652)

#### 4.4 会员信息编辑
- **接口**: `POST /shopUpdate/cuscheckout/editCus.do`
- **实现**: [`CPP/ControlModule/MemberControl.cpp:794`](../CPP/ControlModule/MemberControl.cpp#L794)

### 5. 订单管理流程

#### 5.1 销售记录查询
- **接口**: `POST /shopmanager/pc/queryTodayLists.do`
- **实现**: [`CPP/ControlModule/OrderControl.cpp:296`](../CPP/ControlModule/OrderControl.cpp#L296)

#### 5.2 网单查询
- **接口**: `POST /shopmanager/pc/pcQuerySaleList.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:20`](../CPP/ControlModule/NetOrderControl.cpp#L20)

#### 5.3 订单状态统计
- **接口**: `POST /shopmanager/pc/selectOnlineOrderCountByStatus.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:76`](../CPP/ControlModule/NetOrderControl.cpp#L76)
- **接口**: `POST /shopmanager/pc/getListCountByStatus.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:145`](../CPP/ControlModule/NetOrderControl.cpp#L145)

#### 5.4 退货处理
- **接口**: `POST /shopmanager/ret/addNewRetRecord.do`
- **实现**: [`CPP/ControlModule/OrderControl.cpp:682`](../CPP/ControlModule/OrderControl.cpp#L682)

#### 5.5 商品销售统计
- **接口**: `POST /shopmanager/pc/queryGoodsSaleStatistics.do`
- **实现**: [`CPP/ControlModule/OrderControl.cpp:454`](../CPP/ControlModule/OrderControl.cpp#L454)

#### 5.6 交班记录
- **接口**: `POST /shopmanager/pc/pcStaffSignOutForBoss.do`
- **实现**: [`CPP/ControlModule/OrderControl.cpp:573`](../CPP/ControlModule/OrderControl.cpp#L573)

### 6. 配送管理流程

#### 6.1 获取配送员列表
- **接口**: `POST /peisong/order/shopCourierList.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:442`](../CPP/ControlModule/NetOrderControl.cpp#L442)
- **服务器**: `req_host_prefix_takeaway` (外卖服务器)

#### 6.2 创建配送订单
- **接口**: `POST /peisong/createOrder.do`
- **实现**: [`CPP/ControlModule/NetOrderControl.cpp:492`](../CPP/ControlModule/NetOrderControl.cpp#L492)
- **自配送实现**: [`CPP/ControlModule/NetOrderControl.cpp:580`](../CPP/ControlModule/NetOrderControl.cpp#L580)

#### 6.3 配送方式支持
```cpp
// 配送方式映射 - CPP/ControlModule/NetOrderControl.cpp:756
QVariant NetOrderControl::getDispatchTypeByName(QVariant name) {
    QString name_str = name.toString();
    if (name_str == "自配送") return 0;
    else if (name_str == "美团配送") return 1;
    else if (name_str == "一刻钟配送") return 2;
    // -1: 到店自提
}
```

#### 6.4 配送订单参数
- `delivery_type`: 配送方式 (-1:到店自提, 0:自配送, 1:美团配送, 2:一刻钟配送)
- `shop_courier_id`: 自配送商家快递员ID
- `courier_name`: 配送员姓名
- `goods_weight`: 订单商品重量(kg)
- `sale_list_unique`: 订单编号

## MQTT实时通信

### MQTT配置
```cpp
// MQTT服务器配置 - CPP/ControlModule/MqttControl.h
const QString HOST_MQTT  = "**************";
const quint16 PORT_MQTT  = 1883;
const QString TOPIC_MQTT = "win_qt_cash_1.0";
```

### MQTT消息类型
- **实现**: [`CPP/ControlModule/MqttControl.cpp`](../CPP/ControlModule/MqttControl.cpp)

#### 连接初始化消息
```json
{
  "ctrl": "msg_init",
  "ID": "设备MAC地址",
  "status": 200,
  "errcode": 0,
  "msg": "success",
  "count": 1,
  "data": {
    "shop_unique": "店铺ID"
  }
}
```

#### 设备关闭消息
```json
{
  "ctrl": "msg_shutdown",
  "ID": "设备MAC地址",
  "version": "版本号"
}
```

#### 业务消息类型

##### 系统控制消息
- `msg_init`: 设备初始化消息
- `msg_shutdown`: 设备关闭消息
- `msg_pay_ok`: 支付成功通知
- `msg_pay_error`: 支付失败通知

##### 订单相关消息
- `msg_net_refund`: 网络退款消息
- `msg_net_new`: 新订单消息（触发语音提醒和自动打印）

##### 商品管理消息
- `msg_goods_kind_update`: 商品分类变更
- `msg_goods_kind_add`: 商品分类添加
- `msg_goods_kind_del`: 商品分类删除
- `msg_goods_update`: 商品信息更新
- `msg_goods_add`: 商品添加
- `msg_goods_del`: 商品删除

##### Qt信号映射
- 商品分类操作: `sigMqttAddGoodsKind4Qml`, `sigMqttDelGoodsKind4Qml`, `sigMqttRefreshGoodsKind4Qml`
- 商品操作: `sigMqttAddGoods4Qml`, `sigMqttDelGoods4Qml`, `sigMqttRefreshGoodsByBarcode4Qml`
- 订单统计: `sigMqttRefreshOrderCountByStatus4Qml`
- 日志上传: `sigLogUpload`

#### MQTT消息处理流程
```cpp
// 消息接收处理 - CPP/ControlModule/MqttControl.cpp:108
void MqttControl::onReceived(const QByteArray &message, const QMqttTopicName &topic)
{
    // 1. 解析JSON消息
    Json json_doc = Json::parse(received_str, nullptr, false);

    // 2. 根据ctrl字段分发处理
    string ctrl_str = json_doc["ctrl"];

    // 3. 特殊处理网单消息
    if(ctrl_str == "msg_net_new") {
        // 触发订单统计刷新
        emit sigMqttRefreshOrderCountByStatus4Qml();

        // 语音提醒（可配置）
        if(ConfigTool::getInstance()->isPlayMiniProgramTakeOrders()) {
            ControlManager::getInstance()->getTtsControl()->say("您有网单请及时处理");
        }

        // 自动接单打印（可配置）
        if(ConfigTool::getInstance()->getSetting(ConfigEnum::IS_AUTO_TAKE_ORDERS_AND_PRINT)) {
            printer_control->printNetOrder(QVariant(QString::fromStdString(json_doc.dump())));
        }
    }
}
```

#### MQTT主题动态生成
```cpp
// 主题生成规则 - CPP/ControlModule/MqttControl.cpp:678
QString MqttControl::getTopic() {
    auto result = QString("win_qt_cash_") + Utils::getHostMacAddressWithoutColon();
    return result;  // 例如: win_qt_cash_AABBCCDDEEFF
}
```

## 硬件设备通信

### 1. 电子秤通信
- **实现**: [`CPP/ControlModule/WeightingScaleControl.cpp`](../CPP/ControlModule/WeightingScaleControl.cpp)
- **通信方式**: 串口通信 (QSerialPort)
- **功能**:
  - 重量数据读取
  - 零点校准
  - 去皮功能
  - 稳定重量检测

#### 串口配置
```cpp
// 串口参数设置
void setCom(QString com);           // 设置串口号
void setBaud(QString baud);         // 设置波特率
bool setParseType(QString type);    // 设置数据解析类型
```

### 2. 打印机通信
- **实现**: [`CPP/ControlModule/PrinterControl.cpp`](../CPP/ControlModule/PrinterControl.cpp)
- **工作线程**: [`CPP/WorkerModule/PrinterWorker.h`](../CPP/WorkerModule/PrinterWorker.h)

#### 打印功能
```cpp
// 打印订单
void printOrder(std::shared_ptr<ShopCartList> shop_cart_list, int print_num = -1, std::string addition_info = "");
// 组合支付打印
void printOrderCombine(std::shared_ptr<ShopCartList> shop_cart_list, int print_num, std::string addition_info = "", double cash=0, double online=0, double totalPay=0);
// 餐饮打印
void printOrder4Food(std::shared_ptr<ShopCartList> shop_cart_list, int print_num = -1, bool is_reprint = false, std::string addition_info = "");
// 宁宇打印
void printOrderNingyu(std::shared_ptr<ShopCartList> shop_cart_list, int print_num = -1, bool is_reprint = false, std::string addition_info = "");
```

### 3. 条码标签秤通信
- **实现**: [`CPP/ControlModule/BarcodeLabelScale.cpp`](../CPP/ControlModule/BarcodeLabelScale.cpp)

#### 支持的秤类型
- **托利多秤**: `send2ToledoScaleSingleGoodsWizard()`
- **大华秤**: `sendPluFile2Scale_DaHua()`

### 4. 人脸识别通信
- **实现**: [`CPP/WorkerModule/FaceRecognizer.cpp`](../CPP/WorkerModule/FaceRecognizer.cpp)
- **技术栈**: OpenCV + 级联分类器
- **服务器**: `http://face.buyhoo.cc/`

#### 人脸识别功能
```cpp
// 人脸检测和处理
static int imageProcess(cv::Mat camera, cv::CascadeClassifier &cascade_classifier);
static int detection2(cv::Mat img_in, cv::Mat &img_out, cv::CascadeClassifier &cascade_classifier);
static cv::Mat extractFace(cv::Mat input, std::vector<cv::Rect> *faces, cv::CascadeClassifier &cascade_classifier);
static cv::Mat preProcessImage(cv::Mat input);
```

#### 人脸识别流程
1. 摄像头捕获图像
2. 图像预处理(灰度化、直方图均衡化)
3. 人脸检测(级联分类器)
4. 人脸特征提取
5. 与服务器进行人脸比对
6. 返回识别结果

## 支付方式识别机制

### 支付码识别规则
系统通过支付码的前缀和长度来识别不同的支付方式：

| 支付方式 | 前缀规则 | 长度范围 | 实现位置 |
|---------|----------|----------|----------|
| 支付宝在线 | 10-15 | 16-24位 | [`PayMethodControl.cpp:319`](../CPP/ControlModule/PayMethodControl.cpp#L319) |
| 微信在线 | 25-30 | 16-24位 | 同上 |
| 易通支付 | 62 | 16-24位 | 同上 |
| 银联支付 | - | 16-24位 | 同上 |
| 小程序支付 | 36 | 16-24位 | 同上 |

### 支付方式枚举
```cpp
// 支付方式定义 - CPP/ControlModule/PayMethodControl.cpp
enum PayMethodEnum {
    PAY_METHOD__CASH = 1,           // 现金
    PAY_METHOD__ALIPAY_OFFLINE,     // 支付宝离线
    PAY_METHOD__WECHAT_OFFLINE,     // 微信离线
    PAY_METHOD__BANK_CARD,          // 银行卡
    PAY_METHOD__VIPCARD,            // 储值卡
    PAY_METHOD__FACE,               // 人脸支付
    PAY_METHOD__ALIPAY_ONLINE,      // 支付宝在线
    PAY_METHOD__WECHAT_ONLINE,      // 微信在线
    PAY_METHOD__MINI_PROGRAM,       // 小程序支付
    PAY_METHOD__YI_TONG,            // 易通支付
    PAY_METHOD__UNIONPAY,           // 银联支付
    PAY_METHOD__COMBINED            // 组合支付
};
```

## 数据统计分析接口

### 1. 主页数据概览
- **接口**: `POST /shopmanager/pc/mainMessageForPC.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:34`](../CPP/CheckDataModule/checkData.cpp#L34)

### 2. 24小时营业额分布
- **接口**: `POST /shopmanager/pc/querySaleTotalByHour.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:130`](../CPP/CheckDataModule/checkData.cpp#L130)

### 3. 库存指标概览
- **接口**: `POST /shopmanager/pc/overviewOfStock.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:178`](../CPP/CheckDataModule/checkData.cpp#L178)

### 4. 销售额走势图
- **接口**: `POST /shopmanager/pc/salesTrend.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:324`](../CPP/CheckDataModule/checkData.cpp#L324)
- **参数**: `startTime`, `endTime`

### 5. 滞销商品TOP5
- **接口**: `POST /shopmanager/pc/queryUnmarketableTop5.do`
- **实现**: [`CPP/CheckDataModule/checkData.cpp:502`](../CPP/CheckDataModule/checkData.cpp#L502)
- **参数**: `dateType`, `standard`, `pageSize`

## 网络状态监控

### 网络连接检测
- **实现**: [`CPP/WorkerModule/NetStatusWorker.cpp`](../CPP/WorkerModule/NetStatusWorker.cpp)
- **控制器**: [`CPP/ControlModule/NetStatusCtrl.h`](../CPP/ControlModule/NetStatusCtrl.h)
- **Ping工具**: [`CPP/WorkerModule/Ping.h`](../CPP/WorkerModule/Ping.h)

#### 检测机制
```cpp
// Ping检测 - CPP/WorkerModule/NetStatusWorker.cpp:21
int bResult = ping_.Ping("www.baidu.com");
if (bResult >= 0 && bResult <= 2000) {
    emit signalsNetwork_States(true);  // 在线
} else {
    emit signalsNetwork_States(false); // 离线
}
```

#### 网络状态管理
```cpp
// 状态更新 - CPP/ControlModule/NetStatusCtrl.cpp:13
connect(network_thread, &NetStatusWorker::signalsNetwork_States, this,
    [&](bool state) {
        setNetStatus(state ?
            (int)NetworkStatus::NETWORK_STATUS__CONNECTED :
            (int)NetworkStatus::NETWORK_STATUS__DISCONNECTED);

        if(state) {
            net_status_global = "101";  // 网络正常
        } else {
            net_status_global = "102";  // 网络断开
        }
    });
```

#### 检测参数
- **检测目标**: www.baidu.com
- **检测间隔**: 1秒
- **超时阈值**: 2000毫秒
- **运行方式**: 独立工作线程

## HTTP客户端架构

### HttpClient封装
- **实现**: [`CPP/NetModule/HttpClient.h`](../CPP/NetModule/HttpClient.h)
- **基于**: Qt的QNetworkAccessManager

#### 支持的HTTP方法
```cpp
HttpRequest head(const QString &url);
HttpRequest get(const QString &url);
HttpRequest post(const QString &url);
HttpRequest put(const QString &url);
```

### HttpWorker工作线程
- **实现**: [`CPP/NetModule/HttpWorker.cpp`](../CPP/NetModule/HttpWorker.cpp)
- **实现**: [`CPP/NetModule/HttpWorker2.cpp`](../CPP/NetModule/HttpWorker2.cpp)

#### 请求处理流程
1. 创建QNetworkRequest
2. 设置请求头和内容类型
3. 添加用户信息和语言设置
4. 执行POST/GET请求
5. 处理响应数据

## 业务流程时序图

### 支付流程
```mermaid
sequenceDiagram
    participant UI as 收银界面
    participant PC as PayMethodControl
    participant HTTP as HttpClient
    participant Server as 服务器
    participant MQTT as MQTT服务器

    UI->>PC: 发起支付请求
    PC->>HTTP: 创建支付订单
    HTTP->>Server: POST /harricane/payOnline/v2/cashierPay.do
    Server-->>HTTP: 返回支付结果
    HTTP-->>PC: 处理响应
    PC->>MQTT: 发布支付状态
    MQTT-->>UI: 推送支付完成通知
    PC->>UI: 更新界面状态
```

### 商品同步流程
```mermaid
sequenceDiagram
    participant GC as GoodsControl
    participant HTTP as HttpWorker
    participant Server as 服务器
    participant DB as 本地数据库
    participant MQTT as MQTT服务器

    GC->>HTTP: 请求商品数据
    HTTP->>Server: POST /shopmanager/pc/pcGoods.do
    Server-->>HTTP: 返回商品列表
    HTTP-->>GC: 解析商品数据
    GC->>DB: 保存到本地
    MQTT->>GC: 商品变更通知
    GC->>UI: 刷新商品显示
```

## 错误处理机制

### HTTP请求错误处理
```cpp
// 网络错误处理 - CPP/NetModule/HttpWorker.cpp
switch (http_handle_type) {
    case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
        // 请求成功处理
        break;
    case HttpHandleType::HTTP_HANDLE__ON_ERROR:
    case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
        // 网络错误处理
        break;
}
```

### MQTT重连机制
```cpp
// MQTT自动重连 - CPP/ControlModule/MqttControl.cpp
timer_reconn.setInterval(13000);
connect(&timer_reconn, &QTimer::timeout, [&]() {
    setCleanSession(false);
    connectToHost();
});
```

## 安全机制

### 密码加密
```cpp
// MD5密码加密 - CPP/ControlModule/MemberControl.cpp
post_json_doc.at("cusPassword") = Utils::Encrypt::str2Md5(
    QString::fromStdString(post_json_doc.at("cusPassword").get<std::string>())
).toStdString();
```

### 设备标识
```cpp
// MAC地址作为设备唯一标识
QString mac_addr = Utils::getHostMacAddressWithoutColon();
setClientId("Market" + Utils::getHostMacAddressWithoutColon());
```

## 配置管理

### 网络配置
- **配置文件**: [`CPP/ConfModule/ConfigEnum.h`](../CPP/ConfModule/ConfigEnum.h)
- **配置项**:
  - `REQ_HOST_PREFIX`: 主服务器地址
  - `REQ_HOST_PREFIX_MINI_PROGRAM`: 小程序服务器地址
  - `REQ_HOST_PREFIX_FACE`: 人脸识别服务器地址
  - `REQ_HOST_PREFIX_TAKEAWAY`: 外卖服务器地址

### 串口配置
- `SERIAL_PORT_WEIGHING_SCALE_COM`: 电子秤串口号
- `SERIAL_PORT_WEIGHING_SCALE_BAUD`: 电子秤波特率
- `SERIAL_PORT_WEIGHING_SCALE_DATA_PARSE_TYPE`: 数据解析类型

## 日志系统

### 日志分类
- **网络日志**: `LogMgr::getInst()->logger_net_`
- **事件日志**: `LogMgr::getInst()->logger_evt_`
- **操作日志**: 记录用户操作和系统状态变化

### 日志实现
- **日志管理器**: [`CPP/LogModule/LogManager.h`](../CPP/LogModule/LogManager.h)
- **Qt日志**: [`CPP/LogModule/QtLog.h`](../CPP/LogModule/QtLog.h)

## API接口汇总表

### 认证与系统接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/shopmanager/app/shop/staffLogin.do` | POST | 用户登录认证 | [`ShopControl.cpp:407`](../CPP/ControlModule/ShopControl.cpp#L407) |
| `/shopmanager/app/shop/getNowTime.do` | POST | 服务器时间同步 | [`main.cpp:48`](../CPP/main.cpp#L48) |

### 商品管理接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/shopmanager/pc/pcGoods.do` | POST | 商品数据同步 | [`GoodsControl.cpp:75`](../CPP/ControlModule/GoodsControl.cpp#L75) |
| `/shopmanager/app/shop/appNewGoods.do` | POST | 商品上传(旧版) | [`GoodsControl.cpp:670`](../CPP/ControlModule/GoodsControl.cpp#L670) |
| `/shopUpdate/goods/v2/addGoods.do` | POST | 商品上传(新版) | [`GoodsControl.cpp:785`](../CPP/ControlModule/GoodsControl.cpp#L785) |
| `/shopmanager/app/shop/pcSaleGoodsSearch.do` | POST | 商品捆绑查询 | [`GoodsPromotionCtrl.cpp:918`](../CPP/ControlModule/GoodsPromotionCtrl.cpp#L918) |
| `/harricane/payOnline/queryAllGoodsPromotion.do` | POST | 商品促销查询 | [`GoodsPromotionCtrl.cpp:647`](../CPP/ControlModule/GoodsPromotionCtrl.cpp#L647) |
| `/harricane/payOnline/queryOrderPromotion.do` | POST | 订单促销查询 | [`GoodsPromotionCtrl.cpp:1590`](../CPP/ControlModule/GoodsPromotionCtrl.cpp#L1590) |

### 支付处理接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/goBuy/cart/collectMoneyPay.do` | POST | 在线会员支付 | [`PayMethodControl.cpp:792`](../CPP/ControlModule/PayMethodControl.cpp#L792) |
| `/harricane/payOnline/v2/cashierPay.do` | POST | 订单上传/在线支付 | [`PayMethodControl.cpp:3415`](../CPP/ControlModule/PayMethodControl.cpp#L3415) |
| `/payOnline/v2/cashierPay_ny.do` | POST | 宁宇订单上传 | [`PayMethodControl.cpp:3534`](../CPP/ControlModule/PayMethodControl.cpp#L3534) |
| `/harricane/payOnline/queryOrderYT.do` | POST | 支付状态查询 | [`PayMethodControl.cpp:3725`](../CPP/ControlModule/PayMethodControl.cpp#L3725) |
| `/harricane/cuscheckout/recharge.do` | POST | 会员充值 | [`PayMethodControl.cpp:1863`](../CPP/ControlModule/PayMethodControl.cpp#L1863) |
| `/goBuy/cart/v2/queryPlatformCusInfo.do` | POST | 小程序支付 | [`PayMethodControl.cpp:2390`](../CPP/ControlModule/PayMethodControl.cpp#L2390) |
| `/goBuy/my/querySaleListDetailStatus.do` | POST | 小程序支付状态查询 | [`PayMethodControl.cpp:2620`](../CPP/ControlModule/PayMethodControl.cpp#L2620) |

### 人脸识别接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/shopmanager/app/shop/searchCustomerMsg.do` | POST | 人脸识别查询会员 | [`PayMethodControl.cpp:693`](../CPP/ControlModule/PayMethodControl.cpp#L693) |

### 会员管理接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/shopUpdate/cuscheckout/findCusById.do` | POST | 会员信息查询 | [`MemberControl.cpp:256`](../CPP/ControlModule/MemberControl.cpp#L256) |
| `/harricane/cuscheckout/addCus.do` | POST | 会员注册/添加 | [`MemberControl.cpp:574`](../CPP/ControlModule/MemberControl.cpp#L574) |
| `/harricane/cuscheckout/deleteCus.do` | POST | 会员删除 | [`MemberControl.cpp:652`](../CPP/ControlModule/MemberControl.cpp#L652) |
| `/shopUpdate/cuscheckout/editCus.do` | POST | 会员信息编辑 | [`MemberControl.cpp:794`](../CPP/ControlModule/MemberControl.cpp#L794) |

### 订单管理接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/shopmanager/pc/queryTodayLists.do` | POST | 销售记录查询 | [`OrderControl.cpp:296`](../CPP/ControlModule/OrderControl.cpp#L296) |
| `/shopmanager/pc/pcQuerySaleList.do` | POST | 网单查询 | [`NetOrderControl.cpp:20`](../CPP/ControlModule/NetOrderControl.cpp#L20) |
| `/shopmanager/pc/selectOnlineOrderCountByStatus.do` | POST | 订单状态统计 | [`NetOrderControl.cpp:76`](../CPP/ControlModule/NetOrderControl.cpp#L76) |
| `/shopmanager/pc/getListCountByStatus.do` | POST | 订单状态统计(新) | [`NetOrderControl.cpp:145`](../CPP/ControlModule/NetOrderControl.cpp#L145) |
| `/shopmanager/ret/addNewRetRecord.do` | POST | 退货处理 | [`OrderControl.cpp:682`](../CPP/ControlModule/OrderControl.cpp#L682) |
| `/shopmanager/pc/queryGoodsSaleStatistics.do` | POST | 商品销售统计 | [`OrderControl.cpp:454`](../CPP/ControlModule/OrderControl.cpp#L454) |
| `/shopmanager/pc/pcStaffSignOutForBoss.do` | POST | 交班记录 | [`OrderControl.cpp:573`](../CPP/ControlModule/OrderControl.cpp#L573) |

### 配送管理接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/peisong/createOrder.do` | POST | 创建配送订单 | [`NetOrderControl.cpp:492`](../CPP/ControlModule/NetOrderControl.cpp#L492) |
| `/peisong/order/shopCourierList.do` | POST | 获取配送员列表 | [`NetOrderControl.cpp:442`](../CPP/ControlModule/NetOrderControl.cpp#L442) |

### 供应商管理接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/harricane/html/supplier/querySupplierList.do` | POST | 查询供应商列表 | [`SupplierControl.cpp:365`](../CPP/ControlModule/SupplierControl.cpp#L365) |

### 店铺管理接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/harricane/payOnline/queryShopBeans.do` | POST | 查询店铺积分 | [`ShopControl.cpp:1226`](../CPP/ControlModule/ShopControl.cpp#L1226) |

### 组合支付接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/harricane/payOnline/yiTongPaySaleStatus.do` | POST | 易通支付状态查询 | [`PayMethodControl.cpp:4337`](../CPP/ControlModule/PayMethodControl.cpp#L4337) |

### 系统升级接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `https://platform.allscm.net/gw/platformBaseWeb/noAuth/pc/checkVersion` | POST | 检查版本更新(新) | [`UpdateCtrl.cpp:75`](../CPP/ControlModule/UpdateCtrl.cpp#L75) |
| `/upgrade/pc/checkVersion` | POST | 检查版本更新(旧) | [`UpdateCtrl.cpp:197`](../CPP/ControlModule/UpdateCtrl.cpp#L197) |
| `https://platform.allscm.net/gw/platformBaseWeb/noAuth/pc/upgradeFinish` | POST | 升级完成通知 | [`UpdateCtrl.cpp:416`](../CPP/ControlModule/UpdateCtrl.cpp#L416) |

### 数据统计分析接口
| 接口路径 | 方法 | 功能描述 | 实现位置 |
|---------|------|----------|----------|
| `/shopmanager/pc/mainMessageForPC.do` | POST | 主页数据概览 | [`checkData.cpp:34`](../CPP/CheckDataModule/checkData.cpp#L34) |
| `/shopmanager/pc/querySaleTotalByHour.do` | POST | 24小时营业额分布 | [`checkData.cpp:130`](../CPP/CheckDataModule/checkData.cpp#L130) |
| `/shopmanager/pc/overviewOfStock.do` | POST | 库存指标概览 | [`checkData.cpp:178`](../CPP/CheckDataModule/checkData.cpp#L178) |
| `/shopmanager/pc/salesTrend.do` | POST | 销售额走势图 | [`checkData.cpp:324`](../CPP/CheckDataModule/checkData.cpp#L324) |
| `/shopmanager/pc/queryUnmarketableTop5.do` | POST | 滞销商品TOP5 | [`checkData.cpp:502`](../CPP/CheckDataModule/checkData.cpp#L502) |

## 通信协议汇总

### HTTP通信
- **协议**: HTTP/HTTPS
- **数据格式**: JSON / Form-urlencoded
- **认证方式**: 基于用户账号密码和MAC地址
- **超时设置**: 3秒(默认)
- **重试机制**: 支持自动重试

### MQTT通信
- **服务器**: **************:1883
- **主题**: win_qt_cash_[MAC地址] (动态生成)
- **QoS**: 2
- **保活时间**: 3秒
- **重连间隔**: 13秒
- **客户端ID**: Market + MAC地址
- **遗嘱主题**: win_qt_cash_1.0
- **遗嘱消息**: 设备关闭通知
- **清理会话**: true (连接时), false (重连时)

### 串口通信
- **设备类型**: 电子秤、打印机、条码标签秤
- **通信协议**: RS232/RS485
- **数据格式**: 厂商自定义协议
- **波特率**: 可配置(常用9600, 19200, 38400)

## 总结

BUYHOO收银系统通过多种通信方式实现了完整的零售业务流程：

### 通信方式统计
1. **HTTP接口**: 处理业务数据的增删改查，共计40+个API接口
   - 认证与系统接口: 2个
   - 商品管理接口: 6个
   - 支付处理接口: 7个
   - 人脸识别接口: 1个
   - 会员管理接口: 4个
   - 订单管理接口: 7个
   - 配送管理接口: 2个
   - 供应商管理接口: 1个
   - 店铺管理接口: 1个
   - 组合支付接口: 1个
   - 系统升级接口: 3个
   - 数据统计分析接口: 5个

2. **MQTT通信**: 实现实时消息推送和设备状态同步
   - 支持10+种业务消息类型
   - 动态主题生成机制
   - 自动重连和遗嘱消息

3. **串口通信**: 连接硬件设备
   - 电子秤通信
   - 打印机通信
   - 条码标签秤通信

4. **人脸识别**: 基于OpenCV的本地处理 + 服务器比对

5. **网络监控**: 基于Ping的实时网络状态检测

### 系统特点
- **模块化设计**: 各业务模块相对独立，便于维护和扩展
- **多服务器支持**: 主服务器、小程序服务器、人脸识别服务器、外卖服务器分离
- **多支付方式**: 支持现金、银行卡、支付宝、微信、人脸支付等10+种支付方式
- **实时同步**: MQTT实现设备间实时数据同步
- **错误处理**: 完整的异常处理机制和日志记录
- **配置灵活**: 支持多种配置选项和运行模式

系统通过统一的网络层和数据层架构，保证了各模块间的高效协作和数据一致性，为零售业务提供了完整的技术支撑。
