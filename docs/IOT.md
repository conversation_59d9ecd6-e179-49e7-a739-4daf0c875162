# BUYHOO 收银系统 IOT 设备通信文档

## 目录

- [概述](#概述)
- [IOT设备架构](#iot设备架构)
- [核心IOT业务流程](#核心iot业务流程)
  - [1. 收银硬件设备交互流程](#1-收银硬件设备交互流程)
  - [2. 智能识别设备流程](#2-智能识别设备流程)
  - [3. 网络通信设备流程](#3-网络通信设备流程)
  - [4. 外围辅助设备流程](#4-外围辅助设备流程)
- [设备通信协议详解](#设备通信协议详解)
- [设备状态监控](#设备状态监控)
- [错误处理与故障恢复](#错误处理与故障恢复)
- [设备配置管理](#设备配置管理)
- [设备兼容性与部署要求](#设备兼容性与部署要求)
- [IOT设备集成时序图](#iot设备集成时序图)
- [总结](#总结)

## 概述

BUYHOO收银系统集成了多种IOT设备和外部硬件，通过串口通信、网络通信、USB通信等方式实现智能收银功能。本文档详细描述了系统中涉及的IOT设备通信接口、业务流程和技术实现。

## IOT设备架构

### 设备分类

#### 1. 收银核心设备
- **电子秤**: 商品称重和价格计算
- **打印机**: 小票打印和标签打印
- **条码标签秤**: 条码生成和商品标签打印

#### 2. 智能识别设备
- **摄像头**: 人脸识别和商品识别
- **扫码枪**: 条码扫描(通过键盘事件模拟)

#### 3. 网络通信设备
- **MQTT客户端**: 实时消息推送
- **网络状态监控**: 连接状态检测

#### 4. 外围辅助设备
- **TTS语音合成**: 语音播报
- **音效播放**: 操作提示音
- **虚拟键盘**: 触屏输入支持

### 设备管理架构
```cpp
// 设备控制管理器 - CPP/ControlModule/ControlManager.h
class ControlManager {
private:
    MqttControl           *mqtt_control_;            // MQTT通信控制
    CameraControl         *camera_control_;          // 摄像头控制
    PrinterControl        *printer_control_;         // 打印机控制
    WeightingScaleControl *weighting_scale_control_; // 电子秤控制
    BarcodeLabelScale     *barcode_label_scale_;     // 条码标签秤控制
    TtsControl            *tts_control_;             // TTS语音控制
    SoundCtrl             *sound_ctrl_;              // 音效控制
    NetStatusCtrl         *net_status_ctrl_;         // 网络状态控制
};
```

## 核心IOT业务流程

### 1. 收银硬件设备交互流程

#### 1.1 电子秤称重流程
**设备**: 电子秤 (串口通信)
**实现**: [`CPP/ControlModule/WeightingScaleControl.cpp`](../CPP/ControlModule/WeightingScaleControl.cpp)

**业务流程**:
1. **设备初始化**
   ```cpp
   // 串口配置 - WeightingScaleControl.cpp:24
   weighting_scale_ = new QSerialPort(this);
   weighting_scale_->setDataBits(QSerialPort::Data8);
   weighting_scale_->setParity(QSerialPort::NoParity);
   weighting_scale_->setStopBits(QSerialPort::OneStop);
   weighting_scale_->setBaudRate(baud_.toInt());
   weighting_scale_->setPortName(com_);
   ```

2. **重量数据读取**
   - 串口数据接收: `connect(weighting_scale_, &QSerialPort::readyRead, this, &WeightingScaleControl::readDataSlot)`
   - 数据解析: 支持多种解析类型 (`setParseType()`)
   - 重量稳定检测: 333ms稳定时间检测

3. **称重操作**
   - 去皮功能: `setQupi(double weight)`
   - 零点校准: `setZeroOut()`
   - 重量获取: `getLastWeightKg()`

**配置参数**:
- 串口号: `SERIAL_PORT_WEIGHING_SCALE_COM`
- 波特率: `SERIAL_PORT_WEIGHING_SCALE_BAUD`
- 解析类型: `SERIAL_PORT_WEIGHING_SCALE_DATA_PARSE_TYPE`

#### 1.2 打印机打印流程
**设备**: 热敏打印机 (系统打印机接口)
**实现**: [`CPP/ControlModule/PrinterControl.cpp`](../CPP/ControlModule/PrinterControl.cpp)
**工作线程**: [`CPP/WorkerModule/PrinterWorker.cpp`](../CPP/WorkerModule/PrinterWorker.cpp)

**业务流程**:
1. **打印机检测**
   ```cpp
   // 打印机有效性检查 - PrinterControl.cpp:354
   if (!isPrinterValid(ticket_printer_name_)) return;
   ```

2. **打印任务类型**
   - 普通订单打印: `printOrder()`
   - 餐饮订单打印: `printOrder4Food()`
   - 组合支付打印: `printOrderCombine()`
   - 网单打印: `printNetOrder()`

3. **打印参数配置**
   ```cpp
   // 打印参数设置 - PrinterControl.cpp:367
   QPrinter printer;
   printer.setPrinterName(ticket_printer_name_);
   printer.setResolution(mm2Dpi(dpi_mm));
   printer.setColorMode(QPrinter::GrayScale);
   printer.setPageSize(QPageSize::Custom);
   ```

#### 1.3 条码标签秤通信流程
**设备**: 条码标签秤 (网络通信/动态库调用)
**实现**: [`CPP/ControlModule/BarcodeLabelScale.cpp`](../CPP/ControlModule/BarcodeLabelScale.cpp)

**支持的秤类型**:
1. **托利多秤** (网络通信)
   ```cpp
   // 托利多秤通信 - BarcodeLabelScale.cpp:1111
   bool send2ToledoScaleSingleGoodsWizard(std::vector<NetInfoObj> ip_vec, QString goods_barcode) {
       createToledoDataSingle(goods_barcode);
       createToledoCommand();
       createToledoDeviceList(ip_vec);
       createToledoTask();
       execToledoTask();
   }
   ```

2. **大华秤** (动态库调用)
   ```cpp
   // 大华秤通信 - BarcodeLabelScale.cpp:1122
   bool sendPluFile2Scale_DaHua() {
       Fun open = (Fun)lib_dahua_.resolve("dhSendPluDefault");
       if (open) {
           result = open(); // 调用dll中的函数
           return result == 0;
       }
   }
   ```

3. **顶尖条码标签秤** (动态库调用)
   ```cpp
   // 顶尖秤通信 - BarcodeLabelScale.cpp:1025
   pAclasSDKInitialize Initialize = (pAclasSDKInitialize)GetProcAddress(h_module_dingjian_, "AclasSDK_Initialize");
   ```

### 2. 智能识别设备流程

#### 2.1 摄像头控制流程
**设备**: USB摄像头
**实现**: [`CPP/ControlModule/CameraControl.cpp`](../CPP/ControlModule/CameraControl.cpp)
**工作线程**: [`CPP/WorkerModule/CameraWorker2.cpp`](../CPP/WorkerModule/CameraWorker2.cpp)

**业务流程**:
1. **摄像头初始化**
   ```cpp
   // 摄像头配置 - CameraControl.cpp:42
   camera_4_face_  = ConfigTool::getInstance()->getSetting(ConfigEnum::kCamera4Face).toString();
   camera_4_goods_ = ConfigTool::getInstance()->getSetting(ConfigEnum::kCamera4Goods).toString();
   ```

2. **人脸识别流程**
   - 人脸摄像头开启: `openFaceCamera()`
   - 图像捕获: `CameraWorker2::process()`
   - 人脸检测: [`CPP/WorkerModule/FaceRecognizer.cpp`](../CPP/WorkerModule/FaceRecognizer.cpp)
   - 服务器比对: `POST /shopmanager/app/shop/searchCustomerMsg.do`

3. **商品识别流程**
   ```cpp
   // 商品识别 - CameraControl.cpp:182
   RecognitionWorker::processImgMat(selected_mat, device, model, result_list);
   emit sigSendGoodsResult(result_list);
   ```

#### 2.2 人脸识别技术流程
**实现**: [`CPP/WorkerModule/FaceRecognizer.cpp`](../CPP/WorkerModule/FaceRecognizer.cpp)

**技术栈**: OpenCV + 级联分类器

**处理流程**:
1. **图像预处理**
   ```cpp
   // 图像预处理 - FaceRecognizer.cpp:67
   Mat preProcessImage(cv::Mat input) {
       resize(input, input, Size(), 0.5, 0.5, INTER_AREA);
       cvtColor(input, result, COLOR_BGR2GRAY);
       equalizeHist(result, result); // 直方图均衡化
   }
   ```

2. **人脸检测**
   ```cpp
   // 人脸检测 - FaceRecognizer.cpp:56
   cascade_classifier.detectMultiScale(result, *faces, 1.1, 2, 0 | CV_HAL_CMP_GE, 
                                      Size(100, 100), Size(200, 200));
   ```

3. **人脸特征提取和比对**
   - 本地特征提取
   - 服务器端比对 (`http://face.buyhoo.cc/`)
   - 返回会员信息

#### 2.3 商品识别技术流程
**实现**: [`CPP/WorkerModule/RecognitionWorker.cpp`](../CPP/WorkerModule/RecognitionWorker.cpp)

**技术栈**: PyTorch + ResNeXt101模型

**处理流程**:
1. **图像标准化**
   ```cpp
   // 图像标准化 - RecognitionWorker.cpp:92
   Mat setNorm(Mat &img) {
       Mat img_resize = pilResize(img_rgb, 256);
       Mat img_crop   = pilCropCenter(img_resize, 224);
       img_crop.convertTo(image_resized_float, CV_32F, 1.0 / 255.0);
   }
   ```

2. **模型推理**
   ```cpp
   // 模型推理 - RecognitionWorker.cpp:124
   int processImgMat(cv::Mat image, torch::Device &device, 
                     torch::jit::script::Module &model, 
                     std::vector<std::tuple<int, float>> &result_list) {
       auto img_tensor = torch::from_blob(image_resized_merge.data, {224, 224, 3}, torch::kFloat32);
       torch::Tensor prob = model.forward(inputs).toTensor();
       auto top_k = torch::topk(soft_max, top_key_num);
   }
   ```

### 3. 网络通信设备流程

#### 3.1 MQTT实时通信流程
**实现**: [`CPP/ControlModule/MqttControl.cpp`](../CPP/ControlModule/MqttControl.cpp)

**连接配置**:
```cpp
// MQTT配置 - MqttControl.h:17
const QString HOST_MQTT  = "**************";
const quint16 PORT_MQTT  = 1883;
const QString TOPIC_MQTT = "win_qt_cash_1.0";
```

**业务流程**:
1. **连接建立**
   ```cpp
   // 连接初始化 - MqttControl.cpp:36
   setClientId("Market" + Utils::getHostMacAddressWithoutColon());
   setHostname(host);
   setPort(port);
   setKeepAlive(3);
   ```

2. **消息订阅**
   ```cpp
   // 主题订阅 - MqttControl.cpp:66
   auto subscription = subscribe(getTopic(), 2);
   // 动态主题: win_qt_cash_[MAC地址]
   ```

3. **消息处理**
   - 新订单通知: `msg_net_new` → 语音提醒 + 自动打印
   - 商品更新: `msg_goods_update` → 刷新商品数据 + 同步到条码秤
   - 设备状态: `msg_init`, `msg_shutdown`

#### 3.2 网络状态监控流程
**实现**: [`CPP/ControlModule/NetStatusCtrl.cpp`](../CPP/ControlModule/NetStatusCtrl.cpp)
**工作线程**: [`CPP/WorkerModule/NetStatusWorker.cpp`](../CPP/WorkerModule/NetStatusWorker.cpp)

**监控机制**:
```cpp
// Ping检测 - NetStatusWorker.cpp:21
int bResult = ping_.Ping("www.baidu.com");
if (bResult >= 0 && bResult <= 2000) {
    emit signalsNetwork_States(true);  // 网络正常
} else {
    emit signalsNetwork_States(false); // 网络异常
}
```

**状态管理**:
- 检测间隔: 1秒
- 超时阈值: 2000毫秒
- 状态码: "101"(正常), "102"(断开)

### 4. 外围辅助设备流程

#### 4.1 TTS语音合成流程
**实现**: [`CPP/ControlModule/TtsControl.cpp`](../CPP/ControlModule/TtsControl.cpp)

**技术栈**: Qt TextToSpeech

**业务流程**:
1. **TTS引擎初始化**
   ```cpp
   // TTS初始化 - TtsControl.cpp:19
   QStringList engines = QTextToSpeech::availableEngines();
   speech_ = new QTextToSpeech(cur_engine_name_, this);
   ```

2. **语音播报场景**
   - 新订单提醒: "您有网单请及时处理"
   - 测试语音: "欢迎使用百货收银"
   - 自定义语音播报

3. **语音配置**
   - 引擎选择: `setCurEngineByName()`
   - 语言设置: `setCurLocaleByName()`
   - 声音选择: `setCurVoiceByName()`

#### 4.2 音效控制流程
**实现**: [`CPP/ControlModule/SoundCtrl.cpp`](../CPP/ControlModule/SoundCtrl.cpp)

**音效类型**:
```cpp
// 音效资源 - SoundCtrl.cpp:7
sound_di_.reset(new QSound(":/Sound/di.wav"));      // 提示音
sound_click_.reset(new QSound(":/Sound/click.wav")); // 按键音
```

**播放控制**:
- 提示音播放: `playDi()` (可配置开关)
- 按键音播放: `playClick()` (可配置开关)

#### 4.3 虚拟键盘事件流程
**实现**: [`CPP/KeyEvent/KeyEvent.cpp`](../CPP/KeyEvent/KeyEvent.cpp)
**头文件**: [`CPP/KeyEvent/KeyEvent.h`](../CPP/KeyEvent/KeyEvent.h)

**功能**: 模拟键盘输入，支持扫码枪等设备

**事件处理**:
```cpp
// 键盘事件模拟 - KeyEvent.cpp:33
void KeyEvent::sendEvent(QVariant key_text) {
    auto data = key_text.toString().toUpper();

    if (data == "Q") {
        keybd_event(81, 0, 0, 0);  // 按下
        keybd_event(81, 0, 2, 0);  // 释放
    }
    // ... 其他按键处理
}
```

**扫码枪集成**:
- 扫码枪通过HID接口模拟键盘输入
- 系统自动接收条码数据
- 支持各种条码格式 (EAN13, Code128等)
- 自动触发商品查询和添加

**支持功能**:
- 字母数字输入
- 特殊按键: SHIFT, CAPS_LOCK, BACKSPACE, ENTER
- 方向键: UP, DOWN, LEFT, RIGHT
- 系统功能: DESKTOP (切换到桌面)
- 大小写锁定状态检测: `getCapsLockStatus()`

## 设备通信协议详解

### 串口通信协议

#### 电子秤通信协议
**通信参数**:
- 数据位: 8位
- 校验位: 无
- 停止位: 1位
- 波特率: 可配置 (9600/19200/38400)

**数据格式**: 厂商自定义协议，支持多种解析类型

**支持的电子秤品牌**:
- 通用串口协议电子秤
- 支持多种数据解析格式
- 可配置串口参数适配不同厂商

### 网络通信协议

#### MQTT协议参数
- **协议版本**: MQTT 3.1.1
- **QoS级别**: 2 (确保消息送达)
- **保活时间**: 3秒
- **重连间隔**: 13秒
- **清理会话**: 连接时true，重连时false
- **遗嘱消息**: 设备异常断开时自动发送关闭通知

#### HTTP协议参数
- **协议版本**: HTTP/1.1
- **数据格式**: JSON
- **超时时间**: 3秒
- **重试机制**: 支持自动重试
- **认证方式**: 基于用户凭证和设备MAC地址

### USB通信协议

#### 摄像头通信
- **接口**: USB Video Class (UVC)
- **图像格式**: RGB/BGR
- **分辨率**: 可配置 (推荐640x480或更高)
- **帧率**: 30fps
- **支持设备**: 标准UVC摄像头、USB网络摄像头

### 动态库接口协议

#### 条码标签秤厂商接口
- **大华秤**: `dhSendPluDefault()` 函数接口
- **顶尖秤**: `AclasSDK_Initialize()` 等SDK接口
- **托利多秤**: 网络TCP通信协议

## 设备状态监控

### 设备连接状态
```cpp
// 设备状态枚举 - EnumTool.h
enum class DeviceStatus {
    DEVICE_STATUS__CONNECTED,     // 设备已连接
    DEVICE_STATUS__DISCONNECTED,  // 设备断开
    DEVICE_STATUS__ERROR,         // 设备错误
    DEVICE_STATUS__UNKNOWN        // 状态未知
};
```

### 状态监控机制
1. **电子秤状态**: 串口连接状态检测
2. **打印机状态**: 系统打印机可用性检测
3. **摄像头状态**: 设备枚举和连接检测
4. **网络状态**: Ping检测机制
5. **MQTT状态**: 连接状态和心跳检测

## 错误处理与故障恢复

### 设备连接错误处理
```cpp
// 电子秤连接错误 - WeightingScaleControl.cpp:35
if (!weighting_scale_->isOpen()) {
    LOG_EVT_ERROR("串口打开失败");
    setIsConnected(false);
} else {
    LOG_EVT_INFO("串口打开成功");
    setIsConnected(true);
}
```

### 自动重连机制
```cpp
// MQTT自动重连 - MqttControl.cpp:23
timer_reconn.setInterval(13000);
connect(&timer_reconn, &QTimer::timeout, [&]() {
    setCleanSession(false);
    connectToHost();
});
```

### 故障恢复策略
1. **设备重新初始化**: 检测到设备断开时自动重新初始化
2. **网络重连**: 网络断开时自动重连
3. **数据缓存**: 网络异常时本地缓存数据
4. **降级处理**: 关键设备故障时提供备用方案

## 设备配置管理

### 配置项分类
```cpp
// 设备配置枚举 - ConfigEnum.h
enum class ConfigEnum {
    // 电子秤配置
    SERIAL_PORT_WEIGHING_SCALE_COM,           // 串口号
    SERIAL_PORT_WEIGHING_SCALE_BAUD,          // 波特率
    SERIAL_PORT_WEIGHING_SCALE_DATA_PARSE_TYPE, // 解析类型
    
    // 摄像头配置
    kCamera4Face,                             // 人脸摄像头
    kCamera4Goods,                            // 商品摄像头
    
    // TTS配置
    TTS_ENGINE,                               // TTS引擎
    TTS_LOCALE,                               // 语言设置
    TTS_VOICE,                                // 声音设置
    
    // 音效配置
    IS_PLAY_PROMPT_SOUND,                     // 提示音开关
    IS_PLAY_KEYBOARD_SOUND,                   // 按键音开关
    IS_PLAY_MINI_PROGRAM_TAKE_ORDERS,         // 网单语音提醒
    
    // 打印配置
    IS_AUTO_TAKE_ORDERS_AND_PRINT,            // 自动接单打印
};
```

### 配置管理接口
```cpp
// 配置工具 - ConfigTool.h
class ConfigTool {
public:
    QVariant getSetting(ConfigEnum key);
    void setSetting(ConfigEnum key, QVariant value);
    bool isPlayPromptSound();
    bool isPlayKeyboardSound();
    bool isPlayMiniProgramTakeOrders();
};
```

## 设备兼容性与部署要求

### 硬件兼容性

#### 电子秤兼容性
**支持的通信方式**:
- 串口通信 (RS232/RS485)
- 标准波特率: 9600, 19200, 38400, 57600, 115200

**兼容的电子秤品牌**:
- 支持标准串口协议的电子秤
- 可通过配置解析类型适配不同数据格式
- 支持自定义协议解析

**部署要求**:
```cpp
// 电子秤部署检查清单
1. 确认串口连接正常
2. 配置正确的串口号和波特率
3. 选择匹配的数据解析类型
4. 测试重量数据读取和稳定性
```

#### 打印机兼容性
**支持的打印机类型**:
- Windows系统兼容的热敏打印机
- 支持58mm和80mm纸宽
- 标准ESC/POS指令集

**推荐打印机品牌**:
- 佳博(Gprinter)
- 新北洋(SNBC)
- 爱普生(Epson)
- 其他支持Windows驱动的热敏打印机

**部署要求**:
```cpp
// 打印机部署检查清单
1. 安装打印机驱动程序
2. 设置为系统默认打印机或配置打印机名称
3. 测试打印功能和纸张规格
4. 配置打印份数和自动打印选项
```

#### 摄像头兼容性
**支持的摄像头类型**:
- USB UVC标准摄像头
- 分辨率要求: 640x480或更高
- 支持RGB/BGR图像格式

**推荐摄像头规格**:
- 分辨率: 1280x720 (720P) 或更高
- 帧率: 30fps
- 自动对焦功能
- 良好的低光性能(用于人脸识别)

**部署要求**:
```cpp
// 摄像头部署检查清单
1. 确认摄像头被系统正确识别
2. 配置人脸摄像头和商品摄像头
3. 调整摄像头位置和角度
4. 测试图像质量和识别效果
```

#### 条码标签秤兼容性
**支持的品牌和型号**:
- **托利多(Toledo)**: 网络通信型号
- **大华**: 支持DLL接口的型号
- **顶尖(Aclas)**: 支持SDK的型号

**部署要求**:
```cpp
// 条码标签秤部署要求
1. 托利多秤: 配置网络IP地址
2. 大华秤: 安装对应的DLL库文件
3. 顶尖秤: 安装Aclas SDK
4. 测试商品数据同步功能
```

### 软件环境要求

#### 操作系统要求
- **操作系统**: Windows 10/11 (64位)
- **Qt版本**: Qt 5.15或更高版本
- **编译器**: MSVC 2019或更高版本

#### 依赖库要求
```cpp
// 核心依赖库
1. Qt5Core, Qt5Gui, Qt5Widgets, Qt5Quick
2. Qt5SerialPort (串口通信)
3. Qt5Network (网络通信)
4. Qt5Multimedia (音频播放)
5. Qt5TextToSpeech (TTS语音)
6. OpenCV (图像处理)
7. PyTorch C++ (商品识别)
8. MQTT客户端库
```

#### 网络环境要求
- **MQTT服务器**: **************:1883 (可配置)
- **HTTP服务器**: buyhoo.cc (可配置)
- **人脸识别服务器**: face.buyhoo.cc
- **网络带宽**: 建议10Mbps或更高
- **网络延迟**: 建议小于100ms

### 部署配置指南

#### 设备初始化配置
```cpp
// 设备配置初始化流程
1. 电子秤配置:
   - 设置串口号 (COM1, COM2, ...)
   - 设置波特率 (通常9600或19200)
   - 选择数据解析类型

2. 打印机配置:
   - 选择打印机名称
   - 设置打印份数
   - 配置自动打印选项

3. 摄像头配置:
   - 选择人脸识别摄像头
   - 选择商品识别摄像头
   - 调整图像参数

4. 网络配置:
   - 配置MQTT服务器地址
   - 设置HTTP服务器地址
   - 测试网络连接
```

#### 性能优化建议
```cpp
// 性能优化配置
1. 硬件优化:
   - 使用SSD硬盘提高启动速度
   - 配置足够的内存 (建议8GB或更高)
   - 使用有线网络连接保证稳定性

2. 软件优化:
   - 关闭不必要的后台程序
   - 配置合适的线程数量
   - 启用硬件加速 (如GPU加速)

3. 网络优化:
   - 配置静态IP地址
   - 优化网络QoS设置
   - 使用专用网络避免干扰
```

## IOT设备集成时序图

### 收银流程中的设备协作
```mermaid
sequenceDiagram
    participant UI as 收银界面
    participant CM as ControlManager
    participant Scale as 电子秤
    participant Camera as 摄像头
    participant Printer as 打印机
    participant MQTT as MQTT服务器
    participant TTS as TTS语音

    Note over UI,TTS: 商品称重和识别流程
    UI->>CM: 启动商品扫描
    CM->>Scale: 开始称重
    Scale-->>CM: 返回重量数据
    CM->>Camera: 启动商品识别
    Camera-->>CM: 返回识别结果
    CM->>UI: 更新商品信息

    Note over UI,TTS: 支付和打印流程
    UI->>CM: 发起支付
    CM->>Printer: 打印小票
    Printer-->>CM: 打印完成
    CM->>MQTT: 发布支付状态
    MQTT-->>CM: 确认消息发送
    CM->>TTS: 播放完成提示
    TTS-->>CM: 语音播放完成
    CM->>UI: 更新界面状态
```

### 人脸支付设备交互流程
```mermaid
sequenceDiagram
    participant UI as 支付界面
    participant CC as CameraControl
    participant FR as FaceRecognizer
    participant Server as 人脸服务器
    participant PC as PayMethodControl
    participant TTS as TTS语音

    UI->>CC: 启动人脸识别
    CC->>FR: 开启人脸摄像头
    FR->>FR: 图像预处理
    FR->>FR: 人脸检测
    FR-->>CC: 返回人脸图像
    CC->>Server: 上传人脸数据
    Server-->>CC: 返回会员信息
    CC->>PC: 执行人脸支付
    PC->>TTS: 播放支付结果
    TTS-->>PC: 语音播放完成
    PC->>UI: 更新支付状态
```

### MQTT消息驱动的设备联动
```mermaid
sequenceDiagram
    participant MQTT as MQTT服务器
    participant MC as MqttControl
    participant PC as PrinterControl
    participant TTS as TTS语音
    participant BLS as BarcodeLabelScale

    Note over MQTT,BLS: 新订单消息处理
    MQTT->>MC: 推送新订单消息(msg_net_new)
    MC->>TTS: 触发语音提醒
    TTS-->>MC: "您有网单请及时处理"
    MC->>PC: 自动打印网单
    PC-->>MC: 打印完成

    Note over MQTT,BLS: 商品更新消息处理
    MQTT->>MC: 推送商品更新(msg_goods_update)
    MC->>MC: 更新本地商品数据
    MC->>BLS: 同步商品到条码秤
    BLS-->>MC: 同步完成
    MC->>UI: 刷新商品显示
```

### 网络状态监控和故障处理
```mermaid
sequenceDiagram
    participant NSC as NetStatusCtrl
    participant NSW as NetStatusWorker
    participant Ping as Ping工具
    participant UI as 界面状态
    participant MQTT as MQTT客户端

    loop 每秒检测
        NSC->>NSW: 启动网络检测
        NSW->>Ping: Ping www.baidu.com
        Ping-->>NSW: 返回延迟时间

        alt 网络正常 (≤2000ms)
            NSW->>NSC: 网络状态: 正常(101)
            NSC->>UI: 更新网络图标(绿色)
        else 网络异常 (>2000ms)
            NSW->>NSC: 网络状态: 异常(102)
            NSC->>UI: 更新网络图标(红色)
            NSC->>MQTT: 触发重连机制
        end
    end
```

## 总结

### IOT设备统计

BUYHOO收银系统集成了丰富的IOT设备和外部硬件，形成了完整的智能收银生态：

#### 1. 硬件设备分类 (8大类)
- **称重设备**: 电子秤 (串口通信)
- **打印设备**: 热敏打印机 (系统打印机API)
- **标签设备**: 条码标签秤 (网络通信/动态库调用)
- **识别设备**: 摄像头 (USB UVC)、扫码枪 (HID键盘事件)
- **通信设备**: MQTT客户端、网络状态监控 (TCP/IP)
- **音频设备**: TTS语音合成、音效播放 (Qt音频系统)
- **输入设备**: 虚拟键盘、触摸屏 (Windows API/Qt事件)
- **显示设备**: 主显示器、客显 (Qt/QML界面)

#### 2. 通信协议支持 (6种)
- **串口通信**: RS232/RS485 (电子秤)
- **USB通信**: UVC协议 (摄像头)
- **网络通信**: TCP/IP (条码标签秤、MQTT)
- **系统接口**: Windows打印机API (打印机)
- **动态库调用**: DLL接口 (特定品牌设备)
- **键盘事件**: Windows消息机制 (扫码枪模拟)

#### 3. 智能识别技术 (3种)
- **人脸识别**: OpenCV + 级联分类器 + 服务器比对
- **商品识别**: PyTorch + ResNeXt101模型 + 本地推理
- **条码识别**: 扫码枪硬件 + 键盘事件处理

#### 4. 业务流程集成 (6个核心流程)
- **收银称重流程**: 电子秤称重 → 商品识别 → 价格计算 → 界面更新
- **支付打印流程**: 支付处理 → 小票打印 → MQTT状态同步 → 语音提示
- **人脸支付流程**: 人脸捕获 → 图像预处理 → 服务器比对 → 支付执行
- **商品识别流程**: 摄像头捕获 → AI模型推理 → 结果匹配 → 自动添加
- **订单同步流程**: MQTT消息接收 → 语音提醒 → 自动打印 → 设备联动
- **网络监控流程**: 连接检测 → 状态更新 → 故障恢复 → 自动重连

### 系统特点

#### 技术特点
- **多协议支持**: 同时支持串口、USB、网络、系统API等多种通信方式
- **异步处理**: 所有设备操作都在独立线程中执行，避免界面阻塞
- **智能识别**: 集成深度学习模型，支持人脸和商品的智能识别
- **实时通信**: MQTT实现设备间实时消息推送和状态同步
- **故障恢复**: 完善的错误处理和自动重连机制

#### 业务特点
- **设备联动**: 通过MQTT消息实现多设备协同工作
- **智能提醒**: TTS语音和音效提供丰富的用户反馈
- **自动化处理**: 支持自动接单、自动打印等无人值守功能
- **配置灵活**: 所有设备参数都支持动态配置和热更新
- **扩展性强**: 模块化设计便于添加新的IOT设备

#### 可靠性保障
- **连接监控**: 实时监控所有设备的连接状态
- **数据校验**: 对设备返回的数据进行完整性校验
- **异常处理**: 完善的异常捕获和处理机制
- **日志记录**: 详细的设备操作日志便于问题排查
- **降级处理**: 关键设备故障时提供备用方案

### 技术架构优势

1. **统一管理**: ControlManager提供统一的设备管理接口
2. **线程安全**: 所有设备操作都通过Qt信号槽机制保证线程安全
3. **配置集中**: ConfigTool提供统一的配置管理
4. **状态同步**: 设备状态变化实时同步到界面
5. **扩展友好**: 新增设备只需实现标准接口即可集成

### IOT设备管理最佳实践

#### 设备部署建议
1. **设备连接顺序**: 先连接基础设备(电子秤、打印机)，再连接智能设备(摄像头)
2. **网络配置**: 优先配置有线网络，确保MQTT连接稳定
3. **设备测试**: 逐个测试设备功能，确保所有设备正常工作
4. **备份配置**: 保存设备配置文件，便于快速恢复

#### 维护和监控
1. **定期检查**: 每日检查设备连接状态和工作状态
2. **日志监控**: 关注设备错误日志，及时处理异常
3. **性能监控**: 监控设备响应时间和处理效率
4. **预防性维护**: 定期清洁设备，更新驱动程序

#### 故障排除指南
1. **设备无响应**: 检查连接线缆和驱动程序
2. **通信异常**: 检查网络连接和防火墙设置
3. **识别精度下降**: 清洁摄像头镜头，调整光照条件
4. **打印质量问题**: 检查打印纸和打印头状态

BUYHOO收银系统通过完善的IOT设备集成架构，实现了从传统收银到智能收银的全面升级，为零售业务提供了强大的硬件支撑和技术保障。系统的模块化设计和标准化接口，使得设备集成更加灵活高效，为未来的技术升级和功能扩展奠定了坚实基础。
