# BUYHOO 智能收银系统

![Version](https://img.shields.io/badge/version-4.1.2.7-blue.svg)
![Platform](https://img.shields.io/badge/platform-Windows-lightgrey.svg)
![Qt](https://img.shields.io/badge/Qt-5.15.2%2F6.x-green.svg)
![C++](https://img.shields.io/badge/C%2B%2B-17-orange.svg)

## 📋 项目概述

BUYHOO是一款基于Qt/QML开发的现代化智能收银系统，专为零售商店设计。系统集成了商品管理、收银结算、会员管理、库存管理、人脸识别支付等多种功能，支持多种支付方式，提供完整的零售解决方案。

## 🏗️ 技术架构

### 核心技术栈
- **开发语言**: C++17
- **UI框架**: Qt 5.15.2/6.x + QML
- **构建系统**: CMake 3.14+
- **图像处理**: OpenCV
- **网络通信**: Qt Network + MQTT
- **数据库**: SQLite
- **日志系统**: spdlog
- **JSON处理**: nlohmann/json

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   QML UI Layer  │    │  Control Layer  │    │   Data Layer    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • 收银界面      │◄──►│ • 商品控制      │◄──►│ • 商品数据      │
│ • 商品管理      │    │ • 支付控制      │    │ • 订单数据      │
│ • 会员管理      │    │ • 会员控制      │    │ • 会员数据      │
│ • 设置界面      │    │ • 购物车控制    │    │ • 配置数据      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Worker Layer   │
                    ├─────────────────┤
                    │ • 摄像头工作线程│
                    │ • 网络工作线程  │
                    │ • 打印工作线程  │
                    │ • 人脸识别线程  │
                    └─────────────────┘
```

## 🚀 核心功能模块

### 1. 商品管理模块 (GoodsControl)
- **商品信息管理**: 商品增删改查、分类管理
- **库存管理**: 实时库存监控、进销存统计
- **价格管理**: 多级价格体系、促销价格
- **条码管理**: 支持多种条码格式、称重商品

### 2. 收银结算模块 (PayMethodControl)
- **多种支付方式**:
  - 现金支付
  - 支付宝/微信扫码支付
  - 银行卡刷卡
  - 人脸识别支付
  - 会员储值卡
  - 组合支付
- **智能找零**: 自动计算找零金额
- **小票打印**: 支持多种打印机型号

### 3. 购物车管理 (ShopCartControl)
- **商品添加**: 扫码添加、手动添加
- **数量调整**: 支持称重商品、计件商品
- **价格调整**: 自定义价格、折扣设置
- **优惠券**: 支持多种优惠券类型

### 4. 会员管理模块 (MemberControl)
- **会员注册**: 支持多种注册方式
- **积分管理**: 积分累积、兑换
- **储值管理**: 充值、消费记录
- **人脸识别**: 人脸注册、识别登录

### 5. 网络订单模块 (NetOrderControl)
- **在线订单**: 处理网上商城订单
- **订单状态**: 待发货、待收货、已完成等
- **退款处理**: 支持在线退款申请

### 6. 硬件控制模块
- **摄像头控制** (CameraControl): 人脸识别、商品识别
- **打印机控制** (PrinterControl): 小票打印、标签打印
- **电子秤控制** (WeightingScaleControl): 称重商品处理
- **条码枪控制**: 商品扫码识别

### 7. 数据管理模块 (DataManager)
- **本地数据**: SQLite数据库存储
- **云端同步**: 实时数据同步
- **数据备份**: 自动备份机制
- **离线模式**: 支持离线收银

## 📁 项目结构

```
BUYHOO/
├── CPP/                          # C++源代码
│   ├── AdapterModule/            # 适配器模块(图像处理、绘图)
│   ├── CheckDataModule/          # 数据校验模块
│   ├── ConfModule/               # 配置管理模块
│   ├── ControlModule/            # 控制层模块
│   │   ├── GoodsControl.*        # 商品控制
│   │   ├── PayMethodControl.*    # 支付控制
│   │   ├── ShopCartControl.*     # 购物车控制
│   │   ├── MemberControl.*       # 会员控制
│   │   └── ...
│   ├── DataModule/               # 数据层模块
│   │   ├── DataManager.*         # 数据管理器
│   │   ├── GoodsData.*          # 商品数据
│   │   └── OrderData.*          # 订单数据
│   ├── NetModule/                # 网络通信模块
│   ├── WorkerModule/             # 工作线程模块
│   ├── LogModule/                # 日志模块
│   └── Utils/                    # 工具类
├── QML/                          # QML界面文件
├── Images/                       # 图片资源
├── Sound/                        # 音频资源
└── CMakeLists.txt               # CMake构建文件
```

## 🛠️ 编译构建

### 环境要求
- **操作系统**: Windows 7/10/11
- **编译器**: MSVC 2019/2022
- **Qt版本**: 5.15.2 或 6.x
- **CMake**: 3.14+
- **OpenCV**: 4.x

### 构建步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd BUYHOO
```

2. **安装依赖**
- 安装Qt开发环境
- 安装OpenCV库
- 配置CMake环境

3. **配置构建**
```bash
mkdir build
cd build
cmake .. -DCMAKE_PREFIX_PATH="C:/Qt/5.15.2/msvc2019_64"
```

4. **编译项目**
```bash
cmake --build . --config Release
```

## 🔧 配置说明

### 系统配置
- **数据库配置**: 本地SQLite数据库路径
- **网络配置**: 服务器地址、端口设置
- **硬件配置**: 摄像头、打印机、电子秤参数
- **支付配置**: 各支付渠道参数设置

### 功能开关
```cpp
#define BUYHOO_DEBUG        // 调试模式
#define BUYHOO_LOG          // 日志输出
#define _RECOGNITION_       // 人脸识别功能
// #define DISABLE_TTS      // 禁用语音播报
```

## 📊 系统特性

### 性能特点
- **高并发**: 支持多线程处理
- **低延迟**: 优化的数据处理流程
- **稳定性**: 完善的异常处理机制
- **扩展性**: 模块化设计，易于扩展

### 安全特性
- **数据加密**: 敏感数据加密存储
- **权限控制**: 多级权限管理
- **操作日志**: 完整的操作审计
- **备份恢复**: 自动数据备份

## 🎯 使用场景

- **便利店**: 快速收银、商品管理
- **超市**: 大量商品处理、会员管理
- **专卖店**: 精品商品、客户服务
- **餐饮店**: 点餐收银、外卖管理

## 📝 更新日志

### v4.1.2.7 (当前版本)
- 现金-支付组合支付下离线收银功能
- 离线展示、在线上传功能优化
- 离线下功能权限限制
- 离线订单时间修复

### 历史版本
- v4.1.x: 人脸识别支付功能
- v4.0.x: 网络订单管理
- v3.x.x: 基础收银功能

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

版权所有 © 2020-2025 山东影响力智能科技有限公司

## 📞 联系方式

- **公司**: 山东影响力智能科技有限公司
- **网站**: http://www.buyhoo.cc/
- **邮箱**: [联系邮箱]
- **电话**: [联系电话]

## 🙏 致谢

感谢所有为BUYHOO项目做出贡献的开发者和用户！

---

**注意**: 使用前请确保备份重要数据，避免数据丢失。
