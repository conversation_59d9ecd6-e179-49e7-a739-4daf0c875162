﻿<RCC>
    <qresource prefix="/">
        <file>QML/Common/PageSwitcher.qml</file>
        <file>QML/Common/TitleBar.qml</file>
        <file>QML/PayPage/GoodsList/Component/GoodsPriceText.qml</file>
        <file>QML/PayPage/GoodsList/Component/GoodsRect.qml</file>
        <file>QML/PayPage/GoodsList/Component/GoodsStockText.qml</file>
        <file>QML/PayPage/GoodsTabBar/GoodsTabBar.qml</file>
        <file>QML/PayPage/PayMethods/PayMethods.qml</file>
        <file>QML/PayPage/SearchBar/SearchBar.qml</file>
        <file>QML/PayPage/ShoppngCart/ShoppngCart.qml</file>
        <file>QML/PayPage/WeightDisplay/WeightDisplay.qml</file>
        <file>QML/PayPage/PayPage.qml</file>
        <file>QML/main.qml</file>
        <file>QML/NetOrderPage/NetOrderPage.qml</file>
        <file>QML/GodownPage/GodownPage.qml</file>
        <file>QML/MembershipPage/MembershipPage.qml</file>
        <file>QML/QueryPage/QueryPage.qml</file>
        <file>QML/StatisticsPage/StatisticsPage.qml</file>
        <file>QML/HandOverPage/HandOverPage.qml</file>
        <file>QML/SettingPage/Details/Setting__Main.qml</file>
        <file>QML/SettingPage/Details/Setting_Marketing.qml</file>
        <file>QML/SettingPage/Details/Setting_Online.qml</file>
        <file>QML/SettingPage/Details/Setting_Other.qml</file>
        <file>QML/SettingPage/Details/Setting_PriceTag.qml</file>
        <file>QML/SettingPage/Details/Setting_Profit.qml</file>
        <file>QML/SettingPage/Details/Setting_ShopConfig.qml</file>
        <file>QML/SettingPage/Details/Setting_ShopInfo.qml</file>
        <file>QML/SettingPage/Details/Setting_System.qml</file>
        <file>QML/SettingPage/Details/Setting_Tickets.qml</file>
        <file>QML/SettingPage/Details/Setting_Title.qml</file>
        <file>QML/SettingPage/Details/Setting_Weighing.qml</file>
        <file>QML/SettingPage/ListMenu.qml</file>
        <file>QML/SettingPage/SettingPageC.qml</file>
        <file>QML/LoginPage/LoginPage.qml</file>
        <file>QML/_Refactor/ConfirmBtn.qml</file>
        <file>QML/qmldir</file>
        <file>QML/Style.qml</file>
        <file>QML/PayPage/ShoppngCart/ShoppngCartItem.qml</file>
        <file>QML/MiniWindow.qml</file>
        <file>QML/Utils/CalendarC.qml</file>
        <file>QML/Utils/CheckBoxC.qml</file>
        <file>QML/Utils/ComboBoxC.qml</file>
        <file>QML/Utils/RadioBtnRectC.qml</file>
        <file>QML/Utils/RadioButtonRectangleC.qml</file>
        <file>QML/Utils/RectDebug.qml</file>
        <file>QML/Utils/Spacer.qml</file>
        <file>QML/Utils/SpinBoxC.qml</file>
        <file>QML/Utils/StyleC.js</file>
        <file>QML/Utils/SwitchButtonC.qml</file>
        <file>QML/Utils/SwitchButtonWithImageC.qml</file>
        <file>QML/Utils/TabButtonC.qml</file>
        <file>QML/Utils/TextAreaWithShadowC.qml</file>
        <file>QML/Utils/TextWithShadowBoxC.qml</file>
        <file>QML/Utils/TipC.qml</file>
        <file>QML/Utils/TipC1.qml</file>
        <file>QML/Utils/ToastC.qml</file>
        <file>QML/Utils/TriangleC.qml</file>
        <file>QML/Utils/TriangleC1.qml</file>
        <file>QML/Utils/TextStateC.qml</file>
        <file>QML/Utils/PopupC.qml</file>
        <file>QML/PayPage/PayMethods/PopupFace.qml</file>
        <file>QML/Utils/RadioBtnRectC2.qml</file>
        <file>QML/QueryPage/ContainSaleRecord.qml</file>
        <file>QML/QueryPage/ContainStockRecord.qml</file>
        <file>QML/Utils/ExpandableList.qml</file>
        <file>QML/NetOrderPage/ContainStoreOrders.qml</file>
        <file>QML/NetOrderPage/ContainRefundOrder.qml</file>
        <file>QML/MembershipPage/SearchC.qml</file>
        <file>QML/PayPage/ShoppngCart/TextInput4ShopCart.qml</file>
        <file>QML/PayPage/ShoppngCart/OrderDiscount.qml</file>
        <file>QML/PayPage/ShoppngCart/RadioBtnRectDiscount.qml</file>
        <file>QML/PayPage/ShoppngCart/PendingOrderDetail.qml</file>
        <file>QML/PayPage/ShoppngCart/CurrentOrder.qml</file>
        <file>QML/PayPage/GoodsList/PendingOrderList.qml</file>
        <file>QML/QueryPage/ContainHandoverRecord.qml</file>
        <file>QML/QueryPage/Search4QueryPage.qml</file>
        <file>QML/PayPage/PayMethods/PopupCashPay.qml</file>
        <file>QML/Utils/PopupConfirm.qml</file>
        <file>QML/QmlUtils.qml</file>
        <file>QML/PayPage/GoodsList/Component/PopupNoCodeGoods.qml</file>
        <file>QML/PayPage/GoodsList/Component/GoodsRectWithImg.qml</file>
        <file>QML/PayPage/GoodsQuickEdit/GoodsQuickEdit.qml</file>
        <file>QML/Utils/TextInputC.qml</file>
        <file>QML/Utils/CheckBoxC2.qml</file>
        <file>QML/PayPage/GoodsList/MemberShipList.qml</file>
        <file>QML/PayPage/GoodsList/MemberShipListOption.qml</file>
        <file>QML/PayPage/MemberInfo/MemberInfo.qml</file>
        <file>QML/PayPage/MemberInfo/MemberInfoDetail_Edit.qml</file>
        <file>QML/Utils/RadioBtnRectC3.qml</file>
        <file>QML/PayPage/MemberInfo/MemberInfoDetail_Create.qml</file>
        <file>QML/Utils/PopupNumChange.qml</file>
        <file>QML/PayPage/MemberInfo/PopupMemberRecharge.qml</file>
        <file>QML/Utils/RadioBtnRectUnderLine.qml</file>
        <file>QML/PayPage/MemberInfo/PopupMemberExchange.qml</file>
        <file>QML/PayPage/SearchBar/SearchBar4Member.qml</file>
        <file>QML/SecondWindow.qml</file>
        <file>QML/PayPage/PayMethods/PopupPayResults.qml</file>
        <file>QML/Utils/TextFieldC.qml</file>
        <file>QML/Utils2/CusRadioBtnRectManual.qml</file>
        <file>QML/Utils2/CusRect.qml</file>
        <file>QML/Utils2/CusText.qml</file>
        <file>QML/Utils2/CusSpacer.qml</file>
        <file>QML/Utils2/CusCheckBox.qml</file>
        <file>QML/Utils2/CusComboBox.qml</file>
        <file>QML/Utils2/CusButton.qml</file>
        <file>QML/Utils2/CusSwitchButton.qml</file>
        <file>QML/Utils2/CusRadioRectManualStyle2.qml</file>
        <file>QML/Utils2/CusRectShape.qml</file>
        <file>QML/Utils2/CusRadioManual.qml</file>
        <file>QML/NetOrderPage/PopupCourierList.qml</file>
        <file>QML/GodownPage/LeftSide/ContainCategoryDetail/CategoryInfo.qml</file>
        <file>QML/GodownPage/LeftSide/ContainGoodsDetail/GoodsInfo.qml</file>
        <file>QML/GodownPage/LeftSide/ContainGoodsDetail/GoodsInfo2.qml</file>
        <file>QML/GodownPage/LeftSide/ContainGoodsDetail/PopupSotckChange.qml</file>
        <file>QML/GodownPage/LeftSide/ContainSupplierDetail/SupplierInfo.qml</file>
        <file>QML/GodownPage/LeftSide/ContainCategoryDetail.qml</file>
        <file>QML/GodownPage/LeftSide/ContainGoodsDetail.qml</file>
        <file>QML/GodownPage/LeftSide/ContainSupplierCategoryDetail.qml</file>
        <file>QML/GodownPage/LeftSide/ContainSupplierDetail.qml</file>
        <file>QML/GodownPage/RightSide/ProductManagement/ExpandableListGoodsClass.qml</file>
        <file>QML/GodownPage/RightSide/ProductManagement/GoodsRect.qml</file>
        <file>QML/GodownPage/RightSide/ProductManagement/RectSort.qml</file>
        <file>QML/GodownPage/RightSide/CategoryManagement.qml</file>
        <file>QML/GodownPage/RightSide/GoodsManagement.qml</file>
        <file>QML/GodownPage/RightSide/SupplierCategory.qml</file>
        <file>QML/GodownPage/RightSide/SupplierManagement.qml</file>
        <file>QML/GodownPage/LeftSide/ContainSupplierCategoryDetail/SupplierCategoryInfo.qml</file>
        <file>QML/QueryPage/PopupRefundDetails.qml</file>
        <file>QML/Utils2/CusTextField.qml</file>
        <file>QML/Utils2/CusSpinBox.qml</file>
        <file>QML/Utils/PopupInputConfirm.qml</file>
        <file>QML/PayPage/Popup/PopupAddGoods2CurGoodsKind.qml</file>
        <file>QML/PayPage/MemberInfo/Popup/PopupPointChange.qml</file>
        <file>QML/GodownPage/LeftSide/ContainGoodsDetail/CreateGoods.qml</file>
        <file>QML/GodownPage/LeftSide/ContainGoodsDetail/EditGoods.qml</file>
        <file>QML/GodownPage/LeftSide/ContainGoodsDetail/GoodsDetailBasicInfo.qml</file>
        <file>QML/Common/PopupSotckChange.qml</file>
        <file>QML/SettingPage/Details/BarcodeScaleItem.qml</file>
        <file>QML/Utils2/CusTimePicker.qml</file>
        <file>QML/Utils2/CusCalendar.qml</file>
        <file>QML/Utils2/CusDateTimePicker.qml</file>
        <file>QML/Utils2/CusPopupConfirm.qml</file>
        <file>QML/Utils2/CusKeyboard.qml</file>
        <file>QML/QueryPage/PopupRefundDetailsPaymethod.qml</file>
        <file>QML/Utils2/CusMoveArea.qml</file>
        <file>QML/PayPage/PayMethods/PopupCombined.qml</file>
        <file>QML/GodownPage/RightSide/SupplierSelector.qml</file>
        <file>QML/GodownPage/RightSide/GoodsUnitSelector.qml</file>
        <file>QML/GodownPage/RightSide/GoodsKindSelector.qml</file>
        <file>QML/PayPage/GoodsList/GoodsList4AllGoods.qml</file>
        <file>QML/PayPage/GoodsList/Component/GoodsRect4All.qml</file>
        <file>QML/PayPage/GoodsList/Component/GoodsRect4All_copy.qml</file>
        <file>QML/PayPage/Popup/PopupAddGoods2CurVGoodsKind.qml</file>
        <file>QML/SettingPage/Details/Setting_PayConfig.qml</file>
        <file>QML/SettingPage/Details/Icon.qml</file>
        <file>QML/SettingPage/Details/PayIcon.qml</file>
        <file>QML/SettingPage/Details/PayIcon_copy.qml</file>
        <file>QML/GodownPageOld/GodownPageOld.qml</file>
        <file>QML/GodownPageOld/PopupSotckChange.qml</file>
        <file>QML/QmlEvent.qml</file>
        <file>QML/Common/PopupUpdate.qml</file>
        <file>QML/Common/PopupVersion.qml</file>
        <file>QML/Utils2/CusButtonManual.qml</file>
        <file>QML/Utils2/CusImg.qml</file>
        <file>QML/Common/PopupUploadGoods.qml</file>
        <file>QML/Utils2/CusTextArea.qml</file>
        <file>QML/SettingPage/Details/PriceTagWindow1.qml</file>
        <file>QML/SettingPage/Details/PriceTagWindow2.qml</file>
        <file>QML/SettingPage/Details/Setting_Camera.qml</file>
        <file>QML/GodownPage/LeftSide/ContainGoodsDetail/PopupTagList.qml</file>
        <file>QML/GodownPage/LeftSide/ContainGoodsDetail/PopupTagListAdd.qml</file>
        <file>QML/SettingPage/Details/Setting_Sound.qml</file>
        <file>QML/SettingPage/Details/Setting_Godown.qml</file>
        <file>QML/Common/Popup4RealRecognizer.qml</file>
        <file>QML/PayPage/PayMethods/PayMethods_copy.qml</file>
        <file>QML/Utils2/CusShapeButton.qml</file>
        <file>QML/SettingPage/Details/Setting_WipeZero.qml</file>
        <file>QML/Common/PopupAreaDivision.qml</file>
        <file>QML/Common/PopupAreaDivision_copy.qml</file>
        <file>QML/Utils2/CusMoveArea2.qml</file>
        <file>QML/MembershipPage/SearchBar/SearchBar4Member.qml</file>
        <file>QML/MembershipPage/MemberInfo/MemberInfo.qml</file>
        <file>QML/MembershipPage/GoodsList/MemberShipList.qml</file>
        <file>QML/MembershipPage/MemberInfo/MemberInfoDetail_Create.qml</file>
        <file>QML/MembershipPage/MemberInfo/MemberInfoDetail_Edit.qml</file>
        <file>QML/MembershipPage/MemberInfo/Popup/PopupPointChange.qml</file>
        <file>QML/MembershipPage/MemberInfo/PopupMemberExchange.qml</file>
        <file>QML/MembershipPage/MemberInfo/PopupMemberRecharge.qml</file>
        <file>QML/MembershipPage/GoodsList/Component/GoodsPriceText.qml</file>
        <file>QML/MembershipPage/GoodsList/MemberShipListOption.qml</file>
        <file>QML/MembershipPage/MemberInfo/PopupMemberRefund.qml</file>
        <file>QML/PayPage/PayMethods/PopupVipCardPay.qml</file>
        <file>QML/GodownPageOld/PopupInStock.qml</file>
        <file>QML/GodownPageOld/PopupOutStockMean.qml</file>
        <file>QML/GodownPageOld/PopupOutStockFirst.qml</file>
        <file>QML/Utils2/CusDateTimePickerDay.qml</file>
        <file>QML/PayPage/SearchBar/Popup/SearchMemberPopup.qml</file>
        <file>QML/QueryPage/ContainGoodsInformation.qml</file>
        <file>QML/QueryPage/ContainGoodsSale.qml</file>
        <file>QML/Utils2/CusComboBox2.qml</file>
        <file>QML/PayPage/GoodsEdit/GoodsEdit.qml</file>
        <file>QML/MembershipPage/MemberInfo/MemberInfo_Analysis.qml</file>
        <file>QML/Utils2/QChart3.qml</file>
        <file>QML/Utils2/QChart3.js</file>
        <file>QML/Utils2/QChart2.js</file>
        <file>QML/Utils2/QChart2.qml</file>
        <file>QML/Utils2/QChartGallery2.js</file>
        <file>QML/Utils2/SaleRankingRec.qml</file>
        <file>QML/Utils2/QChart.js</file>
        <file>QML/Utils2/QChart.qml</file>
        <file>QML/QueryPage/PopupSalesDetails.qml</file>
        <file>QML/PayPage/SearchBar/Popup/SearchBundleInformation.qml</file>
        <file>QML/Common/PopupUpdateNew.qml</file>
        <file>QML/Common/Toast.qml</file>
        <file>QML/Utils2/CusKeyboardInternational.qml</file>
        <file>QML/PayPage/GoodsEdit/PopupSupplierManger.qml</file>
        <file>QML/GodownPageOld/PopupSuppliersManger.qml</file>
        <file>QML/Utils2/CusRectColor.qml</file>
    </qresource>
</RCC>
