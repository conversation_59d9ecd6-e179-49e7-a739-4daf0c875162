import qbs

Group {
    name: "SortFilterProxyModel"
    prefix: path + "/"
    files: [
        "filters/alloffilter.cpp",
        "filters/alloffilter.h",
        "filters/anyoffilter.cpp",
        "filters/anyoffilter.h",
        "filters/expressionfilter.cpp",
        "filters/expressionfilter.h",
        "filters/filter.cpp",
        "filters/filter.h",
        "filters/filtercontainer.cpp",
        "filters/filtercontainer.h",
        "filters/filtercontainerfilter.cpp",
        "filters/filtercontainerfilter.h",
        "filters/filtersqmltypes.cpp",
        "filters/indexfilter.cpp",
        "filters/indexfilter.h",
        "filters/rangefilter.cpp",
        "filters/rangefilter.h",
        "filters/regexpfilter.cpp",
        "filters/regexpfilter.h",
        "filters/rolefilter.cpp",
        "filters/rolefilter.h",
        "filters/valuefilter.cpp",
        "filters/valuefilter.h",
        "proxyroles/expressionrole.cpp",
        "proxyroles/expressionrole.h",
        "proxyroles/filterrole.cpp",
        "proxyroles/filterrole.h",
        "proxyroles/joinrole.cpp",
        "proxyroles/joinrole.h",
        "proxyroles/proxyrole.cpp",
        "proxyroles/proxyrole.h",
        "proxyroles/proxyrolecontainer.cpp",
        "proxyroles/proxyrolecontainer.h",
        "proxyroles/proxyrolesqmltypes.cpp",
        "proxyroles/regexprole.cpp",
        "proxyroles/regexprole.h",
        "proxyroles/singlerole.cpp",
        "proxyroles/singlerole.h",
        "proxyroles/switchrole.cpp",
        "proxyroles/switchrole.h",
        "sorters/expressionsorter.cpp",
        "sorters/expressionsorter.h",
        "sorters/filtersorter.cpp",
        "sorters/filtersorter.h",
        "sorters/rolesorter.cpp",
        "sorters/rolesorter.h",
        "sorters/sorter.cpp",
        "sorters/sorter.h",
        "sorters/sortercontainer.cpp",
        "sorters/sortercontainer.h",
        "sorters/sortersqmltypes.cpp",
        "sorters/stringsorter.cpp",
        "sorters/stringsorter.h",
        "qqmlsortfilterproxymodel.cpp",
        "qqmlsortfilterproxymodel.h"
    ]
}
