<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- proxyrole.cpp -->
  <title>ProxyRole QML Type | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar">
<div class="toc">
<h3><a name="toc">Contents</a></h3>
<ul>
<li class="level1"><a href="#details">Detailed Description</a></li>
</ul>
</div>
<div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">ProxyRole QML Type</h1>
<span class="subtitle"></span>
<!-- $$$ProxyRole-brief -->
<p>Base type for the <a href="qml-sortfilterproxymodel-sortfilterproxymodel.html">SortFilterProxyModel</a> proxy roles. <a href="#details">More...</a></p>
<!-- @@@ProxyRole -->
<div class="table"><table class="alignedsummary">
<tr><td class="memItemLeft rightAlign topAlign"> Import Statement:</td><td class="memItemRight bottomAlign"> import SortFilterProxyModel .</td></tr><tr><td class="memItemLeft rightAlign topAlign"> Inherited By:</td><td class="memItemRight bottomAlign"> <p><a href="qml-sortfilterproxymodel-regexprole.html">RegExpRole</a> and <a href="qml-sortfilterproxymodel-singlerole.html">SingleRole</a></p>
</td></tr></table></div><ul>
<li><a href="qml-sortfilterproxymodel-proxyrole-members.html">List of all members, including inherited members</a></li>
</ul>
<!-- $$$ProxyRole-description -->
<a name="details"></a>
<h2 id="details">Detailed Description</h2>
<p>The ProxyRole type cannot be used directly in a QML file. It exists to provide a set of common properties and methods, available across all the other proxy role types that inherit from it. Attempting to use the ProxyRole type directly will result in an error.</p>
<!-- @@@ProxyRole -->
</body>
</html>
