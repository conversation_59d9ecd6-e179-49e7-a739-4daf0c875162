<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- joinrole.cpp -->
  <title>List of All Members for JoinRole | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar"><div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">List of All Members for JoinRole</h1>
<p>This is the complete list of members for <a href="qml-sortfilterproxymodel-joinrole.html">JoinRole</a>, including inherited members.</p>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-joinrole.html#name-prop">name</a></b></b> : string</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-joinrole.html#roleNames-prop">roleNames</a></b></b> : list&lt;string&gt;</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-joinrole.html#separator-prop">separator</a></b></b> : string</li>
</ul>
</body>
</html>
