<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- singlerole.cpp -->
  <title>SingleRole QML Type | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar">
<div class="toc">
<h3><a name="toc">Contents</a></h3>
<ul>
<li class="level1"><a href="#properties">Properties</a></li>
<li class="level1"><a href="#details">Detailed Description</a></li>
</ul>
</div>
<div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">SingleRole QML Type</h1>
<span class="subtitle"></span>
<!-- $$$SingleRole-brief -->
<p>Base type for the <a href="qml-sortfilterproxymodel-sortfilterproxymodel.html">SortFilterProxyModel</a> proxy roles defining a single role. <a href="#details">More...</a></p>
<!-- @@@SingleRole -->
<div class="table"><table class="alignedsummary">
<tr><td class="memItemLeft rightAlign topAlign"> Import Statement:</td><td class="memItemRight bottomAlign"> import SortFilterProxyModel .</td></tr><tr><td class="memItemLeft rightAlign topAlign"> Inherits:</td><td class="memItemRight bottomAlign"> <p><a href="qml-sortfilterproxymodel-proxyrole.html">ProxyRole</a></p>
</td></tr><tr><td class="memItemLeft rightAlign topAlign"> Inherited By:</td><td class="memItemRight bottomAlign"> <p><a href="qml-sortfilterproxymodel-expressionrole.html">ExpressionRole</a>, <a href="qml-sortfilterproxymodel-filterrole.html">FilterRole</a>, <a href="qml-sortfilterproxymodel-joinrole.html">JoinRole</a>, and <a href="qml-sortfilterproxymodel-switchrole.html">SwitchRole</a></p>
</td></tr></table></div><ul>
<li><a href="qml-sortfilterproxymodel-singlerole-members.html">List of all members, including inherited members</a></li>
</ul>
<a name="properties"></a>
<h2 id="properties">Properties</h2>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-singlerole.html#name-prop">name</a></b></b> : string</li>
</ul>
<!-- $$$SingleRole-description -->
<a name="details"></a>
<h2 id="details">Detailed Description</h2>
<p>SingleRole is a convenience base class for proxy roles who define a single role. It cannot be used directly in a QML file. It exists to provide a set of common properties and methods, available across all the other proxy role types that inherit from it. Attempting to use the SingleRole type directly will result in an error.</p>
<!-- @@@SingleRole -->
<h2>Property Documentation</h2>
<!-- $$$name -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="name-prop"></a><span class="name">name</span> : <span class="type">string</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds the role name of the proxy role.</p>
</div></div><!-- @@@name -->
<br/>
</body>
</html>
