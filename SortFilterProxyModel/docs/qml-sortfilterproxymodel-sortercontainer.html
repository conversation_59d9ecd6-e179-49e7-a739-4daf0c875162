<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- sortercontainer.cpp -->
  <title>SorterContainer QML Type | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar">
<div class="toc">
<h3><a name="toc">Contents</a></h3>
<ul>
<li class="level1"><a href="#attached-properties">Attached Properties</a></li>
<li class="level1"><a href="#details">Detailed Description</a></li>
<li class="level2"><a href="#types-implementing-this-interface">Types implementing this interface:</a></li>
</ul>
</div>
<div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">SorterContainer QML Type</h1>
<span class="subtitle"></span>
<!-- $$$SorterContainer-brief -->
<p>Abstract interface for types containing <a href="qml-sortfilterproxymodel-sorter.html">Sorters</a>. <a href="#details">More...</a></p>
<!-- @@@SorterContainer -->
<div class="table"><table class="alignedsummary">
<tr><td class="memItemLeft rightAlign topAlign"> Import Statement:</td><td class="memItemRight bottomAlign"> import SortFilterProxyModel .</td></tr></table></div><ul>
<li><a href="qml-sortfilterproxymodel-sortercontainer-members.html">List of all members, including inherited members</a></li>
</ul>
<a name="attached-properties"></a>
<h2 id="attached-properties">Attached Properties</h2>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-sortercontainer.html#container-attached-prop">container</a></b></b> : bool</li>
</ul>
<!-- $$$SorterContainer-description -->
<a name="details"></a>
<h2 id="details">Detailed Description</h2>
<a name="types-implementing-this-interface"></a>
<h3 id="types-implementing-this-interface">Types implementing this interface:</h3>
<div class="table"><table class="annotated">
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html">SortFilterProxyModel</a></p></td><td class="tblDescr"><p>Filters and sorts data coming from a source QAbstractItemModel</p></td></tr>
</table></div>
<!-- @@@SorterContainer -->
<h2>Attached Property Documentation</h2>
<!-- $$$container -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="container-attached-prop"></a><span class="name">SorterContainer.container</span> : <span class="type">bool</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This attached property allows you to include in a <a href="qml-sortfilterproxymodel-sortercontainer.html">SorterContainer</a> a <a href="qml-sortfilterproxymodel-sorter.html">Sorter</a> that has been instantiated outside of the <a href="qml-sortfilterproxymodel-sortercontainer.html">SorterContainer</a>, for example in an Instantiator.</p>
</div></div><!-- @@@container -->
<br/>
</body>
</html>
