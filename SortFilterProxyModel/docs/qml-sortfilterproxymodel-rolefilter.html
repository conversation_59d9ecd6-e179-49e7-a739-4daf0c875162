<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- rolefilter.cpp -->
  <title>RoleFilter QML Type | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar">
<div class="toc">
<h3><a name="toc">Contents</a></h3>
<ul>
<li class="level1"><a href="#properties">Properties</a></li>
<li class="level1"><a href="#details">Detailed Description</a></li>
</ul>
</div>
<div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">RoleFilter QML Type</h1>
<span class="subtitle"></span>
<!-- $$$RoleFilter-brief -->
<p>Base type for filters based on a source model role. <a href="#details">More...</a></p>
<!-- @@@RoleFilter -->
<div class="table"><table class="alignedsummary">
<tr><td class="memItemLeft rightAlign topAlign"> Import Statement:</td><td class="memItemRight bottomAlign"> import SortFilterProxyModel .</td></tr><tr><td class="memItemLeft rightAlign topAlign"> Inherits:</td><td class="memItemRight bottomAlign"> <p><a href="qml-sortfilterproxymodel-filter.html">Filter</a></p>
</td></tr><tr><td class="memItemLeft rightAlign topAlign"> Inherited By:</td><td class="memItemRight bottomAlign"> <p><a href="qml-sortfilterproxymodel-rangefilter.html">RangeFilter</a>, <a href="qml-sortfilterproxymodel-regexpfilter.html">RegExpFilter</a>, and <a href="qml-sortfilterproxymodel-valuefilter.html">ValueFilter</a></p>
</td></tr></table></div><ul>
<li><a href="qml-sortfilterproxymodel-rolefilter-members.html">List of all members, including inherited members</a></li>
</ul>
<a name="properties"></a>
<h2 id="properties">Properties</h2>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-rolefilter.html#enabled-prop">enabled</a></b></b> : bool</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-rolefilter.html#inverted-prop">inverted</a></b></b> : bool</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-rolefilter.html#roleName-prop">roleName</a></b></b> : string</li>
</ul>
<!-- $$$RoleFilter-description -->
<a name="details"></a>
<h2 id="details">Detailed Description</h2>
<p>The RoleFilter type cannot be used directly in a QML file. It exists to provide a set of common properties and methods, available across all the other filter types that inherit from it. Attempting to use the RoleFilter type directly will result in an error.</p>
<!-- @@@RoleFilter -->
<h2>Property Documentation</h2>
<!-- $$$enabled -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="enabled-prop"></a><span class="name">enabled</span> : <span class="type">bool</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds whether the filter is enabled. A disabled filter will accept every rows unconditionally (even if it's inverted).</p>
<p>By default, filters are enabled.</p>
</div></div><!-- @@@enabled -->
<br/>
<!-- $$$inverted -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="inverted-prop"></a><span class="name">inverted</span> : <span class="type">bool</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds whether the filter is inverted. When a filter is inverted, a row normally accepted would be rejected, and vice-versa.</p>
<p>By default, filters are not inverted.</p>
</div></div><!-- @@@inverted -->
<br/>
<!-- $$$roleName -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="roleName-prop"></a><span class="name">roleName</span> : <span class="type">string</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds the role name that the filter is using to query the source model's data when filtering items.</p>
</div></div><!-- @@@roleName -->
<br/>
</body>
</html>
