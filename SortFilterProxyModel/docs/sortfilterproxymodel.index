<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE QDOCINDEX>
<INDEX url="" title="SortFilterProxyModel Reference Documentation" version="" project="SortFilterProxyModel">
    <namespace name="" status="active" access="public" module="sortfilterproxymodel">
        <qmlclass name="AnyOf" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::Filter" fullname="SortFilterProxyModel.AnyOf" href="qml-sortfilterproxymodel-anyof.html" status="active" access="public" documented="true" title="AnyOf" fulltitle="AnyOf" subtitle="" groups="Filters,FilterContainer" brief="Filter container accepting rows accepted by at least one of its child filters"/>
        <qmlclass name="SwitchRole" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::SingleRole" fullname="SortFilterProxyModel.SwitchRole" href="qml-sortfilterproxymodel-switchrole.html" status="active" access="public" documented="true" title="SwitchRole" fulltitle="SwitchRole" subtitle="" groups="ProxyRoles,FilterContainer" brief="A role using Filter to conditionnaly compute its data">
            <qmlproperty name="value" fullname="SortFilterProxyModel.SwitchRole.value" href="qml-sortfilterproxymodel-switchrole.html#value-attached-prop" status="active" access="public" documented="true" type="var" attached="true" writable="true"/>
            <qmlproperty name="defaultRoleName" fullname="SortFilterProxyModel.SwitchRole.defaultRoleName" href="qml-sortfilterproxymodel-switchrole.html#defaultRoleName-prop" status="active" access="public" documented="true" type="string" attached="false" writable="true"/>
            <qmlproperty name="defaultValue" fullname="SortFilterProxyModel.SwitchRole.defaultValue" href="qml-sortfilterproxymodel-switchrole.html#defaultValue-prop" status="active" access="public" documented="true" type="var" attached="false" writable="true"/>
            <qmlproperty name="filters" fullname="SortFilterProxyModel.SwitchRole.filters" href="qml-sortfilterproxymodel-switchrole.html#filters-prop" status="active" access="public" documented="true" type="list&lt;Filter&gt;" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="AllOf" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::Filter" fullname="SortFilterProxyModel.AllOf" href="qml-sortfilterproxymodel-allof.html" status="active" access="public" documented="true" title="AllOf" fulltitle="AllOf" subtitle="" groups="Filters,FilterContainer" brief="Filter container accepting rows accepted by all its child filters"/>
        <qmlclass name="ExpressionFilter" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::Filter" fullname="SortFilterProxyModel.ExpressionFilter" href="qml-sortfilterproxymodel-expressionfilter.html" status="active" access="public" documented="true" title="ExpressionFilter" fulltitle="ExpressionFilter" subtitle="" groups="Filters" brief="Filters row with a custom filtering">
            <qmlproperty name="expression" fullname="SortFilterProxyModel.ExpressionFilter.expression" href="qml-sortfilterproxymodel-expressionfilter.html#expression-prop" status="active" access="public" documented="true" type="expression" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="IndexFilter" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::Filter" fullname="SortFilterProxyModel.IndexFilter" href="qml-sortfilterproxymodel-indexfilter.html" status="active" access="public" documented="true" title="IndexFilter" fulltitle="IndexFilter" subtitle="" groups="Filters" brief="Filters rows based on their source index">
            <qmlproperty name="minimumIndex" fullname="SortFilterProxyModel.IndexFilter.minimumIndex" href="qml-sortfilterproxymodel-indexfilter.html#minimumIndex-prop" status="active" access="public" documented="true" type="int" attached="false" writable="true"/>
            <qmlproperty name="maximumIndex" fullname="SortFilterProxyModel.IndexFilter.maximumIndex" href="qml-sortfilterproxymodel-indexfilter.html#maximumIndex-prop" status="active" access="public" documented="true" type="int" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="Filter" qml-module-name="SortFilterProxyModel" fullname="SortFilterProxyModel.Filter" href="qml-sortfilterproxymodel-filter.html" status="active" access="public" abstract="true" documented="true" title="Filter" fulltitle="Filter" subtitle="" groups="Filters" brief="Base type for the SortFilterProxyModel filters">
            <qmlproperty name="inverted" fullname="SortFilterProxyModel.Filter.inverted" status="active" access="public" documented="true" type="bool" attached="false" writable="true"/>
            <qmlproperty name="enabled" fullname="SortFilterProxyModel.Filter.enabled" status="active" access="public" documented="true" type="bool" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="FilterContainer" qml-module-name="SortFilterProxyModel" fullname="SortFilterProxyModel.FilterContainer" href="qml-sortfilterproxymodel-filtercontainer.html" status="active" access="public" abstract="true" documented="true" title="FilterContainer" fulltitle="FilterContainer" subtitle="" groups="FilterAttached" brief="Abstract interface for types containing Filters">
            <contents name="types-implementing-this-interface" title="Types implementing this interface:" level="2"/>
            <qmlproperty name="container" fullname="SortFilterProxyModel.FilterContainer.container" status="active" access="public" documented="true" type="bool" attached="true" writable="true"/>
        </qmlclass>
        <qmlclass name="RangeFilter" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::RoleFilter" fullname="SortFilterProxyModel.RangeFilter" href="qml-sortfilterproxymodel-rangefilter.html" status="active" access="public" documented="true" title="RangeFilter" fulltitle="RangeFilter" subtitle="" groups="Filters" brief="Filters rows between boundary values">
            <qmlproperty name="minimumValue" fullname="SortFilterProxyModel.RangeFilter.minimumValue" href="qml-sortfilterproxymodel-rangefilter.html#minimumValue-prop" status="active" access="public" documented="true" type="int" attached="false" writable="true"/>
            <qmlproperty name="minimumInclusive" fullname="SortFilterProxyModel.RangeFilter.minimumInclusive" href="qml-sortfilterproxymodel-rangefilter.html#minimumInclusive-prop" status="active" access="public" documented="true" type="int" attached="false" writable="true"/>
            <qmlproperty name="maximumValue" fullname="SortFilterProxyModel.RangeFilter.maximumValue" href="qml-sortfilterproxymodel-rangefilter.html#maximumValue-prop" status="active" access="public" documented="true" type="int" attached="false" writable="true"/>
            <qmlproperty name="maximumInclusive" fullname="SortFilterProxyModel.RangeFilter.maximumInclusive" href="qml-sortfilterproxymodel-rangefilter.html#maximumInclusive-prop" status="active" access="public" documented="true" type="int" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="SortFilterProxyModel" qml-module-name="SortFilterProxyModel" fullname="SortFilterProxyModel.SortFilterProxyModel" href="qml-sortfilterproxymodel-sortfilterproxymodel.html" status="active" access="public" documented="true" title="SortFilterProxyModel" fulltitle="SortFilterProxyModel" subtitle="" groups="SortFilterProxyModel,FilterContainer,SorterContainer" brief="Filters and sorts data coming from a source QAbstractItemModel">
            <function name="get" fullname="SortFilterProxyModel.SortFilterProxyModel.get" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#get-method" status="active" access="public" documented="true" meta="qmlmethod"/>
            <function name="get" fullname="SortFilterProxyModel.SortFilterProxyModel.get" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#get-method" status="active" access="public" documented="true" meta="qmlmethod"/>
            <function name="mapFromSource" fullname="SortFilterProxyModel.SortFilterProxyModel.mapFromSource" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#mapFromSource-method" status="active" access="public" documented="true" meta="qmlmethod"/>
            <function name="mapFromSource" fullname="SortFilterProxyModel.SortFilterProxyModel.mapFromSource" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#mapFromSource-method" status="active" access="public" documented="true" meta="qmlmethod"/>
            <function name="mapToSource" fullname="SortFilterProxyModel.SortFilterProxyModel.mapToSource" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#mapToSource-method" status="active" access="public" documented="true" meta="qmlmethod"/>
            <function name="mapToSource" fullname="SortFilterProxyModel.SortFilterProxyModel.mapToSource" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#mapToSource-method" status="active" access="public" documented="true" meta="qmlmethod"/>
            <function name="roleForName" fullname="SortFilterProxyModel.SortFilterProxyModel.roleForName" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#roleForName-method" status="active" access="public" documented="true" meta="qmlmethod"/>
            <qmlproperty name="count" fullname="SortFilterProxyModel.SortFilterProxyModel.count" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#count-prop" status="active" access="public" documented="true" type="int" attached="false" writable="true"/>
            <qmlproperty name="proxyRoles" fullname="SortFilterProxyModel.SortFilterProxyModel.proxyRoles" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#proxyRoles-prop" status="active" access="public" documented="true" type="list&lt;ProxyRole&gt;" attached="false" writable="true"/>
            <qmlproperty name="sourceModel" fullname="SortFilterProxyModel.SortFilterProxyModel.sourceModel" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#sourceModel-prop" status="active" access="public" documented="true" type="QAbstractItemModel*" attached="false" writable="true"/>
            <qmlproperty name="sorters" fullname="SortFilterProxyModel.SortFilterProxyModel.sorters" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#sorters-prop" status="active" access="public" documented="true" type="list&lt;Sorter&gt;" attached="false" writable="true"/>
            <qmlproperty name="filters" fullname="SortFilterProxyModel.SortFilterProxyModel.filters" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#filters-prop" status="active" access="public" documented="true" type="list&lt;Filter&gt;" attached="false" writable="true"/>
            <qmlproperty name="sortRoleName" fullname="SortFilterProxyModel.SortFilterProxyModel.sortRoleName" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#sortRoleName-prop" status="active" access="public" documented="true" type="string" attached="false" writable="true"/>
            <qmlproperty name="delayed" fullname="SortFilterProxyModel.SortFilterProxyModel.delayed" href="qml-sortfilterproxymodel-sortfilterproxymodel.html#delayed-prop" status="active" access="public" documented="true" type="bool" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="RegExpFilter" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::RoleFilter" fullname="SortFilterProxyModel.RegExpFilter" href="qml-sortfilterproxymodel-regexpfilter.html" status="active" access="public" documented="true" title="RegExpFilter" fulltitle="RegExpFilter" subtitle="" groups="Filters" brief="Filters rows matching a regular expression">
            <qmlproperty name="syntax" fullname="SortFilterProxyModel.RegExpFilter.syntax" href="qml-sortfilterproxymodel-regexpfilter.html#syntax-prop" status="active" access="public" documented="true" type="enum" attached="false" writable="true"/>
            <qmlproperty name="pattern" fullname="SortFilterProxyModel.RegExpFilter.pattern" href="qml-sortfilterproxymodel-regexpfilter.html#pattern-prop" status="active" access="public" documented="true" type="bool" attached="false" writable="true"/>
            <qmlproperty name="caseSensitivity" fullname="SortFilterProxyModel.RegExpFilter.caseSensitivity" href="qml-sortfilterproxymodel-regexpfilter.html#caseSensitivity-prop" status="active" access="public" documented="true" type="Qt::CaseSensitivity" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="ExpressionSorter" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::Sorter" fullname="SortFilterProxyModel.ExpressionSorter" href="qml-sortfilterproxymodel-expressionsorter.html" status="active" access="public" documented="true" title="ExpressionSorter" fulltitle="ExpressionSorter" subtitle="" groups="Sorters" brief="Sorts row with a custom javascript expression">
            <qmlproperty name="expression" fullname="SortFilterProxyModel.ExpressionSorter.expression" href="qml-sortfilterproxymodel-expressionsorter.html#expression-prop" status="active" access="public" documented="true" type="expression" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="FilterSorter" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::Sorter" fullname="SortFilterProxyModel.FilterSorter" href="qml-sortfilterproxymodel-filtersorter.html" status="active" access="public" documented="true" title="FilterSorter" fulltitle="FilterSorter" subtitle="" groups="Sorters,FilterContainer" brief="Sorts rows based on if they match filters">
            <qmlproperty name="filters" fullname="SortFilterProxyModel.FilterSorter.filters" href="qml-sortfilterproxymodel-filtersorter.html#filters-prop" status="active" access="public" documented="true" type="list&lt;Filter&gt;" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="RoleSorter" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::Sorter" fullname="SortFilterProxyModel.RoleSorter" href="qml-sortfilterproxymodel-rolesorter.html" status="active" access="public" documented="true" title="RoleSorter" fulltitle="RoleSorter" subtitle="" groups="Sorters" brief="Sorts rows based on a source model role">
            <qmlproperty name="roleName" fullname="SortFilterProxyModel.RoleSorter.roleName" href="qml-sortfilterproxymodel-rolesorter.html#roleName-prop" status="active" access="public" documented="true" type="string" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="Sorter" qml-module-name="SortFilterProxyModel" fullname="SortFilterProxyModel.Sorter" href="qml-sortfilterproxymodel-sorter.html" status="active" access="public" abstract="true" documented="true" title="Sorter" fulltitle="Sorter" subtitle="" groups="Sorters" brief="Base type for the SortFilterProxyModel sorters">
            <qmlproperty name="enabled" fullname="SortFilterProxyModel.Sorter.enabled" status="active" access="public" documented="true" type="bool" attached="false" writable="true"/>
            <qmlproperty name="sortOrder" fullname="SortFilterProxyModel.Sorter.sortOrder" status="active" access="public" documented="true" type="Qt::SortOrder" attached="false" writable="true"/>
            <qmlproperty name="priority" fullname="SortFilterProxyModel.Sorter.priority" status="active" access="public" documented="true" type="int" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="SorterContainer" qml-module-name="SortFilterProxyModel" fullname="SortFilterProxyModel.SorterContainer" href="qml-sortfilterproxymodel-sortercontainer.html" status="active" access="public" abstract="true" documented="true" title="SorterContainer" fulltitle="SorterContainer" subtitle="" groups="SorterAttached" brief="Abstract interface for types containing Sorters">
            <contents name="types-implementing-this-interface" title="Types implementing this interface:" level="2"/>
            <qmlproperty name="container" fullname="SortFilterProxyModel.SorterContainer.container" status="active" access="public" documented="true" type="bool" attached="true" writable="true"/>
        </qmlclass>
        <qmlclass name="tst_builtins" qml-module-name="tst_builtins" href="qml-tst-builtins.html" status="internal" access="private" location="tst_builtins.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_builtins.qml" lineno="6" title="tst_builtins" fulltitle="tst_builtins" subtitle=""/>
        <qmlclass name="tst_expressionrole" qml-module-name="tst_expressionrole" href="qml-tst-expressionrole.html" status="internal" access="private" location="tst_expressionrole.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_expressionrole.qml" lineno="7" title="tst_expressionrole" fulltitle="tst_expressionrole" subtitle="">
            <qmlproperty name="c" fullname="tst_expressionrole::c" href="qml-tst-expressionrole.html#c-prop" status="internal" access="private" location="tst_expressionrole.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_expressionrole.qml" lineno="8" type="int" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="tst_delayed" qml-module-name="tst_delayed" href="qml-tst-delayed.html" status="internal" access="private" location="tst_delayed.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_delayed.qml" lineno="7" title="tst_delayed" fulltitle="tst_delayed" subtitle=""/>
        <qmlclass name="StringSorter" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::RoleSorter" fullname="SortFilterProxyModel.StringSorter" href="qml-sortfilterproxymodel-stringsorter.html" status="active" access="public" documented="true" title="StringSorter" fulltitle="StringSorter" subtitle="" groups="Sorters" brief="Sorts rows based on a source model string role">
            <qmlproperty name="caseSensitivity" fullname="SortFilterProxyModel.StringSorter.caseSensitivity" href="qml-sortfilterproxymodel-stringsorter.html#caseSensitivity-prop" status="active" access="public" documented="true" type="Qt.CaseSensitivity" attached="false" writable="true"/>
            <qmlproperty name="ignorePunctation" fullname="SortFilterProxyModel.StringSorter.ignorePunctation" href="qml-sortfilterproxymodel-stringsorter.html#ignorePunctation-prop" status="active" access="public" documented="true" type="bool" attached="false" writable="true"/>
            <qmlproperty name="locale" fullname="SortFilterProxyModel.StringSorter.locale" href="qml-sortfilterproxymodel-stringsorter.html#locale-prop" status="active" access="public" documented="true" type="Locale" attached="false" writable="true"/>
            <qmlproperty name="numericMode" fullname="SortFilterProxyModel.StringSorter.numericMode" href="qml-sortfilterproxymodel-stringsorter.html#numericMode-prop" status="active" access="public" documented="true" type="bool" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="tst_filtercontainerattached" qml-module-name="tst_filtercontainerattached" href="qml-tst-filtercontainerattached.html" status="internal" access="private" location="tst_filtercontainerattached.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_filtercontainerattached.qml" lineno="7" title="tst_filtercontainerattached" fulltitle="tst_filtercontainerattached" subtitle=""/>
        <qmlclass name="tst_filtercontainers" qml-module-name="tst_filtercontainers" href="qml-tst-filtercontainers.html" status="internal" access="private" location="tst_filtercontainers.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_filtercontainers.qml" lineno="6" title="tst_filtercontainers" fulltitle="tst_filtercontainers" subtitle="">
            <qmlproperty name="filters" fullname="tst_filtercontainers::filters" href="qml-tst-filtercontainers.html#filters-prop" status="internal" access="private" location="tst_filtercontainers.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_filtercontainers.qml" lineno="7" type="Filter" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="tst_filterrole" qml-module-name="tst_filterrole" href="qml-tst-filterrole.html" status="internal" access="private" location="tst_filterrole.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_filterrole.qml" lineno="7" title="tst_filterrole" fulltitle="tst_filterrole" subtitle=""/>
        <qmlclass name="tst_filtersorter" qml-module-name="tst_filtersorter" href="qml-tst-filtersorter.html" status="internal" access="private" location="tst_filtersorter.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_filtersorter.qml" lineno="7" title="tst_filtersorter" fulltitle="tst_filtersorter" subtitle=""/>
        <qmlclass name="tst_helpers" qml-module-name="tst_helpers" href="qml-tst-helpers.html" status="internal" access="private" location="tst_helpers.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_helpers.qml" lineno="7" title="tst_helpers" fulltitle="tst_helpers" subtitle=""/>
        <qmlclass name="tst_indexfilter" qml-module-name="tst_indexfilter" href="qml-tst-indexfilter.html" status="internal" access="private" location="tst_indexfilter.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_indexfilter.qml" lineno="6" title="tst_indexfilter" fulltitle="tst_indexfilter" subtitle="">
            <qmlproperty name="filters" fullname="tst_indexfilter::filters" href="qml-tst-indexfilter.html#filters-prop" status="internal" access="private" location="tst_indexfilter.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_indexfilter.qml" lineno="7" type="IndexFilter" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="tst_joinrole" qml-module-name="tst_joinrole" href="qml-tst-joinrole.html" status="internal" access="private" location="tst_joinrole.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_joinrole.qml" lineno="7" title="tst_joinrole" fulltitle="tst_joinrole" subtitle=""/>
        <qmlclass name="tst_proxyroles" qml-module-name="tst_proxyroles" href="qml-tst-proxyroles.html" status="internal" access="private" location="tst_proxyroles.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_proxyroles.qml" lineno="8" title="tst_proxyroles" fulltitle="tst_proxyroles" subtitle=""/>
        <qmlclass name="tst_rangefilter" qml-module-name="tst_rangefilter" href="qml-tst-rangefilter.html" status="internal" access="private" location="tst_rangefilter.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_rangefilter.qml" lineno="6" title="tst_rangefilter" fulltitle="tst_rangefilter" subtitle="">
            <qmlproperty name="filters" fullname="tst_rangefilter::filters" href="qml-tst-rangefilter.html#filters-prop" status="internal" access="private" location="tst_rangefilter.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_rangefilter.qml" lineno="7" type="RangeFilter" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="tst_regexprole" qml-module-name="tst_regexprole" href="qml-tst-regexprole.html" status="internal" access="private" location="tst_regexprole.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_regexprole.qml" lineno="7" title="tst_regexprole" fulltitle="tst_regexprole" subtitle=""/>
        <qmlclass name="tst_sorters" qml-module-name="tst_sorters" href="qml-tst-sorters.html" status="internal" access="private" location="tst_sorters.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_sorters.qml" lineno="7" title="tst_sorters" fulltitle="tst_sorters" subtitle="">
            <qmlproperty name="tieSorters" fullname="tst_sorters::tieSorters" href="qml-tst-sorters.html#tieSorters-prop" status="internal" access="private" location="tst_sorters.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_sorters.qml" lineno="72" type="RoleSorter" attached="false" writable="true"/>
            <qmlproperty name="sorters" fullname="tst_sorters::sorters" href="qml-tst-sorters.html#sorters-prop" status="internal" access="private" location="tst_sorters.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_sorters.qml" lineno="20" type="QtObject" attached="false" writable="true"/>
            <qmlproperty name="sortersWithPriority" fullname="tst_sorters::sortersWithPriority" href="qml-tst-sorters.html#sortersWithPriority-prop" status="internal" access="private" location="tst_sorters.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_sorters.qml" lineno="77" type="RoleSorter" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="tst_stringsorter" qml-module-name="tst_stringsorter" href="qml-tst-stringsorter.html" status="internal" access="private" location="tst_stringsorter.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_stringsorter.qml" lineno="6" title="tst_stringsorter" fulltitle="tst_stringsorter" subtitle="">
            <qmlproperty name="sorters" fullname="tst_stringsorter::sorters" href="qml-tst-stringsorter.html#sorters-prop" status="internal" access="private" location="tst_stringsorter.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_stringsorter.qml" lineno="7" type="StringSorter" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="tst_sourceroles" qml-module-name="tst_sourceroles" href="qml-tst-sourceroles.html" status="internal" access="private" location="tst_sourceroles.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_sourceroles.qml" lineno="6" title="tst_sourceroles" fulltitle="tst_sourceroles" subtitle=""/>
        <qmlclass name="tst_switchrole" qml-module-name="tst_switchrole" href="qml-tst-switchrole.html" status="internal" access="private" location="tst_switchrole.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_switchrole.qml" lineno="7" title="tst_switchrole" fulltitle="tst_switchrole" subtitle=""/>
        <page name="index.html" href="index.html" status="active" location="index.qdoc" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/index.qdoc" lineno="1" documented="true" subtype="page" title="SortFilterProxyModel QML Module" fulltitle="SortFilterProxyModel QML Module" subtitle="" module="SortFilterProxyModel">
            <contents name="filters" title="Filters" level="1"/>
            <contents name="related-attached-types" title="Related attached types" level="2"/>
            <contents name="sorters" title="Sorters" level="1"/>
            <contents name="related-attached-types" title="Related attached types" level="2"/>
            <contents name="proxyroles" title="ProxyRoles" level="1"/>
        </page>
        <qmlclass name="RoleFilter" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::Filter" fullname="SortFilterProxyModel.RoleFilter" href="qml-sortfilterproxymodel-rolefilter.html" status="active" access="public" abstract="true" documented="true" title="RoleFilter" fulltitle="RoleFilter" subtitle="" groups="Filters" brief="Base type for filters based on a source model role">
            <qmlproperty name="roleName" fullname="SortFilterProxyModel.RoleFilter.roleName" status="active" access="public" documented="true" type="string" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="ValueFilter" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::RoleFilter" fullname="SortFilterProxyModel.ValueFilter" href="qml-sortfilterproxymodel-valuefilter.html" status="active" access="public" documented="true" title="ValueFilter" fulltitle="ValueFilter" subtitle="" groups="Filters" brief="Filters rows matching exactly a value">
            <qmlproperty name="value" fullname="SortFilterProxyModel.ValueFilter.value" href="qml-sortfilterproxymodel-valuefilter.html#value-prop" status="active" access="public" documented="true" type="variant" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="FilterRole" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::SingleRole" fullname="SortFilterProxyModel.FilterRole" href="qml-sortfilterproxymodel-filterrole.html" status="active" access="public" documented="true" title="FilterRole" fulltitle="FilterRole" subtitle="" groups="ProxyRoles,FilterContainer" brief="A role resolving to true for rows matching all its filters">
            <qmlproperty name="filters" fullname="SortFilterProxyModel.FilterRole.filters" href="qml-sortfilterproxymodel-filterrole.html#filters-prop" status="active" access="public" documented="true" type="list&lt;Filter&gt;" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="ExpressionRole" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::SingleRole" fullname="SortFilterProxyModel.ExpressionRole" href="qml-sortfilterproxymodel-expressionrole.html" status="active" access="public" documented="true" title="ExpressionRole" fulltitle="ExpressionRole" subtitle="" groups="ProxyRoles" brief="A custom role computed from a javascript expression">
            <qmlproperty name="expression" fullname="SortFilterProxyModel.ExpressionRole.expression" href="qml-sortfilterproxymodel-expressionrole.html#expression-prop" status="active" access="public" documented="true" type="expression" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="ProxyRole" qml-module-name="SortFilterProxyModel" fullname="SortFilterProxyModel.ProxyRole" href="qml-sortfilterproxymodel-proxyrole.html" status="active" access="public" documented="true" title="ProxyRole" fulltitle="ProxyRole" subtitle="" groups="ProxyRoles" brief="Base type for the SortFilterProxyModel proxy roles"/>
        <qmlclass name="JoinRole" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::SingleRole" fullname="SortFilterProxyModel.JoinRole" href="qml-sortfilterproxymodel-joinrole.html" status="active" access="public" documented="true" title="JoinRole" fulltitle="JoinRole" subtitle="" groups="ProxyRoles" brief="Role made from concatenating other roles">
            <qmlproperty name="roleNames" fullname="SortFilterProxyModel.JoinRole.roleNames" href="qml-sortfilterproxymodel-joinrole.html#roleNames-prop" status="active" access="public" documented="true" type="list&lt;string&gt;" attached="false" writable="true"/>
            <qmlproperty name="separator" fullname="SortFilterProxyModel.JoinRole.separator" href="qml-sortfilterproxymodel-joinrole.html#separator-prop" status="active" access="public" documented="true" type="string" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="tst_sortercontainerattached" qml-module-name="tst_sortercontainerattached" href="qml-tst-sortercontainerattached.html" status="internal" access="private" location="tst_sortercontainerattached.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_sortercontainerattached.qml" lineno="7" title="tst_sortercontainerattached" fulltitle="tst_sortercontainerattached" subtitle=""/>
        <qmlclass name="SingleRole" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::ProxyRole" fullname="SortFilterProxyModel.SingleRole" href="qml-sortfilterproxymodel-singlerole.html" status="active" access="public" abstract="true" documented="true" title="SingleRole" fulltitle="SingleRole" subtitle="" groups="ProxyRoles" brief="Base type for the SortFilterProxyModel proxy roles defining a single role">
            <qmlproperty name="name" fullname="SortFilterProxyModel.SingleRole.name" status="active" access="public" documented="true" type="string" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="RegExpRole" qml-module-name="SortFilterProxyModel" qml-base-type="SortFilterProxyModel::ProxyRole" fullname="SortFilterProxyModel.RegExpRole" href="qml-sortfilterproxymodel-regexprole.html" status="active" access="public" documented="true" title="RegExpRole" fulltitle="RegExpRole" subtitle="" groups="ProxyRoles" brief="A ProxyRole extracting data from a source role via a regular expression">
            <qmlproperty name="roleName" fullname="SortFilterProxyModel.RegExpRole.roleName" href="qml-sortfilterproxymodel-regexprole.html#roleName-prop" status="active" access="public" documented="true" type="QString" attached="false" writable="true"/>
            <qmlproperty name="pattern" fullname="SortFilterProxyModel.RegExpRole.pattern" href="qml-sortfilterproxymodel-regexprole.html#pattern-prop" status="active" access="public" documented="true" type="QString" attached="false" writable="true"/>
            <qmlproperty name="caseSensitivity" fullname="SortFilterProxyModel.RegExpRole.caseSensitivity" href="qml-sortfilterproxymodel-regexprole.html#caseSensitivity-prop" status="active" access="public" documented="true" type="Qt::CaseSensitivity" attached="false" writable="true"/>
        </qmlclass>
        <qmlclass name="tst_rolesorter" qml-module-name="tst_rolesorter" href="qml-tst-rolesorter.html" status="internal" access="private" location="tst_rolesorter.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_rolesorter.qml" lineno="6" title="tst_rolesorter" fulltitle="tst_rolesorter" subtitle="">
            <qmlproperty name="sorters" fullname="tst_rolesorter::sorters" href="qml-tst-rolesorter.html#sorters-prop" status="internal" access="private" location="tst_rolesorter.qml" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/tests/tst_rolesorter.qml" lineno="7" type="RoleSorter" attached="false" writable="true"/>
        </qmlclass>
        <group name="FilterAttached" href="filterattached.html" status="internal" seen="false" title="" members="FilterContainer"/>
        <group name="FilterContainer" href="filtercontainer.html" status="internal" seen="false" title="" members="AllOf,AnyOf,FilterRole,SwitchRole,SortFilterProxyModel,FilterSorter"/>
        <group name="Filters" href="filters.html" status="internal" seen="false" title="" members="AllOf,AnyOf,ExpressionFilter,Filter,IndexFilter,RangeFilter,RegExpFilter,RoleFilter,ValueFilter"/>
        <group name="ProxyRoles" href="proxyroles.html" status="internal" seen="false" title="" members="ExpressionRole,FilterRole,JoinRole,ProxyRole,RegExpRole,SingleRole,SwitchRole"/>
        <group name="SortFilterProxyModel" href="sortfilterproxymodel.html" status="internal" seen="false" title="" members="SortFilterProxyModel"/>
        <group name="SorterAttached" href="sorterattached.html" status="internal" seen="false" title="" members="SorterContainer"/>
        <group name="SorterContainer" href="sortercontainer.html" status="internal" seen="false" title="" members="SortFilterProxyModel"/>
        <group name="Sorters" href="sorters.html" status="internal" seen="false" title="" members="ExpressionSorter,FilterSorter,RoleSorter,Sorter,StringSorter"/>
        <qmlmodule name="SortFilterProxyModel" qml-module-name="SortFilterProxyModel" qml-module-version="." href="sortfilterproxymodel-qmlmodule.html" status="active" location="index.qdoc" filepath="D:/coding/MeetupSFPMMap/vendor/SortFilterProxyModel/index.qdoc" lineno="25" documented="true" seen="true" title="" module="SortFilterProxyModel" members="AllOf,AnyOf,ExpressionFilter,Filter,FilterContainer,IndexFilter,RangeFilter,RegExpFilter,RoleFilter,ValueFilter,ExpressionRole,FilterRole,JoinRole,ProxyRole,RegExpRole,SingleRole,SwitchRole,SortFilterProxyModel,ExpressionSorter,FilterSorter,RoleSorter,Sorter,SorterContainer,StringSorter"/>
    </namespace>
</INDEX>
