<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- index.qdoc -->
  <title>SortFilterProxyModel QML Module | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<h1 class="title">SortFilterProxyModel QML Module</h1>
<span class="subtitle"></span>
<!-- $$$index.html-description -->
<div class="descr"> <a name="details"></a>
<p><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html">SortFilterProxyModel</a> is an implementation of QSortFilterProxyModel conveniently exposed for QML. <div class="table"><table class="annotated">
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html">SortFilterProxyModel</a></p></td><td class="tblDescr"><p>Filters and sorts data coming from a source QAbstractItemModel</p></td></tr>
</table></div>
</p>
<a name="filters"></a>
<h2 id="filters">Filters</h2>
<div class="table"><table class="annotated">
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-allof.html">AllOf</a></p></td><td class="tblDescr"><p>Filter container accepting rows accepted by all its child filters</p></td></tr>
<tr class="even topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-anyof.html">AnyOf</a></p></td><td class="tblDescr"><p>Filter container accepting rows accepted by at least one of its child filters</p></td></tr>
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-expressionfilter.html">ExpressionFilter</a></p></td><td class="tblDescr"><p>Filters row with a custom filtering</p></td></tr>
<tr class="even topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-filter.html">Filter</a></p></td><td class="tblDescr"><p>Base type for the SortFilterProxyModel filters</p></td></tr>
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-indexfilter.html">IndexFilter</a></p></td><td class="tblDescr"><p>Filters rows based on their source index</p></td></tr>
<tr class="even topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-rangefilter.html">RangeFilter</a></p></td><td class="tblDescr"><p>Filters rows between boundary values</p></td></tr>
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-regexpfilter.html">RegExpFilter</a></p></td><td class="tblDescr"><p>Filters rows matching a regular expression</p></td></tr>
<tr class="even topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-rolefilter.html">RoleFilter</a></p></td><td class="tblDescr"><p>Base type for filters based on a source model role</p></td></tr>
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-valuefilter.html">ValueFilter</a></p></td><td class="tblDescr"><p>Filters rows matching exactly a value</p></td></tr>
</table></div>
<a name="related-attached-types"></a>
<h3 id="related-attached-types">Related attached types</h3>
<div class="table"><table class="annotated">
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-filtercontainer.html">FilterContainer</a></p></td><td class="tblDescr"><p>Abstract interface for types containing Filters</p></td></tr>
</table></div>
<a name="sorters"></a>
<h2 id="sorters">Sorters</h2>
<div class="table"><table class="annotated">
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-expressionsorter.html">ExpressionSorter</a></p></td><td class="tblDescr"><p>Sorts row with a custom javascript expression</p></td></tr>
<tr class="even topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-filtersorter.html">FilterSorter</a></p></td><td class="tblDescr"><p>Sorts rows based on if they match filters</p></td></tr>
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-rolesorter.html">RoleSorter</a></p></td><td class="tblDescr"><p>Sorts rows based on a source model role</p></td></tr>
<tr class="even topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-sorter.html">Sorter</a></p></td><td class="tblDescr"><p>Base type for the SortFilterProxyModel sorters</p></td></tr>
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-stringsorter.html">StringSorter</a></p></td><td class="tblDescr"><p>Sorts rows based on a source model string role</p></td></tr>
</table></div>
<a name="related-attached-types"></a>
<h3 id="related-attached-types">Related attached types</h3>
<div class="table"><table class="annotated">
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-sortercontainer.html">SorterContainer</a></p></td><td class="tblDescr"><p>Abstract interface for types containing Sorters</p></td></tr>
</table></div>
<a name="proxyroles"></a>
<h2 id="proxyroles">ProxyRoles</h2>
<div class="table"><table class="annotated">
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-expressionrole.html">ExpressionRole</a></p></td><td class="tblDescr"><p>A custom role computed from a javascript expression</p></td></tr>
<tr class="even topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-filterrole.html">FilterRole</a></p></td><td class="tblDescr"><p>A role resolving to true for rows matching all its filters</p></td></tr>
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-joinrole.html">JoinRole</a></p></td><td class="tblDescr"><p>Role made from concatenating other roles</p></td></tr>
<tr class="even topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-proxyrole.html">ProxyRole</a></p></td><td class="tblDescr"><p>Base type for the SortFilterProxyModel proxy roles</p></td></tr>
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-regexprole.html">RegExpRole</a></p></td><td class="tblDescr"><p>A ProxyRole extracting data from a source role via a regular expression</p></td></tr>
<tr class="even topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-singlerole.html">SingleRole</a></p></td><td class="tblDescr"><p>Base type for the SortFilterProxyModel proxy roles defining a single role</p></td></tr>
<tr class="odd topAlign"><td class="tblName"><p><a href="qml-sortfilterproxymodel-switchrole.html">SwitchRole</a></p></td><td class="tblDescr"><p>A role using Filter to conditionnaly compute its data</p></td></tr>
</table></div>
</div>
<!-- @@@index.html -->
</body>
</html>
