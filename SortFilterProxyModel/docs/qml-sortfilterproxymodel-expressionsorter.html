<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- expressionsorter.cpp -->
  <title>ExpressionSorter QML Type | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar">
<div class="toc">
<h3><a name="toc">Contents</a></h3>
<ul>
<li class="level1"><a href="#properties">Properties</a></li>
<li class="level1"><a href="#details">Detailed Description</a></li>
</ul>
</div>
<div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">ExpressionSorter QML Type</h1>
<span class="subtitle"></span>
<!-- $$$ExpressionSorter-brief -->
<p>Sorts row with a custom javascript expression. <a href="#details">More...</a></p>
<!-- @@@ExpressionSorter -->
<div class="table"><table class="alignedsummary">
<tr><td class="memItemLeft rightAlign topAlign"> Import Statement:</td><td class="memItemRight bottomAlign"> import SortFilterProxyModel .</td></tr><tr><td class="memItemLeft rightAlign topAlign"> Inherits:</td><td class="memItemRight bottomAlign"> <p><a href="qml-sortfilterproxymodel-sorter.html">Sorter</a></p>
</td></tr></table></div><ul>
<li><a href="qml-sortfilterproxymodel-expressionsorter-members.html">List of all members, including inherited members</a></li>
</ul>
<a name="properties"></a>
<h2 id="properties">Properties</h2>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-expressionsorter.html#enabled-prop">enabled</a></b></b> : bool</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-expressionsorter.html#expression-prop">expression</a></b></b> : expression</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-expressionsorter.html#priority-prop">priority</a></b></b> : int</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-expressionsorter.html#sortOrder-prop">sortOrder</a></b></b> : Qt::SortOrder</li>
</ul>
<!-- $$$ExpressionSorter-description -->
<a name="details"></a>
<h2 id="details">Detailed Description</h2>
<p>An ExpressionSorter is a <a href="qml-sortfilterproxymodel-sorter.html">Sorter</a> allowing to implement custom sorting based on a javascript expression.</p>
<!-- @@@ExpressionSorter -->
<h2>Property Documentation</h2>
<!-- $$$enabled -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="enabled-prop"></a><span class="name">enabled</span> : <span class="type">bool</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds whether the sorter is enabled. A disabled sorter will not change the order of the rows.</p>
<p>By default, sorters are enabled.</p>
</div></div><!-- @@@enabled -->
<br/>
<!-- $$$expression -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="expression-prop"></a><span class="name">expression</span> : <span class="type">expression</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>An expression to implement custom sorting. It must evaluate to a bool. It has the same syntax has a <a href="http://doc.qt.io/qt-5/qtqml-syntax-propertybinding.html">Property Binding</a>, except that it will be evaluated for each of the source model's rows. Model data is accessible for both rows with the <code>modelLeft</code>, and <code>modelRight</code> properties:</p>
<pre class="cpp">sorters: ExpressionSorter {
    expression: {
        <span class="keyword">return</span> modelLeft<span class="operator">.</span>someRole <span class="operator">&lt;</span> modelRight<span class="operator">.</span>someRole;
    }
}</pre>
<p>The <code>index</code> of the row is also available through <code>modelLeft</code> and <code>modelRight</code>.</p>
<p>The expression should return <code>true</code> if the value of the left item is less than the value of the right item, otherwise returns false.</p>
<p>This expression is reevaluated for a row every time its model data changes. When an external property (not <code>index*</code> or in <code>model*</code>) the expression depends on changes, the expression is reevaluated for every row of the source model. To capture the properties the expression depends on, the expression is first executed with invalid data and each property access is detected by the QML engine. This means that if a property is not accessed because of a conditional, it won't be captured and the expression won't be reevaluted when this property changes.</p>
<p>A workaround to this problem is to access all the properties the expressions depends unconditionally at the beggining of the expression.</p>
</div></div><!-- @@@expression -->
<br/>
<!-- $$$priority -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="priority-prop"></a><span class="name">priority</span> : <span class="type">int</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds the sort priority of this sorter. Sorters with a higher priority are applied first. In case of equal priority, Sorters are ordered by their insertion order.</p>
<p>By default, the priority is 0.</p>
</div></div><!-- @@@priority -->
<br/>
<!-- $$$sortOrder -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="sortOrder-prop"></a><span class="name">sortOrder</span> : <span class="type">Qt::SortOrder</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds the sort order of this sorter.</p>
<div class="table"><table class="valuelist"><tr valign="top" class="odd"><th class="tblConst">Constant</th><th class="tbldscr">Description</th></tr>
<tr><td class="topAlign"><code>Qt.AscendingOrder</code></td><td class="topAlign">The items are sorted ascending e.g&#x2e; starts with 'AAA' ends with 'ZZZ' in Latin-1 locales</td></tr>
<tr><td class="topAlign"><code>Qt.DescendingOrder</code></td><td class="topAlign">The items are sorted descending e.g&#x2e; starts with 'ZZZ' ends with 'AAA' in Latin-1 locales</td></tr>
</table></div>
<p>By default, sorting is in ascending order.</p>
</div></div><!-- @@@sortOrder -->
<br/>
</body>
</html>
