<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- switchrole.cpp -->
  <title>List of All Members for SwitchRole | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar"><div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">List of All Members for SwitchRole</h1>
<p>This is the complete list of members for <a href="qml-sortfilterproxymodel-switchrole.html">SwitchRole</a>, including inherited members.</p>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-switchrole.html#defaultRoleName-prop">defaultRoleName</a></b></b> : string</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-switchrole.html#defaultValue-prop">defaultValue</a></b></b> : var</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-switchrole.html#filters-prop">filters</a></b></b> : list&lt;Filter&gt; [default]</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-switchrole.html#name-prop">name</a></b></b> : string</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-switchrole.html#value-attached-prop">value</a></b></b> : var [attached]</li>
</ul>
</body>
</html>
