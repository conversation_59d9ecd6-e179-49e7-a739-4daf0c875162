<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- filterrole.cpp -->
  <title>List of All Members for FilterRole | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar"><div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">List of All Members for FilterRole</h1>
<p>This is the complete list of members for <a href="qml-sortfilterproxymodel-filterrole.html">FilterRole</a>, including inherited members.</p>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-filterrole.html#filters-prop">filters</a></b></b> : list&lt;Filter&gt; [default]</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-filterrole.html#name-prop">name</a></b></b> : string</li>
</ul>
</body>
</html>
