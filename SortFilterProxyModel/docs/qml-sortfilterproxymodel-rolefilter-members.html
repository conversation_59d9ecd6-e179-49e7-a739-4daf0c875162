<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- rolefilter.cpp -->
  <title>List of All Members for RoleFilter | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar"><div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">List of All Members for RoleFilter</h1>
<p>This is the complete list of members for <a href="qml-sortfilterproxymodel-rolefilter.html">RoleFilter</a>, including inherited members.</p>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-rolefilter.html#enabled-prop">enabled</a></b></b> : bool</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-rolefilter.html#inverted-prop">inverted</a></b></b> : bool</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-rolefilter.html#roleName-prop">roleName</a></b></b> : string</li>
</ul>
</body>
</html>
