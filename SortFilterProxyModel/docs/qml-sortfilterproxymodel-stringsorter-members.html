<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- stringsorter.cpp -->
  <title>List of All Members for StringSorter | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar"><div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">List of All Members for StringSorter</h1>
<p>This is the complete list of members for <a href="qml-sortfilterproxymodel-stringsorter.html">StringSorter</a>, including inherited members.</p>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-stringsorter.html#caseSensitivity-prop">caseSensitivity</a></b></b> : Qt.CaseSensitivity</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-stringsorter.html#ignorePunctation-prop">ignorePunctation</a></b></b> : bool</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-stringsorter.html#locale-prop">locale</a></b></b> : Locale</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-stringsorter.html#numericMode-prop">numericMode</a></b></b> : bool</li>
</ul>
<p>The following members are inherited from <a href="qml-sortfilterproxymodel-rolesorter.html">RoleSorter</a>.</p>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-stringsorter.html#enabled-prop">enabled</a></b></b> : bool</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-stringsorter.html#priority-prop">priority</a></b></b> : int</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-rolesorter.html#roleName-prop">roleName</a></b></b> : string</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-stringsorter.html#sortOrder-prop">sortOrder</a></b></b> : Qt::SortOrder</li>
</ul>
</body>
</html>
