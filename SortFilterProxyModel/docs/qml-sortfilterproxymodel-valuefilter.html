<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- valuefilter.cpp -->
  <title>ValueFilter QML Type | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar">
<div class="toc">
<h3><a name="toc">Contents</a></h3>
<ul>
<li class="level1"><a href="#properties">Properties</a></li>
<li class="level1"><a href="#details">Detailed Description</a></li>
</ul>
</div>
<div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">ValueFilter QML Type</h1>
<span class="subtitle"></span>
<!-- $$$ValueFilter-brief -->
<p>Filters rows matching exactly a value. <a href="#details">More...</a></p>
<!-- @@@ValueFilter -->
<div class="table"><table class="alignedsummary">
<tr><td class="memItemLeft rightAlign topAlign"> Import Statement:</td><td class="memItemRight bottomAlign"> import SortFilterProxyModel .</td></tr><tr><td class="memItemLeft rightAlign topAlign"> Inherits:</td><td class="memItemRight bottomAlign"> <p><a href="qml-sortfilterproxymodel-rolefilter.html">RoleFilter</a></p>
</td></tr></table></div><ul>
<li><a href="qml-sortfilterproxymodel-valuefilter-members.html">List of all members, including inherited members</a></li>
</ul>
<a name="properties"></a>
<h2 id="properties">Properties</h2>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-valuefilter.html#enabled-prop">enabled</a></b></b> : bool</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-valuefilter.html#inverted-prop">inverted</a></b></b> : bool</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-valuefilter.html#roleName-prop">roleName</a></b></b> : string</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-valuefilter.html#value-prop">value</a></b></b> : variant</li>
</ul>
<!-- $$$ValueFilter-description -->
<a name="details"></a>
<h2 id="details">Detailed Description</h2>
<p>A ValueFilter is a simple <a href="qml-sortfilterproxymodel-rolefilter.html">RoleFilter</a> that accepts rows matching exactly the filter's value</p>
<p>In the following example, only rows with their <code>favorite</code> role set to <code>true</code> will be accepted when the checkbox is checked :</p>
<pre class="cpp">CheckBox {
   id: showOnlyFavoriteCheckBox
}

SortFilterProxyModel {
   sourceModel: contactModel
   filters: ValueFilter {
       roleName: <span class="string">&quot;favorite&quot;</span>
       value: <span class="keyword">true</span>
       enabled: showOnlyFavoriteCheckBox<span class="operator">.</span>checked
   }
}</pre>
<!-- @@@ValueFilter -->
<h2>Property Documentation</h2>
<!-- $$$enabled -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="enabled-prop"></a><span class="name">enabled</span> : <span class="type">bool</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds whether the filter is enabled. A disabled filter will accept every rows unconditionally (even if it's inverted).</p>
<p>By default, filters are enabled.</p>
</div></div><!-- @@@enabled -->
<br/>
<!-- $$$inverted -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="inverted-prop"></a><span class="name">inverted</span> : <span class="type">bool</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds whether the filter is inverted. When a filter is inverted, a row normally accepted would be rejected, and vice-versa.</p>
<p>By default, filters are not inverted.</p>
</div></div><!-- @@@inverted -->
<br/>
<!-- $$$roleName -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="roleName-prop"></a><span class="name">roleName</span> : <span class="type">string</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds the role name that the filter is using to query the source model's data when filtering items.</p>
</div></div><!-- @@@roleName -->
<br/>
<!-- $$$value -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="value-prop"></a><span class="name">value</span> : <span class="type">variant</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds the value used to filter the contents of the source model.</p>
</div></div><!-- @@@value -->
<br/>
</body>
</html>
