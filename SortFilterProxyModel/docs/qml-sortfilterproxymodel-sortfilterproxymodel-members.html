<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- qqmlsortfilterproxymodel.cpp -->
  <title>List of All Members for SortFilterProxyModel | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar"><div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">List of All Members for SortFilterProxyModel</h1>
<p>This is the complete list of members for <a href="qml-sortfilterproxymodel-sortfilterproxymodel.html">SortFilterProxyModel</a>, including inherited members.</p>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#count-prop">count</a></b></b> : int</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#delayed-prop">delayed</a></b></b> : bool</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#filters-prop">filters</a></b></b> : list&lt;Filter&gt;</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#proxyRoles-prop">proxyRoles</a></b></b> : list&lt;ProxyRole&gt;</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#sortRoleName-prop">sortRoleName</a></b></b> : string</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#sorters-prop">sorters</a></b></b> : list&lt;Sorter&gt;</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#sourceModel-prop">sourceModel</a></b></b> : QAbstractItemModel*</li>
<li class="fn">variant <b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#get-method-1">get</a></b></b>(<i>row</i>,  string <i>roleName</i>)</li>
<li class="fn">object <b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#get-method">get</a></b></b>(<i>row</i>)</li>
<li class="fn">int <b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#mapFromSource-method-1">mapFromSource</a></b></b>(<i>sourceRow</i>)</li>
<li class="fn">QModelIndex <b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#mapFromSource-method">mapFromSource</a></b></b>(<i>sourceIndex</i>)</li>
<li class="fn">int <b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#mapToSource-method-1">mapToSource</a></b></b>(<i>proxyRow</i>)</li>
<li class="fn">index <b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#mapToSource-method">mapToSource</a></b></b>(<i>proxyIndex</i>)</li>
<li class="fn">int <b><b><a href="qml-sortfilterproxymodel-sortfilterproxymodel.html#roleForName-method">roleForName</a></b></b>(<i>roleName</i>)</li>
</ul>
</body>
</html>
