<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- regexprole.cpp -->
  <title>List of All Members for RegExpRole | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar"><div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">List of All Members for RegExpRole</h1>
<p>This is the complete list of members for <a href="qml-sortfilterproxymodel-regexprole.html">RegExpRole</a>, including inherited members.</p>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-regexprole.html#caseSensitivity-prop">caseSensitivity</a></b></b> : Qt::CaseSensitivity</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-regexprole.html#pattern-prop">pattern</a></b></b> : QString</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-regexprole.html#roleName-prop">roleName</a></b></b> : QString</li>
</ul>
</body>
</html>
