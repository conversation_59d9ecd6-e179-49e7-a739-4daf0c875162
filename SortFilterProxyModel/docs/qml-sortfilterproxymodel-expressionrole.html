<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!-- expressionrole.cpp -->
  <title>ExpressionRole QML Type | SortFilterProxyModel</title>
  <link rel="stylesheet" type="text/css" href="style/offline.css" />
</head>
<body>
<div class="sidebar">
<div class="toc">
<h3><a name="toc">Contents</a></h3>
<ul>
<li class="level1"><a href="#properties">Properties</a></li>
<li class="level1"><a href="#details">Detailed Description</a></li>
</ul>
</div>
<div class="sidebar-content" id="sidebar-content"></div></div>
<h1 class="title">ExpressionRole QML Type</h1>
<span class="subtitle"></span>
<!-- $$$ExpressionRole-brief -->
<p>A custom role computed from a javascript expression. <a href="#details">More...</a></p>
<!-- @@@ExpressionRole -->
<div class="table"><table class="alignedsummary">
<tr><td class="memItemLeft rightAlign topAlign"> Import Statement:</td><td class="memItemRight bottomAlign"> import SortFilterProxyModel .</td></tr><tr><td class="memItemLeft rightAlign topAlign"> Inherits:</td><td class="memItemRight bottomAlign"> <p><a href="qml-sortfilterproxymodel-singlerole.html">SingleRole</a></p>
</td></tr></table></div><ul>
<li><a href="qml-sortfilterproxymodel-expressionrole-members.html">List of all members, including inherited members</a></li>
</ul>
<a name="properties"></a>
<h2 id="properties">Properties</h2>
<ul>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-expressionrole.html#expression-prop">expression</a></b></b> : expression</li>
<li class="fn"><b><b><a href="qml-sortfilterproxymodel-expressionrole.html#name-prop">name</a></b></b> : string</li>
</ul>
<!-- $$$ExpressionRole-description -->
<a name="details"></a>
<h2 id="details">Detailed Description</h2>
<p>An ExpressionRole is a <a href="qml-sortfilterproxymodel-proxyrole.html">ProxyRole</a> allowing to implement a custom role based on a javascript expression.</p>
<p>In the following example, the <code>c</code> role is computed by adding the <code>a</code> role and <code>b</code> role of the model :</p>
<pre class="cpp">SortFilterProxyModel {
   sourceModel: numberModel
   proxyRoles: ExpressionRole {
       name: <span class="string">&quot;c&quot;</span>
       expression: model<span class="operator">.</span>a <span class="operator">+</span> model<span class="operator">.</span>b
  }
}</pre>
<!-- @@@ExpressionRole -->
<h2>Property Documentation</h2>
<!-- $$$expression -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="expression-prop"></a><span class="name">expression</span> : <span class="type">expression</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>An expression to implement a custom role. It has the same syntax has a <a href="http://doc.qt.io/qt-5/qtqml-syntax-propertybinding.html">Property Binding</a> except it will be evaluated for each of the source model's rows. The data for this role will be the retuned valued of the expression. Data for each row is exposed like for a delegate of a QML View.</p>
<p>This expression is reevaluated for a row every time its model data changes. When an external property (not <code>index</code> or in <code>model</code>) the expression depends on changes, the expression is reevaluated for every row of the source model. To capture the properties the expression depends on, the expression is first executed with invalid data and each property access is detected by the QML engine. This means that if a property is not accessed because of a conditional, it won't be captured and the expression won't be reevaluted when this property changes.</p>
<p>A workaround to this problem is to access all the properties the expressions depends unconditionally at the beggining of the expression.</p>
</div></div><!-- @@@expression -->
<br/>
<!-- $$$name -->
<div class="qmlitem"><div class="qmlproto">
<div class="table"><table class="qmlname">
<tr valign="top" class="odd" id="">
<td class="tblQmlPropNode"><p>
<a name="name-prop"></a><span class="name">name</span> : <span class="type">string</span></p></td></tr>
</table></div>
</div><div class="qmldoc"><p>This property holds the role name of the proxy role.</p>
</div></div><!-- @@@name -->
<br/>
</body>
</html>
