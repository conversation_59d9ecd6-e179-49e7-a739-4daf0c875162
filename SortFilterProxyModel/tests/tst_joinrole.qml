import QtQuick 2.0
import QtQml 2.2
import QtTest 1.1
import SortFilterProxyModel 0.2
import QtQml 2.2

Item {
    ListModel {
        id: listModel
        ListElement { firstName: "<PERSON>"; lastName: "Tim<PERSON><PERSON>" }
    }

    SortFilterProxyModel {
        id: testModel
        sourceModel: listModel

        proxyRoles: JoinR<PERSON> {
            id: joinRole
            name: "fullName"
            roleNames: ["firstName", "lastName"]
        }
    }

    Instantiator {
        id: instantiator
        model: testModel
        QtObject {
            property string fullName: model.fullName
        }
    }

    TestCase {
        name: "JoinRole"

        function test_joinRole() {
            compare(instantiator.object.fullName, "<PERSON> Timberlake");
            listModel.setProperty(0, "lastName", "Bieber");
            compare(instantiator.object.fullName, "<PERSON> Bieber");
            joinRole.roleNames = ["lastName", "firstName"];
            compare(instantiator.object.fullName, "<PERSON><PERSON><PERSON> Justin");
            joinRole.separator = " - ";
            compare(instantiator.object.fullName, "<PERSON><PERSON><PERSON> - <PERSON>");
        }
    }
}
