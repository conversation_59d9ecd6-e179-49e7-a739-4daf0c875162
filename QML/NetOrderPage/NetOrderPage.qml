﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import EnumTool 1.0
import QtGraphicalEffects 1.0

CusRect {
    id: net_order_page
    width: 800 * dpi_ratio
    height: 500 * dpi_ratio
    color: ST.color_grey

    property int width_btn: 340 * dpi_ratio
    property int height_btn: 70 * dpi_ratio

    property bool is_check_order: false //网单核单

    Component.onCompleted: {
    }

    function openCalendarBegin(date) {
        window_root.loader_4_calendar_begin.sourceComponent = calendar_begin
        window_root.loader_4_calendar_begin.item.open()
        window_root.loader_4_calendar_begin.item.set(date)
    }
    function openCalendarEnd() {
        window_root.loader_4_calendar_end.sourceComponent = calendar_end
        window_root.loader_4_calendar_end.item.open()
    }

    function refreshContainData() {
        switch (lv_net_order_tab.cur_net_order_page) {
        case EnumTool.NET_ORDER_UNKNOW:
        case EnumTool.NET_ORDER_WAITING_SHIPMENT:
        case EnumTool.NET_ORDER_WAITING_RECEIVING:
        case EnumTool.NET_ORDER_WAITING_SELF_PICKUP:
        case EnumTool.NET_ORDER_COMPLETED:
        case EnumTool.NET_ORDER_WAITING_VERIFY:
        case EnumTool.NET_ORDER_DELIVERY_EXCEPTION:
        case EnumTool.NET_ORDER_WAITING_EVALUATED:
        case EnumTool.NET_ORDER_ALL_ORDERS:
            contain_store_orders.reqStoreOrdersRecord()
            break
        case EnumTool.NET_ORDER_REFUND_WAITING_REVIEW:
        case EnumTool.NET_ORDER_REFUND_REFUNDED:
        case EnumTool.NET_ORDER_REFUND_REJECTED:
        case EnumTool.NET_ORDER_REFUND_ALL_ORDERS:
            contain_refund_orders.reqRefundOrdersRecord()
            break
        }
        //lv_net_order_tab.reqRefreshNetOrderCount()
        lv_net_order_tab.reqRefreshNetOrderSaleCount()
        lv_net_order_tab.reqRefreshNetOrderRefundCount()
    }

    Image {
        anchors.fill: parent
        source: "/Images/mainPageBackground.jpg"
    }

    ColumnLayout {
        id: cl_net_order_page
        anchors.fill: parent
        anchors.margins: ST.margin
        spacing: ST.margin

        CusRect {
            id: rect_tab_bar
            Layout.fillWidth: true
            Layout.preferredHeight: 80 * dpi_ratio
            color: ST.color_white_pure
            Layout.alignment: Qt.AlignTop

            //            layer.enabled: true
            //            layer.effect: OpacityMask {
            //                maskSource: Rectangle {
            //                    width: rect_tab_bar.width
            //                    height: rect_tab_bar.height
            //                    radius: ST.radius
            //                }
            //            }
            RowLayout {
                anchors.fill: parent
                spacing: ST.margin * 2

                ListView {
                    id: lv_net_order_tab

                    Layout.fillHeight: true
                    Layout.preferredWidth: list_model1.count * height * 1.6

                    property int cur_net_order_page: EnumTool.NET_ORDER_WAITING_SHIPMENT

                    property bool is_sotre_orders: lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_UNKNOW || ////
                                                   lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_WAITING_SHIPMENT || /////
                                                   lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_WAITING_RECEIVING || //////
                                                   lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_WAITING_SELF_PICKUP || ////
                                                   lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_COMPLETED || //////
                                                   lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_DELIVERY_EXCEPTION || ////////
                                                   lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_WAITING_EVALUATED || ////////
                                                   lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_WAITING_VERIFY || ////////
                                                   lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_ALL_ORDERS ///////

                    property bool is_refund_orders: lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_REFUND_WAITING_REVIEW || ////
                                                    lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_REFUND_REFUNDED || /////
                                                    lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_REFUND_REJECTED || /////
                                                    lv_net_order_tab.cur_net_order_page == EnumTool.NET_ORDER_REFUND_ALL_ORDERS /////

                    onIs_sotre_ordersChanged: {
                        if (is_sotre_orders) {
                            setSotreOrdersTab()
                        }
                    }
                    onIs_refund_ordersChanged: {
                        if (is_refund_orders) {
                            setRefundOrdersTab()
                        }
                    }

                    interactive: false
                    //                    clip: true
                    model: ListModel {
                        id: list_model1
                    }
                    orientation: ListView.Horizontal

                    delegate: RadioBtnRectC3 {
                        id: rect_page_tab_item
                        height: ListView.view.height
                        //width: height * 1.4
                        width: text.length > 8 ?height * 1.8:height * 1.4
                        checked: index == 0
                        onClicked: {
                            lv_net_order_tab.cur_net_order_page = control
                            if (lv_net_order_tab.is_sotre_orders) {
                                contain_store_orders.reqStoreOrdersRecord()
                            } else if (lv_net_order_tab.is_refund_orders) {
                                contain_refund_orders.reqRefundOrdersRecord()
                            }
                        }

                        //小红点
                        CusRect {
                            id:hongdianRec
                            anchors.top: rect_page_tab_item.top
                            //                            anchors.topMargin: -5 * dpi_ratio
                            anchors.right: rect_page_tab_item.right
                            //                            anchors.rightMargin: -5 * dpi_ratio
                            width: red_point_text.text.length > 2 ? 30 * dpi_ratio + (red_point_text.text.length - 2) * 20 * dpi_ratio : 30 * dpi_ratio
                            height: 30 * dpi_ratio
                            radius: height / 2
                            color:  ST.color_red
                            visible: true
                            CusText {
                                id: red_point_text
                                text: num
                                color: ST.color_white_pure
                                anchors.centerIn: parent
                                onTextChanged: {
                                    if (red_point_text.text !== "0")
                                    {
                                        hongdianRec.visible = true;
                                    }else{
                                        hongdianRec.visible = false;
                                    }
                                }
                            }

                        }
                    }

                    Component.onCompleted: {
                        setSotreOrdersTab()
                        //reqRefreshNetOrderCount()
                        reqRefreshNetOrderSaleCount()
                        reqRefreshNetOrderRefundCount()
                    }
                    onVisibleChanged: {
                        if (visible)
                            //reqRefreshNetOrderCount()
                            reqRefreshNetOrderSaleCount()
                            reqRefreshNetOrderRefundCount()
                    }
                    function reqRefreshNetOrderSaleCount() {
                        netOrderControl.reqGetNetOrderCountNewByStatus(function (is_succ, data) {
                            if (is_succ) {
                                var json_doc = JSON.parse(data)
                                var json_doc_data = json_doc["data"]
                                if(json_doc_data.hasOwnProperty('sendCount')){//待发货
                                    setOrdersTabNum(EnumTool.NET_ORDER_WAITING_SHIPMENT, json_doc_data.sendCount)
                                    orderCount = json_doc_data.sendCount + json_doc_data.pickupCount;
                                }
                                if(json_doc_data.hasOwnProperty('confirmCount')){//待确认 //配送订单待接单
                                    setOrdersTabNum(EnumTool.NET_ORDER_WAITING_VERIFY, json_doc_data.confirmCount)
                                }
                                if(json_doc_data.hasOwnProperty('errorCount')){//配送异常
                                    setOrdersTabNum(EnumTool.NET_ORDER_DELIVERY_EXCEPTION, json_doc_data.errorCount)
                                }
                                if(json_doc_data.hasOwnProperty('evaluateCount')){//待评论
                                    setOrdersTabNum(EnumTool.NET_ORDER_WAITING_EVALUATED, json_doc_data.evaluateCount)
                                }
                                if(json_doc_data.hasOwnProperty('pickupCount')){//待自提
                                    setOrdersTabNum(EnumTool.NET_ORDER_WAITING_SELF_PICKUP, json_doc_data.pickupCount)
                                }
                                if(json_doc_data.hasOwnProperty('receivedCount')){//待收货
                                    setOrdersTabNum(EnumTool.NET_ORDER_WAITING_RECEIVING, json_doc_data.receivedCount)
                                }
                                if(json_doc_data.hasOwnProperty('successCount')){//已完成
                                    setOrdersTabNum(EnumTool.NET_ORDER_COMPLETED, json_doc_data.successCount)
                                }
                                if(json_doc_data.hasOwnProperty('totalCount')){//全部订单
                                    setOrdersTabNum(EnumTool.NET_ORDER_ALL_ORDERS, json_doc_data.totalCount)
                                }
                            } else {
                                toast.openWarn(qsTr("获取网单小红点信息失败"))
                                orderCount = 0;
                            }
                        }, btn_calendar_begin.text, btn_calendar_end.text,"1")
                    }
                    function reqRefreshNetOrderRefundCount() {
                        //resetRefundOrdersTabNum() 会造成角标闪动
                        netOrderControl.reqGetNetOrderCountNewByStatus(function (is_succ, data) {
                            if (is_succ) {
                                var json_doc = JSON.parse(data)
                                var json_doc_data = json_doc["data"]
                                if(json_doc_data.hasOwnProperty('auditCount')){//退款-待审核
                                    setOrdersTabNum(EnumTool.NET_ORDER_REFUND_WAITING_REVIEW, json_doc_data.auditCount)
                                }
                                if(json_doc_data.hasOwnProperty('refundCount')){//退款-已退款
                                    setOrdersTabNum(EnumTool.NET_ORDER_REFUND_REFUNDED, json_doc_data.refundCount)
                                }
                                if(json_doc_data.hasOwnProperty('refuseCount')){//退款-已拒绝
                                    setOrdersTabNum(EnumTool.NET_ORDER_REFUND_REJECTED, json_doc_data.refuseCount)
                                }
                                if(json_doc_data.hasOwnProperty('retTotalCount')){//退款-全部订单
                                    setOrdersTabNum(EnumTool.NET_ORDER_REFUND_ALL_ORDERS, json_doc_data.retTotalCount)
                                    rect_red_point_swith.all_refund_num = json_doc_data.retTotalCount
                                }
                            } else {
                                toast.openWarn(qsTr("获取网单小红点信息失败"))
                                orderCount = 0;
                            }
                        }, btn_calendar_begin.text, btn_calendar_end.text,"2")
                    }
                    function reqRefreshNetOrderCount() {
                        //resetRefundOrdersTabNum() 会造成角标闪动
                        netOrderControl.reqGetNetOrderCountByStatus(function (is_succ, data) {
                            if (is_succ) {
                                var json_doc = JSON.parse(data)
                                var json_doc_data = json_doc["data"]
                                for (var i = 0; i < json_doc_data.length; ++i) {
                                    var cur_item = json_doc_data[i]
                                    switch (cur_item.saleListHandleState) {
                                    case 2:
                                        //待发货
                                        setOrdersTabNum(EnumTool.NET_ORDER_WAITING_SHIPMENT, cur_item.count)
                                        orderCount = cur_item.count;
                                        break
                                    case 3:
                                        //待收货
                                        setOrdersTabNum(EnumTool.NET_ORDER_WAITING_RECEIVING, cur_item.count)
                                        break
                                    case 4:
                                        //已完成
                                        setOrdersTabNum(EnumTool.NET_ORDER_COMPLETED, cur_item.count)
                                        break
                                        //                                    case 5:
                                        //                                        //已取消
                                        //                                        setRefundOrdersTabNum(NET_ORDER_WAITING_SHIPMENT, cur_item.count)
                                        //                                        break
                                    case 6:
                                        //待评论
                                        setOrdersTabNum(EnumTool.NET_ORDER_WAITING_EVALUATED, cur_item.count)
                                        break
                                    case 7:
                                        //配送订单待接单
                                        setOrdersTabNum(EnumTool.NET_ORDER_WAITING_VERIFY, cur_item.count)
                                        break
                                        //                                    case 8:
                                        //                                        //待付款
                                        //                                        setRefundOrdersTabNum(NET_ORDER_WAITING_SHIPMENT, cur_item.count)
                                        //                                        break
                                    case 9:
                                        //待自提
                                        setOrdersTabNum(EnumTool.NET_ORDER_WAITING_SELF_PICKUP, cur_item.count)
                                        break
                                    case 10:
                                        //配送异常
                                        setOrdersTabNum(EnumTool.NET_ORDER_DELIVERY_EXCEPTION, cur_item.count)
                                        break
                                    case -1:
                                        //全部订单
                                        setOrdersTabNum(EnumTool.NET_ORDER_ALL_ORDERS, cur_item.count)
                                        //rect_red_point_swith.all_order_num = cur_item.count //error
                                        break
                                        //*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\
                                    case -2:
                                        //退款-待审核
                                        setOrdersTabNum(EnumTool.NET_ORDER_REFUND_WAITING_REVIEW, cur_item.count)
                                        break
                                    case -3:
                                        //退款-已退款
                                        setOrdersTabNum(EnumTool.NET_ORDER_REFUND_REFUNDED, cur_item.count)
                                        break
                                    case -4:
                                        //退款-已拒绝
                                        setOrdersTabNum(EnumTool.NET_ORDER_REFUND_REJECTED, cur_item.count)
                                        break
                                    case -5:
                                        //退款-全部订单
                                        setOrdersTabNum(EnumTool.NET_ORDER_REFUND_ALL_ORDERS, cur_item.count)
                                        rect_red_point_swith.all_refund_num = cur_item.count
                                        break
                                    }

                                }
                            } else {
                                toast.openWarn(qsTr("获取网单小红点信息失败"))
                                orderCount = 0;
                            }
                        }, btn_calendar_begin.text, btn_calendar_end.text)
                    }

                    function setSotreOrdersTab() {
                        list_model1.clear()
                        list_model1.append({
                                               "name": qsTr("待发货"),
                                               "control": EnumTool.NET_ORDER_WAITING_SHIPMENT,
                                               "num": 0
                                           })
                        list_model1.append({
                                               "name": qsTr("待收货"),
                                               "control": EnumTool.NET_ORDER_WAITING_RECEIVING,
                                               "num": 0
                                           })
                        list_model1.append({
                                               "name": qsTr("待自提"),
                                               "control": EnumTool.NET_ORDER_WAITING_SELF_PICKUP,
                                               "num": 0
                                           })
                        list_model1.append({
                                               "name": qsTr("已完成"),
                                               "control": EnumTool.NET_ORDER_COMPLETED,
                                               "num": 0
                                           })
                        list_model1.append({
                                               "name": qsTr("配送异常"),
                                               "control": EnumTool.NET_ORDER_DELIVERY_EXCEPTION,
                                               "num": 0
                                           })
                        list_model1.append({
                                               "name": qsTr("待确认"),
                                               "control": EnumTool.NET_ORDER_WAITING_VERIFY,
                                               "num": 0
                                           })
                        list_model1.append({
                                               "name": qsTr("待评价"),
                                               "control": EnumTool.NET_ORDER_WAITING_EVALUATED,
                                               "num": 0
                                           })
                        list_model1.append({
                                               "name": qsTr("全部订单"),
                                               "control": EnumTool.NET_ORDER_ALL_ORDERS,
                                               "num": 0
                                           })
                    }

                    function setRefundOrdersTab() {
                        list_model1.clear()
                        list_model1.append({
                                               "name": qsTr("待审核"),
                                               "control": EnumTool.NET_ORDER_REFUND_WAITING_REVIEW,
                                               "num": 0
                                           })
                        list_model1.append({
                                               "name": qsTr("已退款"),
                                               "control": EnumTool.NET_ORDER_REFUND_REFUNDED,
                                               "num": 0
                                           })
                        list_model1.append({
                                               "name": qsTr("已拒绝"),
                                               "control": EnumTool.NET_ORDER_REFUND_REJECTED,
                                               "num": 0
                                           })
                        list_model1.append({
                                               "name": qsTr("全部订单"),
                                               "control": EnumTool.NET_ORDER_REFUND_ALL_ORDERS,
                                               "num": 0
                                           })
                    }

                    function setOrdersTabNum(control, num) {
                        for (var i = 0; i < list_model1.count; ++i) {
                            var cur_item = list_model1.get(i)
                            if (cur_item.control == control) {
                                list_model1.setProperty(i, "num", num)
                                break
                            }
                        }
                    }

                    function resetRefundOrdersTabNum() {
                        for (var i = 0; i < list_model1.count; ++i) {
                            list_model1.setProperty(i, "num", 0)
                        }
                    }
                    Connections {
                        target: mqttControl
                        function onSigMqttRefreshOrderCountByStatus4Qml() {
                            logMgr.logDataInfo4Qml("网单MQTT消息到来，网单页开启更新角标显示")
                            lv_net_order_tab.reqRefreshNetOrderSaleCount()
                            lv_net_order_tab.reqRefreshNetOrderRefundCount()
                            }
                        }
                }
                CusRect {
                    Layout.preferredHeight: 65 * dpi_ratio
                    Layout.preferredWidth: 2 * dpi_ratio
                    color: ST.color_grey_border
                    Layout.alignment: Qt.AlignVCenter
                }

                CusRect {
                    id: rect_red_point_swith

                    Layout.fillHeight: true
                    Layout.preferredWidth: 210 * dpi_ratio
                    color: ST.color_transparent

                    property string all_refund_num: "0"
                    property string all_order_num: "0"

                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 15 * dpi_ratio

                        Image {
                            source: lv_net_order_tab.is_sotre_orders ? "/Images/refund_page.png" : "/Images/net_record.png"
                            Layout.fillHeight: true
                            Layout.preferredWidth: 32 * dpi_ratio
                            Layout.alignment: Qt.AlignVCenter
                            fillMode: Image.PreserveAspectFit
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusText {
                                anchors.centerIn: parent

                                text: if (lv_net_order_tab.is_sotre_orders) {
                                          return qsTr("退款单")
                                      } else if (lv_net_order_tab.is_refund_orders) {
                                          return qsTr("店铺订单")
                                      } else {
                                          return ""
                                      }
                            }
                        }
                    }
                    //小红点
                    CusRect {
                        anchors.top: rect_red_point_swith.top
                        anchors.right: rect_red_point_swith.right
                        width: red_point_text_4_switch.text.length > 2 ? 30 * dpi_ratio + (red_point_text_4_switch.text.length - 2) * 20 * dpi_ratio : 30 * dpi_ratio
                        height: 30 * dpi_ratio
                        radius: height / 2
                        color: red_point_text_4_switch.text == "0" ? ST.color_green : ST.color_red
                        visible: red_point_text_4_switch.text != "0"
                        CusText {
                            id: red_point_text_4_switch
                            text: if (lv_net_order_tab.is_sotre_orders) {
                                      return rect_red_point_swith.all_refund_num
                                  } else if (lv_net_order_tab.is_refund_orders) {
                                      return rect_red_point_swith.all_order_num
                                  } else {
                                      return ""
                                  }
                            color: ST.color_white_pure
                            anchors.centerIn: parent
                        }
                    }
                    MouseArea {
                        anchors.fill: parent

                        onClicked: {
                            if (lv_net_order_tab.is_sotre_orders) {
                                lv_net_order_tab.cur_net_order_page = EnumTool.NET_ORDER_REFUND_WAITING_REVIEW
                                //lv_net_order_tab.reqRefreshNetOrderCount()
                                lv_net_order_tab.reqRefreshNetOrderSaleCount()
                                lv_net_order_tab.reqRefreshNetOrderRefundCount()
                                return
                            }
                            if (lv_net_order_tab.is_refund_orders) {
                                lv_net_order_tab.cur_net_order_page = EnumTool.NET_ORDER_WAITING_SHIPMENT
                                //lv_net_order_tab.reqRefreshNetOrderCount()
                                lv_net_order_tab.reqRefreshNetOrderSaleCount()
                                lv_net_order_tab.reqRefreshNetOrderRefundCount()
                                return
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.fillWidth: true
                }

                CusRect {
                    Layout.alignment: Qt.AlignRight
                    Layout.fillHeight: true
                    Layout.preferredWidth: 240 * dpi_ratio
                    Layout.topMargin: 12 * dpi_ratio
                    Layout.bottomMargin: 12 * dpi_ratio
                    radius: ST.radius
                    color: ST.color_grey

                    CusText {
                        id: btn_calendar_begin
                        anchors.centerIn: parent
                        text: utils4Qml.getCurDate()
                        property bool is_need_init: true
                        onTextChanged: {
                            if (is_need_init)
                                return
                            refreshContainData()
                        }
                        Component.onCompleted: {
                            is_need_init = false
                        }
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            openCalendarBegin(btn_calendar_begin.text)
                    }
                    Component {
                        id: calendar_begin
                        CusCalendar {
                            calendar_target: btn_calendar_begin
                        }
                    }
                }
                }
                CusRect {
                    Layout.alignment: Qt.AlignRight
                    Layout.fillHeight: true
                    Layout.preferredWidth: 240 * dpi_ratio
                    Layout.topMargin: 12 * dpi_ratio
                    Layout.bottomMargin: 12 * dpi_ratio
                    radius: ST.radius
                    color: ST.color_grey

                    CusText {
                        id: btn_calendar_end
                        anchors.centerIn: parent
                        text: utils4Qml.getCurDate()
                        property bool is_need_init: true
                        onTextChanged: {
                            if (is_need_init)
                                return
                            refreshContainData()
                        }
                        Component.onCompleted: {
                            is_need_init = false
                        }
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            openCalendarEnd()
                        }
                    }
                    Component {
                        id: calendar_end
                        CusCalendar {
                            calendar_target: btn_calendar_end
                        }
                    }
                }

                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 170 * dpi_ratio
                    Layout.topMargin: 12 * dpi_ratio
                    Layout.bottomMargin: 12 * dpi_ratio
                    color: ST.color_transparent

                    //                    visible: list_view.cur_net_order_page == EnumTool.NET_ORDER_WAITING_SHIPMENT || list_view.cur_net_order_page == EnumTool.NET_ORDER_WAITING_SELF_PICKUP
                    //                             || list_view.cur_net_order_page == EnumTool.NET_ORDER_ALL_ORDERS || list_view.cur_net_order_page == EnumTool.NET_ORDER_DELIVERY_EXCEPTION
                    visible: false

                    CusSwitchButton {
                        height: 26 * dpi_ratio
                        width: parent.width
                        text: qsTr("开启核单")
                        checked: is_check_order
                        anchors.verticalCenter: parent.verticalCenter
                        onClicked: {
                            is_check_order = !is_check_order
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredWidth: 20 * dpi_ratio
                }
            }
        }

        ContainStoreOrders {
            id: contain_store_orders
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible: lv_net_order_tab.is_sotre_orders
        }

        ContainRefundOrder {
            id: contain_refund_orders
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible: lv_net_order_tab.is_refund_orders
        }
    }
}
