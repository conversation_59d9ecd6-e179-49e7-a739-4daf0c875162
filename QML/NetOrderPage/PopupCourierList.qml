﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."
import ".."

Item {
    id: popup_root

    function open(json_data) {
        popup_root.visible = true
        setCourierList(json_data)
    }

    function close() {
        popup_root.visible = false
    }

    signal confirm(var courier_info)
    signal cancel

    property string title_name: qsTr("请选择配送员")

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    function setCourierList(json_data) {
        lm_courier_list.clear()

        var json_doc = JSON.parse(json_data)

        for (var i = 0; i < json_doc.length; ++i) {
            var cur_item = json_doc[i]
            lm_courier_list.append(cur_item)
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 600 * dpi_ratio
        height: 300 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        onVisibleChanged: {
            x = (parent.width - width) / 2
            y = (parent.height - height) / 2
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35 * dpi_ratio
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        CusComboBox {
                            id: cb_courier_list
                            model: ListModel {
                                id: lm_courier_list
                            }
                            textRole: "courier_name"
                            width: parent.width
                            height: 70 * dpi_ratio
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 70 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusButton {
                                text: qsTr("确认")
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                onClicked: {
                                    if (cb_courier_list.currentIndex == -1) {
                                        toast.openWarn(qsTr("未选择配送员"))
                                        return
                                    }
                                    confirm(JSON.stringify(lm_courier_list.get(cb_courier_list.currentIndex)))
                                    close()
                                }
                            }
                            CusButton {
                                text: qsTr("取消")
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                onClicked: {
                                    cancel()
                                    close()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
