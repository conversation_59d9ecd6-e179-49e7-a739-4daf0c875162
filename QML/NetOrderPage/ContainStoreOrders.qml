﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import ".."
import EnumTool 1.0

Item {

    Component.onCompleted: {
        reqStoreOrdersRecord()
    }
    onVisibleChanged: {
        if (visible) {

            reqStoreOrdersRecord()
        }
    }

    function resetAllInfo() {
        lm_sale_record.clear()
        right_side.reset()
    }

    function reqStoreOrdersRecord() {
        resetAllInfo()

        var is_have_power = false

        switch (lv_net_order_tab.cur_net_order_page) {
        case EnumTool.NET_ORDER_WAITING_SHIPMENT:
            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_NET_ORDER__WAITING_SHIPMENT_LIST)) {
                is_have_power = true
            }
            break
        case EnumTool.NET_ORDER_WAITING_RECEIVING:
            // if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_NET_ORDER__WAITING_SHIPMENT_LIST)) {
            is_have_power = true
            // }
            break
        case EnumTool.NET_ORDER_WAITING_SELF_PICKUP:
            // if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_NET_ORDER__WAITING_SHIPMENT_LIST)) {
            is_have_power = true
            // }
            break
        case EnumTool.NET_ORDER_COMPLETED:
            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_NET_ORDER__FINISH_LIST)) {
                is_have_power = true
            }
            break
        case EnumTool.NET_ORDER_WAITING_VERIFY:
            // if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_NET_ORDER__WAITING_SHIPMENT_LIST)) {
            is_have_power = true
            // }
            break
        case EnumTool.NET_ORDER_DELIVERY_EXCEPTION:
            // if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_NET_ORDER__WAITING_SHIPMENT_LIST)) {
            is_have_power = true
            // }
            break
        case EnumTool.NET_ORDER_WAITING_EVALUATED:
            // if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_NET_ORDER__WAITING_SHIPMENT_LIST)) {
            is_have_power = true
            // }
            break
        case EnumTool.NET_ORDER_ALL_ORDERS:
            // if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_NET_ORDER__WAITING_SHIPMENT_LIST)) {
            is_have_power = true
            // }
            break
        }

        if (!is_have_power) {
            toast.openWarn(qsTr("无此权限"))
            return
        }
        //lv_net_order_tab.reqRefreshNetOrderCount()
        lv_net_order_tab.reqRefreshNetOrderSaleCount()
        lv_net_order_tab.reqRefreshNetOrderRefundCount()

        netOrderControl.getStoreOrdersRecord4Qml(function (data) {
            lm_sale_record.clear()
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data

            var cur_sale_list_handlestate = -1

            switch (lv_net_order_tab.cur_net_order_page) {
            case EnumTool.NET_ORDER_WAITING_SHIPMENT:
                cur_sale_list_handlestate = 2
                break
            case EnumTool.NET_ORDER_WAITING_RECEIVING:
                cur_sale_list_handlestate = 3
                break
            case EnumTool.NET_ORDER_WAITING_SELF_PICKUP:
                cur_sale_list_handlestate = 9
                break
            case EnumTool.NET_ORDER_COMPLETED:
                cur_sale_list_handlestate = 4
                break
            case EnumTool.NET_ORDER_WAITING_VERIFY:
                cur_sale_list_handlestate = 7
                break
            case EnumTool.NET_ORDER_DELIVERY_EXCEPTION:
                cur_sale_list_handlestate = 10
                break
            case EnumTool.NET_ORDER_WAITING_EVALUATED:
                cur_sale_list_handlestate = 6
                break
            case EnumTool.NET_ORDER_ALL_ORDERS:
                cur_sale_list_handlestate = -1
                break
            default:
                cur_sale_list_handlestate = -1
            }

            for (var i = 0; i < json_doc_data.length; ++i) {
                var cur_item = json_doc_data[i]
                if (cur_sale_list_handlestate == -1) {
                    lm_sale_record.append(cur_item)
                } else if (cur_item.sale_list_handlestate == cur_sale_list_handlestate) {
                    lm_sale_record.append(cur_item)
                }
            }
            listview_sale_record.currentIndex = -1
        }, -2 //直接查全部然后筛选
        , btn_calendar_begin.text, btn_calendar_end.text)
        //    }, net_order_page.getEnumUnique(list_view.cur_net_order_page), btn_calendar_begin.text, btn_calendar_end.text)
    }

    function reqOrderDetail(order_unique) {
        right_side.reset()

        netOrderControl.getStoreOrdersRecordDetail4Qml(function (data) {
            lm_sale_record_detail.clear()
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            var json_doc_data_list_detail = json_doc.data.listDetail
            for (var i = 0; i < json_doc_data_list_detail.length; ++i) {
                lm_sale_record_detail.append(json_doc_data_list_detail[i])
            }
            rect_order_info._original_data_bak = data
            rect_order_info._sale_list_datetime = json_doc_data.sale_list_datetime
            rect_order_info._payDetailStr = json_doc_data.payDetailStr
            rect_order_info._sale_list_total = json_doc_data.sale_list_total
            rect_order_info._sale_list_unique = json_doc_data.sale_list_unique

            rect_order_info._name = json_doc_data.sale_list_name
            rect_order_info._phone = json_doc_data.sale_list_phone

            rect_order_info._delivery_type = json_doc_data.delivery_type
            rect_order_info._delivery_type_str = netOrderControl.getDispatchNameByType(json_doc_data.delivery_type)
            rect_order_info._sale_list_handlestatecode = json_doc_data.sale_list_handlestatecode
            rect_order_info._sale_list_handlestatecode_str = getSaleListHandleStateStr(Number(rect_order_info._sale_list_handlestatecode))
            cb_dispatch.currentIndex = cb_dispatch.find(rect_order_info._delivery_type_str)
            rect_order_info._addr = json_doc_data.sale_list_address
        }, order_unique)
    }

    function showCourierListConfirm(json_data) {
        window_root.loader_4_courier_list.sourceComponent = compo_courier_list
        window_root.loader_4_courier_list.item.open(json_data)
    }

    function printNetOrder(data) {
        printerControl.printNetOrder(data)
    }

    function getSaleListHandleStateStr(sale_list_handlestate) {
        switch (sale_list_handlestate) {
        case 0:
            return qsTr("已删除")
        case 1:
            return qsTr("无效订单")
        case 2:
            return qsTr("待发货")
        case 3:
            return qsTr("待收货")
        case 4:
            return qsTr("已完成")
        case 5:
            return qsTr("已取消")
        case 6:
            return qsTr("待评论")
        case 7:
            return qsTr("配送单待确认")
        case 8:
            return qsTr("待付款")
        case 9:
            return qsTr("待自提")
        case 10:
            return qsTr("配送异常")
        }
    }

    Component {
        id: compo_courier_list
        PopupCourierList {
            onConfirm: {
                var order_json = {
                    "sale_list_unique": rect_order_info._sale_list_unique
                }
                netOrderControl.receivingTakeawayOrder4SelfDispatch(function (is_succ, data) {

                    var json_doc = JSON.parse(data)

                    if (is_succ) {
                        printerControl.printNetOrder(rect_order_info._original_data_bak)
                        reqStoreOrdersRecord()
                        toast.openInfo(json_doc.msg)
                    } else {
                        toast.openInfo(json_doc.msg)
                    }
                }, JSON.stringify(order_json), courier_info)
            }
        }
    }

    RowLayout {
        anchors.fill: parent
        spacing: ST.margin

        CusRect {
            id: left_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 1400
            color: ST.color_white_pure

            layer.enabled: true
            layer.effect: OpacityMask {
                maskSource: Rectangle {
                    width: left_side.width
                    height: left_side.height
                    radius: ST.radius
                }
            }

            ColumnLayout {
                anchors.fill: parent

                //标题栏
                Item {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 50 * dpi_ratio
                    RowLayout {
                        anchors.fill: parent
                        spacing: 1 * dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 160 * dpi_ratio
                            clip: true
                            color: ST.color_green
                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("下单时间")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 100 * dpi_ratio
                            clip: true
                            color: ST.color_green
                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("收件人")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 160 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("订单编号")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 100 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("付款状态")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 140 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("电话")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 430 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("收件地址")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 100 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("总金额")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 100 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("订单状态")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 100 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("订单重量")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                    }
                }

                ListView {
                    id: listview_sale_record
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    clip: true
                    highlightFollowsCurrentItem: true
                    highlightMoveDuration: 0
                    focus: true
                    currentIndex: -1
                    model: ListModel {
                        id: lm_sale_record
                    }

                    delegate: CusRect {
                        id: rect_sale_record_item
                        height: 50 * dpi_ratio
                        width: listview_sale_record.width
                        color: ST.color_white_pure

                        readonly property var _sale_list_datetime: sale_list_datetime
                        readonly property var _sale_list_name: sale_list_name
                        readonly property var _sale_list_unique: sale_list_unique
                        readonly property var _sale_list_unique_str: sale_list_unique_str
                        readonly property var _sale_list_state: sale_list_state
                        readonly property var _sale_list_phone: sale_list_phone
                        readonly property var _sale_list_address: sale_list_address
                        readonly property var _sale_list_total: sale_list_total
                        readonly property var _sale_list_handlestate: sale_list_handlestate

                        function refreshSaleListHandleState() {
                            sale_list_handlestate_str = getSaleListHandleStateStr(_sale_list_handlestate)
                        }

                        on_Sale_list_handlestateChanged: {
                            refreshSaleListHandleState()
                        }

                        Component.onCompleted: {
                            refreshSaleListHandleState()
                        }

                        property string sale_list_handlestate_str: ""

                        readonly property var _goods_weight: goods_weight

                        //                        color: ST.color_white_pure
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                listview_sale_record.currentIndex = index
                            }
                        }

                        RowLayout {
                            anchors.fill: parent
                            spacing: 1 * dpi_ratio

                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 160 * dpi_ratio
                                clip: true
                                color: ST.color_transparent
                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_sale_record_item._sale_list_datetime //"下单时间"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 100 * dpi_ratio
                                clip: true
                                color: ST.color_transparent
                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_sale_record_item._sale_list_name //"收件人"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 160 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_sale_record_item._sale_list_unique_str //"订单编号"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 100 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_sale_record_item._sale_list_state // "付款状态"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 140 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_sale_record_item._sale_list_phone //"电话"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 430 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_sale_record_item._sale_list_address //"收件地址"
                                    font.pixelSize: 18 * dpi_ratio
                                    width: parent.width
                                    elide: Text.ElideMiddle
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 100 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_sale_record_item._sale_list_total // "总金额"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 100 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_sale_record_item.sale_list_handlestate_str //"订单状态"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 100 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: Number(rect_sale_record_item._goods_weight).toFixed(2) //"订单重量"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                        }
                    }

                    highlight: CusRect {
                        width: listview_sale_record.width
                        height: 50 * dpi_ratio
                        color: ST.color_orange
                        opacity: .2
                        z: 2
                    }

                    onCurrentIndexChanged: {
                        if (currentIndex >= 0){
                            reqOrderDetail(lm_sale_record.get(currentIndex).sale_list_unique_str)
                        }

                    }
                }
            }
        }

        CusRect {
            id: right_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 500
            color: ST.color_transparent
            radius: ST.radius

            function reset() {
                rect_order_info.reset()
                //                lm_sale_record.clear()
                cb_dispatch.currentIndex = -1
                lm_sale_record_detail.clear()
            }

            ColumnLayout {
                anchors.fill: parent
                spacing: ST.margin
                Layout.margins: 10 * dpi_ratio

                CusRect {
                    id: rect_order_info
                    Layout.preferredHeight: 240 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    // 原始数据备份(JSON)
                    property var _original_data_bak

                    property var _sale_list_unique: ""
                    property var _sale_list_datetime: ""
                    property var _sale_list_total: ""
                    property var _payDetailStr: ""

                    property var _name: ""
                    property var _phone: ""
                    property var _delivery_type: ""
                    property var _delivery_type_str: ""
                    property var _addr: ""
                    property var _sale_list_handlestatecode: ""
                    property var _sale_list_handlestatecode_str: ""

                    function reset() {
                        _original_data_bak = null
                        _sale_list_unique = ""
                        _sale_list_datetime = ""
                        _sale_list_total = ""
                        _payDetailStr = ""
                        _name = ""
                        _phone = ""
                        _delivery_type = ""
                        _addr = ""
                        _sale_list_handlestatecode = ""
                        _sale_list_handlestatecode_str = ""
                    }

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 10 * dpi_ratio

                        anchors.topMargin: 15 * dpi_ratio
                        anchors.bottomMargin: 15 * dpi_ratio

                        CusRect {
                            Layout.preferredHeight: 70 * dpi_ratio
                            Layout.fillWidth: true

                            color: ST.color_transparent
                            ColumnLayout {
                                anchors.fill: parent
                                anchors.leftMargin: 15 * dpi_ratio
                                anchors.rightMargin: anchors.leftMargin

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent
                                    CusText {
                                        text: qsTr("订单编号: ") + rect_order_info._sale_list_unique
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                }
                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent
                                    CusText {
                                        text: qsTr("下单日期: ") + rect_order_info._sale_list_datetime
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                }
                            }
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 45 * dpi_ratio
                            Layout.leftMargin: 15 * dpi_ratio
                            Layout.rightMargin: 15 * dpi_ratio
                            color: ST.color_transparent

                            CusComboBox {
                                id: cb_dispatch
                                height: parent.height
                                width: 300 * dpi_ratio
                                textRole: "name_role"
                                enabled: rect_order_info._sale_list_handlestatecode_str == qsTr("待发货")
                                model: ListModel {
                                    ListElement {
                                        name_role: qsTr("自配送")
                                    }
                                    ListElement {
                                        name_role: qsTr("到店自提")
                                    }
                                    ListElement {
                                        name_role: qsTr("一刻钟配送")
                                    }
                                }
                                onCurrentIndexChanged: {
                                    if (currentIndex == -1) {
                                       rect_order_info._delivery_type = ""
                                    }else if(currentIndex == 0){
                                        rect_order_info._delivery_type = "0"
                                    }else if(currentIndex == 1){
                                        rect_order_info._delivery_type = "-1"
                                    }else if(currentIndex == 2){
                                        rect_order_info._delivery_type = "2"
                                    }
                                }
                            }
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            Layout.leftMargin: 15 * dpi_ratio
                            Layout.rightMargin: 15 * dpi_ratio

                            ColumnLayout {
                                anchors.fill: parent

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent
                                    CusText {
                                        font.pixelSize: 26 * dpi_ratio
                                        text: rect_order_info._name + "　" + rect_order_info._phone // + "　" + date_selecter._dispatching_type
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                }
                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    CusText {
                                        text: rect_order_info._addr
                                        anchors.verticalCenter: parent.verticalCenter
                                        width: parent.width
                                        elide: Text.ElideMiddle
                                    }
                                }
                            }
                        }
                    }
                }

                CusRect {
                    id: goods_list
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    ColumnLayout {
                        anchors.fill: parent
                        //                        anchors.margins: 15 * dpi_ratio

                        //标题栏
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio

                            RowLayout {
                                anchors.fill: parent
                                spacing: 1 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 210 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    //                                    color: ST.color_transparent
                                    CusText {
                                        //                                        anchors.centerIn: parent
                                        x: 15 * dpi_ratio
                                        anchors.verticalCenter: parent.verticalCenter
                                        text: qsTr("商品名称")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("商品数量")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("小计")
                                        color: ST.color_white_pure
                                        anchors.horizontalCenterOffset: -10 * dpi_ratio
                                    }
                                }
                            }
                        }

                        ListView {
                            id: lv_sale_record_detail
                            Layout.fillWidth: true
                            Layout.fillHeight: true

                            clip: true
                            model: ListModel {
                                id: lm_sale_record_detail
                            }

                            delegate: Item {
                                width: lv_sale_record_detail.width
                                height: 50 * dpi_ratio
                                readonly property var _goods_name: goods_name
                                readonly property var _sale_list_detail_count: sale_list_detail_count
                                readonly property var _sale_list_detail_price: sale_list_detail_price

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 1 * dpi_ratio

                                    //商品名称
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 210 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            x: 15 * dpi_ratio
                                            width: parent.width - (15 * 2 * dpi_ratio)
                                            text: goods_name
                                            color: ST.color_black
                                            elide: Text.ElideRight
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    //商品数量
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: sale_list_detail_count
                                            color: ST.color_black
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    //小计
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            anchors.horizontalCenterOffset: -10 * dpi_ratio
                                            text: sale_list_detail_price
                                            color: ST.color_black
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                CusRect {
                    id: buttons
                    Layout.preferredHeight: 90 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 20 * dpi_ratio
                        ///-------------------------------------------| 待发货 |-------------------------------------------
                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("打印配货单")
                            visible: rect_order_info._sale_list_handlestatecode_str == qsTr("待发货") && is_check_order
                            onClicked: {

                            }
                        }

                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("核单")
                            visible: rect_order_info._sale_list_handlestatecode_str == qsTr("待发货") && is_check_order
                            onClicked: {

                            }
                        }

                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            Layout.alignment: Qt.AlignRight
                            text: qsTr("接单")
                            visible: rect_order_info._sale_list_handlestatecode_str == qsTr("待发货") && !is_check_order
                            onClicked: {
                                if (cb_dispatch.currentText == qsTr("自配送")) {
                                    netOrderControl.getShopCourierList(function (data) {
                                        var json_doc = JSON.parse(data)
                                        showCourierListConfirm(JSON.stringify(json_doc.data))
                                    })
                                } else if (cb_dispatch.currentText == qsTr("一刻钟配送") || cb_dispatch.currentText == qsTr("美团配送")) {
                                    var order_json = {
                                        "sale_list_unique": rect_order_info._sale_list_unique,
                                        "dispatching_type": rect_order_info._delivery_type
                                    }

                                    netOrderControl.receivingTakeawayOrder(function (is_succ, data) {

                                        var json_doc = JSON.parse(data)
                                        if (is_succ) {
                                            reqStoreOrdersRecord()
                                            toast.openInfo(qsTr("接单成功"))
                                        } else {
                                            toast.openWarn(qsTr("接单失败"))
                                        }
                                    }, JSON.stringify(order_json))
                                }else if(cb_dispatch.currentText == qsTr("到店自提")){
                                    netOrderControl.confirmReceiptOrder(function (is_succ, data) {
                                        var json_doc = JSON.parse(data)
                                        if (is_succ) {
                                            toast.openInfo(qsTr("确认收货成功"))
                                        } else {
                                            toast.openWarn(qsTr("确认收货失败"))
                                        }
                                        printNetOrder(rect_order_info._original_data_bak)
                                        reqStoreOrdersRecord()
                                    }, rect_order_info._sale_list_unique)
                                }
                            }
                        }

                        ///-------------------------------------------| 待发货 |-------------------------------------------

                        ///-------------------------------------------| 待收货 |-------------------------------------------
                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("确认收货")
                            visible: rect_order_info._sale_list_handlestatecode_str == "待收货"
                            onClicked: {
                                netOrderControl.confirmReceiptOrder(function (is_succ, data) {
                                    var json_doc = JSON.parse(data)
                                    if (is_succ) {
                                        toast.openInfo(qsTr("确认收货成功"))
                                    } else {
                                        toast.openWarn(qsTr("确认收货失败"))
                                    }
                                    reqStoreOrdersRecord()
                                }, rect_order_info._sale_list_unique)
                            }
                        }
                        ///-------------------------------------------| 待收货 |-------------------------------------------

                        ///-------------------------------------------| 待自提 |-------------------------------------------
                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("打印配货单")
                            visible: rect_order_info._sale_list_handlestatecode_str == "待自提" && is_check_order
                            onClicked: {
                                printNetOrder(rect_order_info._original_data_bak)
                            }
                        }
                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("核单")
                            visible: rect_order_info._sale_list_handlestatecode_str == "待自提" && is_check_order
                            onClicked: {

                            }
                        }

                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("确认收货")
                            visible: rect_order_info._sale_list_handlestatecode_str == "待自提" && !is_check_order
                            onClicked: {
                                netOrderControl.confirmReceiptOrder(function (is_succ, data) {

                                    var json_doc = JSON.parse(data)
                                    if (is_succ) {
                                        toast.openInfo(json_doc.msg)
                                        reqStoreOrdersRecord()

                                    } else {
                                        toast.openWarn(json_doc.msg)
                                    }
                                }, rect_order_info._sale_list_unique)
                            }
                        }
                        ///-------------------------------------------| 待自提 |-------------------------------------------

                        ///-------------------------------------------| 已完成 |-------------------------------------------
                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("打印")
                            visible: rect_order_info._sale_list_handlestatecode_str == "已完成"
                            onClicked: {
                                printNetOrder(rect_order_info._original_data_bak)
                            }
                        }
                        ///-------------------------------------------| 已完成 |-------------------------------------------

                        ///-------------------------------------------| 配送异常 |-------------------------------------------
                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("打印配货单")
                            visible: rect_order_info._sale_list_handlestatecode_str == "配送异常" && is_check_order
                            onClicked: {
                                printNetOrder(rect_order_info._original_data_bak)
                            }
                        }
                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("接单")
                            visible: rect_order_info._sale_list_handlestatecode_str == "配送异常"
                            onClicked: {
                                if (cb_dispatch.currentText == qsTr("自配送")) {
                                    netOrderControl.getShopCourierList(function (data) {
                                        var json_doc = JSON.parse(data)
                                        showCourierListConfirm(JSON.stringify(json_doc.data))
                                    })
                                } else if (cb_dispatch.currentText == qsTr("一刻钟配送") || cb_dispatch.currentText == qsTr("美团配送")) {
                                    var order_json = {
                                        "sale_list_unique": rect_order_info._sale_list_unique,
                                        "dispatching_type": rect_order_info._delivery_type
                                    }

                                    netOrderControl.receivingTakeawayOrder(function (is_succ, data) {

                                        var json_doc = JSON.parse(data)
                                        if (is_succ) {
                                            reqStoreOrdersRecord()
                                            toast.openInfo(json_doc.msg)
                                        } else {
                                            toast.openWarn(json_doc.msg)
                                        }
                                    }, JSON.stringify(order_json))
                                }
                            }
                        }
                        ///-------------------------------------------| 配送异常 |-------------------------------------------

                        ///-------------------------------------------| 待评价 |-------------------------------------------
                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("打印")
                            visible: rect_order_info._sale_list_handlestatecode_str == "待评价" || rect_order_info._sale_list_handlestatecode_str == "待评论"
                            onClicked: {
                                printNetOrder(rect_order_info._original_data_bak)
                            }
                        }
                        ///-------------------------------------------| 待评价 |-------------------------------------------
                    }
                }
            }

            CusRect {
                anchors.fill: parent
                radius: ST.radius
                color: ST.color_white_pure
                visible: listview_sale_record.currentIndex == -1
                CusText {
                    anchors.centerIn: parent
                    text: qsTr("请选择订单")
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {

                    }
                }
            }
        }
    }
}
