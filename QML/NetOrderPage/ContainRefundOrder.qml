﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import ".."
import EnumTool 1.0

CusRect {
    color: ST.color_transparent
    Component.onCompleted: {
        reqRefundOrdersRecord()
    }
    onVisibleChanged: {
        if (visible)
            reqRefundOrdersRecord()
        else{
            left_side._original_data_refund = null
        }
    }

    function reqRefundOrdersRecord() {
        netOrderControl.reqRefundRecord4Qml(function (data) {
            //lv_net_order_tab.reqRefreshNetOrderCount()
            lv_net_order_tab.reqRefreshNetOrderSaleCount()
            lv_net_order_tab.reqRefreshNetOrderRefundCount()
            lm_sale_record.clear()
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            for (var i = 0; i < json_doc_data.length; ++i) {
                var cur_data_item = json_doc_data[i]
                var data_tmp = {
                    "saleListUnique": cur_data_item.saleListUnique,
                    "retListUnique": cur_data_item.retListUnique,
                    "retListDatetime": cur_data_item.retListDatetime,
                    "retListReason": cur_data_item.retListReason,
                    "retListHandlestate": cur_data_item.retListHandlestate,
                    "retListHandlestateMsg": cur_data_item.retListHandlestateMsg,
                    "cusName": cur_data_item.cusName,
                    "saleListPhone": cur_data_item.saleListPhone,
                    "retListTotal": cur_data_item.retListTotal
                }
                lm_sale_record.append(data_tmp)
            }
            listview_sale_record.currentIndex = -1
        }, getRefundState(), btn_calendar_begin.text, btn_calendar_end.text)
    }

    function getDeliveryName(index) {
        if (index == -1) {
            return qsTr("到店自提")
        } else if (index == 0) {
            return qsTr("自配送")
        } else if (index == 1) {
            return qsTr("美团配送")
        } else if (index == 2) {
            return qsTr("一刻钟配送")
        }
    }

    // 获取当前页面订单状态值
    function getRefundState() {
        switch (lv_net_order_tab.cur_net_order_page) {
        case EnumTool.NET_ORDER_REFUND_WAITING_REVIEW:
            return 1
        case EnumTool.NET_ORDER_REFUND_REFUNDED:
            return 3
        case EnumTool.NET_ORDER_REFUND_REJECTED:
            return 4
        case EnumTool.NET_ORDER_REFUND_ALL_ORDERS:
            return -1
        }
    }
    //获取选中的退款单信息
    function reqRefundOrderDetail(order_unique) {
        lm_sale_record_detail.clear()
        netOrderControl.reqRefundRecordDetail4Qml(function (data) {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            left_side._original_data_refund = data
            var json_doc_data_detail_list = json_doc.data.detailList
            for (var i = 0; i < json_doc_data_detail_list.length; ++i) {
                lm_sale_record_detail.append(json_doc_data_detail_list[i])
            }

            rect_order_info._retListReason = json_doc_data.retListReason
            rect_order_info._retListTotal = json_doc_data.retListTotal
            rect_order_info._saleListUnique = json_doc_data.saleListUnique
            rect_order_info._retListUnique = json_doc_data.retListUnique
            rect_order_info._saleListAddress = json_doc_data.saleListAddress
            rect_order_info._saleListPhone = json_doc_data.saleListPhone
            rect_order_info._retListDatetime = json_doc_data.retListDatetime
            rect_order_info._retListHandlestate = json_doc_data._retListHandlestate
            rect_order_info._retListHandlestateMsg = json_doc_data.retListHandlestateMsg
        }, order_unique)
    }

    RowLayout {
        anchors.fill: parent
        spacing: ST.margin

        CusRect {
            id: left_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 1400
            color: ST.color_white_pure

            // 订单信息存储(JSON)
            property var _original_data_refund

            ColumnLayout {
                anchors.fill: parent

                //标题栏
                Item {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 50 * dpi_ratio
                    RowLayout {
                        anchors.fill: parent
                        spacing: 2 * dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 280 * dpi_ratio
                            clip: true
                            color: ST.color_green
                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("退款订单编号")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 150 * dpi_ratio
                            clip: true
                            color: ST.color_green
                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("退款人")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 150 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("联系电话")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 130 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("退款金额")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 250 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("申请时间")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 290 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("退款原因")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 150 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("订单状态")
                                color: ST.color_white_pure
                                font.pixelSize: 18 * dpi_ratio
                            }
                        }
                    }
                }

                ListView {
                    id: listview_sale_record
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    clip: true
                    highlightFollowsCurrentItem: true
                    highlightMoveDuration: 0
                    focus: true
                    currentIndex: -1
                    model: ListModel {
                        id: lm_sale_record
                    }

                    delegate: CusRect {
                        id: rect_delegate_item
                        height: 50 * dpi_ratio
                        width: listview_sale_record.width
                        color: ST.color_white_pure

                        readonly property var _saleListUnique: saleListUnique //订单unique
                        readonly property var _retListUnique: retListUnique //退款单编号
                        readonly property var _cusName: cusName //用户名
                        readonly property var _retListDatetime: retListDatetime //退款时间
                        readonly property var _retListReason: retListReason //退款原因
                        readonly property var _retListHandlestate: retListHandlestate //状态
                        readonly property var _retListHandlestateMsg: retListHandlestateMsg //状态信息
                        readonly property var _saleListPhone: saleListPhone //手机号
                        readonly property var _retListTotal: retListTotal //退款金额

                        RowLayout {
                            anchors.fill: parent
                            spacing: 2 * dpi_ratio

                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 280 * dpi_ratio
                                clip: true
                                color: ST.color_transparent
                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_delegate_item._saleListUnique // "退款订单编号"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 150 * dpi_ratio
                                clip: true
                                color: ST.color_transparent
                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_delegate_item._cusName //"退款人"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 150 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_delegate_item._saleListPhone //"联系电话"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 130 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_delegate_item._retListTotal // "退款金额"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 250 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_delegate_item._retListDatetime // "申请时间"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 290 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_delegate_item._retListReason // "退款原因"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 150 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: rect_delegate_item._retListHandlestateMsg // "订单状态"
                                    font.pixelSize: 18 * dpi_ratio
                                }
                            }
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                listview_sale_record.currentIndex = index
                            }
                        }
                    }

                    highlight: CusRect {
                        width: listview_sale_record.width
                        height: 50 * dpi_ratio
                        color: ST.color_orange
                        opacity: .4
                        z: 2
                    }

                    onCurrentIndexChanged: {
                        if (currentIndex < 0)
                            return
                        reqRefundOrderDetail(lm_sale_record.get(currentIndex).retListUnique)
                        logMgr.logDataInfo4Qml("lm_sale_record.get(currentIndex).retListUnique:{}",lm_sale_record.get(currentIndex).retListUnique)
                    }
                }
            }
        }

        CusRect {
            id: right_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 500
            color: ST.color_transparent
            radius: ST.radius

            function reset() {
                rect_order_info.reset()
                cb_dispatch.currentIndex = -1
                lm_sale_record_detail.clear()
            }

            ColumnLayout {
                anchors.fill: parent
                spacing: ST.margin
                Layout.margins: 10 * dpi_ratio

                CusRect {
                    id: rect_order_info
                    Layout.preferredHeight: 240 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_white_pure
                    radius: ST.radius


                    //退款原因
                    property var _retListReason
                    //应退金额
                    property var _retListTotal
                    //原订单
                    property var _saleListUnique
                    //退款订单
                    property var _retListUnique
                    //订单地址
                    property var _saleListAddress
                    //订单电话
                    property var _saleListPhone
                    //退款申请时间
                    property var _retListDatetime
                    //退款状态
                    property var _retListHandlestate
                    //退款状态str
                    property var _retListHandlestateMsg

                    function reset() {
                        _retListReason = ""
                        _retListTotal = ""
                        _saleListUnique = ""
                        _retListUnique = ""
                        _saleListAddress = ""
                        _saleListPhone = ""
                        _retListDatetime = ""
                        _retListState = ""
                        _retListStateMsg = ""
                    }

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 10 * dpi_ratio

                        anchors.topMargin: 15 * dpi_ratio
                        anchors.bottomMargin: 15 * dpi_ratio

                        CusRect {
                            Layout.preferredHeight: 70 * dpi_ratio
                            Layout.fillWidth: true

                            color: ST.color_transparent
                            ColumnLayout {
                                anchors.fill: parent
                                anchors.leftMargin: 15 * dpi_ratio
                                anchors.rightMargin: anchors.leftMargin

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent
                                    CusText {
                                        text: qsTr("订单编号: ") + rect_order_info._retListUnique
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                }
                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent
                                    CusText {
                                        text: qsTr("申请日期: ") + rect_order_info._retListDatetime
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                }
                            }
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            Layout.leftMargin: 15 * dpi_ratio
                            Layout.rightMargin: 15 * dpi_ratio

                            ColumnLayout {
                                anchors.fill: parent

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent
                                    CusText {
                                        font.pixelSize: 26 * dpi_ratio
                                        text: rect_order_info._saleListPhone
                                        //text: rect_order_info._name + "　" + rect_order_info._phone
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                }
                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    CusText {
                                        text: rect_order_info._saleListAddress
                                        anchors.verticalCenter: parent.verticalCenter
                                        width: parent.width
                                        elide: Text.ElideMiddle
                                    }
                                }
                            }
                        }
                    }
                }

                CusRect {
                    id: goods_list
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    ColumnLayout {
                        anchors.fill: parent
                        //标题栏
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio

                            RowLayout {
                                anchors.fill: parent
                                spacing: 1 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 210 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    //                                    color: ST.color_transparent
                                    CusText {
                                        //                                        anchors.centerIn: parent
                                        x: 15 * dpi_ratio
                                        anchors.verticalCenter: parent.verticalCenter
                                        text: qsTr("商品名称")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("商品数量")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("小计")
                                        color: ST.color_white_pure
                                        anchors.horizontalCenterOffset: -10 * dpi_ratio
                                    }
                                }
                            }
                        }

                        ListView {
                            id: lv_sale_record_detail
                            Layout.fillWidth: true
                            Layout.fillHeight: true

                            clip: true
                            model: ListModel {
                                id: lm_sale_record_detail
                            }

                            delegate: Item {
                                width: lv_sale_record_detail.width
                                height: 50 * dpi_ratio

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 1 * dpi_ratio

                                    //商品名称
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 210 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            x: 15 * dpi_ratio
                                            width: parent.width - (15 * 2 * dpi_ratio)
                                            text: goodsName
                                            color: ST.color_black
                                            elide: Text.ElideRight
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    //商品数量
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: retListDetailCount
                                            color: ST.color_black
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    //小计
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            anchors.horizontalCenterOffset: -10 * dpi_ratio
                                            text: retListDetailPrice
                                            color: ST.color_black
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                CusRect {
                    Layout.preferredHeight: 100 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.topMargin: 5 * dpi_ratio
                        anchors.bottomMargin: 5 * dpi_ratio
                        anchors.leftMargin: 15 * dpi_ratio
                        anchors.rightMargin: 15 * dpi_ratio
                        spacing: 0
                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio
                            color: ST.color_transparent
                            CusText {
                                text: qsTr("应退金额: ") + rect_order_info._retListTotal
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 40 * dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("退款原因: ") + rect_order_info._retListReason
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                    }
                }

                CusRect {
                    Layout.preferredHeight: 45 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.topMargin: 5 * dpi_ratio
                        anchors.bottomMargin: 5 * dpi_ratio
                        anchors.leftMargin: 15 * dpi_ratio
                        anchors.rightMargin: 15 * dpi_ratio

                        CusText {
                            Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                            text: qsTr("原订单: ") + rect_order_info._saleListUnique
                        }
                    }
                }

                CusRect {
                    id: buttons
                    Layout.preferredHeight: 90 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 20 * dpi_ratio
                        ///-------------------------------------------| 待处理 |-------------------------------------------
                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            Layout.alignment: Qt.AlignRight
                            text: qsTr("拒绝")
                            visible: rect_order_info._retListHandlestateMsg == "待处理"
                            onClicked: {
                                netOrderControl.reqRefundRecordStateModify(function (is_succ, data) {

                                    var json_doc = JSON.parse(data)

                                    if (is_succ) {
                                        reqRefundOrdersRecord()
                                        toast.openInfo(json_doc.msg)
                                    } else {
                                        if (json_doc == null) {
                                            toast.openWarn(qsTr("拒绝失败"))
                                        } else {
                                            toast.openWarn(json_doc.msg)
                                        }
                                    }
                                }, rect_order_info._retListUnique, 4, "拒绝退款")
                            }
                        }
                        CusButton {
                            color: ST.color_yellow
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            Layout.alignment: Qt.AlignRight
                            text: qsTr("同意退款")
                            visible: rect_order_info._retListHandlestateMsg === "待处理"
                            onClicked: {
                                netOrderControl.reqRefundRecordStateModify(function () {
                                    //打印退款单
                                    //printerControl.printRefundNetOrder(left_side._original_data_refund)
                                    left_side._original_data_refund = null
                                    reqRefundOrdersRecord()
                                    toast.openInfo(qsTr("同意退款"))
                                }, rect_order_info._retListUnique, 3)
                            }
                        }
                        ///-------------------------------------------| 待处理 |-------------------------------------------
                    }
                }
            }

            CusRect {
                anchors.fill: parent
                radius: ST.radius
                color: ST.color_white_pure
                visible: listview_sale_record.currentIndex == -1
                CusText {
                    anchors.centerIn: parent
                    text: qsTr("请选择订单")
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {

                    }
                }
            }
        }
    }
}
