﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import ".."

FocusScope {

    ListModel {
        id: listModel1
        ListElement {
            key: qsTr("店铺设置")
        }
        ListElement {
            key: qsTr("收银设置")
        }
        ListElement {
            key: qsTr("配件设置")
        }
        ListElement {
            key: qsTr("线上设置")
        }
        ListElement {
            key: qsTr("系统设置")
        }
    }

    CusRect {
        width: parent.width
        height: parent.height
        color: "#FFFFFF"
    }

    ColumnLayout {
        anchors.fill: parent

        Item {
            Layout.preferredHeight: 10 * dpi_ratio
        }

        ListView {
            id: list1
            Layout.fillHeight: true
            Layout.fillWidth: true

            focus: true
            model: listModel1
            interactive: false
            delegate: listViewDelegate
        }
    }

    Component {
        id: listViewDelegate
        Item {
            id: container
            width: ListView.view.width
            property int margin: 10 * dpi_ratio
            height: (52 * dpi_ratio + margin * 2) * configTool.fontRatio
            property bool selected: ListView.isCurrentItem

            //    property int enumIndex: ListView.isCurrentItem
            CusRect {
                id: content
                anchors.fill: parent
                anchors.topMargin: margin
                anchors.bottomMargin: margin
                color: selected ? ST.color_green : "#FFFFFF"
                antialiasing: true
                radius: ST.radius
            }

            Text {
                id: label
                anchors.centerIn: content
                text: key
                font.family: ST.fontFamilyYaHei
                color: selected ? ST.color_white_pure : "#193441"
                font.pixelSize: 26 * dpi_ratio * configTool.fontRatio
            }

            MouseArea {
                id: mouseArea
                anchors.fill: parent
                hoverEnabled: true

                onClicked: {
                    container.ListView.view.currentIndex = index
                    container.forceActiveFocus()
                    listIndex = index
                    pageIndex = 0
                }
            }
        }
    }
}
