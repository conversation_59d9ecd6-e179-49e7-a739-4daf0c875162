﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import EnumTool 1.0
import SettingEnum 1.0
import "../.."

CusRect {
    id: popup_root_price_tag_setting
    color: ST.color_transparent

    Component.onCompleted: {
        refreshPriceTagSetting()
    }

    onVisibleChanged: {
        if (!visible)
            return

        refreshPriceTagSetting()
    }

    property int lable_type: 0

    function open(lable_type_in) {
        popup_root_price_tag_setting.visible = true
        lable_type = lable_type_in
        resetInfo()
        refreshPriceTagSetting()
    }

    function close() {
        popup_root_price_tag_setting.visible = false
        keyboard_c.closeAll()
    }

    function savePriceTagSetting() {
        var obj_price_tag_setting = {
            "paper_w": tf_paper_w.text,
            "paper_h": tf_paper_h.text,
            "dpi_mm": tf_dpi.text,
            "margin_left": tf_margin_left.text,
            "margin_top": tf_margin_top.text,
            "shop_name_x": tf_shop_name_x.text,
            "shop_name_y": tf_shop_name_y.text,
            "shop_name_size": tf_shop_name_size.text,
            "goods_name_x": tf_goods_name_x.text,
            "goods_name_y": tf_goods_name_y.text,
            "goods_name_size": tf_goods_name_size.text,
            "price_x": tf_price_x.text,
            "price_y": tf_price_y.text,
            "price_size": tf_price_size.text,
            "barcode_x": tf_barcode_x.text,
            "barcode_y": tf_barcode_y.text,
            "barcode_size": tf_barcode_size.text,
            "spec_x": tf_spec_x.text,
            "spec_y": tf_spec_y.text,
            "spec_size": tf_spec_size.text,
            "unit_x": tf_unit_x.text,
            "unit_y": tf_unit_y.text,
            "unit_size": tf_unit_size.text
        }
        if(printerControl.setPriceTagPrintFormatByJson(JSON.stringify(obj_price_tag_setting))){
           toast.openInfo("保存成功!")
           close()
        }else{
           toast.openInfo("保存失败!")
        }
    }

    function refreshPriceTagSetting() {
        var json_doc = JSON.parse(printerControl.getPriceTagInfo(lable_type))

        if (!json_doc)
            return

        tf_paper_w.text = Number(json_doc.paper_w).toFixed(2)
        tf_paper_h.text = Number(json_doc.paper_h).toFixed(2)
        tf_dpi.text = Number(json_doc.dpi_mm).toFixed(2)

        tf_margin_left.text = Number(json_doc.margin_left).toFixed(2)
        tf_margin_top.text = Number(json_doc.margin_top).toFixed(2)

        tf_shop_name_x.text = Number(json_doc.shop_name_x).toFixed(2)
        tf_shop_name_y.text = Number(json_doc.shop_name_y).toFixed(2)
        tf_shop_name_size.text = Number(json_doc.shop_name_size).toFixed(2)

        tf_goods_name_x.text = Number(json_doc.goods_name_x).toFixed(2)
        tf_goods_name_y.text = Number(json_doc.goods_name_y).toFixed(2)
        tf_goods_name_size.text = Number(json_doc.goods_name_size).toFixed(2)

        tf_price_x.text = Number(json_doc.price_x).toFixed(2)
        tf_price_y.text = Number(json_doc.price_y).toFixed(2)
        tf_price_size.text = Number(json_doc.price_size).toFixed(2)

        tf_barcode_x.text = Number(json_doc.barcode_x).toFixed(2)
        tf_barcode_y.text = Number(json_doc.barcode_y).toFixed(2)
        tf_barcode_size.text = Number(json_doc.barcode_size).toFixed(2)

        tf_spec_x.text = Number(json_doc.spec_x).toFixed(2)
        tf_spec_y.text = Number(json_doc.spec_y).toFixed(2)
        tf_spec_size.text = Number(json_doc.spec_size).toFixed(2)

        tf_unit_x.text = Number(json_doc.unit_x).toFixed(2)
        tf_unit_y.text = Number(json_doc.unit_y).toFixed(2)
        tf_unit_size.text = Number(json_doc.unit_size).toFixed(2)
    }

    function resetInfo() {
        tf_paper_w.clear()
        tf_paper_h.clear()
        tf_dpi.clear()
        tf_margin_left.clear()
        tf_margin_top.clear()
        tf_shop_name_x.clear()
        tf_shop_name_y.clear()
        tf_shop_name_size.clear()
        tf_goods_name_x.clear()
        tf_goods_name_y.clear()
        tf_goods_name_size.clear()
        tf_price_x.clear()
        tf_price_y.clear()
        tf_price_size.clear()
        tf_barcode_x.clear()
        tf_barcode_y.clear()
        tf_barcode_size.clear()
        tf_spec_x.clear()
        tf_spec_y.clear()
        tf_spec_size.clear()
        tf_unit_x.clear()
        tf_unit_y.clear()
        tf_unit_size.clear()
    }

    property string title_name: "价签设置"

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 1700 * dpi_ratio
        height: 600 * dpi_ratio
        x: (parent.width - width) / 2
        y: 80 * dpi_ratio

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35
                color: ST.color_transparent
                is_penetration: false

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 55 * dpi_ratio

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 70 * dpi_ratio

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "纸张宽高(mm)"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_paper_w
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                is_clicked_select_all: true

                                                digital_keyboard_x: 550 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio

                                                keyboard_status: 1
                                            }

                                            CusTextField {
                                                id: tf_paper_h
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                is_clicked_select_all: true

                                                digital_keyboard_x: 550 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio

                                                keyboard_status: 1
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "分辨率(mm)"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_dpi
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1

                                                digital_keyboard_x: 920 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusSpacer {
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true

                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "边距 左/上(mm)"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_margin_left
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 1370 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusTextField {
                                                id: tf_margin_top
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 1370 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }
                                        }
                                    }
                                }
                            }

                            CusSpacer {
                                Layout.fillWidth: true
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 70 * dpi_ratio

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true

                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "店铺名 X/Y(mm)"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_shop_name_x
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true
                                                digital_keyboard_x: 550 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio

                                                keyboard_status: 1
                                            }

                                            CusTextField {
                                                id: tf_shop_name_y
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true
                                                digital_keyboard_x: 550 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio

                                                keyboard_status: 1
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "店铺名字号"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_shop_name_size
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 920 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusSpacer {
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true

                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "商品名 X/Y(mm)"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_goods_name_x
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 1370 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusTextField {
                                                id: tf_goods_name_y
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 1370 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "商品名字号"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_goods_name_size
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 860 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusSpacer {
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 70 * dpi_ratio

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "价格 X/Y(mm)"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_price_x
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true
                                                digital_keyboard_x: 550 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio

                                                keyboard_status: 1
                                            }

                                            CusTextField {
                                                id: tf_price_y
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true
                                                digital_keyboard_x: 550 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio

                                                keyboard_status: 1
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true

                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "价格字号"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_price_size
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 920 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusSpacer {
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true

                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "条码 X/Y(mm)"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_barcode_x
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 1370 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusTextField {
                                                id: tf_barcode_y
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 1370 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "条码字号"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_barcode_size
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 860 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusSpacer {
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 70 * dpi_ratio

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true

                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "规格 X/Y(mm)"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_spec_x
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true
                                                digital_keyboard_x: 550 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio

                                                keyboard_status: 1
                                            }

                                            CusTextField {
                                                id: tf_spec_y
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                digital_keyboard_x: 550 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio

                                                keyboard_status: 1
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true

                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "规格字号"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_spec_size
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 920 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusSpacer {
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true

                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "单位 X/Y(mm)"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_unit_x
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 1370 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusTextField {
                                                id: tf_unit_y
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                Layout.alignment: Qt.AlignHCenter
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 1370 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: "单位字号"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 20 * dpi_ratio

                                            CusTextField {
                                                id: tf_unit_size
                                                Layout.preferredWidth: 90 * dpi_ratio
                                                Layout.fillHeight: true
                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                digital_keyboard_x: 860 * dpi_ratio
                                                digital_keyboard_y: 440 * dpi_ratio
                                            }

                                            CusSpacer {
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusSpacer {
                                Layout.fillWidth: true
                            }

                            CusButton {
                                Layout.preferredWidth: 180 * dpi_ratio
                                Layout.fillHeight: true
                                text: "保存"

                                onClicked: {
                                    popup_root_price_tag_setting.savePriceTagSetting()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
