﻿import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../.."

CusRect {
    id: setting_main
    color: ST.color_white_pure

    //标题栏属性
    property var strArray: [//\
        [qsTr("店铺信息"), qsTr("店铺配置")], //\
        [qsTr("利润设置"), qsTr("支付设置"), qsTr("入库设置"), qsTr("抹零设置"),qsTr("促销设置")], //\
        [qsTr("小票设置"), qsTr("价签设置"), qsTr("称重设置"), qsTr("摄像头设置"), qsTr("其他设置")], //\
        [qsTr("线上设置")], //\
        [qsTr("系统设置"), qsTr("声音设置")] //\
    ]

    ColumnLayout {
        anchors.fill: parent

        Setting_Title {
            id: shopSetting_Title
            Layout.fillWidth: true
            Layout.preferredHeight: strArray[listIndex].length > 1 ? 68 * dpi_ratio : 0
        }

        //0
        Setting_ShopInfo {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 0 && pageIndex == 0
        }
        Setting_ShopConfig {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 0 && pageIndex == 1
        }
        //1
        Setting_Profit {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 1 && pageIndex == 0
        }
        Setting_PayConfig {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 1 && pageIndex == 1
        }
        Setting_Godown {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 1 && pageIndex == 2
        }
        Setting_WipeZero {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 1 && pageIndex == 3
        }
        Setting_Marketing {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 1 && pageIndex == 4
        }
        //2
        Setting_Tickets {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 2 && pageIndex == 0
        }
        Setting_PriceTag {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 2 && pageIndex == 1
        }
        Setting_Weighing {
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio

            visible: listIndex == 2 && pageIndex == 2
        }
        Setting_Camera {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 2 && pageIndex == 3
        }
        Setting_Other {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 2 && pageIndex == 4
        }
        //3
        Setting_Online {
            Layout.leftMargin: 24 * dpi_ratio
            Layout.rightMargin: 24 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 3 && pageIndex == 0
        }
        //4
        Setting_System {
            Layout.leftMargin: 60 * dpi_ratio
            Layout.rightMargin: 60 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 4 && pageIndex == 0
        }
        Setting_Sound {
            Layout.leftMargin: 60 * dpi_ratio
            Layout.rightMargin: 60 * dpi_ratio
            Layout.topMargin: 32 * dpi_ratio
            Layout.bottomMargin: 32 * dpi_ratio
            Layout.fillWidth: true
            Layout.fillHeight: true
            visible: listIndex == 4 && pageIndex == 1
        }
    }
}
