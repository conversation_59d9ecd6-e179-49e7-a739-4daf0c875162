﻿import QtQuick 2.15;
import EnumTool 1.0
import SettingEnum 1.0

import "../.."

Item {
    id: shopSettingTitle
    width: parent.width
    //    height: 68
    height: strArray[listIndex].length > 1 ? 68 * dpi_ratio : 0
    visible: true
    clip: true

    CusRect {
        width: parent.width
        height: parent.height

        color: "#F1F1F1"
        visible: true
    }

    Row {
        CusRect {
            width: 3 * dpi_ratio
            height: 68 * dpi_ratio
            focus: false
            color: ST.color_transparent
        }

        Repeater {
            id: repeater1
            model: strArray[listIndex].length
            property int selected: pageIndex
            delegate: shopSetting_TitleBtn
        }
    }

    Component {
        id: shopSetting_TitleBtn
        Item {
            id: container
            width: 188 * dpi_ratio * configTool.fontRatio
            height: 68 * dpi_ratio
            focus: true

            property var listModel2: strArray

            CusRect {
                id: content
                color: (repeater1.selected == index) ? "#FFFFFF" : "#F1F1F1"
                antialiasing: true
                anchors.fill: parent
            }

            Text {
                id: name
                text: strArray[listIndex][index]
                anchors.centerIn: content
                font.family: ST.fontFamilySiYan
                font.pixelSize: 24 * dpi_ratio * configTool.fontRatio
            }

            MouseArea {
                id: mouseArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    pageIndex = index
                }
            }
        }
    }
}
