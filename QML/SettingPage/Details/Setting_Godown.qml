﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQml.Models 2.15

import EnumTool 1.0
import SettingEnum 1.0
import SortFilterProxyModel 0.2

import "../.."

Item {
    id: pay_config_root

    ListModel {
        id: lm_category1
    }
    ListModel {
        id: lm_category2
    }

    Component.onCompleted: {
        refreshCategory1()

        if (goodsKindManager.defaultGoodsKind == "")
            return

        focusDefaultGoodsKind(goodsKindManager.defaultGoodsKind)
    }

    onVisibleChanged: {
        if (!visible)
            return

        refreshCategory1()

        if (goodsKindManager.defaultGoodsKind == "")
            return

        focusDefaultGoodsKind(goodsKindManager.defaultGoodsKind)
    }

    // 刷新一级分类
    function refreshCategory1() {
        cb_category1.is_need_init = true
        lm_category1.clear()
        var data_json = JSON.parse(goodsKindManager.getRootGoodsKind4Qml())
        if (!data_json) {
            cb_category2.is_need_init = false
            return
        }
        for (var i = 0; i < data_json.length; ++i) {
            var cur_item = data_json[i]
            lm_category1.append(cur_item)
        }
        cb_category1.is_need_init = false
    }
    // 刷新二级分类
    function refreshCategory2(parent_goods_kind_unique = "") {
        cb_category2.is_need_init = true

        lm_category2.clear()

        if (parent_goods_kind_unique == "")
            return

        var data_json = JSON.parse(goodsKindManager.getSubGoodsKind4Qml(parent_goods_kind_unique))
        if (!data_json) {
            cb_category2.is_need_init = false
            return
        }
        for (var i = 0; i < data_json.length; ++i) {
            var cur_item = data_json[i]
            lm_category2.append(cur_item)
        }
        cb_category2.is_need_init = false
    }

    // 选中一级分类
    function foucesCategory1ByKindUnique(kind_unique) {
        cb_category1.is_need_init = true

        for (var i = 0; i < lm_category1.count; ++i) {
            var cur_goods_kind = lm_category1.get(i)

            if (cur_goods_kind.goods_kind_unique == kind_unique) {
                cb_category1.currentIndex = i
                break
            }
        }

        cb_category1.is_need_init = false
    }
    // 选中二级分类
    function focusCategory2ByKindUnique(kind_unique) {
        cb_category1.is_need_init = true

        for (var i = 0; i < lm_category2.count; ++i) {
            var cur_goods_kind = lm_category2.get(i)
            if (cur_goods_kind.goods_kind_unique == kind_unique) {
                cb_category2.currentIndex = i
                break
            }
        }

        cb_category1.is_need_init = false
    }
    // 选中商品分类
    function focusDefaultGoodsKind(goods_kind_unique) {
        var json_goods_kind_info = JSON.parse(goodsKindManager.getGoodsKindByKindUnique4Qml(goods_kind_unique))

        if (json_goods_kind_info == null)
            return

        foucesCategory1ByKindUnique(json_goods_kind_info.goods_kind_parunique)

        refreshCategory2(json_goods_kind_info.goods_kind_parunique)

        focusCategory2ByKindUnique(goods_kind_unique)
    }

    RowLayout {
        anchors.fill: parent
        spacing: 20 * dpi_ratio

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 10 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * dpi_ratio
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("默认分类")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    RowLayout {
                        anchors.fill: parent

                        CusSpacer {
                            Layout.fillWidth: true
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 500 * dpi_ratio
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent

                                CusComboBox {
                                    id: cb_category1
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    textRole: "goods_kind_name"
                                    model: lm_category1

                                    onCurrentIndexChanged: {
                                        if (is_need_init)
                                            return

                                        if (currentIndex == -1) {
                                            refreshCategory2("")
                                        } else {
                                            refreshCategory2(lm_category1.get(currentIndex).goods_kind_unique)
                                        }
                                    }
                                }

                                CusComboBox {
                                    id: cb_category2
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    textRole: "goods_kind_name"
                                    model: lm_category2
                                    onCurrentIndexChanged: {
                                        if (is_need_init)
                                            return

                                        if (currentIndex == -1) {
                                            goodsKindManager.defaultGoodsKind = ""
                                        } else {
                                            var cur_goods_kind = lm_category2.get(currentIndex).goods_kind_unique
                                            goodsKindManager.defaultGoodsKind = cur_goods_kind
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.fillHeight: true
                }
            }
        }
        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
        }
    }
}
