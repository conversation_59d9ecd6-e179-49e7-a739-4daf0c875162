﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import "../.."

Item {

    RowLayout {
        anchors.fill: parent

        onVisibleChanged: {
            if(visible){
                store_id.text = settingTool.getSetting(SettingEnum.STORE_ID);
                store_name.text = settingTool.getSetting(SettingEnum.STORE_NAME);
                storePhone.text = settingTool.getSetting(SettingEnum.STORE_PHONE_NUMBER);
                staffId.text    = settingTool.getSetting(SettingEnum.STAFF_ID);
                storeAddress.text = settingTool.getSetting(SettingEnum.STORE_ADDRESS);
                mechineId.text = settingTool.getSetting(SettingEnum.MECHINE_ID);
                logMgr.logEvtInfo4Qml("重新加载防止缓存")
            }
        }

        CusRect {
            Layout.preferredWidth: 2
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 25 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * ST.dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 20 * ST.dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 140 * ST.dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("店铺ID")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            CusTextField {
                                id: store_id
                                enabled: false
                                anchors.fill: parent
                                text: settingTool.getSetting(SettingEnum.STORE_ID)
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * ST.dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 20 * ST.dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 140 * ST.dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("店铺名称")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            CusTextField {
                                id: store_name
                                anchors.fill: parent

                                text: settingTool.getSetting(SettingEnum.STORE_NAME)
                                onEditingFinished: {
                                    settingTool.setSetting(SettingEnum.STORE_NAME, text)
                                }
                                normal_keyboard_x: 390 * dpi_ratio
                                normal_keyboard_y: 435 * dpi_ratio

                                digital_keyboard_x: 390 * dpi_ratio
                                digital_keyboard_y: 435 * dpi_ratio
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * ST.dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 20 * ST.dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 140 * ST.dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("店铺位置")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            CusTextField {
                                id:storeAddress
                                anchors.fill: parent
                                text: settingTool.getSetting(SettingEnum.STORE_ADDRESS)
                                onEditingFinished: {
                                    settingTool.setSetting(SettingEnum.STORE_ADDRESS, text)
                                }
                                normal_keyboard_x: 390 * dpi_ratio
                                normal_keyboard_y: 435 * dpi_ratio

                                digital_keyboard_x: 390 * dpi_ratio
                                digital_keyboard_y: 435 * dpi_ratio
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * ST.dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 20 * ST.dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 140 * ST.dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("雇员编号")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            CusTextField {
                                id:staffId
                                enabled: false
                                anchors.fill: parent
                                text: settingTool.getSetting(SettingEnum.STAFF_ID)
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * ST.dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 20 * ST.dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 140 * ST.dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("机器编号")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            CusTextField {
                                id:mechineId
                                enabled: false
                                anchors.fill: parent
                                text: settingTool.getSetting(SettingEnum.MECHINE_ID)
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * ST.dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 20 * ST.dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 140 * ST.dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("机器MAC")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            CusTextField {
                                enabled: false
                                anchors.fill: parent
                                text: utils4Qml.getHostMacAddress()
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * ST.dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 20 * ST.dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 140 * ST.dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("联系电话")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            CusTextField {
                                id:storePhone
                                anchors.fill: parent
                                keyboard_status: 1
                                text: settingTool.getSetting(SettingEnum.STORE_PHONE_NUMBER)
                                onEditingFinished: {
                                    settingTool.setSetting(SettingEnum.STORE_PHONE_NUMBER, text)
                                }

                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio

                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 435 * dpi_ratio
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.fillHeight: true //填充剩余区域
                }
            }
        }

        CusRect {
            Layout.preferredWidth: 1
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
        }
    }
}
