﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import "../.."

CusRect {
    color: ST.color_transparent

    property int height_lable_info: 56 * dpi_ratio
    property int width_lable: 210 * dpi_ratio
    property string imagePath: qsTr("/Images/tickets_c.png")

    RowLayout {
        anchors.fill: parent

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        Rectangle {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 150 * dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("打印机连接方式")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        CusSpacer {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                        }
                        Rectangle {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 280 * dpi_ratio * configTool.fontRatio
                            color: ST.color_transparent

                            CusComboBox {
                                id: combobox_printer
                                anchors.fill: parent
                                onCurrentTextChanged: {
                                    if (!is_need_init)
                                        printerControl.setTicketPrinterByName(currentText)
                                }
                                Component.onCompleted: {
                                    model = printerControl.getPrinterNames()
                                    currentIndex = find(settingTool.getSetting(SettingEnum.PRINTER_NAME_TICKET))
                                    is_need_init = false
                                }
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredHeight: 20 * ST.dpi_ratio
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        Rectangle {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 150 * dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("是否使用餐饮小票")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        CusSpacer {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                        }
                        Rectangle {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 280 * dpi_ratio
                            color: ST.color_transparent

                            CusSwitchButton {
                                anchors.right: parent.right
                                anchors.verticalCenter: parent.verticalCenter

                                checked: printerControl.isUseFoodTicket
                                onClicked: {
                                    printerControl.isUseFoodTicket = !printerControl.isUseFoodTicket
                                }
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredHeight: 20 * ST.dpi_ratio
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent
                    visible: printerControl.isUseFoodTicket

                    RowLayout {
                        anchors.fill: parent
                        Rectangle {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 150 * dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("重置餐饮小票计数")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        CusSpacer {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                        }
                        Rectangle {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 280 * dpi_ratio
                            color: ST.color_transparent

                            CusButton {
                                anchors.right: parent.right
                                anchors.verticalCenter: parent.verticalCenter
                                width: 140 * ST.dpi_ratio * configTool.fontRatio
                                text: qsTr("确认重置")
                                onClicked: {
                                    printerControl.resetFoodTicketIndex()
                                    toast.openInfo(qsTr("重置成功"))
                                }
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredHeight: 20 * ST.dpi_ratio
                }

                CusRect {
                    color: ST.color_transparent
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: width_lable
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("默认打印数量")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusSpacer {
                            Layout.fillWidth: true
                        }

                        CusSpinBox {
                            Layout.preferredWidth: 150 * dpi_ratio * configTool.fontRatio
                            Layout.preferredHeight: 42 * dpi_ratio * configTool.fontRatio
                            Layout.alignment: Qt.AlignVCenter

                            value: printerControl.getTicketPrinterNum()
                            onValueChanged: {
                                printerControl.setTicketPrinterNum(value)
                            }
                        }
                    }
                }
                CusSpacer {
                    Layout.preferredHeight: 20 * ST.dpi_ratio
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent
                    CusText {
                        text: qsTr("备注内容(") + text_area_remark.length.toString() + "/50)"
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }
                CusSpacer {
                    Layout.preferredHeight: 10 * ST.dpi_ratio
                }
                CusTextArea {
                    id: text_area_remark
                    Layout.fillWidth: true
                    Layout.preferredHeight: 130 * ST.dpi_ratio
                    text: settingTool.getSetting(SettingEnum.REMARK_INFO_TICKETS)
                    onTextChanged: {
                        settingTool.setSetting(SettingEnum.REMARK_INFO_TICKETS, text)
                    }
                    onVisibleChanged: {
                        text = settingTool.getSetting(SettingEnum.REMARK_INFO_TICKETS)
                    }
                }
                CusSpacer {
                    Layout.preferredHeight: 20 * ST.dpi_ratio
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 58 * ST.dpi_ratio
                    color: ST.color_transparent
                    CusButton {
                        color: ST.color_orange
                        text: qsTr("打印检测")
                        height: parent.height
                        width: 160 * ST.dpi_ratio
                        onClicked: {
                            printerControl.printTest()
                        }
                    }
                }
                CusSpacer {
                    Layout.preferredHeight: 20 * ST.dpi_ratio
                }
                CusText {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 35 * ST.dpi_ratio
                    text: qsTr("提示：请先测试后无误后使用")
                }

                CusSpacer {
                    Layout.fillHeight: true
                }
            }
        }
        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_transparent
            Image {
                x: 58 * dpi_ratio

                height: 750 * dpi_ratio
                width: 350 * dpi_ratio
                source: imagePath
            }
        }
    }
}
