﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import EnumTool 1.0
import SettingEnum 1.0
import "../.."

CusRect {
    color: ST.color_transparent

    ColumnLayout {
        anchors.fill: parent
        spacing: 30 * dpi_ratio

        CusRect {
            Layout.preferredHeight: 55 * dpi_ratio
            Layout.preferredWidth: 950 * dpi_ratio
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent

                CusRect {
                    Layout.preferredWidth: 650 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("打印机连接方式")
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }

                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 280 * dpi_ratio * configTool.fontRatio
                    color: ST.color_transparent
                    CusComboBox {
                        anchors.fill: parent
                        onCurrentTextChanged: {
                            if (!is_need_init)
                                printerControl.setPriceTagPrinterByName(currentText)
                        }
                        Component.onCompleted: {
                            model = printerControl.getPrinterNames()
                            currentIndex = find(settingTool.getSetting(SettingEnum.PRINTER_NAME_PRICE_TAG))
                            is_need_init = false
                        }
                    }
                }
                CusSpacer {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 55 * dpi_ratio
            Layout.preferredWidth: 950 * dpi_ratio
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent

                CusRect {
                    Layout.preferredWidth: 650 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("打印检测")
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }

                CusButton {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 280 * dpi_ratio * configTool.fontRatio

                    text: qsTr("打印检测")
                    onClicked: {
                        printerControl.printPriceTagTest()
                    }
                }

                CusSpacer {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 55 * dpi_ratio
            Layout.preferredWidth: 950 * dpi_ratio
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent

                CusRect {
                    Layout.preferredWidth: 650 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("打印模板")
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }

                CusSpacer {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                }
            }
        }

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            GridLayout {
                //                anchors.fill: parent
                columns: 4
                rowSpacing: 68 * dpi_ratio
                columnSpacing: 44 * dpi_ratio

                Repeater {
                    model: ListModel {
                        id: list_mode_print_template

                        Component.onCompleted: {
                            append({
                                       "image": qsTr("/Images/tag4.png"),
                                       "label": qsTr("40*30(毫米)"),
                                       "label_type": SettingEnum.PriceTagTemplate_40x30
                                   })
                            append({
                                       "image": qsTr("/Images/tag1.png"),
                                       "label": qsTr("95*38(毫米)"),
                                       "label_type": SettingEnum.PriceTagTemplate_95x38
                                   })
                            append({
                                       "image": qsTr("/Images/tag1.png"),
                                       "label": qsTr("60*35(毫米)"),
                                       "label_type": SettingEnum.PriceTagTemplate_60x35
                                   })
                        }
                    }

                    delegate: ComponentTagTemplate {
                        width: 344 * dpi_ratio
                        height: 192 * dpi_ratio

                        onDoubleClicked: {
                            openPriceTagSettingWindow(model.label_type)
                        }
                        onClicked: {
                            printerControl.curPriceTagType = model.label_type
                        }

                        is_checked: printerControl.curPriceTagType == model.label_type
                    }
                }
            }
        }
    }

    function openPriceTagSettingWindow(lable_type_in) {
        switch (lable_type_in) {
        case SettingEnum.PriceTagTemplate_60x35:
            window_root.loader_4_price_tag_setting.sourceComponent = compo_price_tag_window2
            window_root.loader_4_price_tag_setting.item.open(lable_type_in)
            break
        default:
            window_root.loader_4_price_tag_setting.sourceComponent = compo_price_tag_window
            window_root.loader_4_price_tag_setting.item.open(lable_type_in)
            break
        }
    }

    Component {
        id: compo_price_tag_window
        PriceTagWindow1 {}
    }

    Component {
        id: compo_price_tag_window2
        PriceTagWindow2 {}
    }

    component ComponentTagTemplate: Item {
        id: rect
        width: 344 * dpi_ratio
        height: 192 * dpi_ratio

        signal clicked
        signal doubleClicked

        property bool is_checked: false

        CusRect {
            anchors.fill: parent
            radius: 6 * dpi_ratio
            color: ST.color_grey
            opacity: .5
        }

        Image {
            id: image_tag_template
            anchors.fill: parent
            anchors.margins: 5 * dpi_ratio
            source: image
            fillMode: Image.PreserveAspectFit
        }

        CusRadioManual {
            id: radio_btn_tag_s
            text: label
            anchors.top: image_tag_template.bottom
            anchors.topMargin: 12 * dpi_ratio
            x: 90 * dpi_ratio
            checked: is_checked
        }

        MouseArea {
            anchors.fill: parent

            Timer {
                id: timer
                interval: 500
                onTriggered: rect.clicked()
            }

            onClicked: {
                if (timer.running) {
                    timer.stop()
                    rect.doubleClicked()
                } else {
                    timer.restart()
                }
            }
        }
    }
}
