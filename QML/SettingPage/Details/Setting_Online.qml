﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import "../.."

FocusScope {
    property int margin: 32 * dpi_ratio
    property int widthBtn: 143 * dpi_ratio
    property int heightBtn: 56 * dpi_ratio
    property int spacing_: 15 * dpi_ratio

    property int height_lable_info: 56 * dpi_ratio
    property int width_lable: 154 * dpi_ratio
    property int width_info: (width - width_lable)

    property var component_info: [component1, component2, printer_conn_type, component4, component5, component6, component7,component8]
    property int widthColumnLayout: 880 * dpi_ratio

    //使用独立的备注内容
    ListModel {
        id: list_model
        ListElement {
            key: qsTr("自动接单打印")
            info_index: 1
        }
        ListElement {
            key: qsTr("自动打印退款单")
            info_index: 7
        }
        ListElement {
            key: qsTr("打印机连接方式")
            info_index: 2
        }
        ListElement {
            key: qsTr("默认打印数量")
            info_index: 4
        }
        ListElement {
            key: qsTr("独立的备注内容")
            info_index: 6
        }
    }

    ListView {
        id: columnLayout1
        anchors.left: parent.left
        width: 880 * dpi_ratio
        height: (spacing + height_lable_info) * (component_info.length + 1)
        spacing: spacing_
        model: list_model
        interactive: false

        delegate: Component {
            Loader {
                id: loader
                sourceComponent: component_info[info_index]
                Binding {
                    target: loader.item
                    property: "key"
                    value: key
                }
            }
        }
    }

    CusRect {
        id: remark
        width: width_lable
        height: height_lable_info
        anchors.top: columnLayout1.bottom
        anchors.topMargin: -205 * dpi_ratio
        property int count: text_area_remark.length
        color: ST.color_transparent

        CusText {
            text: qsTr("备注内容(") + text_area_remark.length.toString() + "/50)"
            anchors.verticalCenter: parent.verticalCenter
        }
    }

    CusTextArea {
        id: text_area_remark
        width: 688 * dpi_ratio
        height: 129 * dpi_ratio
        anchors.top: remark.bottom
        anchors.topMargin: spacing_
        property int maxlen: 50
        text: if (settingTool.getSetting(SettingEnum.IS_SEPARATE_ONLINE_REMARK)) {
                  settingTool.getSetting(SettingEnum.REMARK_INFO_ONLINE)
              } else {
                  settingTool.getSetting(SettingEnum.REMARK_INFO_TICKETS)
              }
        onTextChanged: {
            if (settingTool.getSetting(SettingEnum.IS_SEPARATE_ONLINE_REMARK)) {
                settingTool.setSetting(SettingEnum.REMARK_INFO_ONLINE, text)
            } else {
                settingTool.setSetting(SettingEnum.REMARK_INFO_TICKETS, text)
            }
            if (length > maxlen)
                remove(maxlen, length)
        }
        onVisibleChanged: {
            refreshText()
        }

        function refreshText() {
            text_area_remark.text = settingTool.getSetting(SettingEnum.IS_SEPARATE_ONLINE_REMARK) ? settingTool.getSetting(SettingEnum.REMARK_INFO_ONLINE) : settingTool.getSetting(
                                                                                                        SettingEnum.REMARK_INFO_TICKETS)
        }
    }

    CusButton {
        anchors.top: text_area_remark.bottom
        anchors.topMargin: 10 * dpi_ratio

        color: "#FFA240"
        height: 56 * dpi_ratio
        width: 224 * dpi_ratio

        text: qsTr("打印检测")
    }
    property string imagePath: qsTr("/Images/tickets_c.png")
    Image {
        x: 952 * dpi_ratio
        height: 750 * dpi_ratio
        width: 350 * dpi_ratio
        source: imagePath
    }

    Component {
        id: component0
        CusRect {
            anchors.fill: parent
            color: ST.color_transparent

            CusSwitchButton {
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                checked: settingTool.getSetting(SettingEnum.IS_USE_ONLINE_STORE)
            }
        }
    }
    Component {
        id: component1
        CusRect {
            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            CusSwitchButton {
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                checked: settingTool.getSetting(SettingEnum.IS_NET_LIST)
                onClicked: {
                    checked = !checked
                    settingTool.setSetting(SettingEnum.IS_NET_LIST, checked)
                }
            }
        }
    }
    Component {
        id: component2
        CusRect {

            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
            CusSwitchButton {
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                checked: settingTool.getSetting(SettingEnum.IS_AUTO_TAKE_ORDERS_AND_PRINT)

                onCheckedChanged: {
                    settingTool.setSetting(SettingEnum.IS_AUTO_TAKE_ORDERS_AND_PRINT, checked)
                }

                onClicked: {
                    checked = !checked
                }
            }
        }
    }
    Component {
        id: component8
        CusRect {

            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
            CusSwitchButton {
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                checked: settingTool.getSetting(SettingEnum.IS_REFUND_ORDER_AND_PRINT)

                onCheckedChanged: {
                    settingTool.setSetting(SettingEnum.IS_REFUND_ORDER_AND_PRINT, checked)
                }

                onClicked: {
                    checked = !checked
                }
            }
        }
    }
    Component {
        id: component3
        CusRect {
            color: ST.color_transparent

            RowLayout {
                anchors.right: parent.right
                anchors.top: parent.top
                anchors.bottom: parent.bottom

                spacing: 25 * dpi_ratio

                RadioButtonRectangleC {
                    anchors.verticalCenter: parent.verticalCenter
                    checked: true
                    width: widthBtn
                    height: heightBtn
                    text: qsTr("蓝牙连接")
                }
                RadioButtonRectangleC {
                    anchors.verticalCenter: parent.verticalCenter
                    width: widthBtn
                    height: heightBtn
                    text: qsTr("端口连接")
                }
                RadioButtonRectangleC {
                    anchors.verticalCenter: parent.verticalCenter
                    width: widthBtn
                    height: heightBtn
                    text: qsTr("USB连接")
                }
                RadioButtonRectangleC {
                    anchors.verticalCenter: parent.verticalCenter
                    width: widthBtn
                    height: heightBtn
                    text: qsTr("WIFI连接")
                }
            }
        }
    }
    Component {
        id: component4

        CusRect {

            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            TextStateC {
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                state_: false
            }
        }
    }
    Component {
        id: component5

        CusRect {

            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
            CusSpinBox {
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                value: Number(settingTool.getSetting(SettingEnum.PRINTER_DEFAULT_PRINT_COUNT))
                onValueChanged: {
                    settingTool.setSetting(SettingEnum.PRINTER_DEFAULT_PRINT_COUNT, value)
                }
            }
        }
    }
    Component {
        id: component6

        CusRect {

            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
            RowLayout {
                anchors.right: parent.right
                anchors.rightMargin: 50 * dpi_ratio
                anchors.verticalCenter: parent.verticalCenter
                spacing: 50 * dpi_ratio
                CusRadioManual {
                    text: "58mm"
                    checked: settingTool.getSetting(SettingEnum.TICKETS_SPECIFICATION == SettingEnum.SPEC_58)
                    onCheckedChanged: {
                        settingTool.setSetting(SettingEnum.TICKETS_SPECIFICATION, SettingEnum.SPEC_58)
                    }
                }
                CusRadioManual {
                    text: "80mm"
                    checked: settingTool.getSetting(SettingEnum.TICKETS_SPECIFICATION == SettingEnum.SPEC_80)
                    onCheckedChanged: {
                        settingTool.setSetting(SettingEnum.TICKETS_SPECIFICATION, SettingEnum.SPEC_80)
                    }
                }
            }
        }
    }
    Component {
        id: component7

        CusRect {

            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            CusSwitchButton {
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter

                checked: settingTool.getSetting(SettingEnum.IS_SEPARATE_ONLINE_REMARK)

                onCheckedChanged: {
                    settingTool.setSetting(SettingEnum.IS_SEPARATE_ONLINE_REMARK, checked)
                    text_area_remark.refreshText()
                }

                onClicked: {
                    checked = !checked
                }
            }
        }
    }

    Component {
        id: printer_conn_type
        Rectangle {
            property string key
            width: columnLayout1.width
            height: height_lable_info
            RowLayout {
                anchors.fill: parent
                Rectangle {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 150 * dpi_ratio
                    CusText {
                        text: key
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }
                CusSpacer {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                }
                Rectangle {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 280 * dpi_ratio * configTool.fontRatio
                    CusComboBox {
                        id: combobox_printer
                        anchors.fill: parent

                        onCurrentTextChanged: {
                            if (!is_need_init)
                                printerControl.setOnlinePrinterByName(currentText)
                        }
                        Component.onCompleted: {
                            model = printerControl.getPrinterNames()
                            currentIndex = find(settingTool.getSetting(SettingEnum.PRINTER_NAME_ONLINE))
                            is_need_init = false
                        }
                 
                    }
                }
            }
        }
    }
}
