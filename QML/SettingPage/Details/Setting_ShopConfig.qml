﻿import QtQuick 2.15
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import "../.."

Item {

    RowLayout {
        anchors.fill: parent
        spacing: 48 * dpi_ratio

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 25 * dpi_ratio

                Item {
                    Layout.fillWidth: true
                    height: 56 * dpi_ratio

                    CusRect {
                        id: rect1
                        width: 100 * dpi_ratio
                        height: 56 * dpi_ratio
                        color: ST.color_transparent

                        CusText {
                            text: qsTr("负利润提醒")
                            anchors.verticalCenter: parent.verticalCenter
                        }
                    }

                    CusRect {
                        anchors.fill: parent
                        color: ST.color_transparent

                        CusSwitchButton {
                            anchors.right: parent.right
                            anchors.verticalCenter: parent.verticalCenter
                            checked: configTool.isNegativeProfitHint
                            onClicked: {
                                configTool.isNegativeProfitHint = !configTool.isNegativeProfitHint
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.fillHeight: true //填充剩余区域
                }
            }
        }

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
        }
    }
}
