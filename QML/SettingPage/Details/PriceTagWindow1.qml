﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import EnumTool 1.0
import SettingEnum 1.0
import "../.."

CusRect {
    id: popup_root_price_tag_setting
    color: ST.color_transparent

    Component.onCompleted: {
        refreshPriceTagSetting()
    }

    onVisibleChanged: {
        if (!visible)
            return

        refreshPriceTagSetting()
    }

    property int lable_type: 0

    function open(lable_type_in) {
        popup_root_price_tag_setting.visible = true
        lable_type = lable_type_in

        resetInfo()
        refreshPriceTagSetting()

        logMgr.logEvtInfo4Qml("打开价签设置")
    }

    function close() {
        popup_root_price_tag_setting.visible = false
        keyboard_c.closeAll()
        logMgr.logEvtInfo4Qml("关闭价签设置")
    }

    function savePriceTagSetting() {
        logMgr.logEvtInfo4Qml("点击保存价签设置")

        var obj_price_tag_setting = {
            "paper_w": tf_paper_w.text,
            "paper_h": tf_paper_h.text,
            "dpi_mm": tf_dpi.text,
            "margin_left": tf_margin_left.text,
            "margin_top": tf_margin_top.text,
            "goods_name_x": tf_goods_name_x.text,
            "goods_name_y": tf_goods_name_y.text,
            "goods_name_size": tf_goods_name_size.text,
            "rmb_symbol_x": tf_rmb_symbol_x.text,
            "rmb_symbol_y": tf_rmb_symbol_y.text,
            "rmb_symbol_size": tf_rmb_symbol_size.text,
            "price_x": tf_price_x.text,
            "price_y": tf_price_y.text,
            "price_size": tf_price_size.text,
            "barcode_x": tf_barcode_x.text,
            "barcode_y": tf_barcode_y.text,
            "barcode_w": tf_barcode_w.text,
            "barcode_h": tf_barcode_h.text,
            "barcode_size": tf_barcode_size.text
        }
        if(printerControl.setPriceTagPrintFormatByJson(JSON.stringify(obj_price_tag_setting))){
           toast.openInfo("保存成功!")
           close()
        }else{
           toast.openInfo("保存失败!")
        }
    }

    function refreshPriceTagSetting() {
        var json_doc = JSON.parse(printerControl.getPriceTagInfo(lable_type))

        if (!json_doc)
            return

        tf_paper_w.text = Number(json_doc.paper_w).toFixed(2)
        tf_paper_h.text = Number(json_doc.paper_h).toFixed(2)
        tf_dpi.text = Number(json_doc.dpi_mm).toFixed(2)

        tf_margin_left.text = Number(json_doc.margin_left).toFixed(2)
        tf_margin_top.text = Number(json_doc.margin_top).toFixed(2)

        tf_goods_name_x.text = Number(json_doc.goods_name_x).toFixed(2)
        tf_goods_name_y.text = Number(json_doc.goods_name_y).toFixed(2)
        tf_goods_name_size.text = Number(json_doc.goods_name_size).toFixed(2)

        tf_rmb_symbol_x.text = Number(json_doc.rmb_symbol_x).toFixed(2)
        tf_rmb_symbol_y.text = Number(json_doc.rmb_symbol_y).toFixed(2)
        tf_rmb_symbol_size.text = Number(json_doc.rmb_symbol_size).toFixed(2)

        tf_price_x.text = Number(json_doc.price_x).toFixed(2)
        tf_price_y.text = Number(json_doc.price_y).toFixed(2)
        tf_price_size.text = Number(json_doc.price_size).toFixed(2)

        tf_barcode_x.text = Number(json_doc.barcode_x).toFixed(2)
        tf_barcode_y.text = Number(json_doc.barcode_y).toFixed(2)
        tf_barcode_w.text = Number(json_doc.barcode_w).toFixed(2)
        tf_barcode_h.text = Number(json_doc.barcode_h).toFixed(2)
        tf_barcode_size.text = Number(json_doc.barcode_size).toFixed(2)
    }

    function resetInfo() {
        tf_paper_w.clear()
        tf_paper_h.clear()
        tf_dpi.clear()

        tf_margin_left.clear()
        tf_margin_top.clear()

        tf_goods_name_x.clear()
        tf_goods_name_y.clear()
        tf_goods_name_size.clear()

        tf_rmb_symbol_x.clear()
        tf_rmb_symbol_y.clear()
        tf_rmb_symbol_size.clear()

        tf_price_x.clear()
        tf_price_y.clear()
        tf_price_size.clear()

        tf_barcode_x.clear()
        tf_barcode_y.clear()
        tf_barcode_w.clear()
        tf_barcode_h.clear()
        tf_barcode_size.clear()
    }

    property string title_name: "价签设置"

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 1700 * dpi_ratio
        height: 500 * dpi_ratio
        x: (parent.width - width) / 2
        y: 80 * dpi_ratio

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35
                color: ST.color_transparent

                MouseArea {
                    anchors.fill: parent
                }

                GridLayout {
                    id: gl_price_tag_setting
                    anchors.fill: parent
                    anchors.topMargin: 15 * dpi_ratio
                    anchors.bottomMargin: 15 * dpi_ratio

                    columns: 4
                    columnSpacing: (15 + 50) * dpi_ratio
                    rowSpacing: (15 + 30) * dpi_ratio

                    property double col_width: (width - (columns - 1) * columnSpacing) / columns

                    function prefWidth(item) {
                        return col_width * item.Layout.columnSpan + ((item.Layout.columnSpan - 1) * columnSpacing)
                    }
                    function prefHeight(item) {
                        return 50 * dpi_ratio
                    }
                    property real label_width: 145 * dpi_ratio

                    property var regular_str: /^[0-9]{0,4}(?:\.[0-9]{1,2})?$/

                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "纸张宽高(mm)"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_paper_w
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }

                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_paper_h
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }

                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "分辨率(mm)"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_dpi
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }

                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "边距 左/上(mm)"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_margin_left
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_margin_top
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "商品名 X/Y(mm)"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_goods_name_x
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_goods_name_y
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "商品名字号"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_goods_name_size
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "¥符号 X/Y(mm)"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_rmb_symbol_x
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_rmb_symbol_y
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }

                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "¥符号字号"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_rmb_symbol_size
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "价格 X/Y(mm)"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_price_x
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_price_y
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "价格字号"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_price_size
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "条码 X/Y(mm)"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_barcode_x
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_barcode_y
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }

                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "条码宽高"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_barcode_w
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_barcode_h
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.columnSpan: 1
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: gl_price_tag_setting.label_width
                                color: ST.color_transparent
                                CusText {
                                    text: "条码字号"
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 20 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.preferredWidth: 85 * dpi_ratio
                                        Layout.alignment: Qt.AlignHCenter
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            id: tf_barcode_size
                                            anchors.fill: parent
                                            keyboard_status: 1
                                            validator: RegularExpressionValidator {
                                                regularExpression: gl_price_tag_setting.regular_str
                                            }
                                            digital_keyboard_x: 980 * dpi_ratio
                                            digital_keyboard_y: 455 * dpi_ratio
                                            normal_keyboard_x: 660 * dpi_ratio
                                            normal_keyboard_y: 565 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.columnSpan: 4
                        Layout.preferredHeight: gl_price_tag_setting.prefHeight(this)
                        Layout.preferredWidth: gl_price_tag_setting.prefWidth(this)
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusSpacer {
                                Layout.fillWidth: true
                            }
                            CusButton {
                                text: "保存"
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                onClicked: {
                                    popup_root_price_tag_setting.savePriceTagSetting()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
