﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import "../.."

FocusScope {
    property int margin: 32 * dpi_ratio
    property int widthBtn: 143 * dpi_ratio
    property int heightBtn: 56 * dpi_ratio
    property int spacing_: 15 * dpi_ratio

    property bool  bundling: settingTool.getSetting(SettingEnum.IS_BUNDLING_SALE)
    property bool  orderling: settingTool.getSetting(SettingEnum.IS_ORDER_MANJIANMANZENG)
    property bool  goodsling: settingTool.getSetting(SettingEnum.IS_GOODS_MANJIANMANZENG)

    property int height_lable_info: 56 * dpi_ratio
    property int width_lable: 154 * dpi_ratio
    property int width_info: (width - width_lable)

    property var component_info: [component0,component1, component2,component3]
    property int widthColumnLayout: 880 * dpi_ratio

    //使用独立的备注内容
    ListModel {
        id: list_model
        ListElement {
            key: qsTr("捆绑销售")
            info_index: 0
        }
        ListElement {
            key: qsTr("订单满减满赠")
            info_index: 1
        }
        ListElement {
            key: qsTr("商品满减满赠")
            info_index: 2
        }
//        ListElement {
//            key: "改价不参与打折"
//            info_index: 3
//        }

    }

    ListView {
        id: columnLayout1
        anchors.left: parent.left
        width: 880 * dpi_ratio
        height: (spacing + height_lable_info) * (component_info.length + 1)
        spacing: spacing_
        model: list_model
        interactive: false

        delegate: Component {
            Loader {
                id: loader
                sourceComponent: component_info[info_index]
                Binding {
                    target: loader.item
                    property: "key"
                    value: key
                }
            }
        }
    }
    Component {
        id: component0
        CusRect {
            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            CusSwitchButton {
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                checked: bundling
                onClicked: {
                    bundling = !bundling
                    if(bundling == true){
                        settingTool.setSetting(SettingEnum.IS_BUNDLING_SALE, bundling)
                        settingTool.setSetting(SettingEnum.IS_ORDER_MANJIANMANZENG, !bundling)
                        orderling = !bundling
                        settingTool.setSetting(SettingEnum.IS_GOODS_MANJIANMANZENG, !bundling)
                        goodsling = !bundling
                    }else{
                        settingTool.setSetting(SettingEnum.IS_BUNDLING_SALE, bundling)
                    }
                }
            }
        }
    }
    Component {
        id: component1
        CusRect {

            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
            CusSwitchButton {
                id:cusSwitchB1
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                checked: orderling

                onCheckedChanged: {
                    if(orderling ==true ){
                        bundling = !orderling
                        settingTool.setSetting(SettingEnum.IS_BUNDLING_SALE, !orderling)
                        settingTool.setSetting(SettingEnum.IS_ORDER_MANJIANMANZENG, orderling)
                    }else{
                        settingTool.setSetting(SettingEnum.IS_ORDER_MANJIANMANZENG, orderling)
                    }
                }

                onClicked: {
                    orderling = !orderling
                }
            }
        }
    }
    Component {
        id: component2
        CusRect {

            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
            CusSwitchButton {
                id :cusSwitchB2
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                //checked: settingTool.getSetting(SettingEnum.IS_GOODS_MANJIANMANZENG)
                checked: goodsling

                onCheckedChanged: {
                    if(goodsling == true){
                        bundling = !goodsling
                        settingTool.setSetting(SettingEnum.IS_BUNDLING_SALE, !goodsling)
                        settingTool.setSetting(SettingEnum.IS_GOODS_MANJIANMANZENG, goodsling)
                    }else{
                        settingTool.setSetting(SettingEnum.IS_GOODS_MANJIANMANZENG, goodsling)
                    }
                }
                onClicked: {
                    goodsling = !goodsling
                }
            }
        }
    }

    Component {
        id: component3
        CusRect {

            property string key
            width: columnLayout1.width
            height: height_lable_info
            color: ST.color_transparent

            CusRect {
                id: rect1
                width: columnLayout1.width_lable
                height: parent.height
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
            CusSwitchButton {
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                checked: settingTool.getSetting(SettingEnum.IS_PRICE_CHANGE_NO_DISCOUNT)

                onCheckedChanged: {
                    settingTool.setSetting(SettingEnum.IS_PRICE_CHANGE_NO_DISCOUNT, checked)
                }

                onClicked: {
                    checked = !checked
                }
            }
        }
    }

}
