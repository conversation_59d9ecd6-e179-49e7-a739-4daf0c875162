﻿import QtQuick 2.15
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import "../.."

Item {

    RowLayout {
        anchors.fill: parent

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
            ColumnLayout {
                spacing: 15 * dpi_ratio
                anchors.fill: parent

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * dpi_ratio
                    color: ST.color_transparent
                    visible: false

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            color: ST.color_transparent
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            CusText {
                                text: "四舍五入位数"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 400 * dpi_ratio
                            color: ST.color_transparent

                            CusSpinBox {
                                anchors.right: parent.right
                                anchors.verticalCenter: parent.verticalCenter
                                value: Number(settingTool.getSetting(SettingEnum.ROUND_DOWN_DIGIT))
                                onValueChanged: {
                                    if (is_need_init)
                                        return
                                    settingTool.setSetting(SettingEnum.ROUND_DOWN_DIGIT, value)
                                }
                                Component.onCompleted: {
                                    is_need_init = false
                                }
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            color: ST.color_transparent
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            CusText {
                                text: qsTr("无码商品利润比 (利润/售价)")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 400 * dpi_ratio
                            color: ST.color_transparent

                            CusSpinBox {
                                id: spin_nocode
                                anchors.right: parent.right
                                anchors.verticalCenter: parent.verticalCenter
                                decimals: 2
                                value: Number(settingTool.getSetting(SettingEnum.NOCODE_GOODS_PROFIT_RATIO)) * spin_nocode.ratio
                                to: 1 * ratio
                                onValueChanged: {
                                    if (is_need_init)
                                        return
                                    settingTool.setSetting(SettingEnum.NOCODE_GOODS_PROFIT_RATIO, value / spin_nocode.ratio)
                                }
                                Component.onCompleted: {
                                    is_need_init = false
                                }
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            color: ST.color_transparent
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            CusText {
                                text: qsTr("普通商品利润比 (利润/售价)")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 400 * dpi_ratio
                            color: ST.color_transparent

                            CusSpinBox {
                                id: spin_normal_goods
                                anchors.right: parent.right
                                anchors.verticalCenter: parent.verticalCenter
                                decimals: 2
                                value: (configTool.normalGoodsProfitRatio * spin_nocode.ratio).toFixed(0)
                                to: 1 * ratio
                                onValueChanged: {
                                    if (is_need_init)
                                        return
                                    configTool.normalGoodsProfitRatio = (value / spin_nocode.ratio).toFixed(2)
                                }
                                Component.onCompleted: {
                                    is_need_init = false
                                }
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 56 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            color: ST.color_transparent
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            CusText {
                                text: qsTr("积分比例 (-1 ~ 100)")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 400 * dpi_ratio
                            color: ST.color_transparent

                            CusSpinBox {
                                id: spin_normal
                                anchors.right: parent.right
                                anchors.verticalCenter: parent.verticalCenter
                                from: -1
                                to: 100 * ratio
                                value: configTool.pointsRatio * spin_normal.ratio
                                onValueChanged: {
                                    if (is_need_init)
                                        return
                                    configTool.pointsRatio = value / spin_normal.ratio
                                }
                                Component.onCompleted: {
                                    is_need_init = false
                                }
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.fillHeight: true
                }
            }
        }
        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
        }
    }
}
