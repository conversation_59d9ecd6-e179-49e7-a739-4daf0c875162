﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQml.Models 2.15

import EnumTool 1.0
import SettingEnum 1.0

import "../.."

CusRect {
    id: icon
        required property Item dragParent

    width: 72 * dpi_ratio
    height: 72 * dpi_ratio

//    property string name_role

    anchors {
        horizontalCenter: parent.horizontalCenter
        verticalCenter: parent.verticalCenter
    }


    CusText {
        anchors.centerIn: parent
        color: "white"
        text: name_role
        font.bold: true
    }

    //    DragHandler {
    //        id: dragHandler
    //        target: icon
    //    }

    //    Drag.active: dragHandler.active
    //    Drag.hotSpot.x: 36 * dpi_ratio
    //    Drag.hotSpot.y: 36 * dpi_ratio

    //    states: [
    //        State {
    //            when: dragHandler.active
    //            ParentChange {
    //                target: icon
    //                parent: icon.dragParent
    //            }
    //            AnchorChanges {
    //                target: icon
    //                anchors.horizontalCenter: undefined
    //                anchors.verticalCenter: undefined
    //            }
    //        }
    //    ]
}
