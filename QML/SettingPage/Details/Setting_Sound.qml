﻿import QtQuick 2.15
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import "../.."

Item {
    property int height_lable_info: 56 * dpi_ratio

    RowLayout {
        anchors.fill: parent
        spacing: 200 * dpi_ratio

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 10 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("播放首页提示音")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isPlayPromptSound
                        onClicked: {
                            configTool.isPlayPromptSound = !configTool.isPlayPromptSound
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("播放键盘声")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isPlayKeyboardSound
                        onClicked: {
                            configTool.isPlayKeyboardSound = !configTool.isPlayKeyboardSound
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("播报在线支付")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isPlayOnlineTts
                        onClicked: {
                            configTool.isPlayOnlineTts = !configTool.isPlayOnlineTts
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("播报现金支付")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isPlayCashTts
                        onClicked: {
                            configTool.isPlayCashTts = !configTool.isPlayCashTts
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("播报会员支付")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isPlayMemberTts
                        onClicked: {
                            configTool.isPlayMemberTts = !configTool.isPlayMemberTts
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("播报线下支付")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isPlayOfflineTTs
                        onClicked: {
                            configTool.isPlayOfflineTTs = !configTool.isPlayOfflineTTs
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("播报小程序支付")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isPlayMiniProgram
                        onClicked: {
                            configTool.isPlayMiniProgram = !configTool.isPlayMiniProgram
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("播报小程序退款")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isPlayMiniProgramRefund
                        onClicked: {
                            configTool.isPlayMiniProgramRefund = !configTool.isPlayMiniProgramRefund
                        }
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("播报小程序接单")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isPlayMiniProgramTakeOrders
                        onClicked: {
                            configTool.isPlayMiniProgramTakeOrders = !configTool.isPlayMiniProgramTakeOrders
                        }
                    }
                }
                CusSpacer {
                    Layout.fillHeight: true
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent

                CusSpacer {
                    Layout.fillHeight: true
                }
            }
        }
    }
}
