﻿import QtQuick 2.15
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0
import BarcodeScaleEnum 1.0
import "../.."

Item {
    id: weighting_root

    //    property var component_info: [component_header, component0, component1, compo_data_parse, component2, component3]
    property var component_info: [component_header, component0, component1, compo_data_parse, component2, component3]

    property int widthColumnLayout: 779 * dpi_ratio

    signal qmlGetWeighingScaleState

    // 定义一个带参数的信号
    ButtonGroup {
        id: group_weighing_scale
    }
    ListModel {
        id: listModel0
        ListElement {
            key: qsTr("端口秤")
        }
        ListElement {
            key: qsTr("端口号")
        }
        ListElement {
            key: qsTr("波特率")
        }
        ListElement {
            key: qsTr("数据解析方式")
        }
        ListElement {
            key: qsTr("电子秤状态")
        }
        ListElement {
            key: qsTr("当前重量")
        }
    }

    RowLayout {
        anchors.fill: parent
        spacing: 48 * dpi_ratio

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            ListView {
                id: columnLayout1
                anchors.fill: parent
                spacing: 25 * dpi_ratio
                model: listModel0
                interactive: false

                Layout.alignment: Qt.AlignTop

                property int height_lable_info: 56 * dpi_ratio
                property int width_lable: 95 * dpi_ratio
                property int width_info: (width - width_lable)

                delegate: Component {
                    Loader {
                        id: loader
                        sourceComponent: component_info[index]
                        Binding {
                            target: loader.item
                            property: "key"
                            value: key
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
            id: rect_right_side

            //秤类型 Model
            ListModel {
                id: lm_barcode_scale
                Component.onCompleted: {
                    rect_right_side.refreshModel()
                }
            }

            function refreshModel() {
                lm_barcode_scale.clear()
                var json_doc = JSON.parse(barcodeLabelScaleCtrl.getBarcodeScaleTypeListJson())

                if (!json_doc)
                    return

                for (var i = 0; i < json_doc.length; ++i) {
                    var cur_item = json_doc[i]
                    lm_barcode_scale.append(cur_item)
                }
            }

            ColumnLayout {
                anchors.fill: parent
                spacing: 24 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 60 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent

                        CusSpacer {
                            Layout.fillWidth: true
                        }

                        CusButton {
                            id: btn_down_to_weigh
                            text: barcodeLabelScaleCtrl.is_transporting ? qsTr("下载到秤中") : qsTr("下载到秤")
                            Layout.preferredWidth: 200 * dpi_ratio
                            Layout.fillHeight: true
                            focusPolicy: Qt.ClickFocus
                            enabled: !barcodeLabelScaleCtrl.is_transporting
                            onClicked: {
                                if (barcodeLabelScaleCtrl.send2BarcodeScaleWizard()) {
                                    toast.openInfo(qsTr("下载到秤成功"))
                                } else {
                                    toast.openWarn(qsTr("下载到秤失败"))
                                }
                            }
                        }

                        CusButton {
                            Layout.preferredWidth: 143 * dpi_ratio
                            Layout.fillHeight: true
                            text: qsTr("新增秤")
                            onClicked: {
                                var json_doc = JSON.parse(barcodeLabelScaleCtrl.addBarcodeScaleItem())
                                if (!json_doc) {
                                    toast.openWarn(qsTr("新增失败!"))
                                    return
                                }
                                toast.openInfo(qsTr("新增成功"))
                                lm_barcode_scales.append(json_doc)
                            }
                        }
                    }
                }

                ListView {
                    id: lv_barcode_scales
                    clip: true
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    spacing: 24 * dpi_ratio

                    model: ListModel {
                        id: lm_barcode_scales
                    }

                    delegate: BarcodeScaleItem {
                        width: lv_barcode_scales.width
                    }

                    function refresh() {
                        lm_barcode_scales.clear()

                        var json_doc = JSON.parse(barcodeLabelScaleCtrl.getBarcodeScaleListJson())

                        if (!json_doc)
                            return

                        for (var i = 0; i < json_doc.length; ++i) {
                            var cur_item = json_doc[i]
                            lm_barcode_scales.append(cur_item)
                        }
                    }

                    Component.onCompleted: {
                        lv_barcode_scales.refresh()
                    }

                    onVisibleChanged: {
                        if (!visible)
                            return
                        lv_barcode_scales.refresh()
                    }
                }
            }
        }
    }

    Component {
        id: component_header
        CusRect {
            id: rootRect

            width: 779 * dpi_ratio
            height: 144 * dpi_ratio

            property string key
            color: ST.color_transparent

            SwitchButtonWithImageC {
                id: switch_button_with_image_serial_port_weighing_scale
                checked: settingTool.getSetting(SettingEnum.IS_SERIAL_PORT_WEIGHING_SCALE)
                textInfo1: key
                onCheckedChanged: {
                    settingTool.setSetting(SettingEnum.IS_SERIAL_PORT_WEIGHING_SCALE, checked ? true : false)
                }
            }
        }
    }
    Component {
        id: component_header2
        CusRect {
            id: rootRect

            width: 779 * dpi_ratio
            height: 144 * dpi_ratio
            color: ST.color_transparent

            property string key

            SwitchButtonWithImageC {
                id: switch_button_with_image_bar_code_weighing_scale
                textInfo1: key
                textInfo2: qsTr("启用电子秤")
                imageSourceName: "scalesBarcode.png"
                checked: settingTool.getSetting(SettingEnum.IS_BAR_CODE_WEIGHING_SCALE)
                onCheckedChanged: {
                    settingTool.setSetting(SettingEnum.IS_BAR_CODE_WEIGHING_SCALE, checked ? true : false)
                }
            }
        }
    }
    Component {
        id: component0
        CusRect {
            color: ST.color_transparent

            width: widthColumnLayout
            height: columnLayout1.height_lable_info

            property string key

            CusRect {
                width: columnLayout1.width_lable
                height: columnLayout1.height_lable_info
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            CusComboBox {
                id: com_combo
                model: settingTool.getSerialNameList()
                width: 285 * dpi_ratio * configTool.fontRatio
                height: 56 * dpi_ratio
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter

                function refreshCom() {
                    com_combo.model = weightingScaleControl.getAvailablePorts()
                    com_combo.currentIndex = com_combo.find(weightingScaleControl.getCom())
                }

                onVisibleChanged: {
                    if (visible)
                        refreshCom()
                }

                Component.onCompleted: {
                    refreshCom()
                    is_need_init = false
                }

                onCurrentTextChanged: {
                    if (is_need_init)
                        return

                    weightingScaleControl.setCom(currentText)
                }
            }
        }
    }
    Component {
        id: component1
        CusRect {
            color: ST.color_transparent

            property string key
            width: widthColumnLayout
            height: columnLayout1.height_lable_info

            CusRect {
                width: columnLayout1.width_lable
                height: columnLayout1.height_lable_info
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            CusComboBox {
                id: baud_combobox
                model: ["2400", "4800", "9600", "115200"]
                width: 285 * dpi_ratio * configTool.fontRatio
                height: 56 * dpi_ratio
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter

                function refreshInfo() {
                    baud_combobox.currentIndex = find(weightingScaleControl.getBaud())
                }

                onVisibleChanged: {
                    if (visible)
                        refreshInfo()
                }

                onCurrentTextChanged: {
                    if (is_need_init)
                        return
                    weightingScaleControl.setBaud(currentText)
                }

                Component.onCompleted: {
                    refreshInfo()
                    is_need_init = false
                }
            }
        }
    }
    Component {
        id: compo_data_parse
        CusRect {
            id: rect_state
            color: ST.color_transparent

            property string key
            width: widthColumnLayout
            height: columnLayout1.height_lable_info

            CusRect {
                width: columnLayout1.width_lable
                height: columnLayout1.height_lable_info
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            CusComboBox {
                id: baud_combobox
                width: 285 * dpi_ratio * configTool.fontRatio
                height: 56 * dpi_ratio
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter

                Component.onCompleted: {
                    model = weightingScaleControl.getParseTypes()
                    var pase_type = weightingScaleControl.getParseType()

                    var cur_index = baud_combobox.find(pase_type)
                    baud_combobox.currentIndex = cur_index

                    is_need_init = false
                }

                onCurrentTextChanged: {
                    if (is_need_init)
                        return

                    weightingScaleControl.setParseType(currentText)
                }
            }
        }
    }
    Component {
        id: component2
        CusRect {
            id: rect_state
            color: ST.color_transparent

            property string key
            width: widthColumnLayout
            height: columnLayout1.height_lable_info

            CusRect {
                width: columnLayout1.width_lable
                height: columnLayout1.height_lable_info
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            TextStateC {
                id: text_state_weighting_scale
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                state_: weightingScaleControl.isConnected
            }
        }
    }
    Component {
        id: component3
        CusRect {

            property string key
            width: widthColumnLayout
            height: columnLayout1.height_lable_info
            color: ST.color_transparent

            CusRect {
                width: columnLayout1.width_lable
                height: columnLayout1.height_lable_info
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            CusText {
                id: weighing_text
                text: "0"
                font.bold: true
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                font.pixelSize: 48 * configTool.fontRatio
                color: ST.color_green

                Connections {
                    target: weightingScaleControl
                    function onSendWeight(weight_str) {
                        weighing_text.text = weight_str
                    }
                }
            }
        }
    }

    Component {
        id: component_scales_type
        CusRect {

            property string key
            width: widthColumnLayout
            height: columnLayout1.height_lable_info
            color: ST.color_transparent

            CusRect {
                width: columnLayout1.width_lable
                height: columnLayout1.height_lable_info
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            CusComboBox {
                model: ["大华条码秤", "友声条码秤", "太航条码秤", "托利条码秤", "顶尖条码秤", "数衡条码秤", "佰伦斯条码秤"]
                width: 285 * dpi_ratio * configTool.fontRatio
                height: 56 * dpi_ratio
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                currentIndex: settingTool.getSetting(SettingEnum.BAR_CODE_WEIGHING_TYPE)
                onCurrentIndexChanged: {
                    if (currentIndex < 0)
                        return
                    settingTool.setSetting(SettingEnum.BAR_CODE_WEIGHING_TYPE, currentIndex)
                }
            }
        }
    }

    Component {
        id: component1_0
        CusRect {
            color: ST.color_transparent

            property string key
            width: widthColumnLayout
            height: columnLayout1.height_lable_info

            CusRect {
                width: columnLayout1.width_lable
                height: columnLayout1.height_lable_info
                color: ST.color_transparent

                CusText {
                    text: key
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            CusTextField {
                text: settingTool.getSetting(SettingEnum.BAR_CODE_WEIGHING_SCALE_IP)
                width: 285 * dpi_ratio
                height: 56 * dpi_ratio
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                onTextChanged: {
                    currentIndex: settingTool.setSetting(SettingEnum.BAR_CODE_WEIGHING_SCALE_IP, text)
                }
            }
        }
    }
}
