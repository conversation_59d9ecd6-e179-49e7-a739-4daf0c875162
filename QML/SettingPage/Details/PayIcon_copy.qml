﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQml.Models 2.15

import EnumTool 1.0
import SettingEnum 1.0

import "../.."

CusRect {
    id: icon
    property Item dragParent
    property bool is_enabled
    property int pay_enum
    property string name_role
    property int visualIndex: 0

    anchors.horizontalCenter: parent.horizontalCenter
    anchors.verticalCenter: parent.verticalCenter
    radius: ST.radius

    CusRect {
        anchors.fill: parent
        anchors.margins: 25 * dpi_ratio
        color: ST.color_transparent

        ColumnLayout {
            anchors.fill: parent
            CusRect {
                Layout.fillWidth: true
                Layout.preferredHeight: 45 * dpi_ratio
                color: ST.color_transparent

                CusSwitchButton {
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.horizontalCenter: parent.horizontalCenter
                    width: 170 * dpi_ratio
                    text: "是否开启"
                    checked: is_enabled
                    text_compo.color: ST.color_white_pure
                    onClicked: {
                        is_enabled = !is_enabled
                        delegateRoot.setIsEnabled(is_enabled)
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: ST.color_transparent

                CusText {
                    anchors.centerIn: parent
                    text: name_role
                    font.bold: true
                    font.pixelSize: 30 * dpi_ratio
                    color: ST.color_white_pure
                }
            }
        }
    }

    DragHandler {
        id: dragHandler
        target: icon
    }

    Drag.active: dragHandler.active
    Drag.hotSpot.x: 36 * dpi_ratio
    Drag.hotSpot.y: 36 * dpi_ratio

    states: [
        State {
            when: dragHandler.active
            ParentChange {
                target: icon
                parent: icon.dragParent
            }
            AnchorChanges {
                target: icon
                anchors.horizontalCenter: undefined
                anchors.verticalCenter: undefined
            }
        }
    ]
}
