﻿import QtQuick 2.15
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import "../.."
import "../../Common"

Item {
    property int height_lable_info_: 56 * dpi_ratio

    ListModel {
        id: lm_goods_camera

        property bool is_init: false

        function tryInit() {
            if (!is_init) {
                refresh()
                is_init = true
            }
        }

        function refresh() {
            var o_data = cameraControl.getAvailableCameras()
            var json_doc = JSON.parse(o_data)
            lm_goods_camera.clear()

            var lm_item1 = {
                "nameRole": qsTr("无")
            }
            lm_goods_camera.append(lm_item1)

            for (var i = 0; i < json_doc.length; ++i) {
                var cur_item = json_doc[i]

                var lm_item2 = {
                    "nameRole": cur_item
                }

                lm_goods_camera.append(lm_item2)
            }
        }
    }

    RowLayout {
        anchors.fill: parent

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 20 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info_
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("人脸摄像头")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusComboBox {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        height: parent.height
                        width: 500 * dpi_ratio

                        model: lm_goods_camera
                        textRole: "nameRole"

                        onCurrentTextChanged: {
                            if (is_need_init)
                                return
                            cameraControl.camera4Face = currentText
                        }
                        Component.onCompleted: {
                            is_need_init = true
                            lm_goods_camera.tryInit()
                            currentIndex = find(cameraControl.camera4Face)
                            is_need_init = false
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info_
                    color: ST.color_transparent
                    visible: configTool.isUseRecognition

                    CusText {
                        text: "识别摄像头"
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusComboBox {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        height: parent.height
                        width: 500 * dpi_ratio

                        model: lm_goods_camera
                        textRole: "nameRole"

                        onCurrentTextChanged: {
                            if (is_need_init)
                                return
                            cameraControl.camera4Goods = currentText
                        }

                        Component.onCompleted: {
                            is_need_init = true
                            lm_goods_camera.tryInit()
                            currentIndex = find(cameraControl.camera4Goods)
                            is_need_init = false
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info_
                    color: ST.color_transparent

                    CusButton {
                        height: parent.height
                        width: 300 * ST.dpi
                        text: qsTr("设置识别区域")

                        onClicked: {
                            showAreaDivisionPop()
                        }
                    }
                }

                CusSpacer {
                    Layout.fillHeight: true
                }
            }
        }

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
        }
    }

    function showAreaDivisionPop() {
        window_root.loader_4_area_division.sourceComponent = compo_area_division
        window_root.loader_4_area_division.item.open()
    }
    Component {
        id: compo_area_division
        PopupAreaDivision {}
    }
}
