﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0
import BarcodeScaleEnum 1.0
import "../.."

CusRect {
    id: rect_barcode
    height: (230 + (is_have_port ? 50 : 0)) * dpi_ratio
    radius: ST.radius
    color: ST.color_transparent

    property bool is_have_port: scale_type == BarcodeScaleEnum.BARCODE_LABLE_SCALE_TOLEDO
    property int scale_type: BarcodeScaleEnum.BARCODE_LABLE_SCALE_DAHUA

    border {
        width: 1 * dpi_ratio
        color: ST.color_grey_border
    }

    function focusCurScaleType() {
        combo_barcode_scale_selecter.is_need_init = true
        for (var i = 0; i < lm_barcode_scale.count; ++i) {
            var cur_item = lm_barcode_scale.get(i)
            scale_type = cur_item.scale_type_key
            if (cur_item.scale_type_key == barcode_scale_type) {
                combo_barcode_scale_selecter.currentIndex = i
                break
            }
        }
        combo_barcode_scale_selecter.is_need_init = false
    }

    CusRect {
        anchors.fill: parent
        anchors.margins: 24 * dpi_ratio
        color: ST.color_transparent

        RowLayout {
            anchors.fill: parent

            CusRect {
                Layout.fillHeight: true
                Layout.preferredWidth: 180 * dpi_ratio
                color: ST.color_transparent

                CusRect {
                    anchors.horizontalCenter: parent.horizontalCenter
                    anchors.top: parent.top
                    width: 120 * dpi_ratio
                    color: ST.color_transparent
                    height: width
                    Image {
                        source: "/Images/scalesBarcode.png"
                        anchors.fill: parent
                    }
                }
            }

            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 20 * dpi_ratio

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 255 * dpi_ratio * configTool.fontRatio
                                color: ST.color_transparent
                                CusComboBox {
                                    id: combo_barcode_scale_selecter
                                    anchors.fill: parent
                                    model: lm_barcode_scale
                                    textRole: "scale_type_name"

                                    onVisibleChanged: {
                                        if (visible) {
                                            focusCurScaleType()
                                        }
                                    }

                                    Component.onCompleted: {
                                        focusCurScaleType()
                                    }

                                    onCurrentIndexChanged: {
                                        if (is_need_init)
                                            return
                                        var cur_item = lm_barcode_scale.get(currentIndex)
                                        var scale_type = cur_item.scale_type_key
                                        barcodeLabelScaleCtrl.setBarcodeScaleItemScaleTypeByTimestamp(timestamp, scale_type)
                                        rect_barcode.scale_type = cur_item.scale_type_key
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent
                                CusRect {
                                    width: 40 * dpi_ratio
                                    height: width
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.right: parent.right
                                    color: ST.color_transparent
                                    Image {
                                        source: "/Images/delete_red.png"
                                        anchors.fill: parent

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (barcodeLabelScaleCtrl.delBarcodeScaleItemByTimestamp(timestamp)) {
                                                    for (var i = 0; i < lm_barcode_scales.count; ++i) {
                                                        var cur_item = lm_barcode_scales.get(i)
                                                        if (cur_item.timestamp == timestamp) {
                                                            lm_barcode_scales.remove(i)
                                                            toast.openInfo(qsTr("删除成功"))
                                                            return
                                                        }
                                                    }
                                                } else {
                                                    toast.openWarn(qsTr("删除失败!"))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 255 * dpi_ratio
                                color: ST.color_transparent
                                CusText {
                                    text: qsTr("启用电子秤")
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent
                                CusSwitchButton {
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.left: parent.left
                                    checked: is_enabled
                                    onClicked: {
                                        is_enabled = !is_enabled
                                        barcodeLabelScaleCtrl.setBarcodeScaleItemEnableByTimestamp(timestamp, is_enabled)
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        id: rect_ip
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 255 * dpi_ratio
                                color: ST.color_transparent
                                CusText {
                                    text: qsTr("IP地址")
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }

                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent
                                CusTextField {
                                    anchors.fill: parent
                                    text: net_info.ip
                                    keyboard_status: 1
                                    normal_keyboard_x: 733 * dpi_ratio
                                    normal_keyboard_y: 535 * dpi_ratio

                                    digital_keyboard_x: 540 * dpi_ratio
                                    digital_keyboard_y: 219 * dpi_ratio

                                    validator: RegExpValidator {
                                        regExp: /^(\d{1,3}\.){3}\d{1,3}$/
                                    }
                                    onEditingFinished: {
                                        barcodeLabelScaleCtrl.setBarcodeScaleItemIpByTimestamp(timestamp, text)
                                    }
                                }
                            }
                        }
                    }

                    CusRect {
                        id: rect_port
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent
                        visible: is_have_port
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 255 * dpi_ratio
                                color: ST.color_transparent
                                CusText {
                                    text: qsTr("端口号")
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }

                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent
                                CusTextField {
                                    anchors.fill: parent
                                    text: net_info.port
                                    keyboard_status: 1
                                    normal_keyboard_x: 733 * dpi_ratio
                                    normal_keyboard_y: 535 * dpi_ratio

                                    digital_keyboard_x: 540 * dpi_ratio
                                    digital_keyboard_y: 219 * dpi_ratio

                                    validator: RegExpValidator {
                                        regExp: /^[0-9]+$/
                                    }
                                    onEditingFinished: {
                                        barcodeLabelScaleCtrl.setBarcodeScaleItemPortByTimestamp(timestamp, text)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
