﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import "../.."

Item {
    property int height_lable_info_: 56 * dpi_ratio

    RowLayout {
        anchors.fill: parent

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 20 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info_
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("启用副屏")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        checked: settingTool.getSetting(SettingEnum.IS_USE_SECOND_SCREEN)
                        onCheckedChanged: {
                            if (is_need_init)
                                return

                            settingTool.setSetting(SettingEnum.IS_USE_SECOND_SCREEN, checked)
                        }
                        Component.onCompleted: {
                            is_need_init = false
                        }
                        onClicked: {
                            checked = !checked
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info_
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("钱箱打印机")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusComboBox {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        height: parent.height
                        width: 500 * dpi_ratio

                        onCurrentTextChanged: {
                            if (is_need_init)
                                return
                            settingTool.setSetting(SettingEnum.CASHBOX_PORT, currentText)
                        }
                        Component.onCompleted: {
                            model = printerControl.getPrinterNames()
                            currentIndex = find(settingTool.getSetting(SettingEnum.CASHBOX_PORT))
                            is_need_init = false
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info_
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("支付前弹钱箱")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        checked: settingTool.getSetting(SettingEnum.IS_OPEN_CASH_BOX_BEFORE_PAY)
                        onCheckedChanged: {
                            if (is_need_init)
                                return

                            settingTool.setSetting(SettingEnum.IS_OPEN_CASH_BOX_BEFORE_PAY, checked)
                        }
                        Component.onCompleted: {
                            is_need_init = false
                        }
                        onClicked: {
                            checked = !checked
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info_
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("TTS引擎")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusComboBox {
                        id: cb_tts_engines
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        height: parent.height
                        model: ListModel {
                            id: lm_tts
                        }
                        textRole: "role_name"
                        width: 500 * dpi_ratio

                        function refreshTtsEngine() {
                            is_need_init = true
                            lm_tts.clear()
                            var json_doc_data = JSON.parse(ttsControl.getAvailableEngines())
                            for (var i = 0; i < json_doc_data.length; ++i) {
                                var cur_item = json_doc_data[i]
                                lm_tts.append(cur_item)
                            }
                            is_need_init = false
                        }

                        function focusTtsEngine() {
                            is_need_init = true
                            cb_tts_engines.currentIndex = cb_tts_engines.find(ttsControl.getCurEngineName())
                            is_need_init = false
                        }

                        Component.onCompleted: {
                            refreshTtsEngine()
                            focusTtsEngine()
                        }

                        onVisibleChanged: {
                            if (!visible)
                                return
                            refreshTtsEngine()
                            focusTtsEngine()
                        }

                        onCurrentTextChanged: {
                            if (is_need_init)
                                return
                            ttsControl.setCurEngineByName(currentText)
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info_
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("TTS语言")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusComboBox {
                        id: cb_tts_locales
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        height: parent.height
                        model: ListModel {
                            id: lm_tts_locales
                        }
                        textRole: "role_name"
                        width: 500 * dpi_ratio

                        function refreshTtsLanguage() {
                            is_need_init = true
                            lm_tts_locales.clear()
                            var json_doc_data = JSON.parse(ttsControl.getAvailableLocales())

                            for (var i = 0; i < json_doc_data.length; ++i) {
                                var cur_item = json_doc_data[i]
                                lm_tts_locales.append(cur_item)
                            }

                            cb_tts_locales.currentIndex = cb_tts_locales.find(ttsControl.getCurLocaleName())
                            is_need_init = false
                        }

                        Component.onCompleted: {
                            refreshTtsLanguage()
                        }

                        onVisibleChanged: {
                            if (!visible)
                                return
                            refreshTtsLanguage()
                        }

                        onCurrentTextChanged: {
                            if (is_need_init)
                                return
                            ttsControl.setCurLocaleByName(currentText)
                        }

                        Connections {
                            target: ttsControl
                            function onSigEngineChanged() {
                                cb_tts_locales.refreshTtsLanguage()
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info_
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("TTS声音")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusComboBox {
                        id: cb_tts_voices
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        height: parent.height
                        model: ListModel {
                            id: lm_tts_voices
                        }
                        textRole: "role_name"
                        width: 500 * dpi_ratio

                        function refreshTtsVoice() {
                            is_need_init = true
                            lm_tts_voices.clear()
                            var json_doc_data = JSON.parse(ttsControl.getAvailableVoices())
                            for (var i = 0; i < json_doc_data.length; ++i) {
                                var cur_item = json_doc_data[i]
                                lm_tts_voices.append(cur_item)
                            }
                            cb_tts_voices.currentIndex = cb_tts_voices.find(ttsControl.getCurVoiceName())
                            is_need_init = false
                        }

                        Component.onCompleted: {
                            refreshTtsVoice()
                        }

                        onVisibleChanged: {
                            if (!visible)
                                return
                            refreshTtsVoice()
                        }

                        onCurrentTextChanged: {
                            if (is_need_init)
                                return
                            ttsControl.setCurVoiceByName(currentText)
                        }

                        Connections {
                            target: ttsControl
                            function onSigLocaleChanged() {
                                cb_tts_voices.refreshTtsVoice()
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info_
                    color: ST.color_transparent

                    CusButton {
                        height: parent.height
                        width: 320 * dpi_ratio
                        anchors.right: parent.right
                        text: qsTr("语音测试")
                        onClicked: {
                            ttsControl.testTts()
                        }
                    }
                }
                CusSpacer {
                    Layout.fillHeight: true
                }
            }
        }

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
        }
    }
}
