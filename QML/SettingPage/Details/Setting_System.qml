﻿import QtQuick 2.15
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import "../.."

Item {
    property int height_lable_info: 56 * dpi_ratio

    RowLayout {
        anchors.fill: parent
        spacing: 200 * dpi_ratio

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 10 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info

                    color: ST.color_transparent

                    CusText {
                        text: qsTr("称重商品的前两位")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusTextField {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 300 * dpi_ratio
                        text: settingTool.getSetting(SettingEnum.BAR_CODE_WEIGHING_PRE_TWO_CODE)
                        keyboard_status: 1

                        onTextChanged: {
                            settingTool.setSetting(SettingEnum.BAR_CODE_WEIGHING_PRE_TWO_CODE, text)
                        }

                        validator: RegularExpressionValidator {
                            regularExpression: /^[1-9]\d{0,1}$|^0?[1-9]$/
                        }

                        normal_keyboard_x: 670 * dpi_ratio
                        normal_keyboard_y: 560 * dpi_ratio

                        digital_keyboard_x: 1140 * dpi_ratio
                        digital_keyboard_y: 114 * dpi_ratio
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info

                    color: ST.color_transparent

                    CusText {
                        text: qsTr("普通商品的前两位")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusTextField {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 300 * dpi_ratio
                        text: settingTool.getSetting(SettingEnum.BAR_CODE_NORMAL_PRE_TWO_CODE)
                        keyboard_status: 1

                        onTextChanged: {
                            settingTool.setSetting(SettingEnum.BAR_CODE_NORMAL_PRE_TWO_CODE, text)
                        }

                        validator: RegularExpressionValidator {
                            regularExpression: /^[1-9]\d{0,1}$|^0?[1-9]$/
                        }

                        normal_keyboard_x: 670 * dpi_ratio
                        normal_keyboard_y: 560 * dpi_ratio

                        digital_keyboard_x: 1140 * dpi_ratio
                        digital_keyboard_y: 114 * dpi_ratio
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("串口秤市斤")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: settingTool.isUseJinWeightScale
                        onClicked: {
                            settingTool.isUseJinWeightScale = !settingTool.isUseJinWeightScale
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("未创建无码商品直接添加到购物车")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: settingTool.getSetting(SettingEnum.INEXIST_WEIGHT_GOODS_SEARCH_STATUS) == SettingEnum.INEXIST_WEIGHT_GOODS_SEARCH_STATUS__ADD_2_CART
                        onClicked: {
                            checked = !checked
                            if (checked) {
                                settingTool.setSetting(SettingEnum.INEXIST_WEIGHT_GOODS_SEARCH_STATUS, SettingEnum.INEXIST_WEIGHT_GOODS_SEARCH_STATUS__ADD_2_CART)
                            } else {
                                settingTool.setSetting(SettingEnum.INEXIST_WEIGHT_GOODS_SEARCH_STATUS, SettingEnum.INEXIST_WEIGHT_GOODS_SEARCH_STATUS__ADD)
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("程序置顶")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: settingTool.getSetting(SettingEnum.IS_PROGRAM_TOP)
                        onClicked: {
                            checked = !checked
                            is_program_top = checked
                            settingTool.setSetting(SettingEnum.IS_PROGRAM_TOP, checked)
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("开机自启")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: settingTool.getSetting(SettingEnum.IS_AUTO_RUN)
                        onClicked: {
                            settingTool.setSetting(SettingEnum.IS_AUTO_RUN, !checked)
                            checked = settingTool.getSetting(SettingEnum.IS_AUTO_RUN)
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent
                    visible: false

                    CusText {
                        text: qsTr("显示保质期")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: settingTool.getSetting(SettingEnum.IS_SHOW_EXPIRATION_DATE)
                        onClicked: {
                            checked = !checked
                            settingTool.setSetting(SettingEnum.IS_SHOW_EXPIRATION_DATE, checked)
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("显示 '无码商品/称重'")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: goodsManager.isShowNoCodeGoods
                        onClicked: {
                            goodsManager.isShowNoCodeGoods = !goodsManager.isShowNoCodeGoods
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("交换 '无码商品/称重' 位置")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: goodsManager.isSwapNoCodeGoods
                        onClicked: {
                            goodsManager.isSwapNoCodeGoods = !goodsManager.isSwapNoCodeGoods
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("使用虚拟键盘")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isUseVirtualKeyboard
                        onClicked: {
                            configTool.isUseVirtualKeyboard = !configTool.isUseVirtualKeyboard
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("挂单打印小票")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: settingTool.getSetting(SettingEnum.IS_PENDING_ORDER_PRINT_TICKETS)
                        onClicked: {
                            settingTool.setSetting(SettingEnum.IS_PENDING_ORDER_PRINT_TICKETS, !checked)
                            checked = settingTool.getSetting(SettingEnum.IS_PENDING_ORDER_PRINT_TICKETS)
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("重新同步商品")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusButton {
                        id: btn_goods_sync
                        anchors.right: parent.right
                        height: parent.height
                        width: 180 * dpi_ratio * configTool.fontRatio
                        text: qsTr("开始同步")

                        onClicked: {
                            btn_goods_sync.enabled = false
                            goodsControl.reqGoodsAndSave()
                        }

                        Connections {
                            target: goodsControl
                            function onSigGoodsReload() {
                                toast.openInfo(qsTr("同步完成"))
                                btn_goods_sync.enabled = true
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.fillHeight: true
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info

                    color: ST.color_transparent

                    CusText {
                        text: qsTr("界面比例")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusTextField {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 300 * dpi_ratio

                        text: configTool.fontRatio.toFixed(2)
                        keyboard_status: 1

                        onEditingFinished: {
                            configTool.fontRatio = Number(text)
                        }

                        validator: RegularExpressionValidator {
                            regularExpression: /^\d+(\.\d{1,2})?$/
                        }

                        normal_keyboard_x: 670 * dpi_ratio
                        normal_keyboard_y: 560 * dpi_ratio

                        digital_keyboard_x: 610 * dpi_ratio
                        digital_keyboard_y: 290 * dpi_ratio
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    visible: false
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("计算小数位数")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusTextField {
                        anchors.right: parent.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 300 * dpi_ratio

                        text: configTool.clacDigitNum
                        keyboard_status: 1

                        onEditingFinished: {
                            configTool.clacDigitNum = Number(text)
                        }

                        validator: RegularExpressionValidator {
                            regularExpression: /^\d+(\.\d{1,2})?$/
                        }

                        normal_keyboard_x: 670 * dpi_ratio
                        normal_keyboard_y: 560 * dpi_ratio

                        digital_keyboard_x: 610 * dpi_ratio
                        digital_keyboard_y: 290 * dpi_ratio
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    visible: false
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 230 * dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("超出小数计算方式")
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusSpacer {
                            Layout.fillWidth: true
                        }

                        CusRect {
                            Layout.preferredWidth: 320 * dpi_ratio
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent

                                CusSpacer {
                                    Layout.fillWidth: true
                                }

                                CusRadioManual {
                                    Layout.preferredWidth: 150 * dpi_ratio
                                    Layout.alignment: Qt.AlignVCenter
                                    text: qsTr("全舍")
                                    checked: configTool.calcType == 0

                                    onClicked: {
                                        configTool.calcType = 0
                                    }
                                }

                                CusRadioManual {
                                    Layout.preferredWidth: 150 * dpi_ratio
                                    Layout.alignment: Qt.AlignVCenter
                                    text: qsTr("四舍五入")
                                    checked: configTool.calcType == 1

                                    onClicked: {
                                        configTool.calcType = 1
                                    }
                                }
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("首页输入数字创建无码商品")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isQuickCreateNocodeGoods
                        onClicked: {
                            configTool.isQuickCreateNocodeGoods = !configTool.isQuickCreateNocodeGoods
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent
                    visible: false

                    CusText {
                        text: qsTr("是否展示库存")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isShowStock
                        onClicked: {
                            configTool.isShowStock = !configTool.isShowStock
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("退出时关闭计算机")
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    CusSwitchButton {
                        anchors.right: parent.right
                        anchors.rightMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        checked: configTool.isPowerOffWhenExit
                        onClicked: {
                            configTool.isPowerOffWhenExit = !configTool.isPowerOffWhenExit
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredHeight: 20 * dpi_ratio
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 10 * dpi_ratio * configTool.fontRatio

                        CusButton {
                            Layout.alignment: Qt.AlignVCenter

                            Layout.fillHeight: true
                            Layout.preferredWidth: 180 * dpi_ratio * configTool.fontRatio
                            text: qsTr("检查更新")
                            onClicked: {
                                if(isNeedUpdate == true){
                                    updateCtrl.updateStart()
                                }else{
                                    toast.openInfo(qsTr("无可更新内容!"))
                                }
                            }
                        }

                        CusButton {
                            Layout.alignment: Qt.AlignVCenter
                            Layout.fillHeight: true
                            Layout.preferredWidth: 180 * dpi_ratio * configTool.fontRatio
                            text: qsTr("上传当前日志")
                            onClicked: {
                                logCtrl.uploadLogZipWizard()
                            }
                        }

                        CusSpacer {
                            Layout.fillWidth: true
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredHeight: 8 * dpi_ratio
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: height_lable_info
                    color: ST.color_transparent

                    CusButton {
                        anchors.verticalCenter: parent.verticalCenter
                        height: parent.height
                        width: 180 * dpi_ratio * configTool.fontRatio
                        text: qsTr("退出程序")
                        color: ST.color_red
                        onClicked: {
                            window_root.close()
                        }
                    }
                }

                CusSpacer {
                    Layout.fillHeight: true
                }
            }
        }
    }
}
