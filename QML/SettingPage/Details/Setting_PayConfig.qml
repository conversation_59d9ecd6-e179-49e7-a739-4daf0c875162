﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQml.Models 2.15

import EnumTool 1.0
import SettingEnum 1.0
import SortFilterProxyModel 0.2

import "../.."

Item {
    id: pay_config_root
    property int margin: 32 * dpi_ratio
    property int widthBtn: 143 * dpi_ratio
    property int widthColumnLayout: 700 * dpi_ratio

    function refreshPayMethod() {
        lm_pay_method.clear()

        var json_pay_method = JSON.parse(payMethodControl.getEnabledPayMethodStatusListJson())

        for (var i = 0; i < json_pay_method.length; ++i) {
            var cur_item = json_pay_method[i]
            lm_pay_method.append(cur_item)
        }

        rect_pay_method_items.refreshPayMethodItemStatus()
    }

    function savePayMethod() {
        var result_data = []

        for (var i = 0; i < lm_pay_method.count; ++i) {
            var cur_item = lm_pay_method.get(i)
            result_data.push(cur_item)
        }

        payMethodControl.savePayMethodStatusListByJson(JSON.stringify(result_data))
    }

    Component.onCompleted: {
        refreshPayMethod()
    }

    ListModel {
        id: lm_pay_method
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 55 * dpi_ratio

        CusRect {
            Layout.fillWidth: true
            Layout.preferredHeight: 260 * dpi_ratio
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 30 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 40 * dpi_ratio
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("请选择首页快捷操作显示功能，开启后功能按钮会在首页展示")
                        font.pixelSize: 28 * dpi_ratio * configTool.fontRatio
                        font.bold: true
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    radius: ST.radius
                    color: ST.color_black_2

                    GridView {
                        id: gv_pay_config
                        anchors.fill: parent

                        cellWidth: gv_pay_config.width / 7.001 //精度问题
                        cellHeight: gv_pay_config.height

                        interactive: false

                        displaced: Transition {
                            NumberAnimation {
                                properties: "x,y"
                                easing.type: Easing.OutQuad
                            }
                        }

                        model: lm_pay_method

                        delegate: DropArea {
                            id: delegateRoot

                            width: gv_pay_config.cellWidth
                            height: gv_pay_config.cellHeight

                            onEntered: function (drag) {
                                var index_tmp = (drag.source as PayIcon).visualIndex
                                if (index_tmp != pay_icon.visualIndex) {
                                    lm_pay_method.move(index_tmp, pay_icon.visualIndex, 1)
                                    savePayMethod()
                                }
                            }

                            function setIsEnabled(is_enable) {
                                model.is_enabled = is_enable
                            }

                            property int visualIndex: index //index
                            property bool is_enabled: model.is_enabled //开启
                            property int pay_enum: model.pay_enum //支付方式
                            property string name_role: model.name_role //支付方式名字

                            PayIcon {
                                id: pay_icon
                                width: delegateRoot.width - 10 * 2 * dpi_ratio
                                height: delegateRoot.height - 10 * 2 * dpi_ratio
                                is_enabled: delegateRoot.is_enabled
                                pay_enum: delegateRoot.pay_enum
                                name_role: delegateRoot.name_role
                                dragParent: gv_pay_config
                                visualIndex: delegateRoot.visualIndex

                                color: visualIndex % 2 == 1 ? ST.color_blue_deeper : ST.color_green
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            id: rect_pay_method_items
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            function refreshPayMethodItemStatus() {
                pay_method_item_cash.refreshPayMethodStatus()
                pay_method_item_alipay.refreshPayMethodStatus()
                pay_method_item_wechat.refreshPayMethodStatus()
                pay_method_item_face.refreshPayMethodStatus()

                pay_method_item_vip.refreshPayMethodStatus()
                pay_method_item_combined.refreshPayMethodStatus()
                pay_method_item_goods_mgr.refreshPayMethodStatus()
            }

            ColumnLayout {
                anchors.fill: parent
                spacing: 40 * dpi_ratio
                PayMethodItem {
                    id: pay_method_item_cash
                    Layout.fillWidth: true
                    Layout.preferredHeight: 38 * dpi_ratio
                    img_path: "/Images/icon_cash.png"
                    pay_method_title: qsTr("现金收款")
                    pay_method_detail: qsTr("(消费者使用现金支付)")
                    pay_method_enum: EnumTool.PAY_METHOD__CASH
                }
                PayMethodItem {
                    id: pay_method_item_alipay
                    Layout.fillWidth: true
                    Layout.preferredHeight: 38 * dpi_ratio
                    img_path: "/Images/icon_alipay.png"
                    pay_method_title: qsTr("支付宝收款 ")
                    pay_method_detail: qsTr("(消费者使用支付宝扫收款码支付)")
                    pay_method_enum: EnumTool.PAY_METHOD__ALIPAY_OFFLINE
                }
                PayMethodItem {
                    id: pay_method_item_wechat
                    Layout.fillWidth: true
                    Layout.preferredHeight: 38 * dpi_ratio
                    img_path: "/Images/icon_wechat.png"
                    pay_method_title: qsTr("微信收款")
                    pay_method_detail: qsTr("(消费者使用微信扫收款码支付)")
                    pay_method_enum: EnumTool.PAY_METHOD__WECHAT_OFFLINE
                }
                PayMethodItem {
                    id: pay_method_item_face
                    Layout.fillWidth: true
                    Layout.preferredHeight: 38 * dpi_ratio
                    img_path: "/Images/employer_image.png"
                    pay_method_title: qsTr("人脸收款")
                    pay_method_detail: qsTr("(消费者使用人脸识别支付)")
                    pay_method_enum: EnumTool.PAY_METHOD__FACE
                }
                PayMethodItem {
                    id: pay_method_item_vip
                    Layout.fillWidth: true
                    Layout.preferredHeight: 38 * dpi_ratio
                    img_path: "/Images/icon_bank_card.png"
                    pay_method_title: qsTr("储值卡支付")
                    pay_method_detail: qsTr("(消费者使用会员余额支付)")
                    pay_method_enum: EnumTool.PAY_METHOD__VIPCARD
                }
                PayMethodItem {
                    id: pay_method_item_combined
                    Layout.fillWidth: true
                    Layout.preferredHeight: 38 * dpi_ratio
                    img_path: "/Images/icon_combined.png"
                    pay_method_title: qsTr("组合支付")
                    pay_method_detail: qsTr("(消费者使用支付宝/微信+现金多种方式支付)")
                    pay_method_enum: EnumTool.PAY_METHOD__COMBINED
                }
                PayMethodItem {
                    id: pay_method_item_goods_mgr
                    Layout.fillWidth: true
                    Layout.preferredHeight: 38 * dpi_ratio
                    img_path: "/Images/icon_goods_mgr.png"
                    pay_method_title: qsTr("商品管理")
                    pay_method_detail: qsTr("(快速添加、修改商品信息)")
                    pay_method_enum: EnumTool.PAY_METHOD__GOODS_MANAGER
                }

                CusSpacer {
                    Layout.fillHeight: true
                }
            }
        }
    }

    component PayMethodItem: CusRect {
        id: pay_method_item_root
        Layout.fillWidth: true
        Layout.preferredHeight: 38 * dpi_ratio
        color: ST.color_transparent

        property int pay_method_enum: EnumTool.PAY_METHOD__UNKNOW
        property string img_path
        property string pay_method_title
        property string pay_method_detail
        property bool is_enabled: false

        function refreshPayMethodStatus() {
            is_enabled = payMethodControl.getPayMethodStatus(pay_method_enum)
        }

        function setPayMethodStatus(is_enabled) {
            payMethodControl.setPayMethodStatus(pay_method_enum, is_enabled)
            refreshPayMethod()
            savePayMethod()
        }

        RowLayout {
            anchors.fill: parent

            CusRect {
                Layout.fillHeight: true
                Layout.preferredWidth: 800 * dpi_ratio * configTool.fontRatio
                color: ST.color_transparent

                RowLayout {
                    anchors.fill: parent
                    spacing: 16 * dpi_ratio

                    CusRect {
                        Layout.fillHeight: true
                        Layout.preferredWidth: 34 * dpi_ratio
                        color: ST.color_transparent
                        Image {
                            anchors.centerIn: parent
                            width: 34 * dpi_ratio * configTool.fontRatio
                            height: width
                            source: pay_method_item_root.img_path
                            fillMode: Image.PreserveAspectFit
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        CusText {
                            id: t_pay_method_title
                            text: pay_method_item_root.pay_method_title
                            anchors.verticalCenter: parent.verticalCenter
                            font.pixelSize: 28 * dpi_ratio * configTool.fontRatio
                        }

                        CusText {
                            text: pay_method_item_root.pay_method_detail
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left: t_pay_method_title.right
                            anchors.leftMargin: 10 * dpi_ratio
                            color: ST.color_grey_font
                            font.pixelSize: 28 * dpi_ratio * configTool.fontRatio
                        }
                    }
                }
            }

            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_transparent

                CusSwitchButton {
                    id: sw_pay_method_status
                    anchors.verticalCenter: parent.verticalCenter
                    checked: pay_method_item_root.is_enabled
                    onClicked: {
                        checked = !checked
                        pay_method_item_root.setPayMethodStatus(checked)
                    }
                }
            }
        }
    }
}
