﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import "Details"
import ".."

Item {
    //标识当前页面
    property int listIndex: 0
    property int pageIndex: 0
    Rectangle {
        anchors.fill: parent
        color: ST.color_white
    }

    Image {
        anchors.fill: parent
        source: "/Images/mainPageBackground.jpg"
    }

    RowLayout {
        anchors.fill: parent
        anchors.margins: 15 * dpi_ratio
        //        anchors.topMargin: 0
        spacing: 15 * dpi_ratio
        //左侧列表
        ListMenu {
            Layout.preferredWidth: 216 * dpi_ratio
            Layout.fillHeight: true
        }
        //右侧内容
        Setting__Main {
            Layout.fillWidth: true
            Layout.fillHeight: true
        }
    }
}
