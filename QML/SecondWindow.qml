﻿import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import ShopCartModel 1.0
import EnumTool 1.0

import "PayPage"
import "Utils"
import "Common"
import "SettingPage"
import "NetOrderPage"
import "QueryPage"
import "GodownPage"
import "MembershipPage"
import "StatisticsPage"
import "HandOverPage"
import "LoginPage"
import "."
import "PayPage/PayMethods/"

Window {
    id: second_window
    x: utils4Qml.getScreenWidth()
    flags: Qt.Window | Qt.FramelessWindowHint
    readonly property real window_width: utils4Qml.getScreenWidth(1) + 2
    readonly property real window_height: utils4Qml.getScreenHeight(1) + 2

    property real dpi_ratio: dpi_ratio_second_screen

    //副屏
    RowLayout {
        anchors.fill: parent
        spacing: 0
        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true

            ColumnLayout {
                anchors.fill: parent
                spacing: 0
                //广告图
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Image {
                        id: ad_img
                        cache: false
                        source: "/Images/defaultPictute.png"
                        anchors.fill: parent
                    }
                }
                //底部公司名称
                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 70 * dpi_ratio_second_screen
                    color: ST.color_font
                    CusText {
                        text: qsTr("山东影响力智能科技有限公司")
                        anchors.verticalCenter: parent.verticalCenter
                        font.pixelSize: 30 * dpi_ratio_second_screen
                        color: ST.color_white_pure
                        x: 30 * dpi_ratio_second_screen
                    }

                    CusText {
                        text: "V" + utils4Qml.getVersion()
                        anchors.verticalCenter: parent.verticalCenter
                        font.pixelSize: 30 * dpi_ratio_second_screen
                        color: ST.color_white_pure
                        anchors.right: parent.right
                        anchors.rightMargin: 30 * dpi_ratio_second_screen
                    }
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: 480 * dpi_ratio_second_screen
            color: ST.color_white_pure

            CusRect {
                anchors.fill: parent
                anchors.margins: 30 * dpi_ratio_second_screen
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent

                    //条码部分
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 320 * dpi_ratio_second_screen
                        color: ST.color_transparent

                        ColumnLayout {
                            anchors.fill: parent
                            CusRect {
                                id: rect_qrcode
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent

                                Image {
                                    id: qrcode_img
                                    anchors.centerIn: parent
                                    width: 210 * dpi_ratio_second_screen
                                    height: 210 * dpi_ratio_second_screen
                                    fillMode: Image.PreserveAspectFit

                                    function reloadImage() {
                                        var oldSource = source
                                        source = ""
                                        source = oldSource
                                    }
                                }

                                property string mini_program_img_url: ""

                                Image {
                                    id: mini_program_img

                                    anchors.centerIn: parent
                                    width: 250 * dpi_ratio_second_screen
                                    height: 250 * dpi_ratio_second_screen
                                    fillMode: Image.PreserveAspectFit

                                    source: rect_qrcode.mini_program_img_url

                                    function reloadImage() {
                                        var oldSource = source
                                        source = ""
                                        source = oldSource
                                    }
                                }

                                Connections {
                                    target: payMethodControl
                                    function onSigCloseQRCode() {
                                        rect_qrcode.state = "miniProgram"
                                    }
                                    function onSigRefreshMiniProgramCode() {
                                        rect_qrcode.mini_program_img_url = payMethodControl.getMiniProgramCodeUrl()
                                        mini_program_img.reloadImage()
                                    }
                                }

                                state: "miniProgram"
                                states: [
                                    State {
                                        name: "miniProgram"
                                        PropertyChanges {
                                            target: qrcode_img
                                            source: ""
                                            visible: false
                                        }
                                        PropertyChanges {
                                            target: mini_program_img
                                            visible: true
                                        }
                                        PropertyChanges {
                                            target: text_code_img_info
                                            text: qsTr("欢迎使用一刻钟小程序")
                                        }
                                    }
                                ]
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 70 * dpi_ratio_second_screen
                                color: ST.color_transparent
                                CusText {
                                    id: text_code_img_info
                                    text: qsTr("请使用微信或支付宝扫码支付")
                                    anchors.centerIn: parent
                                    font.pixelSize: 25 * dpi_ratio_second_screen
                                }
                            }
                        }
                    }

                    //购物车
                    CusRect {
                        id: rect_shop_cart
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        state: "default"
                        states: [
                            State {
                                name: "default"
                                PropertyChanges {
                                    target: img_pay_succ
                                    visible: false
                                }
                            },
                            State {
                                name: "pay_finish"
                                PropertyChanges {
                                    target: img_pay_succ
                                    visible: true
                                }
                            }
                        ]

                        ColumnLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 50 * dpi_ratio_second_screen
                                color: ST.color_white_pure

                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("商品")
                                            x: 10 * dpi_ratio_second_screen
                                            anchors.verticalCenter: parent.verticalCenter
                                            color: ST.color_black
                                            width: (parent.width - 10 * dpi_ratio_second_screen)
                                            elide: Text.ElideMiddle
                                            font.pixelSize: 20 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 85 * dpi_ratio_second_screen
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("单价")
                                            x: 20 * dpi_ratio_second_screen
                                            anchors.verticalCenter: parent.verticalCenter
                                            color: ST.color_black
                                            font.pixelSize: 20 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 85 * dpi_ratio_second_screen
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("重量")
                                            x: 20 * dpi_ratio_second_screen
                                            anchors.verticalCenter: parent.verticalCenter
                                            color: ST.color_black
                                            font.pixelSize: 20 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 85 * dpi_ratio_second_screen
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("价格")
                                            x: 20 * dpi_ratio_second_screen
                                            anchors.verticalCenter: parent.verticalCenter
                                            color: ST.color_black
                                            font.pixelSize: 20 * dpi_ratio
                                        }
                                    }
                                }
                            }

                            ListView {
                                id: lv_shop_cart
                                Layout.fillWidth: true
                                Layout.fillHeight: true

                                clip: true
                                model: ShopCartModel {
                                    list: shopCartList //绑定购物车
                                }
                                delegate: CusRect {
                                    width: lv_shop_cart.width
                                    height: 60 * dpi_ratio_second_screen
                                    color: ST.color_white_pure

                                    CusRect {
                                        color: ST.color_grey_border
                                        height: 1
                                        width: parent.width
                                        anchors.top: parent.top
                                    }

                                    CusRect {
                                        color: ST.color_grey_border
                                        height: 1
                                        width: parent.width
                                        anchors.bottom: parent.bottom
                                    }

                                    RowLayout {
                                        anchors.fill: parent
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent
                                            Text {
                                                id: second_goods_name_text
                                                anchors.left: parent.left
                                                anchors.leftMargin: 2 * dpi_ratio
                                                anchors.top: parent.top
                                                anchors.topMargin: 3 * dpi_ratio
                                                text: qsTr("惠")
                                                font.family: "微软雅黑"
                                                font.pointSize: 11 * dpi_ratio
                                                color: "#fa2b2b"
                                                visible: second_goods_name_text.visible
                                            }

                                            CusText {
                                                text: model.goods_name_role //"商品"
                                                x: 10 * dpi_ratio_second_screen
                                                anchors.verticalCenter: parent.verticalCenter
                                                color: ST.color_black
                                                width: (parent.width - 10 * dpi_ratio_second_screen)
                                                elide: Text.ElideMiddle
                                                font.pixelSize: 20 * dpi_ratio
                                            }
                                        }
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 85 * dpi_ratio_second_screen
                                            color: ST.color_transparent
                                            CusText {
                                                text: (model.goods_price_role) // "单价"
                                                x: 20 * dpi_ratio_second_screen
                                                anchors.verticalCenter: parent.verticalCenter
                                                color: ST.color_black
                                                font.pixelSize: 20 * dpi_ratio

                                                onTextChanged: {
                                                    var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(model.goods_barcode))
                                                    if ((cur_goods.goods_sale_price).toFixed(2) > Number(text)) {
                                                        second_goods_name_text.visible = true
                                                    } else {
                                                        second_goods_name_text.visible = false
                                                    }
                                                }
                                            }
                                        }
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 85 * dpi_ratio_second_screen
                                            color: ST.color_transparent
                                            CusText {
                                                text: model.goods_amount_role // + "斤" //"重量"
                                                x: 20 * dpi_ratio_second_screen
                                                anchors.verticalCenter: parent.verticalCenter
                                                color: ST.color_black
                                                font.pixelSize: 20 * dpi_ratio
                                            }
                                        }
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 85 * dpi_ratio_second_screen
                                            color: ST.color_transparent
                                            CusText {
                                                text: (model.goods_total_price_role) //"价格"
                                                x: 20 * dpi_ratio_second_screen
                                                anchors.verticalCenter: parent.verticalCenter
                                                color: ST.color_black
                                                font.pixelSize: 20 * dpi_ratio
                                            }
                                        }
                                    }
                                }

                                Image {
                                    id: img_pay_succ
                                    anchors.centerIn: parent
                                    source: "/Images/account_success.png"
                                    width: 300 * dpi_ratio_second_screen
                                    height: 100 * dpi_ratio_second_screen
                                    fillMode: Image.PreserveAspectFit
                                }
                            }
                        }
                    }

                    //总计
                    CusRect {
                        id: rect_shop_cart_total
                        Layout.fillWidth: true
                        Layout.preferredHeight: 125 * dpi_ratio_second_screen
                        color: ST.color_transparent

                        property real total_price: 0
                        property bool is_wipe_zero: false

                        function reFreshCartTotal() {
                            total_price = shopCartList.getFinalTotalGoodsPrice()
                        }

                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 120 * dpi_ratio_second_screen
                                color: ST.color_transparent
                                CusText {
                                    text: qsTr("总金额:")
                                    anchors.top: parent.top
                                    font.pixelSize: 33 * dpi_ratio_second_screen
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                CusText {
                                    text: rect_shop_cart_total.total_price.toFixed(2)
                                    anchors.top: parent.top
                                    font.pixelSize: 100 * dpi_ratio_second_screen
                                    font.bold: true
                                    font.family: ST.fontFamilyAharoni
                                    anchors.right: parent.right
                                }
                            }
                        }
                        ///-------------------------------------------| 当购物车变更后刷新统计 |-------------------------------------------
                        Connections {
                            target: shopCartList
                            function onPostItemReset() {
                                rect_shop_cart_total.reFreshCartTotal()
                            }
                            function onPostItemRemoved() {
                                rect_shop_cart_total.reFreshCartTotal()
                            }
                            function onPostItemAppended() {
                                rect_shop_cart_total.reFreshCartTotal()
                            }
                            function onPostRowChanged(row) {
                                rect_shop_cart_total.reFreshCartTotal()
                            }
                            function onRefreshOrderTotal() {
                                rect_shop_cart_total.reFreshCartTotal()
                            }
                            function onSigInitState() {
                                rect_shop_cart.state = "default"
                            }
                            function onSigPayFinished() {
                                rect_shop_cart.state = "pay_finish"
                            }
                        }
                        ///-------------------------------------------| 当购物车变更后刷新统计 |-------------------------------------------
                    }
                }
            }
        }
    }

    property alias loader_4_pay_status_second: loader_4_pay_status_second
    Loader {
        id: loader_4_pay_status_second
        z: 81
        anchors.fill: parent
    }

    property alias loader_4_face_pay_second: loader_4_face_pay_second
    Loader {
        id: loader_4_face_pay_second
        z: 81
        anchors.fill: parent
    }
}
