﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import "../Utils2/QChart2.js"        as Charts2
import "../Utils2/QChartGallery2.js" as ChartsData2
import "../Utils2/QChart3.js"        as Charts3
import "../Utils2"

CusRect {
    id: statistics_page
    width: 800
    height: 500
    color: ST.color_grey
    property int width_btn: 340
    property int height_btn: 70
    property string statisticsJinriyingyeeText: "0.0"
    property string statisticsJinriyingyeePersont: "0"
    property string statisticsMaolirun : "0"
    property string statisticsMaolirunpersont: "0"
    property string statisticsDingdanliang : "0"
    property string statisticsDingdanliangPersont: "0"
    property string statisticsKedanjia : "0"
    property string statisticsKedanjiaPersont: "0"
    property string statisticsWangdanliang : "0"
    property string statisticsWangdanliangPersont: "0"
    property string statisticsXiaoshoushujuzhouqizhanbiText5:"0.0"
    property string statisticsXiaoshoushujuzhouqizhanbiText10: "0.0"
    property string statisticsXiaoshoushujuzhouqizhanbiText6: "0.0"
    property string statisticsXiaoshoushujuzhouqizhanbiText11: "0.0"
    property string statisticsKehudengjifenbuText3: "0"
    property string maxDataStr: "0"
    property string maxDataStr1: "0"
    property string statisticsXiaoshoupinleizhanbiText5: ""
    property string statisticsXiaoshoupinleizhanbiText6: ""
    property string statisticsXiaoshoupinleizhanbiText7: ""
    property string statisticsXiaoshoupinleizhanbiText8: ""
    property string statisticsXiaoshoupinleizhanbiText9: ""
    property string statisticsXiaoshoupinleizhanbiTextTotal5: ""
    property string statisticsXiaoshoupinleizhanbiTextTotal6: ""
    property string statisticsXiaoshoupinleizhanbiTextTotal7: ""
    property string statisticsXiaoshoupinleizhanbiTextTotal8: ""
    property string statisticsXiaoshoupinleizhanbiTextTotal9: ""
    property double arrayValueTmp: 0
    property double arrayValueTmp2: 0
    property string statisticsZhifufenleiText5 : ""
    property string statisticsZhifufenleiText6 : ""
    property string statisticsZhifufenleiText7 : ""
    property string statisticsZhifufenleiWeixinText : ""
    property string statisticsZhifufenleiXianjinText : ""
    property string statisticsZhifufenleiTextTotal5 : ""
    property string statisticsZhifufenleiTextTotal6 : ""
    property string statisticsZhifufenleiTextTotal7 : ""
    property string statisticsZhifufenleiWeixinTextTotal : ""
    property string statisticsZhifufenleiXianjinTextTotal : ""

    function open(){
        //clearData();
        reqMainMessage();
        reqCycleRatioMessage("1");
        reqSaleTotalByHourMessage();
        reqsalesCategoryRatioMessage("1");
        reqpaymentTypeRatioMessage("1");
        reqsalesTrendMessage(checkData.getPreSomeDay(getCurDay(),7),getCurDay());
        reqoverviewOfStockMessage();
        reqtopGoodsSaleCountMessage("1","1", "5", "1");
        reqtopGoodsPorfitMessage("1","1", "5", "2");
        reqqueryUnmarketableTop5Message("1", "1", "5");
    }
    function add_zero(temp)
    {
        if(temp < 10)
        {
            return "0" + temp;
        }
        else
        {
            return temp;
        }
    }
    function getCurDay()
    {
        var d = new Date();
        var week;

        var years = d.getFullYear();
        var month = add_zero(d.getMonth()+1);
        var days = add_zero(d.getDate());
        var ndate = years+"-"+month+"-"+days;
        return ndate;
    }
    function processJsonPara(value) {
        if (value !== "") {
            return value;
        }
    }
    function clearData(){
        statisticsJinriyingyeeText = "0.0"
        statisticsJinriyingyeePersont = "0"
        statisticsMaolirun = "0"
        statisticsMaolirunpersont= "0"
        statisticsDingdanliang = "0"
        statisticsDingdanliangPersont= "0"
        statisticsKedanjia = "0"
        statisticsKedanjiaPersont= "0"
        statisticsWangdanliang = "0"
        statisticsWangdanliangPersont= "0"
        statisticsXiaoshoushujuzhouqizhanbiText5 = "0"
        statisticsXiaoshoushujuzhouqizhanbiText10 = "0"
        statisticsXiaoshoushujuzhouqizhanbiText6 ="0"
        statisticsXiaoshoushujuzhouqizhanbiText11 ="0"
        statisticsKehudengjifenbuText3 = "0"
        maxDataStr = "0"
        maxDataStr1 = "0"
        statisticsXiaoshoupinleizhanbiText5= ""
        statisticsXiaoshoupinleizhanbiText6= ""
        statisticsXiaoshoupinleizhanbiText7= ""
        statisticsXiaoshoupinleizhanbiText8= ""
        statisticsXiaoshoupinleizhanbiText9= ""
        statisticsXiaoshoupinleizhanbiTextTotal5= ""
        statisticsXiaoshoupinleizhanbiTextTotal6= ""
        statisticsXiaoshoupinleizhanbiTextTotal7= ""
        statisticsXiaoshoupinleizhanbiTextTotal8= ""
        statisticsXiaoshoupinleizhanbiTextTotal9= ""
        arrayValueTmp = 0
        arrayValueTmp2 = 0
        statisticsZhifufenleiText5 = ""
        statisticsZhifufenleiText6 = ""
        statisticsZhifufenleiText7 = ""
        statisticsZhifufenleiWeixinText = ""
        statisticsZhifufenleiXianjinText = ""
        statisticsZhifufenleiTextTotal5 = ""
        statisticsZhifufenleiTextTotal6 = ""
        statisticsZhifufenleiTextTotal7 = ""
        statisticsZhifufenleiWeixinTextTotal = ""
        statisticsZhifufenleiXianjinTextTotal = ""

    }
    //第一排横轴数据查询
    function reqMainMessage() {
        checkData.mainMessageForPC(function (data) {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            if (json_doc.status === 1) {
                //第一排
                statisticsJinriyingyeeText = (processJsonPara(json_doc_data.saleTotal) !== undefined)?processJsonPara(json_doc_data.saleTotal):statisticsJinriyingyeeText
                statisticsJinriyingyeePersont = (processJsonPara(json_doc_data.saleTotalRatio) !== undefined)?processJsonPara(json_doc_data.saleTotalRatio).toFixed(1):statisticsJinriyingyeePersont
                statistics_jinriyingyee_persont_text.text = (statisticsJinriyingyeePersont*1 > 0)?"▲":"▼"
                statisticsMaolirun = (processJsonPara(json_doc_data.grossProfit) !== undefined)?processJsonPara(json_doc_data.grossProfit).toFixed(1):statisticsMaolirun
                statisticsMaolirunpersont = (processJsonPara(json_doc_data.grossProfitRatio) !== undefined)?processJsonPara(json_doc_data.grossProfitRatio).toFixed(1):statisticsMaolirunpersont
                statistics_maolirun_persont_text.text = (statisticsMaolirunpersont*1 > 0)?"▲":"▼"
                statisticsDingdanliang = (processJsonPara(json_doc_data.listCount) !== undefined)?processJsonPara(json_doc_data.listCount).toFixed(1):statisticsDingdanliang
                statisticsDingdanliangPersont = (processJsonPara(json_doc_data.listCountRatio) !== undefined)?processJsonPara(json_doc_data.listCountRatio).toFixed(1):statisticsDingdanliangPersont
                statistics_dingdanliang_persont_text.text = (statisticsDingdanliangPersont*1 > 0)?"▲":"▼"
                statisticsKedanjia = (processJsonPara(json_doc_data.averPrice) !== undefined)?processJsonPara(json_doc_data.averPrice).toFixed(1):statisticsKedanjia
                statisticsKedanjiaPersont = (processJsonPara(json_doc_data.averPriceRatio) !== undefined)?processJsonPara(json_doc_data.averPriceRatio).toFixed(1):statisticsKedanjiaPersont
                statistics_kedanjia_persont_text.text = (statisticsKedanjiaPersont*1 > 0)?"▲":"▼"
                statisticsWangdanliang = (processJsonPara(json_doc_data.netListCount) !== undefined)?processJsonPara(json_doc_data.netListCount).toFixed(1):statisticsWangdanliang
                statisticsWangdanliangPersont = (processJsonPara(json_doc_data.netListCountRatio) !== undefined)?processJsonPara(json_doc_data.netListCountRatio).toFixed(1):statisticsWangdanliangPersont
                statistics_wangdanliang_persont_text.text = (statisticsWangdanliangPersont*1 > 0)?"▲":"▼"
                //会员等级分布
                statisticsKehudengjifenbuText3 = (processJsonPara(json_doc_data.cusSum) !== undefined)?processJsonPara(json_doc_data.cusSum):statisticsKehudengjifenbuText3
            }
        });
    }
    //销售品类占比
    function reqsalesCategoryRatioMessage(type) {
        checkData.salesCategoryRatio(function (data)
        {
            var colorArrary  = new Array;
            var array1 = new Array;
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            colorArrary[0] = "#2cb7f5";
            colorArrary[1] = "#818bc7";
            colorArrary[2] = "#7cc956";
            colorArrary[3] = "#5b6877";
            colorArrary[4] = "#f9c002";
            if (json_doc.status === 1) {
                if (Array.isArray(json_doc_data))
                {
                    var nSize = json_doc_data.length;
                    for (var i = 0; i < nSize; ++i)
                    {
                        if (processJsonPara(json_doc_data[i].kindName) !== undefined)
                        {
                            switch (i)
                            {
                                case 0:
                                    statisticsXiaoshoupinleizhanbiText5 = json_doc_data[i].kindName;
                                    break;
                                case 1:
                                    statisticsXiaoshoupinleizhanbiText6 = json_doc_data[i].kindName;
                                    break;
                                case 2:
                                    statisticsXiaoshoupinleizhanbiText7 = json_doc_data[i].kindName;
                                    break;
                                case 3:
                                    statisticsXiaoshoupinleizhanbiText8 = json_doc_data[i].kindName;
                                    break;
                                case 4:
                                    statisticsXiaoshoupinleizhanbiText9 = json_doc_data[i].kindName;
                                    break;
                            }
                        }
                        if (processJsonPara(json_doc_data[i].saleTotal) !== undefined)
                        {
                            switch (i)
                            {
                                case 0:
                                    statisticsXiaoshoupinleizhanbiTextTotal5 = json_doc_data[i].saleTotal.toFixed(0);
                                    break;
                                case 1:
                                    statisticsXiaoshoupinleizhanbiTextTotal6 = json_doc_data[i].saleTotal.toFixed(0);
                                    break;
                                case 2:
                                    statisticsXiaoshoupinleizhanbiTextTotal7 = json_doc_data[i].saleTotal.toFixed(0);
                                    break;
                                case 3:
                                    statisticsXiaoshoupinleizhanbiTextTotal8 = json_doc_data[i].saleTotal.toFixed(0);
                                    break;
                                case 4:
                                    statisticsXiaoshoupinleizhanbiTextTotal9 = json_doc_data[i].saleTotal.toFixed(0);
                                    break;
                            }
                        }

                    }
                    for (var i = 0; i < nSize; ++i)
                    {
                        if (i < 5)
                        {
                            arrayValueTmp = json_doc_data[i].saleTotal*1;
                            array1[i] = [arrayValueTmp, colorArrary[i]];
                        }
                    }
                    chart_pie2.chartData = array1;
                    chart_pie2.repaint();
                }
            }
        },type);
    }
    function timeStampToString(ts) {
        var date = new Date(ts);
        var year = date.getUTCFullYear();
        var month = String(date.getUTCMonth() + 1).padStart(2, '0');
        var day = String(date.getUTCDate()).padStart(2, '0');
        var hours = String(date.getUTCHours()).padStart(2, '0');
        var minutes = String(date.getUTCMinutes()).padStart(2, '0');
        var seconds = String(date.getUTCSeconds()).padStart(2, '0');
        //return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        return `${month}.${day}`;
    }
    function timeStampToYearString(ts) {
        var date = new Date(ts);
        var year = date.getUTCFullYear();
        var month = String(date.getUTCMonth() + 1).padStart(2, '0');
        var day = String(date.getUTCDate()).padStart(2, '0');
        var hours = String(date.getUTCHours()).padStart(2, '0');
        var minutes = String(date.getUTCMinutes()).padStart(2, '0');
        var seconds = String(date.getUTCSeconds()).padStart(2, '0');
        //return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        return `${year}-${month}`;
    }
    function addStringIfNotExists(value: string) {
           if (labelsArray.indexOf(value) === -1) {
               myArray.push(value);
           } else {
           }
       }
    //销售额走势图
    function reqsalesTrendMessage(startTime, endTime) {
        checkData.salesTrend(function (data)
        {
            var str1;
            var dataArray2Sum;
            var dataArraySum;
            var labelsArray = new Array;
            var datasetsArray = new Array;
            var dataArray = new Array;
            var dataArray2 = new Array;

            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            if (json_doc.status === 1) {
                if (Array.isArray(json_doc_data))
                {
                    var nSize = json_doc_data.length;
                    //logMgr.logDataInfo4Qml("nSize:{}",nSize);
                    if(nSize > 31){
                        for (var i = 0; i < nSize; ++i)
                        {
                            if (processJsonPara(json_doc_data[i].datelist) !== undefined)
                            {

                                if (labelsArray.indexOf(timeStampToYearString(json_doc_data[i].datelist)) === -1) {
                                    dataArraySum = 0;
                                    dataArray2Sum = 0;
                                    labelsArray.push(timeStampToYearString(json_doc_data[i].datelist));
                                    if (processJsonPara(json_doc_data[i].saleTotal) !== undefined)
                                    {
                                        dataArraySum += (json_doc_data[i].saleTotal).toFixed(0);
                                        dataArray[labelsArray.length -1] = dataArraySum;
                                        maxDataStr1 = (dataArray[labelsArray.length -1]*1 > maxDataStr1*1)?dataArray[labelsArray.length -1]:maxDataStr1
                                    }
                                    if (processJsonPara(json_doc_data[i].listCount) !== undefined)
                                    {
                                        dataArray2Sum += (json_doc_data[i].listCount).toFixed(0);
                                        dataArray2[labelsArray.length -1] = dataArray2Sum;
                                        maxDataStr1 = (dataArray2[labelsArray.length -1]*1 > maxDataStr1*1)?dataArray2[labelsArray.length -1]:maxDataStr1

                                    }

                                }else{
                                    if (processJsonPara(json_doc_data[i].saleTotal) !== undefined)
                                    {
                                        dataArraySum += (json_doc_data[i].saleTotal).toFixed(0);
                                        dataArray[labelsArray.length -1] = dataArraySum;
                                        maxDataStr1 = (dataArray[labelsArray.length -1]*1 > maxDataStr1*1)?dataArray[labelsArray.length -1]:maxDataStr1
                                    }
                                    if (processJsonPara(json_doc_data[i].listCount) !== undefined)
                                    {
                                        dataArray2Sum += (json_doc_data[i].listCount).toFixed(0);
                                        dataArray2[labelsArray.length -1] = dataArray2Sum;
                                        maxDataStr1 = (dataArray2[labelsArray.length -1]*1 > maxDataStr1*1)?dataArray2[labelsArray.length -1]:maxDataStr1
                                    }
                                }

                            }
                            dataArraySum = 0;
                            dataArray2Sum = 0;
                        }
                        datasetsArray[0] = {
                            fillColor: "rgba(254,51,154,0)",
                            strokeColor: "rgba(44,183,245,1)",
                            pointColor: "rgba(254,51,154,0)",
                            pointStrokeColor: "#2cb7f5",
                            scaleMaxValue: maxDataStr1,
                            data: dataArray};
                        datasetsArray[1] = {
                            fillColor: "rgba(254,51,154,0)",
                            strokeColor: "rgba(129,139,199,1)",
                            pointColor: "rgba(254,51,154,0)",
                            pointStrokeColor: "#818BC7",
                            data: dataArray2};
                        str1 = {labels: labelsArray, datasets: datasetsArray};
                        chart_line6.chartData = str1;
                        chart_line6.repaint();
                    }
                    else{
                        for (var i = 0; i < nSize; ++i)
                        {
                            if (processJsonPara(json_doc_data[i].datelist) !== undefined)
                            {
//                                logMgr.logDataInfo4Qml("json_doc_data[:{}].datelist:{}",i,timeStampToString(json_doc_data[i].datelist));
                                labelsArray[i] = timeStampToString(json_doc_data[i].datelist);

                            }
                            if (processJsonPara(json_doc_data[i].saleTotal) !== undefined)
                            {
//                                logMgr.logDataInfo4Qml("json_doc_data[:{}].saleTotal:{}",i,json_doc_data[i].saleTotal);
                                dataArray[i] = (json_doc_data[i].saleTotal).toFixed(0);
                                maxDataStr1 = (dataArray[i]*1 > maxDataStr1*1)?dataArray[i]:maxDataStr1
                            }
                            if (processJsonPara(json_doc_data[i].listCount) !== undefined)
                            {

                                dataArray2[i] = (json_doc_data[i].listCount).toFixed(0);
//                                logMgr.logDataInfo4Qml("dataArray2[:{}].listCount:{}",i,dataArray2);
                                maxDataStr1 = (dataArray2[i]*1 > maxDataStr1*1)?dataArray2[i]:maxDataStr1
                            }
                        }
                        datasetsArray[0] = {
                            fillColor: "rgba(254,51,154,0)",
                            strokeColor: "rgba(44,183,245,1)",
                            pointColor: "rgba(254,51,154,0)",
                            pointStrokeColor: "#2cb7f5",
                            scaleMaxValue: maxDataStr1,
                            data: dataArray};
                        datasetsArray[1] = {
                            fillColor: "rgba(254,51,154,0)",
                            strokeColor: "rgba(129,139,199,1)",
                            pointColor: "rgba(254,51,154,0)",
                            pointStrokeColor: "#818BC7",
                            data: dataArray2};
                        str1 = {labels: labelsArray, datasets: datasetsArray};
                        chart_line6.chartData = str1;
                        chart_line6.repaint();
                        }
                }
            }
        },startTime, endTime);
    }
    //支付分类占比
    function reqpaymentTypeRatioMessage(type) {
        checkData.paymentTypeRatio(function (data)
        {
            var colorArrary  = new Array;
            var array2 = new Array;
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            colorArrary[0] = "#2cb7f5";
            colorArrary[1] = "#818bc7";
            colorArrary[2] = "#7cc956";
            colorArrary[3] = "#5b6877";
            colorArrary[4] = "#f9c002";
            if (json_doc.status === 1) {
                if (Array.isArray(json_doc_data))
                {
                    var nSize = json_doc_data.length;
                    for (var i = 0; i < nSize; ++i)
                    {
                        if (processJsonPara(json_doc_data[i].payment) !== undefined)
                        {
                            switch (i)
                            {
                                case 0:
                                    statisticsZhifufenleiText5 = json_doc_data[i].payment;
                                    break;
                                case 1:
                                    statisticsZhifufenleiText6 = json_doc_data[i].payment;
                                    break;
                                case 2:
                                    statisticsZhifufenleiText7 = json_doc_data[i].payment;
                                    break;
                                case 3:
                                    statisticsZhifufenleiWeixinText = json_doc_data[i].payment;
                                    break;
                                case 4:
                                    statisticsZhifufenleiXianjinText = json_doc_data[i].payment;
                                    break;
                            }
                        }
                        if (processJsonPara(json_doc_data[i].saleTotal) !== undefined)
                        {
                            switch (i)
                            {
                                case 0:
                                    statisticsZhifufenleiTextTotal5 = json_doc_data[i].saleTotal.toFixed(2);
                                    break;
                                case 1:
                                    statisticsZhifufenleiTextTotal6 = json_doc_data[i].saleTotal.toFixed(2);
                                    break;
                                case 2:
                                    statisticsZhifufenleiTextTotal7 = json_doc_data[i].saleTotal.toFixed(2);
                                    break;
                                case 3:
                                    statisticsZhifufenleiWeixinTextTotal = json_doc_data[i].saleTotal.toFixed(2);
                                    break;
                                case 4:
                                    statisticsZhifufenleiXianjinTextTotal = json_doc_data[i].saleTotal.toFixed(2);
                                    break;
                            }
                        }
                    }
                    for (var i = 0; i < nSize; ++i)
                    {
                        if (i < 5)
                        {
                            arrayValueTmp2 = json_doc_data[i].saleTotal*1;
                            array2[i] = {value:arrayValueTmp2, color: colorArrary[i]};
                        }
                    }
                    chart_doughnut2.chartData = array2;
                    chart_doughnut2.repaint();
                }
            }
        },type);
    }
    //销售数据周期占比
    function reqCycleRatioMessage(type) {
        checkData.saleStatisticsCycleRatio(function (data)
        {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            if (json_doc.status === 1) {
                if (processJsonPara(json_doc_data.saleTotalRadio)*1 > 100)
                {
                    dial.value = 100;
                }
                else if (processJsonPara(json_doc_data.saleTotalRadio)*1 < 0)
                {
                    dial.value = 0;
                }
                else
                {
                    dial.value = processJsonPara(json_doc_data.saleTotalRadio);
                }
                statisticsXiaoshoushujuzhouqizhanbiText5 = (processJsonPara(json_doc_data.saleTotalRadio) !== undefined)?processJsonPara(json_doc_data.saleTotalRadio).toFixed(1):statisticsXiaoshoushujuzhouqizhanbiText5
                statisticsXiaoshoushujuzhouqizhanbiText6 = (processJsonPara(json_doc_data.grossProfitRadio) !== undefined)?processJsonPara(json_doc_data.grossProfitRadio).toFixed(1):statisticsXiaoshoushujuzhouqizhanbiText6
                statisticsXiaoshoushujuzhouqizhanbiText10 = (processJsonPara(json_doc_data.netListCountRadio) !== undefined)?processJsonPara(json_doc_data.netListCountRadio).toFixed(1):statisticsXiaoshoushujuzhouqizhanbiText10
                statisticsXiaoshoushujuzhouqizhanbiText11 = (processJsonPara(json_doc_data.listCountRadio) !== undefined)?processJsonPara(json_doc_data.listCountRadio).toFixed(1):statisticsXiaoshoushujuzhouqizhanbiText11

            }
        },type);
    }
    //热销商品TOP5
    function reqtopGoodsSaleCountMessage(dateType,standard,pageSize,order) {
        checkData.topGoodsSaleCount(function (data)
        {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            if (json_doc.status === 1) {
                if (Array.isArray(json_doc_data))
                {
                    var nSize = json_doc_data.length;
                    for (var i = 0; i < nSize; ++i)
                    {
                        if (i == 0)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_rexiaoshangpinTops_sale_ranking_rec1.rank_num = i+1;
                                statistics_rexiaoshangpinTops_sale_ranking_rec1.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_rexiaoshangpinTops_sale_ranking_rec1.goods_name = json_doc_data[i].goodsName;
                                statistics_rexiaoshangpinTops_sale_ranking_rec1.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].percentage*1 > 0)
                                {
                                    statistics_rexiaoshangpinTops_sale_ranking_rec1.goods_zhouhuanbi = json_doc_data[i].percentage+"%▲";
                                    statistics_rexiaoshangpinTops_sale_ranking_rec1.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_rexiaoshangpinTops_sale_ranking_rec1.goods_zhouhuanbi = (json_doc_data[i].percentage*(-100)).toFixed(0)+"%▼";
                                    statistics_rexiaoshangpinTops_sale_ranking_rec1.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 1)
                        {
                            if (Object.keys(json_doc_data[i]).length>= 4)
                            {
                                statistics_rexiaoshangpinTops_sale_ranking_rec2.rank_num = i+1;
                                statistics_rexiaoshangpinTops_sale_ranking_rec2.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_rexiaoshangpinTops_sale_ranking_rec2.goods_name = json_doc_data[i].goodsName;
                                statistics_rexiaoshangpinTops_sale_ranking_rec2.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].percentage*1 > 0)
                                {
                                    statistics_rexiaoshangpinTops_sale_ranking_rec2.goods_zhouhuanbi = json_doc_data[i].percentage+"%▲";
                                    statistics_rexiaoshangpinTops_sale_ranking_rec2.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_rexiaoshangpinTops_sale_ranking_rec2.goods_zhouhuanbi = (json_doc_data[i].percentage*(-100)).toFixed(0)+"%▼";
                                    statistics_rexiaoshangpinTops_sale_ranking_rec2.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 2)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_rexiaoshangpinTops_sale_ranking_rec3.rank_num = i+1;
                                statistics_rexiaoshangpinTops_sale_ranking_rec3.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_rexiaoshangpinTops_sale_ranking_rec3.goods_name = json_doc_data[i].goodsName;
                                statistics_rexiaoshangpinTops_sale_ranking_rec3.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].percentage*1 > 0)
                                {
                                    statistics_rexiaoshangpinTops_sale_ranking_rec3.goods_zhouhuanbi = json_doc_data[i].percentage+"%▲";
                                    statistics_rexiaoshangpinTops_sale_ranking_rec3.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_rexiaoshangpinTops_sale_ranking_rec3.goods_zhouhuanbi = (json_doc_data[i].percentage*(-100)).toFixed(0)+"%▼";
                                    statistics_rexiaoshangpinTops_sale_ranking_rec3.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 3)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_rexiaoshangpinTops_sale_ranking_rec4.rank_num = i+1;
                                statistics_rexiaoshangpinTops_sale_ranking_rec4.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_rexiaoshangpinTops_sale_ranking_rec4.goods_name = json_doc_data[i].goodsName;
                                statistics_rexiaoshangpinTops_sale_ranking_rec4.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].percentage*1 > 0)
                                {
                                    statistics_rexiaoshangpinTops_sale_ranking_rec4.goods_zhouhuanbi = json_doc_data[i].percentage+"%▲";
                                    statistics_rexiaoshangpinTops_sale_ranking_rec4.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_rexiaoshangpinTops_sale_ranking_rec4.goods_zhouhuanbi = (json_doc_data[i].percentage*(-100)).toFixed(0)+"%▼";
                                    statistics_rexiaoshangpinTops_sale_ranking_rec4.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 4)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_rexiaoshangpinTops_sale_ranking_rec5.rank_num = i+1;
                                statistics_rexiaoshangpinTops_sale_ranking_rec5.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_rexiaoshangpinTops_sale_ranking_rec5.goods_name = json_doc_data[i].goodsName;
                                statistics_rexiaoshangpinTops_sale_ranking_rec5.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].percentage*1 > 0)
                                {
                                    statistics_rexiaoshangpinTops_sale_ranking_rec5.goods_zhouhuanbi = json_doc_data[i].percentage+"%▲";
                                    statistics_rexiaoshangpinTops_sale_ranking_rec5.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_rexiaoshangpinTops_sale_ranking_rec5.goods_zhouhuanbi = (json_doc_data[i].percentage*(-100)).toFixed(0)+"%▼";
                                    statistics_rexiaoshangpinTops_sale_ranking_rec5.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                    }
                }

            }
        },dateType,standard,pageSize,order);
    }

    //滞销商品TOP5
    function reqqueryUnmarketableTop5Message(dateType,standard,pageSize) {
        checkData.queryUnmarketableTop5(function (data)
        {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            if (json_doc.status === 1) {
                if (Array.isArray(json_doc_data))
                {
                    var nSize = json_doc_data.length;
                    for (var i = 0; i < nSize; ++i)
                    {
                        if (i == 0)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_zhixiaoshangpinTops_sale_ranking_rec1.rank_num = i+1;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec1.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec1.goods_name = json_doc_data[i].goodsName;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec1.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].porfitPercentage*1 > 0)
                                {
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec1.goods_zhouhuanbi = json_doc_data[i].porfitPercentage+"%▲";
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec1.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec1.goods_zhouhuanbi = (json_doc_data[i].porfitPercentage*(-100)).toFixed(0)+"%▼";
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec1.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 1)
                        {
                            if (Object.keys(json_doc_data[i]).length>= 4)
                            {
                                statistics_zhixiaoshangpinTops_sale_ranking_rec2.rank_num = i+1;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec2.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec2.goods_name = json_doc_data[i].goodsName;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec2.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].porfitPercentage*1 > 0)
                                {
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec2.goods_zhouhuanbi = json_doc_data[i].porfitPercentage+"%▲";
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec2.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec2.goods_zhouhuanbi = (json_doc_data[i].porfitPercentage*(-100)).toFixed(0)+"%▼";
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec2.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 2)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_zhixiaoshangpinTops_sale_ranking_rec3.rank_num = i+1;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec3.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec3.goods_name = json_doc_data[i].goodsName;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec3.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].porfitPercentage*1 > 0)
                                {
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec3.goods_zhouhuanbi = json_doc_data[i].porfitPercentage+"%▲";
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec3.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec3.goods_zhouhuanbi = (json_doc_data[i].porfitPercentage*(-100)).toFixed(0)+"%▼";
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec3.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 3)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_zhixiaoshangpinTops_sale_ranking_rec4.rank_num = i+1;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec4.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec4.goods_name = json_doc_data[i].goodsName;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec4.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].porfitPercentage*1 > 0)
                                {
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec4.goods_zhouhuanbi = json_doc_data[i].porfitPercentage+"%▲";
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec4.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec4.goods_zhouhuanbi = (json_doc_data[i].porfitPercentage*(-100)).toFixed(0)+"%▼";
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec4.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 4)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_zhixiaoshangpinTops_sale_ranking_rec5.rank_num = i+1;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec5.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec5.goods_name = json_doc_data[i].goodsName;
                                statistics_zhixiaoshangpinTops_sale_ranking_rec5.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].porfitPercentage*1 > 0)
                                {
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec5.goods_zhouhuanbi = json_doc_data[i].porfitPercentage+"%▲";
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec5.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec5.goods_zhouhuanbi = (json_doc_data[i].porfitPercentage*(-100)).toFixed(0)+"%▼";
                                    statistics_zhixiaoshangpinTops_sale_ranking_rec5.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                    }
                }

            }
        },dateType,standard,pageSize);
    }
    //累计利润商品TOP5
    function reqtopGoodsPorfitMessage(dateType,standard,pageSize,order) {
        checkData.topGoodsPorfit(function (data)
        {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            if (json_doc.status === 1) {
                if (Array.isArray(json_doc_data))
                {
                    var nSize = json_doc_data.length;
                    for (var i = 0; i < nSize; ++i)
                    {
                        if (i == 0)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_lijilirunshanggpinTops_sale_ranking_rec1.rank_num = i+1;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec1.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec1.goods_name = json_doc_data[i].goodsName;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec1.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].percentage*1 > 0)
                                {
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec1.goods_zhouhuanbi = json_doc_data[i].percentage+"%▲";
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec1.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec1.goods_zhouhuanbi = (json_doc_data[i].percentage*(-100)).toFixed(0)+"%▼";
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec1.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 1)
                        {
                            if (Object.keys(json_doc_data[i]).length>= 4)
                            {
                                statistics_lijilirunshanggpinTops_sale_ranking_rec2.rank_num = i+1;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec2.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec2.goods_name = json_doc_data[i].goodsName;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec2.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].percentage*1 > 0)
                                {
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec2.goods_zhouhuanbi = json_doc_data[i].percentage+"%▲";
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec2.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec2.goods_zhouhuanbi = (json_doc_data[i].percentage*(-100)).toFixed(0)+"%▼";
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec2.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 2)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_lijilirunshanggpinTops_sale_ranking_rec3.rank_num = i+1;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec3.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec3.goods_name = json_doc_data[i].goodsName;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec3.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].percentage*1 > 0)
                                {
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec3.goods_zhouhuanbi = json_doc_data[i].percentage+"%▲";
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec3.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec3.goods_zhouhuanbi = (json_doc_data[i].percentage*(-100)).toFixed(0)+"%▼";
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec3.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 3)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_lijilirunshanggpinTops_sale_ranking_rec4.rank_num = i+1;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec4.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec4.goods_name = json_doc_data[i].goodsName;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec4.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].percentage*1 > 0)
                                {
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec4.goods_zhouhuanbi = json_doc_data[i].percentage+"%▲";
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec4.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec4.goods_zhouhuanbi = (json_doc_data[i].percentage*(-100)).toFixed(0)+"%▼";
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec4.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                        else if (i == 4)
                        {
                            if (Object.keys(json_doc_data[i]).length >= 4)
                            {
                                statistics_lijilirunshanggpinTops_sale_ranking_rec5.rank_num = i+1;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec5.goods_image = json_doc_data[i].goodsPicturePath;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec5.goods_name = json_doc_data[i].goodsName;
                                statistics_lijilirunshanggpinTops_sale_ranking_rec5.goods_sale_count = json_doc_data[i].saleCount.toFixed(2);
                                if (json_doc_data[i].percentage*1 > 0)
                                {
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec5.goods_zhouhuanbi = json_doc_data[i].percentage+"%▲";
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec5.goods_zhouhuanbi_color = "#fe339a";
                                }
                                else
                                {
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec5.goods_zhouhuanbi = (json_doc_data[i].percentage*(-100)).toFixed(0)+"%▼";
                                    statistics_lijilirunshanggpinTops_sale_ranking_rec5.goods_zhouhuanbi_color = "#00cd66";
                                }
                            }
                        }
                    }
                }

            }
        },dateType,standard,pageSize,order);
    }
    //库存指标概览
    function reqoverviewOfStockMessage() {
        checkData.overviewOfStock(function (data)
        {
            //console.debug("onSendOverviewOfStock",data,data2,data3,data4)
            var str1;
            var labelsArray = new Array;
            var datasetsArray = new Array;
            var dataArray = new Array;
            var arrayTmp = data.split("^");
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            var str3;
            var datasetsArray3 = new Array;
            var dataArray3 = new Array;
            var str4;
            var datasetsArray4 = new Array;
            var dataArray4 = new Array;

            if (json_doc.status === 1) {
                if (Array.isArray(json_doc_data))
                {
                    var nSize = json_doc_data.length;
                    for (var i = 0; i < nSize; ++i)
                    {
                        labelsArray[i] = timeStampToString(json_doc_data[i].datelist);
                        dataArray[i] = json_doc_data[i].stockTotal;
                        dataArray3[i] = json_doc_data[i].goodsCount;
                        dataArray4[i] = json_doc_data[i].saleCount;
                    }
                    statistics_kuncunzhibiaogailan_text2.text = json_doc_data[nSize -1].stockTotal.toFixed(2);
                    if ((json_doc_data[nSize -1].stockTotal-json_doc_data[nSize -2].stockTotal) > 0)
                    {
                        if(json_doc_data[nSize -2].stockTotal*1 == 0)
                        {
                            statistics_kuncunzhibiaogailan_text3.text = "▲" + ((json_doc_data[nSize -1].stockTotal-json_doc_data[nSize -2].stockTotal)/1*100).toFixed(2) + "%";
                        }
                        else
                        {
                            statistics_kuncunzhibiaogailan_text3.text = "▲" + ((json_doc_data[nSize -1].stockTotal-json_doc_data[nSize -2].stockTotal)/json_doc_data[nSize -2].stockTotal*100).toFixed(2) + "%";
                        }
                        statistics_kuncunzhibiaogailan_text3.color = "#39af23";
                    }
                    else
                    {
                        if(json_doc_data[nSize -2].stockTotal*1 == 0)
                        {
                            statistics_kuncunzhibiaogailan_text3.text = "▼" + (-(json_doc_data[nSize -1].stockTotal-json_doc_data[nSize -2].stockTotal)/1*100).toFixed(2) + "%";
                        }
                        else
                        {
                            statistics_kuncunzhibiaogailan_text3.text = "▼" + (-(json_doc_data[nSize -1].stockTotal-json_doc_data[nSize -2].stockTotal)/json_doc_data[nSize -2].stockTotal*100).toFixed(2) + "%";
                        }
                        statistics_kuncunzhibiaogailan_text3.color = "#d45134";
                    }

                    datasetsArray[0] = {
                        fillColor: "rgba(205,245,225,1)",
                        strokeColor: "rgba(44,183,245,1)",
                        pointColor: "rgba(44,183,245,0)",
                        pointStrokeColor: "#2cb7f5",
                        data: dataArray};
                    str1 = {labels: labelsArray, datasets: datasetsArray};





                    statistics_shangpinzongshu_text2.text = json_doc_data[nSize -1].goodsCount;
                    if ((json_doc_data[nSize -1].goodsCount - json_doc_data[nSize -2].goodsCount) > 0)
                    {
                        if(json_doc_data[nSize -2].goodsCount*1 == 0)
                        {
                             statistics_shangpinzongshu_text3.text = "▲" + ((json_doc_data[nSize -1].goodsCount - json_doc_data[nSize -2].goodsCount)/1*100).toFixed(2) + "%";
                        }
                        else
                        {
                            statistics_shangpinzongshu_text3.text = "▲" + ((json_doc_data[nSize -1].goodsCount - json_doc_data[nSize -2].goodsCount)/json_doc_data[nSize -2].goodsCount*100).toFixed(2) + "%";
                        }
                        statistics_shangpinzongshu_text3.color = "#39af23";
                    }
                    else
                    {
                        if(json_doc_data[nSize -2].goodsCount*1 == 0)
                        {
                            statistics_shangpinzongshu_text3.text = "▼" + (-(json_doc_data[nSize -1].goodsCount - json_doc_data[nSize -2].goodsCount)/1*100).toFixed(2) + "%";
                        }
                        else
                        {
                            statistics_shangpinzongshu_text3.text = "▼" + (-(json_doc_data[nSize -1].goodsCount-json_doc_data[nSize -2].goodsCount)/json_doc_data[nSize -2].goodsCount*100).toFixed(2) + "%";
                        }
                        statistics_shangpinzongshu_text3.color = "#d45134";
                    }

                    datasetsArray3[0] = {
                        fillColor: "rgba(205,245,225,1)",
                        strokeColor: "rgba(142,153,206,1)",
                        pointColor: "rgba(142,153,206,0)",
                        pointStrokeColor: "#8e99ce",
                        data: dataArray3};
                    str3 = {labels: labelsArray, datasets: datasetsArray3};




                    statistics_kucunzhouzhuantianshu_text2.text = json_doc_data[nSize -1].saleCount;
                    if ((json_doc_data[nSize -1].saleCount-json_doc_data[nSize -2].saleCount) > 0)
                    {
                        if(json_doc_data[nSize -2].saleCount*1 == 0)
                        {
                            statistics_kucunzhouzhuantianshu_text3.text = "▲" + ((json_doc_data[nSize -1].saleCount-json_doc_data[nSize -2].saleCount)/1*100).toFixed(2) + "%";
                        }
                        else
                        {
                            statistics_kucunzhouzhuantianshu_text3.text = "▲" + ((json_doc_data[nSize -1].saleCount-json_doc_data[nSize -2].saleCount)/json_doc_data[nSize -2].saleCount*100).toFixed(2) + "%";
                        }
                        statistics_kucunzhouzhuantianshu_text3.color = "#39af23";
                    }
                    else
                    {
                        if(json_doc_data[nSize -2].saleCount*1 == 0)
                        {
                            statistics_kucunzhouzhuantianshu_text3.text = "▼" + (-(json_doc_data[nSize -1].saleCount-json_doc_data[nSize -2].saleCount)/1*100).toFixed(2) + "%";
                        }
                        else
                        {
                            statistics_kucunzhouzhuantianshu_text3.text = "▼" + (-(json_doc_data[nSize -1].saleCount-json_doc_data[nSize -2].saleCount)/json_doc_data[nSize -2].saleCount*100).toFixed(2) + "%";
                        }
                        statistics_kucunzhouzhuantianshu_text3.color = "#d45134";
                    }

                    datasetsArray4[0] = {
                        fillColor: "rgba(205,245,225,1)",
                        strokeColor: "rgba(148,211,117,1)",
                        pointColor: "rgba(148,211,117,0)",
                        pointStrokeColor: "#94d375",
                        data: dataArray4};
                    str4 = {labels: labelsArray, datasets: datasetsArray4};

                    chart_line3.chartData = str1;
                    chart_line3.repaint();
                    chart_line4.chartData = str3;
                    chart_line4.repaint();
                    chart_line5.chartData = str4;
                    chart_line5.repaint();

                }
            }
        });
    }
    //营业额24H分布图
    function reqSaleTotalByHourMessage() {
        checkData.querySaleTotalByHour(function (data)
        {
            var str1;
            var str2;
            var labelsArray = new Array;
            var labelsArray2 = new Array;
            var datasetsArray = new Array;
            var datasetsArray2 = new Array;
            var data1Array = new Array;
            var data2Array = new Array;

            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            if (json_doc.status === 1) {
                if (Array.isArray(json_doc_data))
                {
                    var nSize = json_doc_data.length;
                    for (var i = 0; i < nSize; ++i)
                    {
                        if (processJsonPara(json_doc_data[i].saleTotal) !== undefined)
                        {
                            data1Array[i] = json_doc_data[i].saleTotal;
                            maxDataStr = (data1Array[i]*1 > maxDataStr*1)?data1Array[i]:maxDataStr
                        }
                        else
                        {
                            data1Array[i] = "0";
                        }

                        if (processJsonPara(json_doc_data[i].yesSaleTotal) !== undefined)
                        {
                            data2Array[i] = json_doc_data[i].yesSaleTotal;
                            maxDataStr = (data2Array[i]*1 > maxDataStr*1)?data2Array[i]:maxDataStr
                        }
                        else
                        {
                            data2Array[i] = "0";
                        }
                    }
                }
            }
            for (var i = 0; i < 24; i++)
            {
                labelsArray[i] = i+"";
            }

            for (var i = 0; i < 24; i++)
            {
                labelsArray2[i] = i+"";
            }
            for (var i = 0; i < data1Array.length; i++)
            {
                //logMgr.logDataInfo4Qml(":{};",data1Array[i])
            }
            datasetsArray[0] = {
                fillColor: "rgba(44,183,245,1)",
                strokeColor: "rgba(44,183,245,1)",
                scaleMaxValue: maxDataStr,
                data: data1Array};
            str1 = {labels: labelsArray, datasets: datasetsArray};

            datasetsArray2[0] = {
                fillColor: "rgba(254,51,154,0)",
                strokeColor: "rgba(254,51,154,1)",
                pointColor: "rgba(254,51,154,1)",
                pointStrokeColor: "#2cb7f5",
                scaleMaxValue: maxDataStr,
                data: data2Array};
            str2 = {labels: labelsArray2, datasets: datasetsArray2};

            chart_bar3.chartData = str1;
            chart_line.chartData = str2;
            chart_bar3.repaint();
            chart_line.repaint();
        });
    }
    Component.onCompleted: {
    }
    Image {
        anchors.fill: parent
        source: "/Images/mainPageBackground.jpg"
    }

    CusText {
        text: qsTr("统计功能正在更新中...")
        font.pixelSize: 30 * dpi_ratio
        anchors.centerIn: parent
        color: ST.color_white_pure
    }

        ColumnLayout {
            anchors.fill: parent
            spacing: ST.margin
            anchors.margins: ST.margin

            CusRect {
                id: top_side
                Layout.fillWidth: true
                Layout.preferredHeight: 90

                RowLayout {
                    anchors.fill: parent
                    spacing: ST.margin
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#2cb7f5";
                        Rectangle
                        {
                            id: statistics_jinriyingyee_image_bck;
                            width: 50* dpi_ratio;
                            height: 50* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 5* dpi_ratio*1.3;////////
                            anchors.verticalCenter: parent.verticalCenter;
                            color: "#333333";
                            opacity: 0.2;
                            radius: 50* dpi_ratio;
                        }
                        Image
                        {
                            id: statistics_jinriyingyee_image;
                            width: 32* dpi_ratio;
                            height :32* dpi_ratio;
                            anchors.centerIn: statistics_jinriyingyee_image_bck;
                            source: "/Images/rili_image.png";
                        }

                        Text
                        {
                            id: statistics_jinriyingyee_text;
                            anchors.left: statistics_jinriyingyee_image.right;
                            anchors.leftMargin: 10* dpi_ratio*1.3;/////////
                            anchors.top: statistics_jinriyingyee_image.top;
                            anchors.topMargin: -5* dpi_ratio;
                            text: qsTr("今日营业额（元）");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                        Text
                        {
                            id: statistics_jinriyingyee;
                            anchors.left: statistics_jinriyingyee_text.left;
                //            anchors.leftMargin: 1* dpi_ratio*1.3;
                            anchors.top: statistics_jinriyingyee_text.bottom;
                            anchors.topMargin: 8* dpi_ratio;
                            text: statisticsJinriyingyeeText;
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#ffffff";
                        }

                        Text
                        {
                            id: statistics_jinriyingyee_persont;
                            anchors.right: parent.right;//////////
                            anchors.rightMargin: 5* dpi_ratio*1.3;////////
                            anchors.top: statistics_jinriyingyee_text.top;
                //            anchors.topMargin: 8* dpi_ratio;
                            text: statisticsJinriyingyeePersont + "%";
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }

                        Text
                        {
                            id: statistics_jinriyingyee_persont_text;
                            anchors.horizontalCenter: statistics_jinriyingyee_persont.horizontalCenter;
                            anchors.top: statistics_jinriyingyee_persont.bottom;
                            anchors.topMargin: 8* dpi_ratio;
                            text: "▲";
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#00cd66";
                        Rectangle
                        {
                            id: statistics_maolirun_image_bck;
                            width: 50* dpi_ratio;
                            height: 50* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.verticalCenter: parent.verticalCenter;
                            color: "#333333";
                            opacity: 0.2;
                            radius: 50* dpi_ratio;


                         }
                        Image
                        {
                            id: statistics_maolirun_image;
                            width: 30* dpi_ratio;
                            height: 30* dpi_ratio;
                            anchors.centerIn: statistics_maolirun_image_bck;
                            source: "/Images/maolirun_image.png";
                        }

                        Text
                        {
                            id: statistics_maolirun_text;
                            anchors.left: statistics_maolirun_image.right;
                            anchors.leftMargin: 20* dpi_ratio*1.3;
                            anchors.top: statistics_maolirun_image.top;
                            anchors.topMargin: -5* dpi_ratio;
                            text: qsTr("毛利润");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                        Text
                        {
                            id: statistics_maolirun;
                            anchors.left: statistics_maolirun_text.left;
                //            anchors.leftMargin: 1* dpi_ratio*1.3;
                            anchors.top: statistics_maolirun_text.bottom;
                            anchors.topMargin: 8* dpi_ratio;
                            text: statisticsMaolirun;
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#ffffff";
                        }

                        Text
                        {
                            id: statistics_maolirun_persont;
                            anchors.right: parent.right;//////////
                            anchors.rightMargin: 5* dpi_ratio*1.3;////////
                            anchors.top: statistics_maolirun_text.top;
                //            anchors.topMargin: 8* dpi_ratio;
                            text: statisticsMaolirunpersont + "%";
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }

                        Text
                        {
                            id: statistics_maolirun_persont_text;
                            anchors.horizontalCenter: statistics_maolirun_persont.horizontalCenter;
                //            anchors.leftMargin: 1* dpi_ratio*1.3;
                            anchors.top: statistics_maolirun_persont.bottom;
                            anchors.topMargin: 8* dpi_ratio;
                            text: "▲";
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#2cb7f5";
                        Rectangle
                        {
                            id: statistics_dingdanliang_image_bck;
                            width: 50* dpi_ratio;
                            height: 50* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.verticalCenter: parent.verticalCenter;
                            color: "#333333";
                            opacity: 0.2;
                            radius: 50* dpi_ratio;


                         }
                        Image
                        {
                            id: statistics_dingdanliang_image;
                            width: 30* dpi_ratio;
                            height: 30* dpi_ratio;
                            anchors.centerIn: statistics_dingdanliang_image_bck;
                            source: "/Images/dingdanliang_image.png";
                        }

                        Text
                        {
                            id: statistics_dingdanliang_text;
                            anchors.left: statistics_dingdanliang_image.right;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: statistics_dingdanliang_image.top;
                            anchors.topMargin: -5* dpi_ratio;
                            text: qsTr("订单量");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                        Text
                        {
                            id: statistics_dingdanliang;
                            anchors.left: statistics_dingdanliang_text.left;
                //            anchors.leftMargin: 1* dpi_ratio*1.3;
                            anchors.top: statistics_dingdanliang_text.bottom;
                            anchors.topMargin: 8* dpi_ratio;
                            text: statisticsDingdanliang;
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#ffffff";
                        }

                        Text
                        {
                            id: statistics_dingdanliang_persont ;
                            anchors.right: parent.right;//////////
                            anchors.rightMargin: 5* dpi_ratio*1.3;////////
                            anchors.top: statistics_dingdanliang_text.top;
                //            anchors.topMargin: 8* dpi_ratio;
                            text: statisticsDingdanliangPersont  + "%";
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }

                        Text
                        {
                            id: statistics_dingdanliang_persont_text;
                            anchors.horizontalCenter: statistics_dingdanliang_persont.horizontalCenter;
                //            anchors.leftMargin: 1* dpi_ratio*1.3;
                            anchors.top: statistics_dingdanliang_persont.bottom;
                            anchors.topMargin: 8* dpi_ratio;
                            text: "▲";
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#00cd66";
                        Rectangle
                        {
                            id: statistics_kedanjia_image_bck;
                            width: 50* dpi_ratio;
                            height: 50* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.verticalCenter: parent.verticalCenter;
                            color: "#333333";
                            opacity: 0.2;
                            radius: 50* dpi_ratio;


                         }
                        Image
                        {
                            id: statistics_kedanjia_image;
                            width: 40* dpi_ratio;
                            height: 40* dpi_ratio;
                            anchors.centerIn: statistics_kedanjia_image_bck;
                            source: "/Images/kedanjia_image.png";
                        }

                        Text
                        {
                            id: statistics_kedanjia_text;
                            anchors.left: statistics_kedanjia_image.right;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: statistics_kedanjia_image.top;
                            anchors.topMargin: -1* dpi_ratio;
                            text: qsTr("客单价");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                        Text
                        {
                            id: statistics_kedanjia;
                            anchors.left: statistics_kedanjia_text.left;
                //            anchors.leftMargin: 1* dpi_ratio*1.3;
                            anchors.top: statistics_kedanjia_text.bottom;
                            anchors.topMargin: 8* dpi_ratio;
                            text: statisticsKedanjia;
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#ffffff";
                        }

                        Text
                        {
                            id: statistics_kedanjia_persont;
                            anchors.right: parent.right;//////////
                            anchors.rightMargin: 5* dpi_ratio*1.3;////////
                            anchors.top: statistics_kedanjia_text.top;
                //            anchors.topMargin: 8* dpi_ratio;
                            text: statisticsKedanjiaPersont + "%";
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }

                        Text
                        {
                            id: statistics_kedanjia_persont_text;
                            anchors.horizontalCenter: statistics_kedanjia_persont.horizontalCenter;
                //            anchors.leftMargin: 1* dpi_ratio*1.3;
                            anchors.top: statistics_kedanjia_persont.bottom;
                            anchors.topMargin: 8* dpi_ratio;
                            text: "▲";
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#2cb7f5";
                        Rectangle
                        {
                            id: statistics_wangdanliang_image_bck;
                            width: 50* dpi_ratio;
                            height: 50* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.verticalCenter: parent.verticalCenter;
                            color: "#333333";
                            opacity: 0.2;
                            radius: 50* dpi_ratio;


                         }
                        Image
                        {
                            id: statistics_wangdanliang_image;
                            width: 30* dpi_ratio;
                            height: 30* dpi_ratio;
                            anchors.centerIn: statistics_wangdanliang_image_bck;
                            source: "/Images/wangdanliang_image.png";
                        }

                        Text
                        {
                            id: statistics_wangdanliang_text;
                            anchors.left: statistics_wangdanliang_image.right;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: statistics_wangdanliang_image.top;
                            anchors.topMargin: -5* dpi_ratio;
                            text: qsTr("网单量");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                        Text
                        {
                            id: statistics_wangdanliang;
                            anchors.left: statistics_wangdanliang_text.left;
                //            anchors.leftMargin: 1* dpi_ratio*1.3;
                            anchors.top: statistics_wangdanliang_text.bottom;
                            anchors.topMargin: 8* dpi_ratio;
                            text: statisticsWangdanliang;
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#ffffff";
                        }

                        Text
                        {
                            id: statistics_wangdanliang_persont;
                            anchors.right: parent.right;//////////
                            anchors.rightMargin: 5* dpi_ratio*1.3;////////
                            anchors.top: statistics_wangdanliang_text.top;
                //            anchors.topMargin: 8* dpi_ratio;
                            text: statisticsWangdanliangPersont + "%";
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }

                        Text
                        {
                            id: statistics_wangdanliang_persont_text;
                            anchors.horizontalCenter: statistics_wangdanliang_persont.horizontalCenter;
                //            anchors.leftMargin: 1* dpi_ratio*1.3;
                            anchors.top: statistics_wangdanliang_persont.bottom;
                            anchors.topMargin: 8* dpi_ratio;
                            text: "▲";
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#00bd74";
                        Text
                        {
                            id: statistics_shagnpinyouhua_text;
                            anchors.centerIn: parent;
                            text: qsTr("商品优化");
                            font.family: "微软雅黑";
                            font.pointSize: 15* dpi_ratio*1.3;
                            color: "#ffffff";
                        }
                        MouseArea
                        {
                            anchors.fill: parent;
                            onClicked:
                            {

                            }
                        }
                    }
                }
            }
            CusRect {
               id: container
               Layout.fillWidth: true
               Layout.fillHeight: true

                GridLayout {
                    id: grid
                    anchors.fill: parent
                    rows: 3
                    columns: 5
                    columnSpacing: ST.margin
                    rowSpacing: ST.margin

                    property double colMulti: grid.width / grid.columns
                    property double rowMulti: grid.height / grid.rows
                    function prefWidth(item) {
                        return colMulti * item.Layout.columnSpan
                    }
                    function prefHeight(item) {
                        return rowMulti * item.Layout.rowSpan
                    }

                    CusRect {
                        Layout.rowSpan: 1
                        Layout.columnSpan: 1
                        Layout.preferredWidth: grid.prefWidth(this)
                        Layout.preferredHeight: grid.prefHeight(this)
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#ffffff";
                        border.width: 2
                        border.color: ST.color_grey
                        Text
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_text;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 20* dpi_ratio;
                            text: qsTr("销售数据周期占比");
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#333333";
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_rec2;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_xiaoshoushujuzhouqizhanbi_text.right;
                            anchors.leftMargin: 85* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshoushujuzhouqizhanbi_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#2cb7f5";
                            radius: 1* dpi_ratio*1.3;
                            z: 2;
                            Text
                            {
                                id: statistics_xiaoshoushujuzhouqizhanbi_text2;
                                anchors.centerIn: parent;
                                text: qsTr("日");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqCycleRatioMessage("1");
                                    statistics_xiaoshoushujuzhouqizhanbi_rec2.border.color = "#2cb7f5";
                                    statistics_xiaoshoushujuzhouqizhanbi_rec3.border.color = "#dad8d8";
                                    statistics_xiaoshoushujuzhouqizhanbi_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_rec3;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_xiaoshoushujuzhouqizhanbi_rec2.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshoushujuzhouqizhanbi_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_xiaoshoushujuzhouqizhanbi_text3;
                                anchors.centerIn: parent;
                                text: qsTr("周");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqCycleRatioMessage("2");
                                    statistics_xiaoshoushujuzhouqizhanbi_rec2.border.color = "#dad8d8";
                                    statistics_xiaoshoushujuzhouqizhanbi_rec3.border.color = "#2cb7f5";
                                    statistics_xiaoshoushujuzhouqizhanbi_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_rec4;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_xiaoshoushujuzhouqizhanbi_rec3.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshoushujuzhouqizhanbi_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_xiaoshoushujuzhouqizhanbi_text4;
                                anchors.centerIn: parent;
                                text: qsTr("月");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqCycleRatioMessage("3");
                                    statistics_xiaoshoushujuzhouqizhanbi_rec2.border.color = "#dad8d8";
                                    statistics_xiaoshoushujuzhouqizhanbi_rec3.border.color = "#dad8d8";
                                    statistics_xiaoshoushujuzhouqizhanbi_rec4.border.color = "#2cb7f5";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: dial;
                            property real value : 60
                            color: "#2cb7f5";
                            width: 140* dpi_ratio;
                            height: 140* dpi_ratio;
                            radius: 80* dpi_ratio;
                            anchors.top: parent.top;
                            anchors.topMargin: 60* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            Rectangle
                            {
                                id: dial2;
                                width: 120* dpi_ratio;
                                height: 120* dpi_ratio;
                                radius: 70* dpi_ratio;
                                anchors.centerIn: parent;
                                color: "#ffffff";
                            }
                            Image
                            {
                                id: needle
                                anchors.horizontalCenter: dial2.horizontalCenter;
                                anchors.verticalCenter: dial2.verticalCenter;
                                anchors.verticalCenterOffset: -5;
                                width: 4* dpi_ratio;
                                height: 45* dpi_ratio;
                                antialiasing: true
                                source: "/Images/needle.png"
                                rotation: Math.min(Math.max(-130, dial.value*2.6 - 130), 133)
                                transformOrigin: "Bottom";
                                z: 3;
                            }
                            Rectangle
                            {
                                id: dial4;
                                width: 70* dpi_ratio;
                                height: 70* dpi_ratio;
                                anchors.horizontalCenter: parent.horizontalCenter;
                                anchors.verticalCenter: parent.verticalCenter;
                                anchors.verticalCenterOffset: 50;
                                color: "#ffffff";
                                rotation: 45;
                                transformOrigin: "Center"
                                z: 2;
                            }
                            Text
                            {
                                id: statistics_xiaoshoushujuzhouqizhanbi_text5;
                                anchors.centerIn: parent;
                                text: statisticsXiaoshoushujuzhouqizhanbiText5 + "%";
                                font.family: "微软雅黑";
                                font.pointSize: 11* dpi_ratio*1.3;
                                color: "#666665";
                                z: 3;
                            }
                            Text
                            {
                                id: statistics_xiaoshoushujuzhouqizhanbi_text_xiaoshoue;
                                anchors.horizontalCenter: statistics_xiaoshoushujuzhouqizhanbi_text5.horizontalCenter;
                                anchors.top: statistics_xiaoshoushujuzhouqizhanbi_text5.bottom;
                                anchors.topMargin: 0* dpi_ratio;
                                text: qsTr("销售额");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                                z: 3;
                            }
                        }
                        Text
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_text10;
                            anchors.top: statistics_xiaoshoushujuzhouqizhanbi_text6.top;
                            anchors.right: statistics_xiaoshoushujuzhouqizhanbi_text7.left;
                            anchors.rightMargin: 30* dpi_ratio*1.3;
                            text: statisticsXiaoshoushujuzhouqizhanbiText10 + "%";
                            font.family: "微软雅黑";
                            font.pointSize: 11* dpi_ratio*1.3;
                            color: "#666665";
                            z: 3;
                        }
                        Text
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_text6;
                            anchors.top: dial.bottom;
                            anchors.topMargin: -5* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            text: statisticsXiaoshoushujuzhouqizhanbiText6 + "%";
                            font.family: "微软雅黑";
                            font.pointSize: 11* dpi_ratio*1.3;
                            color: "#666665";
                            z: 3;
                        }
                        Text
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_text11;
                            anchors.top: statistics_xiaoshoushujuzhouqizhanbi_text6.top;
                            anchors.left: statistics_xiaoshoushujuzhouqizhanbi_text7.right;
                            anchors.leftMargin: 30* dpi_ratio*1.3;
                            text: statisticsXiaoshoushujuzhouqizhanbiText11 + "%";
                            font.family: "微软雅黑";
                            font.pointSize: 11* dpi_ratio*1.3;
                            color: "#666665";
                            z: 3;
                        }
                        Text
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_text8;
                            anchors.top: statistics_xiaoshoushujuzhouqizhanbi_text7.top;
                            anchors.right: statistics_xiaoshoushujuzhouqizhanbi_text7.left;
                            anchors.rightMargin: 30* dpi_ratio*1.3;
                            text: qsTr("网单  ");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                            z: 3;
                        }
                        Text
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_text7;
                            anchors.top: statistics_xiaoshoushujuzhouqizhanbi_text6.bottom;
                            anchors.topMargin: 2* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            text: qsTr("毛利润");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                            z: 3;
                        }
                        Text
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_text9;
                            anchors.top: statistics_xiaoshoushujuzhouqizhanbi_text7.top;
                            anchors.left: statistics_xiaoshoushujuzhouqizhanbi_text7.right;
                            anchors.leftMargin: 30* dpi_ratio*1.3;
                            text: qsTr("订单量");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                            z: 3;
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_rec5;
                            width: 1* dpi_ratio*1.3;
                            height: 30* dpi_ratio;
                            anchors.left: statistics_xiaoshoushujuzhouqizhanbi_text8.right;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoushujuzhouqizhanbi_text6.top;
                            anchors.topMargin: 5* dpi_ratio;
                            z: 3;
                            color: "#dad8d8";
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoushujuzhouqizhanbi_rec6;
                            width: 1* dpi_ratio*1.3;
                            height: 30* dpi_ratio;
                            anchors.left: statistics_xiaoshoushujuzhouqizhanbi_text7.right;
                            anchors.leftMargin: 16* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoushujuzhouqizhanbi_text6.top;
                            anchors.topMargin: 5* dpi_ratio;
                            z: 3;
                            color: "#dad8d8";
                        }
                    }
                    CusRect {
                        Layout.rowSpan: 1
                        Layout.columnSpan: 1
                        Layout.preferredWidth: grid.prefWidth(this)
                        Layout.preferredHeight: grid.prefHeight(this)
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#ffffff";
                        border.width: 2 *dpi_ratio
                        border.color: ST.color_grey
                        Text
                        {
                            id: statistics_kehudengjifenbu_text;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 20* dpi_ratio;
                            text: qsTr("客户等级分布");
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#333333";
                        }
                        Rectangle
                        {
                            id: statistics_kehudengjifenbu_text1;
                            width: 1* dpi_ratio*1.3;
                            height: 25* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_kehudengjifenbu_text.bottom;
                            anchors.topMargin: 15* dpi_ratio;
                            z: 3;
                            color: "#dad8d8";
                        }
                        Text
                        {
                            id: statistics_kehudengjifenbu_text4;
                            anchors.left: statistics_kehudengjifenbu_text1.right;
                            anchors.leftMargin: 15* dpi_ratio*1.3;
                            anchors.top: statistics_kehudengjifenbu_text1.top;
                //            anchors.topMargin: 10* dpi_ratio;
                            text: "30" + "%";
                            font.family: "微软雅黑";
                            font.pointSize: 11* dpi_ratio*1.3;
                            color: "#666665";
                        }
                        Text
                        {
                            id: statistics_kehudengjifenbu_text5;
                            anchors.left: statistics_kehudengjifenbu_text4.right;
                            anchors.leftMargin: 15* dpi_ratio*1.3;
                            anchors.top: statistics_kehudengjifenbu_text1.top;
                //            anchors.topMargin: 10* dpi_ratio;
                            text: qsTr("会员比例");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        Text
                        {
                            id: statistics_kehudengjifenbu_text2;
                            anchors.right: statistics_kehudengjifenbu_text1.left;
                            anchors.rightMargin: 15* dpi_ratio*1.3;
                            anchors.top: statistics_kehudengjifenbu_text1.top;
                //            anchors.topMargin: 10* dpi_ratio;
                            text: qsTr("会员数");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }

                        Text
                        {
                            id: statistics_kehudengjifenbu_text3;
                            anchors.right: statistics_kehudengjifenbu_text2.left;
                            anchors.rightMargin: 15* dpi_ratio*1.3;
                            anchors.top: statistics_kehudengjifenbu_text1.top;
                //            anchors.topMargin: 10* dpi_ratio;
                            text: statisticsKehudengjifenbuText3 ;
                            font.family: "微软雅黑";
                            font.pointSize: 11* dpi_ratio*1.3;
                            color: "#666665";
                        }
                        QChart3
                        {
                            id: chart_bar2;
                            width: 260* dpi_ratio*1.3;
                            height: 160* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_kehudengjifenbu_text1.bottom;
                            anchors.topMargin: 10* dpi_ratio;
                            chartAnimated: true;
                            chartAnimationEasing: Easing.OutBounce;
                            chartAnimationDuration: 2000 * dpi_ratio;
                            chartData: statistics_kehudengjifenbu_text2.text == "Number"?ChartsData2.ChartBarData2E:ChartsData2.ChartBarData2;
                            chartType: Charts3.ChartType.BAR;
                            z: 3;
                        }
                    }
                    CusRect {
                        Layout.rowSpan: 1
                        Layout.columnSpan: 3
                        Layout.preferredWidth: grid.prefWidth(this)
                        Layout.preferredHeight: grid.prefHeight(this)
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#ffffff";
                        border.width: 2
                        border.color: ST.color_grey
                        Text
                        {
                            id: statistics_24Hjiaoyifenbu_text;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 20* dpi_ratio;
                            text: qsTr("24H交易分步");
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#333333";
                        }

                        Rectangle
                        {
                            id: statistics_24Hjiaoyifenbu_rec2;
                            width: 25* dpi_ratio*1.3;
                            height: 3* dpi_ratio;
                            anchors.right: parent.right;
                            anchors.rightMargin: 100* dpi_ratio;
                            anchors.top: parent.top;
                            anchors.topMargin: 30* dpi_ratio;
                            color: "#fe339a";
                        }

                        Text
                        {
                            id: statistics_24Hjiaoyifenbu_text2;
                            anchors.left: statistics_24Hjiaoyifenbu_rec2.right;
                            anchors.leftMargin: 15* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_24Hjiaoyifenbu_rec2.verticalCenter;
                            text: qsTr("昨日曲线");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }

                        Rectangle
                        {
                            id: statistics_24Hjiaoyifenbu_rec3;
                            width: 25* dpi_ratio*1.3;
                            height: 13* dpi_ratio;
                            anchors.left: statistics_24Hjiaoyifenbu_rec2.left;
                            anchors.top: statistics_24Hjiaoyifenbu_rec2.bottom;
                            anchors.topMargin: 20* dpi_ratio;
                            color: "#2cb7f5";
                        }

                        Text
                        {
                            id: statistics_24Hjiaoyifenbu_text4;
                            anchors.left: statistics_24Hjiaoyifenbu_rec3.right;
                            anchors.leftMargin: 15* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_24Hjiaoyifenbu_rec3.verticalCenter;
                            text: qsTr("今日营业额");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }

                        QChart2
                        {
                            id: chart_bar3;
                            width: 730* dpi_ratio*1.3;
                            height: 200* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: parent.top;
                            anchors.topMargin: 50* dpi_ratio;
                            chartAnimated: true;
                            chartAnimationEasing: Easing.OutBounce;
                            chartAnimationDuration: 2000;
                            chartData: ChartsData2.ChartBarData3;
                            chartType: Charts2.ChartType.BAR;
                            z: 3;
                        }
                        QChart2
                        {
                            id: chart_line;
                            width: 760* dpi_ratio*1.3;
                            height: 200* dpi_ratio;
                            //anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.left: parent.left
                            anchors.leftMargin: 92 *dpi_ratio
                            anchors.top: parent.top;
                            anchors.topMargin: 50* dpi_ratio;
                            chartAnimated: true;
                            chartAnimationEasing: Easing.InOutElastic;
                            chartAnimationDuration: 2000;
                            chartData: ChartsData2.ChartLineData3;
                            chartType: Charts2.ChartType.LINE;
                            z: 3;
                        }
                    }
                    CusRect {
                        Layout.rowSpan: 1
                        Layout.columnSpan: 1
                        Layout.preferredWidth: grid.prefWidth(this)
                        Layout.preferredHeight: grid.prefHeight(this)
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#ffffff";
                        border.width: 2
                        border.color: ST.color_grey
                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 20* dpi_ratio;
                            text: qsTr("销售品类占比(个)");
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#333333";
                        }
                        QChart
                        {
                            id: chart_pie2;
                            width: 180* dpi_ratio;
                            height: 180* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 60* dpi_ratio;
                            chartAnimated: true;
                            chartAnimationEasing: Easing.Linear;
                            chartAnimationDuration: 1000;
                            //chartData: ChartsData2.ChartPieData2;
                            chartType: Charts2.ChartType.PIE;
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoupinleizhanbi_rec2;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_xiaoshoupinleizhanbi_text.right;
                            anchors.leftMargin: 90* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshoupinleizhanbi_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#2cb7f5";
                            radius: 1* dpi_ratio*1.3;
                            z: 2;
                            Text
                            {
                                id: statistics_xiaoshoupinleizhanbi_text2;
                                anchors.centerIn: parent;
                                text: qsTr("日");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqsalesCategoryRatioMessage("1");
                                    statistics_xiaoshoupinleizhanbi_rec2.border.color = "#2cb7f5";
                                    statistics_xiaoshoupinleizhanbi_rec3.border.color = "#dad8d8";
                                    statistics_xiaoshoupinleizhanbi_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoupinleizhanbi_rec3;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec2.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshoupinleizhanbi_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_xiaoshoupinleizhanbi_text3;
                                anchors.centerIn: parent;
                                text: qsTr("周");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqsalesCategoryRatioMessage("2");
                                    statistics_xiaoshoupinleizhanbi_rec2.border.color = "#dad8d8";
                                    statistics_xiaoshoupinleizhanbi_rec3.border.color = "#2cb7f5";
                                    statistics_xiaoshoupinleizhanbi_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoupinleizhanbi_rec4;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec3.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshoupinleizhanbi_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_xiaoshoupinleizhanbi_text4;
                                anchors.centerIn: parent;
                                text: qsTr("月");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqsalesCategoryRatioMessage("3");
                                    statistics_xiaoshoupinleizhanbi_rec2.border.color = "#dad8d8";
                                    statistics_xiaoshoupinleizhanbi_rec3.border.color = "#dad8d8";
                                    statistics_xiaoshoupinleizhanbi_rec4.border.color = "#2cb7f5";
                                }
                            }
                        }
                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text_total5;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec9.left;
                            anchors.leftMargin: -30* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec9.top;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            text: statisticsXiaoshoupinleizhanbiTextTotal5;
                            color: "#666665";
                            z: 11;
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoupinleizhanbi_rec9;
                            width: 15* dpi_ratio*1.3;
                            height: 10* dpi_ratio;
                            anchors.right: parent.right;
                            anchors.rightMargin: 50* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 60* dpi_ratio;
                            color: "#2cb7f5";
                            z: 11;
                        }
                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text5;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec9.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec9.top;
                            text: statisticsXiaoshoupinleizhanbiText5;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text_total6;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec5.left;
                            anchors.leftMargin: -30* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec5.top;
                            font.family: "微软雅黑";
                            text: statisticsXiaoshoupinleizhanbiTextTotal6;
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoupinleizhanbi_rec5;
                            width: 15* dpi_ratio*1.3;
                            height: 10* dpi_ratio;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec9.left;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec9.bottom;
                            anchors.topMargin: 12* dpi_ratio;
                            color: "#818bc7";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text6;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec5.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec5.top;
                            text: statisticsXiaoshoupinleizhanbiText6;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text_total7;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec6.left;
                            anchors.leftMargin: -30* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec6.top;
                            font.family: "微软雅黑";
                            text: statisticsXiaoshoupinleizhanbiTextTotal7;
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoupinleizhanbi_rec6;
                            width: 15* dpi_ratio*1.3;
                            height: 10* dpi_ratio;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec9.left;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec5.bottom;
                            anchors.topMargin: 12* dpi_ratio;
                            color: "#7cc956";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text7;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec6.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec6.top;
                            text: statisticsXiaoshoupinleizhanbiText7;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text_total8;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec7.left;
                            anchors.leftMargin: -30* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec7.top;
                            font.family: "微软雅黑";
                            text: statisticsXiaoshoupinleizhanbiTextTotal8;
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoupinleizhanbi_rec7;
                            width: 15* dpi_ratio*1.3;
                            height: 10* dpi_ratio;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec9.left;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec6.bottom;
                            anchors.topMargin: 12* dpi_ratio;
                            color: "#5b6877";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text8;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec7.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec7.top;
                            text: statisticsXiaoshoupinleizhanbiText8;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text_total9;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec8.left;
                            anchors.leftMargin: -30* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec8.top;
                            font.family: "微软雅黑";
                            text: statisticsXiaoshoupinleizhanbiTextTotal9
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshoupinleizhanbi_rec8;
                            width: 15* dpi_ratio*1.3;
                            height: 10* dpi_ratio;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec9.left;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec7.bottom;
                            anchors.topMargin: 12* dpi_ratio;
                            color: "#f9c002";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_xiaoshoupinleizhanbi_text9;
                            anchors.left: statistics_xiaoshoupinleizhanbi_rec8.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.top: statistics_xiaoshoupinleizhanbi_rec8.top;
                            text: statisticsXiaoshoupinleizhanbiText9;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                    }
                    CusRect {
                        Layout.rowSpan: 1
                        Layout.columnSpan: 1
                        Layout.preferredWidth: grid.prefWidth(this)
                        Layout.preferredHeight: grid.prefHeight(this)
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#ffffff";
                        border.width: 2
                        border.color: ST.color_grey
                        Text
                        {
                            id: statistics_zhifufenlei_text;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 20* dpi_ratio;
                            text: qsTr("支付分类占比(元)");
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#333333";
                        }

                        Rectangle
                        {
                            id: statistics_zhifufenlei_rec2;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_zhifufenlei_text.right;
                            anchors.leftMargin: 90* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_zhifufenlei_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#2cb7f5";
                            radius: 1* dpi_ratio*1.3;
                            z: 2;
                            Text
                            {
                                id: statistics_zhifufenlei_text2;
                                anchors.centerIn: parent;
                                text: qsTr("日");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqpaymentTypeRatioMessage("1")
                                    statistics_zhifufenlei_rec2.border.color = "#2cb7f5";
                                    statistics_zhifufenlei_rec3.border.color = "#dad8d8";
                                    statistics_zhifufenlei_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_zhifufenlei_rec3;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_zhifufenlei_rec2.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_zhifufenlei_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_zhifufenlei_text3;
                                anchors.centerIn: parent;
                                text: qsTr("周");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqpaymentTypeRatioMessage("2")
                                    statistics_zhifufenlei_rec2.border.color = "#dad8d8";
                                    statistics_zhifufenlei_rec3.border.color = "#2cb7f5";
                                    statistics_zhifufenlei_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_zhifufenlei_rec4;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_zhifufenlei_rec3.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_zhifufenlei_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_zhifufenlei_text4;
                                anchors.centerIn: parent;
                                text: qsTr("月");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqpaymentTypeRatioMessage("3")
                                    statistics_zhifufenlei_rec2.border.color = "#dad8d8";
                                    statistics_zhifufenlei_rec3.border.color = "#dad8d8";
                                    statistics_zhifufenlei_rec4.border.color = "#2cb7f5";
                                }
                            }
                        }

                        QChart
                        {
                            id: chart_doughnut2;
                            width: 175* dpi_ratio;
                            height: 175* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 30* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 60* dpi_ratio;
                            chartAnimated: true;
                            chartAnimationEasing: Easing.OutElastic;
                            chartAnimationDuration: 2000;
                            chartData: ChartsData2.ChartDoughnutData2;
                            chartType: Charts2.ChartType.DOUGHNUT;
                        }
                        Text
                        {
                            id: statistics_zhifufenlei_text_total5;
                            anchors.left: statistics_zhifufenlei_rec5.left;
                            anchors.leftMargin: -40* dpi_ratio*1.3;
                            anchors.top: statistics_zhifufenlei_rec5.top;
                            font.family: "微软雅黑";
                            text: statisticsZhifufenleiTextTotal5;
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }

                        Rectangle
                        {
                            id: statistics_zhifufenlei_rec5;
                            width: 15* dpi_ratio*1.3;
                            height: 10* dpi_ratio;
                            anchors.right: parent.right;
                            anchors.rightMargin: 40* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 65* dpi_ratio;
                            color: "#2cb7f5";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_zhifufenlei_text5;
                            anchors.left: statistics_zhifufenlei_rec5.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.top: statistics_zhifufenlei_rec5.top;
                            text: statisticsZhifufenleiText5;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_zhifufenlei_text_total6;
                            anchors.left: statistics_zhifufenlei_rec6.left;
                            anchors.leftMargin: -40* dpi_ratio*1.3;
                            anchors.top: statistics_zhifufenlei_rec6.top;
                            font.family: "微软雅黑";
                            text: statisticsZhifufenleiTextTotal6;
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Rectangle
                        {
                            id: statistics_zhifufenlei_rec6;
                            width: 15* dpi_ratio*1.3;
                            height: 10* dpi_ratio;
                            anchors.left: statistics_zhifufenlei_rec5.left;
                            anchors.top: statistics_zhifufenlei_rec5.bottom;
                            anchors.topMargin: 12* dpi_ratio;
                            color: "#818bc7";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_zhifufenlei_text6;
                            anchors.left: statistics_zhifufenlei_rec6.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.top: statistics_zhifufenlei_rec6.top;
                            text: statisticsZhifufenleiText6;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Text
                        {
                            id: statistics_zhifufenlei_text_total7;
                            anchors.left: statistics_zhifufenlei_rec7.left;
                            anchors.leftMargin: -40* dpi_ratio*1.3;
                            anchors.top: statistics_zhifufenlei_rec7.top;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            text: statisticsZhifufenleiTextTotal7
                            color: "#666665";
                            z: 11;
                        }
                        Rectangle
                        {
                            id: statistics_zhifufenlei_rec7;
                            width: 15* dpi_ratio*1.3;
                            height: 10* dpi_ratio;
                            anchors.left: statistics_zhifufenlei_rec5.left;
                            anchors.top: statistics_zhifufenlei_rec6.bottom;
                            anchors.topMargin: 12* dpi_ratio;
                            color: "#7cc956";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_zhifufenlei_text7;
                            anchors.left: statistics_zhifufenlei_rec7.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.top: statistics_zhifufenlei_rec7.top;
                            text: statisticsZhifufenleiText7;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_zhifufenlei_weixin_text_total;
                            anchors.left: statistics_zhifufenlei_rec8.left;
                            anchors.leftMargin: -40* dpi_ratio*1.3;
                            anchors.top: statistics_zhifufenlei_rec8.top;
                            font.family: "微软雅黑";
                            text: statisticsZhifufenleiWeixinTextTotal;
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Rectangle
                        {
                            id: statistics_zhifufenlei_rec8;
                            width: 15* dpi_ratio*1.3;
                            height: 10* dpi_ratio;
                            anchors.left: statistics_zhifufenlei_rec5.left;
                            anchors.top: statistics_zhifufenlei_rec7.bottom;
                            anchors.topMargin: 12* dpi_ratio;
                            color: "#5b6877";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_zhifufenlei_weixin_text;
                            anchors.left: statistics_zhifufenlei_rec8.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.top: statistics_zhifufenlei_rec8.top;
                            text: statisticsZhifufenleiWeixinText;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Text
                        {
                            id: statistics_zhifufenlei_xianjin_text_total;
                            anchors.left: statistics_zhifufenlei_rec9.left;
                            anchors.leftMargin: -40* dpi_ratio*1.3;
                            anchors.top: statistics_zhifufenlei_rec9.top;
                            font.family: "微软雅黑";
                            text: statisticsZhifufenleiXianjinTextTotal
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                        Rectangle
                        {
                            id: statistics_zhifufenlei_rec9;
                            width: 15* dpi_ratio*1.3;
                            height: 10* dpi_ratio;
                            anchors.left: statistics_zhifufenlei_rec5.left;
                            anchors.top: statistics_zhifufenlei_rec8.bottom;
                            anchors.topMargin: 12* dpi_ratio;
                            color: "#f9c002";
                            z: 11;
                        }

                        Text
                        {
                            id: statistics_zhifufenlei_xianjin_text;
                            anchors.left: statistics_zhifufenlei_rec9.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.top: statistics_zhifufenlei_rec9.top;
                            text: statisticsZhifufenleiXianjinText;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio;
                            color: "#666665";
                            z: 11;
                        }
                    }
                    CusRect {
                        Layout.rowSpan: 1
                        Layout.columnSpan: 3
                        Layout.preferredWidth: grid.prefWidth(this)
                        Layout.preferredHeight: grid.prefHeight(this)
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#ffffff";
                        border.width: 2
                        border.color: ST.color_grey
                        Text
                        {
                            id: statistics_xiaoshouezoushi_text;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 20* dpi_ratio;
                            text: qsTr("销售额走势");
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#333333";
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshouezoushi_rec2;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_xiaoshouezoushi_text.right;
                            anchors.leftMargin: 70* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshouezoushi_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#2cb7f5";
                            radius: 1* dpi_ratio*1.3;
                            z: 2;
                            Text
                            {
                                id: statistics_xiaoshouezoushi_text2;
                                anchors.centerIn: parent;
                                text: qsTr("一周");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqsalesTrendMessage(checkData.getPreSomeDay(getCurDay(),7),getCurDay());
                                    statistics_xiaoshouezoushi_rec2.border.color = "#2cb7f5";
                                    statistics_xiaoshouezoushi_rec3.border.color = "#dad8d8";
                                    statistics_xiaoshouezoushi_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshouezoushi_rec3;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_xiaoshouezoushi_rec2.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshouezoushi_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 2;
                            Text
                            {
                                id: statistics_xiaoshouezoushi_text3;
                                anchors.centerIn: parent;
                                text: qsTr("一月");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqsalesTrendMessage(checkData.getPreSomeDay(getCurDay(),30),getCurDay());
                                    statistics_xiaoshouezoushi_rec2.border.color = "#dad8d8";
                                    statistics_xiaoshouezoushi_rec3.border.color = "#2cb7f5";
                                    statistics_xiaoshouezoushi_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshouezoushi_rec4;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_xiaoshouezoushi_rec3.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshouezoushi_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_xiaoshouezoushi_text4;
                                anchors.centerIn: parent;
                                text: qsTr("一年");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqsalesTrendMessage(checkData.getPreSomeDay(getCurDay(),365),getCurDay());
                                    statistics_xiaoshouezoushi_rec2.border.color = "#dad8d8";
                                    statistics_xiaoshouezoushi_rec3.border.color = "#dad8d8";
                                    statistics_xiaoshouezoushi_rec4.border.color = "#2cb7f5";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_xiaoshouezoushi1_rec2;
                            width: 25* dpi_ratio*1.3;
                            height: 3* dpi_ratio;
                            anchors.right: parent.right;
                            anchors.rightMargin: 100* dpi_ratio;
                            anchors.top: parent.top;
                            anchors.topMargin: 25* dpi_ratio;
                            color: "#2cb7f5";
                        }

                        Text
                        {
                            id: statistics_xiaoshouezoushi1_text2;
                            anchors.left: statistics_xiaoshouezoushi1_rec2.right;
                            anchors.leftMargin: 15* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshouezoushi1_rec2.verticalCenter;
                            text: qsTr("销售总额");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }

                        Rectangle
                        {
                            id: statistics_xiaoshouezoushi1_rec3;
                            width: 25* dpi_ratio*1.3;
                            height: 3* dpi_ratio;
                            anchors.left: statistics_xiaoshouezoushi1_rec2.left;
                            anchors.top: statistics_xiaoshouezoushi1_rec2.bottom;
                            anchors.topMargin: 20* dpi_ratio;
                            color: "#818BC7";
                        }

                        Text
                        {
                            id: statistics_xiaoshouezoushi1_text4;
                            anchors.left: statistics_xiaoshouezoushi1_rec3.right;
                            anchors.leftMargin: 15* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_xiaoshouezoushi1_rec3.verticalCenter;
                            text: qsTr("订单量");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        QChart3
                        {
                            id: chart_line6;
                            width: 760* dpi_ratio*1.3;
                            height: 200* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 65* dpi_ratio*1.3;
                            anchors.bottom: parent.bottom;
                            anchors.bottomMargin: 3* dpi_ratio;
                            chartAnimated: true;
                            chartAnimationEasing: Easing.InOutElastic;
                            chartAnimationDuration: 2000;
                           // chartData: ChartsData2.ChartLineData8;
                            chartType: Charts3.ChartType.LINE;
                        }
                    }
                    CusRect {
                        Layout.rowSpan: 1
                        Layout.columnSpan: 2
                        Layout.preferredWidth: grid.prefWidth(this)
                        Layout.preferredHeight: grid.prefHeight(this)
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#ffffff";
                        border.width: 2
                        border.color: ST.color_grey
                        Text
                        {
                            id: statistics_kuncunzhibiaogailan_text;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 20* dpi_ratio;
                            text: qsTr("库存指标概览");
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#333333";
                        }
                        Text
                        {
                            id: statistics_kuncunzhibiaogailan_text1;
                            anchors.left: parent.left;
                            anchors.leftMargin: 15* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 45* dpi_ratio;
                            text: qsTr("库存成本（元）");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        Text
                        {
                            id: statistics_kuncunzhibiaogailan_text2;
                            anchors.left: statistics_kuncunzhibiaogailan_text1.left;
                            anchors.top: statistics_kuncunzhibiaogailan_text1.bottom;
                            anchors.topMargin: 5* dpi_ratio;
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#666665";
                        }
                        Text
                        {
                            id: statistics_kuncunzhibiaogailan_text3;
                            anchors.left: statistics_kuncunzhibiaogailan_text1.left;
                            anchors.top: statistics_kuncunzhibiaogailan_text2.bottom;
                            anchors.topMargin: 5* dpi_ratio;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#39af23";
                        }

                        Text
                        {
                            id: statistics_kuncunzhibiaogailan_text4;
                            anchors.left: statistics_kuncunzhibiaogailan_text3.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_kuncunzhibiaogailan_text3.verticalCenter;
                            text: qsTr("同比昨日");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        QChart3
                        {
                            id: chart_line3;
                            width: 140* dpi_ratio*1.3;
                            height: 130* dpi_ratio;
                            anchors.left: parent.left;
                //            anchors.leftMargin: -2* dpi_ratio*1.3;
                            anchors.bottom: parent.bottom;
                            anchors.bottomMargin: 13* dpi_ratio;
                            chartAnimated: true;
                            chartAnimationEasing: Easing.InOutElastic;
                            chartAnimationDuration: 2000;
                            //chartData: ChartsData2.ChartLineData4;
                            chartType: Charts2.ChartType.LINE;
                        }
                        Text
                        {
                            id: statistics_shangpinzongshu_text1;
                            anchors.left: chart_line4.left;
                            anchors.leftMargin: 20* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 45* dpi_ratio;
                            text: qsTr("商品总数（个）");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        Text
                        {
                            id: statistics_shangpinzongshu_text2;
                            anchors.left: statistics_shangpinzongshu_text1.left;
                            anchors.top: statistics_shangpinzongshu_text1.bottom;
                            anchors.topMargin: 5* dpi_ratio;
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#666665";
                        }
                        Text
                        {
                            id: statistics_shangpinzongshu_text3;
                            anchors.left: statistics_shangpinzongshu_text1.left;
                            anchors.top: statistics_shangpinzongshu_text2.bottom;
                            anchors.topMargin: 5* dpi_ratio;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#39af23";
                        }

                        Text
                        {
                            id: statistics_shangpinzongshu_text4;
                            anchors.left: statistics_shangpinzongshu_text3.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_shangpinzongshu_text3.verticalCenter;
                            text: qsTr("同比昨日");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        QChart3
                        {
                            id: chart_line4;
                            width: 140* dpi_ratio*1.3;
                            height: 130* dpi_ratio;
                            anchors.left: chart_line3.right;
                            anchors.leftMargin: 60* dpi_ratio*1.3;
                            anchors.bottom: parent.bottom;
                            anchors.bottomMargin: 13* dpi_ratio;
                            chartAnimated: true;
                            chartAnimationEasing: Easing.InOutElastic;
                            chartAnimationDuration: 2000;
                            //chartData: ChartsData2.ChartLineData5;
                            chartType: Charts2.ChartType.LINE;
                        }
                        Text
                        {
                            id: statistics_kucunzhouzhuantianshu_text1;
                            anchors.left: chart_line5.left;
                            anchors.leftMargin: 20* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 45* dpi_ratio;
                            text: qsTr("库存周转天数（天）");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        Text
                        {
                            id: statistics_kucunzhouzhuantianshu_text2;
                            anchors.left: statistics_kucunzhouzhuantianshu_text1.left;
                            anchors.top: statistics_kucunzhouzhuantianshu_text1.bottom;
                            anchors.topMargin: 5* dpi_ratio;
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#666665";
                        }
                        Text
                        {
                            id: statistics_kucunzhouzhuantianshu_text3;
                            anchors.left: statistics_kucunzhouzhuantianshu_text1.left;
                            anchors.top: statistics_kucunzhouzhuantianshu_text2.bottom;
                            anchors.topMargin: 5* dpi_ratio;
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#39af23";
                        }

                        Text
                        {
                            id: statistics_kucunzhouzhuantianshu_text4;
                            anchors.left: statistics_kucunzhouzhuantianshu_text3.right;
                            anchors.leftMargin: 5* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_kucunzhouzhuantianshu_text3.verticalCenter;
                            text: qsTr("同比昨日");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        QChart3
                        {
                            id: chart_line5;
                            width: 140* dpi_ratio*1.3;
                            height: 130* dpi_ratio;
                            anchors.left: chart_line4.right;
                            anchors.leftMargin: 60* dpi_ratio*1.3;
                            anchors.bottom: parent.bottom;
                            anchors.bottomMargin: 13* dpi_ratio;
                            chartAnimated: true;
                            chartAnimationEasing: Easing.InOutElastic;
                            chartAnimationDuration: 2000;
                            //chartData: ChartsData2.ChartLineData6;
                            chartType: Charts2.ChartType.LINE;
                        }
                    }
                    CusRect {
                        Layout.rowSpan: 1
                        Layout.columnSpan: 1
                        Layout.preferredWidth: grid.prefWidth(this)
                        Layout.preferredHeight: grid.prefHeight(this)
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#ffffff";
                        border.width: 2
                        border.color: ST.color_grey
                        Text
                        {
                            id: statistics_rexiaoshangpinTops_text;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 20* dpi_ratio;
                            text: qsTr("热销商品TOP5");
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#333333";
                        }

                        Rectangle
                        {
                            id: statistics_rexiaoshangpinTops_rec2;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_rexiaoshangpinTops_text.right;
                            anchors.leftMargin: 100* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_rexiaoshangpinTops_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#2cb7f5";
                            radius: 1* dpi_ratio*1.3;
                            z: 2;
                            Text
                            {
                                id: statistics_rexiaoshangpinTops_text2;
                                anchors.centerIn: parent;
                                text: qsTr("日");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    statistics_rexiaoshangpinTops_text6.text = qsTr("日销量");
                                    reqtopGoodsSaleCountMessage("1","1", "5", "1");
                                    statistics_rexiaoshangpinTops_rec2.border.color = "#2cb7f5";
                                    statistics_rexiaoshangpinTops_rec3.border.color = "#dad8d8";
                                    statistics_rexiaoshangpinTops_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_rexiaoshangpinTops_rec3;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_rexiaoshangpinTops_rec2.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_rexiaoshangpinTops_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_rexiaoshangpinTops_text3;
                                anchors.centerIn: parent;
                                text: qsTr("周");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    statistics_rexiaoshangpinTops_text6.text = qsTr("周销量");
                                    reqtopGoodsSaleCountMessage("2","1", "5", "1");
                                    statistics_rexiaoshangpinTops_rec2.border.color = "#dad8d8";
                                    statistics_rexiaoshangpinTops_rec3.border.color = "#2cb7f5";
                                    statistics_rexiaoshangpinTops_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_rexiaoshangpinTops_rec4;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_rexiaoshangpinTops_rec3.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_rexiaoshangpinTops_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_rexiaoshangpinTops_text4;
                                anchors.centerIn: parent;
                                text: qsTr("月");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    statistics_rexiaoshangpinTops_text6.text = qsTr("月销量");
                                    reqtopGoodsSaleCountMessage("3","1", "5", "1");
                                    statistics_rexiaoshangpinTops_rec2.border.color = "#dad8d8";
                                    statistics_rexiaoshangpinTops_rec3.border.color = "#dad8d8";
                                    statistics_rexiaoshangpinTops_rec4.border.color = "#2cb7f5";
                                }
                            }
                        }

                        Text
                        {
                            id: statistics_rexiaoshangpinTops_text5;
                            anchors.left: parent.left;
                            anchors.leftMargin: 50* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 70* dpi_ratio;
                            text: qsTr("名称");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        Text
                        {
                            id: statistics_rexiaoshangpinTops_text6;
                            anchors.left: statistics_rexiaoshangpinTops_text5.right;
                            anchors.leftMargin: 85* dpi_ratio*1.3;
                            anchors.top: statistics_rexiaoshangpinTops_text5.top;
                            text: qsTr("日销量");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }

                        Text
                        {
                            id: statistics_rexiaoshangpinTops_text7;
                            anchors.left: statistics_rexiaoshangpinTops_text6.right;
                            anchors.leftMargin: 30* dpi_ratio*1.3;
                            anchors.top: statistics_rexiaoshangpinTops_text5.top;
                            text: qsTr("周环比");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        SaleRankingRec
                        {
                            id: statistics_rexiaoshangpinTops_sale_ranking_rec1;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_rexiaoshangpinTops_text6.bottom;
                            anchors.topMargin: 3* dpi_ratio;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_rexiaoshangpinTops_sale_ranking_rec2;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_rexiaoshangpinTops_sale_ranking_rec1.bottom;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_rexiaoshangpinTops_sale_ranking_rec3;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_rexiaoshangpinTops_sale_ranking_rec2.bottom;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_rexiaoshangpinTops_sale_ranking_rec4;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_rexiaoshangpinTops_sale_ranking_rec3.bottom;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_rexiaoshangpinTops_sale_ranking_rec5;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_rexiaoshangpinTops_sale_ranking_rec4.bottom;
                            visible: true;

                        }
                    }
                    CusRect {
                        Layout.rowSpan: 1
                        Layout.columnSpan: 1
                        Layout.preferredWidth: grid.prefWidth(this)
                        Layout.preferredHeight: grid.prefHeight(this)
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#ffffff";
                        border.width: 2
                        border.color: ST.color_grey
                        Text
                        {
                            id: statistics_lijilirunshanggpinTops_text;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 20* dpi_ratio;
                            text: qsTr("累计利润商品TOP5");
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#333333";
                        }

                        Rectangle
                        {
                            id: statistics_lijilirunshanggpinTops_rec2;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_lijilirunshanggpinTops_text.right;
                            anchors.leftMargin: 75* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_lijilirunshanggpinTops_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#2cb7f5";
                            radius: 1* dpi_ratio*1.3;
                            z: 2;
                            Text
                            {
                                id: statistics_lijilirunshanggpinTops_text2;
                                anchors.centerIn: parent;
                                text: qsTr("日");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqtopGoodsPorfitMessage("1","1", "5", "2");
                                    statistics_lijilirunshanggpinTops_rec2.border.color = "#2cb7f5";
                                    statistics_lijilirunshanggpinTops_rec3.border.color = "#dad8d8";
                                    statistics_lijilirunshanggpinTops_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_lijilirunshanggpinTops_rec3;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_lijilirunshanggpinTops_rec2.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_lijilirunshanggpinTops_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_lijilirunshanggpinTops_text3;
                                anchors.centerIn: parent;
                                text: qsTr("周");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqtopGoodsPorfitMessage("2","1", "5", "2");
                                    statistics_lijilirunshanggpinTops_rec2.border.color = "#dad8d8";
                                    statistics_lijilirunshanggpinTops_rec3.border.color = "#2cb7f5";
                                    statistics_lijilirunshanggpinTops_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_lijilirunshanggpinTops_rec4;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_lijilirunshanggpinTops_rec3.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_lijilirunshanggpinTops_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_lijilirunshanggpinTops_text4;
                                anchors.centerIn: parent;
                                text: qsTr("月");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqtopGoodsPorfitMessage("3","1", "5", "2");
                                    statistics_lijilirunshanggpinTops_rec2.border.color = "#dad8d8";
                                    statistics_lijilirunshanggpinTops_rec3.border.color = "#dad8d8";
                                    statistics_lijilirunshanggpinTops_rec4.border.color = "#2cb7f5";
                                }
                            }
                        }
                        Text
                        {
                            id: statistics_lijilirunshanggpinTops_text5;
                            anchors.left: parent.left;
                            anchors.leftMargin: 50* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 70* dpi_ratio;
                            text: qsTr("名称");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        Text
                        {
                            id: statistics_lijilirunshanggpinTops_text6;
                            anchors.left: statistics_lijilirunshanggpinTops_text5.right;
                            anchors.leftMargin: 85* dpi_ratio*1.3;
                            anchors.top: statistics_lijilirunshanggpinTops_text5.top;
                            text: qsTr("总利润");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }

                        Text
                        {
                            id: statistics_lijilirunshanggpinTops_text7;
                            anchors.left: statistics_lijilirunshanggpinTops_text6.right;
                            anchors.leftMargin: 30* dpi_ratio*1.3;
                            anchors.top: statistics_lijilirunshanggpinTops_text5.top;
                            text: qsTr("周环比");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        SaleRankingRec
                        {
                            id: statistics_lijilirunshanggpinTops_sale_ranking_rec1;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_lijilirunshanggpinTops_text6.bottom;
                            anchors.topMargin: 3* dpi_ratio;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_lijilirunshanggpinTops_sale_ranking_rec2;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_lijilirunshanggpinTops_sale_ranking_rec1.bottom;
                //            anchors.topMargin: 5* dpi_ratio;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_lijilirunshanggpinTops_sale_ranking_rec3;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_lijilirunshanggpinTops_sale_ranking_rec2.bottom;
                //            anchors.topMargin: 5* dpi_ratio;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_lijilirunshanggpinTops_sale_ranking_rec4;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_lijilirunshanggpinTops_sale_ranking_rec3.bottom;
                //            anchors.topMargin: 5* dpi_ratio;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_lijilirunshanggpinTops_sale_ranking_rec5;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_lijilirunshanggpinTops_sale_ranking_rec4.bottom;
                //            anchors.topMargin: 5* dpi_ratio;
                            visible: true;
                        }
                    }
                    CusRect {
                        Layout.rowSpan: 1
                        Layout.columnSpan: 1
                        Layout.preferredWidth: grid.prefWidth(this)
                        Layout.preferredHeight: grid.prefHeight(this)
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius: ST.radius
                        color: "#ffffff";
                        border.width: 2
                        border.color: ST.color_grey
                        Text
                        {
                            id: statistics_zhixiaoshangpinTops_text;
                            anchors.left: parent.left;
                            anchors.leftMargin: 10* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 20* dpi_ratio;
                            text: qsTr("滞销商品TOP5");
                            font.family: "微软雅黑";
                            font.pointSize: 10* dpi_ratio*1.3;
                            color: "#333333";
                        }

                        Rectangle
                        {
                            id: statistics_zhixiaoshangpinTops_rec2;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_zhixiaoshangpinTops_text.right;
                            anchors.leftMargin: 100* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_zhixiaoshangpinTops_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#2cb7f5";
                            radius: 1* dpi_ratio*1.3;
                            z: 2;
                            Text
                            {
                                id: statistics_zhixiaoshangpinTops_text2;
                                anchors.centerIn: parent;
                                text: qsTr("日");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqqueryUnmarketableTop5Message("1", "1", "5");
                                    statistics_zhixiaoshangpinTops_rec2.border.color = "#2cb7f5";
                                    statistics_zhixiaoshangpinTops_rec3.border.color = "#dad8d8";
                                    statistics_zhixiaoshangpinTops_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_zhixiaoshangpinTops_rec3;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_zhixiaoshangpinTops_rec2.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_zhixiaoshangpinTops_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_zhixiaoshangpinTops_text3;
                                anchors.centerIn: parent;
                                text: qsTr("周");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqqueryUnmarketableTop5Message("2", "1", "5");
                                    statistics_zhixiaoshangpinTops_rec2.border.color = "#dad8d8";
                                    statistics_zhixiaoshangpinTops_rec3.border.color = "#2cb7f5";
                                    statistics_zhixiaoshangpinTops_rec4.border.color = "#dad8d8";
                                }
                            }
                        }
                        Rectangle
                        {
                            id: statistics_zhixiaoshangpinTops_rec4;
                            width: 25* dpi_ratio*1.3;
                            height: 20* dpi_ratio;
                            anchors.left: statistics_zhixiaoshangpinTops_rec3.right;
                //            anchors.leftMargin: -1* dpi_ratio*1.3;
                            anchors.verticalCenter: statistics_zhixiaoshangpinTops_text.verticalCenter;
                            anchors.verticalCenterOffset: 3* dpi_ratio;
                            border.width: 1* dpi_ratio*1.3;
                            border.color: "#dad8d8";
                            radius: 1* dpi_ratio*1.3;
                            z: 1;
                            Text
                            {
                                id: statistics_zhixiaoshangpinTops_text4;
                                anchors.centerIn: parent;
                                text: qsTr("月");
                                font.family: "微软雅黑";
                                font.pointSize: 8* dpi_ratio*1.3;
                                color: "#666665";
                            }
                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    reqqueryUnmarketableTop5Message("3", "1", "5");
                                    statistics_zhixiaoshangpinTops_rec2.border.color = "#dad8d8";
                                    statistics_zhixiaoshangpinTops_rec3.border.color = "#dad8d8";
                                    statistics_zhixiaoshangpinTops_rec4.border.color = "#2cb7f5";
                                }
                            }
                        }
                        Text
                        {
                            id: statistics_zhixiaoshangpinTops_text5;
                            anchors.left: parent.left;
                            anchors.leftMargin: 50* dpi_ratio*1.3;
                            anchors.top: parent.top;
                            anchors.topMargin: 70* dpi_ratio;
                            text: qsTr("名称");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        Text
                        {
                            id: statistics_zhixiaoshangpinTops_text6;
                            anchors.left: statistics_zhixiaoshangpinTops_text5.right;
                            anchors.leftMargin: 85* dpi_ratio*1.3;
                            anchors.top: statistics_zhixiaoshangpinTops_text5.top;
                            text: qsTr("日销量");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }

                        Text
                        {
                            id: statistics_zhixiaoshangpinTops_text7;
                            anchors.left: statistics_zhixiaoshangpinTops_text6.right;
                            anchors.leftMargin: 20* dpi_ratio*1.3;
                            anchors.top: statistics_zhixiaoshangpinTops_text5.top;
                            text: qsTr("周环比");
                            font.family: "微软雅黑";
                            font.pointSize: 8* dpi_ratio*1.3;
                            color: "#999999";
                        }
                        SaleRankingRec
                        {
                            id: statistics_zhixiaoshangpinTops_sale_ranking_rec1;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_zhixiaoshangpinTops_text6.bottom;
                            anchors.topMargin: 3* dpi_ratio;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_zhixiaoshangpinTops_sale_ranking_rec2;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_zhixiaoshangpinTops_sale_ranking_rec1.bottom;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_zhixiaoshangpinTops_sale_ranking_rec3;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_zhixiaoshangpinTops_sale_ranking_rec2.bottom;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_zhixiaoshangpinTops_sale_ranking_rec4;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_zhixiaoshangpinTops_sale_ranking_rec3.bottom;
                            visible: true;
                        }
                        SaleRankingRec
                        {
                            id: statistics_zhixiaoshangpinTops_sale_ranking_rec5;
                            width: 160* dpi_ratio*1.3;
                            height: 23* dpi_ratio;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            anchors.top: statistics_zhixiaoshangpinTops_sale_ranking_rec4.bottom;
                            visible: true;
                        }
                    }
                }
            }
        }
}
