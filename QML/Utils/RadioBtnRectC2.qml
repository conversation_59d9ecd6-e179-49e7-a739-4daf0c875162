﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import ".."

CusRect {
    id: control
    signal clicked
    property bool is_need_init: true
    property alias control_text: control_text
    property bool checked: false
    property string color_checked: ST.color_white_pure
    property string color_unchecked: ST.color_green

    color: checked ? color_checked : color_unchecked

    height: 50 * utils4Qml.getWidthRatio()
    width: 120 * utils4Qml.getWidthRatio()

    Text {
        id: control_text
        anchors.centerIn: parent
        font.family: ST.fontFamilyYaHei
        font.pixelSize: 22 * dpi_ratio
        color: checked ? ST.color_black : ST.color_white_pure
    }

    MouseArea {
        anchors.fill: parent
        onClicked: {
            control.clicked()
        }
    }
}
