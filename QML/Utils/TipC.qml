﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import ".."

Popup {
    //    id: popup
    //    x: 100
    //    y: 100
    //    width: 200
    //    height: 300
    modal: true
    focus: true
    closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
    //    width: 500
    //    height: 100
    property alias text: control.text

    Item {
        width: 260
        height: 100
        anchors.topMargin: -20
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.leftMargin: -8

        //    property alias text: control.text
        CusRect {
            id: rect1
            width: 15
            height: width
            radius: 1
            rotation: 45
            color: ST.color_orange
            anchors.horizontalCenter: control.left
            anchors.top: control.top
            anchors.topMargin: 12
        }

        TextArea {
            id: control
            anchors.fill: parent
            anchors.leftMargin: 20

            placeholderText: qsTr("请输入提示")
            //            text: "model.text_text"
            font {
                family: "Microsoft YaHei"
                pixelSize: 22
            }
            color: "white"
            selectByMouse: true
            selectionColor: "black"
            selectedTextColor: "white"
            wrapMode: TextEdit.WrapAnywhere

            background: CusRect {
                implicitWidth: 60
                implicitHeight: 40
                height: control.contentHeight + 15
                width: control.contentWidth + 22
                color: ST.color_orange
                radius: 6
            }
        }
    }
}
