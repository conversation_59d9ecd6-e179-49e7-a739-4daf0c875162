﻿import QtQuick 2.15;
import QtQuick.Controls 2.15

import ".."

SpinBox {
    id: control
    width: 200

    from: 0
    to: 100 * ratio
    value: 0
    stepSize: 1
    editable: true

    property bool is_need_init: true

    //小数位数
    property int decimals: 0
    property int ratio: Math.pow(10, decimals)

    font.family: ST.fontFamilyYaHei
    font.pixelSize: 22 * dpi_ratio

    validator: DoubleValidator {
        bottom: Math.min(control.from, control.to)
        top: Math.max(control.from, control.to)
    }

    textFromValue: function (value, locale) {
        return Number(value / control.ratio).toLocaleString(locale, 'f', control.decimals)
    }

    valueFromText: function (text, locale) {
        return Number.fromLocaleString(locale, text) * control.ratio
    }

    contentItem: CusTextField {
        id: text_input1
        z: 2
        text: control.textFromValue(control.value, control.locale)

        font: control.font
        color: ST.color_green
        selectionColor: ST.color_green
        selectedTextColor: "#ffffff"
        horizontalAlignment: Qt.AlignHCenter
        verticalAlignment: Qt.AlignVCenter

        readOnly: !control.editable
        validator: control.validator
        inputMethodHints: Qt.ImhFormattedNumbersOnly
    }

    up.indicator: Rectangle {
        x: control.mirrored ? 0 : parent.width - width
        height: parent.height
        implicitWidth: control.width / 4
        implicitHeight: 40 * dpi_ratio
        color: control.up.pressed ? "#e4e4e4" : "#f6f6f6"
        border.color: enabled ? ST.color_green : "#bdbebf"
        radius: ST.radius
        border.width: ST.border_width

        Text {
            text: "+"
            font.pixelSize: control.font.pixelSize * 2 * configTool.fontRatio
            color: ST.color_green
            anchors.fill: parent
            fontSizeMode: Text.Fit
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }
    down.indicator: Rectangle {
        x: control.mirrored ? parent.width - width : 0
        height: parent.height
        implicitWidth: control.width / 4
        implicitHeight: 40 * dpi_ratio
        color: control.down.pressed ? "#e4e4e4" : "#f6f6f6"
        border.color: enabled ? ST.color_green : "#bdbebf"
        radius: ST.radius
        border.width: ST.border_width

        Text {
            text: "-"
            font.pixelSize: control.font.pixelSize * 2 * configTool.fontRatio
            color: ST.color_green
            anchors.fill: parent
            fontSizeMode: Text.Fit
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }
    background: Rectangle {
        implicitWidth: 140 * dpi_ratio
        border.color: "#bdbebf"
        radius: ST.radius
    }
}
