﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import ".."

Popup {
    modal: true
    focus: true
    closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
    property alias text: control.text
    property alias width_: control.width

    background: CusRect {
        color: Qt.rgba(0, 0, 0, 0) //背景为无色
    }

    contentItem: Item {
        TextArea {
            id: control
            placeholderText: qsTr("请输入提示")
            width: 300
            font {
                family: "Microsoft YaHei"
                pixelSize: 22
            }
            color: "white"
            selectByMouse: false
            selectionColor: "black"
            selectedTextColor: "white"
            wrapMode: TextEdit.WrapAnywhere

            background: CusRect {
                id: backg
                implicitWidth: 60
                implicitHeight: 40
                height: control.contentHeight + 15
                width: control.contentWidth + 22
                color: ST.color_orange
                radius: 4

                TriangleC1 {
                    width: 26
                    height: 30
                    y: 8
                    rotation: 180
                    triangleColor: ST.color_orange
                    anchors.right: parent.left
                    anchors.rightMargin: -10
                }
            }
        }
    }
}
