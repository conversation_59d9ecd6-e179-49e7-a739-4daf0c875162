﻿import QtQuick 2.15;
import ".."

CusRect {
    id: rectBackgtound
    border.color: ST.color_grey_border
    border.width: 1 * dpi_ratio
    radius: 8 * dpi_ratio
    width: 779 * dpi_ratio
    height: 144 * dpi_ratio
    clip: true
    color: ST.color_transparent

    property string textInfo1: qsTr("端口秤")
    property string textInfo2: qsTr("启用电子秤")
    property string imageSourcePrefix: "/Images/"
    property string imageSourceName: "scalesPort.png"
    property alias checked: switch_btn.checked

    Image {
        id: image1
        height: parent.height - 4 * dpi_ratio
        anchors.verticalCenter: parent.verticalCenter
        x: 5 * dpi_ratio
        fillMode: Image.PreserveAspectFit //保持宽高比
        source: imageSourcePrefix + imageSourceName
    }

    CusText {
        id: textCInfo1
        anchors.left: image1.right
        anchors.leftMargin: 30 * dpi_ratio
        anchors.top: rectBackgtound.top
        anchors.topMargin: 20 * dpi_ratio
        text: textInfo1
        font.bold: true
        font.pixelSize: 28 * dpi_ratio
    }
    CusText {
        anchors.left: textCInfo1.left
        anchors.top: textCInfo1.bottom
        anchors.topMargin: 27 * dpi_ratio
        text: textInfo2
    }

    CusSwitchButton {
        id: switch_btn
        anchors.right: rectBackgtound.right
        anchors.rightMargin: 24 * dpi_ratio
        anchors.bottom: rectBackgtound.bottom
        anchors.bottomMargin: 31 * dpi_ratio

        onClicked: {
            checked = !checked
        }
    }
}
