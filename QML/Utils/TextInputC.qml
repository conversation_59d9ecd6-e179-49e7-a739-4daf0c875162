﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import ".."

TextInput {
    font.pixelSize: 22 * dpi_ratio
    font.family: ST.fontFamilyYaHei
    MouseArea {
        anchors.fill: parent
        onClicked: {
            mySelfkeyBoard.visible = true
            myKeyboardDigital.visible = true
            myKeyboardPinyin.visible = false
            mySelfkeyBoard.x = listView_goods_edit.x + listView_goods_edit.width
            mySelfkeyBoard.y = listView_goods_edit.y + listView_goods_edit.width / 4

            parent.selectAll()
            parent.forceActiveFocus()
        }
    }
}
