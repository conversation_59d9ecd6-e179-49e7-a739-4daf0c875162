﻿import QtQuick 2.15;
import ".."

Item {
    id: root
    property alias color: triangle.color
    clip: true
    height: 25
    width: 25
    rotation: 45

    //    radius:ST.radius
    CusRect {
        id: triangle
        width: root.height
        height: width

        anchors.verticalCenter: parent.verticalCenter
        anchors.verticalCenterOffset: -parent.height / 2

        anchors.horizontalCenter: parent.horizontalCenter
        anchors.horizontalCenterOffset: -parent.width / 2

        color: "#4cbeff"
        rotation: 45
    }
}
