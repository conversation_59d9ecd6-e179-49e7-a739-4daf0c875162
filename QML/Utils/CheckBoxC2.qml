﻿import QtQuick 2.15;
import QtQuick.Controls 2.15

import ".."

Rectangle {
    id: control
    signal clicked
    property bool is_need_init: true
    property alias check_text: text_check
    property bool checked: false
    radius: ST.radius

    color: ST.color_white_pure

    opacity: control.checked ? .8 : 1

    height: 50 * utils4Qml.getWidthRatio()
    width: 120 * utils4Qml.getWidthRatio()

    CusText {
        id: text_check
        anchors.centerIn: parent
    }

    MouseArea {
        anchors.fill: parent
        onClicked: {
            control.clicked()
        }
    }
}
