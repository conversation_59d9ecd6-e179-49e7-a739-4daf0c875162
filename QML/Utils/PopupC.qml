﻿import QtQuick 2.15;
import QtQuick.Controls 2.15

Popup {
    id: myPopup
    width: 400
    height: 300
    //    x: (root.width - width) / 2
    //    y: (root.height - height) / 2
    modal: true
    focus: true
    closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutsideParent

    Rectangle {
        //            width: 400
        //            height: 300
        anchors.fill: parent
        Text {
            id: mytext
            font.pixelSize: 24
            text: qsTr("Popup 内容显示模块")
        }
    }
}
