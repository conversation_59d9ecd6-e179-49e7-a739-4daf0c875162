﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import ".."

CusRect {
    id: control
    signal clicked
    property bool is_need_init: true
    property alias control_text: control_text
    property bool checked: false

    color: ST.color_transparent

    height: 50 * utils4Qml.getWidthRatio()
    width: 120 * utils4Qml.getWidthRatio()

    Text {
        id: control_text
        anchors.centerIn: parent
        font.family: ST.fontFamilyYaHei
        font.pixelSize: 22 * utils4Qml.getWidthRatio()
        color: ST.color_black
    }

    MouseArea {
        anchors.fill: parent
        onClicked: {
            control.clicked()
        }
    }

    CusRect {
        visible: control.checked
        width: parent.width * .8
        height: 3
        color: ST.color_green
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
    }
}
