﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import ".."

RadioButton {
    id: control
    text: "test"
    implicitWidth: 120
    implicitHeight: 50

    //    anchors.verticalCenter: parent.verticalCenter
    indicator: CusRect {
        implicitWidth: parent.width
        implicitHeight: parent.height
        //        x: control.leftPadding
        z: 0
        anchors.verticalCenter: parent.verticalCenter
        radius: 4
        border.color: control.checked ? ST.color_green : ST.color_grey
        color: ST.color_transparent

        TriangleC {
            rotation: 90
            anchors.right: parent.right
            color: control.checked ? ST.color_green : ST.color_transparent
        }
    }

    contentItem: Text {
        z: 1
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.verticalCenter: parent.verticalCenter
        text: control.text
        font.family: ST.fontFamilyYaHei
        font.pixelSize: 22
        opacity: control.down ? 1 : .9
        color: control.checked || control.down ? ST.color_green : ST.color_grey
        verticalAlignment: Text.AlignVCenter
        horizontalAlignment: Text.AlignHCenter
    }
}
