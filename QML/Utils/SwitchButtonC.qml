﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import ".."

Switch {
    width: 90
    height: 26
    id: control
    font.family: ST.fontFamilyYaHei
    font.pixelSize: 22 * dpi_ratio
    property bool is_text_right: true
    property bool is_need_init: true
    focusPolicy: Qt.NoFocus
    
    indicator: CusRect {
        id: rect_root
        width: 48 * dpi_ratio
        height: 26 * dpi_ratio
        antialiasing: true
        radius: 13 * dpi_ratio
        color: control.checked ? ST.color_green : "#ffffff"
        border.color: control.checked ? ST.color_green : "#cccccc"
        anchors.verticalCenter: parent.verticalCenter

        //小圆点
        CusRect {
            id: smallRect
            width: 26 * dpi_ratio
            height: 26 * dpi_ratio
            radius: 13 * dpi_ratio
            color: control.down ? "#cccccc" : "#ffffff"
            border.color: control.checked ? (control.down ? "#17a81a" : "#21be2b") : "#999999"
            antialiasing: true
            anchors.verticalCenter: parent.verticalCenter

            //改变小圆点的位置
            NumberAnimation on x {
                to: smallRect.width
                running: control.checked ? true : false
                duration: 200
            }

            //改变小圆点的位置
            NumberAnimation on x {
                to: 0
                running: control.checked ? false : true
                duration: 200
            }
        }
    }

    contentItem: Text {
        id: text_switch
        text: control.text
        font: control.font
        verticalAlignment: Text.AlignVCenter
        anchors.verticalCenter: parent.verticalCenter

        Component.onCompleted: {
            if (control.is_text_right) {
                text_switch.anchors.left = rect_root.right
                text_switch.anchors.leftMargin = 20 * dpi_ratio
            } else {
                text_switch.anchors.right = rect_root.left
                text_switch.anchors.rightMargin = 20 * dpi_ratio
            }
        }
    }
}
