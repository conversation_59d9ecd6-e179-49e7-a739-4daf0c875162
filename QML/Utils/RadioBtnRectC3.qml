﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import ".."

RadioButton {
    property string _name: name

    id: control
    text: _name
    implicitWidth: 120 * dpi_ratio
    implicitHeight: 50 * dpi_ratio
    property alias color: rect_back.color
    font.family: ST.fontFamilyYaHei
    font.pixelSize: 22 * dpi_ratio * configTool.fontRatio
    focusPolicy: Qt.NoFocus

    indicator: CusRect {
        id: rect_back
        implicitWidth: parent.width
        implicitHeight: parent.height
        z: 0
        anchors.verticalCenter: parent.verticalCenter
        radius: 2 * dpi_ratio
        border.width: 0
        color: ST.color_white_pure

        CusRect {
            visible: control.checked
            width: parent.width * .8
            height: 3 * dpi_ratio
            color: ST.color_green
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
        }
    }

    contentItem: CusText {
        id: text_content
        z: 1
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.verticalCenter: parent.verticalCenter
        text: control.text
        font: control.font
        opacity: control.down ? 1 : .9
        color: control.checked || control.down ? ST.color_green : ST.color_black
        verticalAlignment: Text.AlignVCenter
        horizontalAlignment: Text.AlignHCenter
    }
}
