﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import ".."

TextField {
    id: control
    width: 643 * dpi_ratio
    height: 48 * dpi_ratio
    placeholderText: qsTr("请输入")
    property int keyboard_status: 0 //0 普通键盘  1数字键盘

    property bool is_clicked_select_all: false

    placeholderTextColor: ST.color_transparent

    font.family: ST.fontFamilyYaHei
    font.pixelSize: 24 * dpi_ratio
    clip: true

    function focusMe() {
        if (is_use_virtual_keyboard) {
            window_root.keyboard_c.closeAll()
            if (keyboard_status == 0) {
                window_root.keyboard_c.normal_keyboard.visible = true
            } else if (keyboard_status == 1) {
                window_root.keyboard_c.digital_keyboard.visible = true
            }
        }
        control.forceActiveFocus()
    }

    background: Item {}

    MouseArea {
        anchors.fill: parent
        onClicked: {
            focusMe()
            if (is_clicked_select_all)
                selectAll()
        }
    }

    CusText {
        text: placeholderText
        anchors.verticalCenter: parent.verticalCenter
        x: 10 * dpi_ratio
        visible: control.text == ""
        color: ST.color_grey_font
    }
}
