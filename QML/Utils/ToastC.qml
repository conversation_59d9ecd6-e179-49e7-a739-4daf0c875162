﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
import QtGraphicalEffects 1.15

import ".."
import EnumTool 1.0

Item {
    id: popup_root
    anchors.fill: parent

    function openInfo(text, time_imterval = 2000) {
        popup_root.visible = true
        text_info.text = text
        back_rect.color = ST.color_green
        timer.interval = time_imterval
        timer.running = true
    }
    function openWarn(text, time_imterval = 2000) {
        popup_root.visible = true

        back_rect.color = ST.color_orange
        if (text_info.text == text) {
            timer.interval = time_imterval
            timer.restart()
        } else {
            text_info.text = text
            timer.interval = time_imterval
            timer.running = true
        }
    }
    function openError(text, time_imterval = 2000) {
        popup_root.visible = true

        back_rect.color = ST.color_orange_hot
        if (text_info.text == text) {
            timer.interval = time_imterval
            timer.restart()
        } else {
            text_info.text = text
            timer.interval = time_imterval
            timer.running = true
        }
    }

    function close() {
        popup_root.visible = false
        text_info.text = ""
        back_rect.color = ST.color_white_pure
        text_info.color = ST.color_white_pure
    }

    Rectangle {
        id: back_rect
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: parent.top
        anchors.topMargin: 150 * dpi_ratio
        width: (text_info.text.length)*22  > 400 ?(text_info.text.length)*(24 * dpi_ratio * configTool.fontRatio) :400 * dpi_ratio * configTool.fontRatio
        height: 50 * dpi_ratio * configTool.fontRatio
        color: ST.color_green
        radius: ST.radius
        opacity: .9
        border {
            width: 2 * dpi_ratio
            color: ST.color_white_pure
        }
    }
    CusText {
        id: text_info
        anchors.centerIn: back_rect
        color: ST.color_white_pure
    }
    Timer {
        id: timer
        interval: 2000
        repeat: false
        onTriggered: close()
    }
}
