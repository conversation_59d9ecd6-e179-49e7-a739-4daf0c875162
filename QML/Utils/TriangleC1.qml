﻿import QtQuick 2.15;

Canvas {
    id: canvasId
    property color triangleColor: "#474747"
    width: parent.width
    height: parent.height

    function drawRoundedPolygon(points, radius, ctx) {

        ctx.lineWidth = 0
        ctx.strokeStyle = "#00000000"
        ctx.fillStyle = triangleColor

        ctx.beginPath()
        var length = points.length
        var start = Qt.point((points[0].x + points[length - 1].x) / 2, (points[0].y + points[length - 1].y) / 2)
        ctx.moveTo(start.x, start.y)
        for (var i = 0; i < length; i++) {
            if (i === length - 1) {
                ctx.arcTo(points[length - 1].x, points[length - 1].y, start.x, start.y, radius)
            } else {
                ctx.arcTo(points[i].x, points[i].y, points[i + 1].x, points[i + 1].y, radius)
            }
        }
        ctx.closePath()
        ctx.fill()
        ctx.stroke()
    }

    onPaint: {
        var points = [Qt.point(0, 0), Qt.point(0, canvasId.height), Qt.point(canvasId.width, canvasId.height / 2), Qt.point(0, 0)]
        drawRoundedPolygon(points, 2, getContext("2d"))
    }
}
