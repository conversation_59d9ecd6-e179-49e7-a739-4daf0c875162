﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import ".."

RadioButton {
    id: control
    text: "test"
    implicitWidth: 120 * dpi_ratio
    implicitHeight: 50 * dpi_ratio
    //    anchors.verticalCenter: parent.verticalCenter
    property alias color: rect_back.color
    font.family: ST.fontFamilyYaHei
    font.pixelSize: 22 * dpi_ratio
    focusPolicy: Qt.NoFocus

    indicator: CusRect {
        id: rect_back
        implicitWidth: parent.width
        implicitHeight: parent.height
        //        x: control.leftPadding
        z: 0
        anchors.verticalCenter: parent.verticalCenter
        radius: 2 * dpi_ratio
        border.width: 0
        color: control.checked || control.down ? ST.color_white : ST.color_green
        clip: true
//        CusRect {
//            width: parent.width
//            height: parent.height + 20
//            color: ST.color_transparent
//            border {
//                color: ST.color_grey
//                width: 2
//            }
//        }

        //        border.color: control.checked ? ST.color_green : ST.color_grey
        //        TriangleC {
        //            rotation: 90
        //            anchors.right: parent.right
        //            color: control.checked ? ST.color_green : ST.color_transparent
        //        }
    }

    contentItem: Text {
        id: text_content
        z: 1
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.verticalCenter: parent.verticalCenter
        text: control.text
        //        font.family: ST.fontFamilyYaHei
        //        font.pixelSize: 32
        font: control.font
        opacity: control.down ? 1 : .9
        color: control.checked || control.down ? ST.color_black : ST.color_white
        verticalAlignment: Text.AlignVCenter
        horizontalAlignment: Text.AlignHCenter
    }
}
