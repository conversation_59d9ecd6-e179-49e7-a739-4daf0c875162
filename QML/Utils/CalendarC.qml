﻿import QtQuick 2.15
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.15
import Qt.labs.calendar 1.0

import ".."

Item {
    id: popup_root
    anchors.fill: parent

    property var calendar_target

    function open() {
        popup_root.visible = true
    }
    function close() {
        popup_root.visible = false
    }

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: close()
        }
    }

    Rectangle {
        anchors.centerIn: parent
        id: control
        implicitWidth: 700 * dpi_ratio
        implicitHeight: 750 * dpi_ratio
        radius: ST.radius

        function getDateStr(date1) {
            return Qt.formatDate(date1, "yyyy-MM-dd")
        }

        property alias font: month_grid.font
        property alias locale: month_grid.locale
        property string selectDate: utils4Qml.getCurDate()

        MouseArea {
            anchors.fill: parent
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20 * dpi_ratio
            spacing: 20 * dpi_ratio

            Rectangle {
                Layout.fillWidth: true
                Layout.preferredHeight: 80 * dpi_ratio
                Layout.leftMargin: 150 * dpi_ratio
                Layout.rightMargin: 150 * dpi_ratio
                color: ST.getDebugColor()

                Rectangle {
                    anchors.fill: parent
                    //                    color: ST.getDebugColor()
                    Image {
                        anchors.right: text_year_month.left
                        anchors.rightMargin: 20
                        anchors.verticalCenter: text_year_month.verticalCenter
                        source: "/Images/ArrowsL.png"
                        fillMode: Image.PreserveAspectFit
                        height: 50 * dpi_ratio
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                if (month_grid.month == 0) {
                                    --month_grid.year
                                    month_grid.month = 11
                                } else {
                                    --month_grid.month
                                }
                            }
                        }
                    }
                    CusText {
                        id: text_year_month
                        anchors.centerIn: parent
                        text: month_grid.year + "年　" + month_grid.month + "月"
                        font.bold: true
                    }
                    Image {
                        anchors.left: text_year_month.right
                        anchors.leftMargin: 20 * dpi_ratio
                        anchors.verticalCenter: text_year_month.verticalCenter
                        source: "/Images/ArrowsR.png"
                        fillMode: Image.PreserveAspectFit
                        height: 50
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                if (month_grid.month == 11) {
                                    ++month_grid.year
                                    month_grid.month = 0
                                } else {
                                    ++month_grid.month
                                }
                            }
                        }
                    }
                }
            }

            Rectangle {
                Layout.fillWidth: true
                Layout.fillHeight: true

                //                color: ST.getDebugColor()
                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 10 * dpi_ratio

                    //星期1-7
                    DayOfWeekRow {
                        id: week_row
                        Layout.row: 1
                        //                Layout.column: 1
                        Layout.fillWidth: true
                        implicitHeight: 40 * dpi_ratio
                        spacing: 1 * dpi_ratio
                        topPadding: 0
                        bottomPadding: 0
                        font: control.font
                        //locale设置会影响显示星期数中英文
                        locale: control.locale
                        delegate: Text {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            text: shortName
                            font: week_row.font
                            color: ST.color_green
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            required property string shortName
                        }
                        contentItem: Rectangle {
                            color: ST.color_transparent
                            //                    border.color: ST.color_grey_border
                            RowLayout {
                                anchors.fill: parent
                                spacing: week_row.spacing
                                Repeater {
                                    model: week_row.source
                                    delegate: week_row.delegate
                                }
                            }
                        }
                    }

                    //日期单元格
                    MonthGrid {
                        id: month_grid
                        Layout.fillWidth: true
                        Layout.fillHeight: true

                        locale: Qt.locale("zh_CN")
                        spacing: 1
                        font {
                            family: ST.fontFamilyYaHei
                            pixelSize: 20 * dpi_ratio
                        }

                        delegate: Item {

                            Rectangle {
                                anchors.fill: parent
                                radius: width / 2
                                anchors.margins: 10 * dpi_ratio
                                color: if (control.selectDate == control.getDateStr(model.date)) {
                                           return ST.color_green
                                       } else if (model.today) {
                                           return ST.color_green_light
                                       } else {
                                           return ST.color_transparent
                                       }

                                border.color: ST.color_grey_btn
                                border.width: item_mouse.containsMouse ? 1 : 0
                            }

                            Text {
                                anchors.centerIn: parent
                                font: control.font
                                text: model.day
                                color: if (control.selectDate == control.getDateStr(model.date)) {
                                           return ST.color_white
                                       } else {
                                           if (model.month === month_grid.month) {
                                               return ST.color_green
                                           } else {
                                               return ST.color_green2
                                           }
                                       }
                            }
                            MouseArea {
                                id: item_mouse
                                anchors.fill: parent
                                hoverEnabled: true
                                acceptedButtons: Qt.NoButton
                            }
                        }
                        onClicked: {
                            control.selectDate = control.getDateStr(date)
                            popup_root.visible = false
                            calendar_target.text = Qt.formatDate(date, "yyyy-MM-dd")
                        }
                    }
                }
            }
        }
    }
}
