﻿import QtQuick 2.15;
import ".."

Item {
    id: container
    width: 643 * dpi_ratio
    height: 48 * dpi_ratio
//    property string text: "test"
    property alias text: name.text
    property int radius_: 6 * dpi_ratio

    CusRect {
        id: content
        color: "#F1F1F1"
        antialiasing: true
        anchors.fill: parent
        anchors.margins: 1 * dpi_ratio
        radius: radius_
    }

    Text {
        id: name
        text: ""
        anchors.verticalCenter: content.verticalCenter
        x: 20 * dpi_ratio
        color: "#333333"
        font.family: ST.fontFamilyYaHei
        font.pixelSize: 24 * dpi_ratio
    }
}
