﻿import QtQuick 2.15;
import QtQuick.Controls 2.0

import ".."

TabButton {
    id: control
    text: "Test"
    font.family: ST.fontFamilyYaHei
    font.pixelSize: 28
    font.bold: true
    property alias color: rect_back.color
    property alias radius: rect_back.radius

    contentItem: Text {
        text: control.text
        font: control.font
        anchors.centerIn: control
        opacity: enabled ? 1.0 : 0.3
        color: control.checked ? ST.color_green : ST.color_grey_font
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
    }

    background: CusRect {
        id: rect_back
        anchors.fill: control
        color: ST.color_transparent
        opacity: control.checked ? .8 : 1
        Rectangle {
            width: parent.width / 3 * 2
            height: 4
            color: ST.color_green
            anchors.bottom: parent.bottom
            anchors.horizontalCenter: parent.horizontalCenter
            visible: control.checked
        }
    }
}
