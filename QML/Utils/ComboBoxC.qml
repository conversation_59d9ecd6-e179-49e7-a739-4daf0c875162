﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15

import ".."

ComboBox {
    id: control

    font.family: ST.fontFamilyYaHei
    font.pixelSize: 22 * dpi_ratio
    property int preIndex: -1
    property bool is_need_init: true
    currentIndex: -1

    delegate: ItemDelegate {
        width: control.width
        contentItem: Text {
            text: control.textRole ? (Array.isArray(control.model) ? modelData[control.textRole] : model[control.textRole]) : modelData
            color: ST.color_green
            font: control.font
            elide: Text.ElideRight
            verticalAlignment: Text.AlignVCenter
        }
        highlighted: control.highlightedIndex === index
    }

    indicator: Canvas {
        id: canvas
        x: control.width - width - control.rightPadding
        y: control.topPadding + (control.availableHeight - height) / 2
        width: 12 * dpi_ratio
        height: 8 * dpi_ratio
        contextType: "2d"

        Connections {
            target: control
            function onPressedChanged() {
                canvas.requestPaint()
            }
        }

        onPaint: {
            context.reset()
            context.moveTo(0, 0)
            context.lineTo(width, 0)
            context.lineTo(width / 2, height)
            context.closePath()
            context.fillStyle = control.pressed ? "#17a81a" : ST.color_green
            context.fill()
        }
    }

    contentItem: Text {
        leftPadding: 14 * dpi_ratio
        text: control.displayText
        font: control.font
        color: control.pressed ? "#17a81a" : ST.color_green
        verticalAlignment: Text.AlignVCenter
        elide: Text.ElideRight
    }

    background: Rectangle {
        implicitWidth: 120 * dpi_ratio
        implicitHeight: 40 * dpi_ratio
        border.color: control.pressed ? "#17a81a" : ST.color_green
        border.width: ST.border_width
        radius: 6 * dpi_ratio
    }

    popup: Popup {
        y: control.height - ST.border_width
        width: control.width
        implicitHeight: contentItem.implicitHeight
        padding: ST.border_width

        contentItem: ListView {
            id: _rect
            clip: true
            implicitHeight: contentHeight
            model: control.popup.visible ? control.delegateModel : null
            currentIndex: control.highlightedIndex
            ScrollIndicator.vertical: ScrollIndicator {}

            layer.enabled: true
            layer.effect: OpacityMask {
                maskSource: Rectangle {
                    width: _rect.width
                    height: _rect.height
                    radius: ST.radius
                }
            }
        }

        background: Rectangle {
            border.color: ST.color_green
            border.width: ST.border_width
            clip: true
            radius: ST.radius
        }
    }
}
