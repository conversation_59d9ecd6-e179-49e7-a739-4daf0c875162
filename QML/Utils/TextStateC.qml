﻿import QtQuick 2.15;
import QtQuick.Controls 2.15

import ".."

CusRect {
    anchors.fill: parent

    property bool state_: false
    property string connected: qsTr("已连接")
    property string not_connected: qsTr("未连接")
    property int font_size: 22 * dpi_ratio * configTool.fontRatio
    color: ST.color_transparent

    CusText {
        text: "● " + (state_ == true ? connected : not_connected)
        anchors.right: parent.right
        anchors.verticalCenter: parent.verticalCenter
        color: state_ ? ST.color_green : ST.color_orange
        font.pixelSize: font_size
    }
}
