﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import "SearchBar"
import "MemberInfo"
import "GoodsList"
import EnumTool 1.0
import SettingEnum 1.0

CusRect {
    id: net_order_page
    width: 800
    height: 500
    color: ST.color_black_p1

    property int width_btn: 340 * dpi_ratio
    property int height_btn: 70 * dpi_ratio
    property real margin: 10 * dpi_ratio
    property bool shidian: false

    property alias member_ship_list: member_ship_list
    property bool is_member_ship_model: true
    property int member_ship_model_status: EnumTool.PAY_PAGE__MEMBER__NULL
    property var cur_editing_member_id: null
    property var cur_editing_member_phone: null
    property bool is_reqCus_record: false
    property var cur_editing_member_unique: null
    property bool is_need_show_member_order: false
    property bool member_shipList_visible:true
    property string temp_store_id: ""

    property string timeTemp: ""

    property bool is_show_member_order: cur_editing_member_unique != null && (member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER) && is_need_show_member_order

    //是否是虚拟分类
    property var cur_using_member_unique: ""
    property var cur_using_member_name: ""
    property var cur_using_member_points: ""
    property bool is_ningyu: false

    onCur_editing_member_uniqueChanged: {
        if(cur_editing_member_unique !== null){
            member_info.member_info_detail_edit.reqMemberInfoDetail(cur_editing_member_unique)
            is_need_show_member_order = false
        }
    }
    onIs_reqCus_recordChanged:{
        if(is_reqCus_record){

            reqCusConRecord(cur_editing_member_unique);
            is_reqCus_record = false;
            payMent.currentIndex = 0;
        }else{
        }
    }
    function openCalendarBegin(date, hour, minute) {
        window_root.loader_4_calendar_begin.sourceComponent = calendar_begin
        window_root.loader_4_calendar_begin.item.open()
        window_root.loader_4_calendar_begin.item.set(date, hour, minute)
    }

    function openCalendarEnd(date, hour, minute) {
        window_root.loader_4_calendar_end.sourceComponent = calendar_end
        window_root.loader_4_calendar_end.item.open()
        window_root.loader_4_calendar_end.item.set(date, hour, minute)
    }
    function isCorrectTime(btn_calendar_begin,btn_calendar_end) {
        var date_time_vec_begin = btn_calendar_begin.split(" ")
        var date_time_vec_end = btn_calendar_end.split(" ")
        if (date_time_vec_begin.length === 2) {
            var date_vec_begin = date_time_vec_begin[0].split("-")
            var year_begin = date_vec_begin[0]
            var month_begin = date_vec_begin[1]
            var day_begin = date_vec_begin[2]
            var time_vec_begin = date_time_vec_begin[1].split(":")
            var hour_begin = time_vec_begin[0]
            var minute_begin = time_vec_begin[1]
            if (date_time_vec_end.length === 2) {
                var date_vec_end = date_time_vec_end[0].split("-")
                var year_end = date_vec_end[0]
                var month_end = date_vec_end[1]
                var day_end = date_vec_end[2]
                var time_vec_end = date_time_vec_end[1].split(":")
                var hour_end = time_vec_end[0]
                var minute_end = time_vec_end[1]
                if(parseInt(year_begin) < parseInt(year_end)){
                    return true
                }
                else if(parseInt(year_begin) === parseInt(year_end)){
                    if(parseInt(month_begin) < parseInt(month_end)){
                        return ture
                    }
                    else if(parseInt(month_begin) === parseInt(month_end)){
                        if(parseInt(day_begin) < parseInt(day_end)){
                            return true
                        }
                        else if(parseInt(day_begin) === parseInt(day_end)){
                                if(parseInt(hour_begin) < parseInt(hour_end)){
                                    return true
                                }
                                else if(parseInt(hour_begin) === parseInt(hour_end)){
                                    if(parseInt(minute_begin) <= parseInt(minute_end)){
                                        return true
                                    }
                                }
                            }

                    }
                }
            }
        }
        return false
    }
    function reqMemberInfo() {
//            logMgr.logEvtInfo4Qml("刷新会员===")
//            memberControl.reqAllMemberInfo4Qml(function (data) {
//                var json_doc = JSON.parse(data)
//                var json_doc_data = json_doc.data
//                total_stored_value_amount = 0
//                total_number_of_members = 0
//                lm_member_list.clear()
//                lm_member_list.append({
//                                          "cusUnique": "",
//                                          "cusName": "注册"
//                                      })

//                for (var i = 0; i < json_doc_data.length; ++i) {
//                    lm_member_list.append(json_doc_data[i])
//                    total_stored_value_amount += json_doc_data[i].cusBalance
//                    ++total_number_of_members
//                }
//            })
    }
    function reqCusConRecord(cusUnique) {
        if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
            memberControl.reqCusConRecordNingyuQml(function (data) {
                var json_doc = JSON.parse(data)
                var json_doc_data = json_doc.data
                lm_member_order_record_ningyu.clear()
                for (var i = 0; i < json_doc_data.length; ++i) {
                    if(payMent.currentIndex === 0){
                       lm_member_order_record_ningyu.append(json_doc_data[i])
                    }else{
                        if (json_doc_data[i].consumptionType === payMent.currentText) {
                            lm_member_order_record_ningyu.append(json_doc_data[i])
                        }
                    }
                }
                lm_member_order_record_ningyu.currentIndex = -1
            },cur_editing_member_id ,"1","20",btn_calendar_begin.text,btn_calendar_end.text,cur_editing_member_phone)
        }else{
            memberControl.reqCusConRecordQml(function (data) {
                var json_doc = JSON.parse(data)
                var json_doc_data = json_doc.data
                lm_member_order_record.clear()
                for (var i = 0; i < json_doc_data.length; ++i) {
                    if(payMent.currentIndex === 0){
                       lm_member_order_record.append(json_doc_data[i])
                    }else{
                        if (json_doc_data[i].consumptionType === payMent.currentText) {
                            lm_member_order_record.append(json_doc_data[i])
                        }
                    }
                }
                lm_member_order_record.currentIndex = -1
            },cur_editing_member_id ,"1","20","3",btn_calendar_begin.text,btn_calendar_end.text)
        }
    }
    function setMemberInterface(cusUnique, cusName, cus_points, cusBalance) {
        cur_using_member_unique = cusUnique
        cur_using_member_name = cusName
        cur_using_member_points = cus_points
        cur_using_member_balance = cusBalance
    }
    Component.onCompleted: {
        reqMemberInfo()
        member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__NULL; //默认新增编辑都没选择
        if(temp_store_id == ""){
            temp_store_id = settingTool.getSetting(SettingEnum.STORE_ID)
            member_shipList_visible = false
            member_shipList_visible = true
        }else{
            if(temp_store_id.toString() === settingTool.getSetting(SettingEnum.STORE_ID).toString()){
            }else{
                member_shipList_visible = false
                member_shipList_visible = true
                temp_store_id = settingTool.getSetting(SettingEnum.STORE_ID)
            }
        }
    }
    onVisibleChanged: {
        if (visible){
            if(temp_store_id == ""){
                temp_store_id = settingTool.getSetting(SettingEnum.STORE_ID)
                member_shipList_visible = false
                member_shipList_visible = true
            }else{
                if(temp_store_id.toString() !== settingTool.getSetting(SettingEnum.STORE_ID).toString()){
                    logMgr.logEvtInfo4Qml("两次登录不一样")
                    member_shipList_visible = false
                    member_shipList_visible = true
                    temp_store_id = settingTool.getSetting(SettingEnum.STORE_ID)
                }
            }

            reqMemberInfo()
//            reqCusConRecord(cur_editing_member_unique);
            logMgr.logDataInfo4Qml("是否是宁宇会员消费记录店铺类型:{}",shopControl.getShopType())
            if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
                is_ningyu = true
            }else{
                is_ningyu = false
            }
            logMgr.logDataInfo4Qml("是否是宁宇会员消费记录:{}",is_ningyu)
        }else{
        }
    }

    Image {
        anchors.fill: parent
        source: "/Images/mainPageBackground.jpg"
    }

    property var total_stored_value_amount: 0
    property var total_number_of_members: 0

    FocusScope {
        id: left_side
        width: 720 * dpi_ratio
        height: net_order_page.height
        z: 1

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: net_order_page.margin
            anchors.rightMargin: net_order_page.margin / 2
            spacing: net_order_page.margin

            //会员搜索
            SearchBar4Member {
                id: search_bar_4_member
                Layout.preferredHeight: net_order_page.height_btn
                Layout.fillWidth: true
                radius: ST.radius
                visible: is_member_ship_model
                //visible: true
            }
            //会员信息
            MemberInfo {
                id: member_info
                visible: is_member_ship_model
                Layout.fillWidth: true
                Layout.fillHeight: true
            }
        }
    }
    CusRect {
        id: right_side
        clip: true
        height: net_order_page.height
        width: net_order_page.width - left_side.width
        anchors.left: left_side.right
        color: ST.color_transparent
        CusRect {
            anchors.fill: parent
            anchors.margins: net_order_page.margin
            anchors.leftMargin: net_order_page.margin / 2
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 0
                MemberShipList {
                    id: member_ship_list
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: member_shipList_visible
                }
            }
            CusRect {
                id: member_order_record_root
                anchors.fill: parent
                visible: net_order_page.is_show_member_order
                radius: ST.radius
                color: ST.color_white_pure
                is_penetration: false

                property string order_type_str: ""

                function resetInfo() {
                    order_type_str = ""
                    lm_member_order_record.clear()
                    lm_member_order_record_ningyu.clear()
                }

                onVisibleChanged: {
                    if (!visible)
                        return

                    resetInfo()
                }

                ColumnLayout {
                    anchors.fill: parent
                    anchors.topMargin: 30 * dpi_ratio
                    spacing: 30 * dpi_ratio

                    CusRect {
                        Layout.preferredHeight: 40 * dpi_ratio
                        Layout.fillWidth: true
                        Layout.leftMargin: 20 * dpi_ratio
                        Layout.rightMargin: 40 * dpi_ratio
                        color: ST.color_transparent

                        CusText {
                            text: qsTr("消费记录")
                            font.pixelSize: 32 * dpi_ratio
                            font.bold: true
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        CusText {
                            anchors.right: t_repay.left
                            text: member_info.member_info_detail_edit.cusBalance > 0 ? qsTr("账户余额: ") : qsTr("总欠款: ")
                            font.pixelSize: 28 * dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        CusText {
                            id: t_repay
                            font.bold: true
                            font.pixelSize: 35 * dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.right
                            color: member_info.member_info_detail_edit.cusBalance > 0 ? ST.color_green : ST.color_red
                            text: qsTr("￥") + member_info.member_info_detail_edit.cusBalance //"50.52"
                        }
                    }

                    CusRect {
                        Layout.preferredHeight: 60 * dpi_ratio
                        Layout.fillWidth: true
                        Layout.leftMargin: 20 * dpi_ratio
                        Layout.rightMargin: 40 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 20 * dpi_ratio

                            CusComboBox {
                                id: payMent
                                Layout.preferredWidth: 255 * dpi_ratio
                                Layout.fillHeight: true
                                textRole: "payMent_role"
                                visible: !is_ningyu
                                model: ListModel {
                                    id: lm_payment
                                    ListElement {
                                        payMent_role: qsTr("全部")
                                    }
                                    ListElement {
                                        payMent_role: qsTr("现金")
                                    }
                                    ListElement {
                                        payMent_role: qsTr("支付宝")
                                    }
                                    ListElement {
                                        payMent_role: qsTr("微信")
                                    }
                                    ListElement {
                                        payMent_role: qsTr("储值卡")
                                    }
                                    ListElement {
                                        payMent_role: qsTr("混合支付")
                                    }
                                    ListElement {
                                        payMent_role: qsTr("金圈平台")
                                    }
                                }
                                onCurrentIndexChanged: {
                                    logMgr.logEvtInfo4Qml("currentIndex：{}",currentIndex)
                                    if (currentIndex == -1) {
                                    } else {
                                        reqCusConRecord(cur_editing_member_unique);
                                    }
                                }
                                font.pixelSize: 28 * dpi_ratio
                            }

                            CusSpacer {
                                Layout.fillWidth: true
                            }
                            CusRect {
                                Layout.alignment: Qt.AlignRight
                                Layout.fillHeight: payMent.height
                                Layout.preferredWidth: 240 * dpi_ratio * configTool.fontRatio
                                radius: ST.radius
                                color: ST.color_grey

                                CusText {
                                    id: btn_calendar_begin
                                    anchors.centerIn: parent
                                    Layout.fillHeight: true
                                    property bool is_need_init: true
                                    onTextChanged: {
                                        if(btn_calendar_end.text !="" && btn_calendar_begin.text !=""){
                                            if(!isCorrectTime(btn_calendar_begin.text,btn_calendar_end.text)){
                                                btn_calendar_begin.text = timeTemp
                                                toast.openWarn("时间区间不正确!")
                                            }else{
                                                if (is_need_init)
                                                    return
                                                reqCusConRecord(cur_editing_member_unique);
                                            }
                                        }
                                    }
                                    Component.onCompleted: {
                                        is_need_init = true
                                        text = utils4Qml.getCurDate() + " 00:00"
                                        is_need_init = false
                                    }
                                }
                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        var date_time_vec = btn_calendar_begin.text.split(" ")
                                        timeTemp = btn_calendar_begin.text
                                        if (date_time_vec.length == 2) {

                                            var date_vec = date_time_vec[0].split("-")
                                            var year = date_vec[0]
                                            var month = date_vec[1]
                                            var day = date_vec[2]

                                            var time_vec = date_time_vec[1].split(":")
                                            var hour = time_vec[0]
                                            var minute = time_vec[1]

                                            openCalendarBegin(date_time_vec[0], hour, minute)
                                        }
                                    }
                                }
                                Component {
                                    id: calendar_begin
                                    CusDateTimePicker {
                                        is_floor: true
                                        target_control: btn_calendar_begin
                                    }
                                }
                            }

                            CusRect {
                                Layout.alignment: Qt.AlignRight
                                Layout.fillHeight: true
                                Layout.preferredWidth: 240 * dpi_ratio * configTool.fontRatio
                                radius: ST.radius
                                color: ST.color_grey

                                CusText {
                                    id: btn_calendar_end
                                    anchors.centerIn: parent
                                    property bool is_need_init: true
                                    onTextChanged: {
                                        if(btn_calendar_end.text !="" && btn_calendar_begin.text !=""){
                                            if(!isCorrectTime(btn_calendar_begin.text,btn_calendar_end.text)){
                                                btn_calendar_end.text = timeTemp
                                                toast.openWarn("时间区间不正确!")
                                            }else{
                                                if (is_need_init)
                                                    return
                                                reqCusConRecord(cur_editing_member_unique);
                                            }
                                        }
                                    }

                                    Component.onCompleted: {
                                        is_need_init = true
                                        text = utils4Qml.getCurDate() + " 23:55"
                                        is_need_init = false
                                    }
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {

                                        var date_time_vec = btn_calendar_end.text.split(" ")
                                        timeTemp = btn_calendar_end.text
                                        if (date_time_vec.length == 2) {

                                            var date_vec = date_time_vec[0].split("-")
                                            var year = date_vec[0]
                                            var month = date_vec[1]
                                            var day = date_vec[2]

                                            var time_vec = date_time_vec[1].split(":")
                                            var hour = time_vec[0]
                                            var minute = time_vec[1]

                                            openCalendarEnd(date_time_vec[0], hour, minute)
                                        }
                                    }
                                }
                                Component {
                                    id: calendar_end
                                    CusDateTimePicker {
                                        is_floor: false
                                        target_control: btn_calendar_end
                                    }
                                }
                            }

                        }

                    }

                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true

                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 0

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 60 * dpi_ratio
                                color: ST.color_grey

                                RowLayout {
                                    anchors.fill: parent
                                    anchors.leftMargin: 20 * dpi_ratio
                                    anchors.rightMargin: 20 * dpi_ratio
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 585 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("订单编号")
                                            color: ST.color_grey_font2
                                            anchors.centerIn: parent
                                        }
                                    }

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 390 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("订单金额")
                                            color: ST.color_grey_font2
                                            anchors.centerIn: parent
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 300 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: is_ningyu ==true?"店铺名":qsTr("付款方式")
                                            color: ST.color_grey_font2
                                            anchors.centerIn: parent
                                        }
                                    }

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 270 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("时间")
                                            color: ST.color_grey_font2
                                            anchors.centerIn: parent
                                        }
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_white_pure
                                visible: is_ningyu

                                ListView {
                                    id: lv_member_order_record_ningyu
                                    anchors.fill: parent
                                    clip: true

                                    model: ListModel {
                                        id: lm_member_order_record_ningyu
                                    }

                                    delegate: CusRect {
                                        width: lv_member_order_record.width
                                        height: 70 * dpi_ratio
                                        color: ST.color_white_pure

                                        RowLayout {
                                            anchors.fill: parent
                                            anchors.leftMargin: 20 * dpi_ratio
                                            anchors.rightMargin: 20 * dpi_ratio
                                            spacing: 0
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 585 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: sale_list_unique
                                                    color: ST.color_grey_font2
                                                    anchors.centerIn: parent
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 390 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: saleListMoeny
                                                    color: ST.color_grey_font2
                                                    anchors.centerIn: parent
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 300 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: shop_name
                                                    color: ST.color_grey_font2
                                                    anchors.centerIn: parent
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 270 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: saleListDatetime
                                                    color: ST.color_grey_font2
                                                    anchors.centerIn: parent
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_white_pure
                                visible: !is_ningyu

                                ListView {
                                    id: lv_member_order_record
                                    anchors.fill: parent
                                    clip: true

                                    model: ListModel {
                                        id: lm_member_order_record
                                    }

                                    delegate: CusRect {
                                        width: lv_member_order_record.width
                                        height: 70 * dpi_ratio
                                        color: ST.color_white_pure

                                        RowLayout {
                                            anchors.fill: parent
                                            anchors.leftMargin: 20 * dpi_ratio
                                            anchors.rightMargin: 20 * dpi_ratio
                                            spacing: 0
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 585 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: saleListUnique
                                                    color: ST.color_grey_font2
                                                    anchors.centerIn: parent
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 390 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: recMoney
                                                    color: ST.color_grey_font2
                                                    anchors.centerIn: parent
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 300 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: consumptionType
                                                    color: ST.color_grey_font2
                                                    anchors.centerIn: parent
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 270 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: recDate
                                                    color: ST.color_grey_font2
                                                    anchors.centerIn: parent
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
