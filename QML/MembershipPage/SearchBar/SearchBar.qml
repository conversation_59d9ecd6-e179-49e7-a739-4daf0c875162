﻿import QtQuick 2.12
import QtQuick.Layouts 1.12
import QtQuick.Controls 2.12
import QtQuick.Window 2.12
import QtGraphicalEffects 1.0
import "../.."
import EnumTool 1.0
import SettingEnum 1.0

CusRect {
    id: rect_root
    color: ST.color_white

    property string search_str
    property alias tf_search: tf_search

    property var last_virtual_goods_kind_unique
    property var last_virtual_goods_kind_id

    signal signalSearchGoods

    onVisibleChanged: {
        if (visible) {
            refresh_content()
        }
    }

    function tryAddGoodsByBarcode(barcode) {
        var is_succ = false
        var json_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(barcode))
        if (json_goods == null) {
            return false
        }
        if (pay_page_root.is_mamager_model) {
            goods_quick_edit.addItemByBarcode(barcode)
            return true
        } else {
            shopCartList.appendItemByBarcode(barcode)
            return true
        }
    }

    function refresh_content() {
        tf_search.text = ""
    }

    function tf_search_enter(is_accept = false) {
        if (tf_search.text == "") {
            return
        }

        logMgr.logEvtInfo4Qml(tf_search.text)

        if (is_accept) {
            logMgr.logEvtInfo4Qml("支付测试 --- 扫码回车")

            if (configTool.isPlayPromptSound)
                soundCtrl.playDi()

            if (tf_search.text.length === 14 + 2 && tf_search.text.substr(0, 2) === "DH") {
                if (!permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L1_QUERY)) {
                    toast.openWarn(qsTr("无此权限"))
                    return
                }

                window_root.compo_index = EnumTool.PAGE_QUERY
                loader_query_page.loadItem()
                loader_query_page.item.cur_query_page = EnumTool.QUERY_SALES_RECORD
                loader_query_page.item.contain_sale_record.tf_search_order.text = tf_search.text.substring(2)
                loader_query_page.item.contain_sale_record.order_list.search_str = tf_search.text.substring(2)
                text = ""
                return
            }

            var payment_code = tf_search.text
            var left2 = payment_code.substring(0, 2) * 1
            if ((payment_code.length >= 16 && payment_code.length <= 24)) {
                if ((left2 >= 10 && left2 <= 15) || (left2 >= 25 && left2 <= 30) || (left2 == 62) || (left2 == 36)) {

                    if (configTool.isPlayPromptSound) {
                        soundCtrl.playDi()
                    }

                    if (shopCartList.getFinalTotalGoodsPrice() < 0.02) {
                        // toast.openWarn("金额不足0.02,无法在线支付!")
                        tf_search.text = ""
                        toast.openWarn(qsTr("金额未到达最低付款金额"))
                        return
                    }
                    logMgr.logEvtInfo4Qml("支付测试 --- 调用CPP支付")
                    pay_page_root.pay_methods.payByPaymentCode(payment_code)
                    tf_search.text = ""
                    return
                }
            }
            //将已存在商品添加入购物车
            var is_goods_barcode_exist = false

            if (tf_search.text !== "")
                is_goods_barcode_exist = tryAddGoodsByBarcode(tf_search.text)

            if (is_goods_barcode_exist) {
                if (configTool.isPlayPromptSound)
                    soundCtrl.playDi()
                tf_search.text = ""
                pay_methods.hidePayResults()
                return
            }

            //是否是商品条码
            var is_goods_barcode = utils4Qml.isGoodsBarcode(tf_search.text)
            if (is_goods_barcode) {
                var weighing_pre_two_code = settingTool.getSetting(SettingEnum.BAR_CODE_WEIGHING_PRE_TWO_CODE)

                //是称重商品
                if (String(tf_search.text).substring(0, String(weighing_pre_two_code).length) == weighing_pre_two_code) {
                    var tmp = Number(settingTool.getSetting(SettingEnum.INEXIST_WEIGHT_GOODS_SEARCH_STATUS))
                    switch (tmp) {
                    case SettingEnum.INEXIST_WEIGHT_GOODS_SEARCH_STATUS__ADD:
                        showUploadGoodsPopup(tf_search.text)
                        break
                    case SettingEnum.INEXIST_WEIGHT_GOODS_SEARCH_STATUS__ADD_2_CART:
                        shopCartList.appendItemByBarcode(tf_search.text, true)
                        break
                    default:
                        break
                    }
                    tf_search.text = ""
                    return
                } else if (utils4Qml.isDaHuaNocodeGoodsBarcode(tf_search.text)) {
                    var sub_str = tf_search.text.substr(-6)
                    sub_str = sub_str.substring(0, 5)
                    sub_str = (Number(sub_str) / 100.00)

                    shopCartList.appendNoCodeItemByBarcode(String(EnumTool.ID_NO_CODE_GOODS), sub_str, 1)
                    tf_search.text = ""
                    return
                } else {
                    showUploadGoodsPopup(tf_search.text)
                    tf_search.text = ""
                    toast.openInfo(qsTr("未存在商品库中的商品条码"))
                    return
                }
            }

            //将未创建商品添加进购物车(输入价格)
            if (configTool.isQuickCreateNocodeGoods && utils4Qml.isValidPriceNum(tf_search.text)) {
                soundCtrl.playDi()

                if (right_side.cur_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_RECOGNITION) {
                    shopCartList.appendNoCodeItemByBarcode(EnumTool.ID_NO_CODE_WEIGHT_GOODS, Number(tf_search.text), weightingScaleControl.finalWeight)
                } else {
                    shopCartList.appendNoCodeItemByBarcode(EnumTool.ID_NO_CODE_GOODS, Number(tf_search.text), 1)
                }

                tf_search.text = ""
                return
            }
        }

        if (right_side.cur_virtual_goods_kind_unique != EnumTool.CUS_GOODS_KIND_ALL_GOODS) {
            last_virtual_goods_kind_unique = right_side.cur_virtual_goods_kind_unique
            last_virtual_goods_kind_id = right_side.cur_virtual_goods_kind_id
        }
        pay_page_root.goods_tabbar.setSearchStr(tf_search.text)
    }

    onFocusChanged: {
        if (focus)
            tf_search.forceActiveFocus()
    }

    RowLayout {
        spacing: 0
        anchors.fill: parent
        CusRect {
            color: ST.color_transparent
            Layout.preferredWidth: rect_root.height
            Layout.fillHeight: true
            Image {
                anchors.margins: 15
                anchors.fill: parent
                fillMode: Image.PreserveAspectFit
                source: "/Images/codeInput.png"
            }

            MouseArea {
                anchors.fill: parent

                onClicked: {
                    tf_search.forceActiveFocus()
                    tf_search.focus = true
                    tf_search.text = ""
                }
            }
        }
        CusRect {
            Layout.preferredWidth: 100
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            CusTextField {
                id: tf_search
                anchors.fill: parent
                placeholderText: qsTr("请输入商品名称/条码")
                border.width: 0

                normal_keyboard_x: 740 * dpi_ratio
                normal_keyboard_y: 355 * dpi_ratio

                digital_keyboard_x: 740 * dpi_ratio
                digital_keyboard_y: 355 * dpi_ratio

                Timer {
                    id: tf_timer
                    interval: 500
                    repeat: false
                    onTriggered: {
                        logMgr.logEvtInfo4Qml("tf_timer Triggered")
                        tf_search_enter()
                    }
                }

                onAccepted: {
                    tf_timer.stop()
                    tf_search_enter(true)
                }

                onTextChanged: {
                    if (text == "") {
                        tf_timer.stop()
                        goods_list_4_all.model_goods_data.search_str = text

                        if (last_virtual_goods_kind_unique != null && last_virtual_goods_kind_id != null) {
                            right_side.cur_virtual_goods_kind_unique = last_virtual_goods_kind_unique
                            right_side.cur_virtual_goods_kind_id = last_virtual_goods_kind_id
                        }
                    } else {
                        tf_timer.restart()
                    }
                }
            }
        }

        CusRect {
            color: ST.color_transparent
            Layout.preferredWidth: rect_root.height
            Layout.fillHeight: true
            Image {
                anchors.margins: 13
                anchors.fill: parent
                fillMode: Image.PreserveAspectFit
                source: tf_search.text == "" ? "/Images/search_image.png" : "/Images/yuebuzu_icon.png"
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    if (tf_search.text != "") {
                        tf_search.text = ""
                    }
                }
            }
        }
    }
}
