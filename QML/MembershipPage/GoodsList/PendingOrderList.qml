﻿import QtQuick 2.12
import QtQuick.Layouts 1.12
import QtQuick.Controls 2.12
import QtQuick.Window 2.12
import "Component"
import "../.."

import EnumTool 1.0

CusRect {
    width: 1200
    height: 560
    radius: ST.radius
    clip: true
    color: ST.color_transparent

    function reFreshPendingOrders() {
        lm_pending_order.clear()
        var json = orderControl.getPendingOrdersJson()

        var json_doc = JSON.parse(json)

        if (!json_doc || !json_doc.length) {
            pay_page_root.shoppng_cart.pending_order_detail.clearPendingOrderDetail() //清空挂单详情
            cur_sale_list_unique = null
            return
        }

        //逆序插入
        for (var i = json_doc.length - 1; i >= 0; --i) {
            lm_pending_order.append(json_doc[i])
        }

        var tmp_sale_list_unique = json_doc[json_doc.length - 1].sale_list_unique
        pay_page_root.shoppng_cart.pending_order_detail.showPendingOrderDetail(tmp_sale_list_unique)
        cur_sale_list_unique = tmp_sale_list_unique
    }

    Component.onCompleted: {
        reFreshPendingOrders()
    }

    onVisibleChanged: {
        if (visible)
            reFreshPendingOrders()
    }

    property var cur_sale_list_unique

    ColumnLayout {
        anchors.fill: parent

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            GridView {
                id: gv_pending_order
                anchors.fill: parent
                anchors.margins: 20
                model: ListModel {
                    id: lm_pending_order
                }
                clip: true
                snapMode: GridView.SnapToRow
                //控制方向
                flow: GridView.LeftToRight

                property int column_num: 4

                cellWidth: width / column_num
                cellHeight: 200 * dpi_ratio

                delegate: CusRect {
                    width: gv_pending_order.cellWidth
                    height: gv_pending_order.cellHeight
                    color: ST.color_transparent

                    CusRect {
                        id: rect_goods_order_list
                        anchors.fill: parent
                        anchors.margins: 7 * dpi_ratio
                        color: is_checked ? ST.color_green : ST.color_white_pure
                        radius: ST.radius

                        property bool is_checked: cur_sale_list_unique == sale_list_unique

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 10 * dpi_ratio
                            spacing: 15 * dpi_ratio

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                Layout.preferredHeight: 34 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                color: ST.color_transparent
                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("挂单") + sale_list_unique
                                    font.pixelSize: 22 * dpi_ratio
                                    color: rect_goods_order_list.is_checked ? ST.color_white_pure : ST.color_grey_font2
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 40 * dpi_ratio
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    property bool is_member: member_unique != ""
                                    text: is_member ? member_name : qsTr("匿名客户")

                                    color: if (rect_goods_order_list.is_checked) {
                                               return ST.color_white_pure
                                           } else {
                                               return is_member ? ST.color_yellow : ST.color_black
                                           }
                                    font {
                                        pixelSize: 30 * dpi_ratio
                                        bold: is_member
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                Layout.preferredHeight: 40 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                color: ST.color_transparent
                                CusText {
                                    anchors.centerIn: parent
                                    id: t_time
                                    text: sale_list_date + " " + sale_list_time
                                    font.pixelSize: 20 * dpi_ratio
                                    color: rect_goods_order_list.is_checked ? ST.color_white_pure : ST.color_grey_font
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 35 * dpi_ratio
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        Layout.alignment: Qt.AlignTop
                                        color: ST.color_transparent
                                        RowLayout {
                                            anchors.fill: parent
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: height
                                                color: ST.color_transparent

                                                Image {
                                                    anchors.fill: parent
                                                    source: rect_goods_order_list.is_checked ? "/Images/money.png" : "/Images/money_orange.png"
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent
                                                CusText {
                                                    anchors.verticalCenter: parent.verticalCenter
                                                    text: Number(order_price).toFixed(2)
                                                    font.pixelSize: 22 * dpi_ratio
                                                    color: rect_goods_order_list.is_checked ? ST.color_white_pure : ST.color_orange
                                                }
                                            }
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        Layout.alignment: Qt.AlignTop
                                        color: ST.color_transparent
                                        RowLayout {
                                            anchors.fill: parent
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: height
                                                color: ST.color_transparent

                                                Image {
                                                    anchors.fill: parent
                                                    source: rect_goods_order_list.is_checked ? "/Images/quantity.png" : "/Images/quantity_black.png"
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent
                                                CusText {
                                                    anchors.verticalCenter: parent.verticalCenter
                                                    // text: "共" + Number(quantity).toFixed(2).toString() + "件"
                                                    text: qsTr("共") + quantity + qsTr("件")
                                                    font.pixelSize: 22 * dpi_ratio
                                                    color: rect_goods_order_list.is_checked ? ST.color_white_pure : ST.color_grey_font
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                cur_sale_list_unique = sale_list_unique
                                pay_page_root.shoppng_cart.pending_order_detail.showPendingOrderDetail(sale_list_unique)
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.fillWidth: true
            Layout.preferredHeight: 100 * dpi_ratio
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent
                spacing: 25 * dpi_ratio
                CusSpacer {
                    Layout.fillWidth: true
                }
                CusButton {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 300 * dpi_ratio
                    text: qsTr("打印挂单")
                    onClicked: {
                        orderControl.printPendingOrderByOrderUnique(cur_sale_list_unique)
                    }
                }

                CusButton {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 300 * dpi_ratio
                    text: qsTr("清空挂单")
                    onClicked: {
                        showDelCurGoodsByVirtualGoodsKindConfirm()
                    }

                    function showDelCurGoodsByVirtualGoodsKindConfirm() {
                        window_root.loader_4_del_goods_from_virtual_goods_kind.sourceComponent = compo_clear_pending_order
                        window_root.loader_4_del_goods_from_virtual_goods_kind.item.open()
                    }

                    Component {
                        id: compo_clear_pending_order
                        PopupConfirm {
                            title_name: qsTr("清空挂单")
                            message_info: qsTr("确定要清空挂单吗?")
                            onConfirm: {
                                orderControl.clearAllPendingOrders()
                                goods_tabbar.showDefaultGoodsTabBar()
                                shoppng_cart.page_index = EnumTool.SHOP_CART_TAB__MAIN_ORDER
                            }
                        }
                    }
                }

                CusButton {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 300 * dpi_ratio
                    text: qsTr("取单")
                    onClicked: {
                        orderControl.takePendingOrder2ShopCart(cur_sale_list_unique)
                        var member_info = JSON.parse(shopCartList.getMemberInfoJson())

                        if (member_info) {
                            setMemberInterface(member_info["member_unique"], member_info["member_name"], member_info["member_balance"], member_info["member_point"])
                        }
                        if (cur_editing_member_unique != "")
                            refreshInterfaceMemberInfo()
                        goods_tabbar.showDefaultGoodsTabBar()
                        shoppng_cart.backToMainOrder()
                    }
                }
            }
        }
    }

    Connections {
        target: orderControl
        function onSigRefreshPendingOrders() {
            reFreshPendingOrders()
        }
    }
}
