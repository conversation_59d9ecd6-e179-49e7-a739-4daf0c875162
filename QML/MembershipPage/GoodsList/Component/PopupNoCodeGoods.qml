﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../../.."

import EnumTool 1.0

Item {
    id: popup_root

    function open(is_weight = false, weight = 0) {
        reset()
        popup_root.is_weight = is_weight
        popup_root.weight = String(weight)

        if (!is_weight) {
            quantity = 1
        }

        popup_root.visible = true
    }

    function close() {
        popup_root.visible = false
    }

    function reset() {
        quantity = "1"
        unit_price_str = "0"
    }

    property string weight: "0"
    property bool is_weight: false

    property string title_name: is_weight ? qsTr("添加无码称重") : qsTr("添加无码商品")

    property string quantity: is_weight ? (weight == "0" ? "1" : weight) : "1"

    Connections {
        target: weightingScaleControl
        enabled: is_weight
        function onSendWeight(weight_str) {
            quantity = weight_str
        }
    }

    property string total_price: (Number(unit_price_str) * Number(quantity)).toFixed(2).toString()

    property string unit_price_str: "0"
    function resetUnitPrice() {
        unit_price_str = "0"
    }

    function addStrNum(str, str2) {
        if (str == "0" && str2 == "0")
            return "0"

        if (str2 == ".") {
            if (str.indexOf(".") == -1) {
                return str += str2
            }
            return str
        }

        if (str == "0")
            return str2

        str += str2

        let arr = str.split(".")

        var ret_str = arr[0]

        if (arr.length == 2) {
            ret_str = arr[0] + "." + arr[1].substring(0, 2)
        }

        return ret_str
    }

    function setInput(input) {
        unit_price_str = addStrNum(unit_price_str, input)
    }

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 600 * dpi_ratio
        height: 630 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        onVisibleChanged: {
            x = (parent.width - width) / 2
            y = (parent.height - height) / 2
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio * configTool.fontRatio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35
                color: ST.color_transparent

                MouseArea {
                    anchors.fill: parent
                }

                ColumnLayout {
                    anchors.fill: parent

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 135 * dpi_ratio
                        color: ST.color_transparent

                        ColumnLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusText {
                                        text: qsTr("数量: ")
                                        Layout.preferredWidth: 70 * dpi_ratio * configTool.fontRatio
                                        Layout.fillHeight: true
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                    CusTextField {
                                        enabled: false
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.topMargin: 10 * dpi_ratio
                                        Layout.bottomMargin: 10 * dpi_ratio
                                        text: quantity
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        RowLayout {
                                            anchors.fill: parent
                                            CusText {
                                                text: qsTr("单价: ")
                                                Layout.preferredWidth: 70 * dpi_ratio * configTool.fontRatio
                                                Layout.fillHeight: true
                                                verticalAlignment: Text.AlignVCenter
                                            }
                                            CusTextField {
                                                enabled: false
                                                Layout.fillWidth: true
                                                Layout.preferredHeight: 38 * dpi_ratio * configTool.fontRatio
                                                Layout.alignment: Qt.AlignVCenter

                                                text: popup_root.unit_price_str
                                                background: Rectangle {
                                                    id: back_rect
                                                    color: ST.color_transparent
                                                    border.width: 2
                                                    border.color: ST.color_green
                                                    radius: ST.radius
                                                    z: -1
                                                }
                                            }
                                        }
                                    }
                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        RowLayout {
                                            anchors.fill: parent
                                            CusText {
                                                text: qsTr("总价: ")
                                                Layout.preferredWidth: 70 * dpi_ratio * configTool.fontRatio
                                                Layout.fillHeight: true
                                                verticalAlignment: Text.AlignVCenter
                                            }
                                            CusTextField {
                                                enabled: false
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.topMargin: 10 * dpi_ratio
                                                Layout.bottomMargin: 10 * dpi_ratio
                                                text: total_price
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent

                                GridLayout {
                                    id: gl_calc_btns

                                    anchors.fill: parent
                                    columns: 3
                                    rows: 4

                                    columnSpacing: 10 * dpi_ratio
                                    rowSpacing: 10 * dpi_ratio

                                    property real col_width: (width - ((columns - 1) * rowSpacing)) / columns
                                    property real col_height: (height - ((rows - 1) * columnSpacing)) / rows

                                    function prefWidth(item) {
                                        return col_width * item.Layout.columnSpan
                                    }
                                    function prefHeight(item) {
                                        return col_height * item.Layout.rowSpan
                                    }

                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "7"
                                    }

                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "8"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "9"
                                    }

                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "4"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "5"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "6"
                                    }

                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "1"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "2"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "3"
                                    }

                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "0"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "00"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "."
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 130 * dpi_ratio
                                color: ST.color_transparent

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 10 * dpi_ratio

                                    property real cell_height: gl_calc_btns.col_height

                                    KeyRect2 {
                                        Layout.preferredHeight: parent.cell_height
                                        Layout.fillWidth: true
                                        key_text: qsTr("清空")
                                        color: ST.color_red
                                        text.color: ST.color_white_pure
                                        onPressKey: {
                                            resetUnitPrice()
                                        }
                                    }
                                    KeyRect2 {
                                        Layout.preferredHeight: parent.cell_height
                                        Layout.fillWidth: true
                                        key_text: qsTr("取消")
                                        color: ST.color_grey_font
                                        text.color: ST.color_white_pure
                                        onPressKey: {
                                            close()
                                        }
                                    }
                                    KeyRect2 {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        key_text: qsTr("确定")
                                        color: ST.color_blue_deeper
                                        text.color: ST.color_white_pure
                                        onPressKey: {
                                            if (unit_price_str == "0") {
                                                toast.openWarn(qsTr("价格不可为0"))
                                                return
                                            }
                                            if (is_weight) {
                                                shoppng_cart.addNoCodeGoodsByBarcode(EnumTool.ID_NO_CODE_WEIGHT_GOODS, unit_price_str, quantity)
                                            } else {
                                                shoppng_cart.addNoCodeGoodsByBarcode(EnumTool.ID_NO_CODE_GOODS, unit_price_str, quantity)
                                            }

                                            close()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    component KeyRect: CusRect {
        id: rect_key
        property string key_text: "0"
        signal pressKey(var key)
        color: ST.color_transparent

        border {
            width: 1 * dpi_ratio
            color: ST.color_grey_border
        }

        CusText {
            anchors.centerIn: parent
            text: rect_key.key_text
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {
                rect_key.pressKey(rect_key.key_text)
            }
        }

        onPressKey: {
            setInput(key)
        }
    }
    component KeyRect2: CusRect {
        id: rect_key2
        property string key_text: "0"
        signal pressKey(var key)

        property alias text: text

        CusText {
            id: text
            anchors.centerIn: parent
            text: rect_key2.key_text
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {
                rect_key2.pressKey(rect_key2.key_text)
            }
        }
    }
}
