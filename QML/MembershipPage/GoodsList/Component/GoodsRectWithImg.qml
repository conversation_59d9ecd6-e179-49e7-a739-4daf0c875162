﻿import QtQuick 2.15;
import QtGraphicalEffects 1.0
import QtQuick.Layouts 1.15
import "../../.."

import EnumTool 1.0

Item {
    id: goods_rect_root
    width: 270
    height: 130

    readonly property bool is_no_code_goods: goods_id == EnumTool.ID_NO_CODE_GOODS || goods_id == EnumTool.ID_NO_CODE_WEIGHT_GOODS
    readonly property bool is_no_code_goods_normal: goods_id == EnumTool.ID_NO_CODE_GOODS
    readonly property bool is_no_code_goods_weight: goods_id == EnumTool.ID_NO_CODE_WEIGHT_GOODS
    readonly property var _prefix: "http://file.buyhoo.cc"

    function showNoCodeGoodsIfo() {
        window_root.loader_center.sourceComponent = compo_popup_no_code_goods
        var cur_weight = weightingScaleControl.getLastWeightKg()
        window_root.loader_center.item.open(is_no_code_goods_weight, cur_weight)
    }

    CusRect {
        anchors.margins: 7 * dpi_ratio
        anchors.fill: parent
        color: ST.color_white_pure
        id: goods_rect_rect
        radius: ST.radius

        CusRect {
            anchors.margins: 12 * dpi_ratio
            anchors.fill: parent
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 12 * dpi_ratio

                CusRect {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredHeight: 140 * dpi_ratio
                    id: rect_goods
                    color: ST.color_transparent
                    Image {
                        //                        anchors.fill: parent
                        height: parent.height
                        width: height
                        anchors.horizontalCenter: parent.horizontalCenter
                        fillMode: Image.PreserveAspectCrop
                        source: goods_picturepath == "" ? "http://file.buyhoo.cc/upload/no_goodsB.jpg" : _prefix + goods_picturepath
                        id: img_goods

                        layer.enabled: true
                        layer.effect: OpacityMask {
                            maskSource: Rectangle {
                                width: img_goods.width
                                height: img_goods.height
                                radius: ST.radius
                            }
                        }
                    }
                    layer.enabled: true // Set Layer for Enable
                    layer.effect: DropShadow {
                        //                        anchors.fill: rect_goods
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        source: rect_goods
                        horizontalOffset: 2
                        verticalOffset: 2
                        radius: 12
                        samples: 17
                        color: ST.color_black_p2
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 40 * dpi_ratio
                    color: ST.color_transparent

                    CusText {
                        text: goods_name
                        anchors.centerIn: parent
                        elide: Text.ElideRight
                        width: parent.width
                        font.pixelSize: 30 * dpi_ratio
                        Layout.alignment: Qt.AlignVCenter
                        font.bold: true
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 40 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 155 * dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: goods_sale_price.toFixed(2)
                                anchors.centerIn: parent
                                elide: Text.ElideRight
                                width: parent.width
                                color: ST.color_green
                                font.bold: true
                                font.pixelSize: 26 * dpi_ratio
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        CusRect {
                            id: rect_stock
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 120 * dpi_ratio
                            color: ST.color_transparent

                            layer.enabled: true
                            layer.effect: OpacityMask {
                                maskSource: Rectangle {
                                    width: rect_stock.width
                                    height: rect_stock.height
                                    radius: ST.radius
                                }
                            }

                            RowLayout {
                                anchors.fill: parent

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_green_light

                                    CusText {
                                        text: Number(goods_count).toFixed(2)
                                        anchors.right: parent.right
                                        elide: Text.ElideRight
                                        color: ST.color_black
                                        anchors.verticalCenter: parent.verticalCenter
                                        font.pixelSize: 18 * dpi_ratio
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: height
                                    color: ST.color_green
                                    CusText {
                                        text: "库"
                                        anchors.centerIn: parent
                                        color: ST.color_white_pure
                                        font.pixelSize: 20 * dpi_ratio
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Component {
            id: compo_popup_no_code_goods
            PopupNoCodeGoods {//                anchors.fill: parent
            }
        }

        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.AllButtons
            onClicked: {
               
                if (mouse.button == Qt.LeftButton) {
                    if (goods_id == EnumTool.ID_NO_CODE_GOODS || goods_id == EnumTool.ID_NO_CODE_WEIGHT_GOODS) {
                        showNoCodeGoodsIfo()
                        //                        shoppng_cart.addNoCodeGoodsById(goods_id)
                    } else {
                        shoppng_cart.addGoodsById(goods_id)
                    }
                }
            }
        }

        CusRect {
            anchors.top: goods_rect_rect.top
            anchors.topMargin: -5
            anchors.right: goods_rect_rect.right
            anchors.rightMargin: -5
            width: red_point_text.text.length > 2 ? 30 + (red_point_text.text.length - 2) * 20 : 30
            height: 30
            radius: height / 2
            color: ST.color_red
            visible: red_point_text.text != "0"
            CusText {
                id: red_point_text
                text: count_in_cart
                color: ST.color_white_pure
                anchors.centerIn: parent
                visible: text != 0
            }
        }

        Image {
            width: 47
            height: 47
            anchors.left: parent.left
            anchors.top: parent.top
            source: "/Images/weight_badge.png"
            visible: goods_is_weight
        }
    }
}
