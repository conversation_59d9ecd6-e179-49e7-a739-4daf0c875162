﻿import QtQuick 2.12
import QtQuick.Layouts 1.12
import QtQuick.Controls 2.12
import QtQuick.Window 2.12
import "Component"
import "../.."
import EnumTool 1.0
import SettingEnum 1.0
import SortFilterProxyModel 0.2
CusRect {
    width: 1200 * dpi_ratio
    height: 560 * dpi_ratio
    radius: ST.radius
    clip: true
    color: ST.color_transparent

    property real total_member_value_stored: 0 //会员储值总金额
    property int total_members: 0 //会员总数
    property bool initOnce: true
    property bool is_ningyu: false
    property string temp_store_id: ""
    Component.onCompleted: {
//        logMgr.logEvtInfo4Qml("组件加载时初始化会员一次")
//        refreshMember()
        if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
            is_ningyu =true
        }
    }

    onVisibleChanged: {
        if(temp_store_id == ""){
            temp_store_id = settingTool.getSetting(SettingEnum.STORE_ID)
            initOnce= true
        }else{
            if(temp_store_id.toString() === settingTool.getSetting(SettingEnum.STORE_ID).toString()){
                initOnce = false
            }else{
                initOnce = true
                temp_store_id = settingTool.getSetting(SettingEnum.STORE_ID)
            }
        }
            if(initOnce){
                initOnce =false
                logMgr.logEvtInfo4Qml("可视化初始化会员一次")
                refreshMember()
                if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
                    is_ningyu =true
                }
            }
    }

    function refreshMember() {
            logMgr.logEvtInfo4Qml("刷新会员")
            memberControl.reqAllMemberInfo4Qml(function (data) {
                var json_doc = JSON.parse(data)
                var json_doc_data = json_doc.data
                total_member_value_stored = 0
                total_members = 0
                lm_member.clear()

                for (var i = 0; i < json_doc_data.length; ++i) {
                    //                lm_member.append(json_doc_data[i])
                    var cur_data = json_doc_data[i]
                    lm_member.append({
                                         "cus_id": cur_data.hasOwnProperty("cus_id") ? cur_data.cus_id : "",
                                         "cusUnique": cur_data.hasOwnProperty("cusUnique") ? cur_data.cusUnique : "",
                                         "cusName": cur_data.hasOwnProperty("cusName") ? cur_data.cusName : "",
                                         "cus_points": cur_data.hasOwnProperty("cus_points") ? cur_data.cus_points : "",
                                         "cusBalance": cur_data.hasOwnProperty("cusBalance") ? cur_data.totalBalance : "",
                                         "cusPhone": cur_data.hasOwnProperty("cusPhone") ? cur_data.cusPhone : ""
                                     })
                    total_member_value_stored += json_doc_data[i].cusBalance
                    ++total_members
                }
            })
    }

    ColumnLayout {
        anchors.fill: parent
        //        anchors.margins: 10 * dpi_ratio
        spacing: 10 * dpi_ratio

        CusRect {
            Layout.fillWidth: true
            Layout.preferredHeight: 55 * dpi_ratio
            Layout.leftMargin: 10 * dpi_ratio
            Layout.rightMargin: 10 * dpi_ratio
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent

                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 400 * dpi_ratio
                    color: ST.color_transparent
                    CusText {
                        text: qsTr("储值总金额: ") + total_member_value_stored.toFixed(2)
                        anchors.verticalCenter: parent.verticalCenter
                        font.pixelSize: 28 * dpi_ratio
                        color: ST.color_white_pure
                    }
                }

                CusSpacer {
                    Layout.fillWidth: true
                }

                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 400 * dpi_ratio
                    color: ST.color_transparent
                    CusText {
                        text: qsTr("会员总数: ") + lm_member.count
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 28 * dpi_ratio
                        color: ST.color_white_pure
                    }
                }
            }
        }

        ListModel {
            id: lm_member
        }

        SortFilterProxyModel {
            id: member_proxy_model
            sourceModel: lm_member
            sorters: [
                RoleSorter {
                    roleName: "favorite"
                    sortOrder: Qt.DescendingOrder
                }
            ]
            filters: [
                AnyOf {
//                    RegExpFilter {
//                        roleName: "cusUnique"
//                        pattern: search_bar_4_member.search_str
//                        caseSensitivity: Qt.CaseInsensitive
//                    }
//                    RegExpFilter {
//                        roleName: "cus_id"
//                        pattern: search_bar_4_member.search_str
//                        caseSensitivity: Qt.CaseInsensitive
//                    }
                    RegExpFilter {
                        roleName: "cusPhone"
                        pattern: search_bar_4_member.search_str
                        caseSensitivity: Qt.CaseInsensitive
                    }
                    RegExpFilter {
                        roleName: "cusName"
                        pattern: search_bar_4_member.search_str
                        caseSensitivity: Qt.CaseInsensitive
                        enabled: !is_ningyu
                    }
                }
            ]
        }

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            GridView {
                id: gv_pending_order
                anchors.fill: parent
                model: member_proxy_model

                clip: true
                snapMode: GridView.SnapToRow
                //控制方向
                flow: GridView.LeftToRight
                boundsBehavior: Flickable.StopAtBounds

                property real column_num: 4

                cellWidth: width / column_num
                cellHeight: 160 * dpi_ratio

                delegate: CusRect {
                    width: gv_pending_order.cellWidth
                    height: gv_pending_order.cellHeight
                    color: ST.color_transparent

                    CusRect {
                        id: rect_goods_order_list
                        anchors.fill: parent
                        anchors.margins: 10 * dpi_ratio
                        radius: ST.radius

                        property bool is_checked: member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__NULL
                                                  && cur_using_member_unique == cusUnique // !is_member_ship_model__mgr && cur_using_member_unique == cusUnique

                        color: is_checked ? ST.color_green : ST.color_white_pure

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 15 * dpi_ratio
                            spacing: 15 * dpi_ratio

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 30 * dpi_ratio
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: cusName
                                            font.pixelSize: 28 * dpi_ratio
                                            font.bold: true
                                            color: rect_goods_order_list.is_checked ? ST.color_white_pure : ST.color_black
                                            anchors.verticalCenter: parent.verticalCenter
                                            anchors.right: parent.right
                                            width: parent.width
                                            elide: Text.ElideMiddle
                                            horizontalAlignment: Text.AlignLeft
                                        }
                                    }

                                    CusSpacer {
                                        Layout.fillWidth: true
                                    }

                                    CusRect {
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        CusText {
                                            text: cusUnique
                                            font.pixelSize: 18 * dpi_ratio
                                            color: rect_goods_order_list.is_checked ? ST.color_white_pure : ST.color_grey_font
                                            anchors.verticalCenter: parent.verticalCenter
                                            anchors.right: parent.right
                                            width: parent.width
                                            elide: Text.ElideMiddle
                                            horizontalAlignment: Text.AlignRight
                                        }
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 25 * dpi_ratio
                                color: ST.color_transparent
                                CusText {
                                    text: cusPhone ? cusPhone : ""
                                    font.pixelSize: 22 * dpi_ratio
                                    anchors.verticalCenter: parent.verticalCenter
                                    color: rect_goods_order_list.is_checked ? ST.color_white_pure : ST.color_black
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 30 * dpi_ratio
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 110 * dpi_ratio
                                        color: ST.color_transparent
                                        RowLayout {
                                            anchors.fill: parent
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: height
                                                color: ST.color_transparent
                                                Image {
                                                    width: 24 * dpi_ratio
                                                    height: width
                                                    anchors.centerIn: parent
                                                    fillMode: Image.PreserveAspectFit
                                                    source: rect_goods_order_list.is_checked ? "/Images/member_balance_white.png" : "/Images/member_balance.png"
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent
                                                CusText {
                                                    text: cusBalance
                                                    color: rect_goods_order_list.is_checked ? ST.color_white_pure : ST.color_yellow

                                                    anchors.verticalCenter: parent.verticalCenter
                                                    width: parent.width
                                                    elide: Text.ElideRight
                                                }
                                            }
                                        }
                                    }

                                    CusSpacer {
                                        Layout.fillWidth: true
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 110 * dpi_ratio
                                        color: ST.color_transparent
                                        RowLayout {
                                            anchors.fill: parent
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: height
                                                color: ST.color_transparent
                                                Image {
                                                    width: 24 * dpi_ratio
                                                    height: width
                                                    anchors.centerIn: parent
                                                    fillMode: Image.PreserveAspectFit
                                                    source: rect_goods_order_list.is_checked ? "/Images/member_point_white.png" : "/Images/member_point.png"
                                                }
                                            }
                                            CusRect {
                                                Layout.fillWidth: true
                                                Layout.fillHeight: true
                                                color: ST.color_transparent
                                                CusText {
                                                    text: cus_points
                                                    color: rect_goods_order_list.is_checked ? ST.color_white_pure : ST.color_green
                                                    anchors.verticalCenter: parent.verticalCenter
                                                    width: parent.width
                                                    elide: Text.ElideRight
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                if (member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER) {
                                    //编辑会员
                                    cur_editing_member_unique = cusUnique
                                    logMgr.logEvtInfo4Qml("会员号：{}",cur_editing_member_unique)
                                } else if (member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__NULL) {
                                    //选择会员
                                    setMemberInterface(cusUnique, cusName, cus_points, cusBalance)
                                    shopCartList.setCurMemberUnique(cusUnique, cusName, cusBalance, cus_points)
                                    goods_tabbar.showDefaultGoodsTabBar()
                                }
                            }
                        }
                    }

                    CusRect {
                        width: 35 * dpi_ratio
                        height: width
                        anchors.right: rect_goods_order_list.right
                        anchors.top: rect_goods_order_list.top
                        anchors.rightMargin: -width / 4
                        anchors.topMargin: -height / 4
                        color: ST.color_transparent
                        visible: member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER
                        Image {
                            anchors.fill: parent
                            source: "/Images/yuebuzu_icon_solid.png"
                        }
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_DELETE))
                                    showDeleteConfirm()
                                else
                                    toast.openWarn(qsTr("无此权限"))
                            }
                            function showDeleteConfirm() {
                                window_root.loader_4_confirm.sourceComponent = delete_confirm
                                window_root.loader_4_confirm.item.open()
                            }
                            Component {
                                id: delete_confirm
                                PopupConfirm {
                                    title_name: qsTr("删除会员")
                                    message_info: qsTr("确认要删除此会员吗?")
                                    onConfirm: {
                                        if (cur_editing_member_unique == cusUnique)
                                            cur_editing_member_unique = null

                                        memberControl.reqDeleteMember4Qml(function (is_succ, data) {
                                            if (is_succ) {
                                                total_member_value_stored -= Number(cusBalance)
                                                var cus_name = cusName
                                                lm_member.remove(index)

                                                toast.openInfo(qsTr("删除会员:") + cus_name + qsTr("成功"))
                                            } else {
                                                toast.openInfo(qsTr("删除失败"))
                                            }
                                        }, cusUnique)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        MemberShipListOption {
            Layout.fillWidth: true
            Layout.preferredHeight: 160 * dpi_ratio
            color: ST.color_transparent
        }
    }
}
