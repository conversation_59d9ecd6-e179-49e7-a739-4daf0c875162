﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."
import ".."

Item {
    id: popup_root

    function open() {
        popup_root.visible = true
        resetAll()
        tf_points_num.focusMe()
    }
    function close() {
        popup_root.visible = false
        keyboard_c.closeAll()
        sigClose()
    }

    function resetAll() {
        tf_barcode.text = ""
        tf_points_num.text = ""
    }

    signal confirm(var point_num, var goods_barcode)
    signal cancel
    signal sigClose

    property string title_name: qsTr("积分兑换")

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 700 * dpi_ratio
        height: 550 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white_pure
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35 * dpi_ratio
                color: ST.color_white_pure

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 30 * dpi_ratio

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 60 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                color: ST.color_transparent
                                CusText {
                                    text: qsTr("剩余积分数:")
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent
                                CusText {
                                    text: cusPoints
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 60 * dpi_ratio
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                color: ST.color_transparent
                                CusText {
                                    text: qsTr("兑换积分数:")
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent
                                CusTextField {
                                    id: tf_points_num
                                    anchors.fill: parent
                                    is_clicked_select_all: true
                                    keyboard_status: 1
                                    validator: RegularExpressionValidator {
                                        regularExpression: /^\d+(\.\d+)?$/
                                    }
                                    digital_keyboard_x: 1320 * dpi_ratio
                                    digital_keyboard_y: 440 * dpi_ratio
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 60 * dpi_ratio
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                color: ST.color_transparent
                                CusText {
                                    text: qsTr("请输入条码:")
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent
                                CusTextField {
                                    id: tf_barcode
                                    anchors.fill: parent
                                    is_clicked_select_all: true
                                    keyboard_status: 1
                                    validator: RegularExpressionValidator {
                                        regularExpression: /^\d+(\.\d+)?$/
                                    }
                                    digital_keyboard_x: 1320 * dpi_ratio
                                    digital_keyboard_y: 440 * dpi_ratio
                                }
                            }
                        }
                    }
                    CusSpacer {
                        Layout.fillHeight: true
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 70 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusButton {
                                text: qsTr("确认")
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                onClicked: {
                                    if (Number(tf_points_num.text) > Number(cusPoints)) {
                                        toast.openWarn(qsTr("积分余额不足!"))
                                        return
                                    }
                                    confirm(tf_points_num.text, tf_barcode.text)
                                    close()
                                }
                            }
                            CusButton {
                                text: qsTr("取消")
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                onClicked: {
                                    cancel()
                                    close()
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    component KeyRect: CusRect {
        id: rect_key
        property string key_text: "0"
        signal pressKey(var key)
        color: ST.color_transparent

        border {
            width: 1 * dpi_ratio
            color: ST.color_grey_border
        }

        CusText {
            anchors.centerIn: parent
            text: rect_key.key_text
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {
                rect_key.pressKey(rect_key.key_text)
            }
        }

        onPressKey: {
            setInput(key)
        }
    }
    component KeyRect2: CusRect {
        id: rect_key2
        property string key_text: "0"
        signal pressKey(var key)

        property alias text: text

        CusText {
            id: text
            anchors.centerIn: parent
            text: rect_key2.key_text
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {
                rect_key2.pressKey(rect_key2.key_text)
            }
        }
    }
}
