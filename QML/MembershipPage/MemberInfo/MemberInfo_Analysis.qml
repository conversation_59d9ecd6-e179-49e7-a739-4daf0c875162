﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import SortFilterProxyModel 0.2
import EnumTool 1.0
import QtCharts 2.3

CusRect {
    id: member_analysis_Root
    color: ST.color_white_pure
    visible: false

    //时段分布
    property var dataRealTime: [{x:24 *dpi_ratio,y:2 *dpi_ratio}]
    property double scaleFactorX:0
    property double scaleFactorY:0
    //会员类型
    property int membersCredit:0
    property int memberStored:0
    property int memberRegular:0
    //会员积分
    property int cusPoint_0:0
    property int cusPoint_100:0
    property int cusPoint_200:0
    property int cusPoint_500:0
    //会员等级
    property int memberSilver:0
    property int memberGold:0
    property int memberDiamond:0
    property int memberBronze:0
    //会员储值金额
    property int cusBalance_0:0
    property int cusBalance_50:0
    property int cusBalance_100:0
    property int cusBalance_200:0
    property int cusBalance_500:0
    //总额统计
    property double creditMoney:0
    property int customerNumber:0
    property double storedMoney:0

    onVisibleChanged: {
        if (visible){
            reqCustomerRealTimeState()
            reqCustomerState()
            dataRealTime =  [{x:24,y:2}]
            member_analysis_Root.scaleFactorX = (650 - 25 -15)/ Math.max.apply(null, dataRealTime.map(function(item) { return item.x; }))
            member_analysis_Root.scaleFactorY = (200 - 25 - 5)/ Math.max.apply(null, dataRealTime.map(function(item) { return item.y; }))
        }
    }
    function reqCustomerRealTimeState(){
        memberControl.reqCustomerRealTimeStatQml(function (is_succ, data) {
            if (is_succ) {
                var json_doc = JSON.parse(data)
                var json_doc_data = json_doc["data"]
                logMgr.logDataInfo4Qml("会员管理-会员到店实时消费图")
                dataRealTime.pop()
                for (var i = 0; i < 24; i++) {
                    var newData = { x: i, y: json_doc_data[i] };
                    dataRealTime.push(newData);
                }
                member_analysis_Root.scaleFactorX = ((650 - 25 -15)*dpi_ratio) / Math.max.apply(null, dataRealTime.map(function(item) { return item.x; }))
                member_analysis_Root.scaleFactorY = ((200 - 25 - 5)*dpi_ratio)/ Math.max.apply(null, dataRealTime.map(function(item) { return item.y; }))
                //dataRealTime.push({x:24,y:2})
                canvas.requestPaint(); // 请求重新绘制
            }
        })
}


    function reqCustomerState(){
        memberControl.reqCustomerState4Qml(function (is_succ, data) {
            if (is_succ) {
                logMgr.logDataInfo4Qml("会员管理-会员统计图")
                var json_doc = JSON.parse(data)
                var json_doc_data = json_doc["data"]
                if(json_doc_data !== null){
                    if(json_doc_data.hasOwnProperty('creditMoney')){
                        creditMoney = json_doc_data.creditMoney
                    }
                    if(json_doc_data.hasOwnProperty('customerNumber')){
                        customerNumber = json_doc_data.customerNumber
                    }
                    if(json_doc_data.hasOwnProperty('storedMoney')){
                        storedMoney = json_doc_data.storedMoney
                        logMgr.logEvtInfo4Qml("json_doc_data.storedMoney2222:{}",storedMoney)
                    }
                    if(json_doc_data.hasOwnProperty('customerType')){
                        memberRegular = json_doc_data.customerType['普通会员']
                        membersCredit = json_doc_data.customerType['赊欠会员']
                        memberStored  = json_doc_data.customerType['储值会员']
                        pieChart.requestPaint();
                    }
                    if(json_doc_data.hasOwnProperty('customerLevel')){
                        memberSilver = json_doc_data.customerLevel['白银会员']
                        memberDiamond = json_doc_data.customerLevel['钻石会员']
                        memberBronze  = json_doc_data.customerLevel['铜牌会员']
                        memberGold  = json_doc_data.customerLevel['黄金会员']
                        canvasMemberType.requestPaint();
                    }
                    if(json_doc_data.hasOwnProperty('cusPoint')){
                        cusPoint_0 = json_doc_data.cusPoint['0-100']
                        cusPoint_100 = json_doc_data.cusPoint['100-200']
                        cusPoint_200  = json_doc_data.cusPoint['200-500']
                        cusPoint_500  = json_doc_data.cusPoint['500以上']
                        canvasCusPoint.requestPaint();
                    }
                    if(json_doc_data.hasOwnProperty('cusBalance')){
                        cusBalance_0 = json_doc_data.cusBalance['0-50']
                        cusBalance_50 = json_doc_data.cusBalance['50-100']
                        cusBalance_100 = json_doc_data.cusBalance['100-200']
                        cusBalance_200  = json_doc_data.cusBalance['200-500']
                        cusBalance_500  = json_doc_data.cusBalance['500以上']
                    }
                }

            }
        })
    }

    component CusRectId: CusRect {

        property alias text: rowRightId.text
        property alias textColor: rowRightCircle.color
        CusRect {
            id:rowRightCircle
            anchors.left: parent.left
            anchors.verticalCenter: parent.verticalCenter
            width: 16 *dpi_ratio
            height: 16 *dpi_ratio
            color:"#FFFFFF"
            radius: width/2
        }
        Text {
            id:rowRightId
            width:30 *dpi_ratio
            height: 10 *dpi_ratio
            anchors.left: rowRightCircle.right
            anchors.leftMargin: 10 *dpi_ratio
            anchors.verticalCenter: parent.verticalCenter
            horizontalAlignment: Text.AlignHCenter;
            verticalAlignment: Text.AlignVCenter;
            font.family: ST.fontFamilySiYan;
            font.pointSize: 8  * dpi_ratio;
            color: "#9E9E9E"
            text:""
        }
    }
    CusRect {
        anchors.fill: parent
        anchors.bottomMargin: 30 * dpi_ratio
        color: ST.color_transparent
            CusRect {
                id:titleRec
                Layout.fillWidth: true
                anchors.left: parent.left
                anchors.top: parent.top
                height: 100 *dpi_ratio
                CusText {
                    text: qsTr("店内会员分析")
                    anchors.left: parent.left
                    anchors.leftMargin: 30 *dpi_ratio
                    anchors.verticalCenter: parent.verticalCenter
                    font {
                        pixelSize: 30 * dpi_ratio
                        bold: true
                    }
                }
            }
            CusRect {
                id:totalAmount
                width:660 * dpi_ratio
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: titleRec.bottom
                height: 100 *dpi_ratio
                color:ST.color_transparent
                CusRect {
                    id:memberAmount
                    anchors.left: parent.left
                    anchors.top: titleRec.bottom
                    height: 100 *dpi_ratio
                    width:214 *dpi_ratio
                    CusRect {
                        id:memberAmount1
                        anchors.left: parent.left
                        anchors.top: parent.top
                        height: 50 *dpi_ratio
                        width:214 *dpi_ratio
                        color:"#F0FBF7"
                        Text {
                            anchors.verticalCenter: parent.verticalCenter;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            horizontalAlignment: Text.AlignHCenter;
                            verticalAlignment: Text.AlignVCenter;
                            font.family: ST.fontFamilySiYan;
                            font.pointSize: 18  * dpi_ratio;
                            color: "#000000"
                            text: customerNumber
                        }
                    }
                    CusRect {
                        id:memberAmount2
                        anchors.left: parent.left
                        anchors.top: memberAmount1.bottom
                        height: 50 *dpi_ratio
                        width:214 *dpi_ratio
                        color:"#F0FBF7"
                        Text {
                            anchors.verticalCenter: parent.verticalCenter;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            horizontalAlignment: Text.AlignHCenter;
                            verticalAlignment: Text.AlignVCenter;
                            font.family: ST.fontFamilySiYan;
                            font.pointSize: 18  * dpi_ratio;
                            color: "#000000"
                            text:qsTr("会员总数")
                        }
                    }
                }
                CusRect {
                    id:storeAmount
                    Layout.fillWidth: true
                    anchors.left: memberAmount.right
                    anchors.leftMargin: 9 * dpi_ratio
                    anchors.top: titleRec.bottom
                    height: 100 *dpi_ratio
                    width:214 *dpi_ratio
                    CusRect {
                        id:storeAmount1
                        anchors.left: parent.left
                        anchors.top: parent.top
                        height: 50 *dpi_ratio
                        width:214 *dpi_ratio
                        color:"#FFF9F0"
                        Text {
                            anchors.verticalCenter: parent.verticalCenter;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            horizontalAlignment: Text.AlignHCenter;
                            verticalAlignment: Text.AlignVCenter;
                            font.family: ST.fontFamilySiYan;
                            font.pointSize: 18  * dpi_ratio;
                            color: "#000000"
                            text: storedMoney
                        }
                    }
                    CusRect {
                        id:storeAmount2
                        anchors.left: parent.left
                        anchors.top: storeAmount1.bottom
                        height: 50 *dpi_ratio
                        width:214 *dpi_ratio
                        color:"#FFF9F0"
                        Text {
                            anchors.verticalCenter: parent.verticalCenter;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            horizontalAlignment: Text.AlignHCenter;
                            verticalAlignment: Text.AlignVCenter;
                            font.family: ST.fontFamilySiYan;
                            font.pointSize: 18  * dpi_ratio;
                            color: "#000000"
                            text:qsTr("储值总额")
                        }
                    }
                }
                CusRect {
                    id:creditAmount
                    Layout.fillWidth: true
                    anchors.left: storeAmount.right
                    anchors.leftMargin: 9 * dpi_ratio
                    anchors.top: titleRec.bottom
                    height: 100 *dpi_ratio
                    width:214 *dpi_ratio
                    CusRect {
                        id:creditAmount1
                        anchors.left: parent.left
                        anchors.top: parent.top
                        height: 50 *dpi_ratio
                        width:214 *dpi_ratio
                        color:"#FFF3F3"
                        Text {
                            anchors.verticalCenter: parent.verticalCenter;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            horizontalAlignment: Text.AlignHCenter;
                            verticalAlignment: Text.AlignVCenter;
                            font.family: ST.fontFamilySiYan;
                            font.pointSize: 18  * dpi_ratio;
                            color: "#000000"
                            text:  creditMoney
                        }
                    }
                    CusRect {
                        id:creditAmount2
                        anchors.left: parent.left
                        anchors.top: creditAmount1.bottom
                        height: 50 *dpi_ratio
                        width:214 *dpi_ratio
                        color:"#FFF3F3"
                        Text {
                            anchors.verticalCenter: parent.verticalCenter;
                            anchors.horizontalCenter: parent.horizontalCenter;
                            horizontalAlignment: Text.AlignHCenter;
                            verticalAlignment: Text.AlignVCenter;
                            font.family: ST.fontFamilySiYan;
                            font.pointSize: 18  * dpi_ratio;
                            color: "#000000"
                            text:qsTr("赊欠总额")
                        }
                    }
                }

            }
            CusRect {
                id:rowPicture1
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: totalAmount.bottom
                anchors.topMargin: 10 * dpi_ratio
                height: 200 *dpi_ratio
                width: 660 * dpi_ratio
                color:ST.color_transparent
                CusRect {
                    id:rowPicture11
                    anchors.left: parent.left
                    height: 200 *dpi_ratio
                    width:325 *dpi_ratio
                    // 会员类型占比
                    property var categories: [
                        { name:qsTr("赊欠会员"),value: membersCredit, color: "#0098FA" },
                        { name:qsTr("储值会员"),value: memberStored, color: "#0CD9B5" },
                        { name:qsTr("普通会员"),value: memberRegular, color: "#3B72AD" }
                    ]

                    CusRect {
                        id:rowPicture11Title
                        anchors.left: parent.left
                        anchors.top: parent.top
                        width:325 *dpi_ratio
                        height: 30 *dpi_ratio
                        color:"#F4F8FF"
                        Text {
                            anchors.left: parent.left
                            anchors.leftMargin: 20 * dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            font.family: ST.fontFamilySiYan;
                            font.pointSize: 18  * dpi_ratio;
                            color: "#000000"
                            text:qsTr("会员类型占比")
                        }
                    }
                    CusRect {
                        id:rowPicturePieSeries
                        anchors.left: parent.left
                        anchors.top: rowPicture11Title.bottom
                        width:175 *dpi_ratio
                        height: 170 *dpi_ratio
                        color:"#F4F8FF"
                        Canvas {
                            id: pieChart
                            width: 170 *dpi_ratio
                            height: 170 *dpi_ratio

                            // 绘制饼状图
                            onPaint: {
                                var ctx = getContext("2d");
                                var centerX = width / 2;
                                var centerY = height / 2;
                                var radius = Math.min(centerX, centerY) - 20; // 减去一些边距以确保饼状图不会触及边缘
                                var startAngle = 0;
                                var total = rowPicture11.categories.reduce(function(sum, item) { return sum + item.value; }, 0);

                                // 清除画布
                                ctx.clearRect(0, 0, width, height);

                                // 绘制每个扇形
                                for (var i = 0; i < rowPicture11.categories.length; i++) {
                                    var sliceAngle = (rowPicture11.categories[i].value / total) * 2 * Math.PI;
                                    ctx.beginPath();
                                    ctx.moveTo(centerX, centerY);
                                    ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle);
                                    ctx.closePath();
                                    ctx.fillStyle = rowPicture11.categories[i].color;
                                    ctx.fill();
                                    startAngle += sliceAngle;
                                }
                            }
                        }
                        }
                    CusRect {
                        id:rowPicturePieSeriesRight
                        anchors.left: rowPicturePieSeries.right
                        anchors.top: rowPicturePieSeries.top
                        width:150 *dpi_ratio
                        height: 170 *dpi_ratio
                        color:"#F4F8FF"
                        CusRectId
                        {
                            id:rowPieSeriesRight1
                            anchors.left: parent.left
                            anchors.leftMargin: 20 *dpi_ratio
                            anchors.top: parent.top
                            anchors.topMargin: 30 * dpi_ratio
                            width:100 *dpi_ratio
                            height: 20 *dpi_ratio
                            text:rowPicture11.categories[0].name
                            color:"#F4F8FF"
                            visible:rowPicture11.categories.length > 0
                            textColor:rowPicture11.categories[0].color
                        }
                        CusRectId
                        {
                            id:rowPieSeriesRight2
                            anchors.left: rowPieSeriesRight1.left
                            anchors.top: rowPieSeriesRight1.bottom
                            anchors.topMargin: 5 * dpi_ratio
                            width:100 *dpi_ratio
                            height: 20 *dpi_ratio
                            text:rowPicture11.categories[1].name
                            color:"#F4F8FF"
                            visible:rowPicture11.categories.length > 1
                            textColor:rowPicture11.categories[1].color
                        }
                        CusRectId
                        {
                            id:rowPieSeriesRight3
                            anchors.left: rowPieSeriesRight2.left
                            anchors.top: rowPieSeriesRight2.bottom
                            anchors.topMargin: 5 * dpi_ratio
                            width:100 *dpi_ratio
                            height: 20 *dpi_ratio
                            text:rowPicture11.categories[2].name
                            color:"#F4F8FF"
                            visible:rowPicture11.categories.length > 2
                            textColor:rowPicture11.categories[2].color
                        }
//                        CusRectId
//                        {
//                            id:rowPieSeriesRight4
//                            anchors.left: rowPieSeriesRight3.left
//                            anchors.top: rowPieSeriesRight3.bottom
//                            anchors.topMargin: 5 * dpi_ratio
//                            width:100 *dpi_ratio
//                            height: 20 *dpi_ratio
//                            text:rowPicture11.categories[3].name
//                            color:"#F4F8FF"
//                            visible:rowPicture11.categories.length > 3
//                            textColor:rowPicture11.categories[3].color
//                        }

                    }

                }
                CusRect {
                    id:rowPicture12
                    anchors.left: rowPicture11.right
                    anchors.leftMargin: 10 *dpi_ratio
                    height: 200 *dpi_ratio
                    width:325 *dpi_ratio
                    CusRect {
                        id:rowPicture12Title
                        anchors.left: parent.left
                        anchors.top: parent.top
                        width:325 *dpi_ratio
                        height: 30 *dpi_ratio
                        color:"#F0FBF7"
                        Text {
                            anchors.left: parent.left
                            anchors.leftMargin: 20 * dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            font.family: ST.fontFamilySiYan;
                            font.pointSize: 18  * dpi_ratio;
                            color: "#000000"
                            text:qsTr("储值金额占比")
                        }
                    }
                    CusRect {
                        id: rowBarChart
                        anchors.left: parent.left
                        anchors.top: rowPicture12Title.bottom
                        width: 325 * dpi_ratio
                        height: 170 * dpi_ratio
                        color: "#F0FBF7"

                        Canvas {
                            id: barChart
                            width: 325 *dpi_ratio
                            height: 150 *dpi_ratio

                            property var categories: [
                                { name:"0-50",value: cusBalance_0, color: "#0098FA" },
                                { name:"50-100",value: cusBalance_50, color: "#0CD9B5" },
                                { name:"100-200",value: cusBalance_100, color: "#3B72AD" },
                                { name:"200-500",value: cusBalance_200, color: "#DDC026" },
                                { name:qsTr("500以上"),value: cusBalance_500, color: "#E0457B" }
                            ]
                            property int xAxisHeight: 20 *dpi_ratio
                            property int yAxisWidth: 50 *dpi_ratio
                            property int barSpacing: 5 *dpi_ratio
                            property int barHeights: 10 *dpi_ratio
                            property int barHeight: (height - xAxisHeight - barSpacing * (categories.length - 1)) / categories.length
                            property int maxValue: Math.max.apply(null, categories.map(function(item) { return item.value; }))

                            onPaint: {
                                var ctx = getContext("2d");
                                ctx.clearRect(0, 0, width, height);

                                // 绘制Y轴（现在是高度轴）
                                ctx.lineWidth = 1 *dpi_ratio;
                                ctx.beginPath();
                                ctx.moveTo(xAxisHeight, 0);
                                ctx.lineTo(xAxisHeight, height+10);
                                ctx.strokeStyle = "#D8D8D8";
                                ctx.stroke();

                                // 绘制X轴
                                ctx.lineWidth = 3 *dpi_ratio;
                                ctx.beginPath();
                                ctx.moveTo(xAxisHeight, height);
                                ctx.lineTo( width - xAxisHeight, height);
                                ctx.strokeStyle = "#D8D8D8";
                                ctx.stroke();

                                // 绘制条形图
                                var currentX = xAxisHeight;
                                for (var i = 0; i < categories.length; i++) {
                                    var barWidth = (categories[i].value / maxValue) *dpi_ratio  * (width  - 2 * xAxisHeight);
                                    ctx.beginPath();
                                    ctx.rect(21*dpi_ratio, barHeights, barWidth, barHeight);
                                    ctx.fillStyle = categories[i].color;
                                    ctx.fill();

                                    // 绘制数值
                                   var textX = 20 + barWidth / 2; // 文本中心的x坐标
                                   var textY = barHeights + (barHeight/1.5); // 文本的y坐标，假设在条形上方10个像素的位置
                                   ctx.font = "12px Arial"; // 设置字体和大小
                                   ctx.fillStyle = "black"; // 设置文本颜色
                                   ctx.textAlign = "center"; // 设置文本对齐方式为居中
                                   ctx.fillText(categories[i].value, textX, textY); // 绘制文本
                                   currentX += barWidth + barSpacing; // 更新下一个条形的起始x坐标
                                   barHeights += barHeight + barSpacing;
                                }
                            }
                        }
                    }


                }

            }
            CusRect {
                id:rowPicture2
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: rowPicture1.bottom
                anchors.topMargin: 10 * dpi_ratio
                height: 200 *dpi_ratio
                width:660 * dpi_ratio
                color:ST.color_transparent
                CusRect {
                    id:rowPicture21
                    anchors.left: parent.left
                    height: 200 *dpi_ratio
                    width:325 *dpi_ratio

                    property var categories: [
                        { name:"0-100",value: cusPoint_0, color: "#0098FA" },
                        { name:"100-200",value: cusPoint_100, color: "#0CD9B5" },
                        { name:"200-500",value: cusPoint_200, color: "#3B72AD" },
                        { name:qsTr("500以上"),value: cusPoint_500, color: "#DDC026" }
                    ]
                    CusRect {
                        id:rowPicture21Title
                        anchors.left: parent.left
                        anchors.top: parent.top
                        width:325 *dpi_ratio
                        height: 30 *dpi_ratio
                        color:"#FFF9F0"
                        Text {
                            anchors.left: parent.left
                            anchors.leftMargin: 20 * dpi_ratio
                            anchors.top:parent.top
                            anchors.topMargin:0 * dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            font.family: ST.fontFamilySiYan;
                            font.pointSize: 18  * dpi_ratio;
                            color: "#000000"
                            text:qsTr("会员积分占比")
                        }
                    }
                    CusRect {
                        id:rowPicture21QPieSeries
                        anchors.left: parent.left
                        anchors.top: rowPicture21Title.bottom
                        width:175 *dpi_ratio
                        height: 170 *dpi_ratio
                        color:"#FFF9F0"

                        Canvas {
                            id:canvasCusPoint
                            anchors.fill: parent

                            property int totalValue: rowPicture21.categories.reduce(function(sum, cat) { return sum + cat.value; }, 0)
                            property double innerRadius: 45 *dpi_ratio// 环形图内圆的半径
                            property double outerRadius: 75 *dpi_ratio// 环形图外圆的半径

                            onPaint: {
                                var ctx = getContext("2d");
                                var startAngle = 0;

                                // 绘制中心空白区域
                                ctx.beginPath();
                                ctx.arc(width / 2, height / 2, innerRadius, 0, 2 * Math.PI);
                                ctx.fillStyle = "white";
                                ctx.fill();

                                // 绘制环形图的各个部分
                                for (var i = 0; i < rowPicture21.categories.length; i++) {
                                    if(i < 4){
                                        var category = rowPicture21.categories[i];
                                        var sliceAngle = (category.value / totalValue) * 2 * Math.PI;

                                        ctx.beginPath();
                                        ctx.moveTo(width / 2, height / 2);
                                        ctx.arc(width / 2, height / 2, outerRadius, startAngle, startAngle + sliceAngle);
                                        ctx.arc(width / 2, height / 2, innerRadius, startAngle + sliceAngle, startAngle, true); // 第二个arc逆时针绘制，形成闭合路径
                                        ctx.closePath();
                                        ctx.fillStyle = rowPicture21.categories[i].color;
                                        ctx.fill();
                                        startAngle += sliceAngle;
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        id:rowPicture21QPieSeriesRight
                        anchors.left: rowPicture21QPieSeries.right
                        anchors.top: rowPicture21QPieSeries.top
                        width:150 *dpi_ratio
                        height: 170 *dpi_ratio
                        color:"#FFF9F0"
                        CusRectId
                        {
                            id:rowRight1
                            anchors.left: parent.left
                            anchors.leftMargin: 20 *dpi_ratio
                            anchors.top: parent.top
                            anchors.topMargin: 30 * dpi_ratio
                            width:100 *dpi_ratio
                            height: 20 *dpi_ratio
                            text:rowPicture21.categories[0].name
                            color:"#FFF9F0"
                            visible:rowPicture21.categories.length > 0
                            textColor:rowPicture21.categories[0].color
                        }
                        CusRectId
                        {
                            id:rowRight2
                            anchors.left: rowRight1.left
                            anchors.top: rowRight1.bottom
                            anchors.topMargin: 5 * dpi_ratio
                            width:100 *dpi_ratio
                            height: 20 *dpi_ratio
                            text:rowPicture21.categories[1].name
                            color:"#FFF9F0"
                            visible:rowPicture21.categories.length > 1
                            textColor:rowPicture21.categories[1].color
                        }
                        CusRectId
                        {
                            id:rowRight3
                            anchors.left: rowRight2.left
                            anchors.top: rowRight2.bottom
                            anchors.topMargin: 5 * dpi_ratio
                            width:100 *dpi_ratio
                            height: 20 *dpi_ratio
                            text:rowPicture21.categories[2].name
                            color:"#FFF9F0"
                            visible:rowPicture21.categories.length > 2
                            textColor:rowPicture21.categories[2].color
                        }
                        CusRectId
                        {
                            id:rowRight4
                            anchors.left: rowRight3.left
                            anchors.top: rowRight3.bottom
                            anchors.topMargin: 5 * dpi_ratio
                            width:100 *dpi_ratio
                            height: 20 *dpi_ratio
                            text:rowPicture21.categories[3].name
                            color:"#FFF9F0"
                            visible:rowPicture21.categories.length > 3
                            textColor:rowPicture21.categories[3].color
                        }

                    }
                }
                CusRect {
                    id:rowPicture22
                    anchors.left: rowPicture21.right
                    anchors.leftMargin: 10 *dpi_ratio
                    height: 200 *dpi_ratio
                    width:325 *dpi_ratio
                    CusRect
                    {
                        id:rowPicture22Title
                        anchors.left: parent.left
                        anchors.top: parent.top
                        width:325 *dpi_ratio
                        height: 30 *dpi_ratio
                        color:"#D6E5FF"
                        Text {
                            anchors.left: parent.left
                            anchors.leftMargin: 20 * dpi_ratio
                            anchors.top:parent.top
                            anchors.topMargin:0 * dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            font.family: ST.fontFamilySiYan;
                            font.pointSize: 18  * dpi_ratio;
                            color: "#000000"
                            text:qsTr("会员等级占比")
                        }
                    }
                    CusRect {
                        id:rowPicture22PieSeries
                        anchors.left: parent.left
                        anchors.top: rowPicture22Title.bottom
                        width:325 *dpi_ratio
                        height: 170 *dpi_ratio
                        color:"#D6E5FF"
                        Canvas {
                            id: canvasMemberType
                            anchors.fill: parent                            

                            property var categories: [
                                { name:qsTr("铜牌会员"),value: memberBronze, color: "#DDC026" },
                                { name:qsTr("白银会员"),value: memberSilver, color: "#0098FA" },
                                { name:qsTr("黄金会员"),value: memberGold, color: "#0CD9B5" },
                                { name:qsTr("钻石会员"),value: memberDiamond, color: "#3B72AD" }
                            ]

                            property int totalValue: categories.reduce(function(sum, cat) { return sum + cat.value; }, 0)
                            property double innerRadius: 100 *dpi_ratio// 环形图内圆的半径
                            property double outerRadius: 140 *dpi_ratio// 环形图外圆的半径
                            onPaint: {
                                var ctx = getContext("2d");
                                var startAngle = -Math.PI; // 从0度开始
                                var sliceAngle;

                                ctx.clearRect(0, 0, width, height);

                                // 绘制环形图的各个部分
                                for (var i = 0; i < categories.length; i++) {
                                    var category = categories[i];
                                    sliceAngle = (category.value / totalValue) * Math.PI; // 每个扇形的角度（180度范围内）

//                                    if (i === categories.length - 1) {
//                                        // 最后一个扇形需要填满剩余的180度范围
//                                        sliceAngle = 0 - startAngle;
//                                    }

                                    ctx.beginPath();
                                    ctx.moveTo(width / 2, height -10 *dpi_ratio);
                                    ctx.arc(width / 2, height -10 *dpi_ratio, outerRadius, startAngle, startAngle + sliceAngle, false);
                                    ctx.arc(width / 2, height -10 *dpi_ratio, innerRadius, startAngle + sliceAngle, startAngle, true);
                                    ctx.closePath();

                                    ctx.fillStyle = category.color;
                                    ctx.fill();

                                    startAngle += sliceAngle;
                                }
                            }
                        }
                    }
                }
            }
            CusRect {
                id:rowPicture3
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: rowPicture2.bottom
                anchors.topMargin: 10 * dpi_ratio
                height: 240 *dpi_ratio
                width:660 * dpi_ratio
                CusRect {
                    id:rowPicture3Title
                    anchors.left: parent.left
                    anchors.top: parent.top
                    width:660 *dpi_ratio
                    height: 30 *dpi_ratio
                    color:"#F0FBF7"
                    Text {
                        anchors.left: parent.left
                        anchors.leftMargin: 20 * dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 18  * dpi_ratio;
                        color: "#000000"
                        text:qsTr("会员到店时段分布")
                    }
                }
                CusRect {
                    id:rowlineSeries
                    anchors.left: parent.left
                    anchors.top: rowPicture3Title.bottom
                    width:660 *dpi_ratio
                    height: 210 *dpi_ratio
                    color:"#F0FBF7"

                    Canvas {
                        id: canvas
                        width:650 *dpi_ratio
                        height: 200 *dpi_ratio

                        property int xAxisHeight: 25 *dpi_ratio
                        property int yAxisWidth: 25 *dpi_ratio

                        anchors.fill: parent
                        onPaint: {

                            var ctx = getContext("2d");
                            ctx.reset();

                            // 绘制X轴
                            ctx.lineWidth = 3 *dpi_ratio;
                            ctx.beginPath();
                            ctx.moveTo(yAxisWidth, height -xAxisHeight);
                            ctx.lineTo(width, height - xAxisHeight);
                            ctx.strokeStyle = "#D8D8D8";
                            ctx.stroke();

                            // 绘制Y轴
                            ctx.lineWidth = 3 *dpi_ratio;
                            ctx.beginPath();
                            ctx.moveTo(yAxisWidth, height - xAxisHeight );
                            ctx.lineTo(yAxisWidth, yAxisWidth - 10 *dpi_ratio);
                            ctx.strokeStyle = "#D8D8D8";
                            ctx.stroke();
                            //绘制下方色彩
                              ctx.beginPath();
                              ctx.moveTo(yAxisWidth, height - xAxisHeight - dataRealTime[0].y * scaleFactorY);
                              for (var i = 0; i < dataRealTime.length - 1; ++i) {
                                  var x1 = yAxisWidth + dataRealTime[i].x * member_analysis_Root.scaleFactorX;
                                  var y1 = height - xAxisHeight - dataRealTime[i].y * scaleFactorY;
                                  var x2 = yAxisWidth + dataRealTime[i + 1].x * member_analysis_Root.scaleFactorX;
                                  var y2 = height - xAxisHeight - dataRealTime[i + 1].y * scaleFactorY;
                                  ctx.lineTo(x1, y1);
                                  ctx.lineTo(x2, y2);
                              }
                              ctx.lineTo(width -15 *dpi_ratio, height - xAxisHeight);
                              ctx.lineTo(yAxisWidth, height - yAxisWidth);
                              ctx.closePath();
                              ctx.fillStyle = "#D1F8F1";
                              ctx.fill();
                              ctx.beginPath();
                              ctx.moveTo(yAxisWidth, height - xAxisHeight - dataRealTime[0].y * scaleFactorY);
                              for (var i = 0; i < dataRealTime.length; ++i) {
                                  var x = yAxisWidth + dataRealTime[i].x * member_analysis_Root.scaleFactorX;
                                  var y = height - xAxisHeight - dataRealTime[i].y * scaleFactorY;
                                  ctx.lineTo(x, y);
                              }
                              ctx.strokeStyle = "#0CD9B5";
                              ctx.lineWidth = 2 *dpi_ratio;
                              ctx.stroke();
                              //刻度
                              for (var i = 0; i < dataRealTime.length; ++i) {
                                  var xPos = yAxisWidth + dataRealTime[i].x * member_analysis_Root.scaleFactorX;
                                  var tickLength = 5 *dpi_ratio;
                                  // Draw tick
                                  ctx.beginPath();
                                  ctx.moveTo(xPos, height - xAxisHeight);
                                  ctx.lineTo(xPos, height - xAxisHeight - tickLength);
                                  ctx.strokeStyle = "#FFF9F0";
                                  ctx.stroke();
                              }
                              // Draw x-axis values
                              ctx.font = "10px Arial";
                              ctx.fillStyle = "black";
                              for (var j = 0; j < dataRealTime.length; ++j) {
                                  var text = dataRealTime[j].x; // Format the value as needed
                                  var xPoS = yAxisWidth + dataRealTime[j].x * member_analysis_Root.scaleFactorX ;
                                  ctx.fillText(text, xPoS, height  ); // Adjust the y position for label placement
                              }

                          }
                    }
                }
            }
    }
}
