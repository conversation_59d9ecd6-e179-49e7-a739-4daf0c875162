﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import SortFilterProxyModel 0.2
import EnumTool 1.0
import SettingEnum 1.0
import "Popup"

CusRect {
    id: rect_goods_detail
    color: ST.color_white_pure
    visible: member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER

    property bool is_mini: false
    property bool is_ningyu: true
    property bool is_adminNingyu: false
    property bool is_member_recharge: false
    property bool is_member_exchange: false
    property bool is_member_point: false

    property var cusId: "0"
    property var cusId_hui: "0"
    property var cusId_chu: "0"
    property var cusBalance: 0.0 //账户余额
    property var cusName: "" //会员姓名
    property var cusPassword: "" //账号密码
    property var cusUnique: "" //会员卡号
    property var cusPhone: "" //手机号码
    property var cusBirthday: "" //生日
    property var regeditDate: "" //注册日期
    property var cusRebate:"" //会员赠送余额
    property var cusPoints: 0.0 //会员积分
    property var cusAddress: 0.0 //会员地址
    property var cusRemark: "" //备注信息
    property var cusType: "" //会员类型: 会 储
    property var powerCredit: 0
    property var creditLimit: 0
    property var creditLimit__show: powerCredit == 0 ? 0 : creditLimit


    function clearMemberInfo() {
        cusId = ""
        cusId_hui = ""
        cusBalance = ""
        cusName = ""
        cusPassword = ""
        cusUnique = ""
        cusPhone = ""
        cusBirthday = ""
        regeditDate = ""
        cusPoints = ""
        cusAddress = ""
        cusRemark = ""
        cusType = ""
        powerCredit = 0
        creditLimit = 0
    }

    function reqMemberInfoDetail(member_unique) {
        if(settingTool.getSetting(SettingEnum.NINGYU_ADMIN_ACCOUNT) == settingTool.getSetting(SettingEnum.STORE_ID)){
            is_adminNingyu = true;
            logMgr.logEvtInfo4Qml("宁宇主管理账号");
        }
        if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
            is_ningyu = false
        }
        if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_RECHARGE)){
            is_member_recharge = true;
        }
        if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_EXCHANGE)){
            is_member_exchange = true;
        }
        if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_POINT_UPDATE)){
            is_member_point = true;
        }

        if (member_unique == "") {
            member_detail.clearAll()
            return
        }
        memberControl.reqMemberInfoDetail4Qml(function (is_succ, data) {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            var json_doc_data1 = json_doc.data1

            var data_hui
            var data_chu

            if (json_doc_data == null) {
                data_hui = json_doc_data1
                data_chu = json_doc_data1
                cusType = json_doc_data1.cusType
            } else if (json_doc_data1 == null) {
                data_hui = json_doc_data
                data_chu = json_doc_data
                cusType = json_doc_data.cusType
            } else {
                cusType = "会,储"
                if (json_doc_data.cusType == "会") {
                    data_hui = json_doc_data
                    data_chu = json_doc_data1
                } else {
                    data_hui = json_doc_data1
                    data_chu = json_doc_data
                }
            }
            //            total_stored_value_amount = 0
            //            total_number_of_members = 0
            //            lm_member_list.clear()
            //            for (var i = 0; i < json_doc_data.length; ++i) {
            //                lm_member_list.append(json_doc_data[i])
            //                total_stored_value_amount += json_doc_data[i].cusBalance
            //                ++total_number_of_members
            //            }
            //            member_detail._gift_balance // = data_chu.cusBalance //TODO 需完成
            cusId = data_chu.cusId
            cur_editing_member_id = data_chu.cusId
            cur_editing_member_phone = data_chu.cusPhone
            cusId_hui = data_hui.cusId
            cusId_chu = data_chu.cusId
            cusBalance = data_chu.cusBalance
            cusName = data_hui.cusName
            cusPassword = data_chu.cusPassword
            cusUnique = data_chu.cusUnique
            cusPhone = data_chu.cusPhone
            cusBirthday = data_chu.cusBirthday
            regeditDate = data_chu.regeditDate
            cusRebate = data_chu.cusRebate
            cusPoints = data_hui.cusPoints
            cusAddress = data_chu.cusAddress
            cusRemark = data_chu.cusRemark
            powerCredit = data_chu.powerCredit
            creditLimit = data_chu.creditLimit
        }, member_unique)
    }

    function refreshMemberInfo() {
        reqMemberInfoDetail(cur_editing_member_unique)
    }

    function saveMemberInfo() {

        var data = {
            "cusUnique"//会员唯一ID
            : cusUnique,
            "cusName"//会员名
            : cusName,
            "cusPhone"//会员电话
            : cusPhone,
            "cusType"//会员类型
            : cusType,
            "cus_remark"//会员备注
            : cusRemark,
            "cusPassword"//会员密码
            : cusPassword
        }

        memberControl.reqChangeMemberInfoByMemberId4Qml(function (is_succ, data) {
            if (is_succ) {
                member_ship_list.refreshMember()
                toast.openInfo(qsTr("修改成功"))
            } else {
                toast.openInfo(qsTr("修改失败"))
            }
        }, JSON.stringify(data))
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 15 * dpi_ratio
        anchors.bottomMargin: 30 * dpi_ratio
        anchors.leftMargin: 30 * dpi_ratio
        anchors.rightMargin: 30 * dpi_ratio

        Flickable {
            Layout.fillHeight: true
            Layout.fillWidth: true
            contentWidth: width
            contentHeight: 850 * dpi_ratio
            clip: true

            CusRect {
                anchors.fill: parent
                color: ST.color_white_pure

                ColumnLayout {
                    anchors.fill: parent
                    anchors.topMargin: 30 * dpi_ratio

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 70 * dpi_ratio
                        color: ST.color_transparent
                        Layout.alignment: Qt.AlignTop

                        CusText {
                            text: qsTr("会员详情")
                            anchors.verticalCenter: parent.verticalCenter
                            font {
                                pixelSize: 30 * dpi_ratio
                                bold: true
                            }
                        }
                    }

                    CusRect {
                        id: rect_new_member_contain
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 24 * dpi_ratio

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("会员卡号:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusText {
                                            text: cusUnique
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("会员姓名:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            anchors.fill: parent
                                            text: cusName
                                            is_clicked_select_all: true
                                            onTextEdited: {
                                                cusName = text
                                            }
                                            onAccepted: {
                                                search_bar_4_member.forceActiveFocus()
                                            }

                                            normal_keyboard_x: 730 * dpi_ratio
                                            normal_keyboard_y: 560 * dpi_ratio

                                            digital_keyboard_x: 730 * dpi_ratio
                                            digital_keyboard_y: 430 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("手机号码:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            anchors.fill: parent
                                            text: cusPhone
                                            keyboard_status: 1
                                            is_clicked_select_all: true
                                            onTextEdited: {
                                                cusPhone = text
                                            }
                                            onAccepted: {
                                                search_bar_4_member.forceActiveFocus()
                                            }
                                            validator: RegularExpressionValidator {
                                                regularExpression: /^1\d{0,10}$/
                                            }
                                            normal_keyboard_x: 730 * dpi_ratio
                                            normal_keyboard_y: 560 * dpi_ratio

                                            digital_keyboard_x: 730 * dpi_ratio
                                            digital_keyboard_y: 430 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                color: ST.color_transparent
                                visible: cusType == "会" || cusType == "会,储"
                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("会员积分:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusText {
                                            text: cusPoints
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 110 * dpi_ratio
                                        color: ST.color_transparent
                                        visible:is_member_point
                                        CusButton {
                                            anchors.fill: parent
                                            text: qsTr("增加积分")
                                            onClicked: {
                                                if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_POINT_UPDATE))
                                                    showChangePointsConfirm()
                                                else
                                                    toast.openWarn(qsTr("无此权限"))
                                            }
                                            function showChangePointsConfirm() {
                                                window_root.loader_4_confirm.sourceComponent = compo_points_add_confirm
                                                window_root.loader_4_confirm.item.open()
                                            }
                                            Component {
                                                id: compo_points_add_confirm

                                                PopupPointChange {
                                                    id: points_chage_confirm
                                                    title_name: qsTr("增加积分")
                                                    message_info: qsTr("请输入积分数量")
                                                    max_point: Number(100000)
                                                    cur_point: Number(cusPoints)

                                                    onConfirm: {
                                                        memberControl.reqChangeMemberPointsByMemberId4Qml(function (is_succ, data) {
                                                            var json_doc = JSON.parse(data)

                                                            if (is_succ) {
                                                                member_ship_list.refreshMember()
                                                                refreshMemberInfo()
                                                                toast.openInfo(json_doc.msg)
                                                            } else {
                                                                toast.openWarn(qsTr("增加失败"))
                                                            }
                                                        }, cusId_hui, str_num)
                                                    }
                                                    onSigClose: {
                                                        search_bar_4_member.forceActiveFocus()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 110 * dpi_ratio
                                        color: ST.color_transparent
                                        visible:is_member_point
                                        CusButton {
                                            anchors.fill: parent
                                            text: qsTr("减少积分")
                                            onClicked: {
                                                if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_POINT_UPDATE))
                                                    showChangePointsConfirm()
                                                else
                                                    toast.openWarn(qsTr("无此权限"))
                                            }
                                            function showChangePointsConfirm() {
                                                window_root.loader_4_confirm.sourceComponent = compo_points_subtract_confirm
                                                window_root.loader_4_confirm.item.open()
                                            }
                                            Component {
                                                id: compo_points_subtract_confirm

                                                PopupPointChange {
                                                    id: points_chage_confirm
                                                    title_name: qsTr("减少积分")
                                                    message_info: qsTr("请输入积分数量")
                                                    max_point: Number(cusPoints)
                                                    cur_point: Number(cusPoints)
                                                    onConfirm: {
                                                        memberControl.reqChangeMemberPointsByMemberId4Qml(function (is_succ, data) {
                                                            var json_doc = JSON.parse(data)

                                                            if (is_succ) {
                                                                member_ship_list.refreshMember()
                                                                refreshMemberInfo()
                                                                toast.openInfo(json_doc.msg)
                                                            } else {
                                                                toast.openWarn(qsTr("减少失败"))
                                                            }
                                                        }, cusId_hui, -str_num)
                                                    }
                                                    onSigClose: {
                                                        search_bar_4_member.forceActiveFocus()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 80 * dpi_ratio
                                        color: ST.color_transparent
                                        visible:is_member_exchange
                                        CusButton {
                                            anchors.fill: parent
                                            text: qsTr("兑换")
                                            onClicked: {
                                                if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_POINT_UPDATE))
                                                    showPointsExchangeConfirm()
                                                else
                                                    toast.openWarn(qsTr("无此权限"))
                                            }

                                            function showPointsExchangeConfirm() {
                                                window_root.loader_4_confirm.sourceComponent = compo_points_exchange_confirm
                                                window_root.loader_4_confirm.item.open()
                                            }

                                            Component {
                                                id: compo_points_exchange_confirm

                                                PopupMemberExchange {
                                                    id: points_chage_confirm
                                                    title_name: qsTr("兑换积分")
                                                    onConfirm: {
                                                        payMethodControl.reqMemberPointExchange4Qml(function (is_succ, data) {
                                                            if (is_succ) {
                                                                var json_doc = JSON.parse(data)
                                                                toast.openInfo(qsTr("兑换成功"))
                                                                refreshMemberInfo()
                                                                member_ship_list.refreshMember()
                                                            } else {
                                                                toast.openError(qsTr("兑换失败"))
                                                            }
                                                        }, cusUnique, point_num, goods_barcode)
                                                    }
                                                    onSigClose: {
                                                        search_bar_4_member.forceActiveFocus()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("账户余额:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusText {
                                            text: cusBalance
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 110 * dpi_ratio
                                        color: ST.color_transparent
                                        visible: cusBalance !== 0 &&is_member_recharge
                                        CusButton {
                                            anchors.fill: parent
                                            text: qsTr("退费")
                                            color: ST.color_yellow
                                            onClicked: {
                                                if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_RECHARGE))
                                                    showMemberRefundConfirm()
                                                else
                                                    toast.openWarn(qsTr("无此权限"))
                                            }

                                            function showMemberRefundConfirm() {
                                                window_root.loader_4_member_refund.sourceComponent = compo_popup_member_refund
                                                window_root.loader_4_member_refund.item.open()
                                            }

                                            Component {
                                                id: compo_popup_member_refund
                                                PopupMemberRefund {
                                                    id: popup_member_refund
                                                    onConfirm: {
                                                        if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
                                                            payMethodControl.reqRefundMemberByChangeNingyuQml(function (pay_status, pay_type, detail_json) {
                                                                var json_doc = JSON.parse(detail_json)
                                                                logMgr.logEvtInfo4Qml("宁宇会员退费pay_status:{}",pay_status);
                                                                if (pay_status == EnumTool.PAY_STATUS__SUCCESS) {
                                                                    member_ship_list.refreshMember()
                                                                    refreshMemberInfo()
                                                                    if(json_doc.msg == "会员退费成功"){
                                                                        toast.openInfo(qsTr("会员退费成功"))
                                                                    }
                                                                } else {
                                                                    if(json_doc.msg == "会员退费失败"){
                                                                        toast.openInfo(qsTr("会员退费失败"))
                                                                    }
                                                                    window_root.loader_4_member_refund.item.close()
                                                                }
                                                            }, cusId_chu, cusBalance)
                                                        }else{
                                                            payMethodControl.reqRefundMemberByChange4Qml(function (pay_status, pay_type, detail_json) {
                                                                var json_doc = JSON.parse(detail_json)
                                                                logMgr.logEvtInfo4Qml("pay_status:{}",pay_status);
                                                                if (pay_status == EnumTool.PAY_STATUS__SUCCESS) {
                                                                    member_ship_list.refreshMember()
                                                                    refreshMemberInfo()
                                                                    if(json_doc.msg == "会员退费成功"){
                                                                        toast.openInfo(qsTr("会员退费成功"))
                                                                    }
                                                                } else {
                                                                    if(json_doc.msg == "会员退费失败"){
                                                                        toast.openInfo(qsTr("会员退费失败"))
                                                                    }
                                                                    window_root.loader_4_member_refund.item.close()
                                                                }
                                                            }, cusUnique, cusBalance)
                                                        }
                                                    }
                                                    onSigClose: {
                                                        search_bar_4_member.forceActiveFocus()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    CusButtonManual {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 110 * dpi_ratio
                                        color_font: ST.color_white_pure
                                        color_font2: ST.color_red
                                        color_background: color_font2
                                        color_background2: color_font
                                        visible:is_member_recharge
                                        text: qsTr("消费记录")

                                        is_check_btn: true
                                        is_checked: is_need_show_member_order

                                        onSigClicked: {
                                            is_need_show_member_order = !is_need_show_member_order
                                            is_reqCus_record = true
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 80 * dpi_ratio
                                        color: ST.color_transparent
                                        visible: is_member_recharge
                                        CusButton {
                                            anchors.fill: parent
                                            text: qsTr("充值")
                                            color: ST.color_yellow

                                            onClicked: {
                                                if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_RECHARGE))
                                                    showMemberRechargeConfirm(is_adminNingyu)
                                                else
                                                    toast.openWarn(qsTr("无此权限"))
                                            }

                                            function showMemberRechargeConfirm(is_adminNingyu) {
                                                window_root.loader_4_member_recharge.sourceComponent = compo_popup_member_recharge
                                                window_root.loader_4_member_recharge.item.open(is_adminNingyu)
                                            }

                                            Component {
                                                id: compo_popup_member_recharge
                                                PopupMemberRecharge {
                                                    id: popup_member_recharge
                                                    onSigClose: {
                                                        refreshMemberInfo()
                                                        search_bar_4_member.forceActiveFocus()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                visible: !rect_goods_detail.is_mini
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("赠送余额:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusText {
                                            text: cusRebate
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                visible: !rect_goods_detail.is_mini
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("账号密码:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            anchors.fill: parent
                                            echoMode: TextInput.Password
                                            text: cusPassword
                                            is_clicked_select_all: true
                                            onTextEdited: {
                                                cusPassword = text
                                            }
                                            onAccepted: {
                                                search_bar_4_member.forceActiveFocus()
                                            }
                                            normal_keyboard_x: 730 * dpi_ratio
                                            normal_keyboard_y: 560 * dpi_ratio

                                            digital_keyboard_x: 730 * dpi_ratio
                                            digital_keyboard_y: 430 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                //                                    visible: !rect_goods_detail.is_mini
                                visible: false
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("会员等级:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusComboBox {
                                            anchors.fill: parent
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                //                                    visible: !rect_goods_detail.is_mini
                                visible: false
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("会员生日:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            anchors.fill: parent
                                            text: cusBirthday
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                //                                    visible: !rect_goods_detail.is_mini
                                visible: false
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("会员性别:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        id: rect_gender

                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent

                                        property int gender: 0

                                        RowLayout {
                                            anchors.fill: parent

                                            CusSpacer {
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                //                                    visible: !rect_goods_detail.is_mini
                                visible: false
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("会员地址:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            anchors.fill: parent
                                            text: cusAddress
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                visible: !rect_goods_detail.is_mini
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("欠款限额:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            anchors.fill: parent
                                            text: creditLimit__show
                                            is_clicked_select_all: true

                                            normal_keyboard_x: 730 * dpi_ratio
                                            normal_keyboard_y: 560 * dpi_ratio

                                            digital_keyboard_x: 730 * dpi_ratio
                                            digital_keyboard_y: 430 * dpi_ratio

                                            enabled: false
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                visible: !rect_goods_detail.is_mini
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("备注信息:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusTextField {
                                            anchors.fill: parent
                                            text: cusRemark
                                            is_clicked_select_all: true
                                            onTextEdited: {
                                                cusRemark = text
                                            }
                                            onAccepted: {
                                                search_bar_4_member.forceActiveFocus()
                                            }
                                            normal_keyboard_x: 730 * dpi_ratio
                                            normal_keyboard_y: 560 * dpi_ratio

                                            digital_keyboard_x: 730 * dpi_ratio
                                            digital_keyboard_y: 430 * dpi_ratio
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                visible: !rect_goods_detail.is_mini
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 120 * dpi_ratio
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("注册日期:")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        CusText {
                                            text: regeditDate
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        color: ST.color_transparent
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 55 * dpi_ratio
                                Layout.alignment: Qt.AlignTop
                                color: ST.color_transparent
                                visible: false
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: height
                                        color: ST.color_transparent

                                        Image {
                                            anchors.centerIn: parent
                                            width: 20 * dpi_ratio
                                            height: width
                                            source: rect_goods_detail.is_mini ? "/Images/down.png" : "/Images/up.png"
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent

                                        CusText {
                                            text: rect_goods_detail.is_mini ? qsTr("展开信息 ") : qsTr("收起信息")
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                }
                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        rect_goods_detail.is_mini = !rect_goods_detail.is_mini
                                    }
                                }
                            }

                            CusSpacer {
                                Layout.fillHeight: true
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            id: rect_save_goods_kind
            Layout.preferredHeight: 80 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent
                anchors.leftMargin: 30 * dpi_ratio
                anchors.rightMargin: 30 * dpi_ratio

                CusButton {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    text: qsTr("取消")
                    color: ST.color_grey_btn
                    onClicked: {
                        cur_editing_member_unique = null
                    }
                }
                CusSpacer {
                    Layout.fillWidth: true
                }

                CusButton {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    text: qsTr("保存会员")
                    onClicked: {
                        if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_UPDATE)) {
                            saveMemberInfo()
                        } else {
                            toast.openWarn(qsTr("无此权限"))
                        }
                    }
                }
            }
        }
    }
}
