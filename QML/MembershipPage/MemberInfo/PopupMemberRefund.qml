﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."
import ".."
import EnumTool 1.0

Item {
    id:popup_root
    signal sigClose
    signal confirm()
    function open() {
        popup_root.visible = true
        refund_member.visible = true;
        keyboard_c.closeAll()
    }
    function close() {
        popup_root.visible = false
        refund_member.visible = true;
        keyboard_c.closeAll()
        sigClose()
    }
    Rectangle {
        anchors.fill: parent
        color: "black";
        opacity: 0.6;
        MouseArea {
            anchors.fill: parent
            onClicked: {
                if (popup_root.visible)
                    close()
            }
        }
    }
    //退费界面
    Rectangle
    {
        id: refund_member_main;
        width: 550*dpi_ratio;
        height: 320*dpi_ratio;
        anchors.verticalCenter: parent.verticalCenter;
        anchors.left: parent.left;
        anchors.leftMargin: 300*dpi_ratio;
        color: "transparent";
        radius: 10 *dpi_ratio;
        border.width: 1*dpi_ratio;
        border.color: "transparent";
        visible: true;

        onVisibleChanged: {
            if(refund_member_main.visible){
            }
        }

        Rectangle
        {
            id: refund_member;
            width: 550*dpi_ratio;
            height: 320*dpi_ratio;
            anchors.left: parent.left;
            anchors.bottom: parent.bottom;
            color: "#ffffff";
            radius: 10 *dpi_ratio;
            border.width: 1 * dpi_ratio;
            border.color: "#999999";
            visible: true;
            MouseArea
            {
                anchors.fill: parent;
                onClicked:
                {
            }
            }
            Rectangle
            {
                id: refund_closeRec;
                width: 548*dpi_ratio;
                height: 40*dpi_ratio;
                anchors.top:parent.top;
                anchors.topMargin: 20 * dpi_ratio;
                anchors.horizontalCenter: parent.horizontalCenter;
                color: "#ffffff";
                Text {
                    id: refund_close_text
                    anchors.horizontalCenter: parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    text: qsTr("会员退费")
                    font.family: ST.fontFamilySiYan
                    font.pointSize: 20*dpi_ratio;
                    font.weight: Font.Bold
                    color: "black"
                }
            }
            Rectangle
            {
                id: refund_member_rec1;
                anchors.top: refund_closeRec.bottom;
                anchors.topMargin: 30 * dpi_ratio;
                color: "#ffffff";
                anchors.horizontalCenter: parent.horizontalCenter;
                width: 500 *dpi_ratio;
                height: 140 *dpi_ratio;

                Text
                {
                    id: refund_member_Text;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    //anchors.verticalCenter: parent.verticalCenter;
                    text: "<p style='margin: 2;'><span>" + qsTr("退费时，‌系统仅清零会员储值余额，‌请通过现") + "</span></p>" +
                          "<p style='margin: 2;'><span>" + qsTr("金或线下转账的形式将余额退还给消费者！‌") + "</span></p>";
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 16*dpi_ratio;
                    lineHeight: font.pointSize * 0.08;
                    color: "#333333";
                }
            }
            Rectangle
            {
                id: hindeRec;
                width: 500*dpi_ratio;
                height: 15*dpi_ratio;
                color: "red";
                anchors.bottom: parent.bottom;
                anchors.left: parent.left;
                visible: false;
                Rectangle
                {
                    id: refundMoneyCopyRec;
                    width: 250*dpi_ratio;
                    height: 15*dpi_ratio;
                    color: "blue";
                    anchors.bottom: parent.bottom;
                    anchors.left: parent.left;
                    Text
                    {
                        id: refundMoneyCopy;
                        anchors.left: parent.left
                        text:"111";
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 8*dpi_ratio;
                        color: "black";
                    }
                }
                Rectangle
                {
                    id: cashierIdCopyRec;
                    width: 250*dpi_ratio;
                    height: 15*dpi_ratio;
                    color: "green";
                    anchors.bottom: parent.bottom;
                    anchors.left: refundMoneyCopyRec.right;
                    Text
                    {
                        id: cashierIdCopy;
                        anchors.right: parent.right;
                        text:"2222";
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 8*dpi_ratio;
                        lineHeight: font.pointSize * 0.08;
                        color: "black";
                    }
                }
            }
            Rectangle
            {
                id: confir_refund_btn;
                width: 180*dpi_ratio;
                height: 60*dpi_ratio;
                color: "#FC9404";
                anchors.bottom: hindeRec.top;
                anchors.bottomMargin: 20 * dpi_ratio;
                anchors.left: parent.left;
                anchors.leftMargin: 65*dpi_ratio;
                radius: 10 *dpi_ratio;
                Timer
                {
                    id: refund_memberConfirmBtnTimer;
                    interval: 100;
                    repeat: false;
                    running: false;
                    onTriggered:
                    {
                        confir_refund_btn.color="#00bd74";
                    }
                }
                Text
                {
                    id: confir_refund_btn_text;
                    anchors.centerIn: parent;
                    text: qsTr("取消");
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 20*dpi_ratio;
                    color: "#ffffff";
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onClicked:
                    {
                        close();
                    }
                }
            }
            Rectangle
            {
                id: cancle_refund_btn;
                width: 180*dpi_ratio;
                height: 60*dpi_ratio;
                color: "#FF3734";
                border.width: 1;
                border.color: "#FF3734";
                anchors.bottom: hindeRec.top;
                anchors.bottomMargin: 20 * dpi_ratio;
                anchors.left: confir_refund_btn.right;
                anchors.leftMargin: 50*dpi_ratio;
                radius: 10 *dpi_ratio;

                Text
                {
                    id: cancle_refund_btn_text;
                    anchors.centerIn: parent;
                    text: qsTr("确认退费");
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 20*dpi_ratio;
                    color: "#ffffff";
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onClicked:
                    {
                        logMgr.logEvtInfo4Qml("点击确认退费")
                        confir_refund_btn.color="#333333";
                        refund_memberConfirmBtnTimer.start();
                        confirm();
                        close();
                    }
                }
            }
        }

    }
}
