﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../../.."

Item {
    id: popup_root

    property real max_point: 100
    property real cur_point: 100

    function open() {
        popup_root.visible = true
        tf_stock_change.clear()
        tf_stock_change.focusMe(false)
        keyboard_c.closeAll()
    }
    function close() {
        popup_root.visible = false
        keyboard_c.closeAll()
        sigClose()
    }

    function tryConfirm() {
        if (popup_contain_root.str_num == "") {
            toast.openWarn("不可为空")
            return
        }
        if (Number(popup_contain_root.str_num) > max_point) {
            toast.openWarn("数值过大")
            return
        }
        confirm(popup_contain_root.str_num)
        close()
    }

    signal confirm(var str_num)
    signal cancel
    signal sigClose

    property string title_name: "测试标题"
    property string message_info: "测试内容"

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 458 * dpi_ratio
        height: 606 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        onVisibleChanged: {
            x = (parent.width - width) / 2
            y = (parent.height - height) / 2
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        property string str_num: "0"

        function addStrNum(str, str2) {
            if (str == "0" && str2 == "0")
                return "0"

            if (str2 == ".") {
                if (str.indexOf(".") == -1) {
                    return str += str2
                }
                return str
            }

            if (str == "0")
                return str2

            str += str2

            let arr = str.split(".")

            var ret_str = arr[0]

            if (arr.length == 2) {
                ret_str = arr[0] + "." + arr[1].substring(0, 2)
            }

            return ret_str
        }

        function setInput(input) {
            str_num = addStrNum(str_num, input)
        }

        function backspace() {
            str_num = str_num.substring(0, str_num.length - 1)
            if (str_num == "") {
                str_num = "0"
            }
        }

        function resetStrNum() {
            str_num = "0"
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 20 * dpi_ratio
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 30 * dpi_ratio

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 50 * dpi_ratio
                                color: ST.color_transparent
                                CusText {
                                    text: qsTr("当前积分: ") + cur_point
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 58 * dpi_ratio
                                color: ST.color_transparent
                                CusTextField {
                                    id: tf_stock_change
                                    keyboard_status: 1
                                    anchors.centerIn: parent
                                    anchors.fill: parent
                                    validator: RegularExpressionValidator {
                                        regularExpression: /^\d+(\.\d+)?$/
                                    }
                                    enabled: false
                                    text: popup_contain_root.str_num
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                component KeyRect: CusRect {
                                    id: rect_key
                                    property string key_text: "0"
                                    signal pressKey(var key)
                                    color: ST.color_transparent

                                    border {
                                        width: 1 * dpi_ratio
                                        color: ST.color_grey_border
                                    }

                                    CusText {
                                        anchors.centerIn: parent
                                        text: rect_key.key_text
                                    }
                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            rect_key.pressKey(rect_key.key_text)
                                        }
                                    }

                                    onPressKey: {
                                        popup_contain_root.setInput(key)
                                    }
                                }
                                component KeyRect2: CusRect {
                                    id: rect_key2
                                    property string key_text: "0"
                                    signal pressKey(var key)

                                    property alias text: text

                                    CusText {
                                        id: text
                                        anchors.centerIn: parent
                                        text: rect_key2.key_text
                                    }
                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            rect_key2.pressKey(rect_key2.key_text)
                                        }
                                    }
                                }

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 4 * dpi_ratio

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent

                                        GridLayout {
                                            id: gl_calc_btns

                                            anchors.fill: parent
                                            columns: 3
                                            rows: 4

                                            columnSpacing: 4 * dpi_ratio
                                            rowSpacing: 4 * dpi_ratio

                                            property real col_width: (width - ((columns - 1) * rowSpacing)) / columns
                                            property real col_height: (height - ((rows - 1) * columnSpacing)) / rows

                                            function prefWidth(item) {
                                                return col_width * item.Layout.columnSpan
                                            }
                                            function prefHeight(item) {
                                                return col_height * item.Layout.rowSpan
                                            }

                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "7"
                                            }

                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "8"
                                            }
                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "9"
                                            }

                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "4"
                                            }
                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "5"
                                            }
                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "6"
                                            }

                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "1"
                                            }
                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "2"
                                            }
                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "3"
                                            }

                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "0"
                                            }
                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "00"
                                            }
                                            KeyRect {
                                                Layout.preferredWidth: parent.prefWidth(this)
                                                Layout.preferredHeight: parent.prefHeight(this)
                                                key_text: "."
                                            }
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 88 * dpi_ratio
                                        color: ST.color_transparent

                                        ColumnLayout {
                                            anchors.fill: parent
                                            spacing: 4 * dpi_ratio

                                            property real cell_height: gl_calc_btns.col_height

                                            KeyRect2 {
                                                Layout.preferredHeight: parent.cell_height
                                                Layout.fillWidth: true
                                                key_text: qsTr("清空")
                                                color: ST.color_red
                                                text.color: ST.color_white_pure
                                                onPressKey: {
                                                    popup_contain_root.resetStrNum()
                                                }
                                            }
                                            KeyRect2 {
                                                Layout.preferredHeight: parent.cell_height
                                                Layout.fillWidth: true
                                                key_text: qsTr("退格")
                                                color: ST.color_grey_font
                                                text.color: ST.color_white_pure
                                                onPressKey: {
                                                    popup_contain_root.backspace()
                                                }
                                            }
                                            KeyRect2 {
                                                Layout.fillWidth: true
                                                Layout.fillHeight: true
                                                key_text: qsTr("确定")
                                                color: popup_contain_root.page_index == 0 ? ST.color_grey : ST.color_blue_deeper
                                                text.color: ST.color_white_pure
                                                enabled: popup_contain_root.page_index != 0
                                                onPressKey: {
                                                    tryConfirm()
                                                    //                                                    confirm(popup_contain_root.str_num)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
