﻿import QtQuick 2.0

Rectangle
{
    id: confirm_rec;
    property string tip;
    property bool isConfirm;
    property string yesText: qsTr("确认");
    property string noText: qsTr("取消");
    property int widthNum: 200;
    property int heightNum: 100;
    signal yes();
    signal no();
    width: widthNum*utils4Qml.getWidthRatio();
    height: heightNum*dpi_ratio;
    color: "#ffffff";
    radius: 5*dpi_ratio;
    border.width: 1;
    border.color: "#333333";
    anchors.centerIn: parent;
    visible: false;
    z: 15;
    Text
    {
        id: tip_text;
        anchors.top: parent.top;
        anchors.horizontalCenter: parent.horizontalCenter;
        anchors.topMargin: 10*dpi_ratio;
        text: confirm_rec.tip;
        font.family: "微软雅黑";
        font.pointSize: 14*dpi_ratio;
        color: "#333333";
        horizontalAlignment: Text.AlignHCenter;
    }
    Rectangle
    {
        id: hor_line;
        width: confirm_rec.width -2*utils4Qml.getWidthRatio();
        height: 1*dpi_ratio;
        anchors.top: tip_text.bottom;
        anchors.topMargin: 15*dpi_ratio;
        anchors.horizontalCenter: parent.horizontalCenter;
        color: "#eeeeee";
    }

    Rectangle
    {
        id: confirm_btn;
        height: 40*dpi_ratio;
        anchors.top: hor_line.bottom;
        anchors.topMargin: 1*dpi_ratio;
        anchors.right: parent.right;
        anchors.rightMargin: 5*utils4Qml.getWidthRatio();
        anchors.left: vor_line.right;
        color: "#ffffff";
        Text
        {
            anchors.centerIn: parent;
            text: yesText
            font.family: "微软雅黑";
            font.pointSize: 14*dpi_ratio;
            color: "#333333";
        }
        MouseArea
        {
            anchors.fill: parent;
            onClicked:
            {
                yes();
                pay_page_new.setFocus();
            }
        }
    }

    Rectangle
    {
        id: vor_line;
        width: 1*utils4Qml.getWidthRatio();
        height: 45*dpi_ratio;
        anchors.top: hor_line.bottom;
        anchors.horizontalCenter: parent.horizontalCenter;
        color: "#eeeeee";
    }

    Rectangle
    {
        id: cancel_btn;
        height: 40*dpi_ratio;
        anchors.top: hor_line.bottom;
        anchors.topMargin: 1*dpi_ratio;
        anchors.left: parent.left;
        anchors.leftMargin: 5*utils4Qml.getWidthRatio();
        color: "#ffffff";
        anchors.right: vor_line.left;
        Text
        {
            anchors.centerIn: parent;
            text: noText
            font.family: "微软雅黑";
            font.pointSize: 14*dpi_ratio;
            color: "#333333";
        }
        MouseArea
        {
            anchors.fill: parent;
            onClicked:
            {
                no();
                pay_page_new.setFocus();
            }
        }
    }
}
