﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import ".."

Item {
    id: popup_root
    anchors.fill: parent

    property var calendar_target
    property alias rect_contain: rect_contain

    function open() {
        popup_root.visible = true
    }
    function close() {
        popup_root.visible = false
    }

    property bool is_floor: true

    function set(hour, minute) {
        lv_hour.currentIndex = hour
        minute = is_floor ? Math.floor(minute / 5) : Math.ceil(minute / 5)
    }
    function get() {}

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: close()
        }
    }

    signal clicked(date date)

    CusRect {
        id: rect_contain

        width: 700 * dpi_ratio
        height: 400 * dpi_ratio

        anchors.centerIn: parent

        color: ST.color_white_pure

        RowLayout {
            anchors.fill: parent

            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_transparent

                ListView {
                    id: lv_hour
                    anchors.fill: parent
                    model: 24
                    clip: true
                    snapMode: ListView.SnapToItem
                    delegate: CusRect {
                        id: rect_hour
                        width: lv_hour.width
                        height: 80 * dpi_ratio
                        property bool is_current: lv_hour.currentItem == this

                        radius: ST.radius

                        color: ST.color_transparent

                        CusRect {
                            anchors.fill: parent
                            anchors.margins: 10 * dpi_ratio
                            radius: ST.radius
                            color: rect_hour.is_current ? ST.color_green : ST.color_white_pure
                            CusText {
                                text: index
                                anchors.centerIn: parent
                                color: rect_hour.is_current ? ST.color_white_pure : ST.color_black
                            }
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                lv_hour.currentIndex = index
                            }
                        }
                    }
                    onVisibleChanged: {
                        if (visible)
                            lv_hour.positionViewAtIndex(currentIndex, ListView.Center)
                    }
                    move: Transition {
                        NumberAnimation {
                            duration: 0
                        }
                    }
                }
            }

            CusRect {
                Layout.fillHeight: true
                Layout.preferredWidth: 30 * dpi_ratio
                color: ST.color_transparent

                CusText {
                    text: ":"
                    anchors.centerIn: parent
                    font.pixelSize: 40
                }
            }

            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_white_pure

                ListView {
                    id: lv_minute
                    anchors.fill: parent
                    model: 13
                    clip: true
                    delegate: CusRect {
                        id: rect_minute
                        width: lv_minute.width
                        height: 80 * dpi_ratio
                        property bool is_current: lv_minute.currentItem == this
                        color: ST.color_transparent

                        CusRect {
                            anchors.fill: parent
                            anchors.margins: 10 * dpi_ratio
                            radius: ST.radius
                            color: rect_minute.is_current ? ST.color_green : ST.color_white_pure
                            CusText {
                                text: index * 5
                                anchors.centerIn: parent
                            }
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                lv_minute.currentIndex = index
                            }
                        }
                    }
                    onVisibleChanged: {
                        if (visible)
                            lv_minute.positionViewAtIndex(currentIndex, ListView.Center)
                    }
                }
            }
        }
    }
}
