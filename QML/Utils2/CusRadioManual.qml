﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import ".."

CusRect {
    id: control
    signal clicked
    property bool is_need_init: true
    property alias text: control_text.text
    property alias control_text: control_text
    property bool checked: false
    color: ST.color_transparent
    implicitHeight: 30 * ST.dpi_ratio
    implicitWidth: 120 * ST.dpi_ratio

    RowLayout {
        anchors.fill: parent
        spacing: 12 * ST.dpi_ratio

        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: height
            radius: width / 2
            border.color: ST.color_green
            border.width: 2 * ST.dpi_ratio
            color: ST.color_transparent

            CusRect {
                anchors.centerIn: parent
                width: parent.width / 5 * 3
                height: width
                radius: width / 2
                color: ST.color_green
                visible: control.checked
            }
        }

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
            CusText {
                id: control_text
                anchors.verticalCenter: parent.verticalCenter
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        onClicked: {
            control.clicked()
        }
    }
}
