﻿import QtQuick 2.15
import QtQuick.Controls 2.0
import ".."

CusRectShape {
    id: control
    signal clicked
    property bool is_need_init: true
    property alias control_text: control_text
    property alias text: control_text.text
    property bool checked: false
    property string color_checked: ST.color_white_pure
    property string color_unchecked: ST.color_green
    property string control_mark: ""

    cornersRadius: [ST.radius, ST.radius, ST.radius, ST.radius]

    borderWidth: checked ? 2 * dpi_ratio : 0
    borderColor: checked ? ST.color_grey_border : ST.color_transparent

    color: checked ? color_checked : color_unchecked

    height: 50 * ST.dpi_ratio
    width: 120 * ST.dpi_ratio

    CusText {
        id: control_text
        anchors.centerIn: parent
        font.family: ST.fontFamilyYaHei
        font.pixelSize: 22 * dpi_ratio * configTool.fontRatio
        color: checked ? ST.color_black : ST.color_white_pure
        width: parent.width
        horizontalAlignment: Text.AlignHCenter
        elide: Text.ElideNone
    }

    MouseArea {
        anchors.fill: parent
        onClicked: {
            control.clicked()
        }
    }
}
