﻿import QtQuick 2.15;
import QtQuick.Controls 2.15

import ".."

TextArea {
    id: control
    width: 688 * dpi_ratio
    height: 129 * dpi_ratio

    font.family: "微软雅黑"
    font.pixelSize: 22 * dpi_ratio * configTool.fontRatio

    property int keyboard_status: 0

    wrapMode: TextEdit.WordWrap
    placeholderText: qsTr("请输入")

    leftPadding: 10 * dpi_ratio * configTool.fontRatio
    rightPadding: 10 * dpi_ratio * configTool.fontRatio

    topPadding: 10 * dpi_ratio * configTool.fontRatio
    bottomPadding: 10 * dpi_ratio * configTool.fontRatio

    background: CusRect {
        z: -1
        implicitWidth: 200 * dpi_ratio
        implicitHeight: 40 * dpi_ratio
        radius: 6 * dpi_ratio
        color: "#F7F7F7"
    }

    MouseArea {
        anchors.fill: parent
        onClicked: {
            control.forceActiveFocus()
            control.cursorPosition = control.text.length
            window_root.keyboard_c.closeAll()
            window_root.keyboard_c.normal_keyboard.visible = true
        }
    }
    onFocusChanged: {
        if (!focus)
        {
             window_root.keyboard_c.closeAll()
        }
    }
}
