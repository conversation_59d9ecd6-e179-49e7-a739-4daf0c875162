﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15

import ".."

ComboBox {
    id: control

    font.family: ST.fontFamilyYaHei
    font.pixelSize: 22 * dpi_ratio * configTool.fontRatio
    property int preIndex: -1
    property bool is_need_init: true
    currentIndex: -1

    property string color_enabled: control.focus ? ST.color_green : color_disabled
    property string color_disabled: ST.color_grey_border2

    //property string color_disabled: "#6a6a6a"
    onFocusChanged: {
        canvas.requestPaint()
    }

    onEnabledChanged: {
        canvas.requestPaint()
    }

    delegate: ItemDelegate {
        width: control.width
        contentItem: Text {
            text: control.textRole ? (Array.isArray(control.model) ? modelData[control.textRole] : model[control.textRole]) : modelData
            // color: control.enabled ? color_enabled : color_disabled
            color: control.focus ? ST.color_green : ST.color_black
            font: control.font
            elide: Text.ElideRight
            verticalAlignment: Text.AlignVCenter
        }
        highlighted: control.highlightedIndex === index
    }

    indicator: Canvas {
        id: canvas
        x: control.width - width - control.rightPadding
        y: control.topPadding + (control.availableHeight - height) / 2
        width: 12 * dpi_ratio * configTool.fontRatio
        height: 8 * dpi_ratio * configTool.fontRatio
        contextType: "2d"

        Connections {
            target: control
            function onPressedChanged() {
                canvas.requestPaint()
            }
        }

        onPaint: {
            context.reset()
            context.moveTo(0, 0)
            context.lineTo(width, 0)
            context.lineTo(width / 2, height)
            context.closePath()
            context.fillStyle = control.enabled ? color_enabled : color_disabled
            //            context.fillStyle = color_enabled
            context.fill()
        }
    }

    contentItem: Text {
        leftPadding: 14 * dpi_ratio * configTool.fontRatio
        //text: control.displayText
        text: control.currentIndex === -1 ? qsTr("请选择") : control.displayText
        font: control.font
        color: control.enabled ? (control.focus ? ST.color_green : ST.color_black) : color_disabled
        verticalAlignment: Text.AlignVCenter
        elide: Text.ElideRight
    }

    background: Rectangle {
        implicitWidth: 120 * dpi_ratio * configTool.fontRatio
        implicitHeight: 40 * dpi_ratio * configTool.fontRatio
        border.color: control.enabled ? color_enabled : color_disabled
        border.width: 1.5 * dpi_ratio * configTool.fontRatio
        radius: ST.radius
    }

    popup: Popup {
        y: control.height - ST.border_width
        width: control.width
        implicitHeight: contentItem.implicitHeight
        padding: ST.border_width

        contentItem: ListView {
            id: _rect
            clip: true
            implicitHeight: contentHeight
            model: control.popup.visible ? control.delegateModel : null
            currentIndex: control.highlightedIndex
            ScrollIndicator.vertical: ScrollIndicator {}

            layer.enabled: true
            layer.effect: OpacityMask {
                maskSource: Rectangle {
                    width: _rect.width
                    height: _rect.height
                    radius: ST.radius
                }
            }
        }

        background: Rectangle {
            border.color: control.enabled ? color_enabled : color_disabled
            border.width: ST.border_width
            clip: true
            radius: ST.radius
        }
    }
}
