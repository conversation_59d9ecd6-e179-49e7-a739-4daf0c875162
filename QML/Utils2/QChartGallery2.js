﻿// QChartGallery.js ---
//
// Author: <PERSON>
// Created: Thu Feb 13 23:43:13 2014 (+0100)
// Version:
// Last-Updated:
//           By:
//     Update #: 13
//

// Change Log:
//
//

// /////////////////////////////////////////////////////////////////
// Line Chart Data Sample
// /////////////////////////////////////////////////////////////////
var ChartLineData = {
    labels: ["1","","","","5","","","","","10","","","","","15","","","","","20","","","","","25","","","","","30"],
    datasets: [{
            fillColor: "rgba(220,220,220,0.5)",
            strokeColor: "rgba(220,220,220,1)",
            pointColor: "rgba(220,220,220,1)",
            pointStrokeColor: "#ffffff",
            data: [40,59,90,81,56,55,60,90,80,40,59,90,81,56,55,60,90,80,40,59,90,81,56,55,60,90,80,30,10,30]
        }]
}

// /////////////////////////////////////////////////////////////////
// Polar Chart Data Sample
// /////////////////////////////////////////////////////////////////
var ChartPolarData = [{
                          value: 30,
                          color: "#D97041"
                      }, {
                          value: 90,
                          color: "#C7604C"
                      }, {
                          value: 24,
                          color: "#21323D"
                      }, {
                          value: 58,
                          color: "#9D9B7F"
                      }, {
                          value: 82,
                          color: "#7D4F6D"
                      }, {
                          value: 8,
                          color: "#584A5E"
                      }]

// /////////////////////////////////////////////////////////////////
// Radar Chart Data Sample
// /////////////////////////////////////////////////////////////////
var ChartRadarData = {
    labels: ["Eating","Drinking","Sleeping","Designing","Coding","Partying","Running"],
    datasets: [{
            fillColor: "rgba(220,220,220,0.5)",
            strokeColor: "rgba(220,220,220,1)",
            pointColor: "rgba(220,220,220,1)",
            pointStrokeColor: "#fff",
            data: [65,59,90,81,56,55,40]
        }, {
            fillColor: "rgba(151,187,205,0.5)",
            strokeColor: "rgba(151,187,205,1)",
            pointColor: "rgba(151,187,205,1)",
            pointStrokeColor: "#fff",
            data: [28,48,40,19,96,27,100]
        }]
}

// /////////////////////////////////////////////////////////////////
// Pie Chart Data Sample
// /////////////////////////////////////////////////////////////////
var ChartPieData =
        [[50,"#2dc6c8","瓜子"], [100,"#b6a2dd", "花生"], [200,"#5ab1ee","土豆"], [700,"#d7797f","南瓜四号"]];
var ChartPieData2 =
        [[50,"#2cb7f5"], [100,"#818bc7"], [200,"#7cc956"], [700,"#5b6877"], [300,"#f9c002"]];
// /////////////////////////////////////////////////////////////////
// Doughnut Chart Data Sample
// /////////////////////////////////////////////////////////////////
var ChartDoughnutData = [{
                             value: 30,
                             color: "#F7464A"
                         }, {
                             value: 50,
                             color: "#E2EAE9"
                         }, {
                             value: 100,
                             color: "#D4CCC5"
                         }, {
                             value: 40,
                             color: "#949FB1"
                         }, {
                             value: 120,
                             color: "#4D5360"
                         }]
var ChartDoughnutData2 = [{
                             value: 30,
                             color: "#01cfe3"
                         }, {
                             value: 50,
                             color: "#f9c002"
                         }, {
                             value: 100,
                             color: "#818bc7"
                         }, {
                             value: 40,
                             color: "#2cb7f5"
                         }, {
                             value: 120,
                             color: "#4D5360"
                         }]
// /////////////////////////////////////////////////////////////////
// Bar Chart Data Sample
// /////////////////////////////////////////////////////////////////
var ChartBarData = {
    labels: ["0","","","3","","","6","","","9","","","12","","","15","","","18","","","21","","","24"],
    datasets: [{
            fillColor: "rgba(60,143,255,1)",
            strokeColor: "rgba(60,143,255,1)",
            data: [2,2,2,2,2,2,2,5,8,20,9,2,2,3,6,15,10,25,3,10,15,5,2,2,2]
        }]
}
var ChartBarData2 = {
    labels: ["路人","低频","普通","高频","优质"],
    datasets: [{
            fillColor: "rgba(44,183,245,1)",
            strokeColor: "rgba(44,183,245,1)",
            data: [5,40,30,20,25]
        }]
}
var ChartBarData2E = {
    labels: ["few","low","medium","high","good"],
    datasets: [{
            fillColor: "rgba(44,183,245,1)",
            strokeColor: "rgba(44,183,245,1)",
            data: [5,40,30,20,25]
        }]
}
var ChartBarData3 = {
    labels: ["0","1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23",""],
    datasets: [{
            fillColor: "rgba(44,183,245,1)",
            strokeColor: "rgba(44,183,245,1)",
            data: [0,0,0,0,0,0,10,20,30,40,30,30,10,0,2,3,20,50,60,10,20,10,2,0,0]
        }]
}
var ChartLineData3 = {
    labels: ["0","1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23",""],
    datasets: [{
            fillColor: "rgba(254,51,154,0)",
            strokeColor: "rgba(254,51,154,1)",
            pointColor: "rgba(254,51,154,0)",
            pointStrokeColor: "#2cb7f5",
            data: [0,0,0,0,0,0,10,20,30,40,30,30,10,0,2,3,20,50,60,10,20,10,2,0,0]
        }]
}

var ChartLineData4 = {
    labels: ["","","","","","",""],
    datasets: [{
            fillColor: "rgba(205,245,225,1)",
            strokeColor: "rgba(220,220,220,0)",
            pointColor: "rgba(220,220,220,0)",
            pointStrokeColor: "#cdf5e1",
            data: [30,70,20,61,56,95,60]
        }]
}
var ChartLineData5 = {
    labels: ["","","","","","",""],
    datasets: [{
            fillColor: "rgba(205,245,225,1)",
            strokeColor: "rgba(142,153,206,1)",
            pointColor: "rgba(142,153,206,0)",
            pointStrokeColor: "#8e99ce",
            data: [60,49,90,71,56,95,60]
        }]
}

var ChartLineData6 = {
    labels: ["","","","","","",""],
    datasets: [{
            fillColor: "rgba(205,245,225,1)",
            strokeColor: "rgba(148,211,117,1)",
            pointColor: "rgba(148,211,117,0)",
            pointStrokeColor: "#94d375",
            data: [30,59,80,81,76,55,60]
        }]
}
var ChartLineData7 = {
    labels: ["5.20","5.22","5.24","5.26","5.28","5.30","6.01","6.03","6.05","6.07","6.09","6.11","6.13","6.15","6.17"],
    datasets: [{
            fillColor: "rgba(254,51,154,0)",
            strokeColor: "rgba(44,183,245,1)",
            pointColor: "rgba(254,51,154,0)",
            pointStrokeColor: "#2cb7f5",
            data: [10,10000,30,10000,30,20000,50,20,30,40,30,30,10,8000,25000]
        }]
}

var ChartLineData8 = {
    labels: ["5.20","5.22","5.24","5.26","5.28","5.30","6.01","6.03","6.05","6.07","6.09","6.11","6.13","6.15","6.17"],
    datasets: [{
            fillColor: "rgba(254,51,154,0)",
            strokeColor: "rgba(129,139,199,1)",
            pointColor: "rgba(254,51,154,0)",
            pointStrokeColor: "#2cb7f5",
            data: [500,8000,9000,400,6000,800,60,9000,12000,800,400,300,30000,200,30000]
        },{
            fillColor: "rgba(254,51,154,0)",
            strokeColor: "rgba(44,183,245,1)",
            pointColor: "rgba(254,51,154,0)",
            pointStrokeColor: "#2cb7f5",
            data: [10,10000,30,10000,30,20000,50,20,30,40,30,30,10,8000,25000]
        }]
}

