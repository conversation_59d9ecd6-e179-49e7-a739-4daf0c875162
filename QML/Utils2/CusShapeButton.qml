﻿import QtQuick 2.15
import QtQuick.Controls 2.0
import QtGraphicalEffects 1.15

import ".."

MouseArea {
    id: control_root
    width: 100 * dpi_ratio
    height: 50 * dpi_ratio

    property alias cornersRadius: control.cornersRadius

    property alias font: btn_text.font

    property alias text: btn_text.text

    property alias font_opacity: btn_text.opacity

    property alias color: control.color

    CusRectShape {
        id: control
        anchors.fill: parent

        opacity: control_root.containsPress ? .85 : 1


        property real font_opacity: enabled ? 1.0 : 0.3

        CusText {
            id: btn_text
            anchors.centerIn: parent
            color: "#FFFFFF"
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }
}
