﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.calendar 1.0
import ".."

Item {
    id: popup_root
    anchors.fill: parent
    signal sigDateClose
    property var target_control

    property string cur_date_str: utils4Qml.getCurDate()
    property string cur_time_str: cur_time_hour_str + ":" + cur_time_minute_str
    property string cur_time_hour_str: "0"
    property string cur_time_minute_str: "0"

    property alias rect_contain: rect_contain

    function open() {
        popup_root.visible = true
    }
    function close() {
        popup_root.visible = false
        sigDateClose();
    }

    property bool is_floor: true

    function set(date, hour, minute) {
        var date_vec = date.split('-')
        if (date_vec.length != 3)
            return

        var final_minute = Number(minute)
        var final_hour = Number(hour)
        var final_day = Number(date_vec[2])
        var final_month = Number(date_vec[1])
        var final_year = Number(date_vec[0])

        if (final_minute > 55) {
            final_minute = 1
            final_hour = ++final_hour
        }

        if (final_hour > 23) {
            final_hour = 1
            final_day = ++final_day
        }

        var day_num = utils4Qml.getDaysByYearMonth(final_year, final_month)
        if (final_day > day_num) {
            final_day = 1
            ++final_month
        }

        if (final_month > 12) {
            final_month = 1
            ++final_year
        }

        lv_hour.currentIndex = final_hour
        lv_minute.currentIndex = Math.floor(minute / 5)

        cur_date_str = final_year + "-" + String(final_month).padStart(2, 0) + "-" + String(final_day).padStart(2, 0)
    }

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: close()
        }
    }

    signal clicked(date date)

    CusRect {
        id: rect_contain

        width: 1600 * dpi_ratio
        height: 800 * dpi_ratio

        anchors.centerIn: parent
        color: ST.color_transparent

        MouseArea {
            anchors.fill: parent
        }

        CusRect {
            anchors.fill: parent
            color: ST.color_white_deeper2
            radius: ST.radius
        }

        RowLayout {
            anchors.fill: parent

            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_transparent

                Rectangle {
                    anchors.centerIn: parent
                    id: rect_calendar
                    implicitWidth: 700 * dpi_ratio
                    implicitHeight: 750 * dpi_ratio
                    radius: ST.radius
                    color: ST.color_white_pure

                    function getDateStr(date1) {
                        return Qt.formatDate(date1, "yyyy-MM-dd")
                    }

                    property alias font: month_grid.font
                    property alias locale: month_grid.locale

                    //                    property string selectDate: utils4Qml.getCurDateTime()
                    MouseArea {
                        anchors.fill: parent
                    }

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 20 * dpi_ratio
                        spacing: 20 * dpi_ratio

                        Rectangle {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 80 * dpi_ratio
                            Layout.leftMargin: 150 * dpi_ratio
                            Layout.rightMargin: 150 * dpi_ratio
                            color: ST.getDebugColor()

                            Rectangle {
                                anchors.fill: parent
                                //                    color: ST.getDebugColor()
                                Image {
                                    anchors.right: text_year_month.left
                                    anchors.rightMargin: 20
                                    anchors.verticalCenter: text_year_month.verticalCenter
                                    source: "/Images/ArrowsL.png"
                                    fillMode: Image.PreserveAspectFit
                                    height: 50 * dpi_ratio
                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            if (month_grid.month == 0) {
                                                --month_grid.year
                                                month_grid.month = 11
                                            } else {
                                                --month_grid.month
                                            }
                                        }
                                    }
                                }
                                CusText {
                                    id: text_year_month
                                    anchors.centerIn: parent
                                    text: month_grid.year + qsTr("年　") + (month_grid.month + 1) + qsTr("月")
                                    font.bold: true
                                }
                                Image {
                                    anchors.left: text_year_month.right
                                    anchors.leftMargin: 20 * dpi_ratio
                                    anchors.verticalCenter: text_year_month.verticalCenter
                                    source: "/Images/ArrowsR.png"
                                    fillMode: Image.PreserveAspectFit
                                    height: 50
                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            if (month_grid.month == 11) {
                                                ++month_grid.year
                                                month_grid.month = 0
                                            } else {
                                                ++month_grid.month
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        Rectangle {
                            Layout.fillWidth: true
                            Layout.fillHeight: true

                            //                color: ST.getDebugColor()
                            ColumnLayout {
                                anchors.fill: parent
                                anchors.margins: 10 * dpi_ratio

                                //星期1-7
                                DayOfWeekRow {
                                    id: week_row
                                    Layout.row: 1
                                    //                Layout.column: 1
                                    Layout.fillWidth: true
                                    implicitHeight: 40 * dpi_ratio
                                    spacing: 1 * dpi_ratio
                                    topPadding: 0
                                    bottomPadding: 0
                                    font: rect_calendar.font
                                    //locale设置会影响显示星期数中英文
                                    locale: rect_calendar.locale
                                    delegate: Text {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        text: shortName
                                        font: week_row.font
                                        color: ST.color_green
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                        required property string shortName
                                    }
                                    contentItem: Rectangle {
                                        color: ST.color_transparent
                                        //                    border.color: ST.color_grey_border
                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: week_row.spacing
                                            Repeater {
                                                model: week_row.source
                                                delegate: week_row.delegate
                                            }
                                        }
                                    }
                                }

                                //日期单元格
                                MonthGrid {
                                    id: month_grid
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true

                                    locale: Qt.locale("zh_CN")
                                    spacing: 1
                                    font {
                                        family: ST.fontFamilyYaHei
                                        pixelSize: 20 * dpi_ratio
                                    }

                                    delegate: Item {

                                        Rectangle {
                                            anchors.fill: parent
                                            radius: width / 2
                                            anchors.margins: 10 * dpi_ratio
                                            color: if (cur_date_str == rect_calendar.getDateStr(model.date)) {
                                                       return ST.color_green
                                                   } else if (model.today) {
                                                       return ST.color_green_light
                                                   } else {
                                                       return ST.color_transparent
                                                   }

                                            border.color: ST.color_grey_btn
                                            border.width: item_mouse.containsMouse ? 1 : 0
                                        }

                                        Text {
                                            anchors.centerIn: parent
                                            font: rect_calendar.font
                                            text: model.day
                                            color: if (cur_date_str == rect_calendar.getDateStr(model.date)) {
                                                       return ST.color_white
                                                   } else {
                                                       if (model.month === month_grid.month) {
                                                           return ST.color_green
                                                       } else {
                                                           return ST.color_green2
                                                       }
                                                   }
                                        }
                                        MouseArea {
                                            id: item_mouse
                                            anchors.fill: parent
                                            hoverEnabled: true
                                            acceptedButtons: Qt.NoButton
                                        }
                                    }
                                    onClicked: {
                                        cur_date_str = rect_calendar.getDateStr(date)
                                        //                                        cur_date_str = Qt.formatDate(date, "yyyy-MM-dd")
                                    }
                                }
                            }
                        }
                    }
                }
            }

            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_transparent

                CusRect {
                    width: 700 * dpi_ratio
                    height: 400 * dpi_ratio
                    radius: ST.radius
                    anchors.horizontalCenter: parent.horizontalCenter
                    y: 70 * dpi_ratio

                    color: ST.color_white_pure

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            ListView {
                                id: lv_hour
                                anchors.fill: parent
                                model: 24
                                clip: true
                                snapMode: ListView.SnapToItem
                                delegate: CusRect {
                                    id: rect_hour
                                    width: lv_hour.width
                                    height: 80 * dpi_ratio
                                    property bool is_current: lv_hour.currentItem == this

                                    radius: ST.radius

                                    color: ST.color_transparent

                                    CusRect {
                                        anchors.fill: parent
                                        anchors.margins: 10 * dpi_ratio
                                        radius: ST.radius
                                        color: rect_hour.is_current ? ST.color_green : ST.color_white_pure
                                        CusText {
                                            text: String(index).padStart(2, 0)
                                            anchors.centerIn: parent
                                            color: rect_hour.is_current ? ST.color_white_pure : ST.color_black
                                        }
                                    }

                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            lv_hour.currentIndex = index
                                        }
                                    }
                                }
                                onVisibleChanged: {
                                    if (visible)
                                        lv_hour.positionViewAtIndex(currentIndex, ListView.Center)
                                }

                                move: Transition {
                                    NumberAnimation {
                                        duration: 0
                                    }
                                }

                                onCurrentIndexChanged: {
                                    cur_time_hour_str = String(currentIndex).padStart(2, 0)
                                }
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 30 * dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: ":"
                                anchors.centerIn: parent
                                font.pixelSize: 36 * dpi_ratio
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_white_pure

                            ListView {
                                id: lv_minute
                                anchors.fill: parent
                                model: 12
                                clip: true
                                delegate: CusRect {
                                    id: rect_minute
                                    width: lv_minute.width
                                    height: 80 * dpi_ratio
                                    property bool is_current: lv_minute.currentItem == this
                                    color: ST.color_transparent

                                    CusRect {
                                        anchors.fill: parent
                                        anchors.margins: 10 * dpi_ratio
                                        radius: ST.radius
                                        color: rect_minute.is_current ? ST.color_green : ST.color_white_pure
                                        CusText {
                                            text: String(index * 5).padStart(2, 0)
                                            anchors.centerIn: parent
                                            color: rect_minute.is_current ? ST.color_white_pure : ST.color_black
                                        }
                                    }

                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            lv_minute.currentIndex = index
                                        }
                                    }
                                }
                                onVisibleChanged: {
                                    if (visible)
                                        lv_minute.positionViewAtIndex(currentIndex, ListView.Center)
                                }
                                move: Transition {
                                    NumberAnimation {
                                        duration: 0
                                    }
                                }
                                onCurrentIndexChanged: {
                                    cur_time_minute_str = String(currentIndex * 5).padStart(2, 0)
                                }
                            }
                        }
                    }
                }

                CusRect {
                    width: 700 * dpi_ratio
                    height: 60 * dpi_ratio

                    anchors.horizontalCenter: parent.horizontalCenter
                    anchors.bottom: parent.bottom
                    anchors.bottomMargin: 70 * dpi_ratio

                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        anchors.leftMargin: 40 * dpi_ratio
                        anchors.rightMargin: 40 * dpi_ratio

                        CusButton {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 200 * dpi_ratio
                            text: qsTr("确定")
                            onClicked: {
                                target_control.text = cur_date_str + " " + cur_time_str
                                close()
                            }
                        }

                        CusSpacer {
                            Layout.fillWidth: true
                        }

                        CusButton {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 200 * dpi_ratio
                            text: qsTr("取消")
                            onClicked: {
                                close()
                            }
                        }
                    }
                }
            }
        }
    }
}
