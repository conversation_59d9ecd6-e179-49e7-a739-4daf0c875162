﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtGraphicalEffects 1.15

import ".."

CusRect {
    id: rect_button_root
    width: 100
    height: 50
    radius: ST.radius

    signal sigClicked

    property alias font: t_btn.font
    property alias text: t_btn.text

    property bool is_check_btn: false
    property bool is_checked: false

    property bool is_pressed: ma_btn.pressed

    property string color_font: ST.color_black
    property string color_font2: ST.color_white_pure

    property string color_background: ST.color_white_pure
    property string color_background2: ST.color_green

    property bool is_bold: false

    property bool is_need_border: false

    border {
        width: is_pressed || is_checked || is_need_border ? 1 * ST.dpi : 0
        color: is_pressed || is_checked ? color_font2 : color_font
    }

    state: is_check_btn ? "check_btn" : "normal_btn"

    states: [
        State {
            name: "normal_btn"
            PropertyChanges {
                target: rect_button_root
                color: is_pressed ? color_background2 : color_background
            }
            PropertyChanges {
                target: t_btn
                color: is_pressed ? color_font2 : color_font
                opacity: rect_button_root.enabled ? 1 : .6
            }
        },
        State {
            name: "check_btn"
            PropertyChanges {
                target: rect_button_root
                color: (is_pressed || is_checked) ? color_background2 : color_background
            }
            PropertyChanges {
                target: t_btn
                color: (is_pressed || is_checked) ? color_font2 : color_font
                opacity: rect_button_root.enabled ? 1 : .6
            }
        }
    ]

    CusText {
        id: t_btn
        text: "placeholder"
        anchors.centerIn: parent
        font.bold: is_bold
        width: parent.width
        horizontalAlignment: Text.AlignHCenter
    }

    MouseArea {
        id: ma_btn
        anchors.fill: parent
        onClicked: {
            sigClicked()
        }
    }
}
