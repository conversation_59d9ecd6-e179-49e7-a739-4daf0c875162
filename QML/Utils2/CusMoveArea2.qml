﻿import QtQuick 2.9
import QtQuick.Controls 2.2

MouseArea {
    id: moveArea

    property var control: parent //导出一个control属性，指定要拖动的目标， 默认就用parent好了。注意目标要有x和y属性并且可修改

    property point clickPos: "0,0"

    z: -1

    signal sigClickOnly

    property bool is_moved: false

    onPressed: {
        clickPos = Qt.point(mouse.x, mouse.y)
        is_moved = false
    }

    onPositionChanged: {
        if (pressed) {
            var delta = Qt.point(mouse.x - clickPos.x, mouse.y - clickPos.y)
            control.x += delta.x
            control.y += delta.y
            is_moved = true
        }
    }

    onReleased: {
        if (clickPos == Qt.point(mouse.x, mouse.y) && !is_moved) {
            sigClickOnly()
        }
    }
}
