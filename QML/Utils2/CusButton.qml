﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtGraphicalEffects 1.15

import ".."

Button {
    id: control
    font.family: ST.fontFamilyYaHei
    font.pixelSize: 22 * dpi_ratio * configTool.fontRatio
    focusPolicy: Qt.NoFocus

    width: 100 * dpi_ratio
    height: 50 * dpi_ratio

    property alias color: rect_back.color
    property alias radius: rect_back.radius
    property alias border: rect_back.border
    property alias font_color: btn_text.color


    property real font_opacity: enabled ? 1.0 : 0.3

    contentItem: Text {
        id: btn_text
        text: control.text
        font: control.font
        opacity: font_opacity
        color: "#FFFFFF"
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
    }

    background: CusRect {
        id: rect_back
        implicitWidth: control.width
        implicitHeight: control.height
        color: ST.color_green
        opacity: control.down ? .85 : 1
        radius: ST.radius * configTool.fontRatio
    }
}
