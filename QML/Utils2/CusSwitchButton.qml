﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import ".."

CusRect {
    id: control
    height: 26 * ST.dpi_ratio * configTool.fontRatio
    width: text == "" ? height * 2 : 90 * ST.dpi_ratio * configTool.fontRatio
    color: ST.color_transparent

    property bool is_need_init: true
    property bool checked: false
    property alias text_compo: text_switch
    property alias text: text_switch.text
    property alias font: text_switch.font

    signal clicked

    onWidthChanged: {
        if (control.checked) {
            na_1.restart()
        } else {
            na_2.restart()
        }
    }

    RowLayout {
        anchors.fill: parent
        spacing: 10 * dpi_ratio

        CusRect {
            id: rect_root
            Layout.preferredWidth: height * 2
            Layout.fillHeight: true
            Layout.alignment: Qt.AlignVCenter
            antialiasing: true
            radius: height / 2
            color: control.checked ? ST.color_green : "#ffffff"
            border.color: control.checked ? ST.color_green : "#cccccc"

            //小圆点
            CusRect {
                id: smallRect
                width: height
                height: parent.height
                radius: height / 2
                color: control.down ? "#cccccc" : "#ffffff"
                border.color: control.checked ? (control.down ? "#17a81a" : "#21be2b") : "#999999"
                antialiasing: true
                anchors.verticalCenter: parent.verticalCenter

                //改变小圆点的位置
                NumberAnimation on x {
                    id: na_1
                    to: smallRect.width
                    running: control.checked ? true : false
                    duration: 200
                }

                //改变小圆点的位置
                NumberAnimation on x {
                    id: na_2
                    to: 0
                    running: control.checked ? false : true
                    duration: 200
                }
            }
        }

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
            visible: text != ""
            CusText {
                id: text_switch
                anchors.verticalCenter: parent.verticalCenter
                verticalAlignment: Text.AlignVCenter
                text: ""
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        onClicked: {
            control.clicked()
        }
    }
}
