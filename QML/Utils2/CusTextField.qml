﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import ".."

TextField {
    id: control
    width: 643 * dpi_ratio
    height: 48 * dpi_ratio
    placeholderText: qsTr("请输入")

    topPadding: 0
    bottomPadding: 0

    placeholderTextColor: ST.color_grey_font

    font.family: ST.fontFamilyYaHei
    font.pixelSize: 24 * dpi_ratio * configTool.fontRatio

    clip: true
    verticalAlignment: TextInput.AlignVCenter

    property alias back_rect: back_rect
    property int keyboard_status: 0 //0 普通键盘  1数字键盘
    property alias border: back_rect.border

    property string background_color_enabled: ST.color_transparent

    property string background_color: control.enabled ? background_color_enabled : "#f1f1f1"
    property bool is_clicked_select_all: false
    property bool is_need_keybord: true

    property real normal_keyboard_x: -1
    property real normal_keyboard_y: -1

    property real digital_keyboard_x: -1
    property real digital_keyboard_y: -1

    property string comnent: "" //注释

    signal sigClicked

    onFocusChanged: {
        if (!focus) {
            window_root.keyboard_c.tryCloseAll()
        }
    }

    function focusMe() {
        if (is_use_virtual_keyboard && is_need_keybord) {
            window_root.keyboard_c.closeAll()
            window_root.keyboard_c.setNormalKeyboardPos(normal_keyboard_x, normal_keyboard_y)
            window_root.keyboard_c.setDigitalKeyboardPos(digital_keyboard_x, digital_keyboard_y)
            if (keyboard_status == 0) {
                window_root.keyboard_c.openNormalKeyboard()
            } else if (keyboard_status == 1) {
                window_root.keyboard_c.openDigitalKeyboard()
            }
        }
        control.forceActiveFocus()
        control.focus = true
        if (is_clicked_select_all)
            selectAll()
    }

    background: Rectangle {
        id: back_rect
        implicitWidth: 200 * dpi_ratio
        implicitHeight: 40 * dpi_ratio
        color: background_color
        border.color: control.focus ? ST.color_green : ST.color_grey_border2
        radius: ST.radius
        z: -10
    }

    MouseArea {
        anchors.fill: parent
        onClicked: {
            focusMe()
            sigClicked()
        }
    }

    onAccepted: {
        window_root.keyboard_c.closeAll()
    }
}
