﻿import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import ".."

Item {
    anchors.fill: parent

    property bool is_caps_lock: false //大写锁定

    property real normal_keyboard_ratio: 1
    property real digital_keyboard_ratio: 1.25

    property alias normal_keyboard: normal_keyboard
    property alias digital_keyboard: digital_keyboard

    QtObject {
        id: objTime
        property real time: 0
        function refreshTime() {
            var date = new Date()
            time = date.getTime()
        }
    }

    function closeAll() {
        normal_keyboard.visible = false
        digital_keyboard.visible = false
    }

    function tryCloseAll() {
        var date = new Date()
        var time = date.getTime()
        if (time - objTime.time > 100) {
            closeAll()
        }
    }

    function setNormalKeyboardPos(pos_x = -1, pos_y = -1) {
        if (pos_x >= 0 && pos_y >= 0) {
            normal_keyboard.x = pos_x
            normal_keyboard.y = pos_y
        }
    }

    function setDigitalKeyboardPos(pos_x = -1, pos_y = -1) {
        if (pos_x >= 0 && pos_y >= 0) {
            digital_keyboard.x = pos_x
            digital_keyboard.y = pos_y
        }
    }

    function openNormalKeyboard(pos_x = -1, pos_y = -1, ratio = 1) {
        digital_keyboard.visible = false
        if (pos_x >= 0 && pos_y >= 0) {
            normal_keyboard.x = pos_x
            normal_keyboard.y = pos_y
        }
        normal_keyboard_ratio = ratio
        normal_keyboard.visible = true

        objTime.refreshTime()

        configTool.isUseStaticKeyboard = false
    }

    function openDigitalKeyboard(pos_x = -1, pos_y = -1, ratio = 1.1) {
        normal_keyboard.visible = false
        if (pos_x >= 0 && pos_y >= 0) {
            digital_keyboard.x = pos_x
            digital_keyboard.y = pos_y
        }
        digital_keyboard_ratio = ratio
        digital_keyboard.visible = true

        objTime.refreshTime()

        configTool.isUseStaticKeyboard = false
    }

    function getIsShift() {
        return keyEvent.getIsShiftDown()
    }

    Connections {
        target: configTool
        function onIsUseStaticKeyboardChanged() {
            if (configTool.isUseStaticKeyboard) {
                closeAll()
            }
        }
    }

    Rectangle {
        id: normal_keyboard
        width: 1150 * dpi_ratio * normal_keyboard_ratio
        height: 500 * dpi_ratio * normal_keyboard_ratio
        x: 80 * dpi_ratio
        y: 550 * dpi_ratio
        radius: ST.radius
        clip: true
        visible: false

        MouseArea {
            anchors.fill: parent
            property point clickPos: "0,0"
            onPressed: {
                clickPos = Qt.point(mouse.x, mouse.y)
            }
            onPositionChanged: {
                if (pressed) {
                    var delta = Qt.point(mouse.x - clickPos.x, mouse.y - clickPos.y)
                    normal_keyboard.x += delta.x
                    normal_keyboard.y += delta.y
                }
            }
            onClicked: {

                // logMgr.logEvtInfo4Qml("normal 键盘位置：" + normal_keyboard.x / dpi_ratio + "," + normal_keyboard.y / dpi_ratio)
            }
        }

        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: normal_keyboard.width
                height: normal_keyboard.height
                radius: ST.radius
            }
        }

        //背景
        LinearGradient {
            anchors.fill: parent
            start: Qt.point(0, 0)
            end: Qt.point(parent.width, parent.height)
            gradient: Gradient {
                GradientStop {
                    position: 0.0
                    color: "#F1F5FF"
                }
                GradientStop {
                    position: 1.0
                    color: "#DCDFE8"
                }
            }
        }
        Rectangle {
            anchors.top: parent.top
            anchors.right: parent.right
            anchors.rightMargin: 25 * dpi_ratio * normal_keyboard_ratio
            anchors.topMargin: 23 * dpi_ratio * normal_keyboard_ratio
            height:30 * dpi_ratio * normal_keyboard_ratio
            width: 250 * dpi_ratio * normal_keyboard_ratio
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 85 * dpi_ratio * normal_keyboard_ratio
            KeyType {
                Layout.preferredWidth: 50 * dpi_ratio * normal_keyboard_ratio
                key1: "LEFT"
                img_down: "/keyboard2/polygon 1_s.png"
                img_up: "/keyboard2/polygon 1_s.png"
            }
            KeyType {
                Layout.preferredWidth: 50 * dpi_ratio * normal_keyboard_ratio
                key1: "RIGHT"
                img_down: "/keyboard2/polygon 2.png"
                img_up: "/keyboard2/polygon 2.png"
            }
            KeyType {
                width: 110 * dpi_ratio * normal_keyboard_ratio
                key1: "CLOSE"
                img_down: "/keyboard2/digital_close_sz.png"
                img_up: "/keyboard2/digital_close_sz.png"
                onClicked: {
                    normal_keyboard.visible = false
                }
            }
            }
        }

        //键盘
        Item {
            anchors.fill: parent
            anchors.margins: 17 * dpi_ratio * normal_keyboard_ratio
            anchors.topMargin: 75 * dpi_ratio * normal_keyboard_ratio

            ColumnLayout {
                anchors.fill: parent
                spacing: 10 * dpi_ratio * normal_keyboard_ratio

                Item {
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    RowLayout {
                        anchors.fill: parent
                        spacing: 12 * dpi_ratio * normal_keyboard_ratio

                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "Q"
                            img_down: "/keyboard/normal_Q_s.png"
                            img_up: "/keyboard/normal_Q.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "W"
                            img_down: "/keyboard/normal_W_s.png"
                            img_up: "/keyboard/normal_W.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "E"
                            img_down: "/keyboard/normal_E_s.png"
                            img_up: "/keyboard/normal_E.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "R"
                            img_down: "/keyboard/normal_R_s.png"
                            img_up: "/keyboard/normal_R.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "T"
                            img_down: "/keyboard/normal_T_s.png"
                            img_up: "/keyboard/normal_T.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "Y"
                            img_down: "/keyboard/normal_Y_s.png"
                            img_up: "/keyboard/normal_Y.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "U"
                            img_down: "/keyboard/normal_U_s.png"
                            img_up: "/keyboard/normal_U.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "I"
                            img_down: "/keyboard/normal_I_s.png"
                            img_up: "/keyboard/normal_I.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "O"
                            img_down: "/keyboard/normal_O_s.png"
                            img_up: "/keyboard/normal_O.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "P"
                            img_down: "/keyboard/normal_P_s.png"
                            img_up: "/keyboard/normal_P.png"
                        }
                    }
                }

                Item {
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    RowLayout {
                        anchors.fill: parent
                        spacing: 12 * dpi_ratio * normal_keyboard_ratio

                        CusSpacer {
                            Layout.preferredWidth: 31 * dpi_ratio * normal_keyboard_ratio
                        }

                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "A"
                            img_down: "/keyboard/normal_A_s.png"
                            img_up: "/keyboard/normal_A.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "S"
                            img_down: "/keyboard/normal_S_s.png"
                            img_up: "/keyboard/normal_S.png"
                        }
                        KeyType {

                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "D"
                            img_down: "/keyboard/normal_D_s.png"
                            img_up: "/keyboard/normal_D.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "F"
                            img_down: "/keyboard/normal_F_s.png"
                            img_up: "/keyboard/normal_F.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "G"
                            img_down: "/keyboard/normal_G_s.png"
                            img_up: "/keyboard/normal_G.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "H"
                            img_down: "/keyboard/normal_H_s.png"
                            img_up: "/keyboard/normal_H.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "J"
                            img_down: "/keyboard/normal_J_s.png"
                            img_up: "/keyboard/normal_J.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "K"
                            img_down: "/keyboard/normal_K_s.png"
                            img_up: "/keyboard/normal_K.png"
                        }
                        KeyType {

                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "L"
                            img_down: "/keyboard/normal_L_s.png"
                            img_up: "/keyboard/normal_L.png"
                        }

                        CusSpacer {
                            Layout.preferredWidth: 43 * dpi_ratio * normal_keyboard_ratio
                        }
                    }
                }

                Item {
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    RowLayout {
                        anchors.fill: parent
                        spacing: 12 * dpi_ratio * normal_keyboard_ratio
                        KeyType2 {
                            Layout.preferredWidth: 190 * dpi_ratio * normal_keyboard_ratio
                            key1: "CAPS_LOCK"
                            img_down: "/keyboard2/normal_CAPS_xiao_s.png"
                            img_up: "/keyboard2/normal_CAPS_xiao.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "Z"
                            img_down: "/keyboard/normal_Z_s.png"
                            img_up: "/keyboard/normal_Z.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "X"
                            img_down: "/keyboard/normal_X_s.png"
                            img_up: "/keyboard/normal_X.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "C"
                            img_down: "/keyboard/normal_C_s.png"
                            img_up: "/keyboard/normal_C.png"
                        }
                        KeyType {

                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "V"
                            img_down: "/keyboard/normal_V_s.png"
                            img_up: "/keyboard/normal_V.png"
                        }
                        KeyType {

                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "B"
                            img_down: "/keyboard/normal_B_s.png"
                            img_up: "/keyboard/normal_B.png"
                        }
                        KeyType {

                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "N"
                            img_down: "/keyboard/normal_N_s.png"
                            img_up: "/keyboard/normal_N.png"
                        }
                        KeyType {

                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "M"
                            img_down: "/keyboard2/normal_M_s.png"
                            img_up: "/keyboard2/normal_M.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "backspace"
                            img_down: "/keyboard2/normal_backspace_s.png"
                            img_up: "/keyboard2/normal_backspace.png"
                        }
                    }
                }
                Item {
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    RowLayout {
                        anchors.fill: parent
                        spacing: 9 * dpi_ratio * normal_keyboard_ratio
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            is_cus_enevt: true
                            key1: "DESKTOP"
                            img_down: "/keyboard2/normal_desktop_s.png"
                            img_up: "/keyboard2/normal_desktop.png"
                            onClicked: {
                                is_mini = true
                                closeAll()
                            }
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "123"
                            img_down: "/keyboard2/normal_123_s.png"
                            img_up: "/keyboard2/normal_123.png"
                            onClicked: {
                                openDigitalKeyboard()
                                normal_keyboard.visible = false
                                digital_keyboard.visible = true
                            }
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "COMMA"
                            img_down: "/keyboard2/digital_comma_s.png"
                            img_up: "/keyboard2/digital_comma.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 500 * dpi_ratio * normal_keyboard_ratio
                            key1: "SPACE"
                            img_down: "/keyboard2/normal_SPACE_s.png"
                            img_up: "/keyboard2/normal_SPACE.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "AT"
                            img_down: "/keyboard2/normal_@_s.png"
                            img_up: "/keyboard2/normal_@.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 88 * dpi_ratio * normal_keyboard_ratio
                            key1: "SWITCH"
                            img_down: "/keyboard2/normal_switch_z_s.png"
                            img_up: "/keyboard2/normal_switch_z.png"
                        }
                        KeyType {
                            Layout.preferredWidth: 160 * dpi_ratio * normal_keyboard_ratio
                            key1: "ENTER"
                            img_down: "/keyboard2/digital_enter_s.png"
                            img_up: "/keyboard2/digital_enter.png"
                        }
                    }
                }
            }
        }
    }

    Rectangle {
        id: digital_keyboard
        width: 430 * dpi_ratio * digital_keyboard_ratio
        height: 500 * dpi_ratio * digital_keyboard_ratio
        visible: false
        x: 1400 * dpi_ratio
        y: 550 * dpi_ratio
        MouseArea {
            anchors.fill: parent
            property point clickPos: "0,0"
            onPressed: {
                clickPos = Qt.point(mouse.x, mouse.y)
            }
            onPositionChanged: {
                if (pressed) {
                    var delta = Qt.point(mouse.x - clickPos.x, mouse.y - clickPos.y)
                    digital_keyboard.x += delta.x
                    digital_keyboard.y += delta.y
                }
            }
            onClicked: {

                // logMgr.logEvtInfo4Qml("digital 键盘位置：" + digital_keyboard.x / dpi_ratio + "," + digital_keyboard.y / dpi_ratio)
            }
        }
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: digital_keyboard.width
                height: digital_keyboard.height
                radius: ST.radius
            }
        }

        //背景
        LinearGradient {
            anchors.fill: parent
            start: Qt.point(0, 0)
            end: Qt.point(parent.width, parent.height)
            gradient: Gradient {
                GradientStop {
                    position: 0.0
                    color: "#F1F5FF"
                }
                GradientStop {
                    position: 1.0
                    color: "#DCDFE8"
                }
            }
        }
        Rectangle {
            //anchors.fill: parent
            anchors.top: parent.top
            anchors.right: parent.right
            anchors.rightMargin: 25 * dpi_ratio * normal_keyboard_ratio
            anchors.topMargin: 23 * dpi_ratio * normal_keyboard_ratio
            height:30 * dpi_ratio * normal_keyboard_ratio
            width: 250 * dpi_ratio * normal_keyboard_ratio
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 85 * dpi_ratio * normal_keyboard_ratio
            //anchors.topMargin: 0 * dpi_ratio * normal_keyboard_ratio
            KeyType {
                Layout.preferredWidth: 50 * dpi_ratio * normal_keyboard_ratio
                key1: "LEFT"
                img_down: "/keyboard2/polygon 1_s.png"
                img_up: "/keyboard2/polygon 1_s.png"
            }
            KeyType {
                Layout.preferredWidth: 50 * dpi_ratio * normal_keyboard_ratio
                key1: "RIGHT"
                img_down: "/keyboard2/polygon 2.png"
                img_up: "/keyboard2/polygon 2.png"
            }
            KeyType {
                width: 110 * dpi_ratio * normal_keyboard_ratio
                key1: "CLOSE"
                img_down: "/keyboard2/digital_close_sz.png"
                img_up: "/keyboard2/digital_close_sz.png"
                onClicked: {
                    digital_keyboard.visible = false
                }
            }
            }
        }
        //键盘
        Item {
            anchors.fill: parent
            anchors.margins: 17 * dpi_ratio * digital_keyboard_ratio
            anchors.topMargin: 75 * dpi_ratio * digital_keyboard_ratio

            GridLayout {
                anchors.fill: parent
                columns: 4
                rows: 4
                columnSpacing: 9 * dpi_ratio * digital_keyboard_ratio
                rowSpacing: 9 * dpi_ratio * digital_keyboard_ratio
                flow: GridView.LeftToRight
                layoutDirection: Qt.LeftToRight
                property int cell_width: (width - ((columns - 1) * columnSpacing)) / columns
                property int cell_height: (height - ((rows - 1) * rowSpacing)) / rows

                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "7"
                    img_down: "/keyboard/digital_7_s.png"
                    img_up: "/keyboard/digital_7.png"
                }
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "8"
                    img_down: "/keyboard/digital_8_s.png"
                    img_up: "/keyboard/digital_8.png"
                }
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "9"
                    img_down: "/keyboard/digital_9_s.png"
                    img_up: "/keyboard/digital_9.png"
                }
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "BACKSPACE"
                    img_down: "/keyboard/normal_backspace_s.png"
                    img_up: "/keyboard/normal_backspace.png"
                }
                //////////////////////
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "4"
                    img_down: "/keyboard/digital_4_s.png"
                    img_up: "/keyboard/digital_4.png"
                }
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "5"
                    img_down: "/keyboard/digital_5_s.png"
                    img_up: "/keyboard/digital_5.png"
                }
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "6"
                    img_down: "/keyboard/digital_6_s.png"
                    img_up: "/keyboard/digital_6.png"
                }
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "SPACE"
                    img_down: "/keyboard/digital_space_s.png"
                    img_up: "/keyboard/digital_space.png"
                }
                //////////////////////
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "1"
                    img_down: "/keyboard/digital_1_s.png"
                    img_up: "/keyboard/digital_1.png"
                }
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "2"
                    img_down: "/keyboard/digital_2_s.png"
                    img_up: "/keyboard/digital_2.png"
                }
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "3"
                    img_down: "/keyboard/digital_3_s.png"
                    img_up: "/keyboard/digital_3.png"
                }
                KeyType {
                    Layout.rowSpan: 2
                    Layout.columnSpan: 1
                    key1: "ENTER"
                    img_down: qsTr("/keyboard2/digital_enter_sz_hs.png")
                    img_up: qsTr("/keyboard2/digital_enter_sz_h.png")
                }
                //////////////////////
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "ABC"
                    img_down: "/keyboard/digital_ABC_s.png"
                    img_up: "/keyboard/digital_ABC.png"
                    onClicked: {
                        digital_keyboard.visible = false
                        normal_keyboard.visible = true
                    }
                }
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "0"
                    img_down: "/keyboard/digital_0_s.png"
                    img_up: "/keyboard/digital_0.png"
                }
                KeyType {
                    Layout.rowSpan: 1
                    Layout.columnSpan: 1
                    key1: "."
                    img_down: "/keyboard/digital_point_s.png"
                    img_up: "/keyboard/digital_point.png"
                }
            }
        }
    }

    component KeyType: MouseArea {
        id: key_type_root

        Layout.fillHeight: true
        Layout.fillWidth: true
        property bool is_enevt: true
        property bool is_cus_enevt: false
        property string key1
        property string key2
        property string img_up: ""
        property string img_down: ""
        property bool is_single_key: key2 == "" //是否是单键
        property bool is_down: false
        function getKeyActive() {
            if (is_single_key) {
                return key1.toUpperCase()
            } else {
                if (!getIsShift()) {
                    return key1
                } else {
                    return key2
                }
            }
        }

        Image {
            id: img_keytype1_back
            anchors.fill: parent
            //source: key_type_root.pressed ? img_down : img_up
            source: key_type_root.is_down ? img_down : img_up
        }

        onClicked: {
            if (!is_cus_enevt) {
                keyEvent.sendEvent(key_type_root.getKeyActive())
            }
            soundCtrl.playClick()
        }

        onPressed: {
            is_down = true
        }

        onReleased: {
            if (!timer.running) {
                timer.start()
            }
        }

        onCanceled: {
            if (!timer.running) {
                timer.start()
            }
        }

        Timer {
            id: timer
            interval: 80
            repeat: false
            onTriggered: {
                key_type_root.is_down = false
            }
        }
    }

    component KeyType2: Rectangle {
        id: key_type2_root
        color: "transparent"

        Layout.fillHeight: true
        Layout.fillWidth: true

        function getKeyActive() {
            if (is_single_key) {
                return key1.toUpperCase()
            } else {
                if (!getIsShift()) {
                    return key1
                } else {
                    return key2
                }
            }
        }

        property string key1
        property string key2
        property bool is_enevt: true
        property alias pressed: mouse_area_keytype2.pressed

        property bool is_down: false

        property string img_up: ""
        property string img_down: ""

        property bool is_single_key: key2 == "" //是否是单键

        Image {
            id: img_keytype2_back
            anchors.fill: parent
            source: if (is_down) {
                        return img_down
                    } else {
                        return pressed ? img_down : img_up
                    }
        }
        MouseArea {
            id: mouse_area_keytype2
            anchors.fill: parent
            onClicked: {
                //                toast.openWarn(key_type2_root.getKeyActive())
                soundCtrl.playClick()

                keyEvent.sendEvent(key_type2_root.getKeyActive())
                key_type2_root.is_down = !key_type2_root.is_down
            }
        }
    }
}
