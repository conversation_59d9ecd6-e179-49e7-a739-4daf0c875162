﻿import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import ShopCartModel 1.0
import SettingEnum 1.0
import EnumTool 1.0
import "PayPage"
import "Utils"
import "Common"
import "SettingPage"
import "NetOrderPage"
import "QueryPage"
import "GodownPage"
import "MembershipPage"
import "StatisticsPage"
import "HandOverPage"
import "LoginPage"
import "GodownPageOld"
import "."

Window {
    id: window_root
    visible: true
    title: qsTr("BUYHOO")
    flags: is_program_top || is_mini ? Qt.Window | Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint : Qt.Window | Qt.FramelessWindowHint // Qt.Window |
    color: "transparent"
    property bool is_program_top: settingTool.getSetting(SettingEnum.IS_PROGRAM_TOP)
    property int netErrorCounts:0
    // DPI比例
    readonly property real dpi_ratio: utils4Qml.getWidthRatio()
    readonly property real dpi_ratio_second_screen: utils4Qml.getWidthRatio(1)

    readonly property real window_width: utils4Qml.getScreenWidth() + 1
    readonly property real window_height: utils4Qml.getScreenHeight() + 1

    property bool is_mini: false

    property bool is_use_virtual_keyboard: configTool.isUseVirtualKeyboard

    width: is_mini ? rect_mini.width : window_width
    height: is_mini ? rect_mini.height : window_height

    property int compo_index: EnumTool.PAGE_LOGIN
    property int memberOperation: 0; //1:新增 2:管理
    property int orderCount: 0; //待发货数量
    property bool isNeedUpdate: false; //是否可以升级

    onCompo_indexChanged: {
        keyboard_c.closeAll()
    }

    SecondWindow {
        id: second_window
        height: window_height
        color: "transparent"
        property bool is_use_second_screen: settingTool.getSetting(SettingEnum.IS_USE_SECOND_SCREEN)
        visible: is_use_second_screen && !is_mini
        width: window_width
        y: 0

//        onClosing: {
//            window_root.close() //关闭第二个窗口时整个程序关闭
//        }

        Connections {
            target: utils4Qml
            function onSigChangeSecondScreenVisibleState(is_show) {
                second_window.is_use_second_screen = is_show
            }
        }
    }
    RowLayout {
        anchors.fill: parent
        spacing: 0
        visible: !is_mini

        CusRect {
            id: rect_main_screen
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            TitleBar {
                id: title_bar
                height: 100 * dpi_ratio
                width: parent.width
            }

            PageSwitcher {
                id: page_switcher
                width: parent.width
                height: parent.height - title_bar.height
                y: title_bar.height
            }

            // 要实时交互 不使用loader
            PayPage {
                id: pay_page
                visible: window_root.compo_index === EnumTool.PAGE_PAY && !is_mini
                anchors.top: title_bar.bottom
                width: rect_main_screen.width
                height: rect_main_screen.height - title_bar.height
                Connections {
                    target: page_switcher
                    function onPayPageInit() {
                        pay_page.pay_methods.activeGoodsManager(false)
                    }
                }
            }
            property alias popup_update: popup_update
            PopupUpdateNew {
                id: popup_update
            }
            Loader {
                id: loader_login_page
                visible: window_root.compo_index === EnumTool.PAGE_LOGIN
                anchors.fill: parent
                sourceComponent: login_page //默认就要加载
            }

            Loader {
                id: loader_query_page
                visible: window_root.compo_index === EnumTool.PAGE_QUERY
                anchors.top: title_bar.bottom
                width: rect_main_screen.width
                height: rect_main_screen.height - title_bar.height

                function loadItem() {
                    if (!loader_query_page.loaded())
                        sourceComponent = query_page
                }

                onVisibleChanged: {
                    loadItem()
                }
            }

            Loader {
                id: loader_godown_page
                visible: window_root.compo_index === EnumTool.PAGE_GODOWN
                anchors.top: title_bar.bottom
                width: rect_main_screen.width
                height: rect_main_screen.height - title_bar.height

                onVisibleChanged: {
                    if (visible && !loader_godown_page.loaded())
                        sourceComponent = godown_page
                }
            }

            Loader {
                id: loader_godown_page_old
                visible: window_root.compo_index === EnumTool.PAGE_GODOWN_OLD
                anchors.top: title_bar.bottom
                width: rect_main_screen.width
                height: rect_main_screen.height - title_bar.height

                onVisibleChanged: {
                    if (visible && !loader_godown_page_old.loaded())
                        sourceComponent = godown_page_old
                }
            }

            Loader {
                id: loader_net_order_page
                visible: window_root.compo_index === EnumTool.PAGE_NET_ORDER
                anchors.top: title_bar.bottom
                width: rect_main_screen.width
                height: rect_main_screen.height - title_bar.height

                onVisibleChanged: {
                    if (visible && !loader_net_order_page.loaded())
                        sourceComponent = net_order_page
                }
            }

            Loader {
                id: loader_setting_page
                visible: window_root.compo_index === EnumTool.PAGE_SETTING
                anchors.top: title_bar.bottom
                width: rect_main_screen.width
                height: rect_main_screen.height - title_bar.height

                onVisibleChanged: {
                    if (visible && !loader_setting_page.loaded())
                        sourceComponent = setting_page
                }
            }

            Loader {
                id: loader_membership_page
                visible: window_root.compo_index === EnumTool.PAGE_MEMBERSHIP
                anchors.top: title_bar.bottom
                width: rect_main_screen.width
                height: rect_main_screen.height - title_bar.height

                onVisibleChanged: {
                    if (visible && !loader_membership_page.loaded())
                        sourceComponent = membership_page
                }
            }

            Loader {
                id: loader_statistics_page
                visible: window_root.compo_index === EnumTool.PAGE_STATISTICS
                anchors.top: title_bar.bottom
                width: rect_main_screen.width
                height: rect_main_screen.height - title_bar.height

                onVisibleChanged: {
                    if (visible && !loader_statistics_page.loaded())
                        sourceComponent = statistics_page
                        loader_statistics_page.item.open()
                }
            }

            Loader {
                id: loader_hand_over_page
                visible: window_root.compo_index === EnumTool.PAGE_HANDOVER
                anchors.top: title_bar.bottom
                width: rect_main_screen.width
                height: rect_main_screen.height - title_bar.height

                onVisibleChanged: {
                    if (visible && !loader_hand_over_page.loaded())
                        sourceComponent = hand_over_page
                }
            }

            //liuchao 2023116 ---------- 全局快捷键
            Shortcut {
                context: Qt.ApplicationShortcut
                sequence: "F8"
                onActivated: {
                    console.log("F8 pressed")
                }
            }

            Component {
                id: login_page
                LoginPage {}
            }

            Component {
                id: query_page
                QueryPage {}
            }
            Component {
                id: net_order_page
                NetOrderPage {}
            }
            Component {
                id: setting_page
                SettingPageC {}
            }
            Component {
                id: godown_page
                GodownPage {}
            }
            Component {
                id: godown_page_old
                GodownPageOld {}
            }
            Component {
                id: membership_page
                MembershipPage {}
            }
            Component {
                id: statistics_page
                StatisticsPage {}
            }
            Component {
                id: hand_over_page
                HandOverPage {}
            }
        }
    }

    CusRect {
        id: rect_net_status
        width: 225 * ST.dpi
        height: 58 * ST.dpi
        x: parent.width / 2 - width / 2
        y: 13 * ST.dpi
        visible: netStatusCtrl.netStatus == EnumTool.NETWORK_STATUS__DISCONNECTED && (netErrorCounts>2)
        color: ST.color_red
        opacity: 1
        radius: ST.radius

        CusMoveArea2 {
            anchors.fill: parent
            control: rect_net_status
        }

        Timer {
            id: timer_net_status
            repeat: true
            running: true
            interval: 100
            property int opacity_status: 0
            onTriggered: {

                if (rect_net_status.opacity == 1) {
                    opacity_status = 1
                } else if (rect_net_status.opacity <= 0.4) {
                    opacity_status = 0
                }

                if (opacity_status == 0) {
                    rect_net_status.opacity += .1
                } else {
                    rect_net_status.opacity -= .1
                }
            }
        }

        CusImg {
            source: "/Images/wifi.png"
            width: 32 * ST.dpi
            height: width
            anchors.verticalCenter: parent.verticalCenter
            x: 26 * ST.dpi
        }

        CusText {
            anchors.verticalCenter: parent.verticalCenter
            x: 64 * ST.dpi
            font.pixelSize: 28 * ST.dpi
            color: ST.color_white
            text: switch (netStatusCtrl.netStatus) {
                  case EnumTool.NETWORK_STATUS__CONNECTED:
                      return qsTr("网络已连接")
                  case EnumTool.NETWORK_STATUS__DISCONNECTED:
                      if(netErrorCounts > 2){
                        netErrorCounts = 0
                        return qsTr("无网络连接")
                      }else{
                        netErrorCounts += 1
                        return qsTr("无网络连接")
                      }

                  case EnumTool.NETWORK_STATUS__UNKNOW:
                      return qsTr("网络异常")
                  }
        }
    }

    Connections {
        target: utils4Qml
        function onSigOpenToast(msg, level) {
            switch (level) {
            case EnumTool.TOAST_LEVEL__INFO:
                toast.openInfo(msg)
                break
            case EnumTool.TOAST_LEVEL__WARN:
                toast.openWarn(msg)
                break
            case EnumTool.TOAST_LEVEL__ERROR:
                toast.openError(msg)
                break
            }
        }
    }

    function showVersionPop() {
        window_root.loader_4_version.sourceComponent = compo_version_info
        window_root.loader_4_version.item.open()
    }
    Component {
        id: compo_version_info
        PopupVersion {}
    }

    function showUpdatePop() {
        window_root.loader_4_version.sourceComponent = compo_update_info
        window_root.loader_4_version.item.open()
    }
    Component {
        id: compo_update_info
        PopupUpdate {}
    }
    // 自动更新
    Connections {
        target: updateCtrl
        function onSigNeedUpgrade(is_fource_update) {
            console.log("onSigNeedUpgrade")
            showUpdatePop()
        }
    }
    //更新检测触发（新）
    Connections {
        target: shopControl
        function onSigCheckUpdate() {
            logMgr.logEvtInfo4Qml("开始向后端请求检查更新")
            updateCtrl.reqGetVersionInfoNew(function (data_status, data) {
                if (data_status == EnumTool.DATA_STATUS__VALID) {
                    logMgr.logEvtInfo4Qml("需要升级")
                    isNeedUpdate = true
                    popup_update.show()
                } else {
                    toast_root.open("当前已是最新版本")
                }
            })
        }
    }
    // 快速新增商品
    function showUploadGoodsPopup(barcode) {
        window_root.loader_4_quick_create_goods.sourceComponent = compo_quick_upload_goods
        window_root.loader_4_quick_create_goods.item.open(barcode)
    }

    Component {
        id: compo_quick_upload_goods
        PopupUploadGoods {
            title_name: qsTr("快速新增商品")

            onSigClose: {
                pay_page.search_bar.tf_search.forceActiveFocus()
                pay_page.search_bar.tf_search.focus = true
            }
        }
    }

    property alias loader_4_shutdown_confirm: loader_4_shutdown_confirm
    Loader {
        id: loader_4_shutdown_confirm
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_version: loader_4_version
    Loader {
        id: loader_4_version
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_area_division: loader_4_area_division
    Loader {
        id: loader_4_area_division
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_confirm: loader_4_confirm
    Loader {
        id: loader_4_confirm
        z: 80
        anchors.fill: parent
    }

    property alias loader_center: loader_center
    Loader {
        id: loader_center
        z: 80
        anchors.fill: parent
    }

    property alias loader4_cash_pay: loader4_cash_pay
    Loader {
        id: loader4_cash_pay
        z: 80
        anchors.fill: parent
    }
    property alias loader4_vipCard_pay: loader4_vipCard_pay
    Loader {
        id: loader4_vipCard_pay
        z: 80
        anchors.fill: parent
    }
    property alias loader4_combined: loader4_combined
    Loader {
        id: loader4_combined
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_no_code_goods: loader_4_no_code_goods
    Loader {
        id: loader_4_no_code_goods
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_calendar_begin: loader_4_calendar_begin
    Loader {
        id: loader_4_calendar_begin
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_calendar_end: loader_4_calendar_end
    Loader {
        id: loader_4_calendar_end
        z: 80
        anchors.fill: parent
    }
    property alias loader_4_calendar_day: loader_4_calendar_day
    Loader {
        id: loader_4_calendar_day
        z: 80
        anchors.fill: parent
    }
    property alias loader_4_time_selector_begin: loader_4_time_selector_begin
    Loader {
        id: loader_4_time_selector_begin
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_time_selector_end: loader_4_time_selector_end
    Loader {
        id: loader_4_time_selector_end
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_order_discount: loader_4_order_discount
    Loader {
        id: loader_4_order_discount
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_add_goods_2_cur_class: loader_4_add_goods_2_cur_class
    Loader {
        id: loader_4_add_goods_2_cur_class
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_stock_add: loader_4_stock_add
    Loader {
        id: loader_4_stock_add
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_stock_del: loader_4_stock_del
    Loader {
        id: loader_4_stock_del
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_category_confirm: loader_4_category_confirm
    Loader {
        id: loader_4_category_confirm
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_price_tag_setting: loader_4_price_tag_setting
    Loader {
        id: loader_4_price_tag_setting
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_member_recharge: loader_4_member_recharge
    Loader {
        id: loader_4_member_recharge
        z: 80
        anchors.fill: parent
    }
    property alias loader_4_member_refund: loader_4_member_refund
    Loader {
        id: loader_4_member_refund
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_scan_order: loader_4_scan_order
    Loader {
        id: loader_4_scan_order
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_courier_list: loader_4_courier_list
    Loader {
        id: loader_4_courier_list
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_refund_details: loader_4_refund_details
    Loader {
        id: loader_4_refund_details
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_add_home_goods_kind: loader_4_add_home_goods_kind
    Loader {
        id: loader_4_add_home_goods_kind
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_del_goods_from_virtual_goods_kind: loader_4_del_goods_from_virtual_goods_kind
    Loader {
        id: loader_4_del_goods_from_virtual_goods_kind
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_del_cur_goods: loader_4_del_cur_goods
    Loader {
        id: loader_4_del_cur_goods
        z: 80
        anchors.fill: parent
    }

    property alias compo_del_cur_supplier: compo_del_cur_supplier
    Loader {
        id: compo_del_cur_supplier
        z: 80
        anchors.fill: parent
    }

    property alias compo_del_cur_supplier_categroy: compo_del_cur_supplier_categroy
    Loader {
        id: compo_del_cur_supplier_categroy
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_del_virtual_goods_kind: loader_4_del_virtual_goods_kind
    Loader {
        id: loader_4_del_virtual_goods_kind
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_quick_create_goods: loader_4_quick_create_goods
    Loader {
        id: loader_4_quick_create_goods
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_create_goods_unit: loader_4_create_goods_unit
    Loader {
        id: loader_4_create_goods_unit
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_delete_goods_unit: loader_4_delete_goods_unit
    Loader {
        id: loader_4_delete_goods_unit
        z: 80
        anchors.fill: parent
    }

    property alias loader4_face: loader4_face
    Loader {
        id: loader4_face
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_sale_order_refund: loader_4_sale_order_refund
    Loader {
        id: loader_4_sale_order_refund
        z: 81
        anchors.fill: parent
    }

    property alias loader_4_pay_status: loader_4_pay_status
    Loader {
        id: loader_4_pay_status
        z: 81
        anchors.fill: parent
    }

    property alias loader_4_tag_list: loader_4_tag_list
    Loader {
        id: loader_4_tag_list
        z: 81
        anchors.fill: parent
    }

    property alias loader_4_tag_list_add: loader_4_tag_list_add
    Loader {
        id: loader_4_tag_list_add
        z: 81
        anchors.fill: parent
    }

    property alias loader_4_add_goods_2_cur_goods_kind: loader_4_add_goods_2_cur_goods_kind
    Loader {
        id: loader_4_add_goods_2_cur_goods_kind
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_del_cur_goods_4_recognition: loader_4_del_cur_goods_4_recognition
    Loader {
        id: loader_4_del_cur_goods_4_recognition
        z: 80
        anchors.fill: parent
    }

    property alias loader_4_real_recognition_list: loader_4_real_recognition_list
    Loader {
        id: loader_4_real_recognition_list
        z: 80
        anchors.fill: parent
    }

    property alias keyboard_c: keyboard_c
    CusKeyboardInternational {
        id: keyboard_c
        visible: true
        z: 400
    }

    ToastC {
        id: toast
        anchors.fill: parent
        visible: false
        z: 101
    }

    //迷你化
    MiniWindow {
        id: rect_mini
        width: 240 * dpi_ratio * configTool.fontRatio
        height: 60 * dpi_ratio * configTool.fontRatio
        visible: is_mini
        z: 80
    }
}
