﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import EnumTool 1.0
import GoodsDataModel 1.0

import "ProductManagement"

CusRect {
    color: ST.color_white_deeper

    signal clickedSupplier(var supplier_unique, var supplier_name)
    signal close

    function refreshSupplierAndFocus(supplier_unique) {
        if (supplier_unique == "")
            return

        for (var i = 0; i < lm_supplier.count; ++i) {
            var cur_item = lm_supplier.get(i)

            if (cur_item.supplier_unique == supplier_unique) {
                lv_supplier.currentIndex = i
                break
            }
        }
    }

    // 供应商分类选择
    function refreshSupplierInfo() {
        lm_supplier.clear()
        var json_data = JSON.parse(supplierControl.getSupplierJson())
        for (var i = 0; i < json_data.length; ++i) {
            var cur_goods_kind = json_data[i]
            lm_supplier.append(cur_goods_kind)
        }
        lv_supplier.currentIndex = -1
    }

    property var default_supplier_unique: ""

    function open(supplier_unique) {
        default_supplier_unique = supplier_unique
    }

    onVisibleChanged: {
        if (visible)
            refreshSupplierInfo()
    }

    MouseArea {
        anchors.fill: parent
    }

    RowLayout {
        anchors.fill: parent

        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: 94 * dpi_ratio
            color: ST.color_white_pure

            CusRect {
                width: parent.width
                height: width
                anchors.verticalCenter: parent.verticalCenter
                anchors.verticalCenterOffset: -100 * dpi_ratio
                color: ST.color_transparent
                Image {
                    anchors {
                        horizontalCenter: parent.horizontalCenter
                        horizontalCenterOffset: 5 * dpi_ratio

                        verticalCenter: parent.verticalCenter
                        verticalCenterOffset: -10 * dpi_ratio
                    }
                    fillMode: Image.PreserveAspectFit
                    width: 40 * dpi_ratio
                    height: width
                    source: "/Images/collapse.png"
                }
                CusText {
                    text: "收起"
                    color: ST.color_green
                    anchors {
                        horizontalCenter: parent.horizontalCenter
                        verticalCenter: parent.verticalCenter
                        verticalCenterOffset: 40 * dpi_ratio
                    }
                }
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    close()
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_transparent

            CusRect {
                anchors.fill: parent
                anchors.margins: 40 * dpi_ratio
                color: ST.color_white_pure
                radius: ST.radius

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20 * dpi_ratio
                    //标题栏
                    Item {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 50 * dpi_ratio

                        RowLayout {
                            anchors.fill: parent
                            spacing: 2 * dpi_ratio

                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 710 * dpi_ratio
                                clip: true
                                color: ST.color_transparent
                                CusText {
                                    anchors.verticalCenter: parent.verticalCenter
                                    x: 50 * dpi_ratio
                                    text: "名称"
                                    color: ST.color_grey_font
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 335 * dpi_ratio
                                clip: true
                                color: ST.color_transparent
                                CusText {
                                    anchors.centerIn: parent
                                    text: "联系人"
                                    color: ST.color_grey_font
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 835 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: "联系电话"
                                    color: ST.color_grey_font
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 500 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: "所属分类"
                                    color: ST.color_grey_font
                                }
                            }
                        }
                    }

                    ListView {
                        id: lv_supplier
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true
                        highlightFollowsCurrentItem: true
                        highlightMoveDuration: 0
                        focus: true
                        currentIndex: -1
                        model: ListModel {
                            id: lm_supplier
                        }
                        delegate: CusRect {
                            id: lm_supplier_item
                            color: ST.color_transparent
                            width: lv_supplier.width
                            height: 80 * dpi_ratio
                            property bool is_cur_item: lv_supplier.currentItem == this

                            property var supplier_id_: supplier_id
                            property var supplier_unique_: supplier_unique
                            property var supplier_name_: supplier_name
                            property var company_leagl_: company_leagl
                            property var supplier_phone_: supplier_phone
                            property var supplier_address_: supplier_address
                            property var supplier_kind_name_: supplier_kind_name
                            property var supplier_kind_id_: supplier_kind_id

                            CusRect {
                                anchors.fill: parent
                                anchors.topMargin: 8 * dpi_ratio
                                anchors.bottomMargin: 8 * dpi_ratio
                                color: lm_supplier_item.is_cur_item ? ST.color_green : ST.color_transparent
                                radius: ST.radius

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 2 * dpi_ratio

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 710 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.verticalCenter: parent.verticalCenter
                                            x: 50 * dpi_ratio
                                            text: supplier_name //"名称"
                                            color: lm_supplier_item.is_cur_item ? ST.color_white_pure : ST.color_black
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 335 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            text: company_leagl //"联系人"
                                            color: lm_supplier_item.is_cur_item ? ST.color_white_pure : ST.color_black
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 835 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: supplier_phone //"联系电话"
                                            color: lm_supplier_item.is_cur_item ? ST.color_white_pure : ST.color_black
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 500 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: supplier_kind_name //"所属分类"
                                            color: lm_supplier_item.is_cur_item ? ST.color_white_pure : ST.color_black
                                        }
                                    }
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        lv_supplier.currentIndex = index
                                        clickedSupplier(supplier_unique_, supplier_name)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
