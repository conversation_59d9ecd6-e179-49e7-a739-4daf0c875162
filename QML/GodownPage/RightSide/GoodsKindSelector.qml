﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import EnumTool 1.0
import GoodsDataModel 1.0

import "ProductManagement"

CusRect {
    id: rect_goods_kind_chooser
    color: ST.color_white_deeper

    signal close
    signal clickedSupplier(var goods_kind_unique, var goods_kind_name)

    // 刷新一级分类
    function refreshGoodsKind1() {
        lm_goods_kind_1.clear()
        gv_goods_kind_1.currentIndex = -1
        var json_root_goods_kind = JSON.parse(goodsKindManager.getRootGoodsKind4Qml())
        for (var i = 0; i < json_root_goods_kind.length; ++i) {
            var cur_item = json_root_goods_kind[i]
            lm_goods_kind_1.append(cur_item)
        }
    }

    // 刷新二级分类
    function refreshGoodsKind2(kind_unique) {
        if (kind_unique == "")
            return

        lm_goods_kind_2.clear()
        gv_goods_kind_2.currentIndex = -1
        var json_sub_goods_kind = JSON.parse(goodsKindManager.getSubGoodsKind4Qml(kind_unique))

        if (json_sub_goods_kind == null)
            return

        for (var i = 0; i < json_sub_goods_kind.length; ++i) {
            var cur_item = json_sub_goods_kind[i]
            lm_goods_kind_2.append(cur_item)
        }
    }
    // 选中分类(1级+2级)
    function focusGoodsKindByKindUnique(root_kind_unique, sub_kind_unique) {
        foucesRootGoodsKindByKindUnique(root_kind_unique)
        focusSubGoodsKindByKindUnique(sub_kind_unique)
    }

    // 选中二级分类
    function focusSubGoodsKindByKindUnique(kind_unique) {
        for (var i = 0; i < lm_goods_kind_2.count; ++i) {
            var cur_goods_kind = lm_goods_kind_2.get(i)
            if (cur_goods_kind.goods_kind_unique == kind_unique) {
                gv_goods_kind_2.currentIndex = i
                return
            }
        }
    }

    // 选中一级分类
    function foucesRootGoodsKindByKindUnique(kind_unique) {
        for (var i = 0; i < lm_goods_kind_1.count; ++i) {
            var cur_goods_kind = lm_goods_kind_1.get(i)

            if (cur_goods_kind.goods_kind_unique == kind_unique) {
                gv_goods_kind_1.currentIndex = i
                return
            }
        }
    }

    function showGoodsKindAndFocus(goods_kind_unique) {
        if (goods_kind_unique == "")
            return

        refreshGoodsKind1()
        var json_goods_kind_info = JSON.parse(goodsKindManager.getGoodsKindByKindUnique4Qml(goods_kind_unique))

        if (json_goods_kind_info == null)
            return

        refreshGoodsKind2(json_goods_kind_info.goods_kind_parunique)
        focusGoodsKindByKindUnique(json_goods_kind_info.goods_kind_parunique, goods_kind_unique)
    }

    function refreshGoodsKind() {
        refreshGoodsKind1()
        lm_goods_kind_2.clear()
    }

    MouseArea {
        anchors.fill: parent
    }

    RowLayout {
        anchors.fill: parent

        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: 94 * dpi_ratio
            color: ST.color_white_pure

            CusRect {
                width: parent.width
                height: width
                anchors.verticalCenter: parent.verticalCenter
                anchors.verticalCenterOffset: -100 * dpi_ratio
                color: ST.color_transparent
                Image {
                    anchors {
                        horizontalCenter: parent.horizontalCenter
                        horizontalCenterOffset: 5 * dpi_ratio

                        verticalCenter: parent.verticalCenter
                        verticalCenterOffset: -10 * dpi_ratio
                    }
                    fillMode: Image.PreserveAspectFit
                    width: 40 * dpi_ratio
                    height: width
                    source: "/Images/collapse.png"
                }
                CusText {
                    text: "收起"
                    color: ST.color_green
                    anchors {
                        horizontalCenter: parent.horizontalCenter
                        verticalCenter: parent.verticalCenter
                        verticalCenterOffset: 40 * dpi_ratio
                    }
                }
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    goods_detail.edit_goods.rect_goods_detail_basic_info.is_open_goods_kind_selector = false
                    goods_detail.create_goods.rect_goods_detail_basic_info.is_open_goods_kind_selector = false
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_transparent

            CusRect {
                anchors.fill: parent
                anchors.margins: 40 * dpi_ratio
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 260 * dpi_ratio
                        color: ST.color_transparent
                        ColumnLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 60 * dpi_ratio
                                color: ST.color_white_deeper
                                CusText {
                                    text: "商品一级分类"
                                    anchors.verticalCenter: parent.verticalCenter
                                    x: 40 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_white_pure
                                radius: ST.radius
                                GridView {
                                    id: gv_goods_kind_1
                                    anchors.fill: parent
                                    anchors {
                                        leftMargin: 20 * dpi_ratio
                                        rightMargin: 20 * dpi_ratio
                                        topMargin: 10 * dpi_ratio
                                        bottomMargin: 10 * dpi_ratio
                                    }
                                    property int row_num: 5
                                    property int column_num: 2
                                    cellWidth: width / row_num
                                    cellHeight: (60 + 15) * dpi_ratio
                                    model: ListModel {
                                        id: lm_goods_kind_1
                                    }

                                    flow: GridView.LeftToRight
                                    clip: true

                                    delegate: Component {
                                        CusRect {
                                            id: rect_goods_kind
                                            property bool is_cur_item: gv_goods_kind_1.currentItem == this
                                            width: gv_goods_kind_1.cellWidth
                                            height: gv_goods_kind_1.cellHeight
                                            color: ST.color_transparent

                                            CusRect {
                                                anchors {
                                                    fill: parent
                                                    margins: 15 * dpi_ratio
                                                }
                                                border {
                                                    width: is_cur_item ? 0 : 1
                                                    color: ST.color_grey_border
                                                }
                                                radius: ST.radius
                                                color: is_cur_item ? ST.color_green : ST.color_white_pure

                                                CusText {
                                                    anchors.centerIn: parent
                                                    text: goods_kind_name
                                                    color: is_cur_item ? ST.color_white_pure : ST.color_black
                                                }
                                                MouseArea {
                                                    anchors.fill: parent
                                                    onClicked: {
                                                        gv_goods_kind_1.currentIndex = index
                                                        refreshGoodsKind2(goods_kind_unique)
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent
                        ColumnLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 60 * dpi_ratio
                                color: ST.color_white_deeper
                                CusText {
                                    text: "商品二级分类"
                                    anchors.verticalCenter: parent.verticalCenter
                                    x: 40 * dpi_ratio
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_white_pure
                                radius: ST.radius

                                GridView {
                                    id: gv_goods_kind_2
                                    anchors.fill: parent
                                    anchors {
                                        leftMargin: 20 * dpi_ratio
                                        rightMargin: 20 * dpi_ratio
                                        topMargin: 10 * dpi_ratio
                                        bottomMargin: 10 * dpi_ratio
                                    }
                                    property int row_num: 5
                                    property int column_num: 2
                                    cellWidth: width / row_num
                                    cellHeight: (60 + 15) * dpi_ratio
                                    model: ListModel {
                                        id: lm_goods_kind_2
                                    }
                                    flow: GridView.LeftToRight
                                    clip: true

                                    delegate: Component {
                                        CusRect {
                                            property bool is_cur_item: gv_goods_kind_2.currentItem == this
                                            width: gv_goods_kind_2.cellWidth
                                            height: gv_goods_kind_2.cellHeight
                                            color: ST.color_transparent

                                            CusRect {
                                                anchors {
                                                    fill: parent
                                                    margins: 15 * dpi_ratio
                                                }
                                                border {
                                                    width: is_cur_item ? 0 : 1
                                                    color: ST.color_grey_border
                                                }
                                                radius: ST.radius
                                                color: is_cur_item ? ST.color_green : ST.color_white_pure

                                                CusText {
                                                    anchors.centerIn: parent
                                                    text: goods_kind_name
                                                    color: is_cur_item ? ST.color_white_pure : ST.color_black
                                                }

                                                MouseArea {
                                                    anchors.fill: parent
                                                    onClicked: {
                                                        gv_goods_kind_2.currentIndex = index
                                                        clickedSupplier(goods_kind_unique, goods_kind_name)
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
