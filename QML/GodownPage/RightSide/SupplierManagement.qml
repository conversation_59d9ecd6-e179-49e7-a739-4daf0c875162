﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import "ProductManagement"

CusRect {
    color: ST.color_white_deeper

    function refreshSupplierInfoOnline() {
        supplierControl.reqSupplier4Qml(function callback(is_succ, data) {
            if (is_succ)
                refreshSupplierInfo()
        })
    }

    function refreshSupplierInfo() {
        lm_supplier.clear()
        var json_data = JSON.parse(supplierControl.getSupplierJson())
        if (!json_data)
            return
        for (var i = 0; i < json_data.length; ++i) {
            var cur_goods_kind = json_data[i]
            lm_supplier.append(cur_goods_kind)
        }
    }

    onVisibleChanged: {
        if (visible)
            refreshSupplierInfo()
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20 * dpi_ratio
        //标题栏
        Item {
            Layout.fillWidth: true
            Layout.preferredHeight: 50 * dpi_ratio

            RowLayout {
                anchors.fill: parent
                spacing: 2 * dpi_ratio

                CusRect {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredWidth: 710 * dpi_ratio
                    clip: true
                    color: ST.color_transparent
                    CusText {
                        anchors.verticalCenter: parent.verticalCenter
                        x: 50 * dpi_ratio
                        text: "名称"
                        color: ST.color_grey_font
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredWidth: 335 * dpi_ratio
                    clip: true
                    color: ST.color_transparent
                    CusText {
                        anchors.centerIn: parent
                        text: "联系人"
                        color: ST.color_grey_font
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredWidth: 835 * dpi_ratio
                    clip: true
                    color: ST.color_transparent

                    CusText {
                        anchors.centerIn: parent
                        text: "联系电话"
                        color: ST.color_grey_font
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredWidth: 500 * dpi_ratio
                    clip: true
                    color: ST.color_transparent

                    CusText {
                        anchors.centerIn: parent
                        text: "所属分类"
                        color: ST.color_grey_font
                    }
                }
            }
        }

        ListView {
            id: lv_supplier
            Layout.fillWidth: true
            Layout.fillHeight: true
            clip: true
            highlightFollowsCurrentItem: true
            highlightMoveDuration: 0
            focus: true
            currentIndex: -1
            model: ListModel {
                id: lm_supplier
            }
            delegate: CusRect {
                color: ST.color_transparent
                width: lv_supplier.width
                height: 80 * dpi_ratio
                id: lm_supplier_item
                property bool is_cur_item: lv_supplier.currentItem == this

                property var supplier_id_: supplier_id
                property var supplier_unique_: supplier_unique
                property var supplier_name_: supplier_name
                property var company_leagl_: company_leagl
                property var supplier_phone_: supplier_phone
                property var supplier_address_: supplier_address
                property var supplier_kind_name_: supplier_kind_name
                property var supplier_kind_id_: supplier_kind_id

                CusRect {
                    anchors.fill: parent
                    anchors.topMargin: 8 * dpi_ratio
                    anchors.bottomMargin: 8 * dpi_ratio
                    color: ST.color_white_pure
                    radius: ST.radius

                    RowLayout {
                        anchors.fill: parent
                        spacing: 2 * dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 710 * dpi_ratio
                            clip: true
                            color: ST.color_transparent
                            CusText {
                                anchors.verticalCenter: parent.verticalCenter
                                x: 50 * dpi_ratio
                                text: supplier_name //"名称"
                                color: ST.color_black
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 335 * dpi_ratio
                            clip: true
                            color: ST.color_transparent
                            CusText {
                                anchors.centerIn: parent
                                text: company_leagl //"联系人"
                                color: ST.color_black
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 835 * dpi_ratio
                            clip: true
                            color: ST.color_transparent

                            CusText {
                                anchors.centerIn: parent
                                text: supplier_phone //"联系电话"
                                color: ST.color_black
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 500 * dpi_ratio
                            clip: true
                            color: ST.color_transparent

                            CusText {
                                anchors.centerIn: parent
                                text: supplier_kind_name //"所属分类"
                                color: ST.color_black
                            }
                        }
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            supplier_detail.setCurSupplier(lm_supplier_item.supplier_id_, lm_supplier_item.supplier_unique_, lm_supplier_item.supplier_name_,
                                                           lm_supplier_item.company_leagl_, lm_supplier_item.supplier_phone_, lm_supplier_item.supplier_address_,
                                                           lm_supplier_item.supplier_kind_id_)
                        }
                    }
                }
            }

            highlight: CusRect {
                width: lv_supplier.width
                height: 50
                color: ST.color_orange
                opacity: .4
                z: 2
            }
        }
    }
}
