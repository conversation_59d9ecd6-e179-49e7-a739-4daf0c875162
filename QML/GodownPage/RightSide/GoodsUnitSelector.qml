﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import EnumTool 1.0
import GoodsDataModel 1.0

import "ProductManagement"

CusRect {
    id: rect_goods_unit_chooser
    color: ST.color_white_deeper

    Component.onCompleted: {
        refreshGoodsUnit()
        focusGoodsUnit(goods_detail.goods_unit)
    }
    onVisibleChanged: {
        if (visible) {
            refreshGoodsUnit()
        }
    }

    signal close
    signal clickedGoodsUnit(var goods_unit)

    function showCreateGoodsUnitPopup() {
        window_root.loader_4_create_goods_unit.sourceComponent = compo_create_goods_unit
        window_root.loader_4_create_goods_unit.item.open()
    }

    function showDeleteGoodsUnitPopup(goods_unit_id_in, goods_unit_in) {
        window_root.loader_4_delete_goods_unit.sourceComponent = compo_delete_goods_unit
        window_root.loader_4_delete_goods_unit.item.open(goods_unit_id_in, goods_unit_in)
    }

    // 刷新商品单位
    function refreshGoodsUnit() {
        lm_goods_unit.clear()
        var str_tmp = goodsManager.getAllGoodsUnitJson()
        var json_all_goods_unit = JSON.parse(str_tmp)

        if (json_all_goods_unit == null)
            return

        for (var i = 0; i < json_all_goods_unit.length; ++i) {
            var cur_item = json_all_goods_unit[i]
            lm_goods_unit.append(cur_item)
        }

        lm_goods_unit.append({
                                 "goods_unit_id": 0,
                                 "goods_unit": "+"
                             })
    }

    // 选中商品单位
    function focusGoodsUnit(unit_name) {
        gv_goods_unit.currentIndex = -1
        for (var i = 0; i < lm_goods_unit.count; ++i) {
            var cur_item = lm_goods_unit.get(i)

            if (cur_item.goods_unit == unit_name) {
                gv_goods_unit.currentIndex = i
                return
            }
        }
    }

    MouseArea {
        anchors.fill: parent
    }

    RowLayout {
        anchors.fill: parent

        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: 94 * dpi_ratio
            color: ST.color_white_pure

            CusRect {
                width: parent.width
                height: width
                anchors.verticalCenter: parent.verticalCenter
                anchors.verticalCenterOffset: -100 * dpi_ratio
                color: ST.color_transparent
                Image {
                    anchors {
                        horizontalCenter: parent.horizontalCenter
                        horizontalCenterOffset: 5 * dpi_ratio

                        verticalCenter: parent.verticalCenter
                        verticalCenterOffset: -10 * dpi_ratio
                    }
                    fillMode: Image.PreserveAspectFit
                    width: 40 * dpi_ratio
                    height: width
                    source: "/Images/collapse.png"
                }
                CusText {
                    text: "收起"
                    color: ST.color_green
                    anchors {
                        horizontalCenter: parent.horizontalCenter
                        verticalCenter: parent.verticalCenter
                        verticalCenterOffset: 40 * dpi_ratio
                    }
                }
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    goods_detail.edit_goods.rect_goods_detail.is_open_goods_unit_selector = false
                    goods_detail.create_goods.rect_goods_detail.is_open_goods_unit_selector = false
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_transparent

            CusRect {
                anchors.fill: parent
                anchors.margins: 40 * dpi_ratio
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 600 * dpi_ratio
                        color: ST.color_transparent
                        ColumnLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_white_pure
                                radius: ST.radius
                                GridView {
                                    id: gv_goods_unit
                                    anchors.fill: parent
                                    anchors {
                                        leftMargin: 20 * dpi_ratio
                                        rightMargin: 20 * dpi_ratio
                                        topMargin: 10 * dpi_ratio
                                        bottomMargin: 10 * dpi_ratio
                                    }
                                    property int row_num: 5
                                    property int column_num: 2
                                    cellWidth: width / row_num
                                    cellHeight: (60 + 15) * dpi_ratio
                                    model: ListModel {
                                        id: lm_goods_unit
                                    }

                                    flow: GridView.LeftToRight
                                    clip: true

                                    delegate: CusRect {
                                        id: rect_goods_kind
                                        property bool is_cur_item: gv_goods_unit.currentItem == this
                                        width: gv_goods_unit.cellWidth
                                        height: gv_goods_unit.cellHeight
                                        color: ST.color_transparent

                                        CusRect {
                                            anchors {
                                                fill: parent
                                                margins: 15 * dpi_ratio
                                            }
                                            border {
                                                width: rect_goods_kind.is_cur_item ? 0 : 1 * dpi_ratio
                                                color: ST.color_grey_border
                                            }
                                            color: ST.color_transparent

                                            RowLayout {
                                                anchors.fill: parent
                                                spacing: 0
                                                anchors.margins: 1 * dpi_ratio

                                                CusRect {
                                                    Layout.fillHeight: true
                                                    Layout.fillWidth: true
                                                    color: rect_goods_kind.is_cur_item ? ST.color_green : ST.color_white_pure
                                                    CusText {
                                                        anchors.centerIn: parent
                                                        text: goods_unit
                                                        color: rect_goods_kind.is_cur_item ? ST.color_white_pure : ST.color_black
                                                    }

                                                    MouseArea {
                                                        anchors.fill: parent
                                                        onClicked: {
                                                            if (index == lm_goods_unit.count - 1) {
                                                                showCreateGoodsUnitPopup()
                                                            } else {
                                                                gv_goods_unit.currentIndex = index
                                                                clickedGoodsUnit(goods_unit)
                                                            }
                                                        }
                                                    }
                                                }

                                                CusRect {
                                                    Layout.fillHeight: true
                                                    Layout.preferredWidth: height
                                                    color: ST.color_red

                                                    visible: index != lm_goods_unit.count - 1

                                                    Image {
                                                        source: "/Images/close_image.png"
                                                        anchors.centerIn: parent
                                                        width: 26 * dpi_ratio
                                                        height: width
                                                    }

                                                    MouseArea {
                                                        anchors.fill: parent
                                                        onClicked: {
                                                            showDeleteGoodsUnitPopup(goods_unit_id, goods_unit)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    Component {
        id: compo_create_goods_unit
        PopupInputConfirm {
            id: points_chage_confirm
            title_name: "新建商品单位"
            message_info: "请输入商品单位名称"
            onConfirm: {
                goodsControl.reqAddGoodsUnit(function (is_succ, data) {
                    var json_doc = JSON.parse(data)
                    if (is_succ) {
                        toast.openInfo("新建分类成功")
                        refreshGoodsUnit()
                    } else {
                        toast.openWarn("新建分类失败")
                    }
                }, change_str)
            }
        }
    }

    Component {
        id: compo_delete_goods_unit
        CusPopupConfirm {
            property string goods_unit: ""
            property string goods_unit_id: ""

            function open(goods_unit_id_in, goods_unit_in) {
                visible = true
                goods_unit_id = goods_unit_id_in
                goods_unit = goods_unit_in
                title_name = "删除单位"
                message_info = "确认要删除单位 " + goods_unit + " 吗?"
            }
            onConfirm: {
                goodsControl.reqDelGoodsUnit(function (is_succ, data) {
                    if (is_succ) {
                        toast.openInfo("删除单位 " + goods_unit + " 成功")
                        refreshGoodsUnit()
                    } else {
                        toast.openError("删除单位 " + goods_unit + " 失败")
                    }
                }, goods_unit_id)
            }
        }
    }
}
