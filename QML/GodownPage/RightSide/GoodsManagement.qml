﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import EnumTool 1.0
import GoodsDataModel 1.0

import "ProductManagement"

CusRect {
    color: ST.color_white_deeper

    property alias expandable_list_goods_class: expandable_list_goods_class
    property alias model_goods_data: model_goods_data

    property alias rect_goods_kind_chooser: rect_goods_kind_chooser
    property alias rect_goods_unit_chooser: rect_goods_unit_chooser
    property alias supplier_selector: supplier_selector

    property string cur_goods_class_name: ""

    onVisibleChanged: {
        if (visible)
            tf_godown_search.forceActiveFocus()
    }

    // 刷新页面分类(右侧分类选择)
    function refreshGoodsKind() {
        expandable_list_goods_class.clearModelData()

        var all_goods_kind = goodsKindManager.getAllGoodsKind4Qml()

        var json_doc_all_goods_kind = JSON.parse(all_goods_kind)

        for (var i = 0; i < json_doc_all_goods_kind.length; ++i) {
            var cur_goods_kind = json_doc_all_goods_kind[i]

            var parent_unique = cur_goods_kind.goods_kind_parunique
            var is_root_kind = parent_unique == 0

            if (is_root_kind)
                continue

            var root_kind = goodsKindManager.getGoodsKindByKindUnique4Qml(Number(parent_unique))
            var json_doc_root_kind = JSON.parse(root_kind)

            expandable_list_goods_class.addModelData(json_doc_root_kind.goods_kind_name, cur_goods_kind.goods_kind_name, cur_goods_kind.goods_kind_unique)
        }

        expandable_list_goods_class.clickAllGoods()
    }

    RowLayout {
        anchors.fill: parent

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 70 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 15 * dpi_ratio
                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 300 * dpi_ratio
                            color: ST.color_transparent
                            CusText {
                                text: "当前分类商品数: " + model_goods_data.goodsCount
                                anchors.verticalCenter: parent.verticalCenter
                                x: 30 * dpi_ratio
                            }
                        }

                        CusSpacer {
                            Layout.fillWidth: true
                        }

                        RectSort {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 100 * dpi_ratio

                            label_name: "销量"

                            onStateChanged: {
                                if (sort_state == 0) {

                                }
                                if (sort_state == 1) {

                                }
                                if (sort_state == 2) {

                                }
                            }
                        }
                        RectSort {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 100 * dpi_ratio

                            label_name: "库存"

                            onStateChanged: {
                                if (sort_state == 0) {

                                }
                                if (sort_state == 1) {

                                }
                                if (sort_state == 2) {

                                }
                            }
                        }
                        RectSort {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 100 * dpi_ratio

                            label_name: "价格"

                            onStateChanged: {
                                if (sort_state == 0) {

                                }
                                if (sort_state == 1) {

                                }
                                if (sort_state == 2) {

                                }
                            }
                        }
                        CusSpacer {
                            Layout.preferredWidth: 40 * dpi_ratio
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    GridView {
                        id: grid_goods_list
                        anchors.fill: parent
                        anchors.margins: 10 * dpi_ratio

                        model: model_goods_data

                        snapMode: GridView.SnapToRow
                        flow: GridView.LeftToRight

                        clip: true
                        cellHeight: 180 * dpi_ratio
                        property int row_num: 4
                        cellWidth: width / row_num

                        delegate: GoodsRect {
                            width: grid_goods_list.cellWidth
                            height: grid_goods_list.cellHeight
                            onClicked: {
                                goods_detail.setCurGoodsByBarcode(GOODS_BARCODE_ROLE)
                            }
                            is_selected: godown_page_root.goods_detail.edit_goods.rect_goods_detail.barcode == GOODS_BARCODE_ROLE
                                         || godown_page_root.goods_detail.edit_goods.rect_goods_detail2.barcode == GOODS_BARCODE_ROLE
                                         || godown_page_root.goods_detail.edit_goods.rect_goods_detail3.barcode == GOODS_BARCODE_ROLE
                        }
                    }
                }
            }
        }

        GoodsDataModel {
            id: model_goods_data
            goods_data: goodsManager
        }

        // 页面分类
        ExpandableListGoodsClass {
            id: expandable_list_goods_class
            Layout.preferredWidth: 180 * dpi_ratio
            Layout.fillHeight: true

            onClickClass: {
                model_goods_data.goods_kind_unique = class_id
            }

            function clickAllGoodsWizard() {
                model_goods_data.goods_kind_unique = 0
            }

            onClickAllGoods: {
                clickAllGoodsWizard()
            }

            Component.onCompleted: {
                refreshGoodsKind()
            }

            onVisibleChanged: {
                if (visible) {
                    refreshGoodsKind()
                }
            }
        }
    }

    // 商品分类选择
    GoodsKindSelector {
        id: rect_goods_kind_chooser
        anchors.fill: parent

        visible: goods_detail.edit_goods.rect_goods_detail_basic_info.is_open_goods_kind_selector
                 || goods_detail.create_goods.rect_goods_detail_basic_info.is_open_goods_kind_selector

        onClickedSupplier: {
            goods_detail.setGoodsKindUnique(goods_kind_unique)
            close()
        }

        onClose: {
            goods_detail.edit_goods.rect_goods_detail_basic_info.is_open_goods_kind_selector = false
            goods_detail.create_goods.rect_goods_detail_basic_info.is_open_goods_kind_selector = false
        }
    }

    // 商品单位选择
    GoodsUnitSelector {
        id: rect_goods_unit_chooser
        anchors.fill: parent

        visible: goods_detail.edit_goods.rect_goods_detail.is_open_goods_unit_selector || goods_detail.create_goods.rect_goods_detail.is_open_goods_unit_selector

        onClickedGoodsUnit: {
            goods_detail.setGoodsUnit(goods_unit)
            close()
        }

        onClose: {
            goods_detail.edit_goods.rect_goods_detail.is_open_goods_unit_selector = false
            goods_detail.create_goods.rect_goods_detail.is_open_goods_unit_selector = false
        }
    }

    // 供应商选择
    SupplierSelector {
        id: supplier_selector
        anchors.fill: parent

        visible: goods_detail.edit_goods.rect_goods_detail_basic_info.is_open_supplier_selector || goods_detail.create_goods.rect_goods_detail_basic_info.is_open_supplier_selector

        onClickedSupplier: {
            goods_detail.setSupplierInfo(supplier_unique, supplier_name)
            close()
        }

        onClose: {
            goods_detail.edit_goods.rect_goods_detail_basic_info.is_open_supplier_selector = false
            goods_detail.create_goods.rect_goods_detail_basic_info.is_open_supplier_selector = false
        }
    }
}
