﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import "ProductManagement"
CusRect {
    color: ST.color_white_deeper

    function getGoodsKind() {
        refreshGoodsKind()
    }

    function refreshGoodsKind() {
        lm_goods_class.clear()

        var data_json = JSON.parse(goodsKindManager.getRootGoodsKind4Qml())

        for (var i = 0; i < data_json.length; ++i) {
            var cur_goods_kind = data_json[i]
            lm_goods_class.append(cur_goods_kind)
        }
    }

    onVisibleChanged: {
        if (visible)
            getGoodsKind()
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20 * dpi_ratio

        ListView {
            id: lv_goods_class
            Layout.fillWidth: true
            Layout.fillHeight: true
            clip: true
            highlightFollowsCurrentItem: true
            highlightMoveDuration: 0
            focus: true
            currentIndex: -1
            model: ListModel {
                id: lm_goods_class
            }

            delegate: CusRect {
                color: ST.color_transparent
                width: lv_goods_class.width
                height: 80 * dpi_ratio

                property bool is_cur_item: lv_goods_class.currentItem == this

                CusRect {
                    anchors.fill: parent
                    anchors.topMargin: 8 * dpi_ratio
                    anchors.bottomMargin: 8 * dpi_ratio
                    color: ST.color_white_pure
                    radius: ST.radius

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            lv_goods_class.currentIndex = index
                        }
                    }

                    RowLayout {
                        anchors.fill: parent
                        spacing: 2 * dpi_ratio

                        //分类名
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 250 * dpi_ratio
                            clip: true
                            color: ST.color_transparent

                            CusText {
                                x: 15 * dpi_ratio
                                text: goods_kind_name
                                color: ST.color_black
                                elide: Text.ElideRight
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        //拖动块
                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: height
                            clip: true
                            color: ST.color_transparent

                            Image {
                                anchors.fill: parent
                                anchors.margins: 5 * dpi_ratio
                                source: "/Images/drag_area.png"
                            }
                        }
                    }
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        category_detail.setCurGoodsKind(goods_kind_unique)
                    }
                }
            }
        }
    }
}
