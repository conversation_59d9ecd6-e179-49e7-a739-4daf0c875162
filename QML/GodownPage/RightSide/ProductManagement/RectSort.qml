﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import "../../.."

CusRect {
    id: rect_sort

    property string label_name: "排序"
    property int sort_state: 0 // 0:不排序 1:顺序 2:逆序
    color: ST.color_transparent

    RowLayout {
        anchors.fill: parent
        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.leftMargin: 12 * dpi_ratio
            color: ST.color_transparent
            CusText {
                text: label_name
                anchors.verticalCenter: parent.verticalCenter
            }
        }
        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: 30 * dpi_ratio
            color: ST.color_transparent

            property string img_spin_up: "/Images/spin_up.png"
            property string img_spin_down: "/Images/spin_down.png"
            property string img_spin_null: "/Images/spin_null.png"

            Image {
                anchors.fill: parent
                source: if (rect_sort.sort_state == 0) {
                            return parent.img_spin_null
                        } else if (rect_sort.sort_state == 1) {
                            return parent.img_spin_up
                        } else if (rect_sort.sort_state == 2) {
                            return parent.img_spin_down
                        }

                fillMode: Image.PreserveAspectFit
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        onClicked: {
            if (rect_sort.sort_state == 0) {
                rect_sort.sort_state = 1
            } else if (rect_sort.sort_state == 1) {
                rect_sort.sort_state = 2
            } else if (rect_sort.sort_state == 2) {
                rect_sort.sort_state = 0
            }
        }
    }
}
