﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import ".."
import "../../.."

Rectangle {
    id: root
    color: ST.color_black_p2
    property variant cur_btn: all_specs.item

    signal clickClass(var class_id)
    signal clickAllGoods

    function focusAllGoodsBtn() {
        cur_btn = all_specs.item
    }

    layer.enabled: true
    layer.effect: OpacityMask {
        maskSource: Rectangle {
            width: root.width
            height: root.height
            radius: ST.radius
        }
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 0

        Loader {
            id: all_specs
            Layout.fillWidth: true
            sourceComponent: sub_item
            onLoaded: {
                all_specs.item.text = "全部商品"
            }
        }

        ListView {
            id: listView
            Layout.fillWidth: true
            Layout.fillHeight: true
            anchors.topMargin: all_specs.height
            spacing: 1
            Material.background: "white"
            boundsBehavior: Flickable.StopAtBounds
            clip: true
            model: ListModel {
                id: listModel
            }
            delegate: list_delegate
        }

        Component {
            id: list_delegate
            Column {
                id: obj_column

                Component.onCompleted: {
                    for (var i = 1; i < obj_column.children.length - 1; ++i) {
                        obj_column.children[i].visible = false
                    }
                }

                Rectangle {
                    id: header_rect
                    width: listView.width
                    height: 90 * dpi_ratio
                    color: ST.color_black_p1

                    property bool is_open: false

                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 20 * dpi_ratio
                        Image {
                            Layout.preferredWidth: 28 * dpi_ratio
                            Layout.preferredHeight: 28 * dpi_ratio
                            source: header_rect.is_open ? "/Images/minus.png" : "/Images/plus.png"
                        }
                        CusText {
                            text: meetingName
                            color: ST.color_grey
                        }
                    }

                    onIs_openChanged: {
                        for (var i = 1; i < obj_column.children.length - 1; ++i) {
                            obj_column.children[i].visible = header_rect.is_open
                        }
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            header_rect.is_open = !header_rect.is_open
                        }
                    }
                }

                Repeater {
                    model: sub_node
                    delegate: sub_item
                }
            }
        }
        Component {
            id: sub_item
            CusButton {
                width: listView.width
                height: 90 * dpi_ratio
                color: ST.color_transparent
                border.width: 1
                text: name
                radius: 0
                
                Image {
                    anchors.fill: parent
                    source: "/Images/shade.png"
                    visible: cur_btn === parent
                }
                onClicked: {
                    cur_btn = this
                    if (text == "全部商品") {
                        clickAllGoods()
                        return
                    }
                    clickClass(uid)
                }
            }
        }
    }

    function clearModelData() {
        listModel.clear()
    }

    function addModelData(root_name, sub_name, sub_id) {
        var index = findIndex(root_name)
        if (index === -1) {
            listModel.append({
                                 "meetingName": root_name,
                                 "sub_node": [{
                                         "name": sub_name,
                                         "uid": sub_id
                                     }]
                             })
        } else {
            listModel.get(index).sub_node.append({
                                                     "name": sub_name,
                                                     "uid": sub_id
                                                 })
        }
    }

    function findIndex(name) {
        for (var i = 0; i < listModel.count; ++i) {
            if (listModel.get(i).meetingName === name) {
                return i
            }
        }
        return -1
    }
}
