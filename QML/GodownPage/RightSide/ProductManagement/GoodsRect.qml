﻿import QtQuick 2.15;
import QtGraphicalEffects 1.15
import QtQuick.Layouts 1.15
import EnumTool 1.0
import "../../.."

Item {
    id: goods_rect_root

    readonly property bool goods_is_weight: GOODS_CHENG_TYPE_ROLE == 1
    readonly property bool is_shelf_status: PC_SHELF_STATE_ROLE == 1
    property bool is_selected: false
    property string sales: "0"

    signal clicked

    CusRect {
        id: goods_rect_rect
        anchors.margins: 7 * dpi_ratio
        anchors.fill: parent
        radius: ST.radius
        color: ST.color_white_pure

        CusRect {
            anchors.margins: 12 * dpi_ratio
            anchors.fill: parent
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 10 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 40 * dpi_ratio
                    Layout.leftMargin: goods_is_weight ? 20 * dpi_ratio : 0
                    color: ST.color_transparent

                    CusText {
                        text: GOODS_NAME_ROLE
                        anchors.centerIn: parent
                        elide: Text.ElideRight
                        width: parent.width
                        font.pixelSize: 24 * dpi_ratio * configTool.fontRatio
                        Layout.alignment: Qt.AlignVCenter
                        font.bold: true
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 20 * dpi_ratio
                    color: ST.color_transparent

                    CusText {
                        text: GOODS_BARCODE_ROLE
                        anchors.centerIn: parent
                        font.pixelSize: 20 * dpi_ratio * configTool.fontRatio
                        Layout.alignment: Qt.AlignVCenter
                        color: ST.color_grey_font
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 40 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 155 * dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: GOODS_SALE_PRICE_ROLE
                                anchors.centerIn: parent
                                elide: Text.ElideRight
                                width: parent.width
                                color: ST.color_green
                                font.bold: true
                                font.pixelSize: 26 * dpi_ratio * configTool.fontRatio
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            id: rect_stock
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 120 * dpi_ratio
                            Layout.topMargin: 8 * dpi_ratio
                            color: ST.color_green_light

                            visible: configTool.isShowStock

                            layer.enabled: true
                            layer.effect: OpacityMask {
                                maskSource: Rectangle {
                                    width: rect_stock.width
                                    height: rect_stock.height
                                    radius: ST.radius
                                }
                            }

                            RowLayout {
                                anchors.fill: parent

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    CusText {
                                        text: GOODS_COUNT_ROLE
                                        anchors.right: parent.right
                                        elide: Text.ElideRight
                                        color: ST.color_black
                                        anchors.verticalCenter: parent.verticalCenter
                                        font.pixelSize: 18 * dpi_ratio * configTool.fontRatio
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: height
                                    color: ST.color_green
                                    CusText {
                                        text: "库"
                                        anchors.centerIn: parent
                                        color: ST.color_white_pure
                                        font.pixelSize: 20 * dpi_ratio * configTool.fontRatio
                                    }
                                }
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 30 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            visible: false
                            CusText {
                                text: "月销量: " + goods_rect_root.sales
                                font.pixelSize: 18 * dpi_ratio
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: height
                            color: ST.color_transparent
                            Image {
                                anchors.fill: parent
                                source: "/Images/cash_register_icon.png"
                                anchors.margins: 2 * dpi_ratio

                                Colorize {
                                    anchors.fill: parent
                                    source: parent
                                    hue: 0
                                    saturation: 0
                                    lightness: 0
                                    visible: !is_shelf_status
                                }
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: height
                            color: ST.color_transparent
                            visible: false
                            Image {
                                anchors.fill: parent
                                source: "/Images/yikezhong_icon.png"
                                anchors.margins: 2 * dpi_ratio
                            }
                        }
                    }
                }
            }
        }

        Image {
            width: 38 * dpi_ratio
            height: 38 * dpi_ratio
            anchors.left: parent.left
            anchors.top: parent.top
            source: "/Images/weight_badge.png"
            visible: goods_is_weight
        }

        CusRect {
            anchors.fill: parent
            color: ST.color_transparent
            visible: is_selected
            radius: ST.radius
            border {
                width: 2 * dpi_ratio
                color: ST.color_green
            }
            opacity: .7
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
                goods_rect_root.clicked()
            }
        }
    }
}
