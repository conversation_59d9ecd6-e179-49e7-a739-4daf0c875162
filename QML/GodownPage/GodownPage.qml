﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import "LeftSide"
import "RightSide"
import EnumTool 1.0

CusRect {
    id: godown_page_root
    width: 800 * dpi_ratio
    height: 500 * dpi_ratio
    color: ST.color_grey
    property int width_btn: 340 * dpi_ratio
    property int height_btn: 70 * dpi_ratio

    property alias goods_detail: goods_detail
    property alias category_detail: category_detail
    property alias supplier_detail: supplier_detail
    property alias supplier_category_detail: supplier_category_detail

    property alias goods_management: goods_management
    property alias category_management: category_management
    property alias supplier_mamagement: supplier_mamagement
    property alias supplier_category_mamagement: supplier_category_mamagement

    Image {
        anchors.fill: parent
        source: "/Images/mainPageBackground.jpg"
    }

    RowLayout {
        anchors.fill: parent
        spacing: ST.margin
        anchors.margins: 10 * dpi_ratio

        CusRect {
            id: left_side
            Layout.fillHeight: true
            Layout.preferredWidth: 650 * dpi_ratio
            Layout.alignment: Qt.AlignLeft
            color: ST.color_white_pure

            ContainGoodsDetail {
                id: goods_detail
                anchors.fill: parent
                visible: right_side.lv_tabbar_currentIndex == 0
            }
            ContainCategoryDetail {
                id: category_detail
                anchors.fill: parent
                visible: right_side.lv_tabbar_currentIndex == 1
            }
            ContainSupplierDetail {
                id: supplier_detail
                anchors.fill: parent
                visible: right_side.lv_tabbar_currentIndex == 2
            }
            ContainSupplierCategoryDetail {
                id: supplier_category_detail
                anchors.fill: parent
                visible: right_side.lv_tabbar_currentIndex == 3
            }
        }

        CusRect {
            id: right_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
            // 0:商品分类  1:分类管理  2:供应商管理 3:供应商分类
            property alias lv_tabbar_currentIndex: lv_tabbar.currentIndex

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 80 * dpi_ratio
                    color: ST.color_white_pure
                    Layout.alignment: Qt.AlignTop

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            Layout.preferredWidth: 700 * dpi_ratio * configTool.fontRatio2
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            // TabBar
                            CusRect {
                                anchors.fill: parent
                                color: ST.color_transparent

                                ListView {
                                    id: lv_tabbar
                                    anchors.fill: parent
                                    model: ListModel {
                                        id: model_tabbar
                                    }
                                    cacheBuffer: 200
                                    orientation: ListView.Horizontal
                                    highlightFollowsCurrentItem: true
                                    highlightMoveDuration: 0
                                    interactive: false

                                    delegate: Component {
                                        CusRect {
                                            height: lv_tabbar.height
                                            width: 160 * dpi_ratio * configTool.fontRatio2
                                            property bool is_current_item: lv_tabbar.currentItem == this
                                            color: (!is_current_item) ? ST.color_white_pure : ST.color_white_deeper

                                            CusText {
                                                text: name
                                                anchors.centerIn: parent
                                                color: is_current_item ? ST.color_green : ST.color_black
                                            }
                                            MouseArea {
                                                anchors.fill: parent
                                                onClicked: {
                                                    lv_tabbar.currentIndex = index
                                                }
                                            }
                                        }
                                    }

                                    Component.onCompleted: {
                                        model_tabbar.append({
                                                                "name": "商品管理"
                                                            })
                                        model_tabbar.append({
                                                                "name": "分类管理"
                                                            })
                                        model_tabbar.append({
                                                                "name": "供应商管理"
                                                            })
                                        model_tabbar.append({
                                                                "name": "供应商分类"
                                                            })
                                    }
                                }
                            }
                        }

                        CusSpacer {
                            Layout.fillWidth: true
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: height
                            color: ST.color_transparent

                            Image {
                                anchors.centerIn: parent
                                width: 42 * dpi_ratio
                                height: width
                                source: "/Images/switch.png"
                            }

                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    window_root.compo_index = EnumTool.PAGE_GODOWN_OLD
                                }
                            }
                        }

                        CusRect {
                            Layout.preferredHeight: 50 * dpi_ratio
                            Layout.preferredWidth: 330 * dpi_ratio
                            Layout.alignment: Qt.AlignVCenter
                            Layout.rightMargin: 20 * dpi_ratio
                            radius: ST.radius
                            color: ST.color_grey
                            visible: goods_management.visible
                            RowLayout {
                                anchors.fill: parent
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: height
                                    color: ST.color_transparent
                                    Image {
                                        anchors.fill: parent
                                        source: "/Images/search_image.png"
                                        anchors.margins: 12 * dpi_ratio
                                    }
                                }
                                CusTextField {
                                    id: tf_godown_search
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    placeholderText: "商品条码/商品名称"
                                    border.width: 0
                                    keyboard_status: 1

                                    digital_keyboard_x: 1300 * dpi_ratio
                                    digital_keyboard_y: 430 * dpi_ratio

                                    normal_keyboard_x: 750 * dpi_ratio
                                    normal_keyboard_y: 560 * dpi_ratio

                                    onTextChanged: {
                                        if (text == "") {
                                            goods_management.model_goods_data.search_str = ""
                                            timer.stop()
                                        } else {
                                            timer.restart()
                                        }
                                    }

                                    onAccepted: {
                                        timer.stop()

                                        goods_management.model_goods_data.search_str = text

                                        if (goods_management.model_goods_data.rowCount() == 1) {
                                            var json_tmp = JSON.parse(goods_management.model_goods_data.getItemByIndex(0))
                                            goods_detail.setCurGoodsByBarcode(json_tmp.goods_barcode)
                                        }
                                        tf_godown_search.selectAll()
                                    }

                                    Timer {
                                        id: timer
                                        interval: 800
                                        repeat: false
                                        onTriggered: {
                                            goods_management.model_goods_data.search_str = tf_godown_search.text
                                        }
                                    }

                                    CusRect {
                                        color: ST.color_transparent
                                        width: tf_godown_search.height
                                        height: width
                                        anchors.right: parent.right

                                        Image {
                                            width: 38 * dpi_ratio
                                            height: width
                                            anchors.centerIn: parent
                                            fillMode: Image.PreserveAspectFit
                                            visible: tf_godown_search.text != ""
                                            source: "/Images/yuebuzu_icon.png"
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (tf_godown_search.text != "") {
                                                    tf_godown_search.text = ""
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                //商品管理
                GoodsManagement {
                    id: goods_management
                    visible: right_side.lv_tabbar_currentIndex == 0
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                }

                //分类管理
                CategoryManagement {
                    id: category_management
                    visible: right_side.lv_tabbar_currentIndex == 1
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                }

                //供应商管理
                SupplierManagement {
                    id: supplier_mamagement
                    visible: right_side.lv_tabbar_currentIndex == 2
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                }

                //供应商分类
                SupplierCategory {
                    id: supplier_category_mamagement
                    visible: right_side.lv_tabbar_currentIndex == 3
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                }
            }
        }
    }
}
