﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import EnumTool 1.0
import "ContainGoodsDetail"

CusRect {
    id: rect_contain_goods_detail
    color: ST.color_white_pure

    property string cur_goods_barcode: ""
    property bool is_create_goods: false
    property alias edit_goods: edit_goods
    property alias create_goods: create_goods

    function clearAllGoodsInfo() {
        create_goods.rect_goods_detail_basic_info.resetInfo()
        create_goods.rect_goods_detail.resetInfo()
        create_goods.rect_goods_detail2.resetInfo()
        create_goods.rect_goods_detail3.resetInfo()

        edit_goods.rect_goods_detail_basic_info.resetInfo()
        edit_goods.rect_goods_detail.resetInfo()
        edit_goods.rect_goods_detail2.resetInfo()
        edit_goods.rect_goods_detail3.resetInfo()
    }

    function refreshCurGoodsInfo() {
        setCurGoodsByBarcode(cur_goods_barcode)
    }

    function setCurGoodsByBarcode(goods_barcode) {
        cancelGoods()
        clearAllGoodsInfo()

        var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))

        //非基础规格,找出基础规格
        if (cur_goods.goods_contain > 1) {
            var cur_goods_basic = JSON.parse(goodsManager.getGoodsByBarcode4Qml(cur_goods.foreign_key))

            if (!cur_goods_basic) {
                toast.openError("商品信息不完整!")
                cancelGoods()
                return
            }
            cur_goods_barcode = cur_goods_basic.goods_barcode
            setCurGoodsAll(cur_goods_barcode)
        } else {
            cur_goods_barcode = goods_barcode
            setCurGoodsAll(cur_goods_barcode)
        }
    }

    function setCurGoodsAll(cur_goods_barcode) {

        edit_goods.rect_goods_detail.setGoodsByBarcode(cur_goods_barcode)

        var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(cur_goods_barcode))

        // 关联商品列表
        var json_doc = JSON.parse(goodsManager.getForeignGoodsJsonListByBarcode(cur_goods.goods_barcode))
        if (json_doc) {

            if (json_doc.length == 1) {
                edit_goods.rect_goods_detail2.setGoodsByBarcode(json_doc[0].goods_barcode)
            } else if (json_doc.length > 1) {
                if (json_doc[0].goods_contain < json_doc[1].goods_contain) {
                    edit_goods.rect_goods_detail2.setGoodsByBarcode(json_doc[0].goods_barcode)
                    edit_goods.rect_goods_detail3.setGoodsByBarcode(json_doc[1].goods_barcode)
                } else {
                    edit_goods.rect_goods_detail2.setGoodsByBarcode(json_doc[1].goods_barcode)
                    edit_goods.rect_goods_detail3.setGoodsByBarcode(json_doc[0].goods_barcode)
                }
            }
            edit_goods.setContainGoodsNum(json_doc.length + 1)
        } else {
            edit_goods.setContainGoodsNum(1)
        }

        edit_goods.rect_tabbar.cur_tab_enum = EnumTool.GOODS_INFO_TAB_SPEC_BASE
    }

    // 新建商品
    function createGoods() {
        clearAllGoodsInfo()
        is_create_goods = true
    }

    function setSupplierInfo(supplier_uniqe, supplier_name) {
        if (is_create_goods) {
            create_goods.rect_goods_detail_basic_info.supplier_unique = supplier_uniqe
            create_goods.rect_goods_detail_basic_info.supplier_name = supplier_name
        } else {
            edit_goods.rect_goods_detail_basic_info.supplier_unique = supplier_uniqe
            edit_goods.rect_goods_detail_basic_info.supplier_name = supplier_name
        }
    }

    function setGoodsKindUnique(kind_uniqe) {
        if (is_create_goods) {
            create_goods.rect_goods_detail_basic_info.goods_kind_unique = kind_uniqe
        } else {
            edit_goods.rect_goods_detail_basic_info.goods_kind_unique = kind_uniqe
        }
    }

    function setGoodsUnit(goods_unit) {
        if (is_create_goods) {
            create_goods.rect_goods_detail.goods_unit = goods_unit
        } else {
            edit_goods.rect_goods_detail.goods_unit = goods_unit
        }
    }

    function uploadGoods_v2() {

        if (!create_goods.rect_goods_detail_basic_info.checkData())
            return

        if (!create_goods.rect_goods_detail.checkData())
            return

        var result_json = []

        result_json.push({
                             "goods_contain": "1",
                             "goods_kind_unique": create_goods.rect_goods_detail_basic_info.goods_kind_unique,
                             "goods_barcode": create_goods.rect_goods_detail.barcode,
                             "goods_name": create_goods.rect_goods_detail.goods_name,
                             "goods_standard": create_goods.rect_goods_detail.product_specification,
                             "goods_brand": create_goods.rect_goods_detail_basic_info.goods_brand,
                             "pc_shelf_state": create_goods.rect_goods_detail_basic_info.pc_shelf_state,
                             "web_shelf_state": create_goods.rect_goods_detail_basic_info.web_shelf_state,
                             "goods_sale_price": create_goods.rect_goods_detail.unit_price,
                             "goods_web_sale_price": create_goods.rect_goods_detail.web_price,
                             "goods_cus_price": create_goods.rect_goods_detail.member_price,
                             "goods_in_price": create_goods.rect_goods_detail.purchase_price,
                             "goodsChengType": create_goods.rect_goods_detail.price_type,
                             "goods_unit": create_goods.rect_goods_detail.goods_unit,
                             "default_supplier_unique": create_goods.rect_goods_detail_basic_info.supplier_unique
                         })

        if (create_goods.rect_goods_detail2.isHasData()) {

            if (!create_goods.rect_goods_detail2.checkData())
                return

            result_json.push({
                                 "goods_contain": create_goods.rect_goods_detail2.goods_contain,
                                 "goods_kind_unique": create_goods.rect_goods_detail_basic_info.goods_kind_unique,
                                 "goods_barcode": create_goods.rect_goods_detail2.barcode,
                                 "goods_name": create_goods.rect_goods_detail2.goods_name,
                                 "goods_standard": create_goods.rect_goods_detail2.product_specification,
                                 "goods_brand": create_goods.rect_goods_detail_basic_info.goods_brand,
                                 "pc_shelf_state": create_goods.rect_goods_detail_basic_info.pc_shelf_state,
                                 "web_shelf_state": create_goods.rect_goods_detail_basic_info.web_shelf_state,
                                 "goods_sale_price": create_goods.rect_goods_detail2.unit_price,
                                 "goods_web_sale_price": create_goods.rect_goods_detail.web_price,
                                 "goods_cus_price": create_goods.rect_goods_detail2.member_price,
                                 "goods_in_price": create_goods.rect_goods_detail2.purchase_price,
                                 "goodsChengType": create_goods.rect_goods_detail2.price_type,
                                 "goods_unit": create_goods.rect_goods_detail2.goods_unit,
                                 "default_supplier_unique": create_goods.rect_goods_detail_basic_info.supplier_unique
                             })
        }

        if (create_goods.rect_goods_detail3.isHasData()) {

            if (!create_goods.rect_goods_detail3.checkData())
                return

            result_json.push({
                                 "goods_contain": create_goods.rect_goods_detail3.goods_contain,
                                 "goods_kind_unique": create_goods.rect_goods_detail_basic_info.goods_kind_unique,
                                 "goods_barcode": create_goods.rect_goods_detail3.barcode,
                                 "goods_name": create_goods.rect_goods_detail3.goods_name,
                                 "goods_standard": create_goods.rect_goods_detail3.product_specification,
                                 "goods_brand": create_goods.rect_goods_detail_basic_info.goods_brand,
                                 "pc_shelf_state": create_goods.rect_goods_detail_basic_info.pc_shelf_state,
                                 "web_shelf_state": create_goods.rect_goods_detail_basic_info.web_shelf_state,
                                 "goods_sale_price": create_goods.rect_goods_detail3.unit_price,
                                 "goods_web_sale_price": create_goods.rect_goods_detail.web_price,
                                 "goods_cus_price": create_goods.rect_goods_detail3.member_price,
                                 "goods_in_price": create_goods.rect_goods_detail3.purchase_price,
                                 "goodsChengType": create_goods.rect_goods_detail3.price_type,
                                 "goods_unit": create_goods.rect_goods_detail3.goods_unit,
                                 "default_supplier_unique": create_goods.rect_goods_detail_basic_info.supplier_unique
                             })
        }

        goodsControl.reqUploadGoods_v2(function (is_succ, data) {
            var json_doc = JSON.parse(data)
            if (is_succ) {
                toast.openInfo("新建商品成功")
                cancelGoods()
            } else {
                if (!data) {
                    toast.openError("新建商品失败")
                    return
                }
                var json_doc = JSON.parse(data)
                toast.openError(json_doc.msg)
            }
        }, JSON.stringify(result_json))
    }

    function updateGoods_v2() {

        if (!edit_goods.rect_goods_detail_basic_info.checkData()) {
            return
        }

        if (!edit_goods.rect_goods_detail.checkData()) {
            return
        }

        var result_json = []

        //基础规格
        result_json.push({
                             "goods_contain": "1",
                             "goods_barcode": edit_goods.rect_goods_detail.barcode,
                             "goods_name": edit_goods.rect_goods_detail.goods_name,
                             "goods_standard": edit_goods.rect_goods_detail.product_specification,
                             "goodsChengType": edit_goods.rect_goods_detail.price_type,
                             "goods_kind_unique": edit_goods.rect_goods_detail_basic_info.goods_kind_unique,
                             "goods_brand": edit_goods.rect_goods_detail_basic_info.goods_brand,
                             "pc_shelf_state": edit_goods.rect_goods_detail_basic_info.pc_shelf_state,
                             "web_shelf_state": edit_goods.rect_goods_detail_basic_info.web_shelf_state,
                             "goods_sale_price": edit_goods.rect_goods_detail.unit_price,
                             "goods_web_sale_price": edit_goods.rect_goods_detail.web_price,
                             "goods_cus_price": edit_goods.rect_goods_detail.member_price,
                             "goods_in_price": edit_goods.rect_goods_detail.purchase_price,
                             "goods_unit": edit_goods.rect_goods_detail.goods_unit,
                             "default_supplier_unique": edit_goods.rect_goods_detail_basic_info.supplier_unique
                         })

        if (edit_goods.rect_goods_detail2.isHasData()) {

            if (!edit_goods.rect_goods_detail2.checkData())
                return

            result_json.push({
                                 "goods_contain": edit_goods.rect_goods_detail2.goods_contain,
                                 "goods_barcode": edit_goods.rect_goods_detail2.barcode,
                                 "goods_name": edit_goods.rect_goods_detail2.goods_name,
                                 "goodsChengType": edit_goods.rect_goods_detail2.price_type,
                                 "goods_kind_unique": edit_goods.rect_goods_detail_basic_info.goods_kind_unique,
                                 "goods_brand": edit_goods.rect_goods_detail_basic_info.goods_brand,
                                 "pc_shelf_state": edit_goods.rect_goods_detail_basic_info.pc_shelf_state,
                                 "web_shelf_state": edit_goods.rect_goods_detail_basic_info.web_shelf_state,
                                 "goods_sale_price": edit_goods.rect_goods_detail2.unit_price,
                                 "goods_web_sale_price": edit_goods.rect_goods_detail2.web_price,
                                 "goods_cus_price": edit_goods.rect_goods_detail2.member_price,
                                 "goods_in_price": edit_goods.rect_goods_detail2.purchase_price,
                                 "goods_standard": edit_goods.rect_goods_detail2.product_specification,
                                 "goods_unit": edit_goods.rect_goods_detail.goods_unit,
                                 "default_supplier_unique": edit_goods.rect_goods_detail_basic_info.supplier_unique
                                 // "goods_unit": edit_goods.rect_goods_detail2.goods_unit,
                             })
        }

        if (edit_goods.rect_goods_detail3.isHasData()) {

            if (!edit_goods.rect_goods_detail3.checkData())
                return

            result_json.push({
                                 "goods_contain": edit_goods.rect_goods_detail3.goods_contain,
                                 "goods_barcode": edit_goods.rect_goods_detail3.barcode,
                                 "goods_name": edit_goods.rect_goods_detail3.goods_name,
                                 "goods_standard": edit_goods.rect_goods_detail3.product_specification,
                                 "goodsChengType": edit_goods.rect_goods_detail3.price_type,
                                 "goods_kind_unique": edit_goods.rect_goods_detail_basic_info.goods_kind_unique,
                                 "goods_brand": edit_goods.rect_goods_detail_basic_info.goods_brand,
                                 "pc_shelf_state": edit_goods.rect_goods_detail_basic_info.pc_shelf_state,
                                 "web_shelf_state": edit_goods.rect_goods_detail_basic_info.web_shelf_state,
                                 "goods_sale_price": edit_goods.rect_goods_detail3.unit_price,
                                 "goods_web_sale_price": edit_goods.rect_goods_detail3.web_price,
                                 "goods_cus_price": edit_goods.rect_goods_detail3.member_price,
                                 "goods_in_price": edit_goods.rect_goods_detail3.purchase_price,
                                 "goods_unit": edit_goods.rect_goods_detail.goods_unit,
                                 "default_supplier_unique": edit_goods.rect_goods_detail_basic_info.supplier_unique
                                 // "goods_unit": edit_goods.rect_goods_detail3.goods_unit,
                             })
        }

        goodsControl.reqUpdateGoodsByDataJson_v2(function (is_succ, data) {
            if (is_succ) {
                toast.openInfo("修改商品成功")
            } else {
                toast.openWarn("修改商品失败")
            }
        }, JSON.stringify(result_json))
    }

    function cancelGoods() {
        cur_goods_barcode = ""
        is_create_goods = false

        clearAllGoodsInfo()

        create_goods.rect_goods_detail_basic_info.is_open_goods_kind_selector = false
        create_goods.rect_goods_detail.is_open_goods_unit_selector = false

        edit_goods.rect_goods_detail_basic_info.is_open_goods_kind_selector = false
        edit_goods.rect_goods_detail.is_open_goods_unit_selector = false
    }

    function deleteGoods(goods_barcode_in = "") {
        // 关联商品列表
        var cur_goods_doc = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode_in))

        function callback(is_succ, data) {
            if (is_succ) {
                toast.openInfo("删除成功")
            } else {
                toast.openError("删除失败")
            }
        }

        if (cur_goods_doc.goods_contain == 1) {
            var json_doc = JSON.parse(goodsManager.getForeignGoodsJsonListByBarcode(goods_barcode_in))

            if (json_doc) {
                if (json_doc.length > 0) {
                    goodsControl.reqDelGoodsByBarcode(callback, json_doc[0].goods_barcode)
                }
                if (json_doc.length > 1) {
                    goodsControl.reqDelGoodsByBarcode(callback, json_doc[1].goods_barcode)
                }
            }
        }

        goodsControl.reqDelGoodsByBarcode(callback, goods_barcode_in)

        cancelGoods()
    }

    function showStockChangeIn() {
        window_root.loader_4_stock_del.sourceComponent = popup_sotck_change_in
        window_root.loader_4_stock_del.item.open()
    }
    function showStockChangeOut() {
        window_root.loader_4_stock_del.sourceComponent = popup_sotck_change_out
        window_root.loader_4_stock_del.item.open()
    }

    Component {
        id: popup_sotck_change_in
        PopupSotckChange {
            function callback(is_succ, data) {
                if (is_succ) {
                    toast.openInfo("入库成功")
                } else {
                    toast.openInfo("入库失败")
                }
            }
            title_name: "请输入入库数量"
            onSigOpen: {
                keyboard_c.closeAll()
            }
            onConfirm: {
                goodsControl.reqStockChange(callback, cur_goods_barcode, str_num)
                keyboard_c.closeAll()
            }
        }
    }

    Component {
        id: popup_sotck_change_out
        PopupSotckChange {
            function callback(is_succ, data) {
                if (is_succ) {
                    toast.openInfo("出库成功")
                } else {
                    toast.openInfo("出库失败")
                }
            }
            title_name: "请输入出库数量"
            onSigOpen: {
                keyboard_c.closeAll()
            }
            onConfirm: {
                goodsControl.reqStockChange(callback, cur_goods_barcode, -Number(str_num))
                keyboard_c.closeAll()
            }
        }
    }

    function showTagList(goods_barcode) {
        window_root.loader_4_tag_list.sourceComponent = compo_popup_tag_list
        window_root.loader_4_tag_list.item.open(goods_barcode)
    }
    Component {
        id: compo_popup_tag_list
        PopupTagList {}
    }

    // 默认
    CusRect {
        anchors.fill: parent
        visible: cur_goods_barcode == "" && !is_create_goods
        color: ST.color_white_pure

        CusRect {
            width: 180 * dpi_ratio
            height: 180 * dpi_ratio
            anchors.horizontalCenter: parent.horizontalCenter
            y: 240 * dpi_ratio
            color: ST.color_transparent

            Image {
                anchors.fill: parent
                source: "/Images/img_goods.png"
            }
        }

        CusButton {
            width: 230 * dpi_ratio
            height: 70 * dpi_ratio
            anchors.horizontalCenter: parent.horizontalCenter
            y: 450 * dpi_ratio
            text: "新增商品"
            color: ST.color_green
            onClicked: {
                if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_GOODS_SAVE_UPDATE)) {
                    createGoods()
                    create_goods.rect_tabbar.cur_tab_enum = EnumTool.GOODS_INFO_TAB_SPEC_BASE
                } else {
                    toast.openWarn("无此权限")
                }
            }
        }
    }

    // 编辑商品
    EditGoods {
        id: edit_goods
        anchors.fill: parent
        color: ST.color_white_pure
        visible: cur_goods_barcode != ""
    }

    // 新建商品
    CreateGoods {
        id: create_goods
        anchors.fill: parent
        color: ST.color_white_pure
        visible: cur_goods_barcode == "" && is_create_goods
    }

    Connections {
        target: mqttControl
        function onSigMqttRefreshGoodsByBarcode4Qml(goods_barcode) {
            var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))
            if (cur_goods) {
                if (cur_goods.goods_barcode == cur_goods_barcode)
                    setCurGoodsByBarcode(cur_goods.goods_barcode)
            }
        }
    }
}
