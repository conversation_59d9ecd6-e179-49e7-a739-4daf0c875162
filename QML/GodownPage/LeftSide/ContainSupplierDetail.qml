﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import "ContainSupplierDetail"

CusRect {
    id: supplier_detail

    property bool is_create_supplier: false
    property string cur_supplier_id: ""
    property string cur_supplier_unique: ""
    property string cur_supplier_name: ""
    property string cur_supplier_contact: ""
    property string cur_supplier_phone: ""
    property string cur_supplier_address: ""
    property string cur_supplier_kind_id: "" //供应商分类

    function createSupplier() {
        resetInfo()
        is_create_supplier = true
    }

    function setCurSupplier(supplier_id, supplier_unique, supplier_name, supplier_contact, supplier_phone, supplier_address, supplier_kind_id) {
        resetInfo()
        cur_supplier_id = supplier_id
        cur_supplier_unique = supplier_unique
        cur_supplier_name = supplier_name
        cur_supplier_contact = supplier_contact
        cur_supplier_phone = supplier_phone
        cur_supplier_address = supplier_address
        supplier_detail.cur_supplier_kind_id = supplier_kind_id ? supplier_kind_id : ""
        supplier_info.focusCurCategoryCombo()
    }

    function cancelSupplier() {
        cur_supplier_unique = ""
        is_create_supplier = false
    }

    function resetInfo() {
        cur_supplier_id = ""
        cur_supplier_unique = ""
        cur_supplier_name = ""
        cur_supplier_contact = ""
        cur_supplier_phone = ""
        cur_supplier_address = ""
        cur_supplier_kind_id = ""
    }

    function checkData() {
        if (cur_supplier_name == "") {
            toast.openWarn("供应商名称为空!")
            return false
        }
        if (cur_supplier_phone == "") {
            toast.openWarn("供应商电话为空!")
            return false
        }
        if (cur_supplier_phone.length != 11) {
            toast.openWarn("供应商电话有误!")
            return false
        }
        if (cur_supplier_kind_id == "") {
            toast.openWarn("供应商分类为空!")
            return false
        }
        if (cur_supplier_contact == "") {
            toast.openWarn("供应商联系人空!")
            return false
        }
        return true
    }

    function reqCreateSupplier() {

        if (!checkData())
            return

        var data = {
            "shop_name": cur_supplier_name,
            "shop_address_detail": cur_supplier_address,
            "shop_phone": cur_supplier_phone,
            "supplier_kind_id": cur_supplier_kind_id,
            "company_leagl": cur_supplier_contact
        }

        supplierControl.reqCreateSupplier4Qml(function (is_succ, data) {
            if (is_succ) {
                toast.openInfo("创建成功")
                resetInfo()
                cancelSupplier()
                supplier_mamagement.refreshSupplierInfoOnline()
                // supplier_mamagement.refreshSupplierInfo()
            } else {
                toast.openInfo("创建失败")
            }
        }, JSON.stringify(data))
    }

    function reqChangeSupplier() {

        if (!checkData())
            return

        var data = {
            "shop_name": cur_supplier_name,
            "supplier_unique": cur_supplier_unique,
            "shop_address_detail": cur_supplier_address,
            "shop_phone": cur_supplier_phone,
            "supplier_kind_id": cur_supplier_kind_id,
            "company_leagl": cur_supplier_contact
        }

        supplierControl.reqChangeSupplier4Qml(function (is_succ, data) {
            if (is_succ) {
                toast.openInfo("更改成功")
                resetInfo()
                supplier_mamagement.refreshSupplierInfoOnline()
                // supplier_mamagement.refreshSupplierInfo()
            } else {
                toast.openWarn("更改失败")
            }
        }, JSON.stringify(data))
    }

    CusRect {
        anchors.fill: parent
        visible: cur_supplier_unique == "" && !is_create_supplier
        color: ST.color_white_pure

        CusRect {
            width: 180 * dpi_ratio
            height: 180 * dpi_ratio
            anchors.horizontalCenter: parent.horizontalCenter
            y: 240 * dpi_ratio
            color: ST.color_transparent

            Image {
                anchors.fill: parent
                source: "/Images/img_supplier.png"
            }
        }

        CusButton {
            width: 230 * dpi_ratio
            height: 70 * dpi_ratio
            anchors.horizontalCenter: parent.horizontalCenter
            y: 450 * dpi_ratio
            text: "新增供应商"
            color: ST.color_green
            onClicked: {
                createSupplier()
            }
        }
    }

    SupplierInfo {
        id: supplier_info
        anchors.fill: parent
        visible: cur_supplier_unique != "" || is_create_supplier
    }
}
