﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import "../../.."

CusRect {
    color: ST.color_white_pure
    id: rect_goods_detail

    property alias tf_phone: tf_phone

    Component.onCompleted: {
        refreshCategoryCombo()
    }

    onVisibleChanged: {
        refreshCategoryCombo()
    }

    function focusCurCategoryCombo() {

        if (cur_supplier_kind_id == "") {
            combo_category.currentIndex = -1
            return
        }

        for (var i = 0; i < lm_category.count; ++i) {
            var cur_item = lm_category.get(i)
            if (Number(cur_item.id) == Number(cur_supplier_kind_id)) {
                combo_category.currentIndex = i
                return
            }
        }
        combo_category.currentIndex = -1
    }

    function refreshCategoryCombo() {
        supplierControl.reqAllSupplierCategory4Qml(function (data) {
            combo_category.is_need_init = true

            lm_category.clear()
            var data_json = JSON.parse(data)

            var json_data = data_json.data

            for (var i = 0; i < json_data.length; ++i) {
                var cur_goods_kind = json_data[i]
                lm_category.append(cur_goods_kind)
            }

            if (is_create_supplier) {
                combo_category.currentIndex = -1
            } else {
                focusCurCategoryCombo()
            }
            combo_category.is_need_init = false
        })
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 15 * dpi_ratio
        anchors.margins: 30 * dpi_ratio

        CusRect {
            Layout.fillWidth: true
            Layout.preferredHeight: 70 * dpi_ratio
            color: ST.color_transparent
            CusText {
                text: "供应商详情"
                anchors.verticalCenter: parent.verticalCenter
                font {
                    pixelSize: 30 * dpi_ratio
                    bold: true
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                    }
                    CusText {
                        text: "供应商"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                    }
                }
                CusTextField {
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    keyboard_status: 0
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio
                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio

                    text: cur_supplier_name
                    placeholderText: "请输入供应商名称"
                    onTextEdited: {
                        cur_supplier_name = text
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                    }
                    CusText {
                        text: "联系人"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                    }
                }
                CusTextField {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    keyboard_status: 0
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio
                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio
                    text: cur_supplier_contact
                    placeholderText: "请输入联系人"
                    onTextEdited: {
                        cur_supplier_contact = text
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                    }
                    CusText {
                        text: "联系电话"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                    }
                }
                CusTextField {
                    id: tf_phone
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    keyboard_status: 1
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio
                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio
                    text: cur_supplier_phone
                    placeholderText: "请输入联系电话"
                    onTextEdited: {
                        cur_supplier_phone = text
                    }
                    validator: RegularExpressionValidator {
                        regularExpression: /^1\d{10}$/
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                        visible: false
                    }
                    CusText {
                        text: "所在地址"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                    }
                }
                CusTextField {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    keyboard_status: 0
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio
                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio
                    text: cur_supplier_address
                    placeholderText: "请输入地址"
                    onTextEdited: {
                        cur_supplier_address = text
                    }
                }
            }
        }
        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                    }
                    CusText {
                        text: "所属分类"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                    }
                }

                CusComboBox {
                    id: combo_category
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    textRole: "supplier_kind_name"
                    model: ListModel {
                        id: lm_category
                    }
                    onCurrentIndexChanged: {
                        if (is_need_init || currentIndex < 0)
                            return

                        cur_supplier_kind_id = lm_category.get(currentIndex).id
                    }
                }
            }
        }

        CusSpacer {
            Layout.fillHeight: true
        }

        CusRect {
            Layout.preferredHeight: 100 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent
                spacing: 10 * ST.dpi_ratio

                CusButton {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredWidth: 200
                    text: "取消"
                    onClicked: {
                        cancelSupplier()
                    }
                }

                CusButton {
                    visible: !is_create_supplier
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredWidth: 200
                    text: "删除"
                    color: ST.color_red
                    onClicked: {
                        showDelCurSupplierConfirm()
                    }
                }

                CusButton {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredWidth: 330

                    text: "保存供应商"
                    onClicked: {
                        if (is_create_supplier) {
                            reqCreateSupplier()
                        } else {
                            reqChangeSupplier()
                        }
                        keyboard_c.closeAll()
                    }
                }
            }
        }
    }

    function showDelCurSupplierConfirm() {
        window_root.compo_del_cur_supplier.sourceComponent = compo_del_cur_supplier
        window_root.compo_del_cur_supplier.item.open()
    }

    Component {
        id: compo_del_cur_supplier
        PopupConfirm {
            title_name: "删除供应商"
            message_info: "确定要删除当前供应商吗?"
            property string goods_barcode: ""
            onConfirm: {
                supplierControl.reqDelSupplier4Qml(function (is_succ, data) {
                    if (is_succ) {
                        toast.openInfo("删除供应商成功")
                        //    supplier_mamagement.refreshSupplierInfoOnline()
                        cancelSupplier()
                    } else {
                        toast.openWarn("删除供应商失败")
                    }
                    supplier_mamagement.refreshSupplierInfoOnline()
                }, cur_supplier_id)
            }
        }
    }
}
