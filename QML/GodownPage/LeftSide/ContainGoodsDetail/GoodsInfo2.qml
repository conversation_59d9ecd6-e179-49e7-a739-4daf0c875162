﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import "../../.."

CusRect {
    color: ST.color_white_pure
    id: rect_goods_detail

    property string page_name: ""

    property bool is_has_goods: false

    property string barcode: ""
    property string goods_name: ""
    property string goods_contain: ""
    property string unit_price: ""
    property string web_price: "" //网购价
    property string member_price: ""
    property string purchase_price: ""
    property string product_specification: ""

    function isHasData() {
        if (barcode == "" && goods_name == "" && goods_contain == "" && unit_price == "" && member_price == "" && purchase_price == "" && product_specification == "")
            return false
        return true
    }

    function checkData() {
        if (barcode == "") {
            toast.openWarn(page_name + " 商品条码有误!")
            return false
        } else if (goods_name == "") {
            toast.openWarn(page_name + " 商品名称有误!")
            return false
        } else if (goods_contain == "") {
            toast.openWarn(page_name + " 商品出入库换算有误!")
            return false
        } else if (unit_price == "") {
            toast.openWarn(page_name + " 商品零售单价有误!")
            return false
        } else if (purchase_price == "") {
            toast.openWarn(page_name + " 商品采购单价有误!")
            return false
        }
        return true
    }

    function resetInfo() {
        is_has_goods = false
        barcode = ""
        goods_name = ""
        goods_contain = ""
        unit_price = ""
        web_price = "" //网购价
        member_price = ""
        purchase_price = ""
        product_specification = ""
    }

    function setGoodsByBarcode(goods_barcode) {
        var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))
        barcode = cur_goods.goods_barcode
        goods_name = cur_goods.goods_name
        unit_price = Number(cur_goods.goods_sale_price).toFixed(2)
        web_price = Number(cur_goods.goods_web_sale_price).toFixed(2)
        member_price = Number(cur_goods.goods_cus_price).toFixed(2)
        purchase_price = Number(cur_goods.goods_in_price).toFixed(2)
        product_specification = cur_goods.goods_standard
        goods_contain = cur_goods.goods_contain
        is_has_goods = true
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 15 * dpi_ratio

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "商品条码"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                            visible: true
                        }
                    }
                }
                CusTextField {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    keyboard_status: 1
                    text: rect_goods_detail.barcode
                    placeholderText: "请输入/扫描商品条码"
                    enabled: !is_has_goods
                    onTextEdited: {
                        rect_goods_detail.barcode = text
                    }
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio
                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio
                }
            }
        }
        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "商品名称"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                            visible: true
                        }
                    }
                }
                CusTextField {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    text: rect_goods_detail.goods_name
                    placeholderText: "请输入商品名称"
                    onTextEdited: {
                        rect_goods_detail.goods_name = text
                    }
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio
                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 120 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "出入库换算"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                            visible: true
                        }
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusTextField {
                                anchors.fill: parent
                                text: rect_goods_detail.goods_contain
                                placeholderText: "请输入最小单位库存数量"
                                onTextEdited: {
                                    goods_contain = text
                                }
                                keyboard_status: 1
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }
                    }
                }
            }
        }
        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 15 * dpi_ratio

                        CusRect {
                            Layout.preferredWidth: 100 * dpi_ratio
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusText {
                                text: "零售单价"
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.right: parent.right
                                font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                                CusText {
                                    text: "*"
                                    color: ST.color_red
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.right: parent.left
                                    font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                    visible: true
                                }
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusTextField {
                                anchors.fill: parent
                                text: rect_goods_detail.unit_price
                                onTextEdited: {
                                    rect_goods_detail.unit_price = text
                                }
                                is_clicked_select_all: true
                                keyboard_status: 1
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 15 * dpi_ratio

                        CusRect {
                            Layout.preferredWidth: 100 * dpi_ratio
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusText {
                                text: "会员单价"
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.right: parent.right
                                font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                                CusText {
                                    text: "*"
                                    color: ST.color_red
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.right: parent.left
                                    font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                    visible: false
                                }
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusTextField {
                                anchors.fill: parent
                                text: rect_goods_detail.member_price
                                onTextEdited: {
                                    rect_goods_detail.member_price = text
                                }
                                is_clicked_select_all: true
                                keyboard_status: 1
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 15 * dpi_ratio

                        CusRect {
                            Layout.preferredWidth: 100 * dpi_ratio
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusText {
                                text: "网购单价"
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.right: parent.right
                                font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                                CusText {
                                    text: "*"
                                    color: ST.color_red
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.right: parent.left
                                    font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                    visible: true
                                }
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusTextField {
                                anchors.fill: parent
                                text: rect_goods_detail.web_price
                                onTextEdited: {
                                    rect_goods_detail.web_price = text
                                }
                                is_clicked_select_all: true
                                keyboard_status: 1
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 15 * dpi_ratio

                        CusRect {
                            Layout.preferredWidth: 100 * dpi_ratio
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("采购单价")
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.right: parent.right
                                font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                                CusText {
                                    text: "*"
                                    color: ST.color_red
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.right: parent.left
                                    font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                    visible: false
                                }
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusTextField {
                                anchors.fill: parent
                                text: rect_goods_detail.purchase_price
                                onTextEdited: {
                                    rect_goods_detail.purchase_price = text
                                }
                                is_clicked_select_all: true
                                keyboard_status: 1
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "商品规格"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                            visible: false
                        }
                    }
                }
                CusTextField {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    text: rect_goods_detail.product_specification
                    placeholderText: "商品容量、重量、抽数等信息"
                    onTextEdited: {
                        rect_goods_detail.product_specification = text
                    }
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio
                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio
                }
            }
        }
        CusSpacer {
            Layout.fillHeight: true
        }
    }
}
