﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../../.."
import EnumTool 1.0

CusRect {
    property alias rect_goods_detail_basic_info: rect_goods_detail_basic_info
    property alias rect_goods_detail: rect_goods_detail
    property alias rect_goods_detail2: rect_goods_detail2
    property alias rect_goods_detail3: rect_goods_detail3
    property alias rect_tabbar: rect_tabbar

    function printPriceTag() {
        if (rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_BASE) {
            printerControl.printPriceTagByBarcode(rect_goods_detail.barcode)
        } else if (rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_MIDDLE) {
            printerControl.printPriceTagByBarcode(rect_goods_detail2.barcode)
        } else if (rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_MAX) {
            printerControl.printPriceTagByBarcode(rect_goods_detail3.barcode)
        }
    }

    function showDelCurGoodsConfirm(goods_barcode_in) {
        window_root.loader_4_del_cur_goods.sourceComponent = compo_del_cur_goods
        window_root.loader_4_del_cur_goods.item.goods_barcode = goods_barcode_in
        window_root.loader_4_del_cur_goods.item.open()
    }

    function setContainGoodsNum(goods_num) {
        lv_tabbar.resetTabbar(goods_num + 1)
        switch (goods_num) {
        case 1:
            for (var i = 0; i < model_tabbar.count; ++i) {
                var cur_item = model_tabbar.get(i)
                if (i == 0) {
                    model_tabbar.setProperty(i, "is_enabled", true)
                } else {
                    model_tabbar.setProperty(i, "is_enabled", false)
                }
            }
            break
        case 2:
            for (var i = 0; i < model_tabbar.count; ++i) {
                var cur_item = model_tabbar.get(i)
                if (i <= 1) {
                    model_tabbar.setProperty(i, "is_enabled", true)
                } else {
                    model_tabbar.setProperty(i, "is_enabled", false)
                }
            }
            break
        case 3:
            for (var i = 0; i < model_tabbar.count; ++i) {
                var cur_item = model_tabbar.get(i)
                if (i <= 2) {
                    model_tabbar.setProperty(i, "is_enabled", true)
                } else {
                    model_tabbar.setProperty(i, "is_enabled", false)
                }
            }
            break
        }
    }

    Component {
        id: compo_del_cur_goods
        PopupConfirm {
            title_name: qsTr("删除商品")
            message_info: qsTr("确定要删除当前商品吗?")
            property string goods_barcode: ""
            onConfirm: {
                deleteGoods(goods_barcode)
            }
        }
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 0

        CusRect {
            Layout.fillWidth: true
            Layout.preferredHeight: 150 * dpi_ratio
            color: ST.color_white_deeper
            Layout.alignment: Qt.AlignTop

            // 图片
            CusRect {
                width: 110 * dpi_ratio
                height: 110 * dpi_ratio
                anchors.left: parent.left
                anchors.leftMargin: 20 * dpi_ratio
                anchors.bottom: parent.bottom
                anchors.bottomMargin: 10 * dpi_ratio
                color: ST.color_transparent
            }

            // 按钮
            CusRect {
                width: 460 * dpi_ratio
                height: 50 * dpi_ratio
                anchors.right: parent.right
                anchors.rightMargin: 30 * dpi_ratio
                anchors.top: parent.top
                anchors.topMargin: 28 * dpi_ratio
                color: ST.color_transparent

                RowLayout {
                    anchors.fill: parent
                    visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_MAX || rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_MIDDLE
                             || rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_BASE

                    CusButton {
                        text: qsTr("入库")
                        Layout.fillHeight: true
                        Layout.preferredWidth: 125 * dpi_ratio
                        visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_BASE && configTool.isShowStock
                        onClicked: {
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_GOODS_STOCK_IN_UPDATE))
                                showStockChangeIn()
                            else
                                toast.openWarn(qsTr("无此权限"))
                        }
                    }
                    CusButton {
                        text: qsTr("出库")
                        Layout.fillHeight: true
                        Layout.preferredWidth: 125 * dpi_ratio
                        color: ST.color_orange
                        onClicked: {
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_GOODS_STOCK_OUT_UPDATE))
                                showStockChangeOut()
                            else
                                toast.openWarn(qsTr("无此权限"))
                        }
                        visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_BASE && configTool.isShowStock
                    }
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                    CusButton {
                        text: qsTr("打印价签")
                        Layout.fillHeight: true
                        Layout.preferredWidth: 125 * dpi_ratio * configTool.fontRatio
                        color: ST.color_blue_deeper
                        onClicked: {
                            printPriceTag()
                        }
                    }
                }
            }

            // TabBar
            CusRect {
                id: rect_tabbar
                width: 500 * dpi_ratio
                height: 50 * dpi_ratio
                anchors.right: parent.right
                anchors.rightMargin: 1
                anchors.bottom: parent.bottom
                color: ST.color_white_deeper
                property int cur_tab_enum: EnumTool.GOODS_INFO_TAB_SPEC_BASE

                function closeAllSelector() {
                    rect_goods_detail_basic_info.is_open_supplier_selector = false
                    rect_goods_detail_basic_info.is_open_goods_kind_selector = false
                    rect_goods_detail.is_open_goods_unit_selector = false
                }

                onCur_tab_enumChanged: {
                    closeAllSelector()
                }

                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    ListView {
                        id: lv_tabbar
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        model: ListModel {
                            id: model_tabbar
                        }
                        orientation: ListView.Horizontal
                        highlightFollowsCurrentItem: true
                        highlightMoveDuration: 0
                        interactive: false

                        delegate: Component {
                            CusRect {
                                id: rect_tab_btn
                                height: lv_tabbar.height
                                width: 120 * dpi_ratio
                                property bool is_current_item: rect_tabbar.cur_tab_enum == tab_enum
                                color: is_current_item ? ST.color_white_pure : ST.color_white_deeper

                                CusText {
                                    text: "+"
                                    anchors.centerIn: parent
                                    visible: !model.is_enabled
                                    color: ST.color_green
                                }

                                CusText {
                                    text: model.name
                                    anchors.centerIn: parent
                                    visible: model.is_enabled
                                    font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        if (!model.is_enabled) {
                                            model.is_enabled = true
                                            rect_tabbar.cur_tab_enum = tab_enum
                                            if (index == 1) {
                                                lv_tabbar.addTabbar3()
                                            }
                                        } else {
                                            rect_tabbar.cur_tab_enum = tab_enum
                                        }
                                    }
                                }
                            }
                        }

                        Component.onCompleted: {

                        }

                        function resetTabbar(num) {
                            model_tabbar.clear()
                            model_tabbar.append({
                                                    "name": qsTr("基础规格"),
                                                    "tab_enum": EnumTool.GOODS_INFO_TAB_SPEC_BASE,
                                                    "is_enabled": true
                                                })
                            model_tabbar.append({
                                                    "name": qsTr("中间规格"),
                                                    "tab_enum": EnumTool.GOODS_INFO_TAB_SPEC_MIDDLE,
                                                    "is_enabled": false
                                                })

                            if (num > 2) {
                                model_tabbar.append({
                                                        "name": qsTr("最大规格"),
                                                        "tab_enum": EnumTool.GOODS_INFO_TAB_SPEC_MAX,
                                                        "is_enabled": false
                                                    })
                            }
                        }

                        function addTabbar3() {
                            model_tabbar.append({
                                                    "name": qsTr("最大规格"),
                                                    "tab_enum": EnumTool.GOODS_INFO_TAB_SPEC_MAX,
                                                    "is_enabled": false
                                                })
                        }
                    }

                    CusRect {
                        id: rect_tab_btn_base
                        Layout.fillHeight: true
                        Layout.preferredWidth: 120 * dpi_ratio

                        property bool is_current_item: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_BASIC_INFO

                        color: is_current_item ? ST.color_white_pure : ST.color_white_deeper

                        CusText {
                            text: qsTr("基础信息")
                            anchors.centerIn: parent
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                rect_tabbar.cur_tab_enum = EnumTool.GOODS_INFO_TAB_BASIC_INFO
                            }
                        }
                        Component.onCompleted: {
                            rect_tabbar.cur_tab_enum = EnumTool.GOODS_INFO_TAB_BASIC_INFO
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.margins: 30 * dpi_ratio
            color: ST.color_white_pure

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                GoodsDetailBasicInfo {
                    id: rect_goods_detail_basic_info
                    page_name: qsTr("基础信息")
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    color: ST.color_white_pure
                    visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_BASIC_INFO
                }
                GoodsInfo {
                    id: rect_goods_detail
                    page_name: qsTr("基础规格")
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_BASE
                }
                GoodsInfo2 {
                    id: rect_goods_detail2
                    page_name: qsTr("中间规格")
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_MIDDLE
                }
                GoodsInfo2 {
                    id: rect_goods_detail3
                    page_name: qsTr("最大规格")
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_MAX
                }
                CusRect {
                    Layout.preferredHeight: 100 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 10 * ST.dpi_ratio

                        CusButton {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 160 * dpi_ratio
                            text: qsTr("取消")
                            onClicked: {
                                cancelGoods()
                            }
                        }

                        CusButton {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 160 * dpi_ratio
                            color: ST.color_red
                            text: qsTr("删除商品")

                            onClicked: {
                                if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_GOODS_DELETE_UPDATE)) {
                                    var goods_barcode_in

                                    switch (rect_tabbar.cur_tab_enum) {
                                    case EnumTool.GOODS_INFO_TAB_SPEC_BASE:
                                        goods_barcode_in = rect_goods_detail.barcode
                                        break
                                    case EnumTool.GOODS_INFO_TAB_SPEC_MIDDLE:
                                        goods_barcode_in = rect_goods_detail2.barcode
                                        break
                                    case EnumTool.GOODS_INFO_TAB_SPEC_MAX:
                                        goods_barcode_in = rect_goods_detail3.barcode
                                        break
                                    }

                                    showDelCurGoodsConfirm(goods_barcode_in)
                                } else {
                                    toast.openWarn(qsTr("无此权限"))
                                }
                            }
                        }

                        CusButton {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 240 * dpi_ratio
                            text: qsTr("保存商品")
                            onClicked: {
                                keyboard_c.closeAll()
                                if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_GOODS_SAVE_UPDATE)) {
                                    updateGoods_v2()
                                } else {
                                    toast.openWarn(qsTr("无此权限"))
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
