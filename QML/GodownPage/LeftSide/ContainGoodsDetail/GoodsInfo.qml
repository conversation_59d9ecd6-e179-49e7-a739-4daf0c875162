﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import "../../.."

CusRect {
    color: ST.color_white_pure
    id: rect_goods_info_root

    property string page_name: ""

    property bool is_open_goods_unit_selector: false

    property string barcode: "" //条码
    property string goods_name: "" //商品名
    property string goods_unit: "" //单位
    property string unit_price: "" //单价
    property string web_price: "" //网购价
    property string member_price: "" //会员价
    property string purchase_price: "" //采购价
    property string product_specification: "" //规格信息
    property string remaining_inventory: "" //库存
    property string price_type: "0" //称重类型 0计件 1称重

    property string goods_kind_unique: "" //分类unique
    onGoods_kind_uniqueChanged: {
        if (goods_kind_unique == "") {
            goods_kind_name = ""
        }

        var goods_kind_json = JSON.parse(goodsKindManager.getGoodsKindByKindUnique4Qml(cur_goods.goods_kind_unique))
        if (goods_kind_json)
            goods_kind_name = goods_kind_json.goods_kind_name
    }
    property string goods_kind_name: "" //分类名

    function checkData() {
        if (barcode == "") {
            toast.openWarn(page_name + " 商品条码有误!")
            return false
        } else if (goods_name == "") {
            toast.openWarn(page_name + " 商品名称有误!")
            return false
        } else if (price_type == "") {
            toast.openWarn(page_name + " 计价类型有误!")
            return false
        } else if (unit_price == "") {
            toast.openWarn(page_name + " 零售单价有误!")
            return false
        } else if (purchase_price == "") {
            toast.openWarn(page_name + " 采购单价有误!")
            return false
        }
        //else if (goods_unit == "") {
        //    toast.openWarn(page_name + " 商品单位有误!")
        //    return false
        //}
        return true
    }

    function resetInfo() {
        barcode = ""
        goods_name = ""
        goods_unit = ""
        unit_price = ""
        web_price = ""
        member_price = ""
        purchase_price = ""
        product_specification = ""
        remaining_inventory = ""
        price_type = "0"
        goods_kind_unique = ""
    }

    function setGoodsByBarcode(goods_barcode) {
        var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))

        barcode = cur_goods.goods_barcode
        goods_name = cur_goods.goods_name
        unit_price = Number(cur_goods.goods_sale_price).toFixed(2)
        web_price = Number(cur_goods.goods_web_sale_price).toFixed(2)
        member_price = Number(cur_goods.goods_cus_price).toFixed(2)
        purchase_price = Number(cur_goods.goods_in_price).toFixed(2)
        product_specification = cur_goods.goods_standard
        goods_unit = cur_goods.goods_unit
        remaining_inventory = Number(cur_goods.goods_count).toFixed(2)
        price_type = cur_goods.goodsChengType
        rect_goods_detail_basic_info.pc_shelf_state = cur_goods.pc_shelf_state
        rect_goods_detail_basic_info.web_shelf_state = cur_goods.shelfState
        rect_goods_detail_basic_info.goods_brand = cur_goods.goods_brand
        rect_goods_detail_basic_info.goods_kind_unique = cur_goods.goods_kind_unique
        rect_goods_detail_basic_info.supplier_unique = cur_goods.default_supplier_unique
        rect_goods_detail_basic_info.refreshSupplierNameByUnique()
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 15 * dpi_ratio

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "商品条码"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                        }
                    }
                }
                CusTextField {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    keyboard_status: 1
                    text: barcode
                    placeholderText: "请输入/扫描商品条码"
                    enabled: is_create_goods
                    onTextEdited: {
                        barcode = text
                    }
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio
                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio

                    onAccepted: {
                        var goods_json = JSON.parse(goodsManager.getGoodsByBarcode4Qml(text))
                        if (goods_json) {
                            showGoodsExistConfirm(text)
                        } else {
                            goodsControl.reqGetOnlineGoodsInfo(function (is_succ, data) {
                                var goods_json = JSON.parse(data)
                                goods_json = goods_json.data
                                rect_goods_info_root.barcode = goods_json.goods_barcode
                                rect_goods_info_root.goods_name = goods_json.goods_name
                                rect_goods_detail_basic_info.supplier_unique = goods_json.default_supplier_unique
                                // rect_goods_detail_basic_info.goods_kind_unique = goods_json
                                rect_goods_info_root.product_specification = goods_json.goods_standard
                                rect_goods_info_root.unit_price = Number(goods_json.goods_sale_price).toFixed(2)
                                rect_goods_info_root.web_price = Number(goods_json.goods_sale_price).toFixed(2)
                                rect_goods_info_root.member_price = Number(goods_json.goods_cus_price).toFixed(2)
                                rect_goods_info_root.purchase_price = Number(goods_json.goods_in_price).toFixed(2)
                                rect_goods_info_root.remaining_inventory = Number(goods_json.goods_count).toFixed(2)
                                rect_goods_info_root.goods_unit = goods_json.goods_unit
                            }, text)
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "商品名称"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                        }
                    }
                }
                CusTextField {
                    id: tf_goods_name
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    text: goods_name
                    placeholderText: "请输入商品名称"

                    onTextEdited: {
                        goods_name = text
                    }
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio
                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio
                }
            }
        }
        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "计价类型"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                        }
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        spacing: 20
                        anchors.fill: parent

                        CusRadioManual {
                            id: radio_standard
                            Layout.preferredWidth: 130 * dpi_ratio
                            Layout.preferredHeight: 28 * dpi_ratio
                            Layout.alignment: Qt.AlignVCenter
                            text: "计件"
                            checked: price_type == "0"
                            onClicked: {
                                price_type = "0"
                            }
                        }

                        CusRadioManual {
                            id: radio_weight
                            Layout.preferredWidth: 130 * dpi_ratio
                            Layout.preferredHeight: 28 * dpi_ratio
                            Layout.alignment: Qt.AlignVCenter
                            text: "称重"
                            checked: price_type == "1"
                            onClicked: {
                                price_type = "1"
                            }
                        }
                        CusSpacer {
                            Layout.fillWidth: true
                        }
                    }
                }
            }
        }
        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "商品库存"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                            visible: false
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusTextField {
                                id: tf_remaining_inventory
                                anchors.fill: parent
                                enabled: false
                                text: Number(remaining_inventory).toFixed(2)
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            radius: ST.radius
                            border {
                                width: 1.5 * dpi_ratio
                                color: ST.color_grey
                            }
                            CusText {
                                id: t_goods_kind
                                anchors.verticalCenter: parent.verticalCenter
                                text: goods_unit
                                x: 10 * dpi_ratio
                            }

                            RowLayout {
                                anchors.fill: parent
                                anchors.leftMargin: 8 * dpi_ratio
                                visible: t_goods_kind.text == ""

                                CusText {
                                    text: "*"
                                    Layout.alignment: Qt.AlignVCenter
                                    x: 10 * dpi_ratio
                                    color: ST.color_red
                                }
                                CusText {
                                    text: "选择单位"
                                    Layout.alignment: Qt.AlignVCenter
                                    x: 10 * dpi_ratio
                                    color: ST.color_grey_font
                                }
                                CusSpacer {
                                    Layout.fillWidth: true
                                }
                            }

                            CusRect {
                                width: 20 * dpi_ratio
                                height: width
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.right: parent.right
                                anchors.rightMargin: 18 * dpi_ratio
                                color: ST.color_transparent
                                Image {
                                    anchors.fill: parent
                                    source: is_open_goods_unit_selector ? "/Images/left_grey.png" : "/Images/right_grey.png"
                                }
                            }

                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    var tmp_is_open_goods_unit_selector = !is_open_goods_unit_selector
                                    edit_goods.rect_tabbar.closeAllSelector()
                                    create_goods.rect_tabbar.closeAllSelector()

                                    if (tmp_is_open_goods_unit_selector)
                                        is_open_goods_unit_selector = true

                                    goods_management.rect_goods_unit_chooser.focusGoodsUnit(goods_unit)
                                }
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 15 * dpi_ratio

                        CusRect {
                            Layout.preferredWidth: 100 * dpi_ratio
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusText {
                                text: "零售单价"
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.right: parent.right
                                font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                                CusText {
                                    text: "*"
                                    color: ST.color_red
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.right: parent.left
                                    font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                }
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusTextField {
                                id: tf_unit_price
                                anchors.fill: parent
                                text: unit_price
                                onTextEdited: {
                                    unit_price = text
                                }
                                keyboard_status: 1
                                is_clicked_select_all: true
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 15 * dpi_ratio

                        CusRect {
                            Layout.preferredWidth: 100 * dpi_ratio
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusText {
                                text: "*"
                                color: ST.color_red
                                anchors.verticalCenter: parent.verticalCenter
                                visible: false
                            }
                            CusText {
                                text: "会员单价"
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.right: parent.right
                                font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusTextField {
                                id: tf_member_price
                                anchors.fill: parent
                                text: member_price
                                onTextEdited: {
                                    member_price = text
                                }
                                keyboard_status: 1
                                is_clicked_select_all: true
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 15 * dpi_ratio

                        CusRect {
                            Layout.preferredWidth: 100 * dpi_ratio
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusText {
                                text: "网购单价"
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.right: parent.right
                                font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusTextField {
                                anchors.fill: parent
                                text: web_price
                                onTextEdited: {
                                    web_price = text
                                }
                                keyboard_status: 1
                                is_clicked_select_all: true
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 15 * dpi_ratio

                        CusRect {
                            Layout.preferredWidth: 100 * dpi_ratio
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusText {
                                text: qsTr("采购单价")
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.right: parent.right
                                font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                                CusText {
                                    text: "*"
                                    color: ST.color_red
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.right: parent.left
                                    font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                }
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusTextField {
                                id: tf_purchase_price
                                anchors.fill: parent
                                text: purchase_price
                                onTextEdited: {
                                    purchase_price = text
                                }
                                keyboard_status: 1
                                is_clicked_select_all: true
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "商品规格"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                    }
                }
                CusTextField {
                    id: tf_product_specification
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    text: product_specification
                    placeholderText: "商品容量、重量、抽数等信息"
                    onTextEdited: {
                        product_specification = text
                    }
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio
                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio

                CusButton {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 200 * dpi_ratio
                    text: "TAG列表"

                    onClicked: {
                        showTagList(barcode)
                    }
                }

                CusSpacer {
                    Layout.fillWidth: true
                }
            }
        }

        CusSpacer {
            Layout.fillHeight: true
        }
    }
    
    function showGoodsExistConfirm(goods_barcode_in) {
        window_root.loader_4_del_cur_goods.sourceComponent = compo_goods_exist_confirm
        window_root.loader_4_del_cur_goods.item.goods_barcode = goods_barcode_in
        window_root.loader_4_del_cur_goods.item.open()
    }

    Component {
        id: compo_goods_exist_confirm
        CusPopupConfirm {
            title_name: "商品已存在"
            message_info: "商品已存在是否进行编辑?"
            property string goods_barcode: ""
            onConfirm: {
                if (goods_barcode == "")
                    return
                setCurGoodsByBarcode(goods_barcode)
            }

            onCancel: {
                
            }
        }
    }
    Connections {
        target: mqttControl
        function onSigMqttRefreshGoodsByBarcode4Qml(goods_barcode) {
            if (barcode == goods_barcode) {
                resetInfo()
                setGoodsByBarcode(goods_barcode)
            }
        }
    }
}
