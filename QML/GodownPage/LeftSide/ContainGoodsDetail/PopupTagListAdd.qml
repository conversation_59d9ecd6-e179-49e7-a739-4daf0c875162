﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../../.."
import TagEditHelper 1.0

Item {
    id: popup_root

    signal sigClose
    signal sigOpen
    signal sigClicked(var tag_name)

    TagEditHelper {
        id: tag_edit_helper
    }

    function open() {
        popup_root.visible = true
        tag_edit_helper.goodsBarcode = "0"
        sigOpen()
    }

    function close() {
        popup_root.visible = false
        keyboard_c.closeAll()
        sigClose()
    }

    signal confirm(var str_num)
    signal cancel

    property string title_name: "TAG选择"

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 400 * dpi_ratio
        height: 850 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        onVisibleChanged: {
            x = (parent.width - width) / 2
            y = (parent.height - height) / 2
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 20 * dpi_ratio
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent

                    ListView {
                        id: lv_tag_list
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true
                        model: tag_edit_helper
                        spacing: 2 * dpi_ratio
                        delegate: CusRect {
                            width: lv_tag_list.width
                            height: 70 * dpi_ratio
                            color: ST.color_white_pure

                            CusText {
                                text: TAG_NAME
                                anchors.centerIn: parent
                            }

                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    sigClicked(TAG_NAME)
                                    close()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
