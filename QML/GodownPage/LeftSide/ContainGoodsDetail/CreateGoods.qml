﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../../.."
import EnumTool 1.0

CusRect {

    property alias rect_goods_detail_basic_info: rect_goods_detail_basic_info
    property alias rect_goods_detail: rect_goods_detail
    property alias rect_goods_detail2: rect_goods_detail2
    property alias rect_goods_detail3: rect_goods_detail3
    property alias rect_tabbar: rect_tabbar

    ColumnLayout {
        anchors.fill: parent
        spacing: 0

        CusRect {
            Layout.fillWidth: true
            Layout.preferredHeight: 150 * dpi_ratio
            color: ST.color_white_deeper
            Layout.alignment: Qt.AlignTop

            // TabBar
            CusRect {
                id: rect_tabbar
                width: 500 * dpi_ratio
                height: 50 * dpi_ratio
                anchors.right: parent.right
                anchors.rightMargin: 1
                anchors.bottom: parent.bottom
                color: ST.color_white_deeper
                property int cur_tab_enum: EnumTool.GOODS_INFO_TAB_BASIC_INFO

                function closeAllSelector() {
                    rect_goods_detail_basic_info.is_open_supplier_selector = false
                    rect_goods_detail_basic_info.is_open_goods_kind_selector = false
                    rect_goods_detail.is_open_goods_unit_selector = false
                }

                onCur_tab_enumChanged: {
                    closeAllSelector()
                }

                RowLayout {
                    anchors.fill: parent
                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent

                        ListView {
                            id: lv_tabbar
                            anchors.fill: parent
                            model: ListModel {
                                id: model_tabbar_create
                            }
                            orientation: ListView.Horizontal
                            highlightFollowsCurrentItem: true
                            highlightMoveDuration: 0
                            interactive: false

                            delegate: CusRect {
                                id: rect_tab_btn
                                height: lv_tabbar.height
                                width: 120 * dpi_ratio
                                property bool is_current_item: rect_tabbar.cur_tab_enum == tab_enum
                                color: is_current_item ? ST.color_white_pure : ST.color_white_deeper

                                CusText {
                                    text: "+"
                                    anchors.centerIn: parent
                                    visible: !model.is_enabled
                                    color: ST.color_green
                                }

                                CusText {
                                    text: model.name
                                    anchors.centerIn: parent
                                    visible: model.is_enabled
                                    font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        if (!model.is_enabled) {
                                            model.is_enabled = true
                                            rect_tabbar.cur_tab_enum = tab_enum
                                            if (index == 1) {
                                                lv_tabbar.addTabbar3()
                                            }
                                        } else {
                                            rect_tabbar.cur_tab_enum = tab_enum
                                        }
                                    }
                                }
                            }

                            Component.onCompleted: {

                                //                                model_tabbar_create.append({
                                //                                                               "name": "基础规格",
                                //                                                               "tab_enum": EnumTool.GOODS_INFO_TAB_SPEC_BASE
                                //                                                           })
                            }

                            onVisibleChanged: {
                                if (visible) {
                                    lv_tabbar.resetTabbar(2)
                                }
                            }

                            function resetTabbar(num) {
                                model_tabbar_create.clear()
                                model_tabbar_create.append({
                                                               "name": "基础规格",
                                                               "tab_enum": EnumTool.GOODS_INFO_TAB_SPEC_BASE,
                                                               "is_enabled": true
                                                           })
                                model_tabbar_create.append({
                                                               "name": "中间规格",
                                                               "tab_enum": EnumTool.GOODS_INFO_TAB_SPEC_MIDDLE,
                                                               "is_enabled": false
                                                           })

                                if (num > 2) {
                                    model_tabbar_create.append({
                                                                   "name": "最大规格",
                                                                   "tab_enum": EnumTool.GOODS_INFO_TAB_SPEC_MAX,
                                                                   "is_enabled": false
                                                               })
                                }
                            }

                            function addTabbar3() {
                                model_tabbar_create.append({
                                                               "name": "最大规格",
                                                               "tab_enum": EnumTool.GOODS_INFO_TAB_SPEC_MAX,
                                                               "is_enabled": false
                                                           })
                            }
                        }
                    }

                    CusRect {
                        id: rect_tab_btn_base_create
                        Layout.fillHeight: true
                        Layout.preferredWidth: 120 * dpi_ratio

                        property bool is_current_item: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_BASIC_INFO

                        color: is_current_item ? ST.color_white_pure : ST.color_white_deeper

                        CusText {
                            text: "基础信息"
                            anchors.centerIn: parent
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                rect_tabbar.cur_tab_enum = EnumTool.GOODS_INFO_TAB_BASIC_INFO
                            }
                        }
                        Component.onCompleted: {
                            rect_tabbar.cur_tab_enum = EnumTool.GOODS_INFO_TAB_BASIC_INFO
                        }
                    }
                }
            }
        }
        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.margins: 30 * dpi_ratio
            //                Layout.topMargin: 0
            color: ST.color_white_pure

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                GoodsDetailBasicInfo {
                    id: rect_goods_detail_basic_info
                    page_name: "基础信息"
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    color: ST.color_white_pure
                    visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_BASIC_INFO
                }

                GoodsInfo {
                    id: rect_goods_detail
                    page_name: "基础规格"
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_BASE
                }
                GoodsInfo2 {
                    id: rect_goods_detail2
                    page_name: "中间规格"
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_MIDDLE
                }
                GoodsInfo2 {
                    id: rect_goods_detail3
                    page_name: "最大规格"
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: rect_tabbar.cur_tab_enum == EnumTool.GOODS_INFO_TAB_SPEC_MAX
                }

                CusRect {
                    Layout.preferredHeight: 100 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent

                        CusButton {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 210 * dpi_ratio
                            text: "取消"
                            onClicked: {
                                cancelGoods()
                            }
                        }
                        CusSpacer {
                            Layout.fillWidth: true
                        }
                        CusButton {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 330 * dpi_ratio
                            text: "创建商品"
                            onClicked: {
                                uploadGoods_v2()
                            }
                        }
                    }
                }
            }
        }
    }
}
