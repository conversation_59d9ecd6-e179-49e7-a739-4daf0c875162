﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../../.."
import TagEditHelper 1.0

Item {
    id: popup_root

    signal sigClose
    signal sigOpen

    TagEditHelper {
        id: tag_edit_helper
    }

    function open(goods_barcode) {
        popup_root.visible = true
        tag_edit_helper.goodsBarcode = goods_barcode
        sigOpen()
    }

    function close() {
        popup_root.visible = false
        keyboard_c.closeAll()
        sigClose()
    }

    signal confirm(var str_num)
    signal cancel

    property string title_name: "TAG列表"

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 600 * dpi_ratio
        height: 700 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        onVisibleChanged: {
            x = (parent.width - width) / 2
            y = (parent.height - height) / 2
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 20 * dpi_ratio
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent

                    ListView {
                        id: lv_tag_list
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true
                        model: tag_edit_helper
                        spacing: 2 * dpi_ratio
                        delegate: CusRect {
                            width: lv_tag_list.width
                            height: 80 * dpi_ratio
                            color: ST.color_white_pure

                            RowLayout {
                                anchors.fill: parent
                                spacing: 0

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    CusText {
                                        x: 40 * dpi_ratio
                                        anchors.verticalCenter: parent.verticalCenter
                                        text: model.TAG_NAME
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 60 * dpi_ratio
                                    color: ST.color_transparent

                                    CusImg {
                                        width: 40 * dpi_ratio
                                        height: width
                                        anchors.centerIn: parent
                                        source: "/Images/delete_red.png"
                                    }

                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            tag_edit_helper.delTag(model.TAG_NAME);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 50 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio

                            CusButton {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 100 * dpi_ratio
                                text: "添加"
                                onClicked: {
                                    showTagList()
                                }
                            }

                            CusButton {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 100 * dpi_ratio
                                text: "清空"
                                onClicked: {
                                    tag_edit_helper.clearTag()
                                }
                            }

                            CusSpacer {
                                Layout.fillWidth: true
                            }

                            CusButton {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 100 * dpi_ratio
                                text: "取消"
                                onClicked: {
                                    popup_root.close()
                                }
                            }

                            CusButton {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 100 * dpi_ratio
                                text: "保存"
                                onClicked: {
                                    tag_edit_helper.saveTag()
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    function showTagList() {
        window_root.loader_4_tag_list_add.sourceComponent = compo_popup_tag_list_add
        window_root.loader_4_tag_list_add.item.open()
    }
    Component {
        id: compo_popup_tag_list_add
        PopupTagListAdd {
            onSigClicked: {
                tag_edit_helper.addTag(tag_name)
                console.log(tag_name)
            }
        }
    }
}
