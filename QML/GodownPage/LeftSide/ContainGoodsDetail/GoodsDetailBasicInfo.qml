﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../../.."
import EnumTool 1.0

CusRect {
    id: rect_goods_detail_basic_info
    color: ST.color_white_pure

    property string page_name: ""

    property bool is_open_supplier_selector: false

    property string supplier_unique: "" //供货商unique
    property string supplier_name: "" //供货商名

    function refreshSupplierNameByUnique() {
        if (supplier_unique == "")
            return

        var json_data = JSON.parse(supplierControl.getSupplierJson())
        for (var i = 0; i < json_data.length; ++i) {
            var cur_goods_kind = json_data[i]
            if (cur_goods_kind.supplier_unique == supplier_unique) {
                supplier_name = cur_goods_kind.supplier_name
            }
        }
    }

    property bool is_open_goods_kind_selector: false
    property string goods_kind_unique: "" //分类unique
    property string goods_kind_name: "" //分类名
    onGoods_kind_uniqueChanged: {
        if (goods_kind_unique == "") {
            goods_kind_name = ""
            return
        }

        var goods_kind_json = JSON.parse(goodsKindManager.getGoodsKindByKindUnique4Qml(goods_kind_unique))
        if (goods_kind_json)
            goods_kind_name = goods_kind_json.goods_kind_name
    }

    property int pc_shelf_state: 1 //1上架 2下架
    property int web_shelf_state: 1 //1上架 2下架
    property string goods_brand: "" //品牌

    function checkData() {
        if (goods_kind_unique == "") {
            toast.openWarn(page_name + " 分类信息有误!")
            return false
        }
        return true
    }

    function resetInfo() {
        pc_shelf_state = 1
        web_shelf_state = 1
        goods_brand = ""
        goods_kind_unique = ""
        supplier_name = ""
        supplier_unique = ""
        supplier_name = ""
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 15 * dpi_ratio

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "商品分类"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                            visible: true
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent
                    radius: ST.radius
                    border {
                        width: 1.5 * dpi_ratio
                        color: ST.color_grey
                    }
                    CusText {
                        id: t_goods_kind
                        anchors.verticalCenter: parent.verticalCenter
                        text: goods_kind_name
                        x: 10 * dpi_ratio
                    }
                    CusText {
                        text: "请选择分类"
                        anchors.verticalCenter: parent.verticalCenter
                        x: 10 * dpi_ratio
                        visible: t_goods_kind.text == ""
                        color: ST.color_grey_font
                    }
                    CusRect {
                        width: 20 * dpi_ratio
                        height: width
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        anchors.rightMargin: 18 * dpi_ratio
                        color: ST.color_transparent
                        Image {
                            anchors.fill: parent
                            source: is_open_goods_kind_selector ? "/Images/left_grey.png" : "/Images/right_grey.png"
                        }
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            var tmp = !is_open_goods_kind_selector
                            create_goods.rect_tabbar.closeAllSelector()
                            edit_goods.rect_tabbar.closeAllSelector()
                            if (tmp)
                                is_open_goods_kind_selector = true

                            if (goods_kind_unique == "") {
                                goods_management.rect_goods_kind_chooser.refreshGoodsKind()
                            } else {
                                goods_management.rect_goods_kind_chooser.showGoodsKindAndFocus(goods_kind_unique)
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "商品品牌"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                            visible: false
                        }
                    }
                }
                CusTextField {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    text: goods_brand
                    placeholderText: "请输入商品品牌"
                    onTextEdited: {
                        goods_brand = text
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio
                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "供货商"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio

                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                            visible: false
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent
                    radius: ST.radius
                    border {
                        width: 1.5 * dpi_ratio
                        color: ST.color_grey
                    }
                    CusText {
                        id: t_supplier_kind
                        anchors.verticalCenter: parent.verticalCenter
                        text: supplier_name
                        x: 10 * dpi_ratio
                    }
                    CusText {
                        text: "请选择供货商"
                        anchors.verticalCenter: parent.verticalCenter
                        x: 10 * dpi_ratio
                        visible: t_supplier_kind.text == ""
                        color: ST.color_grey_font
                    }
                    CusRect {
                        width: 20 * dpi_ratio
                        height: width
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                        anchors.rightMargin: 18 * dpi_ratio
                        color: ST.color_transparent
                        Image {
                            anchors.fill: parent
                            source: is_open_supplier_selector ? "/Images/left_grey.png" : "/Images/right_grey.png"
                        }
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            var tmp = !is_open_supplier_selector
                            create_goods.rect_tabbar.closeAllSelector()
                            edit_goods.rect_tabbar.closeAllSelector()
                            if (tmp)
                                is_open_supplier_selector = true
                            goods_management.supplier_selector.refreshSupplierAndFocus(supplier_unique)
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 250 * dpi_ratio
                    color: ST.color_transparent
                    RowLayout {
                        anchors.fill: parent
                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: height
                            color: ST.color_transparent
                            Image {
                                anchors.centerIn: parent
                                width: 35 * dpi_ratio
                                height: 35 * dpi_ratio
                                source: "/Images/cash_register_icon.png"
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusText {
                                text: "收银机"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                    }
                }
                CusRadioManual {
                    Layout.preferredWidth: 130 * dpi_ratio
                    Layout.preferredHeight: 28 * dpi_ratio
                    Layout.alignment: Qt.AlignVCenter
                    id: rb_pc_shelf_state_true
                    text: "上架"
                    checked: pc_shelf_state == 1
                    onClicked: {
                        pc_shelf_state = 1
                    }
                }
                CusRadioManual {
                    Layout.preferredWidth: 130 * dpi_ratio
                    Layout.preferredHeight: 28 * dpi_ratio
                    Layout.alignment: Qt.AlignVCenter
                    id: rb_pc_shelf_state_false
                    text: "下架"
                    checked: pc_shelf_state == 2
                    onClicked: {
                        pc_shelf_state = 2
                    }
                }
                CusSpacer {
                    Layout.fillWidth: true
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 250 * dpi_ratio
                    color: ST.color_transparent
                    RowLayout {
                        anchors.fill: parent
                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: height
                            color: ST.color_transparent
                            Image {
                                anchors.centerIn: parent
                                width: 35 * dpi_ratio
                                height: 35 * dpi_ratio
                                source: "/Images/yikezhong_icon.png"
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusText {
                                text: "小程序"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                    }
                }
                CusRadioManual {
                    Layout.preferredWidth: 130 * dpi_ratio
                    Layout.preferredHeight: 28 * dpi_ratio
                    Layout.alignment: Qt.AlignVCenter
                    text: "上架"
                    checked: web_shelf_state == 1
                    onClicked: {
                        web_shelf_state = 1
                    }
                }
                CusRadioManual {
                    Layout.preferredWidth: 130 * dpi_ratio
                    Layout.preferredHeight: 28 * dpi_ratio
                    Layout.alignment: Qt.AlignVCenter
                    text: "下架"
                    checked: web_shelf_state == 2
                    onClicked: {
                        web_shelf_state = 2
                    }
                }
                CusSpacer {
                    Layout.fillWidth: true
                }
            }
        }

        CusSpacer {
            Layout.fillHeight: true
        }
    }
}
