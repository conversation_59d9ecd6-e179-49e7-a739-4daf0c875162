﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import "ContainCategoryDetail"

CusRect {
    id: category_detail

    function createGoodsKind() {
        is_create_parent_goods_kind = true
    }

    function setCurGoodsKind(category_id) {
        cancelGoodsKind()
        cur_goods_kind_unqiue = category_id
    }

    function cancelGoodsKind() {
        is_create_parent_goods_kind = false
        is_create_child_goods_kind = false
        is_editing_child_goods_kind = false
        is_edited_parent_goods_kind_name = false
    }
    function clearGoodsKind() {
        cur_goods_kind_unqiue = ""
        is_create_parent_goods_kind = false
        is_create_child_goods_kind = false
        is_editing_child_goods_kind = false
        is_edited_parent_goods_kind_name = false
        parent_goods_kind_name = ""
    }
    
    function cancelGoodsKindExpectCurGoodsKind() {
//        cur_goods_kind_unqiue = ""
        is_create_parent_goods_kind = false
        is_create_child_goods_kind = false
        is_editing_child_goods_kind = false
        is_edited_parent_goods_kind_name = false
//        parent_goods_kind_name = ""
    }

    property string cur_goods_kind_unqiue: ""
    property bool is_create_parent_goods_kind: false
    property bool is_create_child_goods_kind: false
    property bool is_editing_child_goods_kind: false
    property bool is_edited_parent_goods_kind_name: false
    property string editing_child_goods_kind_unique: ""
    property string editing_child_goods_kind_name: ""
    property alias parent_goods_kind_name: goods_kind_info.tf_parent_goods_kind_name

//    function setCreateGoodsKindName(goods_kind_name) {
//        is_create_child_goods_kind = true
//        editing_child_goods_kind_name = goods_kind_name
//    }

    onCur_goods_kind_unqiueChanged: {
        goods_kind_info.refreshCategoryInfo()
        //        category_info.goods_kind_name = ""
    }

    // 未选择
    CusRect {
        anchors.fill: parent
        visible: cur_goods_kind_unqiue == "" && !is_create_parent_goods_kind
        color: ST.color_white_pure

        CusRect {
            width: 180 * dpi_ratio
            height: 180 * dpi_ratio
            anchors.horizontalCenter: parent.horizontalCenter
            y: 240 * dpi_ratio
            color: ST.color_transparent

            Image {
                anchors.fill: parent
                source: "/Images/img_category.png"
            }
        }

        CusButton {
            width: 230 * dpi_ratio
            height: 70 * dpi_ratio
            anchors.horizontalCenter: parent.horizontalCenter
            y: 450 * dpi_ratio
            text: "新增分类"
            color: ST.color_green
            onClicked: {
                createGoodsKind()
            }
        }
    }

    //已选择或新建
    CategoryInfo {
        id: goods_kind_info
        anchors.fill: parent
        visible: cur_goods_kind_unqiue != "" || is_create_parent_goods_kind
    }
}
