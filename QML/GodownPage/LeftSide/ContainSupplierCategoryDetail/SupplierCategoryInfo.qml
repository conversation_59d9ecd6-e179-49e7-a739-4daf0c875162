﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import "../../.."

CusRect {
    color: ST.color_white_pure
    id: rect_goods_detail

    ColumnLayout {
        anchors.fill: parent
        spacing: 15 * dpi_ratio
        anchors.margins: 30 * dpi_ratio

        CusRect {
            Layout.fillWidth: true
            Layout.preferredHeight: 70 * dpi_ratio
            color: ST.color_transparent
            CusText {
                text: "分类详情"
                anchors.verticalCenter: parent.verticalCenter
                font {
                    pixelSize: 30 * dpi_ratio
                    bold: true
                }
            }
        }

        CusRect {
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            RowLayout {
                anchors.fill: parent
                spacing: 15 * dpi_ratio

                CusRect {
                    Layout.preferredWidth: 100 * dpi_ratio
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                    }
                    CusText {
                        text: is_create_supplier_category ? "新建分类" : "编辑分类"
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.right
                    }
                }

                CusTextField {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    text: cur_supplier_category_name
                    placeholderText: "请输入供应商分类名称"

                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio

                    digital_keyboard_x: 670 * dpi_ratio
                    digital_keyboard_y: 430 * dpi_ratio

                    onTextEdited: {
                        cur_supplier_category_name = text
                    }
                }
            }
        }

        CusSpacer {
            Layout.fillHeight: true
        }

        CusRect {
            Layout.preferredHeight: 100 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent
                spacing: 10 * ST.dpi_ratio

                CusButton {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredWidth: 200
                    text: "取消"
                    onClicked: {
                        cancelSupplierCategory()
                    }
                }

                CusButton {
                    visible: !is_create_supplier_category
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredWidth: 200
                    text: "删除"
                    color: ST.color_red
                    onClicked: {
                        showDelCurSupplierCategoryConfirm()
                    }
                }

                CusButton {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.preferredWidth: 300
                    text: is_create_supplier_category ? "新建供应商分类" : "保存供应商分类"
                    onClicked: {
                        if (is_create_supplier_category) {
                            reqCreateSupplierCategory()
                        } else {
                            reqChangeSupplierCategory()
                        }
                        keyboard_c.closeAll()
                    }
                }
            }
        }
    }

    function showDelCurSupplierCategoryConfirm() {
        window_root.compo_del_cur_supplier_categroy.sourceComponent = compo_del_cur_supplier_categroy
        window_root.compo_del_cur_supplier_categroy.item.open()
    }

    Component {
        id: compo_del_cur_supplier_categroy
        PopupConfirm {
            title_name: "删除供应商分类"
            message_info: "确定要删除当前供应商分类吗?"
            onConfirm: {
                supplierControl.reqDelSupplierCategory4Qml(function (is_succ, data) {
                    if (is_succ) {
                        toast.openInfo("删除供应商分类成功")
                        supplier_category_mamagement.refreshSupplierCategory()
                        cancelSupplierCategory()
                    } else {
                        toast.openWarn("删除供应商分类失败")
                    }
                }, cur_supplier_category_id)
            }
        }
    }
}
