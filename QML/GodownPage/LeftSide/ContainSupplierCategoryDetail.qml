﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import "ContainSupplierCategoryDetail"

CusRect {
    id: supplier_mgr_detail

    property string cur_supplier_category_id: ""
    property string cur_supplier_category_name: ""
    property bool is_create_supplier_category: false
    property bool is_editing_supplier_category: false

    function createSupplierCategory() {
        is_create_supplier_category = true
    }

    function setCurSupplierCategory(category_id, category_name) {
        cancelSupplierCategory()
        cur_supplier_category_id = category_id
        cur_supplier_category_name = category_name
    }

    function cancelSupplierCategory() {
        cur_supplier_category_id = ""
        cur_supplier_category_name = ""
        is_create_supplier_category = false
        is_editing_supplier_category = false
    }

    function resetInfo() {
        cur_supplier_category_id = ""
        cur_supplier_category_name = ""
    }

    function reqCreateSupplierCategory() {

        if (cur_supplier_category_name == "") {
            toast.openWarn("分类名信息有误")
            return
        }

        supplierControl.reqCreateSupplierCategory4Qml(function (is_succ, data) {
            if (is_succ) {
                toast.openInfo("创建成功")
                supplier_category_mamagement.refreshSupplierCategory()
                cancelSupplierCategory()
            } else {
                toast.openWarn("创建失败!")
            }
        }, cur_supplier_category_name)
    }

    function reqChangeSupplierCategory() {
        
        if (cur_supplier_category_name == "") {
            toast.openWarn("分类名信息有误")
            return
        }
        
        supplierControl.reqChangeSupplierCategory4Qml(function (is_succ, data) {
            if (is_succ) {
                toast.openInfo("修改成功")
                supplier_category_mamagement.refreshSupplierCategory()
                cancelSupplierCategory()
            } else {
                toast.openInfo("修改失败")
            }
        }, cur_supplier_category_id, cur_supplier_category_name)
    }

    CusRect {
        anchors.fill: parent
        visible: cur_supplier_category_id == "" && !is_create_supplier_category
        color: ST.color_white_pure

        CusRect {
            width: 180 * dpi_ratio
            height: 180 * dpi_ratio
            anchors.horizontalCenter: parent.horizontalCenter
            y: 240 * dpi_ratio
            color: ST.color_transparent

            Image {
                anchors.fill: parent
                source: "/Images/img_category.png"
            }
        }

        CusButton {
            width: 230 * dpi_ratio
            height: 70 * dpi_ratio
            anchors.horizontalCenter: parent.horizontalCenter
            y: 450 * dpi_ratio
            text: "新增供应商分类"
            color: ST.color_green
            onClicked: {
                createSupplierCategory()
            }
        }
    }
    SupplierCategoryInfo {
        anchors.fill: parent
        visible: cur_supplier_category_id != "" || is_create_supplier_category
    }
}
