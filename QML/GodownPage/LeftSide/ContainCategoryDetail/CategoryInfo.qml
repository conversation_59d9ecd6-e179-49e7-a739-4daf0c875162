﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../../.."
import SortFilterProxyModel 0.2

CusRect {
    color: ST.color_white_deeper
    id: rect_goods_detail

    property alias tf_parent_goods_kind_name: tf_parent_goods_kind_name.text

    function callbackAddRootGoodsKind(is_succ, data) {
        category_management.refreshGoodsKind()
        toast.openInfo("添加主分类成功")
    }
    function callbackChangeRootGoodsKind(is_succ, data) {
        category_management.refreshGoodsKind()
        toast.openInfo("更改主分类成功")
    }
    function callbackAddGoodsKind(is_succ, data) {
        if (is_succ) {
            pay_page.goods_tabbar.refreshGoodsKind()
            is_create_child_goods_kind = false
            goods_kind_info.refreshCategoryInfo()
            toast.openInfo("添加子分类成功")
        } else {
            toast.openInfo("添加子分类失败")
        }
    }
    function callbackDelGoodsKind(is_succ, data) {
        pay_page.goods_tabbar.refreshGoodsKind()
        toast.openInfo("删除子分类成功")
    }
    function callbackChangeGoodsKind(is_succ, data) {
        pay_page.goods_tabbar.refreshGoodsKind()
        is_editing_child_goods_kind = false
        goods_kind_info.refreshCategoryInfo()
        toast.openInfo("编辑子分类成功")
    }

    function saveGoodsKind() {
        if (is_create_child_goods_kind) {
            //新建子分类
            if (editing_child_goods_kind_name == "") {
                toast.openWarn("名字不能为空!")
                return
            }
            goodsControl.reqAddGoodsKind(callbackAddGoodsKind, cur_goods_kind_unqiue, editing_child_goods_kind_name)
        } else if (is_create_parent_goods_kind) {
            //新建父分类
            if (parent_goods_kind_name == "") {
                toast.openWarn("名字不能为空!")
                return
            }
            goodsControl.reqAddRootGoodsKind(callbackAddRootGoodsKind, parent_goods_kind_name)
        } else if (is_editing_child_goods_kind) {
            //编辑子分类
            if (editing_child_goods_kind_name == "") {
                toast.openWarn("名字不能为空!")
                return
            }
            goodsControl.reqChangeGoodsKindNameByKindUnique(callbackChangeGoodsKind, editing_child_goods_kind_unique, editing_child_goods_kind_name)
        } else if (is_edited_parent_goods_kind_name) {
            //编辑父分类
            if (parent_goods_kind_name == "") {
                toast.openWarn("名字不能为空!")
                return
            }
            goodsControl.reqChangeGoodsKindNameByKindUnique(callbackChangeRootGoodsKind, cur_goods_kind_unqiue, parent_goods_kind_name)
        }
    }

    function addNewChildGoodsKind() {
        editing_child_goods_kind_name = ""
        is_create_child_goods_kind = true
    }

    function refreshCategoryInfo() {
        lm_goods_kind_detail.clear()

        var goods_kind_info = JSON.parse(goodsKindManager.getGoodsKindByKindUnique4Qml(cur_goods_kind_unqiue))

        if (goods_kind_info == null) {
            //            cancelGoodsKind()
            return
        }

        parent_goods_kind_name = goods_kind_info.goods_kind_name

        var goods_kind_sub_info = JSON.parse(goodsKindManager.getSubGoodsKind4Qml(cur_goods_kind_unqiue))

        if (goods_kind_sub_info == null)
            return

        for (var i = 0; i < goods_kind_sub_info.length; ++i) {
            var cur_goods_kind = goods_kind_sub_info[i]
            cur_goods_kind["is_new_kind"] = false
            cur_goods_kind["timestamp"] = "0"
            cur_goods_kind["is_edited"] = false
            cur_goods_kind["edited_name"] = ""
            cur_goods_kind["is_deleted"] = false
            lm_goods_kind_detail.append(cur_goods_kind)
        }
    }

    function showDeleteParentConfirm(message_info, goods_kind_unique, goods_kind_name, is_back) {
        window_root.loader_4_category_confirm.sourceComponent = delete_confirm_compo
        window_root.loader_4_category_confirm.item.open("删除分类", message_info, goods_kind_unique, goods_kind_name, true)
    }

    function showDeleteChildConfirm(message_info, goods_kind_unique, goods_kind_name) {
        window_root.loader_4_category_confirm.sourceComponent = delete_confirm_child_compo
        window_root.loader_4_category_confirm.item.open("删除分类", message_info, goods_kind_unique, goods_kind_name)
    }

    Connections {
        target: mqttControl
        function onSigMqttAddGoodsKind4Qml(is_parent_goods_kind, goods_kind_unique) {
            if (!is_parent_goods_kind) {
                cancelGoodsKindExpectCurGoodsKind()
                refreshCategoryInfo()
            } else {
                cancelGoodsKind()
                category_management.refreshGoodsKind()
            }
        }
        function onSigMqttDelGoodsKind4Qml(is_parent_goods_kind, goods_kind_unique) {
            if (!is_parent_goods_kind) {
                cancelGoodsKindExpectCurGoodsKind()
                refreshCategoryInfo()
            } else {
                cancelGoodsKind()
                category_management.refreshGoodsKind()
            }
        }
        function onSigMqttRefreshGoodsKind4Qml(is_parent_goods_kind, goods_kind_unique) {
            if (!is_parent_goods_kind) {
                cancelGoodsKindExpectCurGoodsKind()
                refreshCategoryInfo()
            } else {
                cancelGoodsKind()
                category_management.refreshGoodsKind()
            }
        }
    }

    ListModel {
        id: lm_goods_kind_detail
    }
    SortFilterProxyModel {
        id: lm_goods_kind_detail_filter
        sourceModel: lm_goods_kind_detail
        filters: [
            ValueFilter {
                roleName: "is_deleted"
                enabled: true
                value: false
            }
        ]
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 15 * dpi_ratio
        anchors.bottomMargin: 30 * dpi_ratio

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_white_pure

            ColumnLayout {
                anchors.fill: parent
                anchors.topMargin: 30 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 180 * dpi_ratio
                    color: ST.color_transparent

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.leftMargin: 30 * dpi_ratio
                        anchors.rightMargin: 30 * dpi_ratio

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 70 * dpi_ratio
                            color: ST.color_transparent
                            CusText {
                                text: "分类详情"
                                anchors.verticalCenter: parent.verticalCenter
                                font {
                                    pixelSize: 30 * dpi_ratio
                                    bold: true
                                }
                            }
                        }
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            RowLayout {
                                anchors.fill: parent

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 120 * dpi_ratio
                                    color: ST.color_transparent
                                    CusText {
                                        text: "分类名称"
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                }
                                CusRect {
                                    Layout.preferredHeight: 60 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.alignment: Qt.AlignVCenter
                                    color: ST.color_transparent
                                    CusTextField {
                                        id: tf_parent_goods_kind_name
                                        anchors.fill: parent
                                        keyboard_status: 0
                                        normal_keyboard_x: 670 * dpi_ratio
                                        normal_keyboard_y: 560 * dpi_ratio
                                        digital_keyboard_x: 670 * dpi_ratio
                                        digital_keyboard_y: 430 * dpi_ratio
                                        onTextEdited: {
                                            is_edited_parent_goods_kind_name = true
                                        }
                                        enabled: !is_editing_child_goods_kind
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: height
                                    color: ST.color_transparent
                                    Image {
                                        anchors.centerIn: parent
                                        source: "/Images/delete_red.png"
                                        width: 44 * dpi_ratio
                                        height: width
                                    }
                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            goodsControl.reqGoodsCountByKindUnique(function (is_succ, data) {

                                                if (is_succ) {
                                                    var json_doc = JSON.parse(data)
                                                    showDeleteParentConfirm(json_doc.data, cur_goods_kind_unqiue, parent_goods_kind_name)
                                                } else {
                                                    toast.openWarn("失败")
                                                }
                                            }, cur_goods_kind_unqiue)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_deeper

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 20 * dpi_ratio
                        anchors.leftMargin: 30 * dpi_ratio
                        anchors.rightMargin: 30 * dpi_ratio
                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 60 * dpi_ratio
                            Layout.alignment: Qt.AlignTop
                            color: ST.color_transparent
                            visible: !is_create_parent_goods_kind
                            RowLayout {
                                anchors.fill: parent
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 100 * dpi_ratio
                                    color: ST.color_transparent
                                    CusText {
                                        text: "子分类"
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                }
                                CusSpacer {
                                    Layout.fillWidth: true
                                }
                                CusRect {
                                    Layout.preferredWidth: 160 * dpi_ratio
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    visible: !rect_save_goods_kind.visible

                                    CusText {
                                        text: "+ 新增子分类"
                                        anchors.verticalCenter: parent.verticalCenter
                                        color: ST.color_green
                                    }
                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            addNewChildGoodsKind()
                                        }
                                    }
                                }
                            }
                        }

                        CusRect {
                            id: rect_goods_kind_create
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            visible: is_create_child_goods_kind
                            color: ST.color_transparent
                            CusTextField {
                                back_rect.color: ST.color_white_pure
                                width: parent.width
                                height: 60 * dpi_ratio
                                text: editing_child_goods_kind_name
                                onTextEdited: {
                                    editing_child_goods_kind_name = text
                                }
                                keyboard_status: 0
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }

                        CusRect {
                            id: rect_goods_kind_edit
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            visible: is_editing_child_goods_kind
                            color: ST.color_transparent
                            CusTextField {
                                back_rect.color: ST.color_white_pure
                                width: parent.width
                                height: 60 * dpi_ratio
                                text: editing_child_goods_kind_name
                                onTextEdited: {
                                    editing_child_goods_kind_name = text
                                }
                                keyboard_status: 0
                                normal_keyboard_x: 670 * dpi_ratio
                                normal_keyboard_y: 560 * dpi_ratio
                                digital_keyboard_x: 670 * dpi_ratio
                                digital_keyboard_y: 430 * dpi_ratio
                            }
                        }

                        ListView {
                            id: lv_category_detail
                            visible: !is_create_parent_goods_kind && !is_editing_child_goods_kind && !is_create_child_goods_kind
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            clip: true
                            spacing: 20 * dpi_ratio
                            model: lm_goods_kind_detail_filter
                            delegate: CusRect {
                                width: lv_category_detail.width
                                height: 60 * dpi_ratio
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent

                                        CusTextField {
                                            anchors.fill: parent
                                            visible: !model.is_new_kind
                                            back_rect.color: ST.color_white_pure
                                            text: goods_kind_name

                                            onTextEdited: {
                                                model.is_edited = true
                                                edited_name = text
                                            }
                                            keyboard_status: 0
                                            normal_keyboard_x: 670 * dpi_ratio
                                            normal_keyboard_y: 560 * dpi_ratio
                                            digital_keyboard_x: 670 * dpi_ratio
                                            digital_keyboard_y: 430 * dpi_ratio
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                //                                                is_create_child_goods_kind = true
                                                is_editing_child_goods_kind = true
                                                editing_child_goods_kind_unique = goods_kind_unique
                                                editing_child_goods_kind_name = goods_kind_name
                                            }
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: height
                                        color: ST.color_transparent
                                        Image {
                                            anchors.centerIn: parent
                                            source: "/Images/delete_grey.png"
                                            width: 44 * dpi_ratio
                                            height: width
                                        }
                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                var model_timestam = model.timestamp
                                                if (model.is_new_kind) {
                                                    for (var i = 0; lm_goods_kind_detail.count; ++i) {
                                                        var cur_goods_kind = lm_goods_kind_detail.get(i)
                                                        if (!cur_goods_kind.is_new_kind)
                                                            continue

                                                        if (cur_goods_kind.timestamp == model_timestam) {
                                                            lm_goods_kind_detail.remove(i)
                                                            break
                                                        }
                                                    }
                                                } else {
                                                    goodsControl.reqGoodsCountByKindUnique(function (is_succ, data) {
                                                        var json_doc = JSON.parse(data)
                                                        showDeleteChildConfirm(json_doc.data, goods_kind_unique, goods_kind_name)
                                                    }, goods_kind_unique)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Component {
            id: delete_confirm_compo
            PopupConfirm {
                property string goods_kind_unique: ""
                property string goods_kind_name: ""
                property bool is_back: false
                function open(title_name_in, message_info_in, goods_kind_unique_in, goods_kind_name_in, is_back_in = false) {
                    title_name = title_name_in
                    message_info = message_info_in
                    goods_kind_unique = goods_kind_unique_in
                    goods_kind_name = goods_kind_name_in
                    is_back = is_back_in
                    visible = true
                }
                onConfirm: {
                    goodsControl.reqDelGoodsKindByKindUnique(function (is_succ, data) {
                        if (is_succ) {
                            toast.openInfo("删除" + goods_kind_name + "分类成功")
                            if (is_back) {
                                clearGoodsKind()
                            } else {
                                cancelGoodsKind()
                                category_management.refreshGoodsKind()
                            }
                        } else {
                            toast.openError("删除" + goods_kind_name + "分类失败")
                        }
                    }, goods_kind_unique)
                }
            }
        }

        Component {
            id: delete_confirm_child_compo
            PopupConfirm {
                property string goods_kind_unique: ""
                property string goods_kind_name: ""
                function open(title_name_in, message_info_in, goods_kind_unique_in, goods_kind_name_in) {
                    title_name = title_name_in
                    message_info = message_info_in
                    goods_kind_unique = goods_kind_unique_in
                    goods_kind_name = goods_kind_name_in
                    visible = true
                }
                onConfirm: {
                    goodsControl.reqDelGoodsKindByKindUnique(function (is_succ, data) {
                        if (is_succ) {
                            toast.openInfo("删除" + goods_kind_name + "分类成功")
                            //                            refreshCategoryInfo()
                        } else {
                            toast.openError("删除" + goods_kind_name + "分类失败")
                        }
                    }, goods_kind_unique)
                }
            }
        }

        CusRect {
            id: rect_save_goods_kind
            Layout.preferredHeight: 80 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            visible: is_create_parent_goods_kind || is_create_child_goods_kind || is_editing_child_goods_kind || is_edited_parent_goods_kind_name

            RowLayout {
                anchors.fill: parent
                anchors.leftMargin: 30 * dpi_ratio
                anchors.rightMargin: 30 * dpi_ratio

                CusButton {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    text: "取消"
                    onClicked: {
                        cancelGoodsKind()
                        keyboard_c.closeAll()
                    }
                }
                CusSpacer {
                    Layout.fillWidth: true
                }
                CusButton {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    text: "保存分类"
                    onClicked: {
                        saveGoodsKind()
                        keyboard_c.closeAll()
                    }
                }
            }
        }
        CusRect {
            Layout.preferredHeight: 80 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_transparent
            visible: !rect_save_goods_kind.visible

            RowLayout {
                anchors.fill: parent
                anchors.leftMargin: 30 * dpi_ratio
                anchors.rightMargin: 30 * dpi_ratio

                CusButton {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    text: "返回"
                    onClicked: {
                        clearGoodsKind()
                        keyboard_c.closeAll()
                    }
                }
            }
        }
    }
}
