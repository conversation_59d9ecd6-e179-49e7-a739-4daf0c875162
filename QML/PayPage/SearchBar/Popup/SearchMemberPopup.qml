﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."
import "../../../"
import EnumTool 1.0


Item {
    id: popup_root
    signal sigOpen
    signal sigClose(var is_member_ship)

    function open() {
        popup_root.visible = true;
        if (visible) {
            search_input.forceActiveFocus();
        }
        sigOpen()
    }
    function filterSymbols2(code)
    {
        var str = code;
        var str2 = "";
        for (var i = 0; i < str.length; i++)
        {
            if (str[i] <= '/' && str[i]> ' ' || str[i] <= '@' && str[i]>= ':' || str[i] <= '`' && str[i]>= '[' || str[i] <= '~' && str[i]>= '{'
               || str[i] == '！' || str[i] == '·' || str[i] == '（' || str[i] == '）' || str[i] == '；' || str[i] == '：' || str[i] == '——' || str[i] == '、'
               || str[i] == '‘' || str[i] == '“' || str[i] == '”' || str[i] == '，'|| str[i] == '。'|| str[i] == '《'|| str[i] == '》'|| str[i] == '？')
            {
                str2 += "";
            }

            else
            {
                str2 += str[i];
            }
        }
        return str2;
    }
    function addStrNum(str, str2) {
        if (str == "0" && str2 == "0")
            return "0"

        if (str2 == ".") {
            if (str.indexOf(".") == -1) {
                return str += str2
            }
            return str
        }

        if (str == "0")
            return str2

        str += str2

        let arr = str.split(".")

        var ret_str = arr[0]

        if (arr.length == 2) {
            ret_str = arr[0] + "." + arr[1].substring(0, 2)
        }

        return ret_str
    }
    function setInput(input) {
        if (search_input.activeFocus) {
               search_input.text = addStrNum(search_input.text, input);
           }
    }
    function close() {
        popup_root.visible = false;
        fuzzy_search_rec.visible = false;
        search_input.text = "";
        sigClose(memberSearchRec.is_member_ship);
        memberSearchRec.is_member_ship = false;
    }
    Rectangle {
        anchors.fill: parent
        //color: background_color
        //opacity: .5
        color: "black";
        opacity: 0.6;
        MouseArea {
            anchors.fill: parent
            onClicked: {
                if (popup_root.visible)
                    close()
            }
        }
    }
    Rectangle//会员搜索弹窗
    {
        id: memberSearchRec;
        width: 936  * dpi_ratio;
        height: 731  * dpi_ratio;
        anchors.left:parent.left;
        anchors.top:parent.top;
        anchors.leftMargin: 150 * dpi_ratio;
        anchors.topMargin: 100 * dpi_ratio;
        radius: 10  * dpi_ratio;
        enabled: memberSearchRec.visible;
        color: "#FFFFFF";
        z:6;
        property bool is_member_ship: false;
        onVisibleChanged: {
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {

            }
        }
        Rectangle
        {
            id:memberSearchRac
            width: 936  * dpi_ratio;
            height: 108  * dpi_ratio;
            anchors.top: parent.top;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            radius: 10  * dpi_ratio;
            Rectangle
            {
                id:memberSearchRac1
                width: 160  * dpi_ratio;
                height: 40  * dpi_ratio;
                anchors.horizontalCenter: parent.horizontalCenter;
                anchors.top:parent.top;
                anchors.topMargin: 30 * dpi_ratio;
                color: "#FFFFFF";
                radius: 10  * dpi_ratio;
                Text
                {
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 24  * dpi_ratio;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    color: "#333333";
                    text: qsTr("搜索会员");
                    font.bold: true ;
                }
            }
            Rectangle
            {
                id:memberSearchRac2
                width: 70  * dpi_ratio;
                height: 70  * dpi_ratio;
                anchors.top: parent.top;
                anchors.right: parent.right;
                color: "#FF5D40";
                radius: 10  * dpi_ratio;
                Text
                {
                    id: chuzhirootText;
                    anchors.centerIn: parent;
                    text: "X";
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 28 * dpi_ratio;
                    color: "#FFFFFF";
                }
                MouseArea
                {
                    anchors.fill: parent;

                    onPressed:
                    {
                        close();
                    }
                }
                Rectangle
                {
                    width: 10  * dpi_ratio;
                    height: 10  * dpi_ratio;
                    anchors.top: parent.top;
                    anchors.left: parent.left;
                    color: "#FF5D40";
                }
                Rectangle
                {
                    width: 10  * dpi_ratio;
                    height: 10  * dpi_ratio;
                    anchors.bottom: parent.bottom;
                    anchors.right: parent.right;
                    color: "#FF5D40";
                }
            }
        }
        Rectangle
        {
            id:searchRac;
            width:720  * dpi_ratio;
            height: 72  * dpi_ratio;
            anchors.top: memberSearchRac.bottom;
            anchors.topMargin: 30 * dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            border.width: 1  * dpi_ratio;
            border.color: "#00BD75";
            radius: 10  * dpi_ratio;
            Image
            {
                id: searchImage;
                width: 38  * dpi_ratio;
                height: 38  * dpi_ratio;
                anchors.verticalCenter: parent.verticalCenter;

                anchors.left: parent.left;
                anchors.leftMargin: 210  * dpi_ratio;
                source: "/Images/search_image.png";
                visible: search_input.text == "";
            }
            Text
            {
                id: memberNumber;
                width: 140  * dpi_ratio;
                height: 42  * dpi_ratio;
                anchors.bottom: searchImage.bottom;
                anchors.left: searchImage.right;
                font.family: ST.fontFamilyYaHei;
                font.pointSize: 24  * dpi_ratio;
                color:"#A8A8A8";
                text:qsTr("搜索会员手机号");
                visible: search_input.text == "";
            }

            TextInput
            {
                id: search_input;
                width: 490  * dpi_ratio;
                height: 42  * dpi_ratio;
                anchors.left: parent.left;
                anchors.verticalCenter: parent.verticalCenter;
                anchors.horizontalCenter: parent.horizontalCenter;
                font.family: ST.fontFamilyYaHei;
                font.pointSize: 24 * dpi_ratio;
                verticalAlignment: Text.AlignVCenter;
                color: "#333333";
                horizontalAlignment: Text.AlignHCenter
                maximumLength: 20  * dpi_ratio;
                clip: true;
                MouseArea
                {

                    anchors.fill: search_input;
                    onPressed:
                    {
                        search_input.focus = true;
                        search_input.text = "";

                    }
                }
                onAccepted:
                {
                }
                onTextChanged:
                {
                    if (search_input.text.indexOf("没有网络") !== -1 && search_input.text.length > 4 )
                    {
                        search_input.text = search_input.text.replace("没有网络","");
                    }
                    if(filterSymbols2(search_input.text).length > 0)
                    {
                        search_input.text =filterSymbols2(search_input.text);
                    }
                    else
                    {
                        search_input.text = "";
                    }

                    if (search_input.text.length > 1 && search_input.text.indexOf("没有网络") == -1)
                    {
                        memberControl.reqFuzzyMemberInfo4Qml(function (data) {
                            var json_doc = JSON.parse(data);
                            var json_doc_data = json_doc.data;
                            if((textListView.model.count) > 0){ //匹配当前搜索框数据与已获取数据不一致时，执行清空操作
                                if(search_input.text !== ((textListView.model.get(0)).cusPhone)){
                                    fuzzy_search_model.clear()
                                }
                            }
                            for (var i = 0; i < json_doc_data.length; ++i) {
                                var cur_data = json_doc_data[i]
                                fuzzy_search_model.append({
                                                     "cus_id": cur_data.hasOwnProperty("cus_id") ? cur_data.cus_id : "",
                                                     "cusUnique": cur_data.hasOwnProperty("cusUnique") ? cur_data.cusUnique : "",
                                                     "cusName": cur_data.hasOwnProperty("cusName") ? cur_data.cusName : "",
                                                     "cus_points": cur_data.hasOwnProperty("cus_points") ? cur_data.cus_points : "",
                                                     "cusBalance": cur_data.hasOwnProperty("cusBalance") ? cur_data.cusBalance : "",
                                                     "cusPhone": cur_data.hasOwnProperty("cusPhone") ? cur_data.cusPhone : ""
                                                 })
                                fuzzy_search_rec.visible = true;
                            }
                        },search_input.text,"1","2")
                    }
                    else if (search_input.text.length < 2 && search_input.text.indexOf("没有网络") == -1)
                    {
                        fuzzy_search_rec.visible = false;
                    }
                }
            }
        }
        Rectangle//模糊搜索
        {
            id:fuzzy_search_rec
            anchors.left:searchRac.left;
            anchors.top:searchRac.bottom;
            anchors.topMargin: 5 * dpi_ratio;
            visible: false;
            width: 720 * dpi_ratio;
            height: 165 * dpi_ratio;
            color:"#FFFFFF"
            border.width: 1  * dpi_ratio;
            border.color: "#D8D8D8";
            radius: 10  * dpi_ratio;
            z:100;
            ListView
            {
                id:textListView
                anchors.top: fuzzy_search_rec.top;
                anchors.topMargin: 5 * dpi_ratio;
                anchors.horizontalCenter:fuzzy_search_rec.horizontalCenter;
                visible: fuzzy_search_rec.visible;
                width: 716 * dpi_ratio;
                clip:true
                height: 140 * dpi_ratio;

                model:fuzzy_search_model
                delegate: Rectangle
                {
                    id: textListViewDelegate;
                    width:  716 * dpi_ratio;
                    height: 50 * dpi_ratio;
                    color:"#FFFFFF"
                    anchors.horizontalCenter:textListView.horizontalCenter;
                    Rectangle
                    {
                        id:textListViewDelegate1
                        anchors.left:textListViewDelegate.left
                        anchors.verticalCenter: textListViewDelegate.verticalCenter
                        width:  236 * dpi_ratio;
                        height: 50 * dpi_ratio;
                        Text
                        {
                            id:textListViewDelegateText1
                            anchors.centerIn: parent
                            font.family: ST.fontFamilyYaHei;
                            font.pointSize: 22 * dpi_ratio;
                            color: "#333333";
                            text: cusUnique.length >10? (cusUnique.substring(0,10) + "..."):cusUnique;
                        }
                    }
                    Rectangle
                    {
                        id:textListViewDelegate2
                        anchors.left:textListViewDelegate1.right
                        anchors.verticalCenter: textListViewDelegate.verticalCenter
                        width: 236 * dpi_ratio;
                        height: 50 * dpi_ratio;
                        Text
                        {
                            id:textListViewDelegateText2
                            anchors.centerIn: parent
                            font.family: ST.fontFamilyYaHei;
                            font.pointSize: 22 * dpi_ratio;
                            color: "#333333";
                            text: cusName
                        }
                    }
                    Rectangle
                    {
                        id:textListViewDelegate3
                        anchors.left:textListViewDelegate2.right
                        anchors.verticalCenter: textListViewDelegate.verticalCenter
                        width: 236 * dpi_ratio;
                        height: 50 * dpi_ratio;
                        Text
                        {
                            id:textListViewDelegateText3
                            anchors.centerIn: parent
                            font.family: ST.fontFamilyYaHei;
                            font.pointSize: 22 * dpi_ratio;
                            color: "#333333";
                            text: cusPhone
                        }
                    }
                    Rectangle
                    {
                        id: textListViewLine;
                        width:  718 * dpi_ratio;
                        height: 1;
                        anchors.bottom:textListViewDelegate.bottom;
                        anchors.horizontalCenter: textListViewDelegate.horizontalCenter;
                        color:"#D8D8D8";
                    }
                    MouseArea
                    {
                        anchors.fill:textListViewDelegate;
                        onClicked:
                        {
                            textListViewDelegateText1.color="red"
                            textListViewDelegateText2.color="red"
                            textListViewDelegateText3.color="red"
                            fuzzy_search_timer.start()
                            memberControl.reqMemberCusByIdForQml(function (is_succ, data) {
                                var json_doc = JSON.parse(data);
                                var json_doc_data = json_doc.data
                                if(is_succ){
                                    if(json_doc.status == 1){
                                        if (!(json_doc.cus_status == ""))
                                        {
                                            var value = json_doc.cus_status;
                                            if (value == "0")
                                            {

                                            }
                                        }
                                        if (!(json_doc_data.cusPoints == ""))
                                        {
                                            cur_using_member_points = json_doc_data.cusPoints;
                                            logMgr.logEvtInfo4Qml("积分：{}",cur_using_member_points)
                                        }
                                        if (!(json_doc_data.cus_balance == ""))
                                        {logMgr.logEvtInfo4Qml("赠送余额：{}",json_doc_data.cusRebate)
                                            cur_using_member_balance = json_doc_data.cus_balance*1 + json_doc_data.cusRebate*1;
                                            logMgr.logEvtInfo4Qml("余额：{}",cur_using_member_balance)
                                        }
                                        if (!(json_doc_data.cusPhone == ""))
                                        {
                                            cur_using_member_phone = json_doc_data.cusPhone;
                                            logMgr.logEvtInfo4Qml("手机号：{}",cur_using_member_phone)
                                        }
                                        if (!(json_doc_data.cusName == ""))
                                        {
                                            cur_using_member_name = json_doc_data.cusName;
                                            logMgr.logEvtInfo4Qml("会员名称：{}",cur_using_member_name)
                                        }
                                        if (!(json_doc_data.cusUnique == ""))
                                        {
                                            cur_using_member_unique = json_doc_data.cusUnique;
                                            logMgr.logEvtInfo4Qml("卡号：{}",cur_using_member_unique)
                                        }
                                        shopCartList.setCurMemberUnique(cur_using_member_unique, cur_using_member_name, cur_using_member_balance, cur_using_member_points);                
                                        fuzzy_search_rec.visible = false;
                                        cur_editing_member_unique = cur_using_member_unique;
                                        close();
                                }
                                }
                            }, cusUnique,"")
                        }
                    }
                    Timer
                    {
                        id: fuzzy_search_timer;
                        interval: 100;
                        repeat: false;
                        running: false;
                        onTriggered:
                        {
                            textListViewDelegate1.color="#ffffff"
                            textListViewDelegate2.color="#ffffff"
                            textListViewDelegate3.color="#ffffff"
                            textListViewDelegateText1.color="#333333"
                            textListViewDelegateText2.color="#333333"
                            textListViewDelegateText3.color="#333333"
                        }
                    }
                }
                ListModel
                {
                    id:fuzzy_search_model
                }
            }
        }
        Rectangle
        {
            id:memberManageRac;
            width: 936  * dpi_ratio;
            height: 150  * dpi_ratio;
            anchors.top: searchRac.bottom;
            anchors.topMargin: 30 * dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            border.width: 1  * dpi_ratio;
            border.color: "#FFFFFF";
            radius: 10  * dpi_ratio;
            Rectangle
            {
                id:memberAdd;
                width: 468  * dpi_ratio;
                height: 120  * dpi_ratio;
                anchors.left: parent.left;
                anchors.top: parent.top;
                Image
                {
                    id: memberAddImage;
                    width: 72  * dpi_ratio;
                    height: 72  * dpi_ratio;
                    anchors.top: parent.top;
                    anchors.left: parent.left;
                    anchors.leftMargin: 306  * dpi_ratio;
                    source: "/Images/memberAdd.png";
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            //member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__NEW_MEMBER;
                            window_root.compo_index = EnumTool.PAGE_MEMBERSHIP;
                            window_root.memberOperation = 1;
                            close();
                        }
                    }
                }
                Text
                {
                    id: memberAddImageText;
                    width: 72  * dpi_ratio;
                    height: 36  * dpi_ratio;
                    anchors.top: memberAddImage.bottom;
                    anchors.topMargin: 5 * dpi_ratio;
                    anchors.left: memberAddImage.left;
                    anchors.leftMargin:-5  * dpi_ratio;
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 16  * dpi_ratio;
                    color: "#333333"
                    text:qsTr("新增");
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            //member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__NEW_MEMBER;
                            window_root.compo_index = EnumTool.PAGE_MEMBERSHIP;
                            window_root.memberOperation = 1;
                            close();
                        }
                    }
                }

            }
            Rectangle
            {
                id:memberManage;
                width: 468  * dpi_ratio;
                height: 120  * dpi_ratio;
                anchors.left: memberAdd.right;
                anchors.top: memberAdd.top;
                color:"#FFFFFF"
                Image
                {
                    id: memberManageImage;
                    width: 72  * dpi_ratio;
                    height: 72  * dpi_ratio;
                    anchors.top: parent.top;
                    anchors.right: parent.right;
                    anchors.rightMargin: 306  * dpi_ratio;
                    source: "/Images/memberManger.png";
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
//                            if(cur_using_member_unique !== ""){
                                //member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER;
                            window_root.compo_index = EnumTool.PAGE_MEMBERSHIP;
                            window_root.memberOperation = 2;
                            close();
//                            }
//                            else
//                            {
//                              toast.openWarn("请先选择会员!")
//                            }
                        }
                    }
                }
                Text
                {
                    id: memberManageImageText;
                    width: 72  * dpi_ratio;
                    height: 36  * dpi_ratio;
                    anchors.top: memberManageImage.bottom;
                    anchors.topMargin: 5 * dpi_ratio;
                    anchors.left: memberManageImage.left;
                    anchors.leftMargin:-5  * dpi_ratio;
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 16  * dpi_ratio;
                    color: "#333333"
                    text:qsTr("会员管理");
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
//                            if(cur_using_member_unique !== ""){
                            //member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER;
                            window_root.compo_index = EnumTool.PAGE_MEMBERSHIP;
                            window_root.memberOperation = 2;
                            close();
//                            }
//                            else
//                            {
//                              toast.openWarn("请先选择会员!")
//                            }
                        }
                    }
                }
            }
    }
        Rectangle
        {
            id: keyboardDigital;
            width: 720*dpi_ratio;
            height: 316*dpi_ratio;
            color: "#FFFFFF";
            visible: true;
            anchors.bottom: parent.bottom;
            anchors.bottomMargin: 20 * dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            radius: 10 *dpi_ratio;

            RowLayout {
                anchors.fill: parent

                CusRect {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    GridLayout {
                        id: gl_calc_btns

                        anchors.fill: parent
                        columns: 4
                        rows: 3

                        columnSpacing: 5 * dpi_ratio
                        rowSpacing: 5 * dpi_ratio

                        property real col_width: (width - ((columns - 1) * rowSpacing)) / columns
                        property real col_height: (height - ((rows - 1) * columnSpacing)) / rows

                        function prefWidth(item) {
                            return col_width * item.Layout.columnSpan
                        }
                        function prefHeight(item) {
                            return col_height * item.Layout.rowSpan
                        }

                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "7"


                        }

                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "8"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "9"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "0"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "4"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "5"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "6"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "."
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "1"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "2"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "3"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: qsTr("清空")
                            onPressKey: {
                                if (search_input.activeFocus) {
                                       search_input.text = ""
                                   }
                            }
                        }
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 130 * dpi_ratio
                    color: ST.color_transparent

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 10 * dpi_ratio

                        property real cell_height: gl_calc_btns.col_height

                        KeyRect2 {
                            Layout.preferredHeight: parent.cell_height
                            Layout.fillWidth: true
                            key_text: ""
                            color: "#FFFFFF"
                            text.color: "#FFFFFF"
                            Image {
                                anchors.fill: parent
                                source: "/Images/tuige.png"
                                fillMode: Image.PreserveAspectFit
                                scale: 0.5
                            }
                            onPressKey: {
                                if (search_input.activeFocus) {
                                       search_input.text = search_input.text.slice(0, -1);
                                   }
                            }
                        }
                        KeyRect2 {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            key_text: qsTr("确认")
                            color: "#00BD75"
                            text.color: "white"
                            onPressKey: {
                                if (totalPayment === "0.00") {
                                    toast.openWarn(qsTr("会员手机号不可为空!"))
                                    return
                                }
                            }
                        }
                        component KeyRect: CusRect {
                            id: rect_key
                            property string key_text: "0"
                            signal pressKey(var key)
                            color: ST.color_transparent
                            radius: 10 *dpi_ratio;

                            border {
                                width: 2 * dpi_ratio
                                color: "#E5E5E5"
                            }

                            CusText {
                                id:rect_key_id
                                anchors.centerIn: parent
                                text: rect_key.key_text
                                font.weight: Font.Bold
                                font.pixelSize: 28 * dpi_ratio
                            }
                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    rect_key.color = "#17262B";
                                    rect_key_id.color = "white";
                                    clickAmation.start();
                                    rect_key.pressKey(rect_key.key_text)
                                }
                            }
                            Timer
                            {
                                id: clickAmation;
                                running: false;
                                repeat: false;
                                interval: 100
                                onTriggered:
                                {
                                    rect_key.color = "white";
                                    rect_key_id.color = "black";
                                }
                            }
                            onPressKey: {
                                setInput(key)
                            }
                        }
                        component KeyRect2: CusRect {
                            id: rect_key2
                            property string key_text: "0"
                            signal pressKey(var key)

                            property alias text: text
                            color: ST.color_transparent
                            radius: 10 *dpi_ratio;

                            border {
                                width: 2 * dpi_ratio
                                color: "#E5E5E5"
                            }
                            CusText {
                                id: text
                                anchors.centerIn: parent
                                text: rect_key2.key_text
                            }
                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    rect_key2.color = "#17262B";
                                    text.color = "white";
                                    clickAmation2.start();
                                    rect_key2.pressKey(rect_key2.key_text)
                                    if(rect_key2.key_text == qsTr("确认")){
                                    }
                                    }
                                }                            
                            Timer
                            {
                                id: clickAmation2;
                                running: false;
                                repeat: false;
                                interval: 100
                                onTriggered:
                                {
                                    if(rect_key2.key_text == qsTr("确认")){
                                        rect_key2.color = "#00BD75";
                                        text.color = "white";
                                    }
                                    else{
                                        rect_key2.color = "white";
                                        text.color = "black";
                                    }

                                }
                            }
                        }

                    }
                }
            }
        }
    }
}
