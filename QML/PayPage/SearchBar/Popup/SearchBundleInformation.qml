﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import EnumTool 1.0
import "../../../Utils2"



Item {
    id: popup_bundle_root
    signal sigOpen
    signal sigClose(var isBundle,var shopCartlists)
    property string isBundle: "0"
    property string  shopCartlists: ""
    function open(serachData) {

        cuxiao_root.visible = true
        goodsPromotionCtrl.pcSaleGoodsSearchForQml(serachData);
        logMgr.logEvtInfo4Qml("enter open!!!!!!:{}",serachData)
    }
    onVisibleChanged: {
        if(visible){
            logMgr.logEvtInfo4Qml("black_shadow.visible = true")
            black_shadow.visible = true
            cuxiao_rec.visible = true
        }
    }
    function close() {
        cuxiao_rec.visible = false;
        cuxiao_root.visible = false;
        black_shadow.visible = false;
        sigClose(isBundle,shopCartlists)
        isBundle = "0"
        shopCartlists = ""
        logMgr.logEvtInfo4Qml("black_shadow.visible = false")
    }
    Rectangle
    {
        id: black_shadow;
        anchors.fill: parent;
        color: "black";
        opacity: 0.6;
        visible: false;
        z: 3;
        MouseArea
        {
            anchors.fill: parent;
            onPressed:
            {
                shopCartlists = "0";
                close();
                }
            }
    }
    Rectangle
    {
        id: cuxiao_root;
        width: 900* dpi_ratio;
        height: 600* dpi_ratio;
        color: "transparent";
        anchors.centerIn: parent;
        border.width: 1* dpi_ratio;
        border.color: "transparent";
        visible: false;
        z: 4;
        Rectangle
        {
            id: cuxiao_rec;
            width: 900* dpi_ratio;
            height: 600* dpi_ratio;
            color: "#ffffff";
            anchors.centerIn: parent;
            border.width: 1* dpi_ratio;
            border.color: "#d0d0d0";
            visible: false;
            z: 4;

            Rectangle
            {
                id: return_rec;
                width: 150* dpi_ratio;
                height: 60* dpi_ratio;
                color: "#2982b9";
                anchors.right: parent.right;
                anchors.rightMargin: 10 * dpi_ratio
                anchors.bottomMargin: 10 * dpi_ratio
                anchors.bottom: parent.bottom;

                Text
                {
                    anchors.centerIn: parent;
                    font.family: "微软雅黑";
                    font.pointSize: 18* dpi_ratio;
                    text: "确定";
                    color: "#ffffff";
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onPressed:
                    {
                        black_shadow.visible = false;
                        cuxiao_rec.visible = false;
                        var str = ""
                        for (var l = 0; l < listmodel_cuxiao2.count; l++)
                        {
                            str += listmodel_cuxiao2.get(l).goodsId2;
                            str += "^";
                            str += listmodel_cuxiao2.get(l).goodsName2;
                            str += "^";
                            str += listmodel_cuxiao2.get(l).goodsSalePrice2;
                            str += "^";
                            str += "0";
                            str += "^";
                            str += listmodel_cuxiao2.get(l).goodsCount2;
                            str += "^";
                            str += "";
                            str += "^";
                            str += listmodel_cuxiao2.get(l).goodsBarcode2;
                            str += "^";
                            str += listmodel_cuxiao2.get(l).goodsInPrice2;
                            str += "^";
                            str += listmodel_cuxiao2.get(l).goodsBindingUnique2;
                            str += "^";
                            str += "1";
                            str += "^";
                            str += listmodel_cuxiao2.get(l).goodsSalePrice2;
                            str += "^";
                            str += (listmodel_cuxiao2.get(l).goodsSalePrice2*listmodel_cuxiao2.get(l).goodsCount2*1).toFixed(2);
                            str += "^";
                            str += listmodel_cuxiao2.get(l).goodsSalePrice2;
                            str += ";";
//                            refreshGoodsComponent(str);
                            //                        console.debug("@@@@@@@@@选中@@@@@@@@", str);
                        }
                        isBundle = "1"
                        shopCartlists = str
                        str = ""
                        logMgr.logDataInfo4Qml("----------:{}",shopCartlists)
                        close()
                    }
                }
            }

            Rectangle
            {
                id: confirm_rec;
                width: 150* dpi_ratio;
                height: 60* dpi_ratio;
                color: "#2982b9";
                anchors.left: goods_item_rec.left;
                anchors.bottom: parent.bottom;
                anchors.rightMargin: 10 * dpi_ratio
                anchors.bottomMargin: 10 * dpi_ratio

                Text
                {
                    anchors.centerIn: parent;
                    font.family: "微软雅黑";
                    font.pointSize: 18* dpi_ratio;
                    text: "返回";
                    color: "#ffffff";
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onPressed:
                    {
                        shopCartlists = "0";
                        close();
                    }
                }
            }

            Rectangle
            {
                id: id_rec;
                width: 300* dpi_ratio;
                height: 35* dpi_ratio;
                color: "#ffffff";
                border.color: "#d0d0d0";
                border.width: 1* dpi_ratio;
                anchors.left: parent.left;
                anchors.top: parent.top;

                Text
                {
                    id: id_text
                    anchors.centerIn: parent;
                    font.family: "微软雅黑";
                    font.pointSize: 16* dpi_ratio;
                    text: "id";
                    color: "#333333";
                }
            }

            Rectangle
            {
                id: goods_item_rec;
                width: 600* dpi_ratio;
                height: 35* dpi_ratio;
                color: "#ffffff";
                border.color: "#d0d0d0";
                border.width: 1* dpi_ratio;
                anchors.left: id_rec.right;
                anchors.leftMargin: -1* dpi_ratio;
                anchors.top: parent.top;

                Text
                {
                    id: goods_item_text
                    anchors.centerIn: parent;
                    font.family: "微软雅黑";
                    font.pointSize: 16* dpi_ratio;
                    text: "商品条目";
                    color: "#333333";
                }
            }

            Rectangle
            {
                id: listview_cuxiao_rec;
                width: 300* dpi_ratio;
                height: 565* dpi_ratio;
                anchors.left: parent.left;
                anchors.top: id_rec.bottom;
                border.width: 1* dpi_ratio;
                border.color: "#d0d0d0";
                anchors.topMargin: -1* dpi_ratio;

                ListView
                {
                    id: listview_cuxiao;
                    width: 300* dpi_ratio;
                    height: 565* dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 1* dpi_ratio;
                    anchors.top: parent.top;
                    anchors.topMargin: 1* dpi_ratio;
                    orientation: ListView.Vertical;
                    boundsBehavior: Flickable.OvershootBounds;
                    highlightRangeMode: ListView.ApplyRange;
                    z: 1;
                    spacing: 2;
                    delegate: Rectangle {
                        id:unit3;
                        property string idColumn: id;
                        width: 298* dpi_ratio;
                        height: 35* dpi_ratio;
                        Rectangle
                        {
                            id: idColumn_rec;
                            width: 298* dpi_ratio;
                            height: 34* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 0* dpi_ratio;
                            anchors.top: parent.top;
                            color: colorRec;
                            Text
                            {
                                id: idColumn_text;
                                anchors.centerIn: parent;
                                font.family: "微软雅黑";
                                font.pointSize: 10* dpi_ratio;
                                text: idColumn;
                                color: "#333333";
                            }
                        }
                        Rectangle
                        {
                            id: line7;
                            width: 298* dpi_ratio;
                            height: 1* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 0* dpi_ratio;
                            anchors.top: idColumn_rec.bottom;
                            color: "#d0d0d0";
                        }
                        MouseArea
                        {
                            anchors.fill: parent;
                            onPressed:
                            {
                                var j = 0;
                                for (var i = 0; i < listmodel_cuxiao.count; i++)
                                {
                                    //                                console.debug("@@@@@@@@@@", index)
                                    if (index == i)
                                    {
                                        j = i;
                                    }

                                    listmodel_cuxiao.set(i, {colorRec: "#f2f2f2"});
                                }
                                listmodel_cuxiao.set(j, {colorRec: "#f4f472"});

                                listmodel_cuxiao2.clear();
                                for (var l = 0, m = 0; l < listmodel_cuxiao_all.count; l++)
                                {
                                    if (listmodel_cuxiao_all.get(l).goodsBindingUnique === listmodel_cuxiao.get(j).id)
                                    {
                                        listmodel_cuxiao2.insert(m, {goodsBindingUnique2:listmodel_cuxiao_all.get(l).goodsBindingUnique,
                                                                     goodsId2: listmodel_cuxiao_all.get(l).goodsId,
                                                                     goodsName2: listmodel_cuxiao_all.get(l).goodsName,
                                                                     goodsBarcode2: listmodel_cuxiao_all.get(l).goodsBarcode,
                                                                     goodsSalePrice2: listmodel_cuxiao_all.get(l).goodsSalePrice,
                                                                     goodsCount2: listmodel_cuxiao_all.get(l).goodsCount,
                                                                     goodsDisCount2: listmodel_cuxiao_all.get(l).goodsDisCount,
                                                                     goodsInPrice2: listmodel_cuxiao_all.get(l).goodsInPrice});
                                        m++;
                                    }
                                }
                            }
                        }
                    }
                    model: ListModel
                    {
                        id: listmodel_cuxiao;
                    }
                }
            }

            Rectangle
            {
                id: listview_cuxiao_rec2;
                width: 600* dpi_ratio;
                height: 450* dpi_ratio;
                anchors.left: goods_item_rec.left;
                anchors.leftMargin: 1* dpi_ratio;
                anchors.top: goods_item_rec.bottom;
                color: "#ffffff";

                ListView
                {
                    id: listview_cuxiao2;
                    width: 600* dpi_ratio;
                    height: 350* dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 1* dpi_ratio;
                    anchors.top: parent.top;
                    anchors.topMargin: 1* dpi_ratio;
                    orientation: ListView.Vertical;
                    boundsBehavior: Flickable.OvershootBounds;
                    highlightRangeMode: ListView.ApplyRange;
                    spacing: 2;
                    z: 1;
                    delegate: Rectangle {
                        id:unit4;
                        width: 598* dpi_ratio;
                        height: 35* dpi_ratio;
                        Rectangle
                        {
                            id: bangdinghao_rec;
                            width: 199* dpi_ratio;
                            height: 33* dpi_ratio;
                            anchors.left: parent.left;
                            anchors.leftMargin: 1* dpi_ratio;
                            anchors.top: parent.top;
                            anchors.topMargin: 1* dpi_ratio;
                            color: "#f4f472";
                            Text
                            {
                                id: bangdinghao_text;
                                anchors.centerIn: parent;
                                font.family: "微软雅黑";
                                font.pointSize: 10* dpi_ratio;
                                text: goodsBindingUnique2;
                                color: "#333333";
                            }
                        }
                        Rectangle
                        {
                            id: line31;
                            width: 1* dpi_ratio;
                            height: 33* dpi_ratio;
                            anchors.left: bangdinghao_rec.right;
                            anchors.top: parent.top;
                            anchors.topMargin: 1* dpi_ratio;
                            color: "#d0d0d0";
                        }

                        Rectangle
                        {
                            id: shangpinming_rec;
                            width: 199* dpi_ratio;
                            height: 33* dpi_ratio;
                            anchors.left: line31.right;
                            anchors.top: parent.top;
                            anchors.topMargin: 1* dpi_ratio;
                            color: "#f4f472";
                            Text
                            {
                                id: shangpinming_text;
                                anchors.centerIn: parent;
                                font.family: "微软雅黑";
                                font.pointSize: 10* dpi_ratio;
                                text: goodsName2;
                                color: "#333333";
                            }
                        }

                        Rectangle
                        {
                            id: line32;
                            width: 1* dpi_ratio;
                            height: 33* dpi_ratio;
                            anchors.left: shangpinming_rec.right;
                            anchors.top: parent.top;
                            anchors.topMargin: 1* dpi_ratio;
                            color: "#d0d0d0";
                        }

                        Rectangle
                        {
                            id: shangpintiaoma_rec;
                            width: 199* dpi_ratio;
                            height: 33* dpi_ratio;
                            anchors.left: line32.right;
                            anchors.top: parent.top;
                            anchors.topMargin: 1* dpi_ratio;
                            color: "#f4f472";
                            Text
                            {
                                id: shangpintiaoma_text;
                                anchors.centerIn: parent;
                                font.family: "微软雅黑";
                                font.pointSize: 10* dpi_ratio;
                                text: goodsBarcode2;
                                color: "#333333";
                            }
                        }
                    }
                    model: ListModel
                    {
                        id: listmodel_cuxiao2;
                    }
                }
            }
        }
}
    ListModel
    {
        id: listmodel_cuxiao_all;
    }

    Connections
    {
        target: goodsPromotionCtrl
        function onSendBindingStr(goodsStr, unique){
            if(goodsStr === ""){
                close()
            }
            else
            {
                cuxiao_rec.visible = true;
                black_shadow.visible = true;
                var uniqueListString = unique;
                listmodel_cuxiao.clear();
                if (uniqueListString.length > 0)
                {
                    var uniqueListArray = uniqueListString.split("^");
                    for(var i = 0; i < uniqueListArray.length; i++)
                    {
                        listmodel_cuxiao.insert(i, {id:uniqueListArray[i],
                                                    colorRec: "#f2f2f2"});
                    }
                    listmodel_cuxiao.set(0, {colorRec: "#f4f472"});
                    cuxiao_rec.visible = true;
                    black_shadow.visible = true;
                    listview_cuxiao.positionViewAtBeginning();
                }

                var uniqueListString2 = goodsStr;
                listmodel_cuxiao_all.clear();
                if (uniqueListString2.length > 0)
                {
                    var k = 0;
                    var uniqueListArray2 = uniqueListString2.split("#");
                    for(var i = 0; i < uniqueListArray2.length; i++)
                    {
                        var goodsListArray = uniqueListArray2[i].split(";");
                        for (var j = 0; j < goodsListArray.length; j++)
                        {
                            var goodsListArray3 = goodsListArray[j].split("^");
                            listmodel_cuxiao_all.insert(k, {goodsBindingUnique:goodsListArray3[0],
                                                            goodsId:goodsListArray3[1],
                                                            goodsName: goodsListArray3[2],
                                                            goodsBarcode: goodsListArray3[3],
                                                            goodsSalePrice: goodsListArray3[4],
                                                            goodsCount: goodsListArray3[5],
                                                            goodsDisCount: goodsListArray3[6],
                                                            goodsInPrice: goodsListArray3[7]});
                            //                        console.debug("@@@@@@@@@@@@", listmodel_cuxiao_all[k].goodsName)
                            k++;
                        }
                    }
                }

                listmodel_cuxiao2.clear();
                for (var l = 0, m = 0; l < listmodel_cuxiao_all.count; l++)
                {
                    if (listmodel_cuxiao_all.get(l).goodsBindingUnique === listmodel_cuxiao.get(0).id)
                    {
                        listmodel_cuxiao2.insert(m, {goodsBindingUnique2:listmodel_cuxiao_all.get(l).goodsBindingUnique,
                                                     goodsId2: listmodel_cuxiao_all.get(l).goodsId,
                                                     goodsName2: listmodel_cuxiao_all.get(l).goodsName,
                                                     goodsBarcode2: listmodel_cuxiao_all.get(l).goodsBarcode,
                                                     goodsSalePrice2: listmodel_cuxiao_all.get(l).goodsSalePrice,
                                                     goodsCount2: listmodel_cuxiao_all.get(l).goodsCount,
                                                     goodsDisCount2: listmodel_cuxiao_all.get(l).goodsDisCount,
                                                     goodsInPrice2: listmodel_cuxiao_all.get(l).goodsInPrice});
                        m++;
                    }
                }

                if (uniqueListArray.length === 1)
                {
                    black_shadow.visible = false;
                    enableBtn();
                    cuxiao_rec.visible = false;
                    for (var l = 0; l < listmodel_cuxiao2.count; l++)
                    {
                        var str = listmodel_cuxiao2.get(l).goodsId2;
                        str += "^";
                        str += listmodel_cuxiao2.get(l).goodsName2;
                        str += "^";
                        str += listmodel_cuxiao2.get(l).goodsSalePrice2;
                        str += "^";
                        str += "0";
                        str += "^";
                        str += listmodel_cuxiao2.get(l).goodsCount2;
                        str += "^";
                        str += "";
                        str += "^";
                        str += listmodel_cuxiao2.get(l).goodsBarcode2;
                        str += "^";
                        str += listmodel_cuxiao2.get(l).goodsInPrice2;
                        str += "^";
                        str += listmodel_cuxiao2.get(l).goodsBindingUnique2;
                        str += "^";
                        str += "1";
                        str += "^";
                        str += listmodel_cuxiao2.get(l).goodsSalePrice2;
                        str += "^";
                        str += listmodel_cuxiao2.get(l).goodsSalePrice2;
                        str += "^";
                        str += listmodel_cuxiao2.get(l).goodsSalePrice2;
                        str += "^";
                        str += "0";
                        str += "^";
                        str += "0";
                        str += "^";
                        str += "0";
                        //                    console.debug("@@@@@@@@@选中@@@@@@@@", str);
                        refreshGoodsComponent(str);
                    }
                    code_input.focus = true;
                }
        }
        }
    }
}
