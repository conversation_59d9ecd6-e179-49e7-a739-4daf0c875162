﻿import QtQuick 2.12
import QtQuick.Layouts 1.12
import QtQuick.Controls 2.12
import QtQuick.Window 2.12
import QtGraphicalEffects 1.0
import "../.."
import EnumTool 1.0

CusRect {
    id: rect_root
    color: ST.color_white

    property string search_str

    signal signalSearchGoods

    onFocusChanged: {
        if (focus)
            tf_search.forceActiveFocus()
    }

    RowLayout {
        spacing: 0
        anchors.fill: parent
        CusRect {
            color: ST.color_transparent
            Layout.preferredWidth: rect_root.height
            Layout.fillHeight: true
            Image {
                anchors.margins: 15
                anchors.fill: parent
                fillMode: Image.PreserveAspectFit
                source: "/Images/codeInput.png"
            }
        }

        CusRect {
            Layout.preferredWidth: 100
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
            CusTextField {
                id: tf_search
                anchors.fill: parent
                placeholderText: qsTr("请输入会员手机号/名称")
                border.width: 0

                keyboard_status:1
                normal_keyboard_x: 730 * dpi_ratio
                normal_keyboard_y: 415 * dpi_ratio

                digital_keyboard_x: 178 * dpi_ratio
                digital_keyboard_y: 268 * dpi_ratio

                onTextChanged: {
                    search_str = text
                }
            }
        }

        CusRect {
            color: ST.color_transparent
            Layout.preferredWidth: rect_root.height
            Layout.fillHeight: true
            Image {
                anchors.margins: 13
                anchors.fill: parent
                fillMode: Image.PreserveAspectFit
                source: tf_search.text == "" ? "/Images/search_image.png" : "/Images/yuebuzu_icon.png"
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    if (tf_search.text != "") {
                        tf_search.text = ""
                    }
                }
            }
        }
    }
}
