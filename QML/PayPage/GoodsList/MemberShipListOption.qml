﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15
import QtGraphicalEffects 1.15
import EnumTool 1.0
import "../.."

CusRect {
    radius: ST.radius
    color: ST.color_transparent

    RowLayout {
        anchors.fill: parent
        focus: true
        spacing: 10

        CusSpacer {
            Layout.fillWidth: true
        }

        CusRect {
            id: rect_pay_method_new
            Layout.fillHeight: true
            Layout.preferredWidth: 220 * dpi_ratio
            color: ST.color_transparent

            property bool is_checked: member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__NEW_MEMBER

            Image {
                anchors.fill: parent
                source: rect_pay_method_new.is_checked ? "/Images/yellow_btn_down.png" : "/Images/yellow_btn_up.png"
            }

            CusText {
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.verticalCenter: parent.verticalCenter
                anchors.verticalCenterOffset: rect_pay_method_new.is_checked ? 5 : -10 * dpi_ratio
                font.pixelSize: 40 * dpi_ratio
                text: "新增\n会员"
                color: ST.color_white_pure
                horizontalAlignment: Text.AlignHCenter
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    if (!rect_pay_method_new.is_checked) {
                        member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__NEW_MEMBER
                    } else {
                        member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__NULL
                    }
                }
            }

            layer.enabled: true // Set Layer for Enable
            layer.effect: OpacityMask {
                maskSource: Rectangle {
                    width: rect_pay_method_new.width
                    height: rect_pay_method_new.height
                    radius: ST.radius
                }
            }
        }

        CusRect {
            id: rect_pay_method_mgr
            Layout.fillHeight: true
            Layout.preferredWidth: 220 * dpi_ratio
            color: ST.color_transparent

            property bool is_checked: member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER

            Image {
                anchors.fill: parent
                source: rect_pay_method_mgr.is_checked ? "/Images/green_btn_down.png" : "/Images/green_btn_up.png"
            }

            CusText {
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.verticalCenter: parent.verticalCenter
                anchors.verticalCenterOffset: rect_pay_method_mgr.is_checked ? 5 : -10 * dpi_ratio
                font.pixelSize: 40 * dpi_ratio
                text: "会员\n管理"
                color: ST.color_white_pure
                horizontalAlignment: Text.AlignHCenter
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    if (!rect_pay_method_mgr.is_checked) {
                        member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER
                    } else {
                        member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__NULL
                    }
                }
            }

            layer.enabled: true // Set Layer for Enable
            layer.effect: OpacityMask {
                maskSource: Rectangle {
                    width: rect_pay_method_mgr.width
                    height: rect_pay_method_mgr.height
                    radius: ST.radius
                }
            }
        }
    }
}
