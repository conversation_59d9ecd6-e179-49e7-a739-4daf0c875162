﻿import QtQuick 2.12
import QtQuick.Layouts 1.12
import QtQuick.Controls 2.12
import QtQuick.Window 2.12
import "Component"
import "../.."
import EnumTool 1.0
import SortFilterProxyModel 0.2
import SettingEnum 1.0
import GoodsDataModel 1.0

CusRect {
    id: goods_list_4_all_goods_root

    width: 1200
    height: 560
    radius: ST.radius
    clip: true
    color: ST.color_transparent

    property alias model_goods_data: model_goods_data

    property string highlight_goods_barcode: ""

    GoodsDataModel {
        id: model_goods_data
        goods_data: goodsManager

        Component.onCompleted: {
            set2Static()
        }
        Component.onDestruction: {
            clearStatic()
        }
    }

    GoodsDataModel {
        id: model_goods_data_4_recognition
        goods_data: goodsManager

        Component.onCompleted: {
            set2Static4Recognition()
        }
        Component.onDestruction: {
            clearStatic4Recognition()
        }
    }

    RowLayout {
        anchors.fill: parent

        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent

            GridView {
                id: grid_goods_list
                anchors.fill: parent
                model: model_goods_data
                snapMode: GridView.SnapToRow
                //控制方向
                flow: GridView.LeftToRight
                cellHeight: 150 * dpi_ratio
                property int row_num: 4
                cellWidth: width / row_num
                visible: right_side.cur_virtual_goods_kind_unique != EnumTool.CUS_GOODS_KIND_RECOGNITION
                cacheBuffer: 100

                delegate: GoodsRect4All {
                    width: grid_goods_list.cellWidth
                    height: grid_goods_list.cellHeight
                    onClickedGoods: {
                        if (is_mamager_model) {
                            if (GOODS_BARCODE_ROLE == EnumTool.ID_NO_CODE_GOODS || GOODS_BARCODE_ROLE == EnumTool.ID_NO_CODE_WEIGHT_GOODS) {
                                toast.openWarn(qsTr("无码商品无法编辑"))
                                return
                            }
                            logMgr.logDataInfo4Qml(qsTr("进入商品编辑页面"))
                            //pay_page_root.goods_quick_edit.addItemByBarcode(goods_barcode) //快速编辑功能已经关闭
                            pay_page_root.goods_edit.addItemByBarcode(goods_barcode)
                        } else {
                            logMgr.logDataInfo4Qml(qsTr("将商品加入购物车"))
                            //发送信号，焦点放入搜索栏
                            search_bar.forceActiveFocus()
                            highlight_goods_barcode = goods_barcode

                            if (goods_barcode == EnumTool.ID_NO_CODE_GOODS || goods_barcode == EnumTool.ID_NO_CODE_WEIGHT_GOODS) {
                                showNoCodeGoodsIfo(goods_barcode == EnumTool.ID_NO_CODE_WEIGHT_GOODS)
                            } else {
                                shopCartList.appendItemByBarcode(goods_barcode, false, model_goods_data.search_str != ""
                                                                 && search_bar.last_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_RECOGNITION
                                                                 && right_side.cur_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_ALL_GOODS)
                            }
                        }

                        keyboard_c.closeAll()

                        if (search_bar.last_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_RECOGNITION) {
                            search_bar.refresh_content()
                        }
                    }
                }

                onFlickEnded: {
                    var cur_view_item = indexAt(contentX, contentY)

                    model_goods_data.curPageIndex = Math.floor(cur_view_item / (4 * 4))

                    lv_goods_list_index.positionViewAtIndex(model_goods_data.curPageIndex, GridView.Center)
                }
            }

            CusRect {
                anchors.fill: parent
                visible: right_side.cur_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_RECOGNITION
                color: ST.color_transparent

                GridView {
                    id: grid_goods_list_recognition
                    anchors.fill: parent
                    model: model_goods_data_4_recognition
                    snapMode: GridView.SnapToRow
                    flow: GridView.LeftToRight
                    cellHeight: 150 * dpi_ratio
                    property int row_num: 4
                    cellWidth: width / row_num

                    delegate: GoodsRect4All {
                        width: grid_goods_list_recognition.cellWidth
                        height: grid_goods_list_recognition.cellHeight
                        onClickedGoods: {
                            if (is_mamager_model) {
                                if (GOODS_BARCODE_ROLE == EnumTool.ID_NO_CODE_GOODS || GOODS_BARCODE_ROLE == EnumTool.ID_NO_CODE_WEIGHT_GOODS) {
                                    toast.openWarn(qsTr("无码商品无法编辑"))
                                    return
                                }
                                pay_page_root.goods_quick_edit.addItemByBarcode(goods_barcode)
                            } else {
                                highlight_goods_barcode = goods_barcode

                                if (goods_barcode == EnumTool.ID_NO_CODE_GOODS || goods_barcode == EnumTool.ID_NO_CODE_WEIGHT_GOODS) {
                                    showNoCodeGoodsIfo(goods_barcode == EnumTool.ID_NO_CODE_WEIGHT_GOODS)
                                } else {
                                    //shoppng_cart.addGoodsByBarcode(goods_barcode)
                                }
                            }
                        }
                    }

                    onFlickEnded: {
                        var cur_view_item = indexAt(contentX, contentY)

                        model_goods_data.curPageIndex = Math.floor(cur_view_item / (4 * 4))

                        lv_goods_list_index.positionViewAtIndex(model_goods_data.curPageIndex, GridView.Center)
                    }
                }

                CusRect {
                    anchors.fill: parent
                    color: ST.color_transparent

                    //未连接到电子秤
                    CusRect {
                        anchors.fill: parent
                        visible: !weightingScaleControl.isConnected
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 0

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 3
                                Layout.fillWidth: true
                                color: ST.color_transparent

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusSpacer {
                                        Layout.preferredHeight: 20 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.preferredHeight: 40 * dpi_ratio
                                        Layout.fillWidth: true
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("未连接到电子秤")
                                            anchors.centerIn: parent
                                            font.bold: true
                                            font.pixelSize: 32 * dpi_ratio
                                            color: ST.color_white_pure
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 30 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        CusImg {
                                            source: "/Images/recognition/weight_scale.png"
                                            anchors.centerIn: parent
                                            height: parent.height
                                            fillMode: Image.PreserveAspectFit
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 15 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 45 * dpi_ratio
                                        color: ST.color_transparent

                                        CusRect {
                                            anchors.horizontalCenter: parent.horizontalCenter
                                            height: parent.height
                                            width: t_weight0_metrics.width + t_weight1_metrics.width
                                            color: ST.color_transparent

                                            CusText {
                                                id: t_weight0
                                                text: qsTr("请将商品")
                                                font.pixelSize: 28 * dpi_ratio
                                                color: ST.color_orange
                                                anchors.right: t_weight1.left
                                                anchors.verticalCenter: parent.verticalCenter
                                                TextMetrics {
                                                    id: t_weight0_metrics
                                                    font: t_weight0.font
                                                    text: t_weight0.text
                                                }
                                            }

                                            CusText {
                                                id: t_weight1
                                                text: qsTr("放置到秤盘上")
                                                font.pixelSize: 28 * dpi_ratio
                                                color: ST.color_white_pure
                                                anchors.right: parent.right
                                                anchors.verticalCenter: parent.verticalCenter
                                                TextMetrics {
                                                    id: t_weight1_metrics
                                                    font: t_weight1.font
                                                    text: t_weight1.text
                                                }
                                            }
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 15 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 80 * dpi_ratio
                                        color: ST.color_transparent
                                    }
                                    CusSpacer {
                                        Layout.preferredHeight: 5 * dpi_ratio
                                    }
                                }
                            }
                        }
                    }

                    //未连接到摄像头
                    CusRect {
                        anchors.fill: parent
                        visible: !cameraControl.isGoodsCameraOpened && weightingScaleControl.isConnected
                        color: ST.color_transparent

                        property var goods_count_t: model_goods_data_4_recognition.goodsCount
                        property var isShowNocodeWeightInRecognition_t: GoodsData.isShowNocodeWeightInRecognition

                        RowLayout {
                            anchors.fill: parent
                            spacing: 0

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 3
                                Layout.fillWidth: true
                                color: ST.color_transparent

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusSpacer {
                                        Layout.preferredHeight: 20 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.preferredHeight: 40 * dpi_ratio
                                        Layout.fillWidth: true
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("未连接到摄像头")
                                            anchors.centerIn: parent
                                            font.bold: true
                                            font.pixelSize: 32 * dpi_ratio
                                            color: ST.color_white_pure
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 30 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        CusImg {
                                            source: "/Images/recognition/camera.png"
                                            anchors.centerIn: parent
                                            height: parent.height
                                            fillMode: Image.PreserveAspectFit
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 15 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 45 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("请检查摄像头连接")
                                            font.pixelSize: 28 * dpi_ratio
                                            color: ST.color_white_pure
                                            anchors.centerIn: parent
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 15 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 80 * dpi_ratio
                                        color: ST.color_transparent
                                    }
                                    CusSpacer {
                                        Layout.preferredHeight: 5 * dpi_ratio
                                    }
                                }
                            }
                        }
                    }

                    //未放置商品
                    CusRect {
                        anchors.fill: parent
                        visible: weightingScaleControl.finalWeight == 0 && cameraControl.isGoodsCameraOpened && weightingScaleControl.isConnected
                        color: ST.color_transparent

                        property var goods_count_t: model_goods_data_4_recognition.goodsCount
                        property var isShowNocodeWeightInRecognition_t: GoodsData.isShowNocodeWeightInRecognition

                        RowLayout {
                            anchors.fill: parent
                            spacing: 0

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 3
                                Layout.fillWidth: true
                                color: ST.color_transparent

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusSpacer {
                                        Layout.preferredHeight: 20 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.preferredHeight: 40 * dpi_ratio
                                        Layout.fillWidth: true
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("未放置商品")
                                            anchors.centerIn: parent
                                            font.bold: true
                                            font.pixelSize: 32 * dpi_ratio
                                            color: ST.color_white_pure
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 30 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        CusImg {
                                            source: "/Images/recognition/place.png"
                                            anchors.centerIn: parent
                                            height: parent.height
                                            fillMode: Image.PreserveAspectFit
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 15 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 45 * dpi_ratio
                                        color: ST.color_transparent

                                        CusRect {
                                            anchors.horizontalCenter: parent.horizontalCenter
                                            height: parent.height
                                            width: t_place0_metrics.width + t_place1_metrics.width
                                            color: ST.color_transparent

                                            CusText {
                                                id: t_place0
                                                text: qsTr("请将商品")
                                                font.pixelSize: 28 * dpi_ratio
                                                color: ST.color_orange
                                                anchors.right: t_place1.left
                                                anchors.verticalCenter: parent.verticalCenter
                                                TextMetrics {
                                                    id: t_place0_metrics
                                                    font: t_place0.font
                                                    text: t_place0.text
                                                }
                                            }

                                            CusText {
                                                id: t_place1
                                                text: qsTr("放置到秤盘上")
                                                font.pixelSize: 28 * dpi_ratio
                                                color: ST.color_white_pure
                                                anchors.right: parent.right
                                                anchors.verticalCenter: parent.verticalCenter
                                                TextMetrics {
                                                    id: t_place1_metrics
                                                    font: t_place1.font
                                                    text: t_place1.text
                                                }
                                            }
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 15 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 80 * dpi_ratio
                                        color: ST.color_transparent
                                    }
                                    CusSpacer {
                                        Layout.preferredHeight: 5 * dpi_ratio
                                    }
                                }
                            }
                        }
                    }

                    //未识别到相关商品
                    CusRect {
                        anchors.fill: parent
                        visible: weightingScaleControl.finalWeight != 0 && model_goods_data_4_recognition.goodsCount == 0
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 0

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 3
                                Layout.fillWidth: true
                                color: ST.color_transparent

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusSpacer {
                                        Layout.preferredHeight: 20 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.preferredHeight: 40 * dpi_ratio
                                        Layout.fillWidth: true
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("未识别到相关商品")
                                            anchors.centerIn: parent
                                            font.bold: true
                                            font.pixelSize: 32 * dpi_ratio
                                            color: ST.color_white_pure
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 30 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        CusImg {
                                            source: "/Images/recognition/search.png"
                                            anchors.centerIn: parent
                                            height: parent.height
                                            fillMode: Image.PreserveAspectFit
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 15 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 45 * dpi_ratio
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent

                                                CusText {
                                                    text: qsTr("搜索商品")
                                                    font.pixelSize: 28 * dpi_ratio
                                                    color: ST.color_orange
                                                    anchors.right: t_band.left
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }

                                                CusText {
                                                    id: t_band
                                                    text:qsTr("以绑定")
                                                    font.pixelSize: 28 * dpi_ratio
                                                    color: ST.color_white_pure
                                                    anchors.right: parent.right
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: 75 * dpi_ratio
                                                color: ST.color_transparent
                                                CusText {
                                                    text: "/"
                                                    anchors.centerIn: parent
                                                    color: ST.color_white_pure
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent

                                                CusRect {
                                                    width: t_add_goods_metrics.width
                                                    height: parent.height
                                                    color: ST.color_transparent

                                                    CusText {
                                                        id: t_add_goods
                                                        text: qsTr("+新增商品")
                                                        color: ST.color_orange
                                                        font.pixelSize: 28 * dpi_ratio

                                                        anchors.centerIn: parent
                                                    }

                                                    TextMetrics {
                                                        id: t_add_goods_metrics
                                                        font: t_add_goods.font
                                                        text: t_add_goods.text
                                                    }

                                                    MouseArea {
                                                        anchors.fill: parent
                                                        onClicked: {
                                                            showUploadGoodsPopup()
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredHeight: 15 * dpi_ratio
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 80 * dpi_ratio
                                        color: ST.color_transparent

                                        CusImg {
                                            source: "/Images/recognition/arrows.png"
                                            height: parent.height
                                            fillMode: Image.PreserveAspectFit
                                            x: 400 * dpi_ratio
                                        }
                                    }
                                    CusSpacer {
                                        Layout.preferredHeight: 5 * dpi_ratio
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: 70 * ST.dpi
            color: ST.color_transparent
            visible: model_goods_data.pageMaxNum > 1 && right_side.cur_virtual_goods_kind_unique != EnumTool.CUS_GOODS_KIND_RECOGNITION

            ColumnLayout {
                anchors.fill: parent
                spacing: 20 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: width
                    radius: ST.radius
                    color: ST.color_black
                    opacity: .8

                    CusText {
                        text: qsTr("页数")
                        anchors.centerIn: parent
                        color: ST.color_white_pure
                    }
                }

                ListView {
                    id: lv_goods_list_index
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    spacing: 20 * dpi_ratio
                    model: model_goods_data.pageMaxNum
                    clip: true

                    delegate: CusRect {
                        width: lv_goods_list_index.width
                        height: width
                        radius: ST.radius
                        color: is_cur_item ? ST.color_green : ST.color_black
                        opacity: .8

                        property bool is_cur_item: model_goods_data.curPageIndex == index

                        CusText {
                            text: index + 1
                            anchors.centerIn: parent
                            color: ST.color_white_pure
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                model_goods_data.curPageIndex = index
                                grid_goods_list.positionViewAtIndex(index * 4 * 4, GridView.Beginning)
                            }
                        }
                    }
                }
            }
        }
    }

    function showNoCodeGoodsIfo(is_weight = false) {
        window_root.loader_center.sourceComponent = compo_popup_no_code_goods
        var cur_weight = weightingScaleControl.getLastWeightKg()
        window_root.loader_center.item.open(is_weight, cur_weight)
    }

    Component {
        id: compo_popup_no_code_goods
        PopupNoCodeGoods {}
    }

    function showDelCurGoodsByVirtualGoodsKindConfirm(goods_barcode) {
        loader_4_del_goods_from_virtual_goods_kind.sourceComponent = compo_del_cur_goods_by_virtual_goods_kind
        loader_4_del_goods_from_virtual_goods_kind.item.goods_barcode = goods_barcode
        loader_4_del_goods_from_virtual_goods_kind.item.open()
    }

    function showDelCurGoods4Recognition(goods_barcode) {
        loader_4_del_cur_goods_4_recognition.sourceComponent = compo_del_cur_goods_4_recognition
        loader_4_del_cur_goods_4_recognition.item.goods_barcode = goods_barcode
        loader_4_del_cur_goods_4_recognition.item.open()
    }

    function reqDelCurGoodsByVirtualGoodsKind(goods_barcode) {
        goodsControl.reqDelGoodsByVirtualGoodsKind(function (is_succ, data) {
            if (is_succ) {

                goodsControl.reqGetVirualGoodsKindAndSave4Qml(function (is_succ, data) {
                    if (is_succ) {
                        right_side.refreshCurGoodsByVirtualGoodsKind()
                    } else {

                    }
                })
                toast.openInfo(qsTr("删除成功"))
            } else {
                toast.openWarn(qsTr("删除失败"))
            }
        }, right_side.cur_virtual_goods_kind_id, goods_barcode)
    }

    Component {
        id: compo_del_cur_goods_by_virtual_goods_kind
        PopupConfirm {
            title_name: qsTr("删除商品")
            message_info: qsTr("确定要从当前分类删除吗?")
            property string goods_barcode: ""
            onConfirm: {
                reqDelCurGoodsByVirtualGoodsKind(goods_barcode)
            }
        }
    }

    Component {
        id: compo_del_cur_goods_4_recognition
        PopupConfirm {
            title_name: qsTr("删除绑定")
            message_info: qsTr("确定要从当前识别删除绑定吗?")

            property string goods_barcode: ""
            onConfirm: {
                goodsManager.tryDelTagByRecognition(goods_barcode)
            }
        }
    }
}
