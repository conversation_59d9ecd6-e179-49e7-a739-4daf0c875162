﻿import QtQuick 2.15
import QtGraphicalEffects 1.0
import QtQuick.Layouts 1.15
import EnumTool 1.0
import "../../.."
CusRect {
    id: goods_rect_root
    color: ST.color_transparent

    signal clickedGoods(var goods_barcode)

    property bool is_highlight: highlight_goods_barcode == GOODS_BARCODE_ROLE

    property bool is_nocode_goods: GOODS_BARCODE_ROLE == EnumTool.ID_NO_CODE_GOODS || GOODS_BARCODE_ROLE == EnumTool.ID_NO_CODE_WEIGHT_GOODS
    CusRect {
        id: goods_rect_rect
        anchors.margins: 7 * dpi_ratio
        anchors.fill: parent
        radius: ST.radius
        color: goods_rect_root.is_highlight ? ST.color_green : ST.color_white_pure

        CusRect {
            anchors.margins: 12 * dpi_ratio
            anchors.fill: parent
            color: ST.color_transparent
            visible: is_nocode_goods

            CusText {
                anchors.centerIn: parent

                font.bold: true
                font.pixelSize: 33 * dpi_ratio * configTool.fontRatio
                color: goods_rect_root.is_highlight ? ST.color_white_pure : ST.color_black

                text: if (model.GOODS_BARCODE_ROLE == EnumTool.ID_NO_CODE_GOODS) {
                          return qsTr("无码商品")
                      } else if (model.GOODS_BARCODE_ROLE == EnumTool.ID_NO_CODE_WEIGHT_GOODS) {
                          return qsTr("无码称重")
                      } else {
                          return ""
                      }
            }
        }

        CusRect {
            anchors.margins: 12 * dpi_ratio
            anchors.fill: parent
            color: ST.color_transparent
            visible: !is_nocode_goods

            ColumnLayout {
                anchors.fill: parent
                spacing: 10 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 40 * dpi_ratio
                    Layout.leftMargin: GOODS_CHENG_TYPE_ROLE == 1 ? 20 * dpi_ratio : 0
                    color: ST.color_transparent

                    CusText {
                        text: GOODS_NAME_ROLE
                        anchors.centerIn: parent
                        elide: Text.ElideRight
                        width: parent.width
                        font.pixelSize: 28 * dpi_ratio * configTool.fontRatio
                        Layout.alignment: Qt.AlignVCenter
                        font.bold: true
                        color: goods_rect_root.is_highlight ? ST.color_white_pure : ST.color_black
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 20 * dpi_ratio
                    color: ST.color_transparent

                    CusText {
                        text: GOODS_BARCODE_ROLE
                        anchors.centerIn: parent
                        font.pixelSize: 20 * dpi_ratio * configTool.fontRatio
                        Layout.alignment: Qt.AlignVCenter
                        color: goods_rect_root.is_highlight ? ST.color_white_pure : ST.color_grey_font
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 40 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 155 * dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: GOODS_SALE_PRICE_ROLE
                                anchors.centerIn: parent
                                elide: Text.ElideRight
                                width: parent.width
                                font.bold: true
                                font.pixelSize: 26 * dpi_ratio * configTool.fontRatio
                                anchors.verticalCenter: parent.verticalCenter
                                color: goods_rect_root.is_highlight ? ST.color_white_pure : ST.color_green
                            }
                        }

                        CusRect {
                            id: rect_stock
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 120 * dpi_ratio
                            Layout.topMargin: 8 * dpi_ratio
                            color: ST.color_green_light

                            visible: configTool.isShowStock

                            layer.enabled: true
                            layer.effect: OpacityMask {
                                maskSource: Rectangle {
                                    width: rect_stock.width
                                    height: rect_stock.height
                                    radius: ST.radius
                                }
                            }

                            RowLayout {
                                anchors.fill: parent

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    CusText {
                                        text: GOODS_COUNT_ROLE
                                        anchors.right: parent.right
                                        elide: Text.ElideRight
                                        anchors.verticalCenter: parent.verticalCenter
                                        font.pixelSize: 18 * dpi_ratio * configTool.fontRatio
                                        color: ST.color_black
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: height
                                    color: ST.color_green
                                    CusText {
                                        text: qsTr("库")
                                        anchors.centerIn: parent
                                        color: ST.color_white_pure
                                        font.pixelSize: 20 * dpi_ratio
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
                clickedGoods(GOODS_BARCODE_ROLE)
            }
        }

        //小红点
        CusRect {
            anchors.top: goods_rect_rect.top
            anchors.topMargin: -5 * dpi_ratio
            anchors.right: goods_rect_rect.right
            anchors.rightMargin: -5 * dpi_ratio
            width: red_point_text.text.length > 2 ? 30 * dpi_ratio * configTool.fontRatio + (red_point_text.text.length - 2) * 20 * dpi_ratio * configTool.fontRatio : 30
                                                    * dpi_ratio * configTool.fontRatio
            height: 30 * dpi_ratio * configTool.fontRatio
            radius: height / 2
            color: ST.color_red
            visible: red_point_text.text != "0" && !is_mamager_model
            CusText {
                id: red_point_text
                text: SHOP_CART_AMOUNT_ROLE
                color: ST.color_white_pure
                anchors.centerIn: parent
                visible: text != 0
            }
        }

        Image {
            width: 38 * dpi_ratio
            height: 38 * dpi_ratio
            anchors.left: parent.left
            anchors.top: parent.top
            source: "/Images/weight_badge.png"
            visible: GOODS_CHENG_TYPE_ROLE == 1
        }

        CusRect {
            id: rect_pc_shelf_state
            anchors.fill: parent
            color: ST.color_transparent
            visible: PC_SHELF_STATE_ROLE == 2 //已下架   pc收银上架状态：1、已上架；2、已下架
            CusRect {
                anchors.fill: parent
                color: ST.color_black
                opacity: .3
            }
            Image {
                source: "/Images/shelf_state_off.png"
                anchors.centerIn: parent
                width: 124 * dpi_ratio
                height: width
                fillMode: Image.PreserveAspectFit
            }
            MouseArea {
                anchors.fill: parent
            }
        }

        //删除按钮
        CusRect {
            visible: is_mamager_model && right_side.cur_virtual_goods_kind_id == EnumTool.CUS_GOODS_KIND_RECOGNITION && !is_nocode_goods
            anchors.top: goods_rect_rect.top
            anchors.topMargin: -5 * dpi_ratio
            anchors.right: goods_rect_rect.right
            anchors.rightMargin: -5 * dpi_ratio
            width: 30 * dpi_ratio * configTool.fontRatio
            height: 30 * dpi_ratio * configTool.fontRatio
            radius: height / 2
            color: ST.color_transparent

            Image {
                anchors.fill: parent
                source: "/Images/close_blue.png"
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    goods_list_4_all_goods_root.showDelCurGoods4Recognition(GOODS_BARCODE_ROLE)
                }
            }
        }

        //删除按钮
        CusRect {
            visible: is_mamager_model && right_side.cur_virtual_goods_kind_id != 0 && right_side.cur_virtual_goods_kind_id != EnumTool.CUS_GOODS_KIND_RECOGNITION
                     && !is_nocode_goods
            anchors.top: goods_rect_rect.top
            anchors.topMargin: -5 * dpi_ratio
            anchors.right: goods_rect_rect.right
            anchors.rightMargin: -5 * dpi_ratio
            width: 30 * dpi_ratio * configTool.fontRatio
            height: 30 * dpi_ratio * configTool.fontRatio
            radius: height / 2
            color: ST.color_transparent

            Image {
                anchors.fill: parent
                source: "/Images/close_red.png"
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    goods_list_4_all_goods_root.showDelCurGoodsByVirtualGoodsKindConfirm(GOODS_BARCODE_ROLE)
                }
            }
        }
    }
}
