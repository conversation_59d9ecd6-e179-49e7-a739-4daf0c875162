﻿import QtQuick 2.15;
import QtGraphicalEffects 1.0
import QtQuick.Layouts 1.15
import "../../.."

import EnumTool 1.0

Item {
    id: goods_rect_root

    readonly property bool is_no_code_goods_normal: goods_id == EnumTool.ID_NO_CODE_GOODS
    readonly property bool is_no_code_goods_weight: goods_id == EnumTool.ID_NO_CODE_WEIGHT_GOODS
    readonly property bool is_no_code_goods: is_no_code_goods_normal || is_no_code_goods_weight
    readonly property var _prefix: "http://file.buyhoo.cc"

    function showNoCodeGoodsIfo() {
        window_root.loader_center.sourceComponent = compo_popup_no_code_goods
        var cur_weight = weightingScaleControl.getLastWeightKg()
        window_root.loader_center.item.open(is_no_code_goods_weight, cur_weight)
    }

    function reqDelCurGoodsByVirtualGoodsKind() {
        goodsControl.reqDelGoodsByVirtualGoodsKind(function (is_succ, data) {
            if (is_succ) {

                goodsControl.reqGetVirualGoodsKindAndSave4Qml(function (is_succ, data) {
                    if (is_succ) {
                        right_side.refreshCurGoodsByVirtualGoodsKind()
                    } else {

                    }
                })
                toast.openInfo("删除成功")
            } else {
                toast.openWarn("删除失败")
            }
        }, right_side.cur_virtual_goods_kind_id, goods_barcode)
    }

    function showDelCurGoodsByVirtualGoodsKindConfirm() {
        window_root.loader_4_del_goods_from_virtual_goods_kind.sourceComponent = compo_del_cur_goods_by_virtual_goods_kind
        window_root.loader_4_del_goods_from_virtual_goods_kind.item.open()
    }

    Component {
        id: compo_del_cur_goods_by_virtual_goods_kind
        PopupConfirm {
            title_name: "删除商品"
            message_info: "确定要从当前分类删除吗?"
            onConfirm: {
                reqDelCurGoodsByVirtualGoodsKind()
            }
        }
    }

    CusRect {
        id: goods_rect_rect
        anchors.margins: 7 * dpi_ratio
        anchors.fill: parent
        radius: ST.radius
        color: ST.color_white_pure

        CusRect {
            anchors.margins: 12 * dpi_ratio
            anchors.fill: parent
            color: ST.color_transparent
            visible: is_no_code_goods

            CusText {
                text: goods_name
                anchors.centerIn: parent
                elide: Text.ElideRight
                width: parent.width - 10 * dpi_ratio
                font.pixelSize: 35 * dpi_ratio
                Layout.alignment: Qt.AlignVCenter
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
            }
        }

        CusRect {
            anchors.margins: 12 * dpi_ratio
            anchors.fill: parent
            color: ST.color_transparent
            visible: !is_no_code_goods

            ColumnLayout {
                anchors.fill: parent
                spacing: 10 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 40 * dpi_ratio
                    Layout.leftMargin: goods_is_weight ? 20 * dpi_ratio : 0
                    color: ST.color_transparent

                    CusText {
                        text: goods_name
                        anchors.centerIn: parent
                        elide: Text.ElideRight
                        width: parent.width
                        font.pixelSize: 28 * dpi_ratio
                        Layout.alignment: Qt.AlignVCenter
                        font.bold: true
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 20 * dpi_ratio
                    color: ST.color_transparent

                    CusText {
                        text: goods_barcode
                        anchors.centerIn: parent
                        font.pixelSize: 20 * dpi_ratio
                        Layout.alignment: Qt.AlignVCenter
                        color: ST.color_grey_font
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 40 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 155 * dpi_ratio
                            color: ST.color_transparent

                            CusText {
                                text: goods_sale_price
                                anchors.centerIn: parent
                                elide: Text.ElideRight
                                width: parent.width
                                color: ST.color_green
                                font.bold: true
                                font.pixelSize: 26 * dpi_ratio
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        CusRect {
                            id: rect_stock
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 120 * dpi_ratio * configTool.fontRatio
                            Layout.topMargin: 8 * dpi_ratio
                            color: ST.color_green_light

                            visible: configTool.isShowStock

                            layer.enabled: true
                            layer.effect: OpacityMask {
                                maskSource: Rectangle {
                                    width: rect_stock.width
                                    height: rect_stock.height
                                    radius: ST.radius
                                }
                            }

                            RowLayout {
                                anchors.fill: parent

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    CusText {
                                        text: goods_count
                                        anchors.right: parent.right
                                        elide: Text.ElideRight
                                        color: ST.color_black
                                        anchors.verticalCenter: parent.verticalCenter
                                        font.pixelSize: 18 * dpi_ratio
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: height
                                    color: ST.color_green
                                    CusText {
                                        text: "库"
                                        anchors.centerIn: parent
                                        color: ST.color_white_pure
                                        font.pixelSize: 20 * dpi_ratio
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Component {
            id: compo_popup_no_code_goods
            PopupNoCodeGoods {//                anchors.fill: parent
            }
        }

        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.AllButtons
            onClicked: {
                if (mouse.button == Qt.LeftButton) {
                    if (pay_page.is_mamager_model) {
                        if (goods_id == EnumTool.ID_NO_CODE_GOODS || goods_id == EnumTool.ID_NO_CODE_WEIGHT_GOODS) {
                            logMgr.logEvtInfo4Qml("尝试编辑无码商品")
                            toast.openWarn("无码商品无法编辑")
                            return
                        }
                        pay_page.goods_quick_edit.addItemByBarcode(goods_barcode) //快速编辑功能已经关闭

                    } else {
                        if (goods_id == EnumTool.ID_NO_CODE_GOODS || goods_id == EnumTool.ID_NO_CODE_WEIGHT_GOODS) {
                            showNoCodeGoodsIfo()
                            //shoppng_cart.addNoCodeGoodsById(goods_id)
                        } else {
                            shoppng_cart.addGoodsByBarcode(goods_barcode)
                        }
                    }
                }
            }
        }

        //小红点
        CusRect {
            anchors.top: goods_rect_rect.top
            anchors.topMargin: -5 * dpi_ratio
            anchors.right: goods_rect_rect.right
            anchors.rightMargin: -5 * dpi_ratio
            width: red_point_text.text.length > 2 ? 30 * dpi_ratio + (red_point_text.text.length - 2) * 20 * dpi_ratio : 30 * dpi_ratio
            height: 30 * dpi_ratio
            radius: height / 2
            color: ST.color_red
            visible: red_point_text.text != "0" && !is_mamager_model
            CusText {
                id: red_point_text
                text: count_in_cart
                color: ST.color_white_pure
                anchors.centerIn: parent
                visible: text != 0
            }
        }

        Image {
            width: 38 * dpi_ratio
            height: 38 * dpi_ratio
            anchors.left: parent.left
            anchors.top: parent.top
            source: "/Images/weight_badge.png"
            visible: goods_is_weight
        }

        CusRect {
            id: rect_pc_shelf_state
            anchors.fill: parent
            color: ST.color_transparent
            visible: pc_shelf_state == 2 //已下架   pc收银上架状态：1、已上架；2、已下架
            CusRect {
                anchors.fill: parent
                color: ST.color_black
                opacity: .3
            }
            Image {
                source: "/Images/shelf_state_off.png"
                anchors.centerIn: parent
                width: 124 * dpi_ratio
                height: width
                fillMode: Image.PreserveAspectFit
            }
            MouseArea {
                anchors.fill: parent
            }
        }

        //删除按钮
        CusRect {
            visible: is_mamager_model
            anchors.top: goods_rect_rect.top
            anchors.topMargin: -5 * dpi_ratio
            anchors.right: goods_rect_rect.right
            anchors.rightMargin: -5 * dpi_ratio
            width: 30 * dpi_ratio
            height: 30 * dpi_ratio
            radius: height / 2
            color: ST.color_transparent

            Image {
                anchors.fill: parent
                source: "/Images/yuebuzu_icon_solid.png"
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    showDelCurGoodsByVirtualGoodsKindConfirm()
                }
            }
        }
    }
}
