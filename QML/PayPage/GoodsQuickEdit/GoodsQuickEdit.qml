﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import EnumTool 1.0
import "../../GodownPage/LeftSide/ContainGoodsDetail"

Rectangle {
    id: goods_quickedit_cntr_root

    //元素单项高度
    property real cell_height: 70 * dpi_ratio

    //添加商品
    function addItemByBarcode(goods_barcode) {
        //不添加重复商品
        for (var i = 0; i < model_quickedit.count; ++i)
            if (model_quickedit.get(i).goods_barcode == goods_barcode)
                return

        var json_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))

        if (json_goods == null)
            return

        model_quickedit.append(json_goods)
    }

    //刷新商品
    function refreshItemByBarcode(goods_barcode) {

        var json_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))

        if (json_goods == null)
            return

        for (var i = 0; i < model_quickedit.count; ++i) {
            if (model_quickedit.get(i).goods_barcode == goods_barcode) {
                model_quickedit.set(i, json_goods)
            }
        }
    }

    function printTags() {
        for (var i = 0; i < model_quickedit.count; ++i) {
            printerControl.printPriceTagByBarcode(model_quickedit.get(i).goods_barcode)
        }
    }

    function setGoodsInfos() {}

    function clearAllGoods() {
        model_quickedit.clear()
    }

    //内容列表-model
    ListModel {
        id: model_quickedit
    }

    ColumnLayout {
        anchors.fill: parent

        //标题栏
        Item {
            Layout.fillWidth: true
            Layout.preferredHeight: cell_height
            RowLayout {
                anchors.fill: parent
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent
                    CusText {
                        anchors.leftMargin: 15
                        anchors.rightMargin: 15
                        width: parent.width - (anchors.leftMargin + anchors.rightMargin)
                        text: qsTr("商品名称")
                        anchors.centerIn: parent
                        color: ST.color_grey_font
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 90 * dpi_ratio
                    color: ST.color_transparent
                    CusText {
                        text: qsTr("采购价")
                        anchors.centerIn: parent
                        color: ST.color_grey_font
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 90 * dpi_ratio
                    color: ST.color_transparent
                    CusText {
                        text: qsTr("售价")
                        anchors.centerIn: parent
                        color: ST.color_grey_font
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 90 * dpi_ratio
                    color: ST.color_transparent
                    CusText {
                        text: qsTr("会员价")
                        anchors.centerIn: parent
                        color: ST.color_grey_font
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 90 * dpi_ratio
                    color: ST.color_transparent
                    CusText {
                        text: qsTr("库存")
                        anchors.centerIn: parent
                        color: ST.color_grey_font
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 50 * dpi_ratio
                    color: ST.color_transparent
                }
            }
        }

        //内容列表
        ListView {
            id: listview_quick_edit
            Layout.fillHeight: true
            Layout.fillWidth: true
            clip: true
            spacing: 6 * dpi_ratio
            model: model_quickedit
            delegate: compo_list_item
        }

        CusRect {
            Layout.fillWidth: true
            Layout.preferredHeight: 130 * dpi_ratio
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent
                spacing: 1
                CusButton {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredWidth: 200
                    color: ST.color_red
                    radius: 0
                    text: "清空"
                    font {
                        pixelSize: 30 * dpi_ratio
                    }
                    onClicked: {
                        clearAllGoods()
                    }
                }
                CusButton {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredWidth: 200
                    color: ST.color_green
                    radius: 0
                    text: "打印"
                    font {
                        pixelSize: 30 * dpi_ratio
                    }
                    onClicked: {
                        printTags()
                    }
                }
            }
        }
    }

    Connections {
        target: shopControl
        function onLoggedIn() {
            clearAllGoods()
        }
    }

    //内容列表-单项内容
    Component {
        id: compo_list_item
        Rectangle {
            id: compo_list_item_contain
            width: listview_quick_edit.width
            height: cell_height

            property string goods_id_: goods_id
            property real input_stock_add: 0
            property alias input_stock: input_stock

            function setGoodsInfos() {
                var data = ({
                                "goods_barcode": goods_barcode,
                                "goods_in_price": input_price_in.text,
                                "goods_sale_price": input_price_sell.text,
                                "goods_cus_price": input_price_sell_2_member.text
                            })
                goodsControl.reqUpdateGoodsByDataJson(function (is_succ, data) {
                    if (is_succ) {
                        toast.openInfo("更新成功")
                    } else {
                        toast.openWarn("更新失败")
                    }
                }, JSON.stringify(data))
            }

            function showPopupEdit() {
                window_root.loader_center.sourceComponent = weighing_option_pop
                window_root.loader_center.item.open()
            }

            RowLayout {
                anchors.fill: parent
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    clip: true

                    CusText {
                        anchors.leftMargin: 15 * dpi_ratio
                        anchors.rightMargin: 5 * dpi_ratio
                        width: parent.width - (anchors.leftMargin + anchors.rightMargin)
                        text: goods_name
                        anchors.centerIn: parent
                        elide: Text.ElideMiddle
                    }
                }

                Rectangle {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 90 * dpi_ratio
                    clip: true
                    TextInputC {
                        id: input_price_in
                        text: Number(goods_in_price).toFixed(2)
                        anchors.centerIn: parent

                        onActiveFocusChanged: {
                            if (activeFocus)
                                return

                            var json_doc = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))
                            if (Number(json_doc.goods_in_price).toFixed(2) == Number(text).toFixed(2)) {
                                toast.openInfo("无需更改")
                                return
                            }

                            compo_list_item_contain.setGoodsInfos()
                        }

                        onAccepted: {
                            keyboard_c.closeAll()
                            search_bar.forceActiveFocus()
                        }

                        validator: RegularExpressionValidator {
                            regularExpression: /^\d+(\.\d{1,2})?$/
                        }
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            keyboard_c.openDigitalKeyboard(725 * dpi_ratio, 190 * dpi_ratio)
                            input_price_in.selectAll()
                            input_price_in.forceActiveFocus()
                        }
                    }
                }
                Rectangle {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 90 * dpi_ratio
                    clip: true
                    TextInputC {
                        id: input_price_sell
                        text: Number(goods_sale_price).toFixed(2)
                        anchors.centerIn: parent

                        onActiveFocusChanged: {
                            if (activeFocus)
                                return

                            var json_doc = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))
                            if (Number(json_doc.goods_sale_price).toFixed(2) == Number(text).toFixed(2)) {
                                toast.openInfo("无需更改")
                                return
                            }

                            compo_list_item_contain.setGoodsInfos()
                        }

                        onAccepted: {
                            keyboard_c.closeAll()
                            search_bar.forceActiveFocus()
                        }

                        validator: RegularExpressionValidator {
                            regularExpression: /^\d+(\.\d{1,2})?$/
                        }
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            keyboard_c.openDigitalKeyboard(725 * dpi_ratio, 190 * dpi_ratio)
                            input_price_sell.selectAll()
                            input_price_sell.forceActiveFocus()
                        }
                    }
                }
                Rectangle {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 90 * dpi_ratio
                    clip: true
                    TextInputC {
                        id: input_price_sell_2_member
                        text: Number(goods_cus_price).toFixed(2)
                        anchors.centerIn: parent

                        onActiveFocusChanged: {
                            if (activeFocus)
                                return

                            var json_doc = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))
                            if (Number(json_doc.goods_cus_price).toFixed(2) == Number(text).toFixed(2)) {
                                toast.openInfo("无需更改")
                                return
                            }

                            compo_list_item_contain.setGoodsInfos()
                        }

                        onAccepted: {
                            keyboard_c.closeAll()
                            search_bar.forceActiveFocus()
                        }

                        validator: RegularExpressionValidator {
                            regularExpression: /^\d+(\.\d{1,2})?$/
                        }
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            keyboard_c.openDigitalKeyboard(725 * dpi_ratio, 190 * dpi_ratio)
                            input_price_sell_2_member.selectAll()
                            input_price_sell_2_member.forceActiveFocus()
                        }
                    }
                }

                Rectangle {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 90 * dpi_ratio
                    clip: true
                    TextInputC {
                        id: input_stock
                        text: Number(goods_count).toFixed(2)
                        anchors.centerIn: parent
                        enabled: false
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            // input_stock
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_GOODS_STOCK_IN_UPDATE) && permissionCtrl.isHavePermission(
                                        EnumTool.PERMISSION_ACTION_GOODS_STOCK_OUT_UPDATE)) {
                                compo_list_item_contain.showPopupEdit()
                            }
                        }
                    }
                }
                Rectangle {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 50 * dpi_ratio
                    clip: true

                    Image {
                        source: "/Images/deleteGoodsImage.png"
                        anchors.centerIn: parent
                        width: 30 * dpi_ratio
                        height: width
                        fillMode: Image.PreserveAspectFit
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            model_quickedit.remove(index)
                        }
                    }
                }
            }

            Component {
                id: weighing_option_pop
                PopupSotckChange {
                    title_name: "请输入入库数量"
                    onSigOpen: {
                        keyboard_c.closeAll()
                    }
                    onConfirm: {
                        goodsControl.reqStockChange(function (is_succ, data) {
                            if (is_succ) {
                                toast.openInfo("入库成功")
                            } else {
                                toast.openInfo("入库失败")
                            }
                        }, goods_barcode, str_num)
                        keyboard_c.closeAll()
                    }
                }
            }

            Connections {
                target: mqttControl
                function onSigMqttRefreshGoodsByBarcode4Qml(goods_barcode) {
                    if (goods_barcode == model.goods_barcode) {
                        var json_doc = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))
                        if (json_doc) {
                            model_quickedit.set(index, json_doc)
                        }
                    }
                }
            }
        }
    }
}
