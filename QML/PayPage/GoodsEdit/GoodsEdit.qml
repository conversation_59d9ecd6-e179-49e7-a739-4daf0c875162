﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import EnumTool 1.0
import "../../GodownPage/LeftSide/ContainGoodsDetail"
import "../../GodownPageOld"

Rectangle {

    id: goods_edit_cntr_root

    property string goods_barcode: "" //"商品条码"
    property string goods_name: "" //商品名
    property int goods_cheng_type: 2 //0、按件；1、按重量
    property string goods_price: "" //零售单价
    property string goods_web_price: "" //网购单价
    property string goods_member_price: "" //会员单价
    property string goods_in_price: "" //库存均价
    property string goods_unit: "" //商品单位
    property string goods_stock:"" //商品库存
    property string goods_supplier:""//供应商
    property string goods_kind_unique: ""//商品分类
    property string goodStockPrice: "" //最近入库价
    property string goods_life:"" //保质期
    property bool firstClick: true
    property string supplier_nameTemp:""
    property string supplier_name:""
    property bool searchOpen: false //弹窗控制符
    property bool selectOpen: true //弹窗控制符

    signal signOpenGoodsManger(var goodsBarcodes)

    function openGoodsManger() {
        signOpenGoodsManger(goods_barcode)
    }
    onVisibleChanged: {
        if(visible){
            resetGoodsInfo()
        }else{
            goods_cheng_type = 2
        }
    }
    function clearAllData(){
        goods_barcode = ""
        goods_name = ""
        goods_cheng_type = 2
        goods_price = ""
        goods_web_price = ""
        goods_member_price = ""
        goods_in_price = ""
        goods_unit = ""
        goods_stock = ""
        goods_supplier = ""
        supplier_name = ""
        search_input = ""
        fuzzy_search_model.clear()
    }
    // 重置当前信息
    function resetGoodsInfo() {
        refreshData()
        clearAllData()
    }
    //添加待编辑商品
    function addItemByBarcode(goods_barcode) {
        var goods_json = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))
        goods_edit_cntr_root.goods_barcode = goods_json.goods_barcode //商品条码
        goods_edit_cntr_root.goods_name = goods_json.goods_name        //商品名称
        goods_edit_cntr_root.goods_supplier = goods_json.default_supplier_unique //供货商
        goods_edit_cntr_root.goods_cheng_type = goods_json.goodsChengType //计价类型
        goods_edit_cntr_root.goods_price = Number(goods_json.goods_sale_price).toFixed(2) //零售单价
        goods_edit_cntr_root.goods_web_price = Number(goods_json.goods_web_sale_price).toFixed(2) //网购单价
        goods_edit_cntr_root.goods_member_price = Number(goods_json.goods_cus_price).toFixed(2) //会员单价
        goods_edit_cntr_root.goods_stock = Number(goods_json.goods_count).toFixed(2) //商品库存
        goods_edit_cntr_root.goods_in_price = Number(goods_json.goods_in_price).toFixed(2)  //库存均价
        goods_edit_cntr_root.goods_unit = goods_json.goods_unit //商品单位
        goods_edit_cntr_root.goods_kind_unique = goods_json.goods_kind_unique
        goods_edit_cntr_root.goodStockPrice =Number(goods_json.goodStockPrice).toFixed(2)
        goods_life =  Number(goods_json.goods_life)
//        focusSupplier(goods_json.default_supplier_unique)
        search_input.text = ""
        focusUnit(goods_json.goods_unit)
        supplierNameGene(goods_json.default_supplier_unique)
    }
    // 刷新数据
    function refreshData() {
        refreshUnit()
        refreshSupplier()
    }
    // 刷新单位
    function refreshUnit() {
        lm_goods_unit.clear()
        var str_tmp = goodsManager.getAllGoodsUnitJson()
        var json_all_goods_unit = JSON.parse(str_tmp)

        if (json_all_goods_unit == null)
            return

        for (var i = 0; i < json_all_goods_unit.length; ++i) {
            var cur_item = json_all_goods_unit[i]
            lm_goods_unit.append(cur_item)
        }
    }
    function supplierNameGene(supplierUnique) {
        for (var i = 0; i < lm_supplier.count; ++i) {
            var cur_item = lm_supplier.get(i)
            if (cur_item.supplier_unique == supplierUnique) {
                supplier_name = cur_item.supplier_name
                search_input.text = supplier_name
                break
            }
        }
    }
    // 选中单位
    function focusUnit(unit_name) {

        if (unit_name == "") {
            combo_goods_unit.currentIndex = -1
            return
        }

        for (var i = 0; i < lm_goods_unit.count; ++i) {
            var cur_item = lm_goods_unit.get(i)
            if (cur_item.goods_unit == unit_name) {
                combo_goods_unit.currentIndex = i
                return
            }
        }

        lm_goods_unit.append({
                                 "goods_unit_id": -1,
                                 "goods_unit": unit_name
                             })

        combo_goods_unit.currentIndex = lm_goods_unit.count - 1
    }
    Timer {
        id: printTimer
        interval: 200
        repeat: false
        onTriggered: {
            printCus.border.color = "#999999"
            printCus.color = ST.color_transparent
            goodsDetail_print.color = "#999999"
        }
    }
    // 刷新供应商
    function refreshSupplier() {
        lm_supplier.clear()
        var json_data = JSON.parse(supplierControl.getSupplierJson())
        if (!json_data)
            return
        for (var i = 0; i < json_data.length; ++i) {
            var cur_item = json_data[i]
            lm_supplier.append(cur_item)
        }
    }
    // 选中供应商
//    function focusSupplier(supplier_unique) {
//        for (var i = 0; i < lm_supplier.count; ++i) {
//            var cur_item = lm_supplier.get(i)
//            if (cur_item.supplier_unique == supplier_unique) {
//                combo_supplier.currentIndex = i
//                break
//            }
//        }
//    }
    function showDelCurGoodsConfirm(goods_barcode_in) {
        window_root.loader_4_del_cur_goods.sourceComponent = compo_del_cur_goods
        window_root.loader_4_del_cur_goods.item.goods_barcode = goods_barcode_in
        window_root.loader_4_del_cur_goods.item.open()
    }
    //入库
    function showStockChangeIn(goods_barcode, goods_ratio = 1,good_stock_price,goods_unit,goods_cheng_type) {
        window_root.loader_4_stock_del.sourceComponent = popup_sotck_change_in
        window_root.loader_4_stock_del.item.open(goods_barcode, goods_ratio,good_stock_price,goods_life,goods_unit,goods_cheng_type)
    }
    //出库
    function showStockChangeOutFirst(goods_barcode, goods_ratio = 1,goods_unit,good_stock_price,goods_cheng_type) {
        window_root.loader_4_stock_del.sourceComponent = popup_sotck_change_out_First
        window_root.loader_4_stock_del.item.open(goods_barcode, goods_ratio,goods_barcode,goods_unit,good_stock_price,goods_cheng_type)
    }
    function showStockChangeOutMean(goods_barcode, goods_ratio = 1,goods_unit,good_stock_price,goods_cheng_type) {
        window_root.loader_4_stock_del.sourceComponent = popup_sotck_change_out_Mean
        window_root.loader_4_stock_del.item.open(goods_barcode, goods_ratio,goods_unit,good_stock_price,goods_cheng_type)
    }
    //参数校验
    function checkData() {
        if (goods_barcode == "") {
            toast.openWarn(qsTr(("商品条码不能为空!")))
            return false
        } else if (goods_name == "") {
            toast.openWarn(qsTr("商品名称不能为空!"))
            return false
        } else if (goods_price == "") {
            toast.openWarn(qsTr("零售单价不能为空!"))
            return false
//        } else if (goods_supplier == "") {
//            toast.openWarn(qsTr("供货商名不能为空!"))
//            return false
        } else if (goods_unit == "") {
           toast.openWarn(qsTr("商品单位不能为空!"))
           return false
       }else if (goods_cheng_type == 2) {
            toast.openWarn(qsTr("计价类型未选择!"))
            return false
        }
        return true
    }
    // 删除商品
    function deleteGoods(goods_barcode_in = "") {

        if (goods_barcode_in == "")
            return

        // 关联商品列表
        var cur_goods_doc = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode_in))

        function callback(is_succ, data) {
            if (is_succ) {
                toast.openInfo(qsTr("删除成功"))
            } else {
                toast.openError(qsTr("删除失败"))
            }
        }

        if (cur_goods_doc.goods_contain == 1) {
            var json_doc = JSON.parse(goodsManager.getForeignGoodsJsonListByBarcode(goods_barcode_in))

            if (json_doc) {
                if (json_doc.length > 0) {
                    goodsControl.reqDelGoodsByBarcode(callback, json_doc[0].goods_barcode)
                }
                if (json_doc.length > 1) {
                    goodsControl.reqDelGoodsByBarcode(callback, json_doc[1].goods_barcode)
                }
            }
        }

        goodsControl.reqDelGoodsByBarcode(callback, goods_barcode_in)

        resetGoodsInfo()
    }
    Component {
        id: compo_del_cur_goods
        PopupConfirm {
            title_name: qsTr("删除商品")
            message_info: qsTr("确定要删除当前商品吗?")
            property string goods_barcode: ""
            onSigClose: {
            }
            onConfirm: {
                deleteGoods(goods_barcode)
            }
        }
    }
    Component {
        id: popup_sotck_change_in
        PopupInStock{
            title_names:qsTr("商品入库")
            onSigOpen: {
            }
            onSigClose: {

            }
            onConfirm: {
                goodsControl.reqStockChangeNew_v2(function (is_succ, data) {
                    var json_doc = JSON.parse(data);
                    if (is_succ) {
                        toast.openInfo(qsTr("入库成功"));
                        addItemByBarcode(goods_barcode)
                    } else {
                        toast.openInfo(qsTr("入库失败!")+json_doc.msg);
                    }
                }, goods_barcode, str_num ,"1", stockPriceCopy,"",goods_prod,goodsExp,"",stockTotalPrice)
            }
        }
    }
    Component {
        id: popup_sotck_change_out_Mean
            PopupOutStockMean{
                title_names:qsTr("商品出库")
                onSigOpen: {
                }
                onSigClose: {
                }
                onConfirm: {
                    goodsControl.reqStockChangeNew_v2(function (is_succ, data) {
                        var json_doc = JSON.parse(data);
                        if (is_succ) {
                            toast.openInfo(qsTr("出库成功"));
                            addItemByBarcode(goods_barcode)
                        } else {
                            toast.openInfo(qsTr("出库失败!")+json_doc.msg);
                        }
                    }, goods_barcode, str_num ,"2", stockPriceCopy,stockOutColorType,"","","",stockTotalPrice)
                }
            }
    }
    Component {
        id: popup_sotck_change_out_First
            PopupOutStockFirst{
                title_names:qsTr("商品出库")
                onSigOpen: {
                }
                onSigClose: {

                }
                onConfirm: {
                    //分批次出库
                    goodsControl.reqStockChangeNew_v2(function (is_succ, data) {
                        var json_doc = JSON.parse(data);
                        if (is_succ) {
                            toast.openInfo(qsTr("出库成功"));
                            addItemByBarcode(goods_barcode)
                        } else {
                            toast.openInfo(qsTr("出库失败!")+json_doc.msg);
                        }
                    }, goods_barcode, str_num ,"2", stockPriceCopy,stockOutColorType,"","",goodBatchMessage,stockTotalPrice)
                }

            }
    }

    // 编辑商品
    function updateGoods_v2() {
        if (!checkData()) {
            return
        }
        var result_json = []
        result_json.push({
                             "goods_contain": "1",
                             "goods_barcode": goods_barcode,
                             "goods_name": goods_name,
                             "goodsChengType": goods_cheng_type,
                             "goods_sale_price": goods_price,
                             "goods_cus_price": goods_member_price,
                             "goods_in_price": goods_in_price,
                             "goods_unit": goods_unit,
                             "default_supplier_unique": goods_supplier,
                             "goods_web_sale_price":goods_web_price,
                             "goods_kind_unique":goods_kind_unique,
                         })

        goodsControl.reqUpdateGoodsByDataJson_v2_4Old(function (is_succ, data) {
            if (is_succ) {
                if (data) {
                    var json_doc = JSON.parse(data)
                    toast.openInfo(json_doc.msg)
                } else {
                    toast.openInfo(qsTr("修改商品成功"))
                }
                resetGoodsInfo()
            } else {
                if (data) {
                    var json_doc = JSON.parse(data)
                    toast.openWarn(json_doc.msg)
                } else {
                    toast.openWarn(qsTr("修改商品失败"))
                }
            }
        }, JSON.stringify(result_json))
    }
    //打印
    function printTags() {
        printerControl.printPriceTagByBarcode(goods_barcode)
    }

    component RectLable: CusRect {
        Layout.fillHeight: true
        Layout.preferredWidth: 140 * dpi_ratio
        color: ST.color_transparent

        property alias text: t_name.text
        property bool is_required: false

        CusText {
            text: "*"
            color: ST.color_red
            anchors.right: t_name.left
            anchors.rightMargin: 5 * dpi_ratio
            anchors.verticalCenter: parent.verticalCenter
            font.family: ST.fontFamilyYaHei
            font.pointSize: 18 * dpi_ratio
            visible: is_required
        }
        CusText {
            id: t_name
            text: ""
            anchors.verticalCenter: parent.verticalCenter
            anchors.right: parent.right
            anchors.rightMargin: (!is_required)?12 *dpi_ratio: 0 *dpi_ratio
            font.family: ST.fontFamilyYaHei
            font.pointSize: 18 * dpi_ratio
        }
    }
    ColumnLayout {
        anchors.fill: parent
        //未选择商品
        CusRect {
            id:image1Cus
            Layout.fillWidth: true
            height: 180 * dpi_ratio
            color: ST.color_transparent
            anchors.left: parent.left
            anchors.top: parent.top
            visible:goods_barcode == ""
            anchors.topMargin: 250 *dpi_ratio
            Image {
                id: images1
                width: 180 *dpi_ratio
                height: 180 *dpi_ratio
                anchors.verticalCenter: parent.verticalCenter
                anchors.horizontalCenter: parent.horizontalCenter
                source: "/Images/shopping.png"
            }

        }
        CusRect{
            Layout.fillWidth: true
            height: 130 *dpi_ratio
            color: ST.color_transparent
            anchors.left: parent.left
            visible:goods_barcode == ""
            anchors.top: image1Cus.bottom
            Text {
                id: text1
                font.family: ST.fontFamilyYaHei
                font.pointSize: 20 * dpi_ratio
                color:"#000000"
                anchors.horizontalCenter:parent.horizontalCenter
                anchors.top: parent.top
                anchors.topMargin: 5 *dpi_ratio
                verticalAlignment: Text.AlignVCenter
                horizontalAlignment: Text.AlignHCenter
                text: qsTr("点击左侧商品查看详情")
            }
            Image {
                id: images2
                anchors.left: text1.right
                anchors.leftMargin: 20 *dpi_ratio
                anchors.top: parent.top
                anchors.topMargin: -25 * dpi_ratio
                height:82 * dpi_ratio
                width:110 * dpi_ratio
                source: "/Images/arrow.png"
            }
        }
        //商品详情页面
        ColumnLayout {
            visible:goods_barcode != ""
            anchors.fill: parent
            CusRect{
                id:goodsDetail
                Layout.fillWidth: true
                height: 80 *dpi_ratio
                color: ST.color_transparent
                anchors.left: parent.left
                anchors.top: parent.top
                CusRect{
                    width:200 *dpi_ratio
                    height: 80 *dpi_ratio
                    color: ST.color_transparent
                    anchors.left: parent.left
                    anchors.top: parent.top
                    CusRect{
                        id:goodsDetail_shu
                        width: 4 *dpi_ratio
                        height: 30 *dpi_ratio
                        color: "#00BD75"
                        anchors.left: parent.left
                        anchors.leftMargin: 40 * dpi_ratio
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 22 * dpi_ratio
                        radius:10 * dpi_ratio

                    }
                    Text {
                        id: goodsDetail_text1

                        anchors.left: goodsDetail_shu.right
                        anchors.leftMargin: 10 *dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        font.family: ST.fontFamilyYaHei
                        font.pointSize: 24 * dpi_ratio
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignHCenter
                        color:"#000000"
                        text: qsTr("商品详情")
                    }
                }
                CusRect{
                    id:printCus
                    width:160 *dpi_ratio
                    height: 60 *dpi_ratio
                    color: ST.color_transparent
                    border.color: "#999999"
                    border.width: 2 *dpi_ratio
                    anchors.verticalCenter: goodsDetail.verticalCenter
                    anchors.right: parent.right
                    radius: 10 * dpi_ratio
                    anchors.rightMargin: 20 * dpi_ratio
                    Image {
                        id: goodsDetail_image
                        width:32 *dpi_ratio
                        height:28 *dpi_ratio
                        anchors.left: parent.left
                        anchors.leftMargin: 15 *dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter
                        source: "/Images/print.png"
                    }
                    Text {
                        id: goodsDetail_print
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.left: goodsDetail_image.right
                        anchors.leftMargin: 5 * dpi_ratio
                        font.family: ST.fontFamilyYaHei
                        font.pointSize: 16 * dpi_ratio
                        color:"#999999"
                        text: qsTr("打印价签")
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            if(goods_barcode != ""){
                                printTimer.start()
                                printCus.border.color = "#448DF6"
                                printCus.color = "#448DF6"
                                goodsDetail_print.color = "#FFFFFF"
                                printTags()
                            }else{
                                toast.openWarn("未选择商品!")
                            }
                        }
                    }
                }
            }
            CusRect{
                id: goodsBarcode
                Layout.fillWidth: true
                height: 80 *dpi_ratio
                color: ST.color_transparent
                anchors.left: parent.left
                anchors.top: goodsDetail.bottom
                RectLable {
                    id:goodsBarcodeName
                    height: 80 *dpi_ratio
                    width: 140 * dpi_ratio
                    is_required: true
                    color:ST.color_transparent
                    text: qsTr("商品条码")
                }
                CusTextField {
                    anchors.verticalCenter: parent.verticalCenter
                    height: 50 *dpi_ratio
                    width: 535 * dpi_ratio
                    anchors.left:goodsBarcodeName.right
                    anchors.leftMargin: 10 * dpi_ratio
                    font.family: ST.fontFamilyYaHei
                    font.pointSize: 18 * dpi_ratio
                    text: goods_barcode
                    onTextEdited: {
                    }
                    onTextChanged: {
                        goods_edit_cntr_root.firstClick = true
                    }

                    enabled: false
                    normal_keyboard_x: 725 * dpi_ratio
                    normal_keyboard_y: 565 * dpi_ratio
                    digital_keyboard_x: 1160 * dpi_ratio
                    digital_keyboard_y: 435 * dpi_ratio
                }
            }
            CusRect{
                id: goodsName
                Layout.fillWidth: true
                height: 80 *dpi_ratio
                color: ST.color_transparent
                anchors.left: parent.left
                anchors.top: goodsBarcode.bottom
                RectLable {
                    id:goodsNameRec
                    height: 80 *dpi_ratio
                    width: 140 * dpi_ratio
                    is_required: true
                    color:ST.color_transparent
                    text: qsTr("商品名称")
                }
                TextField {
                    id: textField
                    anchors.verticalCenter: parent.verticalCenter
                    height: 50 *dpi_ratio
                    width: 535 * dpi_ratio
                    anchors.left:goodsNameRec.right
                    anchors.leftMargin: 10 * dpi_ratio
                    font.family: ST.fontFamilyYaHei
                    font.pointSize: 18 * dpi_ratio
                    text: goods_name

                    background: Rectangle {
                        radius: 5 * dpi_ratio // 设置圆角大小
                        border.width: 1
                        border.color: ST.color_grey_border2 // 初始边框颜色为透明
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            textField.background.border.color = ST.color_green
                            if (goods_edit_cntr_root.firstClick) {
                                goods_edit_cntr_root.firstClick = false
                                textField.forceActiveFocus()
                                textField.selectAll()
                            }
                            else {
                                if(!goods_edit_cntr_root.firstClick)
                                {
                                    textField.forceActiveFocus()
                                    if((mapToItem(textField, mouse.x, mouse.y).x / textField.contentWidth) >0.95 ){
                                        textField.cursorPosition = textField.text.length
                                    }
                                    else{
                                        var cursorPosition = mapToItem(textField, mouse.x, mouse.y).x / textField.contentWidth * textField.text.length
                                        textField.cursorPosition = cursorPosition
                                    }
                                    }
                                 }
                            window_root.keyboard_c.closeAll()
                            window_root.keyboard_c.setNormalKeyboardPos(500 * dpi_ratio, 560 * dpi_ratio)
                            window_root.keyboard_c.openNormalKeyboard()
                        }
                    }
                    onTextEdited: {
                        goods_name = text
                    }
                    onFocusChanged: {
                        if (!focus) {
                            goods_edit_cntr_root.firstClick = true
                            textField.background.border.color = ST.color_grey_border2
                        }
                    }
                }

            }
            CusRect{
                id: goodspricingvaluation
                Layout.fillWidth: true
                height: 80 *dpi_ratio
                color: ST.color_transparent
                anchors.left: parent.left
                anchors.top: goodsName.bottom
                RectLable {
                    id:goodspricingvaluationRec
                    height: 80 *dpi_ratio
                    width: 140 * dpi_ratio
                    is_required: true
                    color:ST.color_transparent
                    text: qsTr("计价类型")
                }
                CusRect {
                    id:goodspricingvaluationCus
                    anchors.verticalCenter: parent.verticalCenter
                    height: 50 *dpi_ratio
                    width: 535 * dpi_ratio
                    anchors.left:goodspricingvaluationRec.right
                    anchors.leftMargin: 10 * dpi_ratio
                    color: ST.color_transparent
                    CusRect
                    {
                        id:pieceRec
                        anchors.verticalCenter: parent.verticalCenter
                        height: 50 *dpi_ratio
                        width: 100 * dpi_ratio
                        anchors.left:goodspricingvaluationRec.right
                        anchors.leftMargin: 10 * dpi_ratio
                        color: ST.color_transparent
                        CusRect
                        {
                            id: pieceRec1
                            width: 30 * dpi_ratio
                            height:30 * dpi_ratio
                            radius: width / 2
                            anchors.verticalCenter: parent.verticalCenter
                            color:goods_cheng_type == 0 ?"#00BD75":"#FFFFFF"
                            border.width: 2 *dpi_ratio
                            border.color:goods_cheng_type == 0 ?"#00BD75":"#D3D3D3"

                            Image {
                                width: 25 * dpi_ratio
                                height:18 * dpi_ratio
                                visible:goods_cheng_type == 0
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.horizontalCenter: parent.horizontalCenter
                                source: "/Images/duihao.png"
                            }
                        }
                        Text {
                            id: pieceName
                            anchors.left: pieceRec1.right
                            anchors.leftMargin: 15 *dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            font.family: ST.fontFamilyYaHei
                            font.pointSize: 18 * dpi_ratio
                            color:"#00BD75"
                            text: qsTr("计件")
                        }
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                goods_cheng_type = 0
                            }
                        }
                    }
                    CusRect
                    {
                        id:weightRec
                        anchors.verticalCenter: parent.verticalCenter
                        height: 50 *dpi_ratio
                        width: 100 * dpi_ratio
                        anchors.left:pieceRec.right
                        anchors.leftMargin: 150 * dpi_ratio
                        color: ST.color_transparent
                        CusRect
                        {
                            id: weightRec1
                            width: 30 * dpi_ratio
                            height:30 * dpi_ratio
                            radius: width / 2
                            anchors.verticalCenter: parent.verticalCenter
                            color:goods_cheng_type == 1 ?"#00BD75":"#FFFFFF"
                            border.width: 2 *dpi_ratio
                            border.color:goods_cheng_type == 1?"#00BD75":"#D3D3D3"

                            Image {
                                width: 25 * dpi_ratio
                                height:18 * dpi_ratio
                                anchors.verticalCenter: parent.verticalCenter
                                visible:goods_cheng_type == 1
                                anchors.horizontalCenter: parent.horizontalCenter
                                source: "/Images/duihao.png"
                            }
                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    goods_cheng_type = 1
                                }
                            }
                        }
                        Text {
                            id: weightName
                            anchors.left: weightRec1.right
                            anchors.leftMargin: 15 *dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            font.family: ST.fontFamilyYaHei
                            font.pointSize: 18 * dpi_ratio
                            color:"#00BD75"
                            text: qsTr("计重")
                        }

                    }
                }
            }
            CusRect{
                id: goodsUnitPrice
                Layout.fillWidth: true
                height: 80 *dpi_ratio
                color: ST.color_transparent
                anchors.left: parent.left
                anchors.top: goodspricingvaluation.bottom
                RectLable {
                    id:goodsUnitPriceRec
                    height: 80 *dpi_ratio
                    width: 140 * dpi_ratio
                    is_required: true
                    color:ST.color_transparent
                    text: qsTr("零售单价")
                }
                CusTextField {
                    id:goodsUnitPriceCus
                    anchors.verticalCenter: parent.verticalCenter
                    height: 50 *dpi_ratio
                    width: 200 * dpi_ratio
                    anchors.left:goodsUnitPriceRec.right
                    anchors.leftMargin: 10 * dpi_ratio
                    font.family: ST.fontFamilyYaHei
                    font.pointSize: 18 * dpi_ratio
                    validator: RegExpValidator { regExp: /[0-9]*(\.[0-9]{0,2})?/ }
                    text: goods_price
                    onTextEdited: {
                        goods_price = text
                    }
                    enabled: true

                    is_clicked_select_all: true

                    keyboard_status: 1

                    normal_keyboard_x: 725 * dpi_ratio
                    normal_keyboard_y: 565 * dpi_ratio

                    digital_keyboard_x: 1160 * dpi_ratio
                    digital_keyboard_y: 320 * dpi_ratio
                }
                RectLable {
                    id:onlinePriceRec
                    height: 80 *dpi_ratio
                    width: 140 * dpi_ratio
                    anchors.left:goodsUnitPriceCus.right
                    anchors.leftMargin: -15 * dpi_ratio
                    is_required: false
                    color:ST.color_transparent
                    text: qsTr("网购单价")
                }
                CusTextField {
                    anchors.verticalCenter: parent.verticalCenter
                    height: 50 *dpi_ratio
                    width: 200 * dpi_ratio
                    anchors.left:onlinePriceRec.right
                    anchors.leftMargin: 10 * dpi_ratio
                    font.family: ST.fontFamilyYaHei
                    font.pointSize: 18 * dpi_ratio
                    validator: RegExpValidator { regExp: /[0-9]*(\.[0-9]{0,2})?/ }
                    text: goods_web_price
                    onTextEdited: {
                        goods_web_price = text
                    }
                    enabled: true

                    is_clicked_select_all: true

                    keyboard_status: 1

                    normal_keyboard_x: 725 * dpi_ratio
                    normal_keyboard_y: 565 * dpi_ratio

                    digital_keyboard_x: 1160 * dpi_ratio
                    digital_keyboard_y: 320 * dpi_ratio
                }
            }
            CusRect{
                id: goodsMemberPrice
                Layout.fillWidth: true
                height: 80 *dpi_ratio
                color: ST.color_transparent
                anchors.left: parent.left
                anchors.top: goodsUnitPrice.bottom
                RectLable {
                    id:goodsMemberPriceRec
                    height: 80 *dpi_ratio
                    width: 140 * dpi_ratio
                    is_required: false
                    color:ST.color_transparent
                    text: qsTr("会员单价")
                }
                CusTextField {
                    id:goodsMemberPriceCus
                    anchors.verticalCenter: parent.verticalCenter
                    height: 50 *dpi_ratio
                    width: 200 * dpi_ratio
                    anchors.left:goodsMemberPriceRec.right
                    anchors.leftMargin: 10 * dpi_ratio
                    font.family: ST.fontFamilyYaHei
                    font.pointSize: 18 * dpi_ratio
                    validator: RegExpValidator { regExp: /[0-9]*(\.[0-9]{0,2})?/ }
                    text: goods_member_price
                    onTextEdited: {
                        goods_member_price = text
                    }
                    enabled: true

                    is_clicked_select_all: true

                    keyboard_status: 1

                    normal_keyboard_x: 725 * dpi_ratio
                    normal_keyboard_y: 565 * dpi_ratio

                    digital_keyboard_x: 1160 * dpi_ratio
                    digital_keyboard_y: 320 * dpi_ratio
                }
                RectLable {
                    id:purchasePrice
                    height: 80 *dpi_ratio
                    width: 140 * dpi_ratio
                    anchors.left:goodsMemberPriceCus.right
                    anchors.leftMargin: -15 * dpi_ratio
                    is_required: false
                    color:ST.color_transparent
                    text: qsTr("库存均价")
                }
                CusTextField {
                    anchors.verticalCenter: parent.verticalCenter
                    height: 50 *dpi_ratio
                    width: 200 * dpi_ratio
                    anchors.left:purchasePrice.right
                    anchors.leftMargin: 10 * dpi_ratio
                    font.family: ST.fontFamilyYaHei
                    font.pointSize: 18 * dpi_ratio
                    validator: RegExpValidator { regExp: /[0-9]*(\.[0-9]{0,2})?/ }
                    text: goods_in_price

                    onTextEdited: {
                        goods_in_price = text
                    }
                    enabled: false

                    is_clicked_select_all: true

                    keyboard_status: 1

                    normal_keyboard_x: 725 * dpi_ratio
                    normal_keyboard_y: 565 * dpi_ratio

                    digital_keyboard_x: 1160 * dpi_ratio
                    digital_keyboard_y: 320 * dpi_ratio
                }
            }
            CusRect{
                id: supplierNameCus
                Layout.fillWidth: true
                height: 80 *dpi_ratio
                color: ST.color_transparent
                anchors.left: parent.left
                anchors.top: goodsMemberPrice.bottom
                RectLable {
                    id:supplierName
                    height: 80 *dpi_ratio
                    width: 140 * dpi_ratio
                    anchors.left:supplierNameCus.left
//                    is_required: true
                    color:ST.color_transparent
                    text: qsTr("供货商名")
                }
                CusRect {
                    id:supplierRec
                    anchors.verticalCenter: parent.verticalCenter
                    height: 50 *dpi_ratio
                    width: 535 * dpi_ratio
                    anchors.left:supplierName.right
                    anchors.leftMargin: 10 * dpi_ratio
                    color: ST.color_white
                    border.color: ST.color_grey_border2
                    border.width: 1 *dpi_ratio
                    radius:ST.radius
                    ListModel {
                           id: lm_supplier
                       }
                    Item {
                        anchors.fill: parent
                    Image {
                        id: searchIcon
                        source: "/Images/Subscript_black.png"
                        width: 24 * dpi_ratio
                        height: 24 * dpi_ratio
                        anchors.right: parent.right
                        anchors.rightMargin: 10 * dpi_ratio
                        anchors.verticalCenter: search_input.verticalCenter
                    }
                    Text {
                        id: placeholder
                        text: "请选择"
                        color: "#00BD75"
                        font.family: ST.fontFamilyYaHei
                        font.pointSize: 18 * dpi_ratio
                        anchors.left: search_input.left
                        anchors.leftMargin: 2 * dpi_ratio
                        anchors.verticalCenter: search_input.verticalCenter
                        visible: search_input.text === ""
                    }
                    TextInput
                    {
                        id: search_input;
                        width: 490  * dpi_ratio;
                        height: 42  * dpi_ratio;
                        anchors.left: parent.left;
                        anchors.leftMargin: 15 *dpi_ratio
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.horizontalCenter: parent.horizontalCenter;
                        verticalAlignment: Text.AlignVCenter;
                        horizontalAlignment: Text.AlignLeft
                        font.family: ST.fontFamilyYaHei
                        font.pointSize: 18 * dpi_ratio
                        maximumLength: 30  * dpi_ratio;
                        clip: true;
                        text:supplier_name
                        onFocusChanged: {
                            if (search_input.focus) {
                                supplierRec.border.color = "#00BD75"
                            } else {
                                supplierRec.border.color = ST.color_grey_border2
                                fuzzy_search_rec.visible = false
                            }
                        }

                        MouseArea
                        {

                            anchors.fill: search_input;
                            onPressed:
                            {

                                window_root.keyboard_c.closeAll()
//                               window_root.keyboard_c.setDigitalKeyboardPos(1160 * dpi_ratio, 320 * dpi_ratio)
//                               window_root.keyboard_c.openDigitalKeyboard()
                                window_root.keyboard_c.setNormalKeyboardPos(725 * dpi_ratio, 565 * dpi_ratio)
                                window_root.keyboard_c.openNormalKeyboard()

                                search_input.focus = true;
//                                search_input.text = "";
                                searchOpen = !searchOpen
                                if(searchOpen){
                                    fuzzy_search_model.clear()
                                    fuzzy_search_model.append({
                                                         "supplier_name":"新增供货商   +",
                                                         "supplier_unique": ""
                                                     })
                                    for (var i = 0; i < lm_supplier.count; ++i) {

                                        var item = lm_supplier.get(i)
                                        supplier_nameTemp = item.supplier_name
                                        if (supplier_nameTemp.toString().includes(search_input.text.toString())) {
                                            fuzzy_search_model.append({
                                                                 "supplier_name":item.supplier_name,
                                                                 "supplier_unique": item.supplier_unique
                                                             })

                                        }
                                    }
                                    fuzzy_search_rec.visible = true
                                }
                                else{
                                    fuzzy_search_rec.visible = false
                                    fuzzy_search_model.clear()
                                }

                            }
                        }
                        onAccepted:
                        {
                        }
                        onTextChanged:
                        {
                            if(selectOpen && search_input.focus){
                                fuzzy_search_model.clear()
                                fuzzy_search_model.append({
                                                     "supplier_name":"新增供货商   +",
                                                     "supplier_unique": ""
                                                 })
                                for (var i = 0; i < lm_supplier.count; ++i) {

                                    var item = lm_supplier.get(i)
                                    supplier_nameTemp = item.supplier_name
                                    if (supplier_nameTemp.toString().includes(search_input.text.toString())) {
                                        fuzzy_search_model.append({
                                                             "supplier_name":item.supplier_name,
                                                             "supplier_unique": item.supplier_unique
                                                         })

                                    }
                                }
                                fuzzy_search_rec.visible = true
                            }

                        }
                    }
                    }
                    //                    CusComboBox {
//                        id: combo_supplier
//                        anchors.verticalCenter: parent.verticalCenter
//                        width: parent.width
//                        height: 65 * dpi_ratio
//                        font.family: ST.fontFamilyYaHei
//                        font.pointSize: 18 * dpi_ratio
//                        textRole: "supplier_name"
//                        model: ListModel {
//                            id: lm_supplier
//                        }
//                        onCurrentIndexChanged: {
//                            if (currentIndex == -1) {
//                               goods_supplier = ""
//                            } else {
//                                goods_supplier = lm_supplier.get(currentIndex).supplier_unique
//                            }
//                        }
//                    }
                }
                }
            Rectangle//
            {
                id:fuzzy_search_rec
                anchors.left:parent.left;
                anchors.leftMargin:150 * dpi_ratio
                anchors.top:supplierNameCus.bottom;
                anchors.topMargin: -14 * dpi_ratio;
                visible: false;
                width: 535 * dpi_ratio;
                height: 230 * dpi_ratio;
//                color:"#FFFFFF"
                color:ST.color_white
                border.width: 1  * dpi_ratio;
                border.color: "#00BD75";
                radius: 10  * dpi_ratio;
                z:100;
                ListView
                {
                    id:textListView
                    anchors.top: fuzzy_search_rec.top;
                    anchors.topMargin: 4 *dpi_ratio
                    anchors.left: parent.left;
                    anchors.leftMargin:3 *dpi_ratio
                    visible: fuzzy_search_rec.visible;
                    width: 529 * dpi_ratio;
                    clip:true
                    height: 222 * dpi_ratio;
                    model:fuzzy_search_model
                    delegate: Rectangle
                    {
                        id: textListViewDelegate;
                        width:  529 * dpi_ratio;
                        height: 50 * dpi_ratio;
                        color:"#FFFFFF"
                        anchors.horizontalCenter:textListView.horizontalCenter;
                        Rectangle
                        {
                            id:textListViewDelegate1
                            anchors.left:textListViewDelegate.left
                            anchors.verticalCenter: textListViewDelegate.verticalCenter
                            width:  236 * dpi_ratio;
                            height: 50 * dpi_ratio;
                            Text
                            {
                                id:textListViewDelegateText1
                                anchors.left: parent.left
                                anchors.leftMargin: 15 *dpi_ratio
                                anchors.verticalCenter: parent.verticalCenter
                                font.family: ST.fontFamilyYaHei;
                                font.pointSize: 22 * dpi_ratio;
                                color: supplier_name === "新增供货商   +"? "#082CA4" : "#333333";
                                text: supplier_name.length >10? (supplier_name.substring(0,10) + "..."):supplier_name;
                            }
                        }
                        MouseArea
                        {
                            anchors.fill:textListViewDelegate;
                            onClicked:
                            {
                                textListViewDelegateText1.color="red"
                                fuzzy_search_timer.start()
                                if(supplier_name.toString() =="新增供货商   +"){
                                    showAddSupplier()
                                    fuzzy_search_rec.visible = false
                                }else{
                                    for (var i = 0; i < lm_supplier.count; ++i) {
                                        var item = lm_supplier.get(i)
                                        supplier_nameTemp = item.supplier_name
                                        if (supplier_name.toString() === supplier_nameTemp.toString()) {
                                            goods_supplier =  item.supplier_unique
                                        }
                                    }
                                    search_input.text = supplier_name
                                    fuzzy_search_rec.visible = false
                                    fuzzy_search_model.clear()
                                    selectOpen = false
                                }
                            }
                            function showAddSupplier() {
                                window_root.loader_4_confirm.sourceComponent = compo_show_add_supplier
                                window_root.loader_4_confirm.item.open()
                            }

                            Component {
                                id: compo_show_add_supplier

                                PopupSupplierManger {
                                    id: supplier_add_confirm
                                    title_name: qsTr("新增供货商")
                                    onConfirm: {

                                        supplierControl.addSupplier4Qml(function (is_succ, data) {
                                            if (is_succ) {
                                                toast.openInfo(qsTr("供货商新增成功"))
                                                supplierControl.reqSupplier4Qml(function callback(is_succ, data) {
                                                    if (is_succ)
                                                        refreshSupplier()
                                                        logMgr.logEvtInfo4Qml("更新本地供应商成功")
                                                })
                                            } else {
                                                var json_doc = JSON.parse(data)
                                                var json_doc_data = json_doc["msg"]
                                                toast.openInfo(json_doc_data);
                                            }
                                        }, supplierName)
                                    }
                                    onSigClose: {
                                        search_input.forceActiveFocus()
                                    }
                                }
                            }
                        }
                        Timer
                        {
                            id: fuzzy_search_timer;
                            interval: 100;
                            repeat: false;
                            running: false;
                            onTriggered:
                            {
                                textListViewDelegate1.color="#ffffff"
                                textListViewDelegateText1.color="#333333"
                            }
                        }
                    }
                    ListModel
                    {
                        id:fuzzy_search_model
                    }
                }
            }
            CusRect{
                id: goodsStock
                Layout.fillWidth: true
                height: 80 *dpi_ratio
                color: ST.color_transparent
                anchors.left: parent.left
                anchors.top: supplierNameCus.bottom
                RectLable {
                    id:goodsStockRec
                    height: 80 *dpi_ratio
                    width: 140 * dpi_ratio
                    is_required: true
                    color:ST.color_transparent
                    text: qsTr("商品库存")
                }
                CusTextField {
                    id:goodsStockCus
                    anchors.verticalCenter: parent.verticalCenter
                    height: 50 *dpi_ratio
                    width: 136 * dpi_ratio
                    anchors.left:goodsStockRec.right
                    anchors.leftMargin: 10 * dpi_ratio
                    font.family: ST.fontFamilyYaHei
                    font.pointSize: 18 * dpi_ratio
                    enabled: false
                    text: goods_stock
                    onTextEdited: {
                    }
                    normal_keyboard_x: 725 * dpi_ratio
                    normal_keyboard_y: 565 * dpi_ratio
                    digital_keyboard_x: 1160 * dpi_ratio
                    digital_keyboard_y: 435 * dpi_ratio
                }
                CusComboBox {
                    id: combo_goods_unit
                    width: 108 * dpi_ratio
                    height: 50 * dpi_ratio
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.left:goodsStockCus.right
                    anchors.leftMargin: 10 * dpi_ratio
                    model: lm_goods_unit
                    textRole: "goods_unit"
                    font.pixelSize: 22 * dpi_ratio
                    ListModel {
                        id: lm_goods_unit
                    }
                    onCurrentIndexChanged: {
                        if (currentIndex == -1)
                            goods_unit = ""
                        else
                            goods_unit = lm_goods_unit.get(currentIndex).goods_unit
                    }
                }
                CusRect {
                    height: 80 *dpi_ratio
                    width: 270 * dpi_ratio
                    anchors.left:combo_goods_unit.right
                    anchors.leftMargin: 30 * dpi_ratio
                    color:ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 25 * dpi_ratio

                        CusButton {
                            Layout.preferredHeight: 50 * dpi_ratio
                            Layout.preferredWidth: 110 * dpi_ratio
                            text: qsTr("入库")
                            Layout.alignment: Qt.AlignVCenter

                            visible: true

                            onClicked: {
                                if(goods_barcode != ""){
                                    showStockChangeIn(goods_barcode, 1,goodStockPrice,goods_unit,goods_edit_cntr_root.goods_cheng_type)
                                }else{
                                    toast.openWarn(qsTr("未选择商品!"))
                                }
                            }
                        }

                        CusButton {
                            Layout.preferredHeight: 50 * dpi_ratio
                            Layout.preferredWidth: 110 * dpi_ratio
                            text: qsTr("出库")
                            Layout.alignment: Qt.AlignVCenter
                            color: ST.color_orange

                            visible: true

                            onClicked: {
                                if(goods_barcode != ""){
                                    if(shopControl.goodsInPriceType == 2){
                                       showStockChangeOutFirst(goods_edit_cntr_root.goods_barcode, 1,goods_edit_cntr_root.goods_unit,goodStockPrice,goods_edit_cntr_root.goods_cheng_type);
                                    }
                                    else if(shopControl.goodsInPriceType == 1 || shopControl.goodsInPriceType == 0){
                                       showStockChangeOutMean(goods_edit_cntr_root.goods_barcode, 1,goods_edit_cntr_root.goods_unit,goodStockPrice,goods_edit_cntr_root.goods_cheng_type);
                                    }
                                }else{
                                    toast.openWarn(qsTr("未选择商品!"))
                                }
                            }
                        }

                        CusSpacer {
                            Layout.fillWidth: true
                        }
                    }
                }
            }
            CusRect{
                id: moreInformation
                Layout.fillWidth: true
                height: 80 *dpi_ratio
                color: ST.color_transparent
                anchors.left: parent.left
                anchors.leftMargin: 18 *dpi_ratio
                anchors.top: goodsStock.bottom
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.left: parent.left
                    anchors.leftMargin: 20 *dpi_ratio
                    font.family: ST.fontFamilyYaHei
                    font.pointSize: 20 * dpi_ratio
                    color: "#00BD75"
                    text: qsTr("更多商品信息 >")
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked:
                    {
                      openGoodsManger()
                      logMgr.logDataInfo4Qml("点击更多商品信息")
                    }
                }
            }
            Rectangle{
                id: bottomButton
                Layout.fillWidth: true
                height: 110 *dpi_ratio
                color: ST.color_transparent
                anchors.left: parent.left
                anchors.top: moreInformation.bottom
                anchors.topMargin: 30 * dpi_ratio
                RowLayout {
                    anchors.fill: parent
                    spacing: 25 * dpi_ratio
                    CusRect{
                        id:cleanButton
                        height: 90 * dpi_ratio
                        width: 190 * dpi_ratio
                        anchors.left: parent.left
                        anchors.leftMargin: 40 *dpi_ratio
                        Layout.alignment: Qt.AlignVCenter
                        color: "#FF9500"
                        radius: 10 *dpi_ratio
                        Image
                        {
                            id: cleanButton_image
                            width:32 *dpi_ratio
                            height:28 *dpi_ratio
                            anchors.left: parent.left
                            anchors.leftMargin: 40 *dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            source: "/Images/clear.png"
                        }
                        Text {
                            id: cleanButton_text
                            text: qsTr("清空")
                            color:"#FFFFFF"
                            font.family: ST.fontFamilyYaHei
                            anchors.verticalCenter: parent.verticalCenter
                            font.pointSize: 22 * dpi_ratio
                            anchors.left: cleanButton_image.right
                            anchors.leftMargin: 10 * dpi_ratio
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        MouseArea {
                            anchors.fill: parent
                            onClicked:
                            {
                              clearAllData()
                            }
                        }
                    }
                    CusRect{
                        id:cancleButton
                        height: 90 * dpi_ratio
                        width: 190 * dpi_ratio
                        anchors.left: cleanButton.right
                        anchors.leftMargin: 40 *dpi_ratio
                        Layout.alignment: Qt.AlignVCenter
                        color: "#FF3835"
                        radius: 10 *dpi_ratio
                        Image
                        {
                            id: cancleButton_image
                            width:32 *dpi_ratio
                            height:28 *dpi_ratio
                            anchors.left: parent.left
                            anchors.leftMargin: 40 *dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            source: "/Images/delete.png"
                        }
                        Text {
                            id: cancleButton_text
                            text: qsTr("删除")
                            color:"#FFFFFF"
                            font.family: ST.fontFamilyYaHei
                            anchors.verticalCenter: parent.verticalCenter
                            font.pointSize: 22 * dpi_ratio
                            anchors.left: cancleButton_image.right
                            anchors.leftMargin: 10 * dpi_ratio
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        MouseArea {
                            anchors.fill: parent
                            onClicked:
                            {
                                if(goods_barcode != ""){
                                    showDelCurGoodsConfirm(goods_barcode)
                                }
                            }
                        }
                    }
                    CusRect{
                        height: 90 * dpi_ratio
                        width: 190 * dpi_ratio
                        anchors.left: cancleButton.right
                        anchors.leftMargin: 40 *dpi_ratio
                        Layout.alignment: Qt.AlignVCenter
                        color: "#00BD75"
                        radius: 10 *dpi_ratio
                        Image
                        {
                            id: saveButtom_image
                            width:32 *dpi_ratio
                            height:28 *dpi_ratio
                            anchors.left: parent.left
                            anchors.leftMargin: 40 *dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            source: "/Images/duihao1.png"
                        }
                        Text {
                            id: btn_text
                            text: qsTr("保存")
                            color:"#FFFFFF"
                            font.family: ST.fontFamilyYaHei
                            anchors.verticalCenter: parent.verticalCenter
                            font.pointSize: 22 * dpi_ratio
                            anchors.left: saveButtom_image.right
                            anchors.leftMargin: 10 * dpi_ratio
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        MouseArea {
                            anchors.fill: parent
                            onClicked:
                            {
                                    updateGoods_v2()
                            }
                        }
                    }
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                }
            }

        }
    }
    Connections {
        target: mqttControl
        function onSigMqttRefreshGoodsByBarcode4Qml(goods_barcode) {
            var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))
            if (cur_goods) {
                if (goods_edit_cntr_root.goods_barcode == goods_barcode )
                    addItemByBarcode(goods_barcode)
            }
        }
    }
}
