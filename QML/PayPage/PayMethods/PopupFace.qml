﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import EnumTool 1.0

import "../.."

Item {
    id: popup_root

    function open() {
        //cameraControl.openFaceCamera()

        CameraWorker2.isNeed_face_detected_ = true
        ret_pay_status = EnumTool.FACE_PAY_STATUS__UNKNOW
        popup_root.visible = true
        payMethodControl.is_need_face_pay = true
        face_img.refresh()
    }

    function close() {
        if (!popup_root.visible)
            return
        CameraWorker2.isNeed_face_detected_ = false
        //cameraControl.closeFaceCamera()
        payMethodControl.is_need_face_pay = false
        if(popup_root.visible){
            pay_page.payStatus = true
        }
        popup_root.visible = false
        utils4Qml.sigPopupFacePayClose()
    }

    property string title_name: qsTr("人脸支付")

    property int ret_pay_status: EnumTool.FACE_PAY_STATUS__UNKNOW
    property string other_err_str: ""

    state: if (payMethodControl.is_need_face_pay) {
               if (payMethodControl.is_face_paying) {
                   return "success"
               } else {
                   return "detecting"
               }
           } else {
               switch (ret_pay_status) {
               case EnumTool.FACE_PAY_STATUS__BALANCE_INSUFFICIENT:
                   // return "balance_insufficient"
               case EnumTool.FACE_PAY_STATUS__REQ_ERROR:
               case EnumTool.FACE_PAY_STATUS__ORDER_DUPLICATE:
                   return "other_error"
               default:
                   return "normal"
               }
           }

    states: [
        State {
            name: "normal"
            PropertyChanges {
                target: rect_pay_status_info
                border.color: ST.color_grey_border
            }
            PropertyChanges {
                target: t_pay_status_info
                text: ""
            }
        },
        State {
            name: "success"
            PropertyChanges {
                target: rect_pay_status_info
                border.color: ST.color_green
            }
            PropertyChanges {
                target: t_pay_status_info
                text: qsTr("识别成功")
                color: ST.color_green
            }
        },
        State {
            name: "detecting"
            PropertyChanges {
                target: rect_pay_status_info
                border.color: ST.color_yellow
            }
            PropertyChanges {
                target: t_pay_status_info
                text: qsTr("识别中...")
                color: ST.color_yellow
            }
        },
        State {
            name: "balance_insufficient"
            PropertyChanges {
                target: rect_pay_status_info
                border.color: ST.color_red
            }
            PropertyChanges {
                target: t_pay_status_info
                text: qsTr("余额不足")
                color: ST.color_red
            }
        },
        State {
            name: "other_error"
            PropertyChanges {
                target: rect_pay_status_info
                border.color: ST.color_red
            }
            PropertyChanges {
                target: t_pay_status_info
                text: other_err_str
                color: ST.color_red
            }
        }
    ]

    MouseArea {
        anchors.fill: parent
        onClicked: {
            close()
        }
    }

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
    }

    Rectangle {
        id: popup_contain_root
        width: 750 * dpi_ratio
        height: 800 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        onVisibleChanged: {
            x = (parent.width - width) / 2
            y = (parent.height - height) / 2
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio * configTool.fontRatio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35 * ST.dpi
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        CusImg {
                            id: face_img
                            width: 450 * ST.dpi
                            anchors.horizontalCenter: parent.horizontalCenter
                            height: width
                            source: "image://FaceImg"
                            cache: false

                            layer.enabled: true
                            layer.effect: OpacityMask {
                                maskSource: Rectangle {
                                    width: face_img.width
                                    height: face_img.height
                                    radius: face_img.width / 2
                                }
                            }

                            Connections {
                                target: cameraControl
                                function onSigSendFaceImg() {
                                    face_img.refresh()
                                }
                            }
                        }

                        CusRect {
                            id: rect_pay_status_info
                            width: face_img.width
                            height: face_img.height
                            anchors.centerIn: face_img
                            color: ST.color_transparent
                            radius: width / 2
                            border {
                                width: 5 * ST.dpi
                                color: ST.color_grey_border
                            }

                            CusText {
                                id: t_pay_status_info
                                color: payMethodControl.is_face_paying ? ST.color_green : ST.color_yellow
                                text: ""
                                anchors.centerIn: parent
                                font.pixelSize: 35 * ST.dpi * configTool.fontRatio
                                font.bold: true
                                opacity: .7
                            }
                        }
                    }

                    Item {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 80 * dpi_ratio

                        RowLayout {
                            anchors.fill: parent
                            anchors.leftMargin: 34 * dpi_ratio
                            anchors.rightMargin: anchors.leftMargin

                            CusButton {
                                visible: !payMethodControl.is_need_face_pay
                                Layout.preferredWidth: 350 * dpi_ratio
                                Layout.fillHeight: true
                                Layout.alignment: Qt.AlignHCenter
                                text: qsTr("重新识别")
                                font.pixelSize: 34 * dpi_ratio * configTool.fontRatio
                                color: ST.color_blue
                                onClicked: {
                                    payMethodControl.is_need_face_pay = true
                                }
                            }
                        }
                    }
                }

                CusRect {
                    width: parent.width
                    height: 80 * dpi_ratio
                    anchors.horizontalCenter: parent.horizontalCenter
                    y: 480 * dpi_ratio
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("消费金额 ") + shopCartList.getFinalTotalGoodsPrice() + qsTr(" 元")
                        anchors.centerIn: parent
                        font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                        font.bold: true

                        onVisibleChanged: {
                            if (!visible)
                                return
                            text = qsTr("消费金额 ") + shopCartList.getFinalTotalGoodsPrice() + qsTr(" 元")
                        }
                    }
                }
            }
        }
    }

    Connections {
        target: cameraControl
        function onSigFaceDetected(num) {
            if (!payMethodControl.is_need_face_pay || payMethodControl.is_face_paying)
                return

            payMethodControl.is_face_paying = true

            payMethodControl.getMemberInfoByLastFaceImg(function (pay_status, data) {
                ret_pay_status = pay_status
                switch (pay_status) {
                case EnumTool.FACE_PAY_STATUS__SUCC:
                    close()
                    showPayResults(EnumTool.PAY_STATUS__SUCCESS, EnumTool.PAY_METHOD__FACE, data)
                    break
                case EnumTool.FACE_PAY_STATUS__FACE_NOT_FOUND:
                    // 继续检测
                    toast.openError(qsTr("无此人脸信息"))
                    break
                case EnumTool.FACE_PAY_STATUS__ORDER_DUPLICATE:
                    payMethodControl.is_need_face_pay = false
                    other_err_str = qsTr("订单重复")
                    break
                case EnumTool.FACE_PAY_STATUS__REQ_ERROR:
                    payMethodControl.is_need_face_pay = false
                    other_err_str = qsTr("网络错误")
                    break
                case EnumTool.FACE_PAY_STATUS__BALANCE_INSUFFICIENT:
                    payMethodControl.is_need_face_pay = false
                    other_err_str = qsTr("余额不足")
                    break
                }

                payMethodControl.is_face_paying = false
            },shopCartList.getFinalTotalGoodsPrice())
        }
    }
}
