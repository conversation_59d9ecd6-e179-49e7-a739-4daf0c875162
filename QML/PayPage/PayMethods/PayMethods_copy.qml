﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15
import QtGraphicalEffects 1.15
import EnumTool 1.0
import SettingEnum 1.0
import "../.."

CusRect {
    radius: ST.radius
    color: ST.color_transparent

    function showPayResults(pay_status_enum, pay_method_enum, json_detail) {
        window_root.loader_4_pay_status.sourceComponent = popup_pay_results
        window_root.loader_4_pay_status.item.open(pay_status_enum, pay_method_enum, json_detail)

        second_window.loader_4_pay_status_second.sourceComponent = popup_pay_results
        second_window.loader_4_pay_status_second.item.open(pay_status_enum, pay_method_enum, json_detail)
    }

    function hidePayResults() {
        window_root.loader_4_pay_status.item.close()
        second_window.loader_4_pay_status_second.item.close()
    }
    Connections {
        target: utils4Qml
        function onSigPopupPayResultsClose() {
            hidePayResults()
        }
    }

    function showFaceVideo() {
        window_root.loader4_face.sourceComponent = popup_face
        window_root.loader4_face.item.open()
    }

    function showPayMethod() {
        window_root.loader4_cash_pay.sourceComponent = popup_cash_pay
        window_root.loader4_cash_pay.item.open()
    }

    function showVipCardMethod() {
        if (cur_using_member_unique == "") {
            toast.openWarn(qsTr("请选择会员"))
            return
        }

        window_root.loader4_vipCard_pay.sourceComponent = popup_cash_pay
        window_root.loader4_vipCard_pay.item.open(EnumTool.POPUPCASHPAY_MEMBERSHIP_CARD)
    }

    function showCombinedPopup() {
        window_root.loader4_combined.sourceComponent = popup_combined
        window_root.loader4_combined.item.open()
    }

    //TODO 切换商品管理
    function activeGoodsManager(is_active) {
        logMgr.logEvtInfo4Qml("{}商品管理", is_active ? "打开" : "关闭")
        pay_page_root.is_mamager_model = is_active
        if (!is_active)
            search_bar.forceActiveFocus()
        if (!is_active) {
            search_bar.refresh_content()
            window_root.keyboard_c.closeAll()
        }
    }

    function payByPaymentCode(payment_code) {
        payMethodControl.payByPaymentCode(showPayResults, payment_code)
    }

    function refreshPayMethod() {
        list_model1.clear()
        var json_doc = JSON.parse(payMethodControl.getEnabledPayMethodStatusListJson())

        for (var i = 0; i < json_doc.length; ++i) {
            var cur_item = json_doc[i]

            switch (cur_item.pay_enum) {
            case EnumTool.PAY_METHOD__CASH:
                list_model1.append({
                                       "name": "现金",
                                       "pay_method": EnumTool.PAY_METHOD__CASH,
                                       "is_check_btn": false
                                   })
                break
            case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
                list_model1.append({
                                       "name": /*'<font size="1"> </font>'+ "<br>" + */ "支付宝" + "<br>" + '<font size="2">(线下)</font>',
                                       "pay_method": EnumTool.PAY_METHOD__ALIPAY_OFFLINE,
                                       "is_check_btn": false
                                   })
                break
            case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
                list_model1.append({
                                       "name": /*'<font size="1"> </font>'+ "<br>" + */ "微信" + "<br>" + '<font size="2">(线下)</font>',
                                       "pay_method": EnumTool.PAY_METHOD__WECHAT_OFFLINE,
                                       "is_check_btn": false
                                   })
                break
            case EnumTool.PAY_METHOD__FACE:
                list_model1.append({
                                       "name": "人脸\n支付",
                                       "pay_method": EnumTool.PAY_METHOD__FACE,
                                       "is_check_btn": false
                                   })
                break
            case EnumTool.PAY_METHOD__VIPCARD:
                list_model1.append({
                                       "name": "储值卡\n支付",
                                       "pay_method": EnumTool.PAY_METHOD__VIPCARD,
                                       "is_check_btn": false
                                   })
                break
            case EnumTool.PAY_METHOD__COMBINED:
                list_model1.append({
                                       "name": "组合\n支付",
                                       "pay_method": EnumTool.PAY_METHOD__COMBINED,
                                       "is_check_btn": false
                                   })
                break
            case EnumTool.PAY_METHOD__GOODS_MANAGER:
                list_model1.append({
                                       "name": "商品\n管理",
                                       "pay_method": EnumTool.PAY_METHOD__GOODS_MANAGER,
                                       "is_check_btn": true
                                   })
                break
            }
        }
    }

    onVisibleChanged: {
        if (visible) {
            refreshPayMethod()
        }
    }

    RowLayout {
        anchors.fill: parent
        focus: true
        spacing: 10

        Repeater {
            model: ListModel {
                id: list_model1
            }

            delegate: CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                Layout.maximumWidth: 260 * dpi_ratio
                Layout.preferredWidth: 100
                id: rect_pay_method
                color: ST.color_transparent

                property bool is_press: mouse_area1.pressed
                property bool is_checked: false
                property bool is_down: is_check_btn ? is_checked : is_press

                Connections {
                    target: utils4Qml
                    function onReturnToPayPage() {
                        rect_pay_method.is_checked = false
                    }
                }

                Image {
                    anchors.fill: parent
                    source: if (is_down) {
                                return index % 2 ? "/Images/blue_btn_down.png" : "/Images/green_btn_down.png"
                            } else {
                                return index % 2 ? "/Images/blue_btn_up.png" : "/Images/green_btn_up.png"
                            }
                }

                CusText {
                    anchors.horizontalCenter: parent.horizontalCenter
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.verticalCenterOffset: is_down ? -2 * dpi_ratio : -18 * dpi_ratio
                    font.pixelSize: 40 * dpi_ratio * configTool.fontRatio2
                    text: name
                    color: ST.color_white_pure
                    horizontalAlignment: Text.AlignHCenter
                    font.bold: true
                    lineHeight: .8
                }

                MouseArea {
                    id: mouse_area1
                    anchors.fill: parent

                    onClicked: {
                        if (is_check_btn) {
                            rect_pay_method.is_checked = !rect_pay_method.is_checked
                        }

                        if (pay_method == EnumTool.PAY_METHOD__GOODS_MANAGER) {
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_PAY__GOODS_MGR)) {
                                activeGoodsManager(rect_pay_method.is_checked)
                            } else {
                                toast.openWarn("无此权限")
                            }
                            return
                        }

                        if (shopCartList.getTotalGoodsQuantity() == 0) {
                            toast.openWarn("购物车内无商品")
                            return
                        }

                        if (pay_method == EnumTool.PAY_METHOD__FACE) {
                            showFaceVideo()
                            return
                        }

                        if (pay_method == EnumTool.PAY_METHOD__CASH) {

                            focus = true

                            showPayMethod()

                            if (settingTool.getSetting(SettingEnum.IS_OPEN_CASH_BOX_BEFORE_PAY)) {
                                logMgr.logEvtInfo4Qml("===========================开启钱箱3==============================")
                                printerControl.openCashBox()
                            }

                            return
                        }

                        if (pay_method == EnumTool.PAY_METHOD__VIPCARD) {
                            showVipCardMethod()
                            return
                        }

                        if (pay_method == EnumTool.PAY_METHOD__COMBINED) {
                            showCombinedPopup()

                            if (settingTool.getSetting(SettingEnum.IS_OPEN_CASH_BOX_BEFORE_PAY)) {
                                logMgr.logEvtInfo4Qml("===========================开启钱箱4==============================")
                                printerControl.openCashBox()
                            }
                            return
                        }
                        payMethodControl.payByPayMethod(showPayResults, pay_method)
                    }
                }

                layer.enabled: true // Set Layer for Enable
                layer.effect: OpacityMask {
                    maskSource: Rectangle {
                        width: rect_pay_method.width
                        height: rect_pay_method.height
                        radius: ST.radius
                    }
                }
            }
        }

        CusSpacer {
            Layout.fillWidth: true
        }
    }

    Component {
        id: popup_pay_results
        PopupPayResults {
            onSigClose: {
                search_bar.forceActiveFocus()
                shopCartList.tryResetPaidOrder()
            }
        }
    }

    Component {
        id: popup_cash_pay
        PopupCashPay {}
    }

    Component {
        id: popup_face
        PopupFace {}
    }

    Component {
        id: popup_combined
        PopupCombined {}
    }
}
