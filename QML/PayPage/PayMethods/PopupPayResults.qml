﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."

import EnumTool 1.0

Item {
    id: popup_root

    property int _pay_status_enum: EnumTool.PAY_STATUS__UNKNOW //支付状态
    property int _pay_method_enum: EnumTool.PAY_METHOD__UNKNOW //支付方式

    property string _title_name: qsTr("支付信息")

    property string _receivable: "0.00" //应收
    property string _actually_receive: "0.00" //实收
    property string _give_change: "0.00" //找零

    property string _mini_program_discount: "0.00" //小程序优惠券
    property string _mini_program_beans_use: "0.00" //小程序百货豆抵扣
    property string _mini_program_beans_give: "0.00" //小程序百货豆赠送
    property string _mini_program_balance: "0.00" //小程序余额
    property string _mini_program_wechat: "0.00" //小程序微信支付

    property string _face_nick_name: qsTr("未授权")
    property string _face_phone: qsTr("未授权")
    property string _face_actually_received: "0.00"
    property string _face_wechat: "0.00" //刷脸微信支付
    property string _face_balance: "0.00" //刷脸余额支付
    property string _face_beans_use: "0.00" //刷脸百货豆


    function open(pay_status_enum, pay_method_enum, json_detail) {
        _receivable = "0.00" //应收
        _actually_receive = "0.00" //实收
        _give_change = "0.00" //找零
        _mini_program_beans_use = "0.00" //百货豆付
        _mini_program_discount = "0.00" //优惠券抵扣
        _mini_program_beans_give = "0.00" //奖励豆
        _mini_program_balance = "0.00" //余额付
        _pay_status_enum =  EnumTool.PAY_STATUS__UNKNOW
        _pay_status_enum = pay_status_enum
        logMgr.logEvtInfo4Qml("支付状态为：{}",_pay_status_enum)
        _pay_method_enum = pay_method_enum

        var json_doc = JSON.parse(json_detail)
        logMgr.logEvtInfo4Qml("[PopupPayResult]open {}", JSON.stringify(json_doc) + " pay_method_enum " + pay_method_enum)
        // 应收: receivable_amount
        // 实收: received_amount
        // 找零: change_amount
        // 订单号: order_unique
        switch (pay_method_enum) {
        case EnumTool.PAY_METHOD__CASH:
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
        case EnumTool.PAY_METHOD__VIPCARD:
        case EnumTool.PAY_METHOD__COMBINED:
            _receivable = Number(json_doc.receivable_amount).toFixed(2)
            _actually_receive = Number(json_doc.received_amount).toFixed(2)
            _give_change = Number(json_doc.change_amount).toFixed(2)
            break
        case EnumTool.PAY_METHOD__ALIPAY_ONLINE:
        case EnumTool.PAY_METHOD__WECHAT_ONLINE:
        case EnumTool.PAY_METHOD__UNIONPAY:
            _actually_receive = Number(json_doc.received_amount).toFixed(2)
            break
        case EnumTool.PAY_METHOD__FACE:
            _face_phone = json_doc.data.cus_phone
            _face_actually_received = json_doc.data.sale_list_actually_received
            _face_nick_name = json_doc.data.pc_nick_name
            //_face_wechat = json_doc.data.pc_nick_name
            _face_balance = json_doc.data.card_deduction
            _face_beans_use = json_doc.data.beans_use
            break
        case EnumTool.PAY_METHOD__GOODS_MANAGER:
        case EnumTool.PAY_METHOD__YI_TONG:
            break
        case EnumTool.PAY_METHOD__MINI_PROGRAM:

            //beans_get":0, //奖励豆
            //"beans_money":0,//百货豆付
            //"card_deduction":0.02,//余额付
            //"coupon_amount":0,//优惠券抵扣
            //"sale_list_actually_received":0.02,//实收金额
            //"sale_list_total":0.02 //应收

            // 小程序优惠券: mini_program_discount
            // 小程序百货豆抵扣: mini_program_beans_use
            // 小程序百货豆赠送: mini_program_beans_give
            // 小程序余额: mini_program_balance
            // 小程序微信支付: mini_program_wechat
            _receivable = Number(json_doc.receivable_amount).toFixed(2)
            _actually_receive = Number(json_doc.received_amount).toFixed(2)

            if (!json_doc.hasOwnProperty("mini_program_discount")) {
                _mini_program_discount = qsTr("加载中")
                _mini_program_beans_use = qsTr("加载中")
                _mini_program_beans_give = qsTr("加载中")
                _mini_program_balance = qsTr("加载中")
                _mini_program_wechat = qsTr("加载中")
            } else {
                _mini_program_discount = Number(json_doc.mini_program_discount).toFixed(2)
                _mini_program_beans_use = Number(json_doc.mini_program_beans_use).toFixed(2)
                _mini_program_beans_give = Number(json_doc.mini_program_beans_give).toFixed(2)
                _mini_program_balance = Number(json_doc.mini_program_balance).toFixed(2)
                _mini_program_wechat = Number(json_doc.mini_program_wechat).toFixed(2)
                popup_root.visible = true
            }
            break
        case EnumTool.PAY_METHOD__SCAN:
            if (!!json_doc.received_amount)
                _actually_receive = Number(json_doc.received_amount).toFixed(2)
            if (!!json_doc.receivable_amount)
                _receivable = Number(json_doc.receivable_amount).toFixed(2)
            else
                _receivable = _actually_receive
            if (!!json_doc.change_amount)
                _give_change = Number(json_doc.change_amount).toFixed(2)
            break
        }
        if(pay_method_enum !== EnumTool.PAY_METHOD__MINI_PROGRAM){
            popup_root.visible = true
        }
        else{
        }

        sigOpen()
    }

    function close() {
        if (popup_root.visible) {
            popup_root.visible = false
            shopControl.reqShopBeans()
            sigClose()
        }
    }
    signal sigPaySucc
    signal sigPayStop

    signal sigOpen
    signal sigClose
    signal sigButStatus

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onPressed: {
                if (_pay_status_enum == EnumTool.PAY_STATUS__PAYING)
                    return

                if (_pay_status_enum == EnumTool.PAY_STATUS__SUCCESS){
                    logMgr.logEvtInfo4Qml("支付成功下点击弹窗以外关闭弹窗触发清空购物车")
                }
                 shopCartList.resetAllInfo()
                 utils4Qml.sigPopupPayResultsClose()
                popup_root.visible =false
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 600 * dpi_ratio
        height: 750 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        onVisibleChanged: {
            x = (parent.width - width) / 2
            y = (parent.height - height) / 2
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }

                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: _title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 25
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 10 * dpi_ratio

                    CusRect {
                        //收款进程提示
                        id: rec_status
                        Layout.fillWidth: true
                        Layout.preferredHeight: 170 * dpi_ratio
                        color: ST.color_transparent
                        ColumnLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.alignment: Qt.AlignHCenter
                                Layout.preferredWidth: 100 * dpi_ratio
                                Layout.preferredHeight: width
                                color: ST.color_transparent

                                CusRect {
                                    visible: _pay_method_enum == EnumTool.PAY_METHOD__FACE
                                    anchors.fill: parent
                                    color: ST.color_transparent

                                    CusImg {
                                        id: face_img
                                        anchors.fill: parent
                                        source: "image://FaceImg"

                                        // Component.onCompleted: {
                                        //     face_img.refresh()
                                        // }

                                        // onVisibleChanged: {
                                        //     if (visible)
                                        //         face_img.refresh()
                                        // }
                                        cache: false

                                        layer.enabled: true
                                        layer.effect: OpacityMask {
                                            maskSource: Rectangle {
                                                width: face_img.width
                                                height: face_img.height
                                                radius: face_img.width / 2
                                            }
                                        }

                                        Connections {
                                            target: cameraControl
                                            function onSigSendFaceImg() {
                                                face_img.refresh()
                                            }
                                        }
                                    }
                                }

                                CusRect {
                                    //收款中进度条
                                    visible: _pay_method_enum != EnumTool.PAY_METHOD__FACE
                                    anchors.fill: parent
                                    radius: width / 2
                                    color: _pay_status_enum == EnumTool.PAY_STATUS__PAYING ? ST.color_transparent : "#33CC67"
                                    AnimatedImage {
                                        anchors.centerIn: parent
                                        anchors.fill: parent

                                        source: if (_pay_status_enum == EnumTool.PAY_STATUS__SUCCESS) {
                                                    return "/Images/pay_icon_sucess.png"
                                                } else if (_pay_status_enum == EnumTool.PAY_STATUS__PAYING) {
                                                    playing = true
                                                    return "/Images/loading.gif"
                                                } else if(_pay_status_enum !== EnumTool.PAY_STATUS__UNKNOW){
                                                    return "/Images/yuebuzu_icon_solid.png"
                                                }
                                    }
                                }
                            }

                            CusText {
                                text: if (_pay_status_enum == EnumTool.PAY_STATUS__SUCCESS) {
                                          if(popup_root.visible){
                                            logMgr.logEvtInfo4Qml("订单成功触发清空购物车")
                                            shopCartList.resetAllInfo()
                                            sigButStatus()
                                            logMgr.logEvtInfo4Qml("收款成功 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                          }
                                          return qsTr("收款成功")
                                      } else if (_pay_status_enum == EnumTool.PAY_STATUS__PAYING) {
                                          return qsTr("收款中")
                                      } else if(_pay_status_enum !== EnumTool.PAY_STATUS__UNKNOW){
                                          if(popup_root.visible){
                                          logMgr.logEvtInfo4Qml("订单失败触发清空购物车")
                                          shopCartList.resetAllInfo()
                                          sigButStatus()
                                          logMgr.logEvtInfo4Qml("收款失败 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                          }
                                          return qsTr("收款失败")
                                      }else{
                                          return qsTr("未知状态")
                                      }

                                Layout.alignment: Qt.AlignHCenter
                                font.pixelSize: 28 * dpi_ratio
                            }
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent
                        Layout.leftMargin: 100 * dpi_ratio
                        Layout.rightMargin: 100 * dpi_ratio

                        // 现金
                        CusRect {
                            anchors.fill: parent
                            anchors.leftMargin: 60 * ST.dpi_ratio

                            visible: _pay_method_enum == EnumTool.PAY_METHOD__CASH || _pay_method_enum == EnumTool.PAY_METHOD__ALIPAY_OFFLINE
                                     || _pay_method_enum == EnumTool.PAY_METHOD__WECHAT_OFFLINE || _pay_method_enum == EnumTool.PAY_METHOD__VIPCARD
                                     || _pay_method_enum == EnumTool.PAY_METHOD__COMBINED

                            color: ST.color_transparent

                            ColumnLayout {
                                anchors.fill: parent

                                component PayResultItem: CusRect {
                                    id: rect_pay_result_item_root

                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 70 * dpi_ratio
                                    color: ST.color_transparent

                                    property string lable_str: ""
                                    property int lable_size: 22 * ST.dpi_ratio
                                    property string lable_color: ST.color_black

                                    property string value_str: ""
                                    property int value_size: 22 * ST.dpi_ratio
                                    property string value_color: ST.color_black

                                    RowLayout {
                                        anchors.fill: parent

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            Layout.preferredWidth: 10
                                            color: ST.color_transparent

                                            CusText {
                                                anchors.verticalCenter: parent.verticalCenter
                                                font.pixelSize: rect_pay_result_item_root.lable_size
                                                text: rect_pay_result_item_root.lable_str
                                                color: rect_pay_result_item_root.lable_color
                                            }
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            Layout.preferredWidth: 20
                                            color: ST.color_transparent

                                            CusText {
                                                anchors.verticalCenter: parent.verticalCenter
                                                font.pixelSize: rect_pay_result_item_root.value_size
                                                text: rect_pay_result_item_root.value_str
                                                color: rect_pay_result_item_root.value_color
                                            }
                                        }
                                    }
                                }

                                //应收金额
                                PayResultItem {
                                    visible: (_pay_status_enum == EnumTool.PAY_STATUS__SUCCESS)
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 70 * dpi_ratio

                                    lable_str: qsTr("应收:")
                                    lable_size: 30 * ST.dpi_ratio
                                    lable_color: ST.color_grey_font2

                                    value_str: qsTr("¥") + _receivable //"应收金额"
                                    value_size: 35 * ST.dpi_ratio
                                    value_color: ST.color_grey_font2
                                }

                                //实收金额
                                PayResultItem {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 70 * dpi_ratio

                                    lable_str: _pay_status_enum == EnumTool.PAY_STATUS__PAYING ? qsTr("待收款:") : qsTr("实收:")
                                    lable_size: 30 * ST.dpi_ratio

                                    value_str: qsTr("¥") + _actually_receive //"实收金额"
                                    value_size: 40 * ST.dpi_ratio
                                    value_color: ST.color_red
                                }

                                //实收金额
                                PayResultItem {
                                    visible: (_pay_status_enum == EnumTool.PAY_STATUS__SUCCESS)
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 70 * dpi_ratio

                                    lable_str: Number(_give_change) >= 0 ? qsTr("找零:") : qsTr("优惠:")
                                    lable_size: 30 * ST.dpi_ratio
                                    lable_color: _give_change == "0.00" ? ST.color_grey_font2 : ST.color_black

                                    value_str: qsTr("¥") + (Number(_give_change) > 0 ? _give_change : -1 * Number(_give_change)) //"找零金额"
                                    value_size: 35 * ST.dpi_ratio
                                    value_color: _give_change == "0.00" ? ST.color_grey_font2 : ST.color_red
                                }

                                CusSpacer {
                                    Layout.fillHeight: true
                                }
                            }
                        }

                        CusRect {
                            anchors.fill: parent
                            visible: (_pay_method_enum == EnumTool.PAY_METHOD__ALIPAY_ONLINE || _pay_method_enum == EnumTool.PAY_METHOD__WECHAT_ONLINE
                                      || _pay_method_enum == EnumTool.PAY_METHOD__UNIONPAY) && _pay_status_enum == EnumTool.PAY_STATUS__SUCCESS

                            anchors.topMargin: 20 * ST.dpi_ratio
                            color: ST.color_transparent

                            ColumnLayout {
                                anchors.fill: parent

                                spacing: 20 * ST.dpi_ratio

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 50 * ST.dpi_ratio
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("电子支付成功: ") + _actually_receive + qsTr("元")
                                        font.pixelSize: 30 * ST.dpi_ratio
                                    }
                                }

                                component OnlinePayItem: CusRect {
                                    id: online_pay_item_root

                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 85 * ST.dpi_ratio
                                    color: ST.color_transparent

                                    property string img_path: ""
                                    property string label_str: ""
                                    property string value_str: ""

                                    CusRect {
                                        width: parent.width
                                        height: 2 * ST.dpi_ratio
                                        anchors.top: parent.top
                                        color: ST.color_grey_border
                                    }

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * ST.dpi_ratio

                                        CusRect {
                                            Layout.alignment: Qt.AlignVCenter
                                            Layout.preferredHeight: 45 * ST.dpi_ratio
                                            Layout.preferredWidth: height
                                            color: ST.color_transparent

                                            Image {
                                                source: online_pay_item_root.img_path
                                                anchors.fill: parent
                                            }
                                        }

                                        CusRect {
                                            Layout.preferredWidth: 110 * ST.dpi_ratio
                                            Layout.fillHeight: true
                                            color: ST.color_transparent

                                            CusText {
                                                text: online_pay_item_root.label_str
                                                anchors.verticalCenter: parent.verticalCenter
                                                font.bold: true
                                                font.pixelSize: 30 * ST.dpi_ratio
                                            }
                                        }

                                        CusRect {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            color: ST.color_transparent

                                            CusText {
                                                anchors.verticalCenter: parent.verticalCenter
                                                text: online_pay_item_root.value_str
                                                color: ST.color_red
                                                anchors.right: parent.right
                                                font.bold: true
                                                font.pixelSize: 30 * ST.dpi_ratio
                                            }
                                        }
                                    }

                                    CusRect {
                                        width: parent.width
                                        height: 2 * ST.dpi_ratio
                                        anchors.bottom: parent.bottom
                                        color: ST.color_grey_border
                                    }
                                }

                                OnlinePayItem {
                                    img_path: "/Images/icon_alipay.png"
                                    label_str: qsTr("支付宝")
                                    value_str: qsTr("¥") + _actually_receive
                                    visible: _pay_method_enum == EnumTool.PAY_METHOD__ALIPAY_ONLINE
                                }
                                OnlinePayItem {
                                    img_path: "/Images/wechat_image.png"
                                    label_str: qsTr("微信")
                                    value_str: qsTr("¥") + _actually_receive
                                    visible: _pay_method_enum == EnumTool.PAY_METHOD__WECHAT_ONLINE
                                }
                                OnlinePayItem {
                                    img_path: "/Images/yunPay.png"
                                    label_str: qsTr("云闪付")
                                    value_str: qsTr("¥") + _actually_receive
                                    visible: _pay_method_enum == EnumTool.PAY_METHOD__UNIONPAY
                                }

                                CusSpacer {
                                    Layout.fillHeight: true
                                }
                            }
                        }

                        // 小程序
                        CusRect {
                            anchors.fill: parent
                            visible: (_pay_method_enum == EnumTool.PAY_METHOD__MINI_PROGRAM) && _pay_status_enum == EnumTool.PAY_STATUS__SUCCESS
                            anchors.topMargin: 20 * ST.dpi_ratio
                            color: ST.color_transparent

                            ColumnLayout {
                                anchors.fill: parent

                                spacing: 0

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 50 * ST.dpi_ratio

                                    color: ST.color_transparent
                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("小程序支付成功: ") + _actually_receive + qsTr("元")
                                        font.pixelSize: 30 * ST.dpi_ratio
                                    }
                                }

                                component PayResultItemMiniProgram: CusRect {
                                    id: pay_result_item_mini_program

                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 45 * ST.dpi_ratio
                                    color: ST.color_transparent

                                    property string img_path: ""
                                    property string lable_str: ""
                                    property string value_str: ""
                                    property bool is_need_rmb_symbol: true
                                    property bool is_bigger_than_0: Number(value_str) > 0

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 0
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: height
                                            color: ST.color_transparent
                                            Image {
                                                width: 35 * ST.dpi_ratio
                                                height: width
                                                anchors.centerIn: parent
                                                source: pay_result_item_mini_program.img_path
                                                opacity: pay_result_item_mini_program.is_bigger_than_0 ? 1 : .8
                                                fillMode: Image.PreserveAspectFit
                                            }
                                        }

                                        CusSpacer {
                                            Layout.preferredWidth: 10 * ST.dpi_ratio
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * ST.dpi_ratio
                                            color: ST.color_transparent
                                            CusText {
                                                text: pay_result_item_mini_program.lable_str
                                                anchors.verticalCenter: parent.verticalCenter
                                                font.pixelSize: 26 * ST.dpi_ratio
                                                color: pay_result_item_mini_program.is_bigger_than_0 ? ST.color_black : ST.color_grey_font2
                                            }
                                        }
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            CusText {
                                                text: (is_need_rmb_symbol ? qsTr("¥") : "") + pay_result_item_mini_program.value_str
                                                anchors.verticalCenter: parent.verticalCenter
                                                anchors.right: parent.right
                                                font.pixelSize: 30 * ST.dpi_ratio
                                                font.bold: true
                                                color: pay_result_item_mini_program.is_bigger_than_0 ? ST.color_black : ST.color_grey_font2
                                            }
                                        }
                                        CusSpacer {
                                            Layout.preferredWidth: 10 * ST.dpi_ratio
                                        }
                                    }
                                }

                                CusSpacer {
                                    Layout.preferredHeight: 20 * ST.dpi_ratio
                                }

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    ColumnLayout {
                                        anchors.fill: parent
                                        spacing: 15 * ST.dpi_ratio

                                        PayResultItemMiniProgram {
                                            Layout.fillWidth: true
                                            Layout.preferredHeight: 45 * ST.dpi_ratio
                                            visible: _mini_program_discount !== "0.00"   && _mini_program_discount !== "加载中"
                                            img_path: "/Images/member_balance.png"
                                            lable_str: qsTr("优惠券抵扣")
                                            value_str: _mini_program_discount
                                        }

                                        PayResultItemMiniProgram {
                                            Layout.fillWidth: true
                                            Layout.preferredHeight: 45 * ST.dpi_ratio
                                            visible: Number(_mini_program_beans_use).toFixed(2) !== "0.00"  && _mini_program_beans_use !== "加载中"
                                            is_need_rmb_symbol: false
                                            img_path: "/Images/buyHooBean1.png"
                                            lable_str: qsTr("百货豆付")
                                            value_str: (_mini_program_beans_use ==="加载中")?_mini_program_beans_use:Number(_mini_program_beans_use).toFixed(2)
                                        }

                                        PayResultItemMiniProgram {
                                            Layout.fillWidth: true
                                            Layout.preferredHeight: 45 * ST.dpi_ratio
                                            visible: _mini_program_beans_give !== "0.00" && _mini_program_beans_give !== "加载中"
                                            img_path: "/Images/buyHooBean2.png"
                                            lable_str: qsTr("奖励豆")
                                            value_str: _mini_program_beans_give
                                            is_need_rmb_symbol: false
                                        }

                                        PayResultItemMiniProgram {
                                            Layout.fillWidth: true
                                            Layout.preferredHeight: 45 * ST.dpi_ratio
                                            visible: _mini_program_balance !== "0.00" && _mini_program_balance !== "加载中"
                                            img_path: "/Images/zuHePayRec2Image52.png"
                                            lable_str: qsTr("余额付")
                                            value_str: _mini_program_balance
                                        }

                                        PayResultItemMiniProgram {
                                            Layout.fillWidth: true
                                            Layout.preferredHeight: 45 * ST.dpi_ratio
                                            visible: _mini_program_wechat !== "0.00" && _mini_program_wechat !== "加载中"
                                            img_path: "/Images/zuHePayRec2Image22.png"
                                            lable_str: qsTr("微信支付")
                                            value_str: _mini_program_wechat
                                        }

                                        CusSpacer {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                        }
                                    }
                                }
                            }
                        }

                        // 人脸支付
                        CusRect {
                            anchors.fill: parent
                            visible: (_pay_method_enum == EnumTool.PAY_METHOD__FACE) && _pay_status_enum == EnumTool.PAY_STATUS__SUCCESS
                            anchors.topMargin: 20 * ST.dpi_ratio
                            color: ST.color_transparent

                            ColumnLayout {
                                anchors.fill: parent

                                spacing: 0

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 50 * ST.dpi_ratio

                                    color: ST.color_transparent
                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("刷脸支付成功: ") + Number(_face_actually_received).toFixed(2) + qsTr("元")
                                        font.pixelSize: 30 * ST.dpi_ratio
                                    }
                                }

                                component PayResultItemMiniProgram2: CusRect {
                                    id: pay_result_item_mini_program2

                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 45 * ST.dpi_ratio
                                    color: ST.color_transparent

                                    property string img_path: ""
                                    property string lable_str: ""
                                    property string value_str: ""
                                    property bool is_need_rmb_symbol: true

                                    property bool is_bigger_than_0: Number(value_str) > 0

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 0
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: height
                                            color: ST.color_transparent
                                            Image {
                                                width: 35 * ST.dpi_ratio
                                                height: width
                                                anchors.centerIn: parent
                                                source: pay_result_item_mini_program2.img_path
                                                opacity: pay_result_item_mini_program2.is_bigger_than_0 ? 1 : .8
                                                fillMode: Image.PreserveAspectFit
                                            }
                                        }

                                        CusSpacer {
                                            Layout.preferredWidth: 10 * ST.dpi_ratio
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * ST.dpi_ratio
                                            color: ST.color_transparent
                                            CusText {
                                                text: pay_result_item_mini_program2.lable_str
                                                anchors.verticalCenter: parent.verticalCenter
                                                font.pixelSize: 26 * ST.dpi_ratio
                                                color: pay_result_item_mini_program2.is_bigger_than_0 ? ST.color_black : ST.color_grey_font2
                                            }
                                        }
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            CusText {
                                                text: (is_need_rmb_symbol ? qsTr("¥") : "") + pay_result_item_mini_program2.value_str
                                                anchors.verticalCenter: parent.verticalCenter
                                                anchors.right: parent.right
                                                font.pixelSize: 30 * ST.dpi_ratio
                                                font.bold: true
                                                color: pay_result_item_mini_program2.is_bigger_than_0 ? ST.color_black : ST.color_grey_font2
                                            }
                                        }
                                        CusSpacer {
                                            Layout.preferredWidth: 10 * ST.dpi_ratio
                                        }
                                    }
                                }

                                CusSpacer {
                                    Layout.preferredHeight: 20 * ST.dpi_ratio
                                }

                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    ColumnLayout {
                                        anchors.fill: parent
                                        spacing: 15 * ST.dpi_ratio

                                        PayResultItemMiniProgram2 {
                                            Layout.fillWidth: true
                                            Layout.preferredHeight: 45 * ST.dpi_ratio

                                            img_path: "/Images/buyHooBean1.png"
                                            lable_str: qsTr("百货豆付")
                                            value_str: Number(_face_beans_use).toFixed(2)
                                        }

                                        PayResultItemMiniProgram2 {
                                            Layout.fillWidth: true
                                            Layout.preferredHeight: 45 * ST.dpi_ratio

                                            img_path: "/Images/zuHePayRec2Image52.png"
                                            lable_str: qsTr("余额付")
                                            value_str: _face_balance
                                        }

                                        // PayResultItemMiniProgram2 {
                                        //     Layout.fillWidth: true
                                        //     Layout.preferredHeight: 45 * ST.dpi_ratio
                                        //     img_path: "/Images/zuHePayRec2Image22.png"
                                        //     lable_str: "微信支付"
                                        //     value_str: _face_wechat
                                        // }
                                        CusSpacer {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                        }
                                    }
                                }
                            }
                        }
                    }

                    CusRect {
                        //底部按钮
                        Layout.fillWidth: true
                        Layout.preferredHeight: 70 * dpi_ratio
                        color: ST.color_transparent

                        CusButton {
                            visible: _pay_status_enum == EnumTool.PAY_STATUS__SUCCESS
                            text: qsTr("重复打印")
                            height: parent.height
                            width: 180 * dpi_ratio
                            anchors.centerIn: parent
                            onClicked: {
                                printerControl.reprintLastOrder()
                            }
                        }

                        CusButton {
                            visible: _pay_status_enum == EnumTool.PAY_STATUS__PAYING
                            text: qsTr("终止支付")
                            height: parent.height
                            width: 180 * dpi_ratio
                            anchors.centerIn: parent
                            onClicked: {
                                //pay_page.setPayStatus(true)
                                sigButStatus()
                                logMgr.logEvtInfo4Qml("终止支付 设置pay_page.payStatus为:{}",pay_page.payStatus)

                                payMethodControl.stopCheckOnlinePay()
                                payMethodControl.stopCheckCombinedOnlinePay()
                                payMethodControl.stopCheckMiniProgramPay()
                                sigPayStop()
                                utils4Qml.sigPopupPayResultsClose()
                                close()
                            }
                        }
                    }
                }
            }
        }
    }
}
