﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."
import EnumTool 1.0

Item {
    id: popup_root

    function open() {
        popup_root.visible = true
        rect_content.state = "DEFAULT"
        resetInfo()
        tf_cash.focusMe()
    }

    onVisibleChanged: {
        if(!visible){
            search_bar.tf_search.focus = true
            search_bar.tf_search.forceActiveFocus()
            payment_code.enabled = true;
        }
    }

    function close() {
        popup_root.visible = false
        keyboard_c.closeAll()
    }

    property string title_name: qsTr("组合支付")

    function resetInfo() {
        pay_count = shopCartList.getTotalGoodsPrice()
        tf_cash.text = ""
        tf_jinquan.text = ""
        payment_code.text = ""
    }

    property var pay_count: 0

    property bool is_can_close: true

    property var background_color: ST.color_black

    Rectangle {
        anchors.fill: parent
        color: background_color
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                if (is_can_close)
                    close()
            }
        }
    }

    function autoSetJinquanText() {
        tf_jinquan.text = (pay_count - Number(tf_cash.text)).toFixed(2)
    }

    function autoSetCashText() {
        tf_cash.text = (pay_count - Number(tf_jinquan.text)).toFixed(2)
    }

    Rectangle {
        id: popup_contain_root
        width: 600 * dpi_ratio
        height: 550 * dpi_ratio
        x: (parent.width - width) / 2
        y: 120 * dpi_ratio

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                id: rect_content
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35
                color: ST.color_transparent

                state: "DEFAULT"
                states: [
                    State {
                        name: "DEFAULT"
                        PropertyChanges {
                            target: rect_pay_status_contain
                            visible: false
                        }
                        PropertyChanges {
                            target: rect_pay_status
                            color: ST.color_blue_deeper
                        }
                        PropertyChanges {
                            target: text_pay_status
                            text: ""
                        }
                        PropertyChanges {
                            target: popup_root
                            is_can_close: true
                            background_color: ST.color_black
                        }
                    },
                    State {
                        name: "PAYING"
                        PropertyChanges {
                            target: rect_pay_status_contain
                            visible: true
                        }
                        PropertyChanges {
                            target: rect_pay_status
                            color: ST.color_blue_deeper
                        }
                        PropertyChanges {
                            target: text_pay_status
                            text: qsTr("支付中")
                        }
                        PropertyChanges {
                            target: popup_root
                            is_can_close: false
                            background_color: ST.color_blue_deeper
                        }
                        PropertyChanges {
                            target: rect_pay_btn
                            visible: true
                        }
                    },
                    State {
                        name: "ERROR"
                        PropertyChanges {
                            target: rect_pay_status_contain
                            visible: true
                        }
                        PropertyChanges {
                            target: rect_pay_status
                            color: ST.color_red
                        }
                        PropertyChanges {
                            target: text_pay_status
                            text: qsTr("支付错误")
                        }
                        PropertyChanges {
                            target: popup_root
                            is_can_close: true
                            background_color: ST.color_black
                        }
                        PropertyChanges {
                            target: rect_pay_btn
                            visible: false
                        }
                    },
                    State {
                        name: "SUCCESS"
                        PropertyChanges {
                            target: rect_pay_status_contain
                            visible: true
                        }
                        PropertyChanges {
                            target: rect_pay_status
                            color: ST.color_green
                        }
                        PropertyChanges {
                            target: text_pay_status
                            text: qsTr("支付成功")
                        }
                        PropertyChanges {
                            target: popup_root
                            is_can_close: true
                            background_color: ST.color_black
                        }
                        PropertyChanges {
                            target: rect_pay_btn
                            visible: false
                        }
                    }
                ]

                ColumnLayout {
                    anchors.fill: parent

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        ColumnLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 85 * dpi_ratio
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.verticalCenter: parent.verticalCenter
                                    text: qsTr("合计： ") + pay_count + qsTr("元")
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 85 * dpi_ratio
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 180 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.verticalCenter: parent.verticalCenter
                                            text: qsTr("现金(元):")
                                        }
                                    }
                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        CusTextField {
                                            id: tf_cash
                                            width: parent.width
                                            height: 60 * dpi_ratio
                                            anchors.verticalCenter: parent.verticalCenter
                                            is_clicked_select_all: true
                                            keyboard_status: 1
                                            onTextEdited: {
                                                if (Number(text) > Number(pay_count)) {
                                                    text = pay_count
                                                }
                                                autoSetJinquanText()
                                            }

                                            validator: RegularExpressionValidator {
                                                regularExpression: /^\d+(\.\d+)?$/
                                            }
                                            digital_keyboard_x: 1270 * dpi_ratio
                                            digital_keyboard_y: 350 * dpi_ratio
                                        }
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 85 * dpi_ratio
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 180 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.verticalCenter: parent.verticalCenter
                                            text: qsTr("金圈平台(元):")
                                        }
                                    }
                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        CusTextField {
                                            id: tf_jinquan
                                            width: parent.width
                                            height: 60 * dpi_ratio
                                            anchors.verticalCenter: parent.verticalCenter
                                            is_clicked_select_all: true
                                            keyboard_status: 1
                                            onTextEdited: {
                                                if (Number(text) > Number(pay_count)) {
                                                    text = pay_count
                                                }
                                                autoSetCashText()
                                            }

                                            validator: RegularExpressionValidator {
                                                regularExpression: /^\d+(\.\d+)?$/
                                            }

                                            digital_keyboard_x: 1270 * dpi_ratio
                                            digital_keyboard_y: 350 * dpi_ratio
                                        }
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 85 * dpi_ratio
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 180 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.verticalCenter: parent.verticalCenter
                                            text: qsTr("支付码:")
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        CusTextField {
                                            id: payment_code
                                            width: parent.width
                                            height: 60 * dpi_ratio
                                            anchors.verticalCenter: parent.verticalCenter
                                            keyboard_status: 1
                                            digital_keyboard_x: 1270 * dpi_ratio
                                            digital_keyboard_y: 350 * dpi_ratio


                                            onAccepted: {
                                                payment_code.enabled = false;
                                                shopCartList.setReceivedCash(pay_count)

                                                payMethodControl.combinedOnlinePay(function (pay_status_enum, pay_method_enum, json_detail) {
                                                    switch (pay_status_enum) {
                                                    case EnumTool.PAY_STATUS__SUCCESS:
                                                        close()
                                                        showPayResults(pay_status_enum, pay_method_enum, json_detail)
                                                        search_bar.tf_search.focus = true
                                                        search_bar.tf_search.forceActiveFocus()
                                                        break
                                                    case EnumTool.PAY_STATUS__PAYING:
                                                        rect_content.state = "PAYING"
                                                        break
                                                    case EnumTool.PAY_STATUS__ERROR:
                                                        rect_content.state = "ERROR"
                                                        break
                                                    }
                                                }, text, tf_jinquan.text, tf_cash.text)
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                id: rect_pay_status_contain
                                Layout.fillWidth: true
                                Layout.preferredHeight: 85 * dpi_ratio
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                visible: false

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0
                                    CusRect {
                                        id: rect_pay_status
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        CusText {
                                            id: text_pay_status
                                            anchors.centerIn: parent
                                            color: ST.color_white_pure
                                        }
                                    }

                                    CusButton {
                                        id: rect_pay_btn

                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 130 * dpi_ratio
                                        color: ST.color_red
                                        CusText {
                                            anchors.centerIn: parent
                                            text: qsTr("停止支付")
                                            color: ST.color_white_pure
                                        }

                                        onClicked: {
                                            payMethodControl.stopCheckCombinedOnlinePay()
                                            rect_content.state = "ERROR"
                                            pay_page.payStatus = true
                                            shopCartList.resetAllInfo()
                                            search_bar.tf_search.focus = true
                                            search_bar.tf_search.forceActiveFocus()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
