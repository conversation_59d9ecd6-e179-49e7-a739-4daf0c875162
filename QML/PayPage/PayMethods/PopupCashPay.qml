﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."
import EnumTool 1.0

Item {
    id: popup_root

    property string title_name: qsTr("支付详情")

    function open(tabbar_index_in = EnumTool.POPUPCASHPAY_CASH) {
        popup_root.visible = true
        rect_pay_tab.tabbar_index = tabbar_index_in
    }

    function close() {
        keyboard_c.closeAll()
        popup_root.visible = false
        search_bar.forceActiveFocus()
        pay_page.payStatus = true;
        logMgr.logEvtInfo4Qml("现金收款关闭 设置pay_page.payStatus为:{}",pay_page.payStatus)
    }

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onPressed: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 900 * dpi_ratio
        height: 1000 * dpi_ratio
        x: 10 * dpi_ratio
        y: (parent.height - height) / 2

        color: ST.color_white_pure

        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Layout.alignment: Qt.AlignTop

                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.alignment: Qt.AlignTop
                color: ST.color_transparent

                MouseArea {
                    anchors.fill: parent
                }

                ColumnLayout {
                    anchors.fill: parent

                    CusRect {
                        id: rect_pay_tab
                        Layout.fillWidth: true
                        Layout.preferredHeight: 100 * dpi_ratio
                        Layout.alignment: Qt.AlignTop
                        color: ST.color_grey

                        component TabButton: CusRect {
                            id: control

                            property int index
                            property bool is_checked: rect_pay_tab.tabbar_index == index
                            property alias text: t_text.text

                            color: control.is_checked ? ST.color_green : ST.color_white_pure

                            CusText {
                                id: t_text
                                text: qsTr("现金")
                                font.pixelSize: 28 * dpi_ratio
                                anchors.centerIn: parent
                                color: control.is_checked ? ST.color_white_pure : ST.color_black
                            }

                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    rect_pay_tab.tabbar_index = index
                                }
                            }
                        }

                        property var tabbar_index: EnumTool.POPUPCASHPAY_CASH

                        RowLayout {
                            anchors.fill: parent
                            spacing: 1 * dpi_ratio
                            anchors.bottomMargin: 1 * dpi_ratio

                            TabButton {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                text: qsTr("现金")
                                index: EnumTool.POPUPCASHPAY_CASH
                            }
//                            TabButton {
//                                Layout.fillWidth: true
//                                Layout.fillHeight: true
//                                visible: cur_using_member_unique != ""
//                                text: "储值卡"
//                                index: EnumTool.POPUPCASHPAY_MEMBERSHIP_CARD
//                            }
                            TabButton {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                text: qsTr("组合支付")
                                index: EnumTool.POPUPCASHPAY_COMBINATION
                            }
                        }
                    }

                    // 现金详情
                    CusRect {
                        id: rect_pay_cash
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.margins: 35 * dpi_ratio
                        color: ST.color_transparent

                        visible: rect_pay_tab.tabbar_index == EnumTool.POPUPCASHPAY_CASH

                        property var goods_quantity: 0 //总数量
                        property var total_prices: 0 //总价
                        property alias _payment_amount: ti_payment_amount.text //支付金额

                        // property int wipe_zero_type: EnumTool.WIPE_ZERO__OFF //抹零?
                        property bool is_credit: false //赊账?

                        //>0 优惠金额  <0 找零
                        property real change_amount: (Number(rect_pay_cash._payment_amount) - Number(rect_pay_cash.final_payment_amount) - recharge_change_amount).toFixed(2) //找零

                        // property real change_amount: (Number(rect_pay_cash.final_payment_amount) - Number(rect_pay_cash._payment_amount)).toFixed(2)
                        property real cun_ling_amount: 0

                        property real final_payment_amount: 0 //最终价格

                        property real recharge_change_amount: 0 //存零金额

                        function refreshPayCash() {
                            goods_quantity = shopCartList.getTotalGoodsQuantity()
                            total_prices = shopCartList.getTotalGoodsPrice()
                            final_payment_amount = shopCartList.getFinalTotalGoodsPrice(EnumTool.PAY_METHOD__CASH)

                            logMgr.logDataInfo4Qml("refreshPayCash total_prices {}", total_prices)
                            logMgr.logDataInfo4Qml("refreshPayCash final_payment_amount {}", final_payment_amount)
                        }

                        function syncPaymentAmount() {
                            rect_pay_cash._payment_amount = rect_pay_cash.final_payment_amount.toFixed(2)
                            ti_payment_amount.selectAll()
                            ti_payment_amount.forceActiveFocus()
                        }

                        function syncRechargeChangeAmount() {
                            rect_pay_cash.recharge_change_amount = shopCartList.getRechargeChangeAmount()
                        }

                        function focusActualReceive() {
                            ti_payment_amount.forceActiveFocus()
                            ti_payment_amount.selectAll()
                            keyboard_c.openDigitalKeyboard(920 * dpi_ratio, 410 * dpi_ratio)
                        }

                        onVisibleChanged: {
                            if (visible) {
                                refreshPayCash()
                                syncPaymentAmount()
                                syncRechargeChangeAmount()
                                focusActualReceive()
                            } else {
                                keyboard_c.closeAll()
                            }
                        }

                        Component.onCompleted: {
                            refreshPayCash()
                            syncPaymentAmount()
                            syncRechargeChangeAmount()
                            focusActualReceive()
                        }

                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 18 * dpi_ratio

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 80 * dpi_ratio

                                color: ST.color_red
                                radius: ST.radius

                                CusText {
                                    text: qsTr("共 ") + Number(rect_pay_cash.goods_quantity).toFixed(2).toString() + qsTr(" 件")
                                    anchors.verticalCenter: parent.verticalCenter
                                    color: ST.color_white_pure
                                    anchors.left: parent.left
                                    anchors.leftMargin: 40 * dpi_ratio
                                }

                                CusText {
                                    text: "¥ " + rect_pay_cash.total_prices.toFixed(2).toString()
                                    font.pixelSize: 40 * dpi_ratio
                                    anchors.verticalCenter: parent.verticalCenter
                                    color: ST.color_white_pure
                                    anchors.right: parent.right
                                    anchors.rightMargin: 30 * dpi_ratio
                                    font.bold: true
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 200 * dpi_ratio
                                color: ST.color_transparent

                                GridLayout {
                                    id: gl_change
                                    anchors.fill: parent
                                    columns: 2
                                    rows: 2
                                    columnSpacing: 35 * dpi_ratio
                                    rowSpacing: 18 * dpi_ratio

                                    property double cell_width: (width - (columnSpacing * (columns - 1))) / columns
                                    property double cell_height: 70 * dpi_ratio

                                    component RectGiveChange: CusRect {
                                        id: rect_give_change

                                        property var payment_amount: "1000"
                                        property var change: Number(payment_amount) - Number(rect_pay_cash.final_payment_amount)
                                        color: ST.color_transparent
                                        radius: ST.radius

                                        border {
                                            width: 2 * dpi_ratio
                                            color: ST.color_red
                                        }

                                        visible: Number(rect_give_change.change) >= 0

                                        RowLayout {
                                            anchors.fill: parent

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: 150 * dpi_ratio
                                                color: ST.color_transparent
                                                CusText {
                                                    text: "¥"
                                                    x: 15 * dpi_ratio
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }
                                                CusText {
                                                    text: rect_give_change.payment_amount
                                                    x: 30 * dpi_ratio * configTool.fontRatio
                                                    anchors.verticalCenter: parent.verticalCenter
                                                    font.bold: true
                                                    font.pixelSize: 30 * dpi_ratio
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent
                                                CusText {
                                                    text: qsTr("找零")
                                                    x: 0 * dpi_ratio
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }
                                                CusText {
                                                    text: "¥"
                                                    x: 50 * dpi_ratio * configTool.fontRatio
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }
                                                CusText {
                                                    text: Number(rect_give_change.change).toFixed(2).toString()
                                                    x: 70 * dpi_ratio * configTool.fontRatio
                                                    anchors.verticalCenter: parent.verticalCenter
                                                    font.bold: true
                                                    font.pixelSize: 30 * dpi_ratio
                                                }
                                            }
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                rect_pay_cash._payment_amount = rect_give_change.payment_amount
                                            }
                                        }
                                    }

                                    RectGiveChange {
                                        radius: ST.radius
                                        Layout.preferredWidth: gl_change.cell_width
                                        Layout.preferredHeight: gl_change.cell_height
                                        payment_amount: "5"
                                    }

                                    RectGiveChange {
                                        radius: ST.radius
                                        Layout.preferredWidth: gl_change.cell_width
                                        Layout.preferredHeight: gl_change.cell_height
                                        payment_amount: "10"
                                    }

                                    RectGiveChange {
                                        radius: ST.radius
                                        Layout.preferredWidth: gl_change.cell_width
                                        Layout.preferredHeight: gl_change.cell_height
                                        payment_amount: "50"
                                    }

                                    RectGiveChange {
                                        radius: ST.radius
                                        Layout.preferredWidth: gl_change.cell_width
                                        Layout.preferredHeight: gl_change.cell_height
                                        payment_amount: "100"
                                    }
                                }
                            }

                            CusRect {
                                //折后金额
                                Layout.fillWidth: true
                                Layout.preferredHeight: 80 * dpi_ratio
                                color: ST.color_transparent

                                CusText {
                                    text: qsTr("折后金额")
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.right: parent.right
                                    anchors.rightMargin: 250 * dpi_ratio
                                }
                                CusText {
                                    text: rect_pay_cash.final_payment_amount.toFixed(2)
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.right: parent.right
                                    anchors.rightMargin: 50 * dpi_ratio
                                    font.bold: true
                                    font.pixelSize: 35 * dpi_ratio
                                }
                                CusText {
                                    text: qsTr("元")
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.right: parent.right
                                    anchors.rightMargin: 15 * dpi_ratio
                                }
                            }

                            CusRect {
                                //收款
                                Layout.fillWidth: true
                                Layout.preferredHeight: 85 * dpi_ratio
                                color: ST.color_transparent

                                RowLayout {
                                    //收款
                                    anchors.fill: parent
                                    spacing: 35 * dpi_ratio
                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        border {
                                            color: ST.color_red
                                            width: 2 * dpi_ratio
                                        }

                                        CusText {
                                            text: qsTr("收款")
                                            anchors.verticalCenter: parent.verticalCenter
                                            x: 15 * dpi_ratio
                                            color: ST.color_orange
                                        }

                                        TextInput {
                                            id: ti_payment_amount
                                            validator: RegularExpressionValidator {
                                                regularExpression: /^\d+(\.\d{1,2})?$/
                                            }
                                            horizontalAlignment: Text.AlignRight
                                            verticalAlignment: Text.AlignVCenter

                                            anchors {
                                                fill: parent
                                                rightMargin: 50 * dpi_ratio
                                            }

                                            font {
                                                bold: true
                                                pixelSize: 40 * dpi_ratio
                                                family: ST.fontFamilyYaHei
                                            }

                                            onAccepted: {
                                                keyboard_c.closeAll()
                                            }
                                        }
                                        CusText {
                                            text: qsTr("元")
                                            anchors.verticalCenter: parent.verticalCenter
                                            anchors.right: parent.right
                                            anchors.rightMargin: 15 * dpi_ratio
                                        }
                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                rect_pay_cash.focusActualReceive()
                                            }
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        border {
                                            color: ST.color_green
                                            width: 2 * dpi_ratio
                                        }

                                        CusText {
                                            text: rect_pay_cash.change_amount > 0 ? qsTr("找零") : qsTr("优惠")
                                            anchors.verticalCenter: parent.verticalCenter
                                            x: 15 * dpi_ratio
                                        }

                                        CusText {
                                            text: rect_pay_cash.change_amount > 0 ? rect_pay_cash.change_amount : -1 * rect_pay_cash.change_amount
                                            anchors.verticalCenter: parent.verticalCenter
                                            anchors.right: parent.right
                                            anchors.rightMargin: 50 * dpi_ratio
                                            font.bold: true
                                            color: ST.color_green
                                            font.pixelSize: 40 * dpi_ratio
                                        }
                                        CusText {
                                            text: qsTr("元")
                                            anchors.verticalCenter: parent.verticalCenter
                                            anchors.right: parent.right
                                            anchors.rightMargin: 15 * dpi_ratio
                                            color: ST.color_green
                                        }
                                    }
                                }
                            }

                            CusRect {
                                //抹零
                                Layout.fillWidth: true
                                Layout.preferredHeight: 140 * dpi_ratio
                                color: ST.color_white_pure

                                RowLayout {
                                    anchors.fill: parent
                                    anchors.margins: 1 * dpi_ratio
                                    spacing: 10 * dpi_ratio

                                    CusRect {
                                        //全部存零
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 150 * dpi_ratio
                                        color: ST.color_white_pure
                                        visible: cur_using_member_unique != ""

                                        radius: ST.radius
                                        border {
                                            width: 1 * dpi_ratio
                                            color: ST.color_blue_deeper
                                        }

                                        CusText {
                                            text: qsTr("全部存零")
                                            anchors.centerIn: parent
                                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (cur_using_member_unique == "") {
                                                    toast.openWarn(qsTr("未选择会员"))
                                                }

                                                if (rect_pay_cash.change_amount < 0.01 || rect_pay_cash.recharge_change_amount > 0) {
                                                    toast.openWarn(qsTr("无需存零到元"))
                                                    return
                                                }

                                                payMethodControl.reqRechargeMemberByChange4Qml(function (pay_status, pay_type, detail_json) {
                                                    if (pay_status == EnumTool.PAY_STATUS__SUCCESS) {
                                                        var json_doc = JSON.parse(detail_json)
                                                        rect_pay_cash.recharge_change_amount = json_doc.received_amount
                                                        toast.openInfo(qsTr("存零成功"))
                                                    } else {
                                                        toast.openError(qsTr("存零失败"))
                                                    }
                                                }, cur_using_member_unique, rect_pay_cash.change_amount)
                                            }
                                        }
                                    }

                                    CusRect {
                                        //存零到1元
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 150 * dpi_ratio
                                        color: ST.color_white_pure
                                        visible: cur_using_member_unique != ""

                                        radius: ST.radius
                                        border {
                                            width: 1 * dpi_ratio
                                            color: ST.color_blue_deeper
                                        }

                                        CusText {
                                            text: qsTr("存零到1元")
                                            anchors.centerIn: parent
                                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (cur_using_member_unique == "") {
                                                    toast.openWarn(qsTr("未选择会员"))
                                                }

                                                var change = Math.ceil(rect_pay_cash.final_payment_amount) - rect_pay_cash.final_payment_amount

                                                if (change < 0.01 || rect_pay_cash.recharge_change_amount > 0) {
                                                    toast.openWarn(qsTr("无需存零到元"))
                                                    return
                                                }

                                                payMethodControl.reqRechargeMemberByChange4Qml(function (pay_status, pay_type, detail_json) {
                                                    if (pay_status == EnumTool.PAY_STATUS__SUCCESS) {
                                                        rect_pay_cash._payment_amount = Math.ceil(Number(rect_pay_cash._payment_amount))
                                                        var json_doc = JSON.parse(detail_json)
                                                        shopCartList.setRechargeChangeAmount(json_doc.received_amount)
                                                        rect_pay_cash.syncRechargeChangeAmount()
                                                        toast.openInfo(qsTr("存零成功"))
                                                    } else {
                                                        toast.openError(qsTr("存零失败"))
                                                    }
                                                }, cur_using_member_unique, change)
                                            }
                                        }
                                    }

                                    CusSpacer {
                                        Layout.fillWidth: true
                                    }

                                    CusRect {
                                        //抹零到1元
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 150 * dpi_ratio

                                        radius: ST.radius
                                        border {
                                            width: 1 * dpi_ratio
                                            color: ST.color_grey_font
                                        }

                                        color: shopCartList.wipeZeroType == EnumTool.WIPE_ZERO__1_YUAN ? ST.color_green : ST.color_white_pure

                                        CusText {
                                            text: qsTr("抹零到1元")
                                            anchors.centerIn: parent
                                            color: shopCartList.wipeZeroType == EnumTool.WIPE_ZERO__1_YUAN ? ST.color_white_pure : ST.color_black
                                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (shopCartList.wipeZeroType === EnumTool.WIPE_ZERO__1_YUAN) {
                                                    shopCartList.wipeZeroType = EnumTool.WIPE_ZERO__OFF
                                                } else {
                                                    shopCartList.wipeZeroType = EnumTool.WIPE_ZERO__1_YUAN
                                                }
                                                rect_pay_cash.refreshPayCash()
                                                rect_pay_cash.syncPaymentAmount()
                                            }
                                        }
                                    }

                                    CusRect {
                                        //抹零到5角
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 150 * dpi_ratio

                                        radius: ST.radius
                                        border {
                                            width: 1 * dpi_ratio
                                            color: ST.color_grey_font
                                        }

                                        color: shopCartList.wipeZeroType == EnumTool.WIPE_ZERO__5_JIAO ? ST.color_green : ST.color_white_pure

                                        CusText {
                                            text: qsTr("抹零到5角")
                                            anchors.centerIn: parent
                                            color: shopCartList.wipeZeroType == EnumTool.WIPE_ZERO__5_JIAO ? ST.color_white_pure : ST.color_black
                                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (shopCartList.wipeZeroType == EnumTool.WIPE_ZERO__5_JIAO) {
                                                    shopCartList.wipeZeroType = EnumTool.WIPE_ZERO__OFF
                                                } else {
                                                    shopCartList.wipeZeroType = EnumTool.WIPE_ZERO__5_JIAO
                                                }
                                                rect_pay_cash.refreshPayCash()
                                                rect_pay_cash.syncPaymentAmount()
                                            }
                                        }
                                    }

                                    CusRect {
                                        //抹零到1角
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 150 * dpi_ratio

                                        radius: ST.radius
                                        border {
                                            width: 1 * dpi_ratio
                                            color: ST.color_grey_font
                                        }

                                        color: shopCartList.wipeZeroType == EnumTool.WIPE_ZERO__1_JIAO ? ST.color_green : ST.color_white_pure

                                        CusText {
                                            text: qsTr("抹零到1角")
                                            anchors.centerIn: parent
                                            color: shopCartList.wipeZeroType == EnumTool.WIPE_ZERO__1_JIAO ? ST.color_white_pure : ST.color_black
                                            font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                if (shopCartList.wipeZeroType === EnumTool.WIPE_ZERO__1_JIAO) {
                                                    shopCartList.wipeZeroType = EnumTool.WIPE_ZERO__OFF
                                                } else {
                                                    shopCartList.wipeZeroType = EnumTool.WIPE_ZERO__1_JIAO
                                                }
                                                rect_pay_cash.refreshPayCash()
                                                rect_pay_cash.syncPaymentAmount()
                                            }
                                        }
                                    }
                                }
                            }

                            CusSpacer {
                                Layout.fillHeight: true
                            }

                            CusRect {
                                //取消、确认按钮
                                Layout.preferredHeight: 80 * dpi_ratio
                                Layout.fillWidth: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    CusButton {
                                        //取消
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color:ST.color_red
                                        text: qsTr("取消")
                                        onClicked: {
                                            close()
                                        }
                                    }
                                    CusButton {
                                        //确认
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        text: qsTr("确认")
                                        onClicked: {

                                            // if (rect_pay_cash.change_amount < 0) {
                                            //     toast.openWarn("应收金额过小!")
                                            //     return
                                            // }
                                            shopCartList.setReceivedCash(rect_pay_cash._payment_amount)

                                            logMgr.logDataInfo4Qml("[确认支付] payment_amount 实付 {}", rect_pay_cash._payment_amount)
                                            logMgr.logDataInfo4Qml("[确认支付] total_prices 应付 {}", rect_pay_cash.total_prices)
                                            logMgr.logDataInfo4Qml("[确认支付] change_amount 优惠 {}", rect_pay_cash.change_amount)

                                            payMethodControl.cashPay4Qml(showPayResults)
                                            close()
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 储值卡详情
                    CusRect {
                        id: rect_membership
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.margins: 35 *dpi_ratio
                        visible: rect_pay_tab.tabbar_index == EnumTool.POPUPCASHPAY_MEMBERSHIP_CARD
                        color: ST.color_transparent

                        property string total_prices: ""

                        function refreshInfo() {
                            total_prices = shopCartList.getTotalGoodsPrice()
                        }

                        onVisibleChanged: {
                            refreshInfo()
                        }

                        Component.onCompleted: {
                            refreshInfo()
                        }

                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 30 * dpi_ratio

                            CusRect {
                                id: rect_member_info
                                Layout.preferredHeight: 100 * dpi_ratio
                                Layout.fillWidth: true
                                anchors.leftMargin: 100 * dpi_ratio
                                anchors.rightMargin: 100 * dpi_ratio
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 50 * dpi_ratio
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent
                                        border {
                                            width: 2
                                            color: ST.color_green
                                        }
                                        RowLayout {
                                            anchors.fill: parent
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_green

                                                RowLayout {
                                                    anchors.fill: parent
                                                    anchors.leftMargin: 15 * dpi_ratio
                                                    anchors.topMargin: 15 * dpi_ratio
                                                    anchors.bottomMargin: 15 * dpi_ratio

                                                    CusRect {
                                                        Layout.fillHeight: true
                                                        Layout.preferredWidth: height
                                                        color: ST.color_transparent
                                                        Image {
                                                            source: "/Images/huiyuantouxiang.png"
                                                            anchors.fill: parent
                                                            anchors.margins: 5
                                                        }
                                                    }

                                                    CusRect {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        color: ST.color_transparent

                                                        ColumnLayout {
                                                            anchors.fill: parent
                                                            CusRect {
                                                                Layout.fillWidth: true
                                                                Layout.fillHeight: true
                                                                color: ST.color_transparent
                                                                CusText {
                                                                    //用户名
                                                                    text: cur_using_member_name
                                                                    color: ST.color_white_pure
                                                                    x: 25 * dpi_ratio
                                                                    anchors.verticalCenter: parent.verticalCenter
                                                                }
                                                            }
                                                            CusRect {
                                                                Layout.fillWidth: true
                                                                Layout.fillHeight: true
                                                                color: ST.color_transparent
                                                                CusText {
                                                                    //用户ID(卡号)
                                                                    text: cur_using_member_unique
                                                                    color: ST.color_white_pure
                                                                    x: 25 * dpi_ratio
                                                                    anchors.verticalCenter: parent.verticalCenter
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent

                                                RowLayout {
                                                    anchors.fill: parent
                                                    CusRect {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        color: ST.color_transparent
                                                        ColumnLayout {
                                                            anchors.fill: parent
                                                            anchors.topMargin: 15 * dpi_ratio
                                                            anchors.bottomMargin: 15 * dpi_ratio
                                                            spacing: 0

                                                            CusRect {
                                                                Layout.fillWidth: true
                                                                Layout.fillHeight: true
                                                                color: ST.color_transparent
                                                                CusText {
                                                                    text: cur_using_member_balance
                                                                    color: ST.color_yellow
                                                                    anchors.centerIn: parent
                                                                }
                                                            }
                                                            CusRect {
                                                                Layout.fillWidth: true
                                                                Layout.fillHeight: true
                                                                color: ST.color_transparent
                                                                CusText {
                                                                    text: qsTr("余额")
                                                                    anchors.centerIn: parent
                                                                }
                                                            }
                                                        }
                                                    }
                                                    CusRect {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        color: ST.color_transparent
                                                        ColumnLayout {
                                                            anchors.fill: parent
                                                            anchors.topMargin: 15 * dpi_ratio
                                                            anchors.bottomMargin: 15 * dpi_ratio
                                                            spacing: 0

                                                            CusRect {
                                                                Layout.fillWidth: true
                                                                Layout.fillHeight: true
                                                                color: ST.color_transparent
                                                                CusText {
                                                                    text: cur_using_member_points
                                                                    color: ST.color_yellow
                                                                    anchors.centerIn: parent
                                                                }
                                                            }
                                                            CusRect {
                                                                Layout.fillWidth: true
                                                                Layout.fillHeight: true
                                                                color: ST.color_transparent
                                                                CusText {
                                                                    text: qsTr("积分")
                                                                    anchors.centerIn: parent
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            CusRect {
                                Layout.preferredHeight: 84 * dpi_ratio
                                Layout.fillWidth: true
                                color: ST.color_transparent
                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("商品确认余额支付 ") + rect_membership.total_prices + qsTr(" 元")
                                    font.pixelSize: 40 * dpi_ratio
                                    color: ST.color_black
                                }
                            }
                            CusRect {
                                Layout.preferredHeight: 150 * dpi_ratio
                                Layout.fillWidth: true
                                color: ST.color_transparent
                                visible: false
                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 20 * dpi_ratio

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 75 * dpi_ratio
                                        color: ST.color_transparent
                                        CusTextField {
                                            width: 380 * dpi_ratio
                                            height: 70 * dpi_ratio
                                            anchors.centerIn: parent
                                            font.pixelSize: 50 * dpi_ratio
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusText {
                                            text: qsTr("请输入支付密码")
                                            anchors.centerIn: parent
                                            font.pixelSize: 35 * dpi_ratio
                                            color: ST.color_grey_font
                                        }
                                    }
                                }
                            }
                            CusSpacer {
                                Layout.fillHeight: true
                            }
                            CusRect {
                                Layout.preferredHeight: 80 * dpi_ratio
                                Layout.fillWidth: true

                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    CusButton {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        text: qsTr("取消")
                                        color:ST.color_red
                                        onClicked: {
                                            close()
                                        }
                                    }
                                    CusButton {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        text: qsTr("确认")
                                        onClicked: {
                                            payMethodControl.payByMemberBalance_v2(showPayResults, cur_using_member_unique)
                                            close()
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 组合支付
                    CusRect {
                        id: rect_pay_combination
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.margins: 35 *dpi_ratio
                        visible: rect_pay_tab.tabbar_index == EnumTool.POPUPCASHPAY_COMBINATION
                        color: ST.color_white_pure

                        //总金额
                        property real total_price

                        onVisibleChanged: {
                            if (visible) {
                                refreshInfo()
                            }
                        }

                        Component.onCompleted: {
                            refreshInfo()
                        }

                        property real total_paid_money: _alipay_num + _wechat_num + _bank_card_num + _cash_num + _prepaid_card_num //已付金额(填写)

                        property real remain_money: rect_pay_combination.total_price - total_paid_money //剩余金额

                        property real _alipay_num: _alipay == "" ? 0 : Number(_alipay) //支付宝
                        property string _alipay: "" //支付宝

                        property real _wechat_num: _wechat == "" ? 0 : Number(_wechat) //微信
                        property string _wechat: "" //微信

                        property real _bank_card_num: _bank_card == "" ? 0 : Number(_bank_card) //银行卡
                        property string _bank_card: "" //银行卡

                        property real _cash_num: _cash == "" ? 0 : Number(_cash) //现金
                        property string _cash: "" //现金

                        property real _prepaid_card_num: _prepaid_card == "" ? 0 : Number(_prepaid_card) //储值卡
                        property string _prepaid_card: "" //储值卡

                        function getTotalInputMoney() {
                            return (_alipay_num + _wechat_num + _bank_card_num + _cash_num + _prepaid_card_num)
                        }

                        function getRemainMoney(cur_money) {
                            if (cur_money == "") {
                                cur_money = "0"
                            }
                            if(getTotalInputMoney() === 0){
                                return
                            }else{
                                return (rect_pay_combination.total_price - getTotalInputMoney() + Number(cur_money)).toFixed(2)
                            }
                        }

                        function refreshInfo() {
                            total_price = utils4Qml.roundDecimalStr(shopCartList.getTotalGoodsPrice())

                            //                            total_price = shopCartList.getTotalGoodsPrice()
                            _alipay = ""
                            _wechat = ""
                            _bank_card = ""
                            _cash = ""
                            _prepaid_card = ""

                            payment_item_bank_alipay.ti_money.text = ""
                            payment_item_bank_card.ti_money.text = ""
                            payment_item_bank_cash.ti_money.text = ""
                            payment_item_bank_wechat.ti_money.text = ""
                            payment_item_prepaid_card.ti_money.text = ""
                        }

                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 28 * dpi_ratio
                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 50 * dpi_ratio
                                color: ST.color_transparent

                                CusText {
                                    text: qsTr("合计: ") + rect_pay_combination.total_price
                                    anchors.verticalCenter: parent.verticalCenter
                                    x: 15 * dpi_ratio
                                }
                            }
                            component PaymentItem: Item {
                                id: payment_item_root
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                Layout.preferredHeight: 20
                                signal textChanged1
                                signal clicked

                                property alias image_path: img_single_line.source
                                property string lable_name: qsTr("支付宝")
                                property alias ti_money: ti_money

                                Image {
                                    anchors.fill: parent
                                    source: "/Images/zuHePayRec2Image1.png"
                                }

                                RowLayout {
                                    anchors.fill: parent

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 190 * dpi_ratio
                                        color: ST.color_transparent

                                        Image {
                                            id: img_single_line
                                            source: "/Images/alipay_image.png"
                                            fillMode: Image.PreserveAspectFit
                                            height: 45 * dpi_ratio
                                            width: height
                                            anchors.verticalCenter: parent.verticalCenter
                                            x: 20 * dpi_ratio
                                        }

                                        CusText {
                                            anchors.left: img_single_line.right
                                            anchors.leftMargin: 15 * dpi_ratio
                                            text: lable_name
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 2 * dpi_ratio
                                        Layout.topMargin: 25 * dpi_ratio
                                        Layout.bottomMargin: 25 * dpi_ratio
                                        color: ST.color_black
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color: ST.color_transparent

                                        CusTextField {
                                            id: ti_money
                                            anchors.fill: parent
                                            font.pixelSize: 22 * dpi_ratio
                                            verticalAlignment: Text.AlignVCenter
                                            anchors.leftMargin: 25 * dpi_ratio
                                            font.family: ST.fontFamilyYaHei
                                            keyboard_status: 1
                                            onTextChanged: {
                                                payment_item_root.textChanged1()
                                            }
                                            border.width: 0
                                            placeholderText: qsTr("请输入金额(元)")

                                            digital_keyboard_x: 920 * dpi_ratio
                                            digital_keyboard_y: 410 * dpi_ratio
                                        }
                                    }
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        payment_item_root.clicked()
                                    }
                                }
                            }

                            PaymentItem {
                                //支付宝
                                id: payment_item_bank_alipay
                                image_path: "/Images/alipay_image.png"
                                lable_name: qsTr("支付宝")
                                onTextChanged1: {
                                    rect_pay_combination._alipay = ti_money.text
                                    if (rect_pay_combination.getTotalInputMoney().toFixed(2) > rect_pay_combination.total_price) {
                                        ti_money.text = ""
                                    }
                                }
                                onClicked: {
                                    ti_money.focusMe()
                                    ti_money.text = rect_pay_combination.getRemainMoney(rect_pay_combination._alipay)
                                    ti_money.selectAll()
                                }
                            }
                            PaymentItem {
                                //微信
                                id: payment_item_bank_wechat
                                image_path: "/Images/wechat1.png"
                                lable_name: qsTr("微信")
                                onTextChanged1: {
                                    rect_pay_combination._wechat = ti_money.text
                                    if (rect_pay_combination.getTotalInputMoney().toFixed(2) > rect_pay_combination.total_price) {
                                        ti_money.text = ""
                                    }
                                }
                                onClicked: {
                                    ti_money.focusMe()
                                    ti_money.text = rect_pay_combination.getRemainMoney(rect_pay_combination._wechat)
                                    ti_money.selectAll()
                                }
                            }
                            PaymentItem {
                                //银行卡
                                id: payment_item_bank_card
                                image_path: "/Images/bank_card.png"
                                lable_name: qsTr("银行卡")
                                onTextChanged1: {
                                    rect_pay_combination._bank_card = ti_money.text
                                    if (rect_pay_combination.getTotalInputMoney().toFixed(2) > rect_pay_combination.total_price) {
                                        ti_money.text = ""
                                    }
                                }
                                onClicked: {
                                    ti_money.focusMe()
                                    ti_money.text = rect_pay_combination.getRemainMoney(rect_pay_combination._bank_card)
                                    ti_money.selectAll()
                                }
                            }
                            PaymentItem {
                                //现金
                                id: payment_item_bank_cash
                                image_path: "/Images/cash.png"
                                lable_name: qsTr("现金")
                                onTextChanged1: {
                                    rect_pay_combination._cash = ti_money.text
                                    if (rect_pay_combination.getTotalInputMoney().toFixed(2) > rect_pay_combination.total_price) {
                                        ti_money.text = ""
                                    }
                                }
                                onClicked: {
                                    ti_money.focusMe()
                                    ti_money.text = rect_pay_combination.getRemainMoney(rect_pay_combination._cash)
                                    ti_money.selectAll()
                                }
                            }
                            PaymentItem {
                                //储值卡
                                id: payment_item_prepaid_card
                                image_path: "/Images/bank_card.png"
                                lable_name: qsTr("储值卡")
                                visible: cur_using_member_unique != ""
                                onTextChanged1: {
                                    rect_pay_combination._prepaid_card = ti_money.text
                                    if (rect_pay_combination.getTotalInputMoney().toFixed(2) > rect_pay_combination.total_price) {
                                        ti_money.text = ""
                                    }
                                }
                                onClicked: {
                                    ti_money.focusMe()
                                    ti_money.text = rect_pay_combination.getRemainMoney(rect_pay_combination._prepaid_card)
                                    ti_money.selectAll()
                                }
                            }

                            CusRect {
                                //取消、确认按钮
                                Layout.fillWidth: true
                                Layout.preferredHeight: 80 * dpi_ratio
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    CusButton {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        color:ST.color_red
                                        text: qsTr("取消")
                                        onClicked: {
                                            close()
                                        }
                                    }
                                    CusButton {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        text: qsTr("确认")
                                        onClicked: {

                                            if (rect_pay_combination.total_paid_money.toFixed(2) != rect_pay_combination.total_price.toFixed(2)) {
                                                toast.openWarn(qsTr("金额不匹配"))
                                                return
                                            }

                                            var combined_pay_detail = []

                                            if (rect_pay_combination._cash_num > 0) {
                                                //现金
                                                combined_pay_detail.push({
                                                                             "pay_method": 1,
                                                                             "pay_money": rect_pay_combination._cash_num
                                                                         })
                                            }

                                            if (rect_pay_combination._alipay_num > 0) {
                                                //支付宝
                                                combined_pay_detail.push({
                                                                             "pay_method": 2,
                                                                             "pay_money": rect_pay_combination._alipay_num
                                                                         })
                                            }
                                            if (rect_pay_combination._wechat_num > 0) {
                                                //微信
                                                combined_pay_detail.push({
                                                                             "pay_method": 3,
                                                                             "pay_money": rect_pay_combination._wechat_num
                                                                         })
                                            }

                                            if (rect_pay_combination._bank_card_num > 0) {
                                                //银行卡
                                                combined_pay_detail.push({
                                                                             "pay_method": 4,
                                                                             "pay_money": rect_pay_combination._bank_card_num
                                                                         })
                                            }

                                            if (rect_pay_combination._prepaid_card_num > 0) {
                                                //储值卡
                                                combined_pay_detail.push({
                                                                             "pay_method": 5,
                                                                             "pay_money": rect_pay_combination._prepaid_card_num
                                                                         })
                                            }

                                            payMethodControl.reqCombinedPay4Qml(function (pay_status, pay_method, json_detail) {
                                                logMgr.logDataInfo4Qml("[组合支付回调] pay_status：{}",pay_status)
                                                switch (pay_status) {
                                                case EnumTool.PAY_STATUS__SUCCESS:
                                                    logMgr.logDataInfo4Qml("回调指出结果")
                                                    showPayResults(pay_status, pay_method, json_detail)
                                                    ogMgr.logDataInfo4Qml("回调指出结果结束",)
                                                    break
                                                default:

                                                }
                                            }, cur_using_member_unique == "" ? "" : cur_using_member_unique, JSON.stringify(combined_pay_detail))

                                            close()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
