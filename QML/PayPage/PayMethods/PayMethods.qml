﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15
import QtGraphicalEffects 1.15
import EnumTool 1.0
import SettingEnum 1.0
import "../.."

CusRect {
    id: pay_method_root

    radius: ST.radius
    color: ST.color_white_pure
    property bool loadVipCard: false
    property bool loadCombined:false

    CusRect {
        anchors.fill: parent
        radius: ST.radius
        visible: pay_page_root.is_mamager_model
        color: ST.color_blue
        CusRect {
            anchors.fill: parent
            color: ST.color_black
            opacity: .3
        }
    }
    signal sigBut1Status(bool status)
    onVisibleChanged: {
        if(visible){
            cameraControl.openFaceCamera()
        }else{
            cameraControl.closeFaceCamera()
            activeGoodsManager(false)
        }

        if (!visible)
            return
        refreshPayMethod()
    }

    function showPayResults(pay_status_enum, pay_method_enum, json_detail) {
        window_root.loader_4_pay_status.sourceComponent = popup_pay_results
        window_root.loader_4_pay_status.item.open(pay_status_enum, pay_method_enum, json_detail)

        second_window.loader_4_pay_status_second.sourceComponent = popup_pay_results
        second_window.loader_4_pay_status_second.item.open(pay_status_enum, pay_method_enum, json_detail)
    }

    function hidePayResults() {
        window_root.loader_4_pay_status.item.close()
        second_window.loader_4_pay_status_second.item.close()
    }
    function hidePaysResults() {
        if(loadVipCard){
            window_root.loader4_vipCard_pay.item.close()
            logMgr.logEvtInfo4Qml("扫码关闭储值卡结果弹窗")
        }
        if(loadCombined){
            window_root.loader4_combined.item.close()
            logMgr.logEvtInfo4Qml("扫码关闭组合付窗口")
        }
    }
    Connections {
        target: utils4Qml
        function onSigPopupPayResultsClose() {
            hidePayResults()
        }
    }

    function showFaceVideo() {
        window_root.loader4_face.sourceComponent = popup_face
        window_root.loader4_face.item.open()

        second_window.loader_4_face_pay_second.sourceComponent = popup_face
        second_window.loader_4_face_pay_second.item.open()
    }

    function hideFacePay() {
        window_root.loader4_face.item.close()
        second_window.loader_4_face_pay_second.item.close()
    }

    Connections {
        target: utils4Qml
        function onSigPopupFacePayClose() {
            hideFacePay()
        }
    }

    function showPayMethod() {
        window_root.loader4_cash_pay.sourceComponent = popup_cash_pay
        window_root.loader4_cash_pay.item.open()
    }

    function showVipCardMethod() {
        window_root.loader4_vipCard_pay.sourceComponent = popup_vipcard_pay
        window_root.loader4_vipCard_pay.item.open(EnumTool.POPUPCASHPAY_MEMBERSHIP_CARD)
        loadVipCard = true
    }

    function showCombinedPopup() {
        window_root.loader4_combined.sourceComponent = popup_combined
        window_root.loader4_combined.item.open()
        loadCombined = true
    }

    //TODO 切换商品管理
    function activeGoodsManager(is_active) {
        logMgr.logEvtInfo4Qml("{}商品管理", is_active ? "打开" : "关闭")

        pay_page_root.is_mamager_model = is_active

        if (!is_active) {
            search_bar.forceActiveFocus()
            search_bar.refresh_content()
            window_root.keyboard_c.closeAll()
        }
    }

    function payByPaymentCode(payment_code) {
        payMethodControl.payByPaymentCode(showPayResults, payment_code)
    }

    function refreshPayMethod() {
        lm_pay_method.clear()
        var json_doc = JSON.parse(payMethodControl.getEnabledPayMethodStatusListJson())

        for (var i = 0; i < json_doc.length; ++i) {
            var cur_item = json_doc[i]

            switch (cur_item.pay_enum) {
            case EnumTool.PAY_METHOD__CASH:
                lm_pay_method.append({
                                         "pay_method": EnumTool.PAY_METHOD__CASH,
                                         "is_check_btn": false
                                     })
                break
            case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
                lm_pay_method.append({
                                         "pay_method": EnumTool.PAY_METHOD__ALIPAY_OFFLINE,
                                         "is_check_btn": false
                                     })
                break
            case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
                lm_pay_method.append({
                                         "pay_method": EnumTool.PAY_METHOD__WECHAT_OFFLINE,
                                         "is_check_btn": false
                                     })
                break
            case EnumTool.PAY_METHOD__FACE:
                lm_pay_method.append({
                                         "pay_method": EnumTool.PAY_METHOD__FACE,
                                         "is_check_btn": false
                                     })
                break
            case EnumTool.PAY_METHOD__VIPCARD:
                lm_pay_method.append({
                                         "pay_method": EnumTool.PAY_METHOD__VIPCARD,
                                         "is_check_btn": false
                                     })
                break
            case EnumTool.PAY_METHOD__COMBINED:
                lm_pay_method.append({
                                         "pay_method": EnumTool.PAY_METHOD__COMBINED,
                                         "is_check_btn": false
                                     })
                break
            case EnumTool.PAY_METHOD__GOODS_MANAGER:
                lm_pay_method.append({
                                         "pay_method": EnumTool.PAY_METHOD__GOODS_MANAGER,
                                         "is_check_btn": false
                                     })
                break
            }
        }
    }

    RowLayout {
        anchors.fill: parent
        anchors.margins: 10 * dpi_ratio
        focus: true
        spacing: 10
        visible: !pay_page_root.is_mamager_model

        Repeater {
            model: ListModel {
                id: lm_pay_method
            }

            delegate: CusRect {
                id: rect_pay_method

                Layout.fillHeight: true
                Layout.fillWidth: true
                Layout.maximumWidth: 260 * dpi_ratio
                Layout.preferredWidth: 100
                color: ST.color_transparent

                property bool is_press: mouse_area1.pressed
                property bool is_checked: false
                property bool is_down: is_check_btn ? is_checked : is_press

                Connections {
                    target: utils4Qml
                    function onReturnToPayPage() {
                        rect_pay_method.is_checked = false
                    }
                }

                //背景
                RowLayout {
                    anchors.fill: parent
                    spacing: 0
                    CusRect {
                        Layout.fillHeight: true
                        Layout.preferredWidth: .17 * height
                        color: ST.color_transparent
                        CusImg {
                            anchors.rightMargin: -2
                            anchors.fill: parent
                            source: is_down ? getLeftImgD(model.pay_method) : getLeftImg(model.pay_method)
                        }
                    }
                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent
                        CusImg {
                            anchors.fill: parent
                            source: is_down ? getCenterImgD(model.pay_method) : getCenterImg(model.pay_method)
                        }
                    }
                    CusRect {
                        Layout.fillHeight: true
                        Layout.preferredWidth: .17 * height
                        color: ST.color_transparent
                        CusImg {
                            anchors.leftMargin: -2
                            anchors.fill: parent
                            source: is_down ? getRightImgD(model.pay_method) : getRightImg(model.pay_method)
                        }
                    }
                }

                RowLayout {
                    anchors.fill: parent
                    spacing: 10 * dpi_ratio

                    CusSpacer {
                        Layout.fillWidth: true
                    }

                    CusImg {
                        Layout.preferredWidth: 30 * dpi_ratio
                        Layout.preferredHeight: width
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignHCenter
                        source: getImgPath(model.pay_method)
                        visible: source != ""
                    }

                    ColumnLayout {
                        Layout.fillHeight: true
                        Layout.alignment: Qt.AlignVCenter | Qt.AlignHCenter
                        spacing: 0

                        CusSpacer {
                            Layout.fillHeight: true
                        }

                        CusText {
                            text: getPayName(model.pay_method)
                            color: ST.color_white_pure
                            font.bold: true
                            font.pixelSize:text.length > 4 ?26 * dpi_ratio: 28 * dpi_ratio
                            Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                            visible: text != ""
                        }

                        CusText {
                            color: ST.color_white_pure
                            text: getPayTip(model.pay_method)
                            Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                            font.pixelSize: 16 * dpi_ratio
                            visible: text != ""
                        }

                        CusSpacer {
                            Layout.fillHeight: true
                        }
                    }

                    CusSpacer {
                        Layout.fillWidth: true
                    }
                }

                MouseArea {
                    id: mouse_area1
                    anchors.fill: parent

                    onClicked: {
                        if (is_check_btn) {
                            rect_pay_method.is_checked = !rect_pay_method.is_checked
                        }
                        if(payMethodControl.getNetStatus() !== "101"){
                            if (pay_method === EnumTool.PAY_METHOD__GOODS_MANAGER || pay_method === EnumTool.PAY_METHOD__FACE ||pay_method === EnumTool.PAY_METHOD__COMBINED
                                    ||pay_method === EnumTool.PAY_METHOD__VIPCARD ){
                                toast.openWarn(qsTr("离线状态下该功能不可用！"))
                                return
                            }
                        }
                        if((shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7")&&(shopCartList.memberUnique !== "")){
                            if (pay_method === EnumTool.PAY_METHOD__FACE ||pay_method === EnumTool.PAY_METHOD__ALIPAY_OFFLINE
                                    ||pay_method === EnumTool.PAY_METHOD__WECHAT_OFFLINE||pay_method === EnumTool.PAY_METHOD__COMBINED
                                    ||pay_method === EnumTool.PAY_METHOD__CASH){
                                toast.openWarn(qsTr("请点击储值卡"))
                                return
                            }
                        }

                        if (pay_method == EnumTool.PAY_METHOD__GOODS_MANAGER) {
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_PAY__GOODS_MGR)) {
                                activeGoodsManager(true)
                            } else {
                                toast.openWarn(qsTr("无此权限"))
                            }
                            return
                        }

                        if (shopCartList.getTotalGoodsQuantity() == 0) {
                            toast.openWarn(qsTr("购物车内无商品"))
                            return
                        }

                        if (pay_method == EnumTool.PAY_METHOD__FACE) {
                            pay_page.payStatus = false
                            logMgr.logDataInfo4Qml("===========================点击刷脸付修改扫码权限：{}==============================",pay_page.payStatus.toString())
                            showFaceVideo()
                            return
                        }

                        if (pay_method == EnumTool.PAY_METHOD__CASH) {
                            pay_page.payStatus = false
                            logMgr.logDataInfo4Qml("===========================点击现金修改扫码权限：{}==============================",pay_page.payStatus.toString())
                            focus = true
                            showPayMethod()
                            if (settingTool.getSetting(SettingEnum.IS_OPEN_CASH_BOX_BEFORE_PAY)) {
                                logMgr.logEvtInfo4Qml("===========================开启钱箱1==============================")
                                printerControl.openCashBox()
                            }

                            return
                        }

                        if (pay_method == EnumTool.PAY_METHOD__VIPCARD) {
                            pay_page.payStatus = false
                            logMgr.logDataInfo4Qml("===========================点击储值卡修改扫码权限：{}==============================",pay_page.payStatus.toString())
                            showVipCardMethod()
                            return
                        }

                        if (pay_method == EnumTool.PAY_METHOD__COMBINED) {
                            showCombinedPopup()

                            if (settingTool.getSetting(SettingEnum.IS_OPEN_CASH_BOX_BEFORE_PAY)) {
                                logMgr.logEvtInfo4Qml("===========================开启钱箱2==============================")
                                printerControl.openCashBox()
                            }
                            return
                        }
                        //线下微信、支付宝
                        pay_page.payStatus = false
                        logMgr.logDataInfo4Qml("===========================点击线下微信、支付宝修改扫码权限：{}==============================",pay_page.payStatus.toString())
                        payMethodControl.payByPayMethod(showPayResults, pay_method)
                    }
                }

                layer.enabled: true // Set Layer for Enable
                layer.effect: OpacityMask {
                    maskSource: Rectangle {
                        width: rect_pay_method.width
                        height: rect_pay_method.height
                        radius: ST.radius
                    }
                }
            }
        }

        CusSpacer {
            Layout.fillWidth: true
        }
    }

    RowLayout {
        id:lay_goods_mgr
        anchors.fill: parent
        anchors.margins: 10 * dpi_ratio
        spacing: 10
        visible: pay_page_root.is_mamager_model

        // NumberAnimation {
        //     id: animation
        //     target: lay_goods_mgr
        //     property: "percent"
        //     from: 0
        //     to: 100
        //     alwaysRunToEnd: true
        //     loops: 1
        //     duration: 1000
        // }

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.maximumWidth: 260 * dpi_ratio
            Layout.preferredWidth: 100
            color: ma_add_goods.pressed ? ST.color_grey : ST.color_white_pure
            radius: ST.radius

            CusText {
                text: qsTr("添加商品")
                color: ST.color_black
                font.bold: true
                font.pixelSize: 28 * dpi_ratio
                Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                anchors.centerIn: parent
            }

            MouseArea {
                id: ma_add_goods
                anchors.fill: parent
                onClicked: {
                    showUploadGoodsPopup()
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.maximumWidth: 260 * dpi_ratio
            Layout.preferredWidth: 100
            color: ma_add_home_goods_kind.pressed ? ST.color_grey : ST.color_white_pure
            radius: ST.radius

            CusText {
                text: qsTr("添加首页分类")
                color: ST.color_black
                font.bold: true
                font.pixelSize: 28 * dpi_ratio
                Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                anchors.centerIn: parent
            }

            MouseArea {
                id: ma_add_home_goods_kind
                anchors.fill: parent
                onClicked: {
                    showAddGoodsHomeCategoryPop()
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.maximumWidth: 260 * dpi_ratio
            Layout.preferredWidth: 100
            color: ma_add_goods_2_v_goods_kind.pressed ? ST.color_grey : ST.color_white_pure
            radius: ST.radius

            CusText {
                text: qsTr("添加商品到此分类")
                color: ST.color_black
                font.bold: true
                font.pixelSize: 28 * dpi_ratio
                Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                anchors.centerIn: parent
            }

            MouseArea {
                id: ma_add_goods_2_v_goods_kind
                anchors.fill: parent
                onClicked: {
                    showAddGoods2CurGoodsKindPop()
                }
            }
        }

        CusSpacer {
            Layout.fillWidth: true
        }

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Layout.maximumWidth: 260 * dpi_ratio
            Layout.preferredWidth: 100
            color: ST.color_transparent
            radius: ST.radius

            CusText {
                text: qsTr("退出商品管理")
                color: ST.color_white_pure
                font.bold: true
                font.pixelSize: 28 * dpi_ratio
                Layout.alignment: Qt.AlignVCenter | Qt.AlignLeft
                anchors.centerIn: parent
            }

            MouseArea {
                id: ma_close_goods_mgr
                anchors.fill: parent
                onClicked: {
                    activeGoodsManager(false)
                }
            }
        }
    }

    Component {
        id: popup_pay_results
        PopupPayResults {
            onSigClose: {
                search_bar.forceActiveFocus()
                shopCartList.tryResetPaidOrder()
            }
            onSigButStatus:{
                sigBut1Status(true)
                logMgr.logEvtInfo4Qml("购物订单结束，更改按钮状态为true:{}",pay_page.payStatus)
            }
        }
    }

    Component {
        id: popup_cash_pay
        PopupCashPay {}

    }

    Component {
        id: popup_vipcard_pay
        PopupVipCardPay {}
    }
    Component {
        id: popup_face
        PopupFace {}
    }

    Component {
        id: popup_combined
        PopupCombined {}
    }

    function getLeftImg(pay_type) {
        switch (pay_type) {
        case EnumTool.PAY_METHOD__CASH:
            return "/Images/PayBtn/left_red.png"
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
            return "/Images/PayBtn/left_blue.png"
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
            return "/Images/PayBtn/left_green.png"
        case EnumTool.PAY_METHOD__FACE:
            return "/Images/PayBtn/left_yellow.png"
        case EnumTool.PAY_METHOD__VIPCARD:
            return "/Images/PayBtn/left_red.png"
        case EnumTool.PAY_METHOD__COMBINED:
            return "/Images/PayBtn/left_green.png"
        case EnumTool.PAY_METHOD__GOODS_MANAGER:
            return "/Images/PayBtn/left_blue.png"
        default:
            return ""
        }
    }

    function getRightImg(pay_type) {
        switch (pay_type) {
        case EnumTool.PAY_METHOD__CASH:
            return "/Images/PayBtn/right_red.png"
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
            return "/Images/PayBtn/right_blue.png"
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
            return "/Images/PayBtn/right_green.png"
        case EnumTool.PAY_METHOD__FACE:
            return "/Images/PayBtn/right_yellow.png"
        case EnumTool.PAY_METHOD__VIPCARD:
            return "/Images/PayBtn/right_red.png"
        case EnumTool.PAY_METHOD__COMBINED:
            return "/Images/PayBtn/right_green.png"
        case EnumTool.PAY_METHOD__GOODS_MANAGER:
            return "/Images/PayBtn/right_blue.png"
        default:
            return ""
        }
    }

    function getCenterImg(pay_type) {
        switch (pay_type) {
        case EnumTool.PAY_METHOD__CASH:
            return "/Images/PayBtn/center_red.png"
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
            return "/Images/PayBtn/center_blue.png"
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
            return "/Images/PayBtn/center_green.png"
        case EnumTool.PAY_METHOD__FACE:
            return "/Images/PayBtn/center_yellow.png"
        case EnumTool.PAY_METHOD__VIPCARD:
            return "/Images/PayBtn/center_red.png"
        case EnumTool.PAY_METHOD__COMBINED:
            return "/Images/PayBtn/center_green.png"
        case EnumTool.PAY_METHOD__GOODS_MANAGER:
            return "/Images/PayBtn/center_blue.png"
        default:
            return ""
        }
    }

    function getLeftImgD(pay_type) {
        switch (pay_type) {
        case EnumTool.PAY_METHOD__CASH:
            return "/Images/PayBtn/left_red_d.png"
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
            return "/Images/PayBtn/left_blue_d.png"
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
            return "/Images/PayBtn/left_green_d.png"
        case EnumTool.PAY_METHOD__FACE:
            return "/Images/PayBtn/left_yellow_d.png"
        case EnumTool.PAY_METHOD__VIPCARD:
            return "/Images/PayBtn/left_red_d.png"
        case EnumTool.PAY_METHOD__COMBINED:
            return "/Images/PayBtn/left_green_d.png"
        case EnumTool.PAY_METHOD__GOODS_MANAGER:
            return "/Images/PayBtn/left_blue_d.png"
        default:
            return ""
        }
    }

    function getRightImgD(pay_type) {
        switch (pay_type) {
        case EnumTool.PAY_METHOD__CASH:
            return "/Images/PayBtn/right_red_d.png"
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
            return "/Images/PayBtn/right_blue_d.png"
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
            return "/Images/PayBtn/right_green_d.png"
        case EnumTool.PAY_METHOD__FACE:
            return "/Images/PayBtn/right_yellow_d.png"
        case EnumTool.PAY_METHOD__VIPCARD:
            return "/Images/PayBtn/right_red_d.png"
        case EnumTool.PAY_METHOD__COMBINED:
            return "/Images/PayBtn/right_green_d.png"
        case EnumTool.PAY_METHOD__GOODS_MANAGER:
            return "/Images/PayBtn/right_blue_d.png"
        default:
            return ""
        }
    }

    function getCenterImgD(pay_type) {
        switch (pay_type) {
        case EnumTool.PAY_METHOD__CASH:
            return "/Images/PayBtn/center_red_d.png"
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
            return "/Images/PayBtn/center_blue_d.png"
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
            return "/Images/PayBtn/center_green_d.png"
        case EnumTool.PAY_METHOD__FACE:
            return "/Images/PayBtn/center_yellow_d.png"
        case EnumTool.PAY_METHOD__VIPCARD:
            return "/Images/PayBtn/center_red_d.png"
        case EnumTool.PAY_METHOD__COMBINED:
            return "/Images/PayBtn/center_green_d.png"
        case EnumTool.PAY_METHOD__GOODS_MANAGER:
            return "/Images/PayBtn/center_blue_d.png"
        default:
            return ""
        }
    }

    function getImgPath(pay_type) {
        switch (pay_type) {
        case EnumTool.PAY_METHOD__CASH:
            return "/Images/PayBtn/icon_cash.png"
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
            return "/Images/PayBtn/icon_alipay.png"
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
            return "/Images/PayBtn/icon_wechat.png"
        case EnumTool.PAY_METHOD__FACE:
            return "/Images/PayBtn/icon_face.png"
        case EnumTool.PAY_METHOD__VIPCARD:
            return "/Images/PayBtn/icon_card.png"
        case EnumTool.PAY_METHOD__COMBINED:
            return "/Images/PayBtn/icon_mix.png"
        case EnumTool.PAY_METHOD__GOODS_MANAGER:
        default:
            return ""
        }
    }

    function getPayName(pay_type) {
        switch (pay_type) {
        case EnumTool.PAY_METHOD__CASH:
            return qsTr("现金")
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
            return qsTr("支付宝")
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
            return qsTr("微信")
        case EnumTool.PAY_METHOD__FACE:
            return qsTr("刷脸付")
        case EnumTool.PAY_METHOD__VIPCARD:
            return qsTr("储值卡")
        case EnumTool.PAY_METHOD__COMBINED:
            return qsTr("组合付")
        case EnumTool.PAY_METHOD__GOODS_MANAGER:
            return qsTr("商品管理")
        default:
            return ""
        }
    }

    function getPayTip(pay_type) {
        switch (pay_type) {
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
            return qsTr("(线下收款)")
        default:
            return ""
        }
    }
}
