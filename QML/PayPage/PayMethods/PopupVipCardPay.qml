﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."
import EnumTool 1.0
import SettingEnum 1.0


Item {
    id: popup_root

    function open() {
        popup_root.visible = true
        chuzhiPayRec.visible = true
        logMgr.logEvtInfo4Qml("开启储值卡弹窗")
    }

    function close() {
        logMgr.logEvtInfo4Qml("关闭储值卡弹窗")
        if(popup_root.visible || paymentImport.visible){
            pay_page.payStatus = true;
            logMgr.logEvtInfo4Qml("储值卡模块存在close时修改状态")
        }
        isOpenCashBox = false
        fuzzy_search_vip_rec.visible = false;
        keyboard_c.closeAll()
        popup_root.visible = false
        search_bar.forceActiveFocus()
        chuzhiResult = 0;
        coverValue = false;
        chuzhikaFailReason = "";
        storeRecEditText.text = "";
        shopCartList.setCashMethod("0");
        chuzhikazhiResult.visible = false
        chuzhiPayRec.visible = false;
        black_shadow.visible = false;
        paymentImport.visible = false;
    }
    function resetData(){
        if (search_input.activeFocus) {
               search_input.text = ""
           }
        if (storeRecEditText.activeFocus) {
               storeRecEditText.text = ""
           }
        if (storeRecEditText43.activeFocus) {
               storeRecEditText43.text = ""
           }
    }
    function addStrNum(str, str2) {
        if (str == "0" && str2 == "0")
            return "0"

        if (str2 == ".") {
            if (str.indexOf(".") == -1) {
                return str += str2
            }
            return str
        }

        if (str == "0")
            return str2

        str += str2

        let arr = str.split(".")

        var ret_str = arr[0]

        if (arr.length == 2) {
            ret_str = arr[0] + "." + arr[1].substring(0, 2)
        }

        return ret_str
    }

    function setInput(input) {
        //unit_price_str = addStrNum(unit_price_str, input)
        if(coverValue == true){
            if (search_input.activeFocus) {
                   search_input.text = "";
                   search_input.text = addStrNum(search_input.text, input);
                   coverValue = false;
               }
        }
        else
        {
            if (search_input.activeFocus) {
                   search_input.text = addStrNum(search_input.text, input)
               }
        }
        if(coverValue == true){
            if (storeRecEditText.activeFocus) {
                   storeRecEditText.text = "";
                   storeRecEditText.text = addStrNum(storeRecEditText.text, input);
                   coverValue = false;
               }
        }
        else
        {
            if (storeRecEditText.activeFocus) {
                   storeRecEditText.text = addStrNum(storeRecEditText.text, input)
               }
        }
        if(coverValue == true){
            if (storeRecEditText43.activeFocus) {
                storeRecEditText43.text = "";
                storeRecEditText43.text = addStrNum(storeRecEditText43.text, input);
                coverValue = false;
               }
        }
        else
        {
            if (storeRecEditText43.activeFocus) {
                   storeRecEditText43.text = addStrNum(storeRecEditText43.text, input)
               }
        }
    }
    function reqCombinedUpdate(){
        var combined_pay_detail = []
        if (storeRecEditText43.text*1 > 0) {
            //现金
            combined_pay_detail.push({
                                         "pay_method": 1,
                                         "pay_money": Number((storeRecEditText43.text*1).toFixed(2))
                                     })
        }
        if (storePaytext231.text*1 > 0) {
            //储值卡
            combined_pay_detail.push({
                                         "pay_method": 5,
                                         "pay_money": Number((storePaytext231.text*1).toFixed(2))
                                     })
        }
        if (storeRecEditText.text*1 > 0) {
            //金圈收款
            combined_pay_detail.push({
                                         "pay_method": 13,
                                         "pay_money": Number((storeRecEditText.text*1).toFixed(2))
                                     })
        }
        logMgr.logEvtInfo4Qml("进入组合支付回调:{}",combined_pay_detail);
        payMethodControl.reqCombinedPay4Qml(function (pay_status, pay_method, json_detail) {
            switch (pay_status)  {
            case EnumTool.PAY_STATUS__SUCCESS:
                logMgr.logEvtInfo4Qml("组合支付信息更新成功！");
                chuzhiResult = 2;
                if (chuzhiResult == 2)
                {
                    shopCartList.resetAllInfo();
                    search_bar.tf_search.focus = true
                    search_bar.tf_search.forceActiveFocus()
                    pay_page.payStatus = true;
                    logMgr.logEvtInfo4Qml("储值卡收款成功 设置pay_page.payStatus为:{}",pay_page.payStatus)
                }
                break
            default:
                logMgr.logEvtInfo4Qml("组合支付信息更新异常！");
                shopCartList.resetAllInfo();
                search_bar.tf_search.focus = true
                search_bar.tf_search.forceActiveFocus()
                pay_page.payStatus = true;
                logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
            };

        }, cur_using_member_unique == "" ? "" : cur_using_member_unique, JSON.stringify(combined_pay_detail))
    }

    Timer
    {
        id: updateReceipt;
        interval: 500;
        repeat: true;
        running: false;
        onTriggered:
        {
            totalPayment = utils4Qml.floorDecimal(shopCartList.getTotalGoodsPrice())
        }
    }
    Rectangle
    {
        id: black_shadow;
        anchors.fill: parent;
        color: "black";
        opacity: 0.6;
        visible: false;
        z: 3;
        MouseArea
        {
            anchors.fill: parent;
            onPressed:
            {
                close();
                }
            }
    }
    property bool isSearch:false;
    property string phoneNumber: "0"
    property string cash: "0"
    property string jinquanPay: "0"
    property int chuzhiResult:0;
    property string accountsReceivable: "0.00";
    property string totalPayment: "0.00"
    property string　chuzhikaFailReason: "";
    property bool isChuzhikaPayment: false;
    property alias _cash_amount: storeRecEditText43.text; //支付金额
    property bool coverValue: false;
    property bool isOpenCashBox:false;
    Rectangle//储值卡单独模块
    {
        id: chuzhiPayRec;
        width: 935  * dpi_ratio;
        height: 810  * dpi_ratio;
        anchors.horizontalCenter: parent.horizontalCenter;
        anchors.verticalCenter: parent.verticalCenter;
        visible: false;
        radius: 10  * dpi_ratio;
        enabled: chuzhiPayRec.visible;
        color: "#FFFFFF";
        z:6;
        onVisibleChanged: {
            if (visible) {
                logMgr.logEvtInfo4Qml("进入储值卡支付页面:{}",cur_using_member_name == "");
                search_input.forceActiveFocus();
                search_input.text = cur_using_member_phone;
                updateReceipt.start();
                black_shadow.visible = true;
                if(cur_using_member_name == ""){
                    search_input.text = "";
                    fuzzy_search_vip_rec.visible = false;
                    fuzzy_search_model.clear();

                   // clearChuzhiPage();
                }
                if( totalPayment !== "0.00" && cur_using_member_balance !=="" && cur_using_member_balance !== "0"){
                    if(((Number(totalPayment) > Number(cur_using_member_balance))&&(Number(totalPayment) > balanceRectext1.text*1))){
                        storeRecEditText.text = (totalPayment - balanceRectext1.text*1).toFixed(2);
                        if((totalPayment-balanceRectext1.text*1-storeRecEditText.text*1) > 0){
                            storeRecEditText43.text = Math.abs((totalPayment-balanceRectext1.text*1-storeRecEditText.text*1)).toFixed(2);
                        }else{
                            storeRecEditText43.text = "";
                        }
                    }
                    else{
                        storeRecEditText.text = "";
                        storeRecEditText43.text = "";
                    }
                }
                else{
                    storeRecEditText.text = "";
                    storeRecEditText43.text = "";
                }
            }
            if(!visible){
                updateReceipt.stop();
            }
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {

            }
        }
        ColumnLayout {
            anchors.fill: parent
            spacing: 0
        Item {
            width: 935  * dpi_ratio;
            height: 108  * dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            Rectangle
            {
                id:chuzhiPayRecMemberRacO
                width: 935  * dpi_ratio;
                height: 108  * dpi_ratio;
                anchors.top: chuzhiPayRec.top;
                anchors.topMargin: 30 * dpi_ratio;
                anchors.horizontalCenter: parent.horizontalCenter;
                color: "#FFFFFF";
                radius: 10  * dpi_ratio;
                Rectangle
                {
                    id:chuzhirootRacO
                    width: 160  * dpi_ratio;
                    height: 40  * dpi_ratio;
                    anchors.top: chuzhiPayRecMemberRacO.top;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    color: "#FFFFFF";
                    radius: 10  * dpi_ratio;
                    Text
                    {
                        font.family:ST.fontFamilySiYan ;
                        font.pointSize: 24  * dpi_ratio;
                        anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        color: "#333333";
                        text: qsTr("储值卡付款");
                        font.bold: true ;
                    }
                }
                Rectangle
                {
                    id:chuzhirootRac1
                    width: 70  * dpi_ratio;
                    height: 70  * dpi_ratio;
                    anchors.top: parent.top;
                    anchors.right: parent.right;
                    color: "#FF5D40";
                    radius: 10  * dpi_ratio;
                    Image {
                        id: close_button
                        width: 70  * dpi_ratio;
                        height: 70  * dpi_ratio;
                        source: "/Images/close_1.png"
                    }
                    MouseArea
                    {
                        anchors.fill: parent;

                        onPressed:
                        {
                            close();
                        }
                    }
                    Rectangle
                    {
                        width: 10  * dpi_ratio;
                        height: 10  * dpi_ratio;
                        anchors.top: parent.top;
                        anchors.left: parent.left;
                        color: "#FF5D40";
                    }
                    Rectangle
                    {
                        width: 10  * dpi_ratio;
                        height: 10  * dpi_ratio;
                        anchors.bottom: parent.bottom;
                        anchors.right: parent.right;
                        color: "#FF5D40";
                    }
                }
            }
        }
        Rectangle
        {
            id:searchRac;
            width:720  * dpi_ratio;
            height: 72  * dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            border.width: 1  * dpi_ratio;
            border.color: "#00bd74";
            radius: 10  * dpi_ratio;
            visible:true;
            Image
            {
                id: searchImage;
                width: 38  * dpi_ratio;
                height: 38  * dpi_ratio;
                anchors.verticalCenter: parent.verticalCenter;
                anchors.left: parent.left;
                anchors.leftMargin: 225  * dpi_ratio;
                source: "/Images/search_image.png";
                visible: search_input.text == "";
            }
            Text
            {
                id: memberNumber;
                width: 140  * dpi_ratio;
                height: 72  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left: searchImage.right;
                anchors.leftMargin:memberRectext211.text != ""?15  * dpi_ratio:15  * dpi_ratio;
                font.family: ST.fontFamilySiYan;
                font.pointSize: 24  * dpi_ratio;
                color: memberRectext211.text != ""?"#333333":"#A8A8A8";
                verticalAlignment: Text.AlignVCenter;
                //text: memberRectext211.text != ""?memberRectext211.text:"会员手机号";
                text:qsTr("会员手机号");
                visible: search_input.text == "";
            }
            TextInput
            {
                id: search_input;
                width: 490  * dpi_ratio;
                height: 42  * dpi_ratio;
                anchors.left: parent.left;
                //anchors.leftMargin: 200*dpi_ratio;
                anchors.verticalCenter: parent.verticalCenter;
                anchors.horizontalCenter: parent.horizontalCenter;
                font.family: ST.fontFamilySiYan;
                font.pointSize: 24 * dpi_ratio;
                verticalAlignment: Text.AlignVCenter;
                color: "#333333";
                horizontalAlignment: Text.AlignHCenter
                maximumLength: 30  * dpi_ratio;
                clip: true;
                MouseArea
                {

                    anchors.fill: search_input;
                    onPressed:
                    {
                        search_input.focus = true;
                        search_input.text = "";
                        cur_using_member_unique = ""
                        shopCartList.clearMemberUnique()
                    }
                }
                onAccepted:
                {
                }
                onTextChanged:
                {                 
                    logMgr.logDataInfo4Qml("search_input.text：{} memberRectext211.text：{}",search_input.text,memberRectext211.text)
                    if (search_input.text !== "" &&(search_input.text !== cur_using_member_phone))
                    {
                        logMgr.logDataInfo4Qml("进入模糊查询操作")
                        storeRecEditText.text = "";
                        storeRecEditText43.text = "";
                        isSearch = true;
                        memberControl.reqFuzzyMemberInfo4Qml(function (data) {
                            var json_doc = JSON.parse(data)
                            var json_doc_data = json_doc.data
                            fuzzy_search_model.clear()
                            for (var i = 0; i < json_doc_data.length; ++i) {
                                var cur_data = json_doc_data[i]
                                fuzzy_search_model.append({
                                                     "cus_id": cur_data.hasOwnProperty("cus_id") ? cur_data.cus_id : "",
                                                     "cusUnique": cur_data.hasOwnProperty("cusUnique") ? cur_data.cusUnique : "",
                                                     "cusName": cur_data.hasOwnProperty("cusName") ? cur_data.cusName : "",
                                                     "cus_points": cur_data.hasOwnProperty("cus_points") ? cur_data.cus_points : "",
                                                     "cusBalance": cur_data.hasOwnProperty("cusBalance") ? cur_data.cusBalance : "",
                                                     "cusPhone": cur_data.hasOwnProperty("cusPhone") ? cur_data.cusPhone : ""
                                                 })
                                fuzzy_search_vip_rec.visible = true;
                            }
                        },search_input.text,"1","2")
                    }
                    if(search_input.text === "" && (chuzhiPayRec.visible === true)){
                        cur_using_member_unique = "";
                        cur_using_member_balance = "";
                        cur_using_member_points = "";
                        cur_using_member_name = "";
                        cur_using_member_phone = "";
                        storeRecEditText.text = "";
                        storeRecEditText43.text = "";
                        fuzzy_search_model.clear();
                        fuzzy_search_vip_rec.visible = false;
                    }
                }
            }
        }
        Rectangle//模糊搜索
        {
            id:fuzzy_search_vip_rec
            anchors.left:searchRac.left;
            anchors.top:searchRac.bottom;
            anchors.topMargin: 5 * dpi_ratio;
            visible: false;
            width: 720 * dpi_ratio;
            height: 265 * dpi_ratio;
            color:"#FFFFFF"
            border.width: 1  * dpi_ratio;
            border.color: "#D8D8D8";
            radius: 10  * dpi_ratio;
            z:100;
            onVisibleChanged:
            {
                if(search_input.text === ""){
                   visible = false;
                }
            }
            ListView
            {
                id:textListView
                anchors.top: fuzzy_search_vip_rec.top;
                anchors.topMargin: 5 * dpi_ratio;
                anchors.horizontalCenter:fuzzy_search_vip_rec.horizontalCenter;
                visible: fuzzy_search_vip_rec.visible;
                clip: true
                width: 716 * dpi_ratio;
                height: 250 * dpi_ratio;

                model:fuzzy_search_model
                delegate: CusRect
                {                                       
                    id: textListViewDelegate;
                    width:  textListView.width
                    height: 50 * dpi_ratio;
                    color:"#FFFFFF"
                    anchors.horizontalCenter:textListView.horizontalCenter;
                    RowLayout {
                        anchors.fill: parent
                        anchors.leftMargin: 20 * dpi_ratio
                        anchors.rightMargin: 20 * dpi_ratio
                        Rectangle
                        {
                            id:textListViewDelegate1
                            anchors.left:textListViewDelegate.left
                            anchors.verticalCenter: textListViewDelegate.verticalCenter
                            width:  236 * dpi_ratio;
                            height: 50 * dpi_ratio;
                            Text
                            {
                                id:textListViewDelegateText1
                                anchors.centerIn: parent
                                font.family: ST.fontFamilySiYan;
                                font.pointSize: 22 * dpi_ratio;
                                font.pixelSize: text.length > 10 ?18 * dpi_ratio:22 * dpi_ratio;
                                color: "#333333";
                                text:cusUnique;
                            }
                        }
                        Rectangle
                        {
                            id:textListViewDelegate2
                            anchors.left:textListViewDelegate1.right
                            anchors.verticalCenter: textListViewDelegate.verticalCenter
                            width: 236 * dpi_ratio;
                            height: 50 * dpi_ratio;
                            Text
                            {
                                id:textListViewDelegateText2
                                anchors.centerIn: parent
                                font.family: ST.fontFamilySiYan;
                                font.pointSize: 22 * dpi_ratio;
                                color: "#333333";
                                text: cusName
                            }
                        }
                        Rectangle
                        {
                            id:textListViewDelegate3
                            anchors.left:textListViewDelegate2.right
                            anchors.verticalCenter: textListViewDelegate.verticalCenter
                            width: 236 * dpi_ratio;
                            height: 50 * dpi_ratio;
                            Text
                            {
                                id:textListViewDelegateText3
                                anchors.centerIn: parent
                                font.family: ST.fontFamilySiYan;
                                font.pointSize: 22 * dpi_ratio;
                                color: "#333333";
                                text: cusPhone
                            }
                        }
                        }
                    Rectangle
                    {
                        id: textListViewLine;
                        width: parent.width
                        height: 1;
                        anchors.bottom:textListViewDelegate.bottom;
                        anchors.horizontalCenter: textListViewDelegate.horizontalCenter;
                        color:"#D8D8D8";
                    }
                    MouseArea
                    {
                        anchors.fill:textListViewDelegate;
                        onClicked:
                        {
                            textListViewDelegateText1.color="red"
                            textListViewDelegateText2.color="red"
                            textListViewDelegateText3.color="red"
                            fuzzy_search_timer.start()
                            memberControl.reqMemberCusByIdForQml(function (is_succ, data) {
                                var json_doc = JSON.parse(data);
                                var json_doc_data = json_doc.data
                                if(is_succ){
                                    if(json_doc.status == 1){
                                        if (!(json_doc.cus_status == ""))
                                        {
                                            var value = json_doc.cus_status;
                                            if (value == "0")
                                            {

                                            }
                                        }
                                        if (!(json_doc_data.cusPoints == ""))
                                        {
                                            cur_using_member_points = json_doc_data.cusPoints;
                                            logMgr.logEvtInfo4Qml("积分：{}",cur_using_member_points)
                                        }
                                        if (!(json_doc_data.cus_balance == ""))
                                        {
                                            cur_using_member_balance = json_doc_data.cus_balance*1 + json_doc_data.cusRebate*1;
                                            logMgr.logEvtInfo4Qml("余额：{}",cur_using_member_balance)
                                        }
                                        if (!(json_doc_data.cusPhone == ""))
                                        {
                                            cur_using_member_phone = json_doc_data.cusPhone;
                                            logMgr.logEvtInfo4Qml("手机号：{}",cur_using_member_phone)
                                        }
                                        if (!(json_doc_data.cusName == ""))
                                        {
                                            cur_using_member_name = json_doc_data.cusName;
                                            logMgr.logEvtInfo4Qml("会员名称：{}",cur_using_member_name)
                                        }
                                        if (!(json_doc_data.cusUnique == ""))
                                        {
                                            cur_using_member_unique = json_doc_data.cusUnique;
                                            logMgr.logEvtInfo4Qml("卡号：{}",cur_using_member_unique)
                                        }
                                        shopCartList.setCurMemberUnique(cur_using_member_unique, cur_using_member_name, cur_using_member_balance, cur_using_member_points);
                                        fuzzy_search_vip_rec.visible = false;
                                        cur_editing_member_unique = cur_using_member_unique;
                                }
                                }
                            }, cusUnique,"")
                        }
                    }
                    Timer
                    {
                        id: fuzzy_search_timer;
                        interval: 100;
                        repeat: false;
                        running: false;
                        onTriggered:
                        {
                            textListViewDelegate1.color="#ffffff"
                            textListViewDelegate2.color="#ffffff"
                            textListViewDelegate3.color="#ffffff"
                            textListViewDelegateText1.color="#333333"
                            textListViewDelegateText2.color="#333333"
                            textListViewDelegateText3.color="#333333"
                        }
                    }
                }
                ListModel
                {
                    id:fuzzy_search_model
                }
            }
        }
        Rectangle
        {
            id:memberRac;
            width:720  * dpi_ratio;
            height:114 * dpi_ratio;
    //        anchors.top: searchRac.bottom;
    //        anchors.topMargin: 12  * dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#F2F2F7";
            border.width: 1  * dpi_ratio;
            border.color: "#F2F2F7";
            radius: 10  * dpi_ratio;
            visible: cur_using_member_name != "" && fuzzy_search_vip_rec.visible == false;
            onVisibleChanged: {

            }
            Rectangle
            {
                id: memberRec1;
                width:110  * dpi_ratio;
                height: 110  * dpi_ratio;
                anchors.left: parent.left;
                anchors.top: parent.top;
                border.width: 1  * dpi_ratio;
                border.color: "#F2F2F7";
                color: "#F2F2F7";
                radius: 10  * dpi_ratio;
                Image
                {
                    id: memberImage1;
                    width: 90  * dpi_ratio;
                    height: 90  * dpi_ratio;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    source: "/Images/huiyuantouxiang.png";
                }
            }
            Rectangle
            {
                id: memberRec2;
                width:230  * dpi_ratio;
                height: 114  * dpi_ratio;
                anchors.left: memberRec1.right;
                anchors.top: memberRec1.top;
                border.width: 1  * dpi_ratio;
                border.color: "#F2F2F7";
                color: "#F2F2F7";
                Rectangle
                {
                    id: memberRec21;
                    width:210  * dpi_ratio;
                    height: 57  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.top: parent.top;
                    border.width: 1  * dpi_ratio;
                    border.color: "#F2F2F7";
                    color: "#F2F2F7";
                    Text
                    {
                        id: memberRectext211;
                        width: 140  * dpi_ratio;
                        height: 35  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: parent.left;
                        anchors.leftMargin: 15  * dpi_ratio;
    //                    horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        font.pointSize: 20  * dpi_ratio;
                        color: "#000000";
                        text: cur_using_member_name;
                    }
                }
                Rectangle
                {
                    id: memberRec22;
                    width:210  * dpi_ratio;
                    height: 57  * dpi_ratio;
                    anchors.left: memberRec21.left;
                    anchors.top: memberRec21.bottom;
                    border.width: 1  * dpi_ratio;
                    border.color: "#F2F2F7";
                    color: "#F2F2F7";
                    Text
                    {
                        id: memberRectext221;
                        width: 80  * dpi_ratio;
                        height: 30  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: parent.left;
                        anchors.leftMargin: 15  * dpi_ratio;
    //                    horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        font.pointSize: 18  * dpi_ratio;
                        color: "black";
                        text: qsTr("会员名称");
                    }
                    Image
                    {
                        id: memberRecImage221;
                        width: 30  * dpi_ratio;
                        height: 30  * dpi_ratio;
                        //anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: memberRectext221.right;
                        anchors.bottom: memberRectext221.bottom;
                        anchors.leftMargin: 50  * dpi_ratio;
                        //anchors.horizontalCenter: parent.horizontalCenter;
                        source: "/Images/memberFlag.png";
                    }
                }
            }
            Rectangle
            {
                id: memberRec3;
                width:190  * dpi_ratio;
                height: 114  * dpi_ratio;
                anchors.left: memberRec2.right;
                anchors.top: memberRec2.top;
                border.width: 1  * dpi_ratio;
                border.color: "#F2F2F7";
                color: "#F2F2F7";
                Rectangle
                {
                    id: balanceRec1;
                    width:210  * dpi_ratio;
                    height: 57  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.top: parent.top;
                    border.width: 1  * dpi_ratio;
                    border.color: "#F2F2F7";
                    color: "#F2F2F7";
                    TextInput
                    {
                        id: balanceRectext1;
                        width: 120  * dpi_ratio;
                        height: 35  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        //anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: parent.left;
                        anchors.leftMargin: 15  * dpi_ratio;
                        verticalAlignment: Text.AlignVCenter;
                        font.pointSize: 20  * dpi_ratio;
                        color: "#FF9500";
                        text: cur_using_member_balance;
                        enabled:false;
                        MouseArea
                        {
                        }
                        onAccepted:
                        {
                        }
                        onTextChanged:
                        {
                            if(balanceRectext1.text != ""){
                                logMgr.logEvtInfo4Qml("totalPayment:{}",totalPayment);
                                logMgr.logEvtInfo4Qml("cur_using_member_balance:{}",totalPayment);
                                if( totalPayment !== "0.00" && cur_using_member_balance !=="" && cur_using_member_balance !== "0"){
                                    if( (Number(totalPayment) > Number(cur_using_member_balance))){
                                        storeRecEditText.text = (totalPayment - balanceRectext1.text*1).toFixed(2);
                                        storeRecEditText43.text = Math.abs((totalPayment-balanceRectext1.text*1-storeRecEditText.text*1)).toFixed(2);

                                    }
                                }
                            }

                        }
                    }
                }
                Rectangle
                {
                    id: balanceRec2;
                    width:210  * dpi_ratio;
                    height: 57  * dpi_ratio;
                    anchors.left: balanceRec1.left;
                    anchors.top: balanceRec1.bottom;
                    border.width: 1  * dpi_ratio;
                    border.color: "#F2F2F7";
                    color: "#F2F2F7";
                    Image
                    {
                        id: balanceRecImage21;
                        width: 35  * dpi_ratio;
                        height: 35  * dpi_ratio;
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: parent.left;
                        anchors.leftMargin: 15  * dpi_ratio;
                        source: "/Images/member_balance.png";
                    }
                    Text
                    {
                        id: balanceRectext21;
                        width: 72  * dpi_ratio;
                        height: 30  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        anchors.left: balanceRecImage21.right;
                        anchors.bottom: balanceRecImage21.bottom;
    //                    anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        //horizontalAlignment: Text.AlignHCenter;
                        anchors.leftMargin: 5  * dpi_ratio;
                        verticalAlignment: Text.AlignVCenter;
                        font.pointSize: 18  * dpi_ratio;
                        color: "black";
                        text: qsTr("余额");
                    }
                }
            }
            Rectangle
            {
                id: memberRec4;
                width:190  * dpi_ratio;
                height: 117  * dpi_ratio;
                anchors.left: memberRec3.right;
                anchors.top: memberRec3.top;
                border.width: 1  * dpi_ratio;
                border.color: "#F2F2F7";
                color: "#F2F2F7";
                Rectangle
                {
                    id: integralRec1;
                    width:190  * dpi_ratio;
                    height: 57  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.top: parent.top;
                    border.width: 1  * dpi_ratio;
                    border.color: "#F2F2F7";
                    color: "#F2F2F7";
                    Text
                    {
                        id: integralRectext1;
                        width: 12  * dpi_ratio;
                        height: 15  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        //anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: parent.left;
                        anchors.leftMargin: 15   * dpi_ratio;
                        //horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        font.pointSize: 20  * dpi_ratio;
                        color: "#00BD75";
                        text: cur_using_member_points;
                    }
                }
                Rectangle
                {
                    id: integralRec2;
                    width:110  * dpi_ratio;
                    height: 57  * dpi_ratio;
                    anchors.left: integralRec1.left;
                    anchors.top: integralRec1.bottom;
                    border.width: 1  * dpi_ratio;
                    border.color: "#F2F2F7";
                    color: "#F2F2F7";
                    Image
                    {
                        id: integralRecImage21;
                        width: 35  * dpi_ratio;
                        height: 35  * dpi_ratio;
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: parent.left;
                        anchors.leftMargin: 15   * dpi_ratio;
                        source: "/Images/jifen.png";
                    }
                    Text
                    {
                        id: integralRectext21;
                        width: 72  * dpi_ratio;
                        height: 30  * dpi_ratio;
                        anchors.bottom: integralRecImage21.bottom;
                        anchors.left: integralRecImage21.right;
                        anchors.leftMargin: 5  * dpi_ratio;
                        anchors.bottomMargin: 2  * dpi_ratio;
                        horizontalAlignment: Text.AlignLeft;
                        verticalAlignment: Text.AlignVCenter;
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 18  * dpi_ratio;
                        color: "black";
                        text: qsTr("积分");
                    }
                }
            }

        }
        Rectangle
        {
            id:nomemberRac1;
            width:720  * dpi_ratio;
            height: 114  * dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            border.width: 2  * dpi_ratio;
            border.color: "#FFFFFF";
            radius: 10  * dpi_ratio;
            visible: cur_using_member_name === "" && fuzzy_search_vip_rec.visible == false;
            Rectangle
            {
                id: nomemberRac11;
                width:120  * dpi_ratio;
                height: 110  * dpi_ratio;
                anchors.left: parent.left;
                anchors.verticalCenter: parent.verticalCenter;
                anchors.leftMargin: 130  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                radius: 10  * dpi_ratio;
                Image
                {
                    id: nomemberRac11Image;
                    width: 110  * dpi_ratio;
                    height: 85  * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    source: "/Images/noSearch.png";
                }
            }
            Rectangle
            {
                id: nomemberRac12;
                width:330  * dpi_ratio;
                height: 110  * dpi_ratio;
                anchors.left: nomemberRac11.right;
                anchors.top: nomemberRac11.top;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                Rectangle
                {
                    id: nomemberRac121;
                    width:330  * dpi_ratio;
                    height: 45  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.top: parent.top;
                    anchors.topMargin: 20 * dpi_ratio;
                    anchors.leftMargin: 15  * dpi_ratio;
                    border.width: 1  * dpi_ratio;
                    border.color: "#FFFFFF";
                    color: "#FFFFFF";
                    Text
                    {
                        id: nomemberRac121text1;
                        width: 12  * dpi_ratio;
                        height: 45  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        anchors.left: nomemberRac121.left;
                        //anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        //horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        font.pointSize: 20  * dpi_ratio;
                        color: "#000000";
                        text: qsTr("未搜索到相关会员");
                    }
                }
                Rectangle
                {
                    id: nomemberRac122;
                    width:330  * dpi_ratio;
                    height: 45  * dpi_ratio;
                    anchors.left: nomemberRac121.left;
                    anchors.top: nomemberRac121.bottom;
                    border.width: 1  * dpi_ratio;
                    //anchors.leftMargin: 5  * dpi_ratio;
                    border.color: "#FFFFFF";
                    color: "#FFFFFF";
                    Text
                    {
                        id: nomemberRac122text1;
                        width:  80  * dpi_ratio;
                        height: 45  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        anchors.left: nomemberRac122.left;
                        //anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        //horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        font.pointSize: 18  * dpi_ratio;
                        color: "#999999";
                        //text: qsTr("请更改搜索信息或")+"<font  color='#00BD75'>新增会员</font>";
                        text: qsTr("请更改搜索信息或")+qsTr("新增会员");
                    }
                }

            }
        }
        Rectangle
        {
            id: storePayRec1;
            width:720  * dpi_ratio;
            height: 142  * dpi_ratio;
            border.width: 1  * dpi_ratio;
            border.color: "#D8D8D8";
            radius: 10  * dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            visible: fuzzy_search_vip_rec.visible == false
            Rectangle
            {
                id: storeRec1;
                width:170  * dpi_ratio;
                height: 142  * dpi_ratio;
                anchors.left: parent.left;
                anchors.leftMargin: 7  * dpi_ratio;
                anchors.top: parent.top;
                //anchors.topMargin: 1  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#D8D8D8";
                //radius: 10  * dpi_ratio;
                color: "#FFFFFF";
                Rectangle
                {
                    id: storeRec12;
                    width:168  * dpi_ratio;
                    height: 70  * dpi_ratio;
                    anchors.left: parent.left;
                    //anchors.leftMargin: 1  * dpi_ratio;
                    anchors.top: parent.top;
                    anchors.topMargin:  1  * dpi_ratio;
                    border.width: 1  * dpi_ratio;
                    border.color: "#FFFFFF";
                    color: "#FFFFFF";
                    Text
                    {
                        id: storePaytext1;
                        width: 12  * dpi_ratio;
                        height: 35  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        font.pointSize: 18  * dpi_ratio;
                        color: "#A8A8A8";
                        text: qsTr("应收");
                    }
                }
                Rectangle
                {
                    id: storeRec13;
                    width:168  * dpi_ratio;
                    height: 70  * dpi_ratio;
                    anchors.left: storeRec12.left;
                    anchors.top: storeRec12.bottom;
                    //anchors.topMargin:  1  * dpi_ratio;
                    border.width: 1  * dpi_ratio;
                    border.color: "#FFFFFF";
                    color: "#FFFFFF";

                    Text
                    {
                        id: storePaytext11;
                        width: 165  * dpi_ratio;
                        height: 35  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        font.pointSize: 22  * dpi_ratio;
                        color: "#000000";
                        //text: totalPayment !== "0.00"?qsTr("￥")+totalPayment:""
                        text: "￥"+totalPayment
                        onTextChanged:
                        {
                            if( totalPayment != "0.00" && cur_using_member_phone != "0"){
                                if( (totalPayment > balanceRectext1.text*1)){
                                    if( totalPayment !== "0.00" && cur_using_member_balance !=="" && cur_using_member_balance !== "0"){
                                        if( (Number(totalPayment) > Number(cur_using_member_balance))){
                                            storeRecEditText.text = (totalPayment - balanceRectext1.text*1).toFixed(2);
                                            storeRecEditText43.text = Math.abs((totalPayment-balanceRectext1.text*1-storeRecEditText.text*1)).toFixed(2);

                                        }
                                    }
                                } else{
                                    storeRecEditText.text = "0.00"
                                }
                            }

                        }
                    }
                }

            }
            Rectangle
            {
                id: storeRec2;
                width:170  * dpi_ratio;
                height: 140  * dpi_ratio;
                anchors.left: storeRec1.right;
                anchors.top: parent.top;
                anchors.topMargin: 1  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                Rectangle
                {
                    id: storeRec22;
                    width:168  * dpi_ratio;
                    height: 70  * dpi_ratio;
                    anchors.left: parent.left;
                    //anchors.leftMargin: 1  * dpi_ratio;
                    anchors.top: parent.top;
                    //anchors.topMargin: 1  * dpi_ratio;
                    border.width: 1  * dpi_ratio;
                    border.color: "#FFFFFF";
                    color: "#FFFFFF";
                    Image
                    {
                        id: storePayImage221;
                        width: 35  * dpi_ratio;
                        height: 35  * dpi_ratio;
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: parent.left;
                        anchors.leftMargin: 30   * dpi_ratio;
                        source: "/Images/storemoney.png";
                    }
                    Text
                    {
                        id: storePaytext221;
                        width: 72  * dpi_ratio;
                        height: 30  * dpi_ratio;
                        //anchors.bottom: storePayImage221.bottom;
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: storePayImage221.right;
                        anchors.leftMargin: 5  * dpi_ratio;
                        horizontalAlignment: Text.AlignLeft;
                        verticalAlignment: Text.AlignVCenter;
                        //anchors.bottomMargin: 2  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 18  * dpi_ratio;
                        color: "#000000";
                        text: qsTr("储值卡");
                    }
                }
                Rectangle
                {
                    id: storeRec23;
                    width:168  * dpi_ratio;
                    height: 70  * dpi_ratio;
                    anchors.left: storeRec22.left;
                    anchors.top: storeRec22.bottom;
                    //border.width: 1  * dpi_ratio;
                    border.color: "#FFFFFF";
                    color: "#FFFFFF";
                    Text
                    {
                        id: storePaytext231;
                        width: 168  * dpi_ratio;
                        height: 23  * dpi_ratio;
                        anchors.top: parent.top;
                        anchors.left: parent.left;
                        anchors.topMargin: 2  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 22  * dpi_ratio;
                        anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        color: "#FF3835";
                        text:balanceRectext1.text !== ""?balanceRectext1.text:qsTr("请选会员");
                    }

                }
            }
            Rectangle
            {
                id: storeRec3;
                width:180  * dpi_ratio;
                height: 142  * dpi_ratio;
                anchors.left: storeRec2.right;
                anchors.leftMargin: 1 *dpi_ratio
                anchors.top: parent.top;
                //anchors.topMargin: 1  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#D8D8D8";
                color: "#FFFFFF";
                Rectangle
                {
                    id: storeRec32;
                    width:168 * dpi_ratio;
                    height: 70 * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 1  * dpi_ratio;
                    anchors.top: parent.top;
                    anchors.topMargin: 1  * dpi_ratio;
                    border.width: 1  * dpi_ratio;
                    border.color: "#FFFFFF";
                    color: "#FFFFFF";
                    Image
                    {
                        id: storePayImage321;
                        width: 35  * dpi_ratio;
                        height: 35  * dpi_ratio;
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: parent.left;
                        anchors.leftMargin: 15   * dpi_ratio;
                        source: "/Images/jinquanpay.png";
                    }
                    Text
                    {
                        id: storePaytext321;
                        width: 72  * dpi_ratio;
                        height: 30  * dpi_ratio;
                        anchors.bottom: storePayImage321.bottom;
                        anchors.left: storePayImage321.right;
                        anchors.leftMargin: 5  * dpi_ratio;
                        anchors.bottomMargin: 4  * dpi_ratio;
                        horizontalAlignment: Text.AlignLeft;
                        verticalAlignment: Text.AlignVCenter;
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 18  * dpi_ratio;
                        color: "#000000";
                        text: qsTr("金圈收款");
                    }
                }
                Rectangle
                {
                    id: storeRec33;
                    width:168  * dpi_ratio;
                    height: 70  * dpi_ratio;
                    anchors.left: storeRec32.left;
                    anchors.top: storeRec32.bottom;
                    border.width: 1  * dpi_ratio;
                    border.color: "#FFFFFF";
                    color: "#FFFFFF";
                    TextInput
                    {
                        id: storeRecEditText;
                        anchors.centerIn: parent;
                        width: 168 *dpi_ratio;
                        height: 40*dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 22*dpi_ratio;
                        horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        color: "#333333";
                        maximumLength: 30
                        clip: true;
                        enabled:((totalPayment > balanceRectext1.text*1)&&(storePaytext231.text !== qsTr("请选会员")));
                        validator:DoubleValidator{decimals:(2);
                        bottom: 0.00; top: 9999999.00 ;notation:DoubleValidator.StandardNotation}
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                parent.forceActiveFocus();
                                parent.selectAll();
                                coverValue = true;

                            }
                        }
                        onAccepted:
                        {
                        }
                        onTextChanged:
                        {
                            if(storeRecEditText.text == ".")
                            {
                                storeRecEditText.text = "0.";
                            }
                            if(storeRecEditText.text == "NaN")
                            {
                                storeRecEditText.text = "0.00";
                            }
                            if (((balanceRectext1.text*1) + (storeRecEditText.text*1)) > totalPayment)
                            {
                                if((balanceRectext1.text*1) < totalPayment){
                                    storeRecEditText.text = Math.abs(Number(totalPayment)-(balanceRectext1.text*1)).toFixed(2);
                                }
                            }
                            else
                            {
                                if(storeRecEditText.focus == true){
                                     storeRecEditText43.text = Math.abs((Number(totalPayment)-(balanceRectext1.text*1)-(storeRecEditText.text*1))).toFixed(2);
                                }
                            }
                        }
                    }
                    Text
                    {
                        id: storePaytext331;
                        width: 170  * dpi_ratio;
                        height: 70  * dpi_ratio;
                        anchors.topMargin: 2  * dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 22  * dpi_ratio;
                        anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        color: "#999999";
                        text: nomemberRac1.visible?qsTr("无法收款"):qsTr("请输入");
                        visible: storeRecEditText.text == "";
                    }

                }
            }
            Rectangle
            {
                id: storeRec4;
                width:185  * dpi_ratio;
                height: 140  * dpi_ratio;
                anchors.left: storeRec3.right;
                anchors.top: parent.top;
                anchors.topMargin: 1  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                Rectangle
                {
                    id: storeRec42;
                    width:185  * dpi_ratio;
                    height: 70  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 1  * dpi_ratio;
                    anchors.top: parent.top;
                    border.width: 1  * dpi_ratio;
                    border.color: "#ffffff";
                    color: "#ffffff";
                    Image
                    {
                        id: storePayImage421;
                        width: 35  * dpi_ratio;
                        height: 35  * dpi_ratio;
                        anchors.verticalCenter: parent.verticalCenter;
                        anchors.left: parent.left;
                        anchors.leftMargin: 45   * dpi_ratio;
                        source: "/Images/cashred.png";
                    }
                    Text
                    {
                        id: storePaytext421;
                        width: 72  * dpi_ratio;
                        height: 30  * dpi_ratio;
                        anchors.bottom: storePayImage421.bottom;
                        anchors.left: storePayImage421.right;
                        anchors.leftMargin: 5  * dpi_ratio;
                        anchors.bottomMargin: 2  * dpi_ratio;
                        horizontalAlignment: Text.AlignLeft;
                        verticalAlignment: Text.AlignVCenter;
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 18  * dpi_ratio;
                        color: "#000000";
                        text: qsTr("现金");
                    }
                }
                Rectangle
                {
                    id: storeRec43;
                    width:185  * dpi_ratio;
                    height: 70  * dpi_ratio;
                    anchors.left: storeRec42.left;
                    anchors.top: storeRec42.bottom;
                    border.width: 1  * dpi_ratio;
                    border.color: "#ffffff";
                    color: "#ffffff";

                    TextInput
                    {
                        id: storeRecEditText43;
                        anchors.centerIn: parent;
                        width: 168*dpi_ratio;
                        height: 40*dpi_ratio;
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 22*dpi_ratio;
                        horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        color: "#333333";
                        maximumLength: 30
                        clip: true;
                        enabled:((totalPayment > balanceRectext1.text*1)&&(storePaytext231.text !== qsTr("请选会员")));
                        validator:DoubleValidator{decimals:(2);
                        bottom: 0.00; top: 9999999.00 ;notation:DoubleValidator.StandardNotation}
                        anchors.leftMargin: 27  * dpi_ratio;
                        MouseArea
                        {
                        }
                        onAccepted:
                        {
                        }
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                parent.forceActiveFocus();
                                parent.selectAll();
                                coverValue = true;

                            }
                        }
                        onTextChanged: {
                            if(storeRecEditText43.text == ".")
                            {
                                storeRecEditText43.text = "0.";
                            }
                            if(storeRecEditText43.text == "NaN")
                            {
                                storeRecEditText43.text = "0.00";
                            }
                            if (((balanceRectext1.text*1)+(storeRecEditText43.text*1)) > totalPayment)
                            {
                                storeRecEditText43.text = Math.abs(Number(totalPayment)-(balanceRectext1.text*1)).toFixed(2);
                                if(storeRecEditText43.focus == true)
                                {
                                storeRecEditText.text = Math.abs((Number(totalPayment)-(balanceRectext1.text*1)-(storeRecEditText43.text*1))).toFixed(2);
                                }

                            }
                            else
                            {
                                if(storeRecEditText43.focus == true)
                                {
                                storeRecEditText.text = Math.abs((Number(totalPayment)-(balanceRectext1.text*1)-(storeRecEditText43.text*1))).toFixed(2);
                                }
                            }
                            if(storeRecEditText43.text*1 > 0){
                                if(!isOpenCashBox){
                                    if (settingTool.getSetting(SettingEnum.IS_OPEN_CASH_BOX_BEFORE_PAY)) {
                                        printerControl.openCashBox()
                                        isOpenCashBox = true
                                    }
                                }
                            }
                        }
                    }
                    Text
                    {
                        id: storePaytext431;
                        width: 72  * dpi_ratio;
                        height: 30  * dpi_ratio;
                        anchors.top: parent.top;
                        anchors.left: parent.left;
                        anchors.topMargin: 2  * dpi_ratio;
                        anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        font.family: ST.fontFamilySiYan;
                        font.pointSize: 20  * dpi_ratio;
                        color: "#999999";
                        text: nomemberRac1.visible?qsTr("无法收款"):qsTr("请输入");
                        visible: storeRecEditText43.text == ""
                    }

                }
            }



    }
        CusRect {
            id: payKeyboardDigital;
            width: 720*dpi_ratio;
            height: 316*dpi_ratio;
            color: "#ffffff";
            visible: true;
            anchors.horizontalCenter: parent.horizontalCenter;
            radius: 10 *dpi_ratio;

            RowLayout {
                anchors.fill: parent

                CusRect {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    GridLayout {
                        id: gl_calc_btns

                        anchors.fill: parent
                        columns: 4
                        rows: 3

                        columnSpacing: 5 * dpi_ratio
                        rowSpacing: 5 * dpi_ratio

                        property real col_width: (width - ((columns - 1) * rowSpacing)) / columns
                        property real col_height: (height - ((rows - 1) * columnSpacing)) / rows

                        function prefWidth(item) {
                            return col_width * item.Layout.columnSpan
                        }
                        function prefHeight(item) {
                            return col_height * item.Layout.rowSpan
                        }

                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "7"


                        }

                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "8"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "9"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "0"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "4"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "5"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "6"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "."
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "1"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "2"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "3"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: qsTr("清空")
                            onPressKey: {
                                resetData()
                            }
                        }
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 130 * dpi_ratio
                    color: ST.color_transparent

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 10 * dpi_ratio

                        property real cell_height: gl_calc_btns.col_height

                        KeyRect2 {
                            Layout.preferredHeight: parent.cell_height
                            Layout.fillWidth: true
                            key_text: ""
                            color: "#FFFFFF"
                            text.color: "#FFFFFF"
                            Image {
                                anchors.fill: parent
                                source: "/Images/tuige.png"
                                fillMode: Image.PreserveAspectFit
                                scale: 0.5
                            }
                            onPressKey: {
                                if (search_input.activeFocus) {
                                       search_input.text = search_input.text.slice(0, -1);
                                   }
                                if (storeRecEditText.activeFocus) {
                                       storeRecEditText.text = storeRecEditText.text.slice(0, -1);
                                   }
                                if (storeRecEditText43.activeFocus) {
                                       storeRecEditText43.text = storeRecEditText43.text.slice(0, -1);
                                   }
                            }
                        }
                        KeyRect2 {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            key_text: qsTr("确认")
                            color: "#00BD75"
                            text.color: "white"
                            onPressKey: {
                                if ((balanceRectext1.text ==""||balanceRectext1.text == 0) && (shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7")) {
                                    toast.openWarn(qsTr("未输入会员号或储值卡余额为0!"))
                                    return
                                }
                                if (totalPayment === "0.00") {
                                    toast.openWarn(qsTr("应收金额不可为0"))
                                    return
                                }
                            }
                        }
                    }
                }
            }
        }
        component KeyRect: CusRect {
            id: rect_key
            property string key_text: "0"
            signal pressKey(var key)
            color: ST.color_transparent
            radius: 10 *dpi_ratio;

            border {
                width: 2 * dpi_ratio
                color: "#E5E5E5"
            }

            CusText {
                id:rect_key_id
                anchors.centerIn: parent
                text: rect_key.key_text
                font.weight: Font.Bold
                font.pixelSize: 38 * dpi_ratio
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    rect_key.color = "#17262B";
                    rect_key_id.color = "white";
                    clickAmation.start();
                    rect_key.pressKey(rect_key.key_text)
                }
            }
            Timer
            {
                id: clickAmation;
                running: false;
                repeat: false;
                interval: 100
                onTriggered:
                {
                    rect_key.color = "white";
                    rect_key_id.color = "black";
                }
            }
            onPressKey: {
                setInput(key)
            }
        }
        component KeyRect2: CusRect {
            id: rect_key2
            property string key_text: "0"
            signal pressKey(var key)

            property alias text: text
            color: ST.color_transparent
            radius: 10 *dpi_ratio;

            border {
                width: 2 * dpi_ratio
                color: "#E5E5E5"
            }
            CusText {
                id: text
                anchors.centerIn: parent
                text: rect_key2.key_text
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    rect_key2.color = "#17262B";
                    text.color = "white";
                    clickAmation2.start();
                    rect_key2.pressKey(rect_key2.key_text)
                    if(rect_key2.key_text == qsTr("确认")){
                        logMgr.logEvtInfo4Qml(" ((storeRecEditText==={}",((storeRecEditText.text*1+balanceRectext1.text*1+storeRecEditText43.text*1).toFixed(2)));
                        if((((storeRecEditText.text*1+balanceRectext1.text*1+storeRecEditText43.text*1).toFixed(2)) >= Number(totalPayment))&&(totalPayment !== "0.00"))
                        {
                            //金圈收款额为0
                            if(storeRecEditText.text == "0.00" || storeRecEditText.text == "" ||storeRecEditText.text == "0")
                            {
                              logMgr.logEvtInfo4Qml(" Enter stored value card payment!{}",storeRecEditText43.text == "");
                              //现金收款额为0
                              if(storeRecEditText43.text == "0.00" || storeRecEditText43.text == ""){
                                  isChuzhikaPayment = true;
                                 logMgr.logEvtInfo4Qml(" Enter stored value card payment!chuzhika");
                                  //单独储值卡收款
                                  chuzhiResult = 1;
                                  chuzhikazhiResult.visible = true;
                                  search_input.focus = false;
                                  chuzhiPayRec.visible = false;
                                  if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
                                      logMgr.logEvtInfo4Qml(" ningyu vip");
                                      //宁宇会员
                                      payMethodControl.payByMemberBalance_v2_ningyu(function (pay_status_enum, pay_method_enum, json_detail) {
                                          switch (pay_status_enum) {
                                          case EnumTool.PAY_STATUS__SUCCESS:
                                              logMgr.logEvtInfo4Qml("支付成功");
                                              chuzhiResult = 2;
                                              if (chuzhiResult == 2)
                                              {
                                                  shopCartList.resetAllInfo();
                                                  search_bar.tf_search.focus = true
                                                  search_bar.tf_search.forceActiveFocus()
                                                  pay_page.payStatus = true;
                                                  logMgr.logEvtInfo4Qml("储值卡收款成功 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                              }
                                              break
                                          case EnumTool.PAY_STATUS__PAYING:
                                              chuzhiResult = 1;
                                              logMgr.logEvtInfo4Qml("支付中");
                                              break
                                          case EnumTool.PAY_STATUS__UNKNOW:
                                              chuzhiResult = 3;
                                              logMgr.logEvtInfo4Qml("不存在");
                                              shopCartList.resetAllInfo();
                                              search_bar.tf_search.focus = true
                                              search_bar.tf_search.forceActiveFocus()
                                              pay_page.payStatus = true;
                                              logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                              break
                                          case EnumTool.PAY_STATUS__CANCEL:
                                              chuzhiResult = 3;
                                              logMgr.logEvtInfo4Qml("订单未支付");
                                              shopCartList.resetAllInfo();
                                              search_bar.tf_search.focus = true
                                              search_bar.tf_search.forceActiveFocus()
                                              pay_page.payStatus = true;
                                              logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                              break
                                          case EnumTool.PAY_STATUS__ERROR:
                                              //rect_content.state = "ERROR"
                                              chuzhiResult = 3;
                                              logMgr.logEvtInfo4Qml("支付出错");
                                              chuzhikaFailReason = "支付出错";
                                              shopCartList.resetAllInfo();
                                              search_bar.tf_search.focus = true
                                              search_bar.tf_search.forceActiveFocus()
                                              pay_page.payStatus = true;
                                              logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                              break
                                          };
                                      },cur_using_member_unique,5,totalPayment*1,0,0)
                                  }
                                  else{
                                      //普通会员
                                      logMgr.logEvtInfo4Qml(" no ningyu vip");
                                      payMethodControl.payByMemberBalance_v2(function (pay_status_enum, pay_method_enum, json_detail) {
                                          switch (pay_status_enum) {
                                          case EnumTool.PAY_STATUS__SUCCESS:
                                              logMgr.logEvtInfo4Qml("支付成功");
                                              chuzhiResult = 2;
                                              if (chuzhiResult == 2)
                                              {
                                                  shopCartList.resetAllInfo();
                                                  search_bar.tf_search.focus = true
                                                  search_bar.tf_search.forceActiveFocus()
                                                  pay_page.payStatus = true;
                                                  logMgr.logEvtInfo4Qml("储值卡收款成功 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                              }
                                              break
                                          case EnumTool.PAY_STATUS__PAYING:
                                              chuzhiResult = 1;
                                              logMgr.logEvtInfo4Qml("支付中");
                                              break
                                          case EnumTool.PAY_STATUS__UNKNOW:
                                              chuzhiResult = 3;
                                              logMgr.logEvtInfo4Qml("不存在");
                                              shopCartList.resetAllInfo();
                                              search_bar.tf_search.focus = true
                                              search_bar.tf_search.forceActiveFocus()
                                              pay_page.payStatus = true;
                                              logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                              break
                                          case EnumTool.PAY_STATUS__CANCEL:
                                              chuzhiResult = 3;
                                              logMgr.logEvtInfo4Qml("订单未支付");
                                              shopCartList.resetAllInfo();
                                              search_bar.tf_search.focus = true
                                              search_bar.tf_search.forceActiveFocus()
                                              pay_page.payStatus = true;
                                              logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                              break
                                          case EnumTool.PAY_STATUS__ERROR:
                                              //rect_content.state = "ERROR"
                                              chuzhiResult = 3;
                                              logMgr.logEvtInfo4Qml("支付出错");
                                              chuzhikaFailReason = "支付出错";
                                              shopCartList.resetAllInfo();
                                              search_bar.tf_search.focus = true
                                              search_bar.tf_search.forceActiveFocus()
                                              pay_page.payStatus = true;
                                              logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                              close()
                                              break
                                          };
                                      },cur_using_member_unique)
                              }
                              }
                              else{
                                  isChuzhikaPayment = true;
                                  if(storePaytext231.text == "0.00" || storePaytext231.text == "" || storePaytext231.text == "0"){
                                      // 储值卡收款为0 单独现金收款
                                      if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
                                      }
                                      else{

                                          chuzhiResult = 1;
                                          chuzhikazhiResult.visible = true;
                                          search_input.focus = false;
                                          chuzhiPayRec.visible = false;
                                          shopCartList.setReceivedCash(_cash_amount);//更新现金支付金额
                                          shopCartList.setCashMethod("1");
                                          payMethodControl.cashPay4Qml(function (pay_status_enum, pay_method_enum, json_detail) {
                                              switch (pay_status_enum) {
                                              case EnumTool.PAY_STATUS__SUCCESS:
                                                  logMgr.logEvtInfo4Qml("支付成功");
                                                  chuzhiResult = 2;
                                                  if (chuzhiResult == 2)
                                                  {
                                                      shopCartList.resetAllInfo();
                                                      search_bar.tf_search.focus = true
                                                      search_bar.tf_search.forceActiveFocus()
                                                      pay_page.payStatus = true;
                                                      logMgr.logEvtInfo4Qml("储值卡收款成功 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                                  }
                                                  //shopCartList.resetAllInfo();
                                                  break
                                              case EnumTool.PAY_STATUS__PAYING:
                                                  chuzhiResult = 1;
                                                  logMgr.logEvtInfo4Qml("支付中");
                                                  break
                                              case EnumTool.PAY_STATUS__UNKNOW:
                                                  chuzhiResult = 3;
                                                  shopCartList.resetAllInfo();
                                                  search_bar.tf_search.focus = true
                                                  search_bar.tf_search.forceActiveFocus()
                                                  pay_page.payStatus = true;
                                                  logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                                  logMgr.logEvtInfo4Qml("不存在");
                                                  break
                                              case EnumTool.PAY_STATUS__CANCEL:
                                                  chuzhiResult = 3;
                                                  shopCartList.resetAllInfo();
                                                  search_bar.tf_search.focus = true
                                                  search_bar.tf_search.forceActiveFocus()
                                                  pay_page.payStatus = true;
                                                  logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                                  logMgr.logEvtInfo4Qml("订单未支付");
                                                  break
                                              case EnumTool.PAY_STATUS__ERROR:
                                                  chuzhiResult = 3;
                                                  shopCartList.resetAllInfo();
                                                  search_bar.tf_search.focus = true
                                                  search_bar.tf_search.forceActiveFocus()
                                                  pay_page.payStatus = true;
                                                  logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                                  logMgr.logEvtInfo4Qml("支付出错");
                                                  chuzhikaFailReason = "支付出错";
                                                  close()
                                                  break
                                              };
                                          })
                                  }
                                  }
                                  else{
                                      //储值卡收款不为0，现金收款不为0
                                      chuzhiResult = 1;
                                      chuzhikazhiResult.visible = true;
                                      search_input.focus = false;
                                      chuzhiPayRec.visible = false;
                                      if((storePaytext231.text*1 >= Number(totalPayment))){
                                          storeRecEditText43.text = "";
                                          toast.openWarn(qsTr("请确认支付金额后重新支付!"))
                                      }else{
                                          if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
                                              logMgr.logEvtInfo4Qml(" ningyu cash + vip");
                                              //宁宇会员
                                              payMethodControl.payByMemberBalance_v2_ningyu(function (pay_status_enum, pay_method_enum, json_detail) {
                                                  switch (pay_status_enum) {
                                                  case EnumTool.PAY_STATUS__SUCCESS:
                                                      logMgr.logEvtInfo4Qml("支付成功");
                                                      chuzhiResult = 2;
                                                      if (chuzhiResult == 2)
                                                      {
                                                          shopCartList.resetAllInfo();
                                                          search_bar.tf_search.focus = true
                                                          search_bar.tf_search.forceActiveFocus()
                                                          pay_page.payStatus = true;
                                                          logMgr.logEvtInfo4Qml("储值卡收款成功 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                                      }
                                                      break
                                                  case EnumTool.PAY_STATUS__PAYING:
                                                      chuzhiResult = 1;
                                                      logMgr.logEvtInfo4Qml("支付中");
                                                      break
                                                  case EnumTool.PAY_STATUS__UNKNOW:
                                                      chuzhiResult = 3;
                                                      logMgr.logEvtInfo4Qml("不存在");
                                                      shopCartList.resetAllInfo();
                                                      search_bar.tf_search.focus = true
                                                      search_bar.tf_search.forceActiveFocus()
                                                      pay_page.payStatus = true;
                                                      logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                                      break
                                                  case EnumTool.PAY_STATUS__CANCEL:
                                                      chuzhiResult = 3;
                                                      logMgr.logEvtInfo4Qml("订单未支付");
                                                      shopCartList.resetAllInfo();
                                                      search_bar.tf_search.focus = true
                                                      search_bar.tf_search.forceActiveFocus()
                                                      pay_page.payStatus = true;
                                                      logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                                      break
                                                  case EnumTool.PAY_STATUS__ERROR:
                                                      //rect_content.state = "ERROR"
                                                      chuzhiResult = 3;
                                                      logMgr.logEvtInfo4Qml("支付出错");
                                                      chuzhikaFailReason = "支付出错";
                                                      shopCartList.resetAllInfo();
                                                      search_bar.tf_search.focus = true
                                                      search_bar.tf_search.forceActiveFocus()
                                                      pay_page.payStatus = true;
                                                      logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
//                                                      close()
                                                      break
                                                  };
                                              },cur_using_member_unique,8,(Math.abs((Number(totalPayment) - storeRecEditText.text*1 -
                                                                                     storeRecEditText43.text*1))).toFixed(2),storeRecEditText43.text,0)

                                          }else{
                                              reqCombinedUpdate();
                                          }
                                      }
                                  }

                              }

                            }
                            else{

                            logMgr.logEvtInfo4Qml(" Enter stored value zuhezhifu!:{}",(Math.abs((Number(totalPayment) - storeRecEditText.text*1 -
                                                                                                 storeRecEditText43.text*1).toFixed(2))));
                            if((shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7")&&(storePaytext231.text == "0.00" || storePaytext231.text == "" || storePaytext231.text == "0")){

                            }else{
                                search_input.focus = false;
                                accountsReceivable = storePaytext11.text;
                                paymentImport.visible = true;
                                paycodeHideText.focus = true;
                                chuzhiPayRec.visible = false;
                            }
                            }
                        }

                    }
                }
            }
            Timer
            {
                id: clickAmation2;
                running: false;
                repeat: false;
                interval: 100
                onTriggered:
                {
                    if(rect_key2.key_text == qsTr("确认")){
                        rect_key2.color = "#00BD75";
                        text.color = "white";
                    }
                    else{
                        rect_key2.color = "white";
                        text.color = "black";
                    }

                }
            }
        }
}
    }

    Rectangle
    {
        id: paymentImport;
        width: 550*dpi_ratio;
        height: 810*dpi_ratio;
        anchors.centerIn: parent;
        visible: false;
        radius: 10*dpi_ratio;
        enabled: paymentImport.visible;
        color: "#FFFFFF";
        z:4;
        Timer {
            id: timer
            interval: 1000
            repeat: true
            running: paymentImport.visible

            onTriggered: {
                paycodeHideText.focus = true;
            }
        }
        onVisibleChanged: {
            if (paymentImport.visible) {
                timer.start();
            } else {
                timer.stop();
            }
        }
        Rectangle
        {
            id: paymentImportTitleRec;
            width: 300*dpi_ratio;
            height: 49*dpi_ratio;
            anchors.top: parent.top;
            anchors.topMargin: 49*dpi_ratio;
            color: "#FFFFFF";
            anchors.horizontalCenter: parent.horizontalCenter;
            Text
            {
                id: paymentText;
                width: 280  * dpi_ratio;
                height: 38  * dpi_ratio;
                font.family: ST.fontFamilySiYan;
                anchors.horizontalCenter: parent.horizontalCenter;
                anchors.verticalCenter: parent.verticalCenter;
                horizontalAlignment: Text.AlignHCenter;
                verticalAlignment: Text.AlignVCenter;
                font.pointSize: 28  * dpi_ratio;
                color: "black";
                text: qsTr("金圈收款");
            }
        }
        Rectangle
        {
            id:paymentImportImageRec;
            width: 350*dpi_ratio;
            height: 240*dpi_ratio;
            anchors.top: paymentImportTitleRec.bottom;
            anchors.horizontalCenter: parent.horizontalCenter;
            anchors.topMargin: 40*dpi_ratio;
            color: "#FFFFFF";
            AnimatedImage {
                id: paymentImportImage;
                width: 350  * dpi_ratio;
                height: 240  * dpi_ratio;
                anchors.verticalCenter: parent.verticalCenter;
                anchors.horizontalCenter: parent.horizontalCenter;
                source: "/Images/scanimg.gif"
            }
        }
        Rectangle
        {
            id: paymentImportpromRec;
            width: 300*dpi_ratio;
            height: 40*dpi_ratio;
            anchors.top: paymentImportImageRec.bottom;
            anchors.topMargin: 30*dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            visible: true;
            Text
            {
                id: paymentText1;
                width: 280  * dpi_ratio;
                height: 38  * dpi_ratio;
                font.family: ST.fontFamilySiYan;
                anchors.horizontalCenter: parent.horizontalCenter;
                anchors.verticalCenter: parent.verticalCenter;
                horizontalAlignment: Text.AlignHCenter;
                verticalAlignment: Text.AlignVCenter;
                font.pointSize: 18  * dpi_ratio;
                color: "#999999";
                text: qsTr("请扫描顾客付款码完成收款");
            }
        }
        Rectangle
        {
            id: paycodeHide;
            width:300  * dpi_ratio;
            height: 45  * dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            anchors.bottom: paymentImportCloseRec.top;
            border.width: 1  * dpi_ratio;
            border.color: "#FFFFFF";
            color: "#FFFFFF";
            visible: false;
            TextInput
            {
                id: paycodeHideText;
                anchors.centerIn: parent;
                width: 110*dpi_ratio;
                height: 40*dpi_ratio;
                font.family: ST.fontFamilySiYan;
                font.pointSize: 15*dpi_ratio;
                color: "#333333";
                maximumLength: 30
                clip: true;
                enabled:true;
                MouseArea
                {
                }
                onAccepted:
                {
                    if(paycodeHideText.text !== "")
                    {
                        logMgr.logEvtInfo4Qml("储值卡扫码支付");
                        paymentImport.visible = false;
                        chuzhiResult = 1;
                        chuzhikazhiResult.visible = true;
                        if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
                            logMgr.logEvtInfo4Qml("ningyu vip+store || +cash");
                            payMethodControl.payByMemberBalance_v2_ningyu(function (pay_status_enum, pay_method_enum, json_detail) {
                                logMgr.logEvtInfo4Qml("扫码支付解析");
                                switch (pay_status_enum) {
                                case EnumTool.PAY_STATUS__SUCCESS:
                                    logMgr.logEvtInfo4Qml("支付成功");
                                    chuzhiResult = 2;
                                    if (chuzhiResult == 2)
                                    {
                                        shopCartList.resetAllInfo();
                                        search_bar.tf_search.focus = true
                                        search_bar.tf_search.forceActiveFocus()
                                        pay_page.payStatus = true;
                                        logMgr.logEvtInfo4Qml("储值卡收款成功 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                    }
                                    break
                                case EnumTool.PAY_STATUS__PAYING:
                                    //rect_content.state = "PAYING"
                                    chuzhiResult = 1;
                                    logMgr.logEvtInfo4Qml("支付中");
                                    break
                                case EnumTool.PAY_STATUS__UNKNOW:
                                    //rect_content.state = "PAYING"
                                    //var json_doc = JSON.parse(json_detail)
                                    //chuzhikaFailReason = json_doc.
                                    chuzhiResult = 3;
                                    shopCartList.resetAllInfo();
                                    search_bar.tf_search.focus = true
                                    search_bar.tf_search.forceActiveFocus()
                                    pay_page.payStatus = true;
                                    logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                    chuzhikaFailReason = qsTr("订单不存在");
                                    logMgr.logEvtInfo4Qml("不存在");
                                    break
                                case EnumTool.PAY_STATUS__CANCEL:
                                    //rect_content.state = "PAYING"
                                    chuzhiResult = 3;
                                    shopCartList.resetAllInfo();
                                    search_bar.tf_search.focus = true
                                    search_bar.tf_search.forceActiveFocus()
                                    pay_page.payStatus = true;
                                    logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                    chuzhikaFailReason = qsTr("订单未支付");
                                    logMgr.logEvtInfo4Qml("订单未支付");
                                    break
                                case EnumTool.PAY_STATUS__ERROR:
                                    //rect_content.state = "ERROR"
                                    chuzhiResult = 3;
                                    shopCartList.resetAllInfo();
                                    search_bar.tf_search.focus = true
                                    search_bar.tf_search.forceActiveFocus()
                                    pay_page.payStatus = true;
                                    logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                    chuzhikaFailReason = "支付出错";
                                    logMgr.logEvtInfo4Qml("支付出错");
    //                                close()
                                    break
                                };
                            },cur_using_member_unique,8,(Math.abs((Number(totalPayment) - storeRecEditText.text*1 -
                                                                   storeRecEditText43.text*1))).toFixed(2),storeRecEditText43.text,storeRecEditText.text)
                        }else{
                            payMethodControl.combinedOnlineVipPay(function (pay_status_enum, pay_method_enum, json_detail) {
                                logMgr.logEvtInfo4Qml("扫码支付解析");
                                switch (pay_status_enum) {
                                case EnumTool.PAY_STATUS__SUCCESS:
                                    logMgr.logEvtInfo4Qml("支付成功");
                                    chuzhiResult = 2;
                                    if (chuzhiResult == 2)
                                    {
                                        shopCartList.resetAllInfo();
                                        search_bar.tf_search.focus = true
                                        search_bar.tf_search.forceActiveFocus()
                                        pay_page.payStatus = true;
                                        logMgr.logEvtInfo4Qml("储值卡收款成功 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                    }
                                    break
                                case EnumTool.PAY_STATUS__PAYING:
                                    //rect_content.state = "PAYING"
                                    chuzhiResult = 1;
                                    logMgr.logEvtInfo4Qml("支付中");
                                    break
                                case EnumTool.PAY_STATUS__UNKNOW:
                                    //rect_content.state = "PAYING"
                                    //var json_doc = JSON.parse(json_detail)
                                    //chuzhikaFailReason = json_doc.
                                    chuzhiResult = 3;
                                    shopCartList.resetAllInfo();
                                    search_bar.tf_search.focus = true
                                    search_bar.tf_search.forceActiveFocus()
                                    pay_page.payStatus = true;
                                    logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                    chuzhikaFailReason = qsTr("订单不存在");
                                    logMgr.logEvtInfo4Qml("不存在");
                                    break
                                case EnumTool.PAY_STATUS__CANCEL:
                                    //rect_content.state = "PAYING"
                                    chuzhiResult = 3;
                                    shopCartList.resetAllInfo();
                                    search_bar.tf_search.focus = true
                                    search_bar.tf_search.forceActiveFocus()
                                    pay_page.payStatus = true;
                                    logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                    chuzhikaFailReason = qsTr("订单未支付");
                                    logMgr.logEvtInfo4Qml("订单未支付");
                                    break
                                case EnumTool.PAY_STATUS__ERROR:
                                    //rect_content.state = "ERROR"
                                    chuzhiResult = 3;
                                    shopCartList.resetAllInfo();
                                    search_bar.tf_search.focus = true
                                    search_bar.tf_search.forceActiveFocus()
                                    pay_page.payStatus = true;
                                    logMgr.logEvtInfo4Qml(" 设置pay_page.payStatus为:{}",pay_page.payStatus)
                                    chuzhikaFailReason = "支付出错";
                                    logMgr.logEvtInfo4Qml("支付出错");
    //                                close()
                                    break
                                };
                            }, text, storeRecEditText.text, storeRecEditText43.text,totalPayment)
                        }

                    }
                    paycodeHideText.text = "";

                }
              }
        }
        Rectangle
        {
            id:paymentImportCloseRec;
            width: 300*dpi_ratio;
            height: 100*dpi_ratio;
            anchors.bottom: parent.bottom;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            Rectangle
            {
                id: paymentImportCloseRec2;
                width:300  * dpi_ratio;
                height: 100  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                Image
                {
                    id: paymentImportCloseImage;
                    width: 28  * dpi_ratio;
                    height: 28  * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.left: parent.left;
                    anchors.leftMargin: 85  * dpi_ratio;
                    source: "/Images/cancle.png";
                }
                Text
                {
                    id: paymentImportClosetext;
                    width: 10  * dpi_ratio;
                    height: 10  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    anchors.left: paymentImportCloseImage.right;
                    anchors.leftMargin: 7  * dpi_ratio;
                    anchors.bottom: paymentImportCloseImage.bottom;
                    anchors.verticalCenter: parent.verticalCenter;
                    verticalAlignment: Text.AlignVCenter;
                    font.pointSize: 18  * dpi_ratio;
                    color: "#FF3835";
                    text: qsTr("取消收款");
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        close();
                        //clearMemberInfo();
                        //clearChuzhiPage();
                        //enableBtn();
                    }
                }
        }

    }
    }
    Rectangle
    {
        id: chuzhikazhiResult;
        width: 550*dpi_ratio;
        height: 810*dpi_ratio;
        anchors.centerIn: parent;
        visible: false;
        radius: 10*dpi_ratio;
        enabled: chuzhikazhiResult.visible;
        color: "#FFFFFF";
        z:6;
        MouseArea {
            anchors.fill: parent
            onClicked: {
            }
        }
        Timer {
            id: updateTimer
            interval: 50
            repeat: true
            running: chuzhikazhiResult.visible
            onTriggered: {
                chuzhikazhiText.text = chuzhiResult === 1?qsTr("收款中"):chuzhiResult === 2?qsTr("收款成功"):chuzhiResult === 3?qsTr("收款失败"):"";
                chuzhikazhiImageRec. width = chuzhiResult === 1 ? 300*dpi_ratio:100  * dpi_ratio;
                chuzhikazhiImageRec. height = chuzhiResult === 1 ? 300*dpi_ratio:100  * dpi_ratio;
                chuzhikazhiImageRec.color = chuzhiResult === 1?"#FFFFFF":chuzhiResult === 2?"#00BD75":chuzhiResult === 3?"#FF3835":"";
                chuzhikazhiImageRec.radius = chuzhiResult === 1? 0 : width / 2;
                chuzhikazhiImage.width = chuzhiResult === 1?70  * dpi_ratio:50  * dpi_ratio;
                chuzhikazhiImage.height = chuzhiResult === 1?70  * dpi_ratio:50  * dpi_ratio;
                chuzhikazhiImage.source = chuzhiResult === 1?"/Images/jinquansmall.png":chuzhiResult === 2?"/Images/duihao.png":chuzhiResult === 3?"/Images/xhao.png":"";
                chuzhikazhiText1.color = chuzhiResult === 1? "#999999":chuzhiResult === 2?"#00BD75":chuzhiResult === 3?"#FF3835":"";
                chuzhikazhiText1.text = chuzhiResult === 1?qsTr("收款中"):chuzhiResult === 2?qsTr("收款成功"):chuzhiResult === 3?qsTr("收款失败"):"";
                payMoneyText.font.pointSize = chuzhiResult === 3? 18  * dpi_ratio:15  * dpi_ratio;
                payMoneyText.color = chuzhiResult === 1?"#00BD75":chuzhiResult === 2?"#000000":chuzhiResult === 3?"#666666":"";
                payMoneyText.text = chuzhiResult === 1?qsTr("应收金额：")+storePaytext11.text:chuzhiResult === 2?qsTr("实收金额：￥")+totalPayment:chuzhiResult === 3
                            ?"<p style='margin: 2;line-height: 1.5;'><span>失败原因:" + chuzhikaFailReason + "</span></p><p style='margin: 2;line-height: 1.5;'><span>则请确认客户不再进行支付时再关闭此窗口</span></p>":"";
                chuzhikasucceResult.visible =( (chuzhiResult === 2) && ((Math.abs((Number(totalPayment) - storeRecEditText.text*1 -
                                                                                            storeRecEditText43.text*1).toFixed(2))) !== 0));
                //chuSucceResultRecrTex.text = "￥"+(balanceRectext1.text*1 >= Number(totalPayment))?totalPayment:(Number(totalPayment) - storeRecEditText.text*1 -
                 //                                                                       storeRecEditText43.text*1).toFixed(2);
                chuZhiKaZhiCloseImage.width = chuzhiResult === 1?28  * dpi_ratio:1  * dpi_ratio;
                chuZhiKaZhiCloseImage.height = chuzhiResult === 1?28  * dpi_ratio:1  * dpi_ratio;
                chuZhiKaZhiCloseImage.anchors.leftMargin = chuzhiResult === 1? 15  * dpi_ratio:chuzhiResult === 2?5  * dpi_ratio:65  * dpi_ratio;
                chuZhiKaZhiClosetext.anchors.leftMargin = chuzhiResult === 1?7  * dpi_ratio:chuzhiResult === 2?0  * dpi_ratio:chuzhiResult === 3?60  * dpi_ratio:"";
                chuZhiKaZhiClosetext.color = chuzhiResult === 1?"#FF3835":"#FFFFFF";
                chuZhiKaZhiClosetext.text = chuzhiResult === 1?qsTr("取消收款"):chuzhiResult === 2?qsTr("再次打印小票"):chuzhiResult === 3?qsTr("重新收银"):"";
            }
        }
        onVisibleChanged: {
            if (visible) {
                updateTimer.start();
            } else {
                updateTimer.stop();
            }
        }
        Rectangle
        {
            id: chuzhikazhiTitleRec;
            width: 550*dpi_ratio;
            height: 49*dpi_ratio;
            anchors.top: parent.top;
            anchors.topMargin: 30 *dpi_ratio;
            color: "#FFFFFF";
            anchors.horizontalCenter: parent.horizontalCenter;
            Text
            {
                id: chuzhikazhiText;
                width: 280  * dpi_ratio;
                height: 38  * dpi_ratio;
                font.family: ST.fontFamilySiYan;
                anchors.horizontalCenter: parent.horizontalCenter;
                anchors.verticalCenter: parent.verticalCenter;
                horizontalAlignment: Text.AlignHCenter;
                verticalAlignment: Text.AlignVCenter;
                font.pointSize: 28  * dpi_ratio;
                color: "black";
                text:chuzhiResult === 1?qsTr("收款中"):chuzhiResult === 2?qsTr("收款成功"):chuzhiResult === 3?qsTr("收款失败"):"";

            }
            Rectangle
            {
                id: chuzhikazhiQuit;
                width: 60*dpi_ratio;
                height: 60*dpi_ratio;
                anchors.top: parent.top;
                anchors.topMargin: -10 *dpi_ratio;
                anchors.right: parent.right;
                anchors.rightMargin: 10 *dpi_ratio;
                radius: 45
                color: "#333333"
                opacity: 0.8
                visible: chuzhiResult === 2;
                Text
                {
                    id: chuzhikazhiQuitText;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.verticalCenterOffset: -3*dpi_ratio;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    anchors.horizontalCenterOffset: 2*dpi_ratio;
                    text:"+";
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 50*dpi_ratio;
                    rotation: 45
                    color:"#ffffff"
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onPressed:
                    {
                        close();
                    }
                }
            }
        }
        Rectangle
        {
            id:chuzhikazhiImageRec;
            width: chuzhiResult === 1 ? 100*dpi_ratio:100  * dpi_ratio;
            height: chuzhiResult === 1 ? 100*dpi_ratio:100  * dpi_ratio;
            anchors.top: chuzhikazhiTitleRec.bottom;
            anchors.topMargin: 40 *dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            //anchors.topMargin: 10*dpi_ratio;
            color:chuzhiResult === 1?"#FFFFFF":chuzhiResult === 2?"#00BD75":chuzhiResult === 3?"#FF3835":"";
            radius:chuzhiResult === 1? 0 : width / 2;
            Image
            {
                id: chuzhikazhiImage;
                width: chuzhiResult === 1?125  * dpi_ratio:45  * dpi_ratio;
                height: chuzhiResult === 1?125  * dpi_ratio:45  * dpi_ratio;
                anchors.verticalCenter: parent.verticalCenter;
                anchors.horizontalCenter: parent.horizontalCenter;
                visible: chuzhiResult !== 1;
                source:chuzhiResult === 1?"/Images/jinquansmall.png":chuzhiResult === 2?"/Images/duihao.png":chuzhiResult === 3?"/Images/xhao.png":"";
            }

            AnimatedImage {
                width: 120  * dpi_ratio;
                height: 120  * dpi_ratio;
                anchors.verticalCenter: parent.verticalCenter;
                anchors.horizontalCenter: parent.horizontalCenter;
                source: "/Images/cashiering.gif"
                visible: chuzhiResult === 1;
            }
        }
        Rectangle
        {
            id: chuzhikazhiTitleRec1;
            width: 300*dpi_ratio;
            height: 49*dpi_ratio;
            anchors.top: chuzhikazhiImageRec.bottom;
            anchors.topMargin: 30 *dpi_ratio;
            color: "#FFFFFF";
            anchors.horizontalCenter: parent.horizontalCenter;
            Text
            {
                id: chuzhikazhiText1;
                width: 280  * dpi_ratio;
                height: 38  * dpi_ratio;
                font.family: ST.fontFamilySiYan;
                anchors.horizontalCenter: parent.horizontalCenter;
                anchors.verticalCenter: parent.verticalCenter;
                horizontalAlignment: Text.AlignHCenter;
                verticalAlignment: Text.AlignVCenter;
                font.pointSize: 20  * dpi_ratio;
                color:chuzhiResult === 1? "#999999":chuzhiResult === 2?"#00BD75":chuzhiResult === 3?"#FF3835":"";
                text:chuzhiResult === 1?qsTr("收款中"):chuzhiResult === 2?qsTr("收款成功"):chuzhiResult === 3?qsTr("收款失败"):"";
            }
        }
        Rectangle
        {
            id: payMoneyRec;
            width: 300*dpi_ratio;
            height: 49*dpi_ratio;
            anchors.top: chuzhikazhiTitleRec1.bottom;
            anchors.topMargin: 40 *dpi_ratio;
            color: "#FFFFFF";
            anchors.horizontalCenter: parent.horizontalCenter;
            Text
            {
                id: payMoneyText;
                width: 280  * dpi_ratio;
                height: 38  * dpi_ratio;
                font.family: ST.fontFamilySiYan;
                anchors.horizontalCenter: parent.horizontalCenter;
                anchors.verticalCenter: parent.verticalCenter;
                horizontalAlignment: Text.AlignHCenter;
                verticalAlignment: Text.AlignVCenter;
                font.pointSize:chuzhiResult === 3? 18  * dpi_ratio: 24  * dpi_ratio;
                color:chuzhiResult === 1?"#00BD75":chuzhiResult === 2?"#000000":chuzhiResult === 3?"#666666":"";
                text:chuzhiResult === 1?qsTr("应收金额：")+storePaytext11.text:chuzhiResult === 2?qsTr("实收金额：￥")+totalPayment:chuzhiResult === 3
                ?"<p style='margin: 2;line-height: 1.5;'><span>失败原因:" + chuzhikaFailReason + "</span></p><p style='margin: 2;line-height: 1.5;'><span>则请确认客户不再进行支付时再关闭此窗口</span></p>":"";
            }
        }
        Rectangle
        {
            width:400  * dpi_ratio;
            height: 120  * dpi_ratio;
            anchors.left: parent.left;
            anchors.top: payMoneyRec.bottom;
            anchors.topMargin: 50  * dpi_ratio;
        ColumnLayout {
            anchors.fill: parent
            spacing: 0
        Rectangle
        {
            id: chuzhikasucceResult;
            width:550  * dpi_ratio;
            height: 40  * dpi_ratio;
            anchors.left: parent.left;
            //anchors.top: payMoneyRec.bottom;
            border.width: 1  * dpi_ratio;
            border.color: "#D8D8D8";
            color: "#FFFFFF";
            visible: (chuzhiResult === 2) && ((Math.abs((Number(totalPayment) - storeRecEditText.text*1 -
                                                         storeRecEditText43.text*1).toFixed(2))) !== 0);
            Rectangle
            {
                id: chuzhiSucceResultRecl;
                width:275*dpi_ratio;
                height: 40*dpi_ratio;
                anchors.left: parent.left;
                anchors.top: parent.top;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                Image
                {
                    id: chuSucceResultReclImg;
                    width: 18  * dpi_ratio;
                    height: 18  * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    //anchors.leftMargin: 30   * dpi_ratio;
                    source: "/Images/storemoney.png";
                }
                Text
                {
                    id: chuzhiSucceResultReclTex;
                    width: 72  * dpi_ratio;
                    height: 40  * dpi_ratio;
                    //anchors.bottom: chuSucceResultReclImg.bottom;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.left: chuSucceResultReclImg.right;
                    anchors.leftMargin: 5  * dpi_ratio;
                    //horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 16  * dpi_ratio;
                    color: "#000000";
                    text: qsTr("储值卡");
                }
            }
            Rectangle
            {
                id: chuSucceResultRecr;
                width:275  * dpi_ratio;
                height: 40  * dpi_ratio;
                anchors.left: chuzhiSucceResultRecl.right;
                anchors.top: parent.top;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                Text
                {
                    id: chuSucceResultRecrTex;
                    width: 72  * dpi_ratio;
                    height: 40  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 60  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 16  * dpi_ratio;
                    //anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
    //                horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#000000";
                    text:(Math.abs((Number(totalPayment) - storeRecEditText.text*1 -
                                                                            storeRecEditText43.text*1).toFixed(2))) === "0.00" ?qsTr("￥")+"0.00":qsTr("￥")+(Math.abs((Number(totalPayment) - storeRecEditText.text*1 -
                                                                                                                                                                           storeRecEditText43.text*1))).toFixed(2);
                }

            }
        }
        Rectangle
        {
            id: jinquansucceResult;
            width:550  * dpi_ratio;
            height: 40  * dpi_ratio;
            anchors.left: parent.left;
            //anchors.top: chuzhikasucceResult.bottom;
            border.width: 1  * dpi_ratio;
            border.color: "#D8D8D8";
            color: "#FFFFFF";
            visible: chuzhiResult === 2 && storeRecEditText.text !== "" &&storeRecEditText.text !== "0.00" && storeRecEditText.text !== "0";
            Rectangle
            {
                id: succeResultRecl;
                width:275*dpi_ratio;
                height: 40*dpi_ratio;
                anchors.left: parent.left;
                anchors.top: parent.top;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                Image
                {
                    id: succeResultReclImg;
                    width: 18  * dpi_ratio;
                    height: 18  * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
    //                anchors.left: parent.left;
    //                anchors.leftMargin: 30   * dpi_ratio;
                    source: "/Images/jinquanpay.png";
                }
                Text
                {
                    id: succeResultReclTex;
                    width: 72  * dpi_ratio;
                    height: 40  * dpi_ratio;
                    //anchors.bottom: succeResultReclImg.bottom;
                    anchors.left: succeResultReclImg.right;
                    anchors.leftMargin: 5  * dpi_ratio;
                    //horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 16  * dpi_ratio;
                    color: "#000000";
                    text: qsTr("金圈收款");
                }
            }
            Rectangle
            {
                id: succeResultRecr;
                width:275  * dpi_ratio;
                height: 40  * dpi_ratio;
                anchors.left: succeResultRecl.right;
                anchors.top: parent.top;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                Text
                {
                    id: succeResultRecrTex;
                    width: 72  * dpi_ratio;
                    height: 40  * dpi_ratio;
                    anchors.topMargin: 2  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 16  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 60  * dpi_ratio;
                    //anchors.horizontalCenter: parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    //horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#000000";
                    text:storeRecEditText.text === ""?qsTr("￥")+"0.00":qsTr("￥")+(Number(storeRecEditText.text)).toFixed(2);
                }

            }
        }
        Rectangle
        {
            id: xianjinsucceResult;
            width:550  * dpi_ratio;
            height: 40  * dpi_ratio;
            anchors.left: parent.left;
            //anchors.top: jinquansucceResult.bottom;
            border.width: 1  * dpi_ratio;
            border.color: "#D8D8D8";
            color: "#FFFFFF";
            visible: (chuzhiResult === 2) && (storeRecEditText43.text !== "") && (storeRecEditText43.text !== "0.00");
            Rectangle
            {
                id: succeResultReclXianjin;
                width:275*dpi_ratio;
                height: 40*dpi_ratio;
                anchors.left: parent.left;
                anchors.top: parent.top;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                Image
                {
                    id: succeResultReclXianjinImg;
                    width: 18  * dpi_ratio;
                    height: 18  * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
    //                anchors.left: parent.left;
    //                anchors.leftMargin: 30   * dpi_ratio;
                    source: "/Images/cash.png";
                }
                Text
                {
                    id: succeResultReclXianjinTex;
                    width: 72  * dpi_ratio;
                    height: 40  * dpi_ratio;
                    //anchors.bottom: succeResultReclImg.bottom;
                    anchors.left: succeResultReclXianjinImg.right;
                    anchors.leftMargin: 5  * dpi_ratio;
                    //horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 16  * dpi_ratio;
                    color: "#000000";
                    text: qsTr("现金");
                }
            }
            Rectangle
            {
                id: succeXianjinResultRec;
                width:275  * dpi_ratio;
                height: 40  * dpi_ratio;
                anchors.left: succeResultReclXianjin.right;
                anchors.top: parent.top;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                color: "#FFFFFF";
                Text
                {
                    id: succeXianJinResultRecrTex;
                    width: 72  * dpi_ratio;
                    height: 40  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 60  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 16  * dpi_ratio;
                    //anchors.horizontalCenter: parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    //horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#000000";
                    text:storeRecEditText43.text === ""?qsTr("￥")+"0.00":qsTr("￥")+(Number(storeRecEditText43.text)).toFixed(2);
                }

            }
        }
        }
        }
        Rectangle
        {
            id:chuZhiKaZhiCloseRec;
            width: 300*dpi_ratio;
            height: 100*dpi_ratio;
            anchors.bottom: parent.bottom;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            Rectangle
            {
                id:chuZhiKaZhiCloseRec2;
                width:200  * dpi_ratio;
                height: 50  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#FFFFFF";
                anchors.top: parent.top;
                anchors.horizontalCenter: parent.horizontalCenter;
                radius: 10  * dpi_ratio;
                color: chuzhiResult === 1?"#FFFFFF":"#00BD75";
                Image
                {
                    id: chuZhiKaZhiCloseImage;
                    width:chuzhiResult === 1?28  * dpi_ratio:1  * dpi_ratio;
                    height:chuzhiResult === 1?28  * dpi_ratio:1  * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.left: parent.left;
                    anchors.leftMargin:chuzhiResult === 1? 15 * dpi_ratio:chuzhiResult === 2?0  * dpi_ratio:0  * dpi_ratio;
                    //anchors.horizontalCenter: parent.horizontalCenter;
                    source: "/Images/cancle.png";
                }
                Text
                {
                    id: chuZhiKaZhiClosetext;
                    width: 10  * dpi_ratio;
                    height: 10  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
    //                anchors.left: chuZhiKaZhiCloseImage.right;
    //                anchors.leftMargin: chuzhiResult === 1?7  * dpi_ratio:60  * dpi_ratio;
    //                anchors.bottom: chuZhiKaZhiCloseImage.bottom;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    font.pointSize: 18  * dpi_ratio;
                    color: chuzhiResult === 1?"#FF3835":"#FFFFFF";
                    text:chuzhiResult === 1?qsTr("取消收款"):chuzhiResult === 2?qsTr("再次打印小票"):chuzhiResult === 3?qsTr("重新收银"):"";
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        if(chuzhiResult === 1){
                            close();
                            return;
                        }
                        if(chuzhiResult === 2){
                            printerControl.reprintLastOrder();
                            close();
                            return;
                        }
                        if(chuzhiResult === 3){
                            search_input.forceActiveFocus()
                            chuzhikazhiResult.visible = false
                            chuzhiPayRec.visible = true;
                            paymentImport.visible = false;
                            chuzhiResult = 0;
                            chuzhikaFailReason = "";
                            return;
                        }
                    }
                }
        }

        }

    }

   }
