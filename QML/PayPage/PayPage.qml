﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "ShoppngCart"
import "GoodsList"
import "SearchBar"
import "SearchBar/Popup"
import "WeightDisplay"
import "GoodsTabBar"
import "PayMethods"
import ".."
import "GoodsQuickEdit"
import "GoodsEdit"
import "MemberInfo"
import EnumTool 1.0
import "Popup"
import "../GodownPage/LeftSide/ContainGoodsDetail"
import QtGraphicalEffects 1.15

CusRect {
    id: pay_page_root
    width: 800
    height: 500
    color: ST.color_black_p1

    property int width_btn: 340 * dpi_ratio
    property int height_btn: 70 * dpi_ratio
    property string serachData: ""
    property real margin: 10 * dpi_ratio

    // 定义源属性
    property bool payStatus : true


    property bool is_mamager_model: false //?商品管理模式

    property alias pending_order_list: pending_order_list
    property alias pay_methods: pay_methods
    property alias shoppng_cart: shoppng_cart
    property alias goods_list_4_all: goods_list_4_all
    property alias search_bar: search_bar
    property alias search_bar_4_member: search_bar_4_member
    property alias goods_tabbar: goods_tabbar
    property alias goods_quick_edit: goods_quick_edit
    property alias goods_edit: goods_edit
    property alias member_ship_list: member_ship_list
    property bool is_member_ship_model: false

    signal sendOpenGoodsInfo(var goodsBar)


    Timer {
        id: bundle_timer
        interval: 80
        repeat: false
        onTriggered: {
            logMgr.logDataInfo4Qml("主页打开捆绑信息弹窗")
            showBundleSearch()
        }
    }
    onIs_member_ship_modelChanged: {
        if (is_member_ship_model) {
//            goods_tabbar.showMemberShip()  主页取消显示会员信息
//            search_bar_4_member.forceActiveFocus()
        } else {
            goods_tabbar.eraserCusKind()
            member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__NULL
            search_bar.forceActiveFocus()
        }
    }
    onVisibleChanged: {
        if (visible) {
            search_bar.tf_search.focus = true
            search_bar.tf_search.forceActiveFocus()
            requestActivate()
        }
    }

    property int member_ship_model_status: EnumTool.PAY_PAGE__MEMBER__NULL
//    onMember_ship_model_statusChanged: {
//        search_bar_4_member.forceActiveFocus()
//    }

    property bool is_need_show_member_order: false
    property bool is_show_member_order: cur_editing_member_unique != null && (member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER) && is_need_show_member_order

    //是否是虚拟分类
    property var cur_using_member_unique: ""
    property var cur_using_member_name: ""
    property var cur_using_member_balance: ""
    property var cur_using_member_points: ""
    property var cur_using_member_phone: ""

    Connections {
        target: utils4Qml
        function onReturnToPayPage() {
            //logMgr.logEvtInfo4Qml("显示Paypage页面{}", "")
            is_mamager_model = false
        }
    }

    function setMemberInterface(cusUnique, cusName, cus_points, cusBalance) {
        cur_using_member_unique = cusUnique
        cur_using_member_name = cusName
        cur_using_member_points = cusBalance
        cur_using_member_balance = cus_points
    }
    function setMemberPhoneInterface(cusUnique, cusName, cus_points, cusBalance,cusPhone) {
        cur_using_member_unique = cusUnique
        cur_using_member_name = cusName
        cur_using_member_points = cus_points
        cur_using_member_balance = cusBalance
        logMgr.logEvtInfo4Qml("cur_using_member_phone:{}",cusPhone);
        cur_using_member_phone = cusPhone
        logMgr.logEvtInfo4Qml("cur_using_member_phone:{}",cur_using_member_phone);

    }
    function refreshInterfaceMemberInfo() {

        if (cur_using_member_unique == "")
            return
        memberControl.reqMemberInfoDetail4Qml(function (is_succ, data) {
            if (is_succ) {
                var json_doc = JSON.parse(data)
                var json_doc_data = json_doc.data
                var json_doc_data1 = json_doc.data1

                var data_hui
                var data_chu

                if (json_doc_data == null) {
                    data_hui = json_doc_data1
                    data_chu = json_doc_data1
                } else if (json_doc_data1 == null) {
                    data_hui = json_doc_data
                    data_chu = json_doc_data
                } else {
                    if (json_doc_data.cusType == "会") {
                        data_hui = json_doc_data
                        data_chu = json_doc_data1
                    } else {
                        data_hui = json_doc_data1
                        data_chu = json_doc_data
                    }
                }
                cur_using_member_balance = data_chu.cusBalance*1 +data_chu.cusRebate*1
                cur_using_member_name = data_hui.cusName
                cur_using_member_points = data_hui.cusPoints
            } else {
                toast.openWarn(qsTr("刷新会员信息失败"))
            }
        }, cur_using_member_unique)
    }

    function clearCurMemberInfo() {
        cur_using_member_unique = ""
        cur_using_member_name = ""
        cur_using_member_balance = ""
        cur_using_member_points = ""
    }
    Connections {
        target: shopCartList
        function onSigClearMemberUnique() {
            clearCurMemberInfo()
        }
    }

    property var cur_editing_member_unique: null

    onCur_editing_member_uniqueChanged: {
        member_info.member_info_detail_edit.reqMemberInfoDetail(cur_editing_member_unique)
        is_need_show_member_order = false
    }

    function showAddGoods2CurGoodsKindPop() {
        window_root.loader_4_add_goods_2_cur_goods_kind.sourceComponent = compo_ddgoods_2_cur_goods_kind
        window_root.loader_4_add_goods_2_cur_goods_kind.item.open()
    }

    function addGoods2CurCategory(goods_barcode) {
        goodsControl.reqAddGoodsByVirtualGoodsKind(function (is_succ, data) {

            if (is_succ) {
                goodsControl.reqGetVirualGoodsKindAndSave4Qml(function (is_succ, data) {
                    if (is_succ) {
                        logMgr.logDataInfo4Qml("分类下添加商品成功！")
                        right_side.refreshCurGoodsByVirtualGoodsKind()
                    } else {
                    }
                })
            } else {
                 toast.openWarn("新增的商品信息不存在")
            }
        }, right_side.cur_virtual_goods_kind_id, goods_barcode)
    }

    function showAddGoodsHomeCategoryPop() {
        window_root.loader_4_add_home_goods_kind.sourceComponent = compo_add_home_goods_kind
        window_root.loader_4_add_home_goods_kind.item.open()
    }
    function showBundleSearch() {
        window_root.loader_center.sourceComponent = search_bundle_popup
        window_root.loader_center.item.open(serachData)

    }
    Component {
        id: search_bundle_popup
        SearchBundleInformation { //isBundle,shopCartlists
            onSigClose: {
                logMgr.logDataInfo4Qml("isBundle:{}",isBundle)
                    if(isBundle === "0"){
                        logMgr.logDataInfo4Qml("shopCartlists0:{}",shopCartlists)
                        if(shopCartlists === "0"){
                            search_bar.forceActiveFocus()
                            search_bar.tf_search.text = ""
                        }else{

                            var json_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(serachData))
                            if (json_goods == null) {
                            }
                            if (pay_page_root.is_mamager_model) {
                                goods_quick_edit.addItemByBarcode(serachData)
                            } else {
                                shopCartList.appendItemByBarcode(serachData)
                            }
                        }
                    }
                    else if ((isBundle === "1")){
                       logMgr.logDataInfo4Qml("shopCartlists1:{}",shopCartlists)

                       //拆分商品 取出条形码，修改自定义价格 放入购物车结束
                        if(shopCartlists.length !== 0){
                            var shopArray = shopCartlists.split(";")
                            logMgr.logDataInfo4Qml("shopArray{};length:{}",shopArray,shopArray.length)
                            for (var m = 0; m < shopArray.length - 1; m++)
                            {
                                var shopData = shopArray[m].split("^");

                                for(var i = 0 ;i < shopData.length; i++){
                                    logMgr.logDataInfo4Qml("shopData[{}]:{}",i,shopData[i])
                                }
                                shopCartList.appendIndepItemByBarcode(shopData[6],true,false,shopData[8])
                                shopCartList.setItemNumNoTimeByBarcode(shopData[6], shopData[4],shopData[8])
                                shopCartList.setItemsubtotalNotimeByBarcode(shopData[6], shopData[2]*shopData[4],shopData[8])
                                //setSubtotal(Number(newAllPrice))
                                //logMgr.logDataInfo4Qml("text*1 >= discountData[3]*1:{}",text*1 >= discountData[3]*1)
//                                if(shopData.length > 8){
                        }
                        }


                    }
                    serachData = ""
                    search_bar.forceActiveFocus()
                    search_bar.tf_search.text = ""
            }

        }
    }
    function showMemberSearch() {
        window_root.loader_center.sourceComponent = search_member_popup
        window_root.loader_center.item.open()
    }
    Component {
        id: search_member_popup
        SearchMemberPopup {
            onSigClose: {
                is_member_ship_model = is_member_ship;
            }
        }
    }
    Component {
        id: compo_ddgoods_2_cur_goods_kind
        PopupAddGoods2CurVGoodsKind {
            is_need_focus: true

            title_name: qsTr("请输入商品条码")
            onConfirm: {
                addGoods2CurCategory(str_num)
                keyboard_c.closeAll()
            }
            onSigClose: {
                search_bar.forceActiveFocus()
            }
        }
    }

    Component {
        id: compo_add_home_goods_kind

        PopupInputConfirm {
            id: points_chage_confirm
            title_name: qsTr("新建分类")
            message_info: qsTr("请输入分类名称")
            onConfirm: {
                goodsControl.reqAddVirualGoodsKind4Qml(function (is_succ, data) {
                    var json_doc = JSON.parse(data)
                    if (is_succ) {
                        toast.openInfo(qsTr("新建分类成功"))

                        goodsControl.reqGetVirualGoodsKindAndSave4Qml(function (is_succ, data) {
                            if (is_succ) {
                                goods_tabbar.refreshGoodsKind()
                            }
                        })
                    } else {
                        toast.openWarn(qsTr("新建分类失败"))
                    }
                }, change_str)
            }
            onSigClose: {
                search_bar.forceActiveFocus()
            }
        }
    }

    Image {
        anchors.fill: parent
        source: "/Images/mainPageBackground.jpg"
    }

    FocusScope {
        id: left_side
        width: 720 * dpi_ratio
        height: pay_page_root.height
        z: 1

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: pay_page_root.margin
            anchors.rightMargin: pay_page_root.margin / 2
            spacing: pay_page_root.margin

            SearchBar {
                id: search_bar
                Layout.preferredHeight: pay_page_root.height_btn
                Layout.fillWidth: true
                radius: ST.radius
                memberName:cur_using_member_name
                //visible: !is_member_ship_model
                visible:true //一直保留该弹窗
                onSignalShowBundles: {
                    serachData = serachText
                    bundle_timer.start()
                }
            }

            SearchBar4Member {
                id: search_bar_4_member
                Layout.preferredHeight: pay_page_root.height_btn
                Layout.fillWidth: true
                radius: ST.radius
                visible:false
                //visible: is_member_ship_model
            }


            //快速编辑
            GoodsQuickEdit {
                id: goods_quick_edit
                //visible: is_mamager_model
                visible:false
                Layout.fillWidth: true
                Layout.fillHeight: true
            }
            //商品编辑
            GoodsEdit {
                id: goods_edit
                visible: is_mamager_model
                //visible: false
                Layout.fillWidth: true
                Layout.fillHeight: true

                onSignOpenGoodsManger: {
                    if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L1_GODOWN)) {
                        if (window_root.compo_index !== EnumTool.PAGE_GODOWN_OLD){
                            window_root.compo_index = EnumTool.PAGE_GODOWN_OLD
                            sendOpenGoodsInfo(goodsBarcodes)
                        }
                    }
                }

            }

            //会员信息
            MemberInfo {
                id: member_info
                visible: is_member_ship_model && member_ship_model_status != EnumTool.PAY_PAGE__MEMBER__NULL
                Layout.fillWidth: true
                Layout.fillHeight: true
            }

            // 购物车
            CusRect {
                visible: !is_mamager_model && (!is_member_ship_model || (is_member_ship_model && member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__NULL))
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent
                    spacing: pay_page_root.margin

                    RowLayout {
                        Layout.fillWidth: true
                        Layout.fillHeight: false
                        Layout.preferredHeight: pay_page_root.height_btn
                        spacing: pay_page_root.margin
                        //会员
                        CheckBoxC2 {
                            id: btn_membership
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 200
                            check_text.text: cur_using_member_unique == "" ? qsTr("会员") : ""
                            check_text.color: ST.color_black
                            color: ST.color_white
                            checked: is_member_ship_model
                            onClicked: {
                                if(payMethodControl.getNetStatus() === "101")
                                {
                                    if (is_member_ship_model) {
                                        is_member_ship_model = false
                                        cur_using_member_unique = ""
                                        shopCartList.clearMemberUnique()
                                    } else {
                                        if (cur_using_member_unique != "") {

                                            cur_using_member_unique = ""
                                            shopCartList.clearMemberUnique()
                                        } else {
                                            showMemberSearch(); //会员搜索弹窗
                                        }
                                    }
                                }
                                else{
                                    toast.openWarn("离线状态下该功能不可用！");
                                }
                            }

                            RowLayout {
                                anchors.fill: parent
                                visible: cur_using_member_unique != ""
                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: height
                                            color: ST.color_transparent
                                            Image {
                                                anchors.centerIn: parent
                                                width: parent.width - 5
                                                height: width
                                                fillMode: Image.PreserveAspectFit
                                                source: "/Images/huiyuantouxiang.png"
                                            }
                                        }
                                        CusRect {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            Layout.topMargin: 10 * dpi_ratio
                                            Layout.bottomMargin: 10 * dpi_ratio
                                            color: ST.color_transparent

                                            ColumnLayout {
                                                anchors.fill: parent
                                                CusRect {
                                                    Layout.fillWidth: true
                                                    Layout.fillHeight: true
                                                    color: ST.color_transparent
                                                    CusText {
                                                        width: parent.width
                                                        text: cur_using_member_name
                                                        font.pixelSize: 16 * dpi_ratio
                                                        elide: Text.ElideRight
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        horizontalAlignment: Text.AlignHCenter
                                                    }
                                                }
                                                CusRect {
                                                    Layout.fillWidth: true
                                                    Layout.fillHeight: true
                                                    color: ST.color_transparent
                                                    CusText {
                                                        width: parent.width
                                                        text: cur_using_member_unique
                                                        font.pixelSize: 14 * dpi_ratio
                                                        elide: Text.ElideRight
                                                        verticalAlignment: Text.AlignVCenter
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        horizontalAlignment: Text.AlignHCenter
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                CusRect {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent

                                        CusRect {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            color: ST.color_transparent
                                            Layout.topMargin: 10 * dpi_ratio
                                            Layout.bottomMargin: 10 * dpi_ratio

                                            ColumnLayout {
                                                anchors.fill: parent

                                                CusRect {
                                                    Layout.fillWidth: true
                                                    Layout.fillHeight: true
                                                    color: ST.color_transparent
                                                    CusText {
                                                        width: parent.width
                                                        text: cur_using_member_balance
                                                        font.pixelSize: 16 * dpi_ratio
                                                        elide: Text.ElideRight
                                                        verticalAlignment: Text.AlignVCenter
                                                        horizontalAlignment: Text.AlignHCenter
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        color: ST.color_orange
                                                    }
                                                }
                                                CusRect {
                                                    Layout.fillWidth: true
                                                    Layout.fillHeight: true
                                                    color: ST.color_transparent
                                                    CusText {
                                                        width: parent.width
                                                        text: qsTr("余额")
                                                        font.pixelSize: 17 * dpi_ratio
                                                        elide: Text.ElideRight
                                                        verticalAlignment: Text.AlignVCenter
                                                        horizontalAlignment: Text.AlignHCenter
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        color: ST.color_grey_font
                                                    }
                                                }
                                            }
                                        }
                                        CusRect {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            color: ST.color_transparent
                                            Layout.topMargin: 10 * dpi_ratio
                                            Layout.bottomMargin: 10 * dpi_ratio

                                            ColumnLayout {
                                                anchors.fill: parent

                                                CusRect {
                                                    Layout.fillWidth: true
                                                    Layout.fillHeight: true
                                                    color: ST.color_transparent
                                                    CusText {
                                                        width: parent.width
                                                        text: cur_using_member_points
                                                        font.pixelSize: 16 * dpi_ratio
                                                        elide: Text.ElideRight
                                                        verticalAlignment: Text.AlignVCenter
                                                        horizontalAlignment: Text.AlignHCenter
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        color: ST.color_orange
                                                    }
                                                }

                                                CusRect {
                                                    Layout.fillWidth: true
                                                    Layout.fillHeight: true
                                                    color: ST.color_transparent

                                                    CusText {
                                                        width: parent.width
                                                        text: qsTr("积分")
                                                        font.pixelSize: 17 * dpi_ratio
                                                        elide: Text.ElideRight
                                                        verticalAlignment: Text.AlignVCenter
                                                        horizontalAlignment: Text.AlignHCenter
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        color: ST.color_grey_font
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        CusSpacer {}
                        //重量显示
                        CusButton {
                            id: weight_display
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 200
                            property string cur_weight: "0"
                            text: qsTr("称重") + Number(cur_weight).toFixed(3)
                            font_color: ST.color_black
                            color: ST.color_white

                            Connections {
                                target: weightingScaleControl
                                function onSendWeight(weight_str) {
                                    weight_display.cur_weight = weight_str
                                }
                            }
                        }
                    }
                    ShoppngCart {
                        id: shoppng_cart
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                    }
                }
            }
        }
    }

    CusRect {
        id: right_side
        clip: true
        height: pay_page_root.height
        width: pay_page_root.width - left_side.width
        anchors.left: left_side.right
        color: ST.color_transparent

        ///-------------------------------------------| 根据当前分类刷新商品 |-------------------------------------------
        property var cur_virtual_goods_kind_unique: null
        property var cur_virtual_goods_kind_id: null
        onCur_virtual_goods_kind_uniqueChanged: {
            logMgr.logEvtInfo4Qml("触发分类商品查询")
            refreshCurGoodsByVirtualGoodsKind()
        }

        function refreshCurGoodsKindByCurUnique() {
            is_member_ship_model = false
            goods_tabbar.eraserCusKind()
            goods_list.refreshGoodsByVirtualGoodsKind(cur_virtual_goods_kind_unique)
            shoppng_cart.backToMainOrder()
        }

        function refreshCurGoodsByVirtualGoodsKind() {
            logMgr.logEvtInfo4Qml("根据虚拟分类查询商品:{}",cur_virtual_goods_kind_unique)
            switch (cur_virtual_goods_kind_unique) {
            case EnumTool.CUS_GOODS_KIND_PENDING_ORDER:
            case EnumTool.CUS_GOODS_KIND_MEMBER_SHIP:
            case EnumTool.CUS_GOODS_KIND_RECOGNITION:
                break
            case EnumTool.CUS_GOODS_KIND_ALL_GOODS:
                goods_tabbar.setVGoodsKindUnique(0)
                shoppng_cart.backToMainOrder()
                break
            default:
                logMgr.logEvtInfo4Qml("根据虚拟分类查询商品-进入默认")
                goods_list_4_all.model_goods_data.isRecognition = false
                goods_tabbar.setVGoodsKindUnique(cur_virtual_goods_kind_unique)
                shoppng_cart.backToMainOrder()
            }
        }

        function refreshCurGoodsByVirtualGoodsKindUnique(v_goods_kind_unique) {
            is_member_ship_model = false
            goods_tabbar.eraserCusKind()
            goods_list.refreshGoodsByVirtualGoodsKind(v_goods_kind_unique)
            shoppng_cart.backToMainOrder()
        }

        ///-------------------------------------------| 根据当前分类刷新商品 |-------------------------------------------
        property bool is_cus_goods_list: cur_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_PENDING_ORDER //\
                                         || cur_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_MEMBER_SHIP //\

        // || cur_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_ALL_GOODS //\
        CusRect {
            anchors.fill: parent
            anchors.margins: pay_page_root.margin
            anchors.leftMargin: pay_page_root.margin / 2
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 0 // pay_page_root.margin

                GoodsTabBar {
                    id: goods_tabbar
                    Layout.fillWidth: true
                    // 少于9个分类减少高度 (一行7个分类)
                    Layout.preferredHeight: lm_virtual_goods_tab_bar.count > 7 ? pay_page_root.height_btn * 2 + pay_page_root.margin : pay_page_root.height_btn
                }

                CusSpacer {
                    Layout.preferredHeight: 10 * dpi_ratio
                }

                GoodsList4AllGoods {
                    id: goods_list_4_all
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: !right_side.is_cus_goods_list || right_side.cur_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_ALL_GOODS
                }

                PendingOrderList {
                    id: pending_order_list
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: right_side.cur_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_PENDING_ORDER
                }

                MemberShipList {
                    id: member_ship_list
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    //visible: is_member_ship_model && (right_side.cur_virtual_goods_kind_unique == EnumTool.CUS_GOODS_KIND_MEMBER_SHIP)
                    visible:false;
                }

                CusRect {
                    id: rect_right_bottom_status

                    Layout.fillWidth: true
                    Layout.preferredHeight: if (!configTool.isUseStaticKeyboard) {
                                                return 108 * dpi_ratio
                                            } else {
                                                return 425 * dpi_ratio
                                            }

                    color: ST.color_transparent
                    visible: !right_side.is_cus_goods_list

                    // 0:无键盘 1:有键盘
                    property int right_bottom_status: configTool.isUseStaticKeyboard ? 1 : 0

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 0

                        component KeyBtn1: CusRect {
                            id: key_type_root
                            radius: ST.radius
                            border.color: "#D8D8D8"
                            border.width: 1 * dpi_ratio
                            color: is_down ? "#17262B" : ST.color_white_pure

                            signal clicked

                            property string key1
                            property string key2
                            property bool is_down: is_check_btn ? is_check : is_pressing
                            property bool is_pressing: false
                            property bool is_cus_event: false
                            property bool is_single_key: key2 == "" //是否是单键

                            property bool is_check_btn: false
                            property bool is_check: false

                            function getKeyActive() {
                                if (is_single_key) {
                                    return key1.toUpperCase()
                                } else {
                                    if (!keyEvent.getIsShiftDown()) {
                                        return key1
                                    } else {
                                        return key2
                                    }
                                }
                            }

                            CusText {
                                anchors.centerIn: parent
                                text: keyEvent.isCapsLock ? key_type_root.key1.toUpperCase() : key_type_root.key1.toLowerCase()
                                font.family: "DIN"
                                font.pixelSize: 52 * dpi_ratio
                                color: key_type_root.is_down ? ST.color_white_pure : ST.color_font
                            }

                            Timer {
                                id: timer
                                interval: 80
                                repeat: false
                                onTriggered: {
                                    key_type_root.is_pressing = false
                                }
                            }

                            MouseArea {
                                anchors.fill: parent
                                onPressed: {
                                    if (!key_type_root.is_check_btn)
                                        is_pressing = true
                                }
                                onReleased: {
                                    if (!key_type_root.is_check_btn)
                                        if (!timer.running) {
                                            timer.start()
                                        }
                                }
                                onCanceled: {
                                    if (!key_type_root.is_check_btn)
                                        if (!timer.running) {
                                            timer.start()
                                        }
                                }
                                onClicked: {
                                    soundCtrl.playClick()

                                    if (key_type_root.is_cus_event) {
                                        key_type_root.clicked()
                                        return
                                    }

                                    if (!key_type_root.is_check_btn) {
                                        keyEvent.sendEvent(key_type_root.getKeyActive())
                                    } else {
                                        key_type_root.is_check = !key_type_root.is_check
                                    }
                                }
                            }
                        }

                        //键盘
                        CusRect {
                            id: keyboard_quick
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            radius: ST.radius

                            CusRect {
                                anchors.bottom: keyboard_quick.top
                                height: 39 * dpi_ratio
                                width: 388 * dpi_ratio
                                color: ST.color_transparent
                                CusRect {
                                    width: parent.width
                                    height: 53 * dpi_ratio
                                    color: ST.color_transparent
                                    CusImg {
                                        anchors.fill: parent
                                        source: "/Images/PayBtn/shadow.png"
                                    }

                                    RowLayout {
                                        anchors.fill: parent
                                        anchors.leftMargin: 10 * dpi_ratio

                                        CusRect {
                                            Layout.preferredWidth: 30 * dpi_ratio
                                            Layout.preferredHeight: 20 * dpi_ratio
                                            Layout.alignment: Qt.AlignVCenter
                                            Layout.bottomMargin: 17 * dpi_ratio

                                            color: ST.color_transparent

                                            CusImg {
                                                anchors.fill: parent
                                                source: configTool.isUseStaticKeyboard ? "/Images/PayBtn/keyboard_d.png" : "/Images/PayBtn/keyboard.png"
                                            }

                                            MouseArea {
                                                anchors.fill: parent
                                                anchors.margins: -20 * dpi_ratio
                                                onClicked: {
                                                    configTool.isUseStaticKeyboard = !configTool.isUseStaticKeyboard
                                                }
                                            }
                                        }

                                        CusRect {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            Layout.bottomMargin: 17 * dpi_ratio

                                            color: ST.color_transparent
                                            CusText {
                                                x: 10 * dpi_ratio
                                                text: qsTr("在收银页面点击商品管理可快速新增商品")
                                                font.pixelSize: 16 * dpi_ratio
                                                color: ST.color_white_pure
                                                anchors.verticalCenter: parent.verticalCenter
                                            }
                                        }
                                    }
                                }
                            }

                            CusRect {
                                anchors.fill: parent
                                color: ST.color_white_pure
                                visible: configTool.isUseStaticKeyboard
                                radius: ST.radius

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 0
                                    anchors.margins: 10 * dpi_ratio

                                    //字母
                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        ColumnLayout {
                                            anchors.fill: parent
                                            spacing: 2 * dpi_ratio

                                            CusRect {
                                                Layout.fillWidth: true
                                                Layout.fillHeight: true
                                                color: ST.color_transparent

                                                RowLayout {
                                                    anchors.fill: parent
                                                    spacing: 2 * dpi_ratio

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "Q"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "W"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "E"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "R"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "T"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "Y"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "U"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "I"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "O"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "P"
                                                    }
                                                }
                                            }
                                            CusRect {
                                                Layout.fillWidth: true
                                                Layout.fillHeight: true
                                                color: ST.color_transparent

                                                RowLayout {
                                                    anchors.fill: parent
                                                    spacing: 2 * dpi_ratio

                                                    CusSpacer {
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 39
                                                    }

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "A"
                                                    }

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "S"
                                                    }

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "D"
                                                    }

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "F"
                                                    }

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "G"
                                                    }

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "H"
                                                    }

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "J"
                                                    }

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "K"
                                                    }

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "L"
                                                    }

                                                    CusSpacer {
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 42
                                                    }
                                                }
                                            }

                                            CusRect {
                                                Layout.fillWidth: true
                                                Layout.fillHeight: true
                                                color: ST.color_transparent

                                                RowLayout {
                                                    anchors.fill: parent
                                                    spacing: 2 * dpi_ratio
                                                    KeyBtn1 {
                                                        id: key_caps_lock
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78

                                                        is_cus_event: true
                                                        is_check_btn: true

                                                        is_check: keyEvent.isCapsLock

                                                        CusText {
                                                            text: parent.is_down ? qsTr("小写") : qsTr("大写")
                                                            anchors.centerIn: parent
                                                            color: parent.is_down ? ST.color_white_pure : "#17262B"
                                                        }

                                                        onClicked: {
                                                            keyEvent.sendEvent("CAPS_LOCK")
                                                        }
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "Z"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "X"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "C"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "V"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "B"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "N"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        key1: "M"
                                                    }

                                                    KeyBtn1 {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78

                                                        is_cus_event: true

                                                        CusText {
                                                            text: qsTr("中/英")
                                                            anchors.centerIn: parent
                                                            color: parent.is_down ? ST.color_white_pure : "#17262B"
                                                        }

                                                        onClicked: {
                                                            keyEvent.sendEvent("SWITCH")
                                                        }
                                                    }
                                                    KeyBtn1 {
                                                        id: key_backspace
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 78
                                                        is_cus_event: true

                                                        color: is_down ? ST.color_white_pure : ST.color_red_keyboard

                                                        CusImg {
                                                            source: key_backspace.is_down ?"/Images/PayBtn/BACKSPACEDown.png":"/Images/PayBtn/BACKSPACE.png"
                                                            anchors.centerIn: parent
                                                            width: 44 * dpi_ratio
                                                            height: width

//                                                            ColorOverlay {
//                                                                anchors.fill: parent
//                                                                source: parent
//                                                                color: key_backspace.is_down ? ST.color_red_keyboard : ST.color_white_pure
//                                                            }
                                                        }

                                                        onClicked: {
                                                            keyEvent.sendEvent("BACKSPACE")
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    CusSpacer {
                                        Layout.preferredWidth: 20 * dpi_ratio
                                    }

                                    //数字
                                    CusRect {
                                        Layout.preferredWidth: 360 * dpi_ratio
                                        Layout.fillHeight: true
                                        color: ST.color_transparent

                                        ColumnLayout {
                                            anchors.fill: parent
                                            spacing: 2 * dpi_ratio

                                            CusRect {
                                                Layout.fillWidth: true
                                                Layout.fillHeight: true
                                                color: ST.color_transparent

                                                RowLayout {
                                                    anchors.fill: parent
                                                    spacing: 2 * dpi_ratio
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "7"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "8"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "9"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "0"
                                                    }
                                                }
                                            }

                                            CusRect {
                                                Layout.fillWidth: true
                                                Layout.fillHeight: true
                                                color: ST.color_transparent
                                                RowLayout {
                                                    anchors.fill: parent
                                                    spacing: 2 * dpi_ratio
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "4"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "5"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "6"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "."
                                                    }
                                                }
                                            }
                                            CusRect {
                                                Layout.fillWidth: true
                                                Layout.fillHeight: true
                                                color: ST.color_transparent

                                                RowLayout {
                                                    anchors.fill: parent
                                                    spacing: 2 * dpi_ratio
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "1"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "2"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        key1: "3"
                                                    }
                                                    KeyBtn1 {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        is_cus_event: true

                                                        color: is_down ? ST.color_white_pure : ST.color_green

                                                        CusText {
                                                            text: qsTr("确认")
                                                            anchors.centerIn: parent
                                                            color: parent.is_down ? ST.color_green : ST.color_white_pure
                                                            font.bold: true
                                                        }

                                                        onClicked: {
                                                            keyEvent.sendEvent("ENTER")
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        CusSpacer {
                            visible: configTool.isUseStaticKeyboard
                            Layout.preferredHeight: 5 * dpi_ratio
                        }

                        PayMethods {
                            id: pay_methods
                            Layout.fillWidth: true
                            Layout.preferredHeight: (108 * dpi_ratio)
                            visible: !right_side.is_cus_goods_list
                            onSigBut1Status:{
                                logMgr.logEvtInfo4Qml("开始修改支付状态payStatus:{}",payStatus)
                                payStatus = status
                                logMgr.logEvtInfo4Qml("结束修改支付状态payStatus:{}",payStatus)
                            }
                        }
                    }
                }
            }

            //会员消费记录
            CusRect {
                id: member_order_record_root
                anchors.fill: parent
                visible: pay_page_root.is_show_member_order
                radius: ST.radius
                color: ST.color_white_pure
                is_penetration: false

                property string order_type_str: ""

                function resetInfo() {
                    order_type_str = ""
                    lm_member_order_record.clear()
                }

                onVisibleChanged: {
                    if (!visible)
                        return

                    resetInfo()
                }

                ColumnLayout {
                    anchors.fill: parent
                    anchors.topMargin: 30 * dpi_ratio
                    spacing: 30 * dpi_ratio

                    CusRect {
                        Layout.preferredHeight: 40 * dpi_ratio
                        Layout.fillWidth: true
                        Layout.leftMargin: 20 * dpi_ratio
                        Layout.rightMargin: 40 * dpi_ratio
                        color: ST.color_transparent

                        CusText {
                            text: qsTr("消费记录")
                            font.pixelSize: 32 * dpi_ratio
                            font.bold: true
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        CusText {
                            anchors.right: t_repay.left
                            text: member_info.member_info_detail_edit.cusBalance > 0 ? qsTr("账户余额: ") : qsTr("总欠款: ")
                            font.pixelSize: 28 * dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        CusText {
                            id: t_repay
                            font.bold: true
                            font.pixelSize: 35 * dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.right
                            color: member_info.member_info_detail_edit.cusBalance > 0 ? ST.color_green : ST.color_red
                            text: "￥" + member_info.member_info_detail_edit.cusBalance //"50.52"
                        }
                    }

                    CusRect {
                        Layout.preferredHeight: 60 * dpi_ratio
                        Layout.fillWidth: true
                        Layout.leftMargin: 20 * dpi_ratio
                        Layout.rightMargin: 40 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 20 * dpi_ratio

                            component CheckBtn: CusButtonManual {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 160 * dpi_ratio
                                text: qsTr("全部")

                                is_check_btn: true

                                color_font: ST.color_black
                                color_background: ST.color_white_pure

                                color_font2: ST.color_green
                                color_background2: "#E5F8F1"

                                border {
                                    color: is_checked || is_pressed ? ST.color_green : ST.color_grey_border
                                    width: 1 * dpi_ratio
                                }
                            }

                            CheckBtn {
                                text: qsTr("全部")
                                is_checked: member_order_record_root.order_type_str == ""
                                onSigClicked: {
                                    member_order_record_root.order_type_str = ""
                                }
                            }

                            CheckBtn {
                                text: qsTr("现金")
                                is_checked: member_order_record_root.order_type_str == text
                                onSigClicked: {
                                    member_order_record_root.order_type_str = text
                                }
                            }

                            CheckBtn {
                                text: qsTr("金圈支付")
                                is_checked: member_order_record_root.order_type_str == text
                                onSigClicked: {
                                    member_order_record_root.order_type_str = text
                                }
                            }

                            CheckBtn {
                                text: qsTr("储值卡")
                                is_checked: member_order_record_root.order_type_str == text
                                onSigClicked: {
                                    member_order_record_root.order_type_str = text
                                }
                            }

                            CusSpacer {
                                Layout.fillWidth: true
                            }
                        }
                    }

                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true

                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 0

                            CusRect {
                                Layout.fillWidth: true
                                Layout.preferredHeight: 60 * dpi_ratio
                                color: ST.color_grey

                                RowLayout {
                                    anchors.fill: parent
                                    anchors.leftMargin: 20 * dpi_ratio
                                    anchors.rightMargin: 20 * dpi_ratio
                                    spacing: 0

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 585 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("订单时间")
                                            color: ST.color_grey_font2
                                            anchors.centerIn: parent
                                        }
                                    }

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 390 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("收件人")
                                            color: ST.color_grey_font2
                                            anchors.centerIn: parent
                                        }
                                    }

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 530 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("订单号")
                                            color: ST.color_grey_font2
                                            anchors.centerIn: parent
                                        }
                                    }

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 300 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("付款方式")
                                            color: ST.color_grey_font2
                                            anchors.centerIn: parent
                                        }
                                    }

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 270 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("总金额")
                                            color: ST.color_grey_font2
                                            anchors.centerIn: parent
                                        }
                                    }

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 200 * dpi_ratio
                                        color: ST.color_transparent

                                        CusText {
                                            text: qsTr("数量")
                                            color: ST.color_grey_font2
                                            anchors.centerIn: parent
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_white_pure

                                ListView {
                                    id: lv_member_order_record
                                    anchors.fill: parent
                                    clip: true

                                    model: ListModel {
                                        id: lm_member_order_record
                                    }

                                    delegate: CusRect {
                                        width: lv_member_order_record.width
                                        height: 70 * dpi_ratio
                                        color: ST.color_white_pure

                                        RowLayout {
                                            anchors.fill: parent
                                            anchors.leftMargin: 20 * dpi_ratio
                                            anchors.rightMargin: 20 * dpi_ratio
                                            spacing: 0

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 585 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: qsTr("订单时间")
                                                    color: ST.color_black
                                                    anchors.centerIn: parent
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 390 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: qsTr("收件人")
                                                    color: ST.color_black
                                                    anchors.centerIn: parent
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 530 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: qsTr("订单号")
                                                    color: ST.color_black
                                                    anchors.centerIn: parent
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 300 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: qsTr("付款方式")
                                                    color: ST.color_black
                                                    anchors.centerIn: parent
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 270 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: qsTr("总金额")
                                                    color: ST.color_black
                                                    anchors.centerIn: parent
                                                }
                                            }

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                Layout.preferredWidth: 200 * dpi_ratio
                                                color: ST.color_transparent

                                                CusText {
                                                    text: qsTr("数量")
                                                    color: ST.color_black
                                                    anchors.centerIn: parent
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
