﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import SortFilterProxyModel 0.2
import EnumTool 1.0

CusRect {
    id: rect_goods_detail
    color: ST.color_white_pure

    property bool is_mini: false

    property var cusBalance: 0.0 //账户余额
    property var cusName: "" //会员姓名
    property var cusPassword: "" //账号密码
    property var cusUnique: "" //会员卡号
    property var cusPhone: "" //手机号码
    property var cusBirthday: "" //生日
    property var regeditDate: "" //注册日期
    property var cusPoints: 0.0 //会员积分
    property var cusAddress: 0.0 //会员地址
    property var cusRemark: "" //备注信息
    property var cusType: "" //会员类型: 会 储

    onVisibleChanged: {
        if (visible)
            resetMemberInfo()
    }

    function resetMemberInfo() {
        cusBalance = "0"
        cusName = ""
        cusPassword = ""
        cusUnique = ""
        cusPhone = ""
        cusBirthday = ""
        regeditDate = ""
        cusPoints = ""
        cusAddress = ""
        cusRemark = ""
        cusType = ""
    }

    function refreshMemberInfo() {
        reqMemberInfoDetail(cur_editing_member_unique)
    }

    function registerMember() {

        if (!checkDataValid()) {
            toast.openInfo(qsTr("信息有误"))
            return
        }

        var data = {
            "cusUnique"//会员唯一ID
            : cusUnique,
            "cusName"//会员名
            : cusName,
            "cusPhone"//会员电话
            : cusPhone,
            "cus_remark"//会员备注
            : cusRemark,
            "cusPassword"//会员密码
            : cusPassword,
            "cusBalance"//会员余额
            : cusBalance,
            "cusRemark"//会员备注
            : cusRemark
        }

        if (cusBalance > 999999999) {
            toast.openWarn(qsTr("金额过大"))
            return
        }

        memberControl.reqRegisterMember4Qml(function (is_succ, data) {
            if (is_succ) {
                toast.openInfo(qsTr("创建成功"))
                pay_page_root.member_ship_list.refreshMember()
                member_ship_model_status = EnumTool.PAY_PAGE__MEMBER__NULL
            } else {
                toast.openInfo(qsTr("创建失败"))
            }
        }, JSON.stringify(data))
    }

    function checkDataValid() {
        if (cusUnique == "" || cusName == "" || cusPhone == "" || cusPassword == "") {
            return false
        }
        if (!(tf_phone_number.text.length == 11))
            return false
        return true
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 15 * dpi_ratio
        anchors.bottomMargin: 30 * dpi_ratio
        anchors.leftMargin: 30 * dpi_ratio
        anchors.rightMargin: 30 * dpi_ratio

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_white_pure

            ColumnLayout {
                anchors.fill: parent
                anchors.topMargin: 30 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 70 * dpi_ratio
                    color: ST.color_transparent
                    Layout.alignment: Qt.AlignTop

                    CusText {
                        text: qsTr("新增会员")
                        anchors.verticalCenter: parent.verticalCenter
                        font {
                            pixelSize: 30 * dpi_ratio
                            bold: true
                        }
                    }
                }

                CusRect {
                    id: rect_new_member_contain
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 24 * dpi_ratio

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 55 * dpi_ratio
                            Layout.alignment: Qt.AlignTop
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 20 * dpi_ratio
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 120 * dpi_ratio
                                    color: ST.color_transparent
                                    CusText {
                                        text: "*"
                                        color: ST.color_red
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                    CusText {
                                        text: qsTr("会员卡号:")
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.right: parent.right
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    color: ST.color_transparent
                                    CusTextField {
                                        id: tf_card_id
                                        anchors.fill: parent
                                        text: cusUnique
                                        is_clicked_select_all: true
                                        keyboard_status: 1
                                        onTextEdited: {
                                            cusUnique = text
                                        }
                                        onAccepted: {
                                            search_bar_4_member.forceActiveFocus()
                                        }
                                        validator: RegularExpressionValidator {
                                            regularExpression: /^\d+(\.\d+)?$/
                                        }

                                        normal_keyboard_x: 730 * dpi_ratio
                                        normal_keyboard_y: 560 * dpi_ratio

                                        digital_keyboard_x: 730 * dpi_ratio
                                        digital_keyboard_y: 430 * dpi_ratio
                                    }
                                }
                                CusRect {
                                    color: ST.color_transparent
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 130 * dpi_ratio
                                }
                            }
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 55 * dpi_ratio
                            Layout.alignment: Qt.AlignTop
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 20 * dpi_ratio
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 120 * dpi_ratio
                                    color: ST.color_transparent
                                    CusText {
                                        text: "*"
                                        color: ST.color_red
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                    CusText {
                                        text: qsTr("会员姓名:")
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.right: parent.right
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    color: ST.color_transparent
                                    CusTextField {
                                        id: tf_member_name
                                        anchors.fill: parent
                                        text: cusName
                                        is_clicked_select_all: true
                                        onTextEdited: {
                                            cusName = text
                                        }
                                        onAccepted: {
                                            search_bar_4_member.forceActiveFocus()
                                        }
                                        normal_keyboard_x: 730 * dpi_ratio
                                        normal_keyboard_y: 560 * dpi_ratio

                                        digital_keyboard_x: 730 * dpi_ratio
                                        digital_keyboard_y: 430 * dpi_ratio
                                    }
                                }
                                CusRect {
                                    color: ST.color_transparent
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 130 * dpi_ratio
                                }
                            }
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 55 * dpi_ratio
                            Layout.alignment: Qt.AlignTop
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 20 * dpi_ratio
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 120 * dpi_ratio
                                    color: ST.color_transparent
                                    CusText {
                                        text: "*"
                                        color: ST.color_red
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                    CusText {
                                        text: qsTr("手机号码:")
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.right: parent.right
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    color: ST.color_transparent
                                    CusTextField {
                                        id: tf_phone_number
                                        anchors.fill: parent
                                        text: cusPhone
                                        keyboard_status: 1
                                        is_clicked_select_all: true
                                        onTextEdited: {
                                            cusPhone = text
                                        }
                                        onAccepted: {
                                            search_bar_4_member.forceActiveFocus()
                                        }
                                        validator: RegularExpressionValidator {
                                            regularExpression: /^1\d{0,10}$/
                                        }
                                        normal_keyboard_x: 730 * dpi_ratio
                                        normal_keyboard_y: 560 * dpi_ratio

                                        digital_keyboard_x: 730 * dpi_ratio
                                        digital_keyboard_y: 430 * dpi_ratio
                                    }
                                }
                                CusRect {
                                    color: ST.color_transparent
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 130 * dpi_ratio
                                }
                            }
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 55 * dpi_ratio
                            Layout.alignment: Qt.AlignTop
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 20 * dpi_ratio
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 120 * dpi_ratio
                                    color: ST.color_transparent
                                    CusText {
                                        text: "*"
                                        color: ST.color_red
                                        anchors.verticalCenter: parent.verticalCenter
                                        visible: false
                                    }
                                    CusText {
                                        text: qsTr("账户余额:")
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.right: parent.right
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    color: ST.color_transparent
                                    CusTextField {
                                        anchors.fill: parent
                                        text: cusBalance
                                        is_clicked_select_all: true
                                        keyboard_status: 1
                                        onTextEdited: {
                                            cusBalance = text
                                        }
                                        onAccepted: {
                                            search_bar_4_member.forceActiveFocus()
                                        }
                                        normal_keyboard_x: 730 * dpi_ratio
                                        normal_keyboard_y: 560 * dpi_ratio

                                        digital_keyboard_x: 730 * dpi_ratio
                                        digital_keyboard_y: 430 * dpi_ratio
                                    }
                                }
                                CusRect {
                                    color: ST.color_transparent
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 130 * dpi_ratio
                                }
                            }
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 55 * dpi_ratio
                            Layout.alignment: Qt.AlignTop
                            visible: !rect_goods_detail.is_mini
                            color: ST.color_transparent
                            RowLayout {
                                anchors.fill: parent
                                spacing: 20 * dpi_ratio
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 120 * dpi_ratio
                                    color: ST.color_transparent
                                    CusText {
                                        text: "*"
                                        color: ST.color_red
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                    CusText {
                                        text: qsTr("账号密码:")
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.right: parent.right
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    color: ST.color_transparent
                                    CusTextField {
                                        anchors.fill: parent
                                        echoMode: TextInput.Password
                                        text: cusPassword
                                        is_clicked_select_all: true
                                        onTextEdited: {
                                            cusPassword = text
                                        }
                                        onAccepted: {
                                            search_bar_4_member.forceActiveFocus()
                                        }
                                        normal_keyboard_x: 730 * dpi_ratio
                                        normal_keyboard_y: 560 * dpi_ratio

                                        digital_keyboard_x: 730 * dpi_ratio
                                        digital_keyboard_y: 430 * dpi_ratio
                                    }
                                }
                                CusRect {
                                    color: ST.color_transparent
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 130 * dpi_ratio
                                }
                            }
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 55 * dpi_ratio
                            Layout.alignment: Qt.AlignTop
                            visible: !rect_goods_detail.is_mini
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 20 * dpi_ratio
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 120 * dpi_ratio
                                    color: ST.color_transparent
                                    CusText {
                                        text: "*"
                                        color: ST.color_red
                                        anchors.verticalCenter: parent.verticalCenter
                                        visible: false
                                    }
                                    CusText {
                                        text: qsTr("备注信息:")
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.right: parent.right
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    color: ST.color_transparent
                                    CusTextField {
                                        anchors.fill: parent
                                        text: cusRemark
                                        is_clicked_select_all: true
                                        onTextEdited: {
                                            cusRemark = text
                                        }
                                        onAccepted: {
                                            search_bar_4_member.forceActiveFocus()
                                        }
                                        normal_keyboard_x: 730 * dpi_ratio
                                        normal_keyboard_y: 560 * dpi_ratio

                                        digital_keyboard_x: 730 * dpi_ratio
                                        digital_keyboard_y: 430 * dpi_ratio
                                    }
                                }
                                CusRect {
                                    color: ST.color_transparent
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 130 * dpi_ratio
                                }
                            }
                        }

                        CusSpacer {
                            Layout.fillHeight: true
                        }
                    }
                }

                CusRect {
                    id: rect_save_goods_kind
                    Layout.preferredHeight: 80 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        anchors.leftMargin: 30 * dpi_ratio
                        anchors.rightMargin: 30 * dpi_ratio

                        CusButton {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            text: qsTr("注册")
                            onClicked: {
                                keyboard_c.closeAll()

                                if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_ACTION_MEMBER_REGISTER))
                                    registerMember()
                                else
                                    toast.openWarn(qsTr("无此权限"))
                            }
                        }
                    }
                }
            }
        }
    }
}
