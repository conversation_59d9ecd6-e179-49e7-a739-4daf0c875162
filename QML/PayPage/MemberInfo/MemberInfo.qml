﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../.."
import EnumTool 1.0

CusRect {
    id: category_detail

    property string cur_member_id: ""
    property bool is_create_member: false

    property alias member_info_detail_create: member_info_detail_create
    property alias member_info_detail_edit: member_info_detail_edit

    // 编辑会员时未选择会员
    CusRect {
        anchors.fill: parent
        visible: is_member_ship_model && member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER && cur_editing_member_unique == null
        color: ST.color_white_pure

        CusRect {
            width: 180 * dpi_ratio
            height: 180 * dpi_ratio
            anchors.horizontalCenter: parent.horizontalCenter
            y: 240 * dpi_ratio
            color: ST.color_transparent

            Image {
                anchors.fill: parent
                source: "/Images/img_member.png"
            }
        }

        CusRect {
            width: 230 * dpi_ratio
            height: 70 * dpi_ratio
            anchors.horizontalCenter: parent.horizontalCenter
            y: 450 * dpi_ratio
            color: ST.color_transparent
            CusText {
                text: qsTr("请选择会员")
                anchors.centerIn: parent
            }
        }
    }

    MemberInfoDetail_Create {
        id: member_info_detail_create
        anchors.fill: parent
        visible: is_member_ship_model && member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__NEW_MEMBER
    }

    MemberInfoDetail_Edit {
        id: member_info_detail_edit
        anchors.fill: parent
        visible: is_member_ship_model && member_ship_model_status == EnumTool.PAY_PAGE__MEMBER__MANAGE_MEMBER && cur_editing_member_unique != null
    }
}
