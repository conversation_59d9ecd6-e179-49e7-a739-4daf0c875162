﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."
import ".."
import EnumTool 1.0

Item {
    id: popup_root

    function open() {
        popup_root.visible = true
        keyboard_c.closeAll()
    }
    function close() {
        popup_root.visible = false
        keyboard_c.closeAll()
        sigClose()
    }

    function tryConfirm() {
        if (tf_stock_change.text == "") {
            toast.openWarn("数量不可为空")
            return
        }
        confirm(tf_stock_change.text)
        close()
    }

    function cashPay() {
        if (Number(unit_price_str) <= 0) {
            toast.openWarn("金额过小!")
            return
        }

        payMethodControl.rechargeMemberByCash4Qml(function (pay_state, data) {
            refreshMemberInfo()
            member_ship_list.refreshMember()
            toast.openInfo(data["msg"])
        }, cusUnique, unit_price_str)
        close()
    }

    function reqRechargeConfig() {
        shopControl.reqRechargeConfig(function (is_succ, data) {
            if (is_succ) {
                lm_recharge_cfg.clear()
                var json_doc = JSON.parse(data)

                if (Array.isArray(json_doc.data)) {
                    for (var i = 0; i < json_doc.data.length; ++i) {
                        var cur_item = json_doc.data[i]
                        lm_recharge_cfg.append(cur_item)
                    }
                }
            } else {
                // toast.openWarn("获取充值活动失败")
            }
        })
    }

    signal confirm(var change_num)
    signal cancel
    signal sigClose

    property string title_name: qsTr("会员充值")

    property string unit_price_str: "0"

    onVisibleChanged: {
        if (visible)
            unit_price_str = "0"
    }

    function resetUnitPrice() {
        unit_price_str = "0"
    }

    function addStrNum(str, str2) {
        if (str == "0" && str2 == "0")
            return "0"

        if (str2 == ".") {
            if (str.indexOf(".") == -1) {
                return str += str2
            }
            return str
        }

        if (str == "0")
            return str2

        str += str2

        let arr = str.split(".")

        var ret_str = arr[0]

        if (arr.length == 2) {
            ret_str = arr[0] + "." + arr[1].substring(0, 2)
        }

        return ret_str
    }

    function setInput(input) {
        unit_price_str = addStrNum(unit_price_str, input)
    }

    function backspace() {
        unit_price_str = unit_price_str.substring(0, unit_price_str.length - 1)
        if (unit_price_str == "") {
            unit_price_str = "0"
        }
    }

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 700 * dpi_ratio
        height: 800 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2
        property int page_index: 0 //页面枚举 0金圈 1现金

        color: ST.color_white_pure
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35 * dpi_ratio
                color: ST.color_white_pure

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 20 * dpi_ratio
                    //TAB
                    CusRect {
                        Layout.fillWidth: true
                        Layout.leftMargin: -35 * dpi_ratio
                        Layout.rightMargin: -35 * dpi_ratio
                        Layout.topMargin: -35 * dpi_ratio
                        Layout.preferredHeight: 80 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            RadioBtnRectUnderLine {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                control_text.text: qsTr("金圈平台")
                                checked: popup_contain_root.page_index == 0
                                onClicked: {
                                    popup_contain_root.page_index = 0
                                    tf_payment_code.forceActiveFocus()
                                }
                            }
                            RadioBtnRectUnderLine {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                control_text.text: qsTr("现金")
                                checked: popup_contain_root.page_index == 1
                                onClicked: {
                                    popup_contain_root.page_index = 1
                                    tf_payment_code.focus = false
                                }
                            }
                        }
                    }
                    Component.onCompleted: {
                        visibleChanged()
                    }

                    onVisibleChanged: {
                        if (visible) {
                            if (popup_contain_root.page_index == 0) {
                                tf_payment_code.forceActiveFocus()
                            } else {
                                tf_payment_code.focus = false
                            }
                        }
                    }

                    //输入框
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 70 * dpi_ratio
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 160 * dpi_ratio
                                color: ST.color_transparent
                                CusText {
                                    text: qsTr("付款金额: ")
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent
                                CusTextField {
                                    anchors.fill: parent
                                    text: unit_price_str
                                    enabled: false
                                }
                            }
                        }
                        TextField {
                            id: tf_payment_code
                            width: 0
                            height: 0
                            onAccepted: {
                                payMethodControl.rechargeMemberByPaymentCode4Qml(function (pay_state, data) {
                                    if (pay_state == EnumTool.PAY_STATUS__SUCCESS) {
                                        member_ship_list.refreshMember()
                                        toast.openInfo(qsTr("支付完成"))
                                        popup_root.close()
                                    } else {
                                        toast.openWarn(qsTr("支付失败"))
                                    }
                                }, text, cusUnique, unit_price_str)
                                text = ""
                            }
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 150 * dpi_ratio
                        color: ST.color_white_deeper
                        radius: ST.radius

                        GridView {
                            id: lv_recharge_config
                            anchors.fill: parent

                            cellHeight: 75 * dpi_ratio
                            property int row_num: 2
                            cellWidth: width / row_num

                            model: ListModel {
                                id: lm_recharge_cfg
                            }
                            clip: true
                            delegate: CusRect {
                                width: lv_recharge_config.cellWidth
                                height: lv_recharge_config.cellHeight
                                color: ST.color_transparent

                                CusRect {
                                    anchors.fill: parent
                                    anchors.margins: 8 * dpi_ratio
                                    color: ST.color_white_pure
                                    radius: ST.radius
                                    RowLayout {
                                        anchors.fill: parent
                                        anchors.margins: 3 * dpi_ratio

                                        CusRect {
                                            color: ST.color_transparent
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 45 * dpi_ratio
                                            Layout.fillWidth: true
                                            CusText {
                                                text: qsTr("充")
                                                anchors.centerIn: parent
                                            }
                                        }
                                        CusRect {
                                            color: ST.color_transparent
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 90 * dpi_ratio
                                            Layout.fillWidth: true
                                            CusText {
                                                text: recharge_money.toFixed(2)
                                                color: ST.color_red
                                                font.pixelSize: 30 * dpi_ratio
                                                font.bold: true
                                                anchors.centerIn: parent
                                            }
                                        }

                                        CusRect {
                                            color: ST.color_transparent
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 45 * dpi_ratio
                                            Layout.fillWidth: true
                                            CusText {
                                                text: qsTr("送")
                                                anchors.centerIn: parent
                                            }
                                        }
                                        CusRect {
                                            color: ST.color_transparent
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 90 * dpi_ratio
                                            Layout.fillWidth: true
                                            CusText {
                                                text: give_money.toFixed(2)
                                                anchors.centerIn: parent
                                            }
                                        }
                                    }
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        unit_price_str = recharge_money.toFixed(2)
                                    }
                                }
                            }

                            Component.onCompleted: {
                                reqRechargeConfig()
                            }

                            onVisibleChanged: {
                                if (!visible)
                                    return
                                reqRechargeConfig()
                            }
                        }
                    }

                    //键盘
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent

                                GridLayout {
                                    id: gl_calc_btns

                                    anchors.fill: parent
                                    columns: 3
                                    rows: 4

                                    columnSpacing: 10 * dpi_ratio
                                    rowSpacing: 10 * dpi_ratio

                                    property real col_width: (width - ((columns - 1) * rowSpacing)) / columns
                                    property real col_height: (height - ((rows - 1) * columnSpacing)) / rows

                                    function prefWidth(item) {
                                        return col_width * item.Layout.columnSpan
                                    }
                                    function prefHeight(item) {
                                        return col_height * item.Layout.rowSpan
                                    }

                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "7"
                                    }

                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "8"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "9"
                                    }

                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "4"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "5"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "6"
                                    }

                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "1"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "2"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "3"
                                    }

                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "0"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "00"
                                    }
                                    KeyRect {
                                        Layout.preferredWidth: parent.prefWidth(this)
                                        Layout.preferredHeight: parent.prefHeight(this)
                                        key_text: "."
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 130 * dpi_ratio
                                color: ST.color_transparent

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 10 * dpi_ratio

                                    property real cell_height: gl_calc_btns.col_height

                                    KeyRect2 {
                                        Layout.preferredHeight: parent.cell_height
                                        Layout.fillWidth: true
                                        key_text: qsTr("清空")
                                        color: ST.color_red
                                        text.color: ST.color_white_pure
                                        onPressKey: {
                                            resetUnitPrice()
                                        }
                                    }
                                    KeyRect2 {
                                        Layout.preferredHeight: parent.cell_height
                                        Layout.fillWidth: true
                                        key_text: qsTr("退格")
                                        color: ST.color_grey_font
                                        text.color: ST.color_white_pure
                                        onPressKey: {
                                            backspace()
                                        }
                                    }
                                    KeyRect2 {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        key_text: qsTr("确定")
                                        color: popup_contain_root.page_index == 0 ? ST.color_grey : ST.color_blue_deeper
                                        text.color: ST.color_white_pure
                                        enabled: popup_contain_root.page_index != 0
                                        onPressKey: {
                                            cashPay()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    component KeyRect: CusRect {
        id: rect_key
        property string key_text: "0"
        signal pressKey(var key)
        color: ST.color_transparent

        border {
            width: 1 * dpi_ratio
            color: ST.color_grey_border
        }

        CusText {
            anchors.centerIn: parent
            text: rect_key.key_text
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {
                rect_key.pressKey(rect_key.key_text)
            }
        }

        onPressKey: {
            setInput(key)
        }
    }
    component KeyRect2: CusRect {
        id: rect_key2
        property string key_text: "0"
        signal pressKey(var key)

        property alias text: text

        CusText {
            id: text
            anchors.centerIn: parent
            text: rect_key2.key_text
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {
                rect_key2.pressKey(rect_key2.key_text)
            }
        }
    }
}
