﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.12
import QtGraphicalEffects 1.0
import "../.."
import ShopCartModel 1.0
import SettingEnum 1.0

Item {
    id: control_root

    property bool is_big_font: true
    property bool is_discount_num: false
    property int  discount_num: 0
    property string newAllPrice:""
    property bool is_editing_unit_price: (ti_goods_price.ti_control.activeFocus || ti_discount.activeFocus) && shopCartList.highlightTimeStamp == goods_date_time_role

    property alias ti_control_activeFocus: ti_goods_price.ti_control.activeFocus
    property alias ti_discount_activeFocus: ti_discount.activeFocus
    property string goodNewCountCopy:""
    onTi_control_activeFocusChanged: {
        if (!ti_control_activeFocus) {
            return
        }
        shopCartList.highlightTimeStamp = goods_date_time_role
    }

    onTi_discount_activeFocusChanged: {
        if (!ti_discount_activeFocus) {
            return
        }
        shopCartList.highlightTimeStamp = goods_date_time_role
    }
    Timer {
           id: singleShotTimer
           interval: 200
           repeat: false
           running: false

           onTriggered: {
               shopCartList.handleDiscountsGift()
           }
       }
    function setDiscount(discount) {
        shopCartList.setItemDiscountByBarcode(model.goods_barcode, discount, model.goods_date_time_role)
    }
    function setPrice(price) {
        shopCartList.setItemPriceByBarcode(model.goods_barcode, price, model.goods_date_time_role)
    }
    function setNum(num) {
        shopCartList.setItemNumByBarcode(model.goods_barcode, num, model.goods_date_time_role)
    }
    function setSubtotal(subtotal) {
        shopCartList.setItemSubtotalByBarcode(model.goods_barcode, subtotal, model.goods_date_time_role)
    }
    function calculateFontSize(text,maxLength) {
        var scaleFactor = 1.0;
        var fontSize = 30 * ST.dpi;
        if (text.length > maxLength) {
            scaleFactor = maxLength/text.length;
            fontSize *= scaleFactor ;
        }
        return fontSize;
    }
    function closeDiscountEdit() {
        control_root.forceActiveFocus() //用于移除焦点
        search_bar.forceActiveFocus()
    }

    CusRect {
        id: rect_info
        width: parent.width
        height: 70 * dpi_ratio
        color: ST.color_transparent

        CusRect {
            anchors.fill: parent

            property bool is_cur_cart_item: shopCartList.highlightTimeStamp == goods_date_time_role

            color: is_cur_cart_item ? ST.color_grey_background : ST.color_transparent
            opacity: .2
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    shopCartList.highlightTimeStamp = goods_date_time_role
                    closeDiscountEdit()
                }
            }
        }

        RowLayout {
            anchors.fill: parent

            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                clip: true
                color: ST.color_transparent

                CusText {
                    id: goods_name_text
                    anchors.left: parent.left
                    anchors.leftMargin: 2 * dpi_ratio
                    anchors.top: parent.top
                    anchors.topMargin: 3 * dpi_ratio
                    text: qsTr("惠")
                    font.pixelSize: 14 * dpi_ratio
                    color: ST.color_red
                    visible: false
                }
                CusText {
                    id: goods_name_price_in_text
                    anchors.left: parent.left
                    anchors.leftMargin: 10 * dpi_ratio
                    anchors.bottom: parent.bottom
                    anchors.bottomMargin: 1 * dpi_ratio
                    text: qsTr("此商品售价低于进价")
                    font.pixelSize: 12 * dpi_ratio
                    color: ST.color_green
                    visible: is_need_visible && configTool.isNegativeProfitHint
                    property bool is_need_visible: false
                }
                CusText {
                    id: goodsNameCus
                    anchors.leftMargin: 15 * dpi_ratio
                    anchors.rightMargin: 15 * dpi_ratio
                    width: parent.width - (anchors.leftMargin + anchors.rightMargin)
                    anchors.centerIn: parent
                    elide: Text.ElideRight
                    text:model.goods_name_role
                    font.pixelSize: is_big_font ? 30 * dpi_ratio : 22 * dpi_ratio
                }
            }

            TextInput4ShopCart {
                id: ti_goods_price
                Layout.fillHeight: true
                Layout.preferredWidth: 130 * dpi_ratio
                text: model.goods_price_role
                is_rmb: true
                color: ST.color_transparent

                keyboard_status: 1

                normal_keyboard_x: 670 * dpi_ratio
                normal_keyboard_y: 560 * dpi_ratio

                digital_keyboard_x: 800 * dpi_ratio
                digital_keyboard_y: 190 * dpi_ratio

                ti_control.font.pixelSize: calculateFontSize(ti_goods_total_price.text,7)

                ti_control.validator: RegularExpressionValidator {
                    regularExpression: /^[0-9]{0,4}(?:\.[0-9]{1,2})?$/
                }

                onEditingFinished: {
                    setPrice(Number(text))
                }

                onAccept: {
                    pay_page_root.search_bar.forceActiveFocus()
                    closeDiscountEdit()
                }

                onTextChanged: {
                    var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(model.goods_barcode))
                    if ((cur_goods.goods_sale_price).toFixed(2) > Number(text)) {
                        goods_name_text.visible = true
                    } else {
                        goods_name_text.visible = false
                    }
                    if (cur_goods.goods_in_price.toFixed(2) > Number(text)) {
                        goods_name_price_in_text.is_need_visible = true
                    } else {
                        goods_name_price_in_text.is_need_visible = false
                    }
                }
            }

            TextInput4ShopCart {
                Layout.fillHeight: true
                Layout.preferredWidth: 130 * dpi_ratio
                font_color: ST.color_black
                color: ST.color_transparent
                text: GOODS_IS_WEIGHT ? model.goods_amount_role : Number(model.goods_amount_role).toFixed(0)
                keyboard_status: 1
                normal_keyboard_x: 670 * dpi_ratio
                normal_keyboard_y: 560 * dpi_ratio

                digital_keyboard_x: 800 * dpi_ratio
                digital_keyboard_y: 190 * dpi_ratio
                ti_control.font.pixelSize: calculateFontSize(ti_goods_total_price.text,5)
                ti_control.validator: RegularExpressionValidator {
                    regularExpression: GOODS_IS_WEIGHT ? /^[0-9]{0,4}(?:\.[0-9]{1,3})?$/ : /^[0-9]{0,4}$/
                }
                onFocusChanged: {
                    if (!focus)
                        editingFinished()
                }

                onTextChanged:
                {
                    logMgr.logDataInfo4Qml("商品满减满赠是否开启:{}",configTool.getSetting(SettingEnum.ConfigEnum.IS_GOODS_MANJIANMANZENG))
                    if(configTool.getSetting(SettingEnum.ConfigEnum.IS_GOODS_MANJIANMANZENG)){
                        if(text !== "NaN"){
                        // 满赠商品添加
                        logMgr.logDataInfo4Qml("开始商品<<满赠判断-满赠>>，当前购物车该产品数量为:{};goodNewCountCopy:{};goods_barcode:{}",text.length,goodNewCountCopy,goods_barcode)
                        var goods_json = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))
                        if (goods_json) {
                            var giftVector = goodsPromotionCtrl.queryGoodsPromotionForQml2(goods_json.goods_id)
                            logMgr.logDataInfo4Qml("此处展示函数返回的stringVector数据：{};length:{}",giftVector,giftVector.length)
                            //判断是否存在满赠商品
                            if(giftVector.length !== 0){
                                var manzengArray2 = giftVector.split("^")
                                logMgr.logDataInfo4Qml("manzengArray2[7]:{}",manzengArray2[7])
                                if (manzengArray2.length > 7 && manzengArray2[7] !== "")
                                {
                                    var cur_goods = JSON.parse(goodsManager.getGoodsByGoodId4Qml(manzengArray2[7]))
                                    var goodsInfoStrTmp = ""
                                    goodsInfoStrTmp += (cur_goods.goods_id)
                                    goodsInfoStrTmp += ("^")
                                    goodsInfoStrTmp += (cur_goods.goods_name)
                                    goodsInfoStrTmp += ("^")
                                    goodsInfoStrTmp += (cur_goods.goods_sale_price)
                                    goodsInfoStrTmp += ("^")
                                    goodsInfoStrTmp += (cur_goods.goods_standard)
                                    goodsInfoStrTmp += ("^")
                                    goodsInfoStrTmp += (cur_goods.goods_count)
                                    goodsInfoStrTmp += ("^")
                                    goodsInfoStrTmp += (cur_goods.goods_picturepath)
                                    goodsInfoStrTmp += ("^")
                                    goodsInfoStrTmp += (cur_goods.goods_barcode)
                                    goodsInfoStrTmp += ("^")
                                    goodsInfoStrTmp += (cur_goods.goods_in_price)
                                    goodsInfoStrTmp += ("^")
                                    goodsInfoStrTmp += ("111")
                                    goodsInfoStrTmp += ("^")
                                    goodsInfoStrTmp += ("1")
                                    goodsInfoStrTmp += ("^")
                                    goodsInfoStrTmp += (cur_goods.goods_sale_price);
                                    goodsInfoStrTmp += (";");
                                    var goodsArray = goodsInfoStrTmp.split(";")
                                    logMgr.logDataInfo4Qml("goodsInfoStrTmp:{}",goodsInfoStrTmp )
                                    logMgr.logDataInfo4Qml("goodsArray.length:{}",goodsArray.length )
                                    for(var i = 0;i < goodsArray.length -1 ; i++)
                                    {
                                        logMgr.logDataInfo4Qml("i为:{}",i)
                                        var tmpArray = goodsArray[i].split("^")
                                        logMgr.logDataInfo4Qml("tmpArray:{}",tmpArray[6] )

                                        //计算满赠品数量
                                        var goodNewCount;
                                        if (text*1 >= manzengArray2[1]*1 && manzengArray2[1] != "" && manzengArray2[2] == "")
                                        {
                                            goodNewCount = manzengArray2[4];
                                        }
                                        else if (text*1 >= manzengArray2[1]*1 && manzengArray2[1] != "" &&
                                                 text*1 < manzengArray2[2]*1 && manzengArray2[2] != "")
                                        {
                                            goodNewCount = manzengArray2[4];
                                        }
                                        else if (text*1 >= manzengArray2[2]*1 && manzengArray2[3] == "")
                                        {
                                            goodNewCount = manzengArray2[5];
                                        }
                                        else if (text*1 >= manzengArray2[2]*1 && text*1 < manzengArray2[3]*1 && manzengArray2[3] != "")
                                        {
                                            goodNewCount = manzengArray2[5];
                                        }
                                        else if (text*1 >= manzengArray2[3]*1 && manzengArray2[3] != "")
                                        {
                                            goodNewCount = manzengArray2[6];
                                        }
                                        else
                                        {
                                            break;
                                        }
                                        var giftInfo = tmpArray + (",") + goodNewCount
                                        //selectSql = "select goods_id, meet_count1 ,meet_count2, meet_count3, gift_count1, gift_count2, gift_count3, "
                                        //           "goods_id_gift, start_time, end_time from goodsPromotion2 where goods_id='";
                                        goodNewCountCopy = goodNewCount
                                        if(goodNewCount !== ""){
                                            logMgr.logDataInfo4Qml("满足赠送条件 开始添加满赠商品")
                                            shopCartList.appendGiftItemByBarcode(tmpArray[6], false, false,giftInfo)
                                             //goodsNameCus.text  = "(赠"+goodNewCountCopy+")" + model.goods_name_role
                                        }else{
                                            logMgr.logDataInfo4Qml("不满足赠送条件")
                                        }
                                        giftInfo = ""
                                        goodNewCount = ""
                                        goodsInfoStrTmp = ""

                                    }
                                }
                            }
                            else{
                            logMgr.logDataInfo4Qml("该商品不存在满赠活动 开始判断是否存在<<商品折扣-满减>>")
                            var discountDatas = goodsPromotionCtrl.queryGoodsPromotionForQml1(goods_json.goods_id)
                            logMgr.logDataInfo4Qml("此处展示函数返回的discountDatas数据：{};length:{}",discountDatas,discountDatas.length)
                            //1543305237^3^^^1.00^^^0^5;length:25
                            if(discountDatas.length !== 0){
                                var discountArray = discountDatas.split(";")
                                logMgr.logDataInfo4Qml("discountArray：{};length:{}",discountArray,discountArray.length)
                                for (var m = 0; m < discountArray.length; m++)
                                {
                                    var discountData = discountArray[m].split("^");

                                    for(var i = 0 ;i < discountData.length; i++){
                                        logMgr.logDataInfo4Qml("discountData[{}]:{}",i,discountData[i])
                                    }
                                    logMgr.logDataInfo4Qml("discountData[3]:{}",discountData[3])
                                    logMgr.logDataInfo4Qml("discountData[3]*1:{}",discountData[3]*1)
                                    logMgr.logDataInfo4Qml("text*1 >= discountData[3]*1:{}",text*1 >= discountData[3]*1)
                                    if(discountData.length >= 7){
                                        var newPrice
                                        var newCount
                                        var surplusCount = 0
                                        logMgr.logDataInfo4Qml("text*1:{};",text)
                                        logMgr.logDataInfo4Qml("goods_json.goods_id:{}",typeof text*1)
                                        logMgr.logDataInfo4Qml("goods_json.goods_id:{}",typeof discountData[1]*1)
                                        //if ( (discountData[0] === (goods_json.goods_id.toString())) && discountData[7] === "0") discountData[7] === "0" 不参加订单促销 ==1 是
                                        if ( (discountData[0] === (goods_json.goods_id.toString())) )
                                        {


                                            //goodsIdTmp = listModel.get(i).id; 没用
                                            //                console.debug(listModel.get(i).counts);
                                            if (text*1 < discountData[1]*1 || discountData[1] === "")
                                            {
                                                newPrice = goods_json.goods_sale_price;
                                                newCount = text*1;
                                                logMgr.logDataInfo4Qml("method1")
                                            }
                                            else if ((text*1 >= discountData[1]*1 && text*1 < discountData[2]*1 && discountData[1] !== "")
                                                     || (discountData[1] !== "" && text*1 >= discountData[1]*1 && discountData[2] === "" && text*1 <= discountData[8]*1))
                                            {
                                                newPrice = (goods_json.goods_sale_price*discountData[4]*0.1).toFixed(2);
                                                newCount = text*1;
                                                logMgr.logDataInfo4Qml("method2")
                                            }
                                            else if ((text*1 >= discountData[2]*1 && text*1 < discountData[3]*1 && discountData[2] !== "")
                                                     || (text*1 >= discountData[2]*1 && discountData[3] === "" && text*1 <= discountData[8]*1 && discountData[2] !== ""))
                                            {
                                                newPrice = (goods_json.goods_sale_price*discountData[5]*0.1).toFixed(2);
                                                newCount = text*1;
                                                logMgr.logDataInfo4Qml("method3")

                                            }
                                            else if (text*1 >= discountData[3]*1 && (text*1 <= discountData[8]*1 || discountData[8]*1 == 0) && discountData[3] !== "")
                                            {
                                                newPrice = (goods_json.goods_sale_price*discountData[6]*0.1).toFixed(2);
                                                newCount = text*1;
                                                logMgr.logDataInfo4Qml("method4")
                                            }
                                            else if(text*1 > discountData[8]*1 && discountData[8]*1 > 0)
                                            {
                                                logMgr.logDataInfo4Qml("1:{}:{}:{}",discountData[6]!== "",discountData[5]!== "",discountData[4]!== "")
                                                newPrice = discountData[6]!== ""?(goods_json.goods_sale_price*discountData[6]*0.1).toFixed(2):discountData[5]!== ""?(goods_json.goods_sale_price*discountData[5]*0.1).toFixed(2):discountData[4]!== ""?(goods_json.goods_sale_price*discountData[4]*0.1).toFixed(2):goods_json.goods_sale_price;
                                                surplusCount = (text*1 - discountData[8]*1).toFixed(3);
                                                //newCount = (discountData[8]*1).toFixed(3);
                                                newCount = (discountData[8]*1).toFixed(3);
                                                logMgr.logDataInfo4Qml("method5")
                                            }
                                            else
                                            {
                                                logMgr.logDataInfo4Qml("商品数量不符合商品折扣活动")
                                                break;
                                            }
                                            //自定义小计进行操作
                                            logMgr.logDataInfo4Qml("(goods_json.goods_sale_price)*1:{}",(goods_json.goods_sale_price)*1)
                                            logMgr.logDataInfo4Qml("surplusCount:{};newCount:{};newPrice:{}",surplusCount,newCount,newPrice)
                                            newAllPrice = ((surplusCount*1) * ((goods_json.goods_sale_price)*1)) + ((newCount*1) *(newPrice*1)) ;
                                            logMgr.logDataInfo4Qml("newAllPrice:{}",newAllPrice)
                                            setNum(Number(text*1))
                                            setSubtotal(Number(newAllPrice))
                                            is_discount_num = true
                                        }

                                    }
                                }
                            }else{
                                logMgr.logDataInfo4Qml("该商品不存在折扣活动！")
                            }
                            }
                        }
                    }
                    else{
                        logMgr.logDataInfo4Qml("添加数据为空！")
                    }
                    }

                }
                onEditingFinished: {
                    if (Number(text) === 0) {
                        toast.openWarn("数量不可为0")
                        text = (model.goods_amount_role).toFixed(2)
                    }
                    logMgr.logDataInfo4Qml("进入数量编辑完成后的操作！")
                    setNum(Number(text))
                    logMgr.logDataInfo4Qml("is_discount_num:{}",is_discount_num)
                    if(is_discount_num){
                        logMgr.logDataInfo4Qml("折扣折扣")
                        setSubtotal(Number(newAllPrice))
                        discount_num += 1
                        if(discount_num == 2){
                            discount_num = 0
                            is_discount_num = !is_discount_num
                        }
                    }
                    pay_page_root.search_bar.forceActiveFocus()
                    closeDiscountEdit()
                }
            }

            TextInput4ShopCart {
                id: ti_goods_total_price
                Layout.fillHeight: true
                Layout.preferredWidth: 130 * dpi_ratio
                is_rmb: true
                color: ST.color_transparent
                keyboard_status: 1
                normal_keyboard_x: 670 * dpi_ratio
                normal_keyboard_y: 560 * dpi_ratio

                digital_keyboard_x: 800 * dpi_ratio
                digital_keyboard_y: 190 * dpi_ratio

                text: model.goods_total_price_role

                ti_control.font.pixelSize:calculateFontSize(ti_goods_total_price.text,7)
                ti_control.validator: RegularExpressionValidator {
                    regularExpression: /^[0-9]{0,8}(?:\.[0-9]{1,2})?$/
                }
                onClickedSignal:{
                }
                onTextChanged: {
                    singleShotTimer.start()
                }

                onEditingFinished: {
                    if(Number(text) > 100000 ){
                        toast.openWarn("单个商品总价超过上限");
                        setPrice(Number(ti_goods_price.text))
                    }else{
                        setSubtotal(Number(text));
                        search_bar.forceActiveFocus();
                    }
                }
            }

            CusRect {
                Layout.fillHeight: true
                Layout.preferredWidth: 50 * dpi_ratio
                color: ST.color_transparent
                Image {
                    source: "/Images/deleteGoodsImage.png"
                    anchors.centerIn: parent
                    width: 30 * dpi_ratio
                    height: width
                    fillMode: Image.PreserveAspectFit
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        logMgr.logEvtWarn4Qml("删除商品: {}", model.goods_barcode)
                        clearGoodsByBarcode(model.goods_barcode, model.goods_date_time_role)
                    }
                }
            }
        }
    }

    CusRect {
        anchors.top: rect_info.bottom
        width: parent.width + 80 * dpi_ratio
        height: parent.height - rect_info.height
        visible: is_editing_unit_price
        color: ST.color_grey
        radius: ST.radius

        RowLayout {
            anchors.fill: parent
            anchors.margins: 15 * dpi_ratio
            spacing: 8 * dpi_ratio

            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_white_pure
                property real discount_ratio: .9
                RowLayout {
                    anchors.fill: parent
                    anchors.bottomMargin: 15
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                    CusText {
                        text: "9"
                        font {
                            bold: true
                            pixelSize: 40 * dpi_ratio
                        }
                        Layout.alignment: Qt.AlignHCenter | Qt.AlignBottom
                    }
                    CusText {
                        text: qsTr("折")
                        Layout.alignment: Qt.AlignHCenter | Qt.AlignBottom
                    }
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        setDiscount(.9)
                        closeDiscountEdit()
                        window_root.keyboard_c.closeAll()
                    }
                }
            }
            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_white_pure
                property real discount_ratio: .8
                RowLayout {
                    anchors.fill: parent
                    anchors.bottomMargin: 15
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                    CusText {
                        text: "8"
                        font {
                            bold: true
                            pixelSize: 40 * dpi_ratio
                        }
                        Layout.alignment: Qt.AlignHCenter | Qt.AlignBottom
                    }
                    CusText {
                        text: qsTr("折")
                        Layout.alignment: Qt.AlignHCenter | Qt.AlignBottom
                    }
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        setDiscount(.8)
                        closeDiscountEdit()
                        window_root.keyboard_c.closeAll()
                    }
                }
            }
            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_white_pure
                property real discount_ratio: .7
                RowLayout {
                    anchors.fill: parent
                    anchors.bottomMargin: 15
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                    CusText {
                        text: "7"
                        font {
                            bold: true
                            pixelSize: 40 * dpi_ratio
                        }
                        Layout.alignment: Qt.AlignHCenter | Qt.AlignBottom
                    }
                    CusText {
                        text: qsTr("折")
                        Layout.alignment: Qt.AlignHCenter | Qt.AlignBottom
                    }
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        setDiscount(.7)
                        closeDiscountEdit()
                        window_root.keyboard_c.closeAll()
                    }
                }
            }
            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_white_pure
                property real discount_ratio: .6
                RowLayout {
                    anchors.fill: parent
                    anchors.bottomMargin: 15
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                    CusText {
                        text: "6"
                        font {
                            bold: true
                            pixelSize: 40 * dpi_ratio
                        }
                        Layout.alignment: Qt.AlignHCenter | Qt.AlignBottom
                    }
                    CusText {
                        text: qsTr("折")
                        Layout.alignment: Qt.AlignHCenter | Qt.AlignBottom
                    }
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        setDiscount(.6)
                        closeDiscountEdit()
                        window_root.keyboard_c.closeAll()
                    }
                }
            }
            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_white_pure
                property real discount_ratio: .5
                RowLayout {
                    anchors.fill: parent
                    anchors.bottomMargin: 15
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                    CusText {
                        text: "5"
                        font {
                            bold: true
                            pixelSize: 40 * dpi_ratio
                        }
                        Layout.alignment: Qt.AlignHCenter | Qt.AlignBottom
                    }
                    CusText {
                        text: qsTr("折")
                        Layout.alignment: Qt.AlignHCenter | Qt.AlignBottom
                    }
                    CusSpacer {
                        Layout.fillWidth: true
                    }
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        setDiscount(.5)
                        closeDiscountEdit()
                        window_root.keyboard_c.closeAll()
                    }
                }
            }
            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_white_pure
                property real discount_ratio: 1
                CusText {
                    text: qsTr("不打折")
                    anchors.centerIn: parent
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        setDiscount(1)
                        closeDiscountEdit()
                        window_root.keyboard_c.closeAll()
                    }
                }
            }
            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_white_pure
                TextInput {
                    id: ti_discount
                    anchors.fill: parent
                    anchors.margins: 10 * dpi_ratio

                    validator: RegularExpressionValidator {
                        regularExpression: /^[1-9]$/
                    }

                    property string placeholderText: qsTr("修改折扣")
                    verticalAlignment: TextInput.AlignVCenter | TextInput.AlignHCenter
                    font {
                        pixelSize: 30 * dpi_ratio
                        family: ST.fontFamilyYaHei
                    }

                    onEditingFinished: {
                        setDiscount(Number(text) / 10)
                        closeDiscountEdit()
                    }

                    CusText {
                        text: parent.placeholderText
                        color: ST.color_grey
                        visible: !ti_discount.text
                        anchors.centerIn: parent
                        font.pixelSize: 20 * dpi_ratio
                        font.family: ST.fontFamilyYaHei
                        z: -1
                    }
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        ti_discount.clear()
                        ti_discount.forceActiveFocus()
                        ti_discount.focus = true
                    }
                }
            }
            CusRect {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: ST.color_transparent
                CusRect {
                    anchors.centerIn: parent

                    property real w_or_h_min: Math.min(parent.width, parent.height)

                    width: w_or_h_min
                    height: w_or_h_min
                    radius: w_or_h_min / 2
                    color: ST.color_green
                    CusText {
                        text: qsTr("确认")
                        anchors.centerIn: parent
                        color: ST.color_white_pure
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            if (ti_discount.text != "")
                                setDiscount(Number(ti_discount.text) / 10)
                            closeDiscountEdit()
                            window_root.keyboard_c.closeAll()
                        }
                    }
                }
            }
        }
    }
}
