﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import "../.."

RadioButton {
    id: control
    text: "_name"
    implicitWidth: 120
    implicitHeight: 50
    property alias color: rect_back.color
    property real ratio: 100
    font.family: ST.fontFamilyYaHei
    font.pixelSize: 22 * ST.dpi_ratio
    focusPolicy: Qt.NoFocus

    indicator: CusRect {
        id: rect_back
        implicitWidth: parent.width
        implicitHeight: parent.height
        anchors.verticalCenter: parent.verticalCenter
        radius: ST.radius
        border.width: control.checked ? 3 : 0
        border.color: ST.color_green
        color: ST.color_grey
    }

    contentItem: CusText {
        id: text_content
        z: 1
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.verticalCenter: parent.verticalCenter
        text: control.text + qsTr("折")
        font: control.font
        opacity: control.down ? 1 : .9
        color: control.checked || control.down ? ST.color_green : ST.color_black
        verticalAlignment: Text.AlignVCenter
        horizontalAlignment: Text.AlignHCenter
    }
}
