﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.12
import QtGraphicalEffects 1.0
import "../.."
import ShopCartModel 1.0

CusRect {
    id: rect_shop_cart_input
    property alias text: ti_control.text
    property alias ti_control: ti_control
    property bool is_rmb: false

    property alias normal_keyboard_x: ti_control.normal_keyboard_x
    property alias normal_keyboard_y: ti_control.normal_keyboard_y
    property alias digital_keyboard_x: ti_control.digital_keyboard_x
    property alias digital_keyboard_y: ti_control.digital_keyboard_y

    property alias keyboard_status: ti_control.keyboard_status

    signal accept
    signal editingFinished
    signal clickedSignal

    property string font_color: ST.color_red
    property string regexp_str: ""

    RowLayout {
        anchors.fill: parent
        CusSpacer {
            Layout.fillWidth: true
        }
        CusText {
            text: qsTr("¥")
            visible: is_rmb
            color: font_color
        }
        TextInput {
            id: ti_control
            Layout.alignment: Qt.AlignVCenter
            color: font_color
            font.pixelSize: 22 * dpi_ratio
            font.family: ST.fontFamilyYaHei
            property bool is_clicked_select_all: true
            onEditingFinished: {
                rect_shop_cart_input.editingFinished()
                // deselect()
            }
            onAccepted: {
                rect_shop_cart_input.accept()
                window_root.keyboard_c.closeAll()
            }
            onTextChanged:
            {
            }
            property int keyboard_status: 0 //0 普通键盘  1数字键盘

            property real normal_keyboard_x: -1
            property real normal_keyboard_y: -1

            property real digital_keyboard_x: -1
            property real digital_keyboard_y: -1

            function focusMe() {
                if (is_use_virtual_keyboard) {
                    window_root.keyboard_c.closeAll()
                    window_root.keyboard_c.setNormalKeyboardPos(normal_keyboard_x, normal_keyboard_y)
                    window_root.keyboard_c.setDigitalKeyboardPos(digital_keyboard_x, digital_keyboard_y)
                    if (keyboard_status == 0) {
                        window_root.keyboard_c.openNormalKeyboard()
                    } else if (keyboard_status == 1) {
                        window_root.keyboard_c.openDigitalKeyboard()
                    }
                }
                ti_control.forceActiveFocus()
                ti_control.focus = true
                if (is_clicked_select_all)
                    selectAll()
            }
        }
        CusSpacer {
            Layout.fillWidth: true
        }
    }

    MouseArea {
        anchors.fill: parent
        onClicked: {
            ti_control.focusMe()
            rect_shop_cart_input.clickedSignal()
        }
    }
}
