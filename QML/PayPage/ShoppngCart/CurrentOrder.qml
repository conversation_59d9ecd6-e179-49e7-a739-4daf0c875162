﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.12
import QtGraphicalEffects 1.0
import "../.."
import ShopCartModel 1.0
import SettingEnum 1.0

ColumnLayout {
    property alias t_cart_total_quantity: t_cart_total_quantity
    property alias text_cart_total: text_cart_total
    property alias t_cart_total_no_discount: t_cart_total_no_discount

    property int item_index_: 0
    Timer {
        id: t_focus
        repeat: false
        interval: 50
        onTriggered: {
            lv_shop_cart.positionViewAtIndex(item_index_, ListView.End)
        }
    }

    CusRect {
        Layout.fillWidth: true
        Layout.fillHeight: true
        color: ST.color_transparent

        ColumnLayout {
            anchors.fill: parent

            //标题栏
            Item {
                Layout.fillWidth: true
                Layout.preferredHeight: 50 * dpi_ratio

                RowLayout {
                    anchors.fill: parent
                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        clip: true
                        color: ST.color_transparent
                        CusText {
                            anchors.leftMargin: 15 * dpi_ratio
                            anchors.rightMargin: 15 * dpi_ratio
                            width: parent.width - (anchors.leftMargin + anchors.rightMargin)
                            anchors.centerIn: parent
                            elide: Text.ElideRight
                            text: qsTr("商品名称")
                            color: ST.color_grey_font
                        }
                    }
                    CusRect {
                        Layout.fillHeight: true
                        Layout.preferredWidth: 130 * dpi_ratio
                        clip: true
                        color: ST.color_transparent
                        CusText {
                            anchors.centerIn: parent
                            text: qsTr("单价")
                            color: ST.color_grey_font
                        }
                    }
                    CusRect {
                        Layout.fillHeight: true
                        Layout.preferredWidth: 130 * dpi_ratio
                        clip: true
                        color: ST.color_transparent

                        CusText {
                            anchors.centerIn: parent
                            text: qsTr("数量")
                            color: ST.color_grey_font
                        }
                    }
                    CusRect {
                        Layout.fillHeight: true
                        Layout.preferredWidth: 130 * dpi_ratio
                        clip: true
                        color: ST.color_transparent

                        CusText {
                            anchors.centerIn: parent
                            text: qsTr("小计")
                            color: ST.color_grey_font
                        }
                    }
                    CusRect {
                        color: ST.color_transparent
                        Layout.fillHeight: true
                        Layout.preferredWidth: 50 * dpi_ratio
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.rightMargin: -100 * dpi_ratio //此处为了显示折扣弹窗
                color: ST.color_transparent
                clip: true

                ListView {
                    id: lv_shop_cart
                    anchors.fill: parent
                    anchors.rightMargin: 100 * dpi_ratio
                    //clip: true
                    cacheBuffer: 1000
                    model: ShopCartModel {
                        id: shop_cart_model
                        list: shopCartList //绑定购物车
                    }

                    delegate: ShoppngCartItem {
                        height: (is_editing_unit_price ? 70 + 110 : 70) * dpi_ratio
                        width: lv_shop_cart.width
                    }

                    // function closeDiscountPopup() {
                    //     shop_cart_model.refreshView()
                    // }
                    Connections {
                        target: shopCartList

                        function onFocusItem(item_index) {
                            item_index_ = item_index
                            t_focus.restart()
                        }
                    }
                }
            }
        }
    }

    CusRect {
        Layout.fillWidth: true
        Layout.preferredHeight: 60 * dpi_ratio
        color: ST.color_transparent

        RowLayout {
            anchors.fill: parent

            spacing: 10 * dpi_ratio

            CusSpacer {
                Layout.preferredWidth: 10 * dpi_ratio
            }
            CusButton {
                Layout.preferredWidth: 100 * dpi_ratio
                Layout.preferredHeight: 40 * dpi_ratio
                color: ST.color_gold
                text: qsTr("折扣")

                onClicked: {
                    openOrderDiscount()
                }
                focus: Qt.ClickFocus
            }
            CusSpacer {
                Layout.fillWidth: true
            }

            CusSwitchButton {
                id: switch_is_return
                text: qsTr("是否校验")
                Layout.preferredWidth: 170 * dpi_ratio * configTool.fontRatio
                Layout.preferredHeight: 26 * ST.dpi_ratio * configTool.fontRatio
                Layout.alignment: Qt.AlignVCenter

                Component.onCompleted: {
                    switch_is_return.checked = settingTool.getSetting(SettingEnum.IS_VERIFY_EAN13)
                }

                onClicked: {
                    checked = !checked
                    settingTool.setSetting(SettingEnum.IS_VERIFY_EAN13, checked)
                }
            }

            CusSwitchButton {
                id: switch_is_print
                text: qsTr("是否打印")
                Layout.preferredWidth: 170 * dpi_ratio * configTool.fontRatio
                Layout.preferredHeight: 26 * ST.dpi_ratio * configTool.fontRatio
                Layout.alignment: Qt.AlignVCenter

                onClicked: {
                    checked = !checked
                    printerControl.setIsPrintTicket(checked)
                }

                Component.onCompleted: {
                    checked = printerControl.getIsPrintTicket()
                }
            }
        }
    }
    CusRect {
        Layout.fillWidth: true
        Layout.preferredHeight: 108 * dpi_ratio
        color: ST.color_transparent

        RowLayout {
            anchors.fill: parent
            spacing: 1
            CusShapeButton {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.preferredWidth: 130 * dpi_ratio
                color: ST.color_red

                cornersRadius: [0, 0, 0, ST.radius]
                text: qsTr("清空商品")
                font {
                    pixelSize: 30 * dpi_ratio * configTool.fontRatio
                }
                onClicked: {
                    logMgr.logEvtWarn4Qml("清空商品: {}", shopCartList.getAllGoodsInfo())
                    clearAllGoods()
                }
            }

            CusShapeButton {
                id: rect_cart_calculate_total
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.preferredWidth: 270 * dpi_ratio
                color: ST.color_red
                cornersRadius: [0, 0, ST.radius, 0]
                enabled: false
                CusText {
                    id: t_cart_total_no_discount
                    anchors.left: parent.left
                    anchors.leftMargin: 10 * dpi_ratio
                    anchors.bottom: parent.bottom
                    anchors.bottomMargin: 15 * dpi_ratio
                    property var total_no_discount: "0.00"
                    text: qsTr("¥") + Number(total_no_discount).toFixed(2).toString()
                    color: ST.color_white_pure
                    font.strikeout: true
                    font.pixelSize: 22 * dpi_ratio
                }

                CusText {
                    id: t_cart_total_quantity
                    anchors.left: parent.left
                    anchors.leftMargin: 130 * dpi_ratio

                    anchors.top: parent.top
                    anchors.topMargin: 4 * dpi_ratio

                    font.pixelSize: 35 * dpi_ratio

                    property var quantity: "0"

                    text: qsTr("共") + Number(quantity).toFixed(2).toString() + qsTr("件商品")
                    color: ST.color_white_pure
                }

                CusText {
                    id: text_cart_total
                    color: ST.color_white
                    font.pixelSize: 45 * dpi_ratio
                    anchors.left: parent.left
                    anchors.leftMargin: 150 * dpi_ratio
                    anchors.bottom: parent.bottom
                    anchors.bottomMargin: 4 * dpi_ratio
                    property var total: "0.00"
                    text: qsTr("¥") + total
                }
            }
        }
    }
}
