﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.12
import QtGraphicalEffects 1.0
import "../.."
import ShopCartModel 1.0

ColumnLayout {
    function showPendingOrderDetail(sale_list_unique) {
        lm_pending_order_detail.clear()
        var json_doc = JSON.parse(orderControl.getPendingOrderDetailJson(sale_list_unique))

        if (!json_doc || !json_doc.length)
            return

        for (var i = 0; i < json_doc.length; ++i) {
            lm_pending_order_detail.append(json_doc[i])
        }
    }
    function clearPendingOrderDetail() {
        lm_pending_order_detail.clear()
    }

    CusRect {
        Layout.fillWidth: true
        Layout.fillHeight: true
        color: ST.color_transparent

        RowLayout {
            anchors.fill: parent

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent

                    //标题栏
                    Item {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 50 * dpi_ratio

                        RowLayout {
                            anchors.fill: parent
                            Item {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 100
                                clip: true

                                CusText {
                                    anchors.leftMargin: 15 * dpi_ratio
                                    anchors.rightMargin: 15 * dpi_ratio
                                    width: parent.width - (anchors.leftMargin + anchors.rightMargin)
                                    anchors.centerIn: parent
                                    elide: Text.ElideRight
                                    text: qsTr("商品名称")
                                    color: ST.color_grey_font
                                }
                            }
                            Item {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 50
                                clip: true
                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("单价")
                                    color: ST.color_grey_font
                                }
                            }
                            Item {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 50
                                clip: true

                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("数量")
                                    color: ST.color_grey_font
                                }
                            }
                            Item {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 50
                                clip: true

                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("小计")
                                    color: ST.color_grey_font
                                }
                            }
                        }
                    }

                    ListView {
                        id: lv_pending_order
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true

                        model: ListModel {
                            id: lm_pending_order_detail
                        }

                        delegate: CusRect {
                            height: 70 * dpi_ratio
                            width: lv_pending_order.width
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                Item {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 100
                                    clip: true

                                    CusText {
                                        anchors.leftMargin: 15
                                        anchors.rightMargin: 15
                                        width: parent.width - (anchors.leftMargin + anchors.rightMargin)
                                        anchors.centerIn: parent
                                        elide: Text.ElideRight
                                        text: goods_name //商品名称
                                        color: ST.color_grey_font
                                    }
                                }
                                Item {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 50
                                    clip: true
                                    CusText {
                                        anchors.centerIn: parent
                                        text: Number(goods_price).toFixed(2) //"单价"
                                        color: ST.color_grey_font
                                    }
                                }
                                Item {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 50
                                    clip: true

                                    CusText {
                                        anchors.centerIn: parent
                                        text: goods_num //"数量"
                                        color: ST.color_grey_font
                                    }
                                }
                                Item {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 50
                                    clip: true

                                    CusText {
                                        anchors.centerIn: parent
                                        text: Number(goods_subtotal).toFixed(2) // "小计"
                                        color: ST.color_grey_font
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
