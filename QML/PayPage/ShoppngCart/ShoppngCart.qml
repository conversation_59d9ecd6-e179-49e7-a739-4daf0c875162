﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.12
import QtGraphicalEffects 1.15
import "../.."
import "../../.."
import "../../PayPage/PayMethods"
import ShopCartModel 1.0
import EnumTool 1.0
import SettingEnum 1.0

CusRect {
    id: rect_shop_cart
    radius: ST.radius
    color: ST.color_white

    property alias pending_order_detail: pending_order_detail
    property int page_index: EnumTool.SHOP_CART_TAB__MAIN_ORDER

    function addGoodsByBarcode(goods_barcode) {
        shopCartList.appendItemByBarcode(goods_barcode)
    }
    function addNoCodeGoodsByBarcode(goods_barcode, price, num) {
        shopCartList.appendNoCodeItemByBarcode(goods_barcode, price, num)
    }

    function clearGoodsByBarcode(goods_barcode, date_time) {
        shopCartList.clearGoodsByBarcode(goods_barcode, date_time)
    }

    function clearAllGoods() {
        shopCartList.clearAllGoods()
        shopCartList.setOrderDiscount(1)
    }

    function openOrderDiscount() {
        window_root.loader_4_order_discount.sourceComponent = compo_order_discount
        window_root.loader_4_order_discount.item.open()
        logMgr.logEvtInfo4Qml("打开整单折扣")
    }

    function reFreshCartCalculateTotal() {

        current_order.t_cart_total_quantity.quantity = shopCartList.getTotalGoodsQuantity()
        current_order.text_cart_total.total = utils4Qml.floorDecimal(shopCartList.getTotalGoodsPrice())

        //订单促销
        var orderJian = 0
        var giftGoodNum = 0
        var giftGoodId = 0
//        logMgr.logDataInfo4Qml("订单满减满赠是否开启:{}",configTool.getSetting(SettingEnum.ConfigEnum.IS_ORDER_MANJIANMANZENG))
        if(configTool.getSetting(SettingEnum.ConfigEnum.IS_ORDER_MANJIANMANZENG)){
            var totalTemp = utils4Qml.floorDecimal(shopCartList.getTotalGoodsPrice())
//            logMgr.logDataInfo4Qml("==开始判断购物车是否满足订单促销活动 购物车满赠商品添加== 购物车总价为：{}",totalTemp)
            var oreder_promotion = settingTool.getSetting(SettingEnum.ORDER_PROMOTION_INFO)
            if(oreder_promotion.length >5 ){
                var orderPromotion = oreder_promotion.split("#")
                for (var m = 0; m < orderPromotion.length; m++)
                {
                    var discountData = orderPromotion[m].split("^");

                    for(var i = 0 ;i < discountData.length; i++){
//                        logMgr.logDataInfo4Qml("discountData[{}]:{}",i,discountData[i])
                    }
                    //使用符合促销活动的满减最大值
                    for(var h=0;h<3;h++)
                    {
//                        logMgr.logDataInfo4Qml("totalTemp:{};discountData[h]:{}",totalTemp,discountData[h])
                        if(totalTemp*1 >= discountData[h]*1 ){
//                            logMgr.logDataInfo4Qml("orderJian:{};discountData[h]:{}",orderJian,discountData[h+3])
                            if(orderJian*1 < discountData[h+3]*1){
                               orderJian = discountData[h+3]
                               giftGoodId = discountData[h+6]
                               giftGoodNum = discountData[h+9]
//                               logMgr.logDataInfo4Qml("===giftGoodId:{};giftGoodNum:{}",giftGoodId,giftGoodNum)
                            }
                        }
                    }
                }
            }
        }
        if(giftGoodNum !== 0){
//            logMgr.logDataInfo4Qml("订单满足赠送条件 开始添加满赠商品")

            var giftNumTemp =",,,,,,,,,,," + giftGoodNum;
            var goods_mgr = JSON.parse(goodsManager.getGoodsByGoodId4Qml(giftGoodId))
        }else{
//            logMgr.logDataInfo4Qml("订单不满足赠送条件！")
        }

        current_order.t_cart_total_no_discount.total_no_discount = shopCartList.getTotalGoodsPriceNoDiscount()
    }

    // property string cur_cart_item_timestamp: ""

    function backToMainOrder() {
        page_index = EnumTool.SHOP_CART_TAB__MAIN_ORDER
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        RowLayout {
            Layout.fillWidth: true
            property int height_btn: 60 * dpi_ratio
            spacing: 0

            CusRadioBtnRectManual {
                id: rb_zhudan
                control_text.text: qsTr("主单")
                property int index: 0
                Layout.preferredWidth: 100 * dpi_ratio
                Layout.fillWidth: true
                Layout.preferredHeight: 60 * dpi_ratio
                checked: page_index == EnumTool.SHOP_CART_TAB__MAIN_ORDER
                borderWidth: 0
                borderColor: ST.color_transparent
                cornersRadius: [ST.radius, 0, 0, 0]
                onClicked: {
                    page_index = EnumTool.SHOP_CART_TAB__MAIN_ORDER
                    goods_tabbar.showDefaultGoodsTabBar()
                }
            }

            CusRadioBtnRectManual {
                control_text.text: qsTr("挂单")
                property int index: 1
                Layout.preferredWidth: 100 * dpi_ratio
                Layout.fillWidth: true
                Layout.preferredHeight: 60 * dpi_ratio
                checked: page_index == EnumTool.SHOP_CART_TAB__PENDING_ORDER
                borderWidth: 0
                borderColor: ST.color_transparent
                cornersRadius: [0, 0, 0, 0]
                onClicked: {
                    page_index = EnumTool.SHOP_CART_TAB__PENDING_ORDER
                    orderControl.addPendingOrderByShopCart()
                    clearCurMemberInfo()
                    goods_tabbar.showPendingOrder()
                }
            }

            CusShapeButton {
                id: btn_last_order_price
                property string price: ""
                Layout.preferredHeight: 60 * dpi_ratio
                Layout.preferredWidth: 100 * dpi_ratio
                Layout.fillWidth: true
                text: price == "" ? qsTr("上一单") : qsTr("上一单 ") + price + qsTr(" 元")

                cornersRadius: [0, ST.radius, 0, 0]
                color: ST.color_orange
                onClicked: {
                    if(payMethodControl.getNetStatus() === "101"){
                        if (!permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L1_QUERY)) {
                            toast.openWarn("无此权限")
                            return
                        }
                        window_root.compo_index = EnumTool.PAGE_QUERY

                        window_root.compo_index = EnumTool.PAGE_QUERY
                        loader_query_page.item.cur_query_page = EnumTool.QUERY_SALES_RECORD
                    }
                    else{
                        toast.openWarn("离线状态下该功能不可用！");
                    }
                }
            }

            Connections {
                target: orderControl
                function onSigBackupOrderFinished() {
                    btn_last_order_price.price = orderControl.doubleToString(orderControl.getBackupOrderPrice())
                }
            }
        }
        Connections {
            target: shopControl
            function onLoggedIn() {
                //重新登录后清除上一单信息
                btn_last_order_price.price = ""
            }
        }
        CusRect {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_white_pure
            radius: ST.radius

            CurrentOrder {
                id: current_order
                anchors.fill: parent
                visible: page_index == EnumTool.SHOP_CART_TAB__MAIN_ORDER
            }

            PopupVipCardPay {
                id: popup_vip_cardpay
            }
            PendingOrderDetail {
                id: pending_order_detail
                anchors.fill: parent
                visible: page_index == EnumTool.SHOP_CART_TAB__PENDING_ORDER
            }
        }
    }

    Component {
        id: compo_order_discount

        OrderDiscount {
            onSigOpen: {
                keyboard_c.digital_keyboard.x = 730 * dpi_ratio
                keyboard_c.digital_keyboard.y = 580 * dpi_ratio
            }
            onSigClose: {
                keyboard_c.closeAll()
                search_bar.forceActiveFocus()
            }
        }
    }

    ///-------------------------------------------| 当购物车变更后刷新统计 |-------------------------------------------
    Connections {
        target: shopCartList
        function onPostItemReset() {
            reFreshCartCalculateTotal()
            // syncCurCartItem()
        }
        function onPostItemRemoved() {
            reFreshCartCalculateTotal()
            // syncCurCartItem()
        }
        function onPostItemAppended() {
            reFreshCartCalculateTotal()
            // syncCurCartItem()
        }
        function onPostRowChanged(row) {
            reFreshCartCalculateTotal()
            // syncCurCartItem()
        }
        function onRefreshOrderTotal() {
            reFreshCartCalculateTotal()
            // syncCurCartItem()
        }
        // function onSigRefreshCurItemTimestamp() {
        //     // syncCurCartItem()
        // }
    }
    ///-------------------------------------------| 当购物车变更后刷新统计 |-------------------------------------------
}
