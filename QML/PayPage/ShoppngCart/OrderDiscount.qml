﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."

Item {
    id: popup_root

    function open() {
        popup_root.visible = true
        keyboard_c.closeAll()
        sigOpen()
        calcOrderTotalPrice()
    }
    function close() {
        popup_root.visible = false
        sigClose()
    }

    signal sigOpen
    signal sigClose

    property string title_name: qsTr("整单折扣")
    property alias order_discount: tf_discount.text //订单折扣
    property string order_total_prices

    //订单价格
    property bool is_cus_order_price: false

    onOrder_discountChanged: {
        //is_cus_order_price = false
        calcOrderTotalPrice()
    }

    function commitOperation() {
        if (is_cus_order_price) {
            shopCartList.setCusOrderPrice(Number(tf_total_prices.text))
        } else {
            shopCartList.setOrderDiscount(Number(order_discount) / 100)
        }
    }

    function calcOrderTotalPrice() {
        order_total_prices = Number(order_discount) / 100 * shopCartList.getTotalGoodsPriceNoDiscount()
    }

    onVisibleChanged: {
        rb_discount_no_discount.checked = false
        rb_discount_no_discount.checked = true
    }

    ButtonGroup {
        id: btn_g_discount
        //        buttons: gl_discount_btns.children
    }

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 700 * dpi_ratio
        height: 570 * dpi_ratio
        x: (parent.width - width) / 2
        y: 10 * dpi_ratio

        color: ST.color_white_pure
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        //        onVisibleChanged: {
        //            if (visible) {
        //                x = (parent.width - width) / 2
        //                y = (parent.height - height) / 2
        //            }
        //        }
        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35 * dpi_ratio
                color: ST.color_white_pure

                ColumnLayout {
                    anchors.fill: parent

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 240 * dpi_ratio
                        color: ST.color_transparent

                        GridLayout {
                            id: gl_discount_btns
                            anchors.fill: parent
                            columns: 5
                            rows: 2

                            columnSpacing: 10 * dpi_ratio
                            rowSpacing: 10 * dpi_ratio

                            property double col_width: (width - (columns - 1) * rowSpacing) / columns
                            property double col_height: (height - (rows - 1) * columnSpacing) / rows

                            function prefWidth(item) {
                                return col_width * item.Layout.columnSpan
                            }
                            function prefHeight(item) {
                                return col_height * item.Layout.rowSpan
                            }

                            RadioBtnRectDiscount {
                                text: "99"

                                Layout.columnSpan: 1
                                Layout.rowSpan: 1
                                Layout.preferredWidth: gl_discount_btns.prefWidth(this)
                                Layout.preferredHeight: gl_discount_btns.prefHeight(this)
                                Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                ButtonGroup.group: btn_g_discount
                                onClicked: {
                                    order_discount = 99
                                    is_cus_order_price = false
                                }
                            }
                            RadioBtnRectDiscount {
                                text: "98"
                                Layout.columnSpan: 1
                                Layout.rowSpan: 1
                                Layout.preferredWidth: gl_discount_btns.prefWidth(this)
                                Layout.preferredHeight: gl_discount_btns.prefHeight(this)
                                Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                ButtonGroup.group: btn_g_discount
                                onClicked: {
                                    order_discount = 98
                                    is_cus_order_price = false
                                }
                            }
                            RadioBtnRectDiscount {
                                text: "95"
                                Layout.columnSpan: 1
                                Layout.rowSpan: 1
                                Layout.preferredWidth: gl_discount_btns.prefWidth(this)
                                Layout.preferredHeight: gl_discount_btns.prefHeight(this)
                                Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                ButtonGroup.group: btn_g_discount
                                onClicked: {
                                    order_discount = 95
                                    is_cus_order_price = false
                                }
                            }
                            RadioBtnRectDiscount {
                                text: "89.5"
                                Layout.columnSpan: 1
                                Layout.rowSpan: 1
                                Layout.preferredWidth: gl_discount_btns.prefWidth(this)
                                Layout.preferredHeight: gl_discount_btns.prefHeight(this)
                                Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                ButtonGroup.group: btn_g_discount
                                onClicked: {
                                    order_discount = 89.5
                                    is_cus_order_price = false
                                }
                            }
                            RadioBtnRectDiscount {
                                text: "85"
                                Layout.columnSpan: 1
                                Layout.rowSpan: 1
                                Layout.preferredWidth: gl_discount_btns.prefWidth(this)
                                Layout.preferredHeight: gl_discount_btns.prefHeight(this)
                                Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                ButtonGroup.group: btn_g_discount
                                onClicked: {
                                    order_discount = 85
                                    is_cus_order_price = false
                                }
                            }
                            RadioBtnRectDiscount {
                                text: "80"
                                Layout.columnSpan: 1
                                Layout.rowSpan: 1
                                Layout.preferredWidth: gl_discount_btns.prefWidth(this)
                                Layout.preferredHeight: gl_discount_btns.prefHeight(this)
                                Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                ButtonGroup.group: btn_g_discount
                                onClicked: {
                                    order_discount = 80
                                    is_cus_order_price = false
                                }
                            }
                            RadioBtnRectDiscount {
                                text: "70"
                                Layout.columnSpan: 1
                                Layout.rowSpan: 1
                                Layout.preferredWidth: gl_discount_btns.prefWidth(this)
                                Layout.preferredHeight: gl_discount_btns.prefHeight(this)
                                Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                ButtonGroup.group: btn_g_discount
                                onClicked: {
                                    order_discount = 70
                                    is_cus_order_price = false
                                }
                            }
                            RadioBtnRectDiscount {
                                text: "60"
                                Layout.columnSpan: 1
                                Layout.rowSpan: 1
                                Layout.preferredWidth: gl_discount_btns.prefWidth(this)
                                Layout.preferredHeight: gl_discount_btns.prefHeight(this)
                                Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                ButtonGroup.group: btn_g_discount
                                onClicked: {
                                    order_discount = 60
                                    is_cus_order_price = false
                                }
                            }
                            RadioBtnRectDiscount {
                                text: "50"
                                Layout.columnSpan: 1
                                Layout.rowSpan: 1
                                Layout.preferredWidth: gl_discount_btns.prefWidth(this)
                                Layout.preferredHeight: gl_discount_btns.prefHeight(this)
                                Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                ButtonGroup.group: btn_g_discount
                                onClicked: {
                                    order_discount = 50
                                    is_cus_order_price = false
                                }
                            }
                            RadioBtnRectDiscount {
                                id: rb_discount_no_discount
                                text: qsTr("无")
                                Layout.columnSpan: 1
                                Layout.rowSpan: 1
                                Layout.preferredWidth: gl_discount_btns.prefWidth(this)
                                Layout.preferredHeight: gl_discount_btns.prefHeight(this)
                                Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                ButtonGroup.group: btn_g_discount
                                //                                checked: true
                                onCheckedChanged: {
                                    if (checked) {
                                        order_discount = 100
                                        is_cus_order_price = false
                                        calcOrderTotalPrice()
                                    }
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        ColumnLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent
                                    anchors.topMargin: 15 * dpi_ratio
                                    anchors.bottomMargin: 15 * dpi_ratio
                                    CusText {
                                        text: qsTr("折扣: ")
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 80 * dpi_ratio
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                    CusTextField {
                                        id: tf_discount
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        placeholderText: qsTr("修改产品折扣1~100之间")
                                        RegularExpressionValidator {
                                            regularExpression: /([1-9]|[1-9][0-9]|100)/
                                        }
                                        keyboard_status: 1
                                        is_clicked_select_all: true

                                        digital_keyboard_x: 1330 * dpi_ratio
                                        digital_keyboard_y: 380 * dpi_ratio
                                    }
                                    CusButton {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 200 * dpi_ratio
                                        text: qsTr("抹零")
                                        onClicked: {
                                            rb_discount_no_discount.checked = false
                                            rb_discount_no_discount.checked = true
                                            btn_g_discount.checkedButton = null

                                            let order_discount_tmp = Math.floor(Number(shopCartList.getTotalGoodsPriceNoDiscount()))
                                            order_total_prices = order_discount_tmp.toString()

                                            is_cus_order_price = true
                                        }
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    anchors.topMargin: 15 * dpi_ratio
                                    anchors.bottomMargin: 15 * dpi_ratio
                                    CusText {
                                        text: qsTr("总价: ")
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 80 * dpi_ratio
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                    CusTextField {
                                        id: tf_total_prices
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        text: Number(order_total_prices).toFixed(2).toString()
                                        keyboard_status: 1
                                        is_clicked_select_all: true

                                        digital_keyboard_x: 1330 * dpi_ratio
                                        digital_keyboard_y: 380 * dpi_ratio

                                        validator: RegularExpressionValidator {
                                            regularExpression: /^\d+(\.\d+)?$/
                                        }
                                        onEditingFinished: {
                                            order_total_prices = text
                                            is_cus_order_price = true
                                        }
                                    }
                                    CusButton {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: 200 * dpi_ratio
                                        text: qsTr("确认")
                                        focusPolicy: Qt.ClickFocus

                                        onClicked: {
                                            commitOperation()
                                            close()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    Component.onCompleted: {
        rb_discount_no_discount.checked = true
    }
}
