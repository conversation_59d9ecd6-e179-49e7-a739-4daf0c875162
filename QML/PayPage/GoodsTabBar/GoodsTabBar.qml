﻿import QtQuick 2.12
import QtQuick.Layouts 1.12
import QtQuick.Controls 2.12
import QtQuick.Window 2.12
import QtGraphicalEffects 1.0
import "../.."

import EnumTool 1.0

CusRect {
    radius: ST.radius
    clip: true
    color: ST.color_transparent

    property alias lm_virtual_goods_tab_bar: lm_virtual_goods_tab_bar

    function refreshGoodsKind() {
        refreshVirtualGoodsKind()
    }

    function refreshVirtualGoodsKind() {
        lm_virtual_goods_tab_bar.clear()
        virtualGoodsKindMgr.getVirualGoodsKind(function (data) {

            lm_virtual_goods_tab_bar.clear()

            var json_doc = JSON.parse(data)
            if (json_doc) {
                for (var goods_kind_i = 0; goods_kind_i < json_doc.length; ++goods_kind_i) {
                    var cur_goods_kind = json_doc[goods_kind_i]
                    if (cur_goods_kind.goods_kind_unique == 99990) {
                        lm_virtual_goods_tab_bar.insert(0, cur_goods_kind)
                    } else {
                        lm_virtual_goods_tab_bar.append(cur_goods_kind)
                    }
                }
            }

            lm_virtual_goods_tab_bar.append({
                                                "goods_kind_unique": EnumTool.CUS_GOODS_KIND_ALL_GOODS,
                                                "goods_kind_name": qsTr("全部"),
                                                "goodsKindInventedId": 0
                                            })

            if (configTool.isUseRecognition) {
                lm_virtual_goods_tab_bar.append({
                                                    "goods_kind_unique": EnumTool.CUS_GOODS_KIND_RECOGNITION,
                                                    "goods_kind_name": qsTr("识别"),
                                                    "goodsKindInventedId": EnumTool.CUS_GOODS_KIND_RECOGNITION
                                                })
            }

            showDefaultGoodsTabBar()
        })
    }

    // Component.onCompleted: {
    //     refreshVirtualGoodsKind()
    // }

    function isExistCusKind() {
        for (var i = 0; i < lm_virtual_goods_tab_bar.count; ++i) {
            if (lm_virtual_goods_tab_bar.get(i).goods_kind_unique == EnumTool.CUS_GOODS_KIND_PENDING_ORDER //\
                    || lm_virtual_goods_tab_bar.get(i).goods_kind_unique == EnumTool.CUS_GOODS_KIND_MEMBER_SHIP) {
                return true
            }
        }
        return false
    }

    // 移除自定义分类
    function eraserCusKind() {
        for (var i = 0; i < lm_virtual_goods_tab_bar.count; ++i) {
            if (lm_virtual_goods_tab_bar.get(i).goods_kind_unique == EnumTool.CUS_GOODS_KIND_PENDING_ORDER) {
                lm_virtual_goods_tab_bar.remove(i)
                break
            }
            if (lm_virtual_goods_tab_bar.get(i).goods_kind_unique == EnumTool.CUS_GOODS_KIND_MEMBER_SHIP) {
                lm_virtual_goods_tab_bar.remove(i)
                break
            }
        }
    }

    function eraserRecogitionKind() {
        for (var i = 0; i < lm_virtual_goods_tab_bar.count; ++i) {
            if (lm_virtual_goods_tab_bar.get(i).goods_kind_unique == EnumTool.CUS_GOODS_KIND_RECOGNITION) {
                lm_virtual_goods_tab_bar.remove(i)
                break
            }
        }
    }

    //Connections {
    //    target: cameraControl
    //    function onIsGoodsRecognitionChanged() {
    //        if (cameraControl.isGoodsRecognition) {
    //            tryShowRecognition()
    //        } else {
    //            eraserRecogitionKind()
    //            showDefaultGoodsTabBar()
    //        }
    //    }
    //}
    function disableCusModel() {
        if (is_member_ship_model)
            is_member_ship_model = false
    }

    function showDefaultGoodsTabBar() {
        disableCusModel()
        if (lm_virtual_goods_tab_bar.count > 0) {
            var cur_item = lm_virtual_goods_tab_bar.get(0)
            logMgr.logEvtInfo4Qml("cur_item.goods_kind_unique:{}cur_item.goodsKindInventedId:{}",cur_item.goods_kind_unique,cur_item.goodsKindInventedId)
            right_side.cur_virtual_goods_kind_unique = cur_item.goods_kind_unique
            right_side.cur_virtual_goods_kind_id = cur_item.goodsKindInventedId
        }
    }

    function showPendingOrder() {
        eraserCusKind()

        lm_virtual_goods_tab_bar.append({
                                            "goods_kind_unique": EnumTool.CUS_GOODS_KIND_PENDING_ORDER,
                                            "goods_kind_name": qsTr("挂单"),
                                            "goodsKindInventedId": 0
                                        })

        right_side.cur_virtual_goods_kind_unique = EnumTool.CUS_GOODS_KIND_PENDING_ORDER
    }

    function showMemberShip() {
        eraserCusKind()

        lm_virtual_goods_tab_bar.append({
                                            "goods_kind_unique": EnumTool.CUS_GOODS_KIND_MEMBER_SHIP,
                                            "goods_kind_name": qsTr("会员"),
                                            "goodsKindInventedId": 0
                                        })
        right_side.cur_virtual_goods_kind_unique = EnumTool.CUS_GOODS_KIND_MEMBER_SHIP
    }

    function tryShowRecognition() {
        for (var i = 0; i < lm_virtual_goods_tab_bar.count; ++i) {
            var cur_item = lm_virtual_goods_tab_bar.get(i)
            if (cur_item.goods_kind_unique == EnumTool.CUS_GOODS_KIND_RECOGNITION) {
                return
            }
        }
        showRecognition()
    }

    function showRecognition() {
        eraserCusKind()

        //        lm_virtual_goods_tab_bar.append({
        //                                            "goods_kind_unique": EnumTool.CUS_GOODS_KIND_RECOGNITION,
        //                                            "goods_kind_name": "识别",
        //                                            "goodsKindInventedId": EnumTool.CUS_GOODS_KIND_RECOGNITION
        //                                        })
        right_side.cur_virtual_goods_kind_unique = EnumTool.CUS_GOODS_KIND_RECOGNITION
        right_side.cur_virtual_goods_kind_id = EnumTool.CUS_GOODS_KIND_RECOGNITION
    }

    //    Connections {
    //        target: cameraControl
    //        function onSigRecognitionChange() {
    //            tryShowRecognition()
    //        }
    //    }
    function setSearchStr(search_str) {
        eraserCusKind()
        right_side.cur_virtual_goods_kind_unique = EnumTool.CUS_GOODS_KIND_ALL_GOODS
        goods_list_4_all.model_goods_data.search_str = search_str
    }

    function setVGoodsKindUnique(v_goods_kind_unique) {
        eraserCusKind()
        disableCusModel()
        goodsControl.sendRefreshSig()

        if (goods_list_4_all.model_goods_data.v_goods_kind_unique != v_goods_kind_unique) {
            goods_list_4_all.model_goods_data.v_goods_kind_unique = v_goods_kind_unique
            logMgr.logEvtInfo4Qml("goods_list_4_all.model_goods_data.v_goods_kind_unique:{}",goods_list_4_all.model_goods_data.v_goods_kind_unique)
        }
    }

    GridView {
        id: gv_virtual_goods_tab_bar
        anchors.fill: parent
        property int column_num: 7
        cellWidth: width / column_num
        cellHeight: 72 * dpi_ratio

        model: ListModel {
            id: lm_virtual_goods_tab_bar
        }
        flow: GridView.LeftToRight

        delegate: CusRect {
            id: radio_btn_root
            width: gv_virtual_goods_tab_bar.cellWidth
            height: gv_virtual_goods_tab_bar.cellHeight
            color: ST.color_transparent

            CusRadioBtnRectManual {
                anchors.fill: parent
                anchors.margins: 1 * dpi_ratio
                control_text.font.pixelSize: 26 * dpi_ratio * configTool.fontRatio
                text: goods_kind_name
                checked: right_side.cur_virtual_goods_kind_unique == goods_kind_unique
                onClicked: {
                    right_side.cur_virtual_goods_kind_unique = goods_kind_unique
                    right_side.cur_virtual_goods_kind_id = goodsKindInventedId
                }
            }

            function showDelVirtualKindConfirm() {
                window_root.loader_4_del_virtual_goods_kind.sourceComponent = compo_del_virtual_goods_kind
                window_root.loader_4_del_virtual_goods_kind.item.open()
            }

            function reqDelCurVirtualKind() {
                goodsControl.reqDelVirualGoodsKind4Qml(function (is_succ, data) {
                    if (is_succ) {
                        toast.openInfo(qsTr("删除成功"))
                        goodsControl.reqGetVirualGoodsKindAndSave4Qml(function (is_succ, data) {
                            if (is_succ) {
                                refreshGoodsKind()
                            } else {
                                toast.openWarn(qsTr("更新列表失败"))
                            }
                        })
                    } else {
                        toast.openWarn(qsTr("删除失败"))
                    }
                }, goods_kind_unique)
            }

            Component {
                id: compo_del_virtual_goods_kind
                PopupConfirm {
                    title_name: qsTr("删除分类")
                    message_info: qsTr("确定要删除此分类吗?")
                    onConfirm: {
                        radio_btn_root.reqDelCurVirtualKind()
                    }
                }
            }

            CusRect {
                anchors.fill: parent
                color: ST.color_black
                visible: is_mamager_model
                opacity: .4
                Image {
                    source: "/Images/close_image.png"
                    anchors.centerIn: parent
                    width: 40 * dpi_ratio
                    height: width
                    visible: !(goodsKindInventedId == 0 || goods_kind_unique == 99990)
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        if (goodsKindInventedId == 0 || goods_kind_unique == 99990) {
                            return
                        }
                        radio_btn_root.showDelVirtualKindConfirm()
                    }
                }
            }
        }
    }

    Connections {
        target: goodsControl
        function onSigInitGoodsList() {
            refreshVirtualGoodsKind()
            goodsControl.sendRefreshSig()
        }
    }

    Connections {
        target: goodsManager
        function onSigIsShowNoCodeGoodsChanged() {
            goods_tabbar.showDefaultGoodsTabBar()
        }
        function onIsSwapNoCodeGoodsChanged() {
            goods_tabbar.showDefaultGoodsTabBar()
        }
    }
}
