﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
//import "../Common"
import EnumTool 1.0
import ".."

Popup {
    id: popup_root
    modal: true
    focus: false
    dim: false
    clip: true
    closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
    signal payPageInit

    background: CusRect {
        color: ST.color_black
        opacity: .8
        MouseArea {
            anchors.fill: parent
            onClicked: {
                popup_root.close()
            }
        }
    }
    Timer {
        id: timer
        interval: 2000
        repeat: false
        onTriggered: popClose()
    }
    function popClose() {

        back_rect.visible = false
        text_info.visible = false
        text_info.text = ""
        back_rect.color = ST.color_white_pure
        text_info.color = ST.color_white_pure
    }
    function openWarn(text, time_imterval = 2000) {
        back_rect.visible = true
        text_info.visible = true
        back_rect.color = ST.color_orange
        if (text_info.text == text) {
            timer.interval = time_imterval
            timer.restart()
        } else {
            text_info.text = text
            timer.interval = time_imterval
            timer.running = true
        }
    }
    Component.onCompleted: {
        list_model1.append({
                               "btn_name": qsTr("收银"),
                               "color": ST.color_green,
                               "index": EnumTool.PAGE_PAY
                           })
        list_model1.append({
                               "btn_name": qsTr("查询"),
                               "color": ST.color_red,
                               "index": EnumTool.PAGE_QUERY
                           })
        list_model1.append({
                               "btn_name": qsTr("网单"),
                               "color": ST.color_blue,
                               "index": EnumTool.PAGE_NET_ORDER
                           })
        list_model1.append({
                               "btn_name": qsTr("设置"),
                               "color": ST.color_gold,
                               "index": EnumTool.PAGE_SETTING
                           })
        list_model1.append({
                               "btn_name": qsTr("入库"),
                               "color": ST.color_red,
                               "index": EnumTool.PAGE_GODOWN_OLD
                               // "index": EnumTool.PAGE_GODOWN
                           })
        list_model1.append({
                               "btn_name": qsTr("会员"),
                               "color": ST.color_gold,
                               "index": EnumTool.PAGE_MEMBERSHIP
                           })
        list_model1.append({
                               "btn_name": qsTr("统计"),
                               "color": ST.color_green,
                               "index": EnumTool.PAGE_STATISTICS
                           })
        list_model1.append({
                               "btn_name": qsTr("交班"),
                               "color": ST.color_red,
                               "index": EnumTool.PAGE_HANDOVER
                           })
    }

    function checkPermission(action) {
        return permissionControl.isHaveAccess(EnumTool.ACCESS_DELETE_GOODS)
    }
    Item {
        id: pop_root
        width: 1400 * dpi_ratio
        height: 600 * dpi_ratio
        anchors.centerIn: parent
        Rectangle {
            id: back_rect
            visible:false
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 5 * dpi_ratio
            width: (text_info.text.length)*22  > 400 ?(text_info.text.length)*(24 * dpi_ratio * configTool.fontRatio) :400 * dpi_ratio * configTool.fontRatio
            height: 50 * dpi_ratio * configTool.fontRatio
            color: ST.color_green
            radius: ST.radius
            opacity: .9
            border {
                width: 2 * dpi_ratio
                color: ST.color_white_pure
            }
        }
        CusText {
            id: text_info
            visible: false
            anchors.centerIn: back_rect
            color: ST.color_white_pure
        }
        GridView {
            id: gv_switch
            anchors.fill: parent
            anchors.margins: 20 * dpi_ratio
            property int row_num: 4
            property int column_num: 2
            cellWidth: width / row_num
            cellHeight: height / column_num
            model: ListModel {
                id: list_model1
            }
            flow: GridView.LeftToRight
            interactive: false

            delegate: CusRect {
                width: gv_switch.cellWidth
                height: gv_switch.cellHeight
                color: ST.color_transparent

                CusButton {
                    anchors.centerIn: parent
                    color: model.color
                    width: 185 * dpi_ratio * configTool.fontRatio2
                    height: width
                    font.pixelSize: text.length > 3 ?25 * dpi_ratio * configTool.fontRatio2:60 * dpi_ratio * configTool.fontRatio2
                    text: btn_name
                    onClicked: {
                        var is_have_permission = false
                        if(payMethodControl.getNetStatus() !== "101"){
                            if (index !== EnumTool.PAGE_PAY && index !== EnumTool.PAGE_HANDOVER ){
                                openWarn(qsTr("离线状态下该功能不可用！"))
                                return
                            }
                        }
                        switch (index) {
                        case EnumTool.PAGE_PAY:
                            payPageInit()
                            is_have_permission = true
                            break
                        case EnumTool.PAGE_QUERY:
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L1_QUERY)) {
                                is_have_permission = true
                            } else {
                                toast.openWarn(qsTr("无此权限"))
                            }
                            break
                        case EnumTool.PAGE_NET_ORDER:
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L1_NET_ORDER)) {
                                is_have_permission = true
                            } else {
                                toast.openWarn(qsTr("无此权限"))
                            }
                            break
                        case EnumTool.PAGE_GODOWN:
                        case EnumTool.PAGE_GODOWN_OLD:
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L1_GODOWN)) {
                                is_have_permission = true
                            } else {
                                toast.openWarn(qsTr("无此权限"))
                            }
                            break
                        case EnumTool.PAGE_SETTING:
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L1_SETTING)) {
                                is_have_permission = true
                            } else {
                                toast.openWarn(qsTr("无此权限"))
                            }
                            break
                        case EnumTool.PAGE_HANDOVER:
                            is_have_permission = true
                            break
                        case EnumTool.PAGE_STATISTICS:
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L1_STATISTIC)) {
                                is_have_permission = true
                            } else {
                                toast.openWarn(qsTr("无此权限"))
                            }
                            break
                        case EnumTool.PAGE_MEMBERSHIP:
                            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L1_MEMBER)) {
                                is_have_permission = true
                            } else {
                                toast.openWarn(qsTr("无此权限"))
                            }
                            break
                        }
                        if (is_have_permission) {
                            if (window_root.compo_index !== index)
                                window_root.compo_index = index
                        }
                        popup_root.close()
                    }
                }
            }
        }
    }
}
