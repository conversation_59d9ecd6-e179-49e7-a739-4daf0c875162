﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0

import ".."

Item {
    id: popup_root

    function open() {
        popup_root.visible = true
    }
    function close() {
        popup_root.visible = false
    }

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                popup_root.close()
            }
        }
    }

    CusRect {
        width: 500 * ST.dpi_ratio * configTool.fontRatio2
        height: (630 - 190) * ST.dpi_ratio * configTool.fontRatio2
        color: ST.color_white_pure
        radius: ST.radius
        y: (parent.height - height) / 2
        x: (parent.width - width) / 2

        MouseArea {
            anchors.fill: parent
        }

        CusRect {
            anchors.fill: parent
            anchors.margins: 35 * ST.dpi_ratio
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                CusRect {
                    Layout.preferredHeight: 60 * ST.dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        spacing: 20 * ST.dpi_ratio

                        Image {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 150 * ST.dpi_ratio
                            source: "/Images/logo_image3.png"
                            fillMode: Image.PreserveAspectFit
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent
                            CusText {
                                text: qsTr("软件版本") + utils4Qml.getVersion()
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                    }
                }
                CusRect {
                    Layout.preferredHeight: 48 * ST.dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    CusText {
                        text: qsTr("山东影响力智能科技有限公司")
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }

                CusRect {
                    Layout.preferredHeight: 48 * ST.dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent
                    CusText {
                        text: qsTr("电话：")+"400-616-8180"
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }
                CusRect {
                    Layout.preferredHeight: 48 * ST.dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent
                    CusText {
                        text: qsTr("邮箱：")+"<EMAIL>"
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }
                CusRect {
                    Layout.preferredHeight: 50 * ST.dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent
                    CusText {
                        text: qsTr("地址：")+qsTr("山东省临沂市应用科学城B座7楼")
                        width: parent.width
                        wrapMode: Text.WrapAnywhere
                    }
                }

                CusSpacer {
                    Layout.fillHeight: true
                }

                CusRect {
                    Layout.preferredHeight: 48 * ST.dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    CusButton {
                        width: 280 * ST.dpi_ratio
                        height: parent.height
                        text: qsTr("检查更新")
                        anchors.horizontalCenter: parent.horizontalCenter

                        onClicked: {
                            if(isNeedUpdate == true){
                                updateCtrl.updateStart()
                            }else{
                                toast.openInfo(qsTr("无可更新内容!"))
                            }
                            //接口失效 暂时关闭
//                            updateCtrl.reqGetVersionInfo(function (update_status, data) {
//                                switch (update_status) {
//                                case 0:
//                                    toast.openInfo("已是最新版本")
//                                    break
//                                case 1:
//                                    toast.openWarn("不是最新版本")
//                                    showUpdatePop()
//                                    popup_root.close()
//                                    break
//                                case 2:
//                                    toast.openError("请求错误")
//                                    break
//                                }
//                            })
                        }
                    }
                }

                // CusRect {
                //     Layout.preferredHeight: 190 * ST.dpi_ratio
                //     Layout.fillWidth: true
                //     color: ST.color_transparent

                //     RowLayout {
                //         anchors.fill: parent

                //         CusRect {
                //             Layout.fillHeight: true
                //             Layout.preferredWidth: 170 * ST.dpi_ratio
                //         }

                //         CusSpacer {
                //             Layout.fillWidth: true
                //         }

                //         CusRect {
                //             Layout.fillHeight: true
                //             Layout.preferredWidth: 170 * ST.dpi_ratio
                //         }
                //     }
                // }
            }
        }
    }
}
