﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import ".."

Item {
    id: popup_root
    opacity: .6
    function open() {
        popup_root.visible = true
    }
    function close() {
        popup_root.visible = false
    }

    signal confirm(var change_num)
    signal cancel

    property string title_name: "debug list"

    Rectangle {
        anchors.fill: parent
        color: ST.color_transparent
    }

    Rectangle {
        id: popup_contain_root
        width: 600 * dpi_ratio
        height: 400 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        onVisibleChanged: {
            x = (parent.width - width) / 2
            y = (parent.height - height) / 2
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35
                color: ST.color_transparent

                RowLayout {
                    anchors.fill: parent

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true

                        CusImg {
                            id: goods_img
                            anchors.fill: parent
                            source: "image://goodsImgSelected"
                            cache: false
                            Connections {
                                target: cameraControl
                                function onSigSendDebugList() {
                                    goods_img.refresh()
                                }
                            }
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        ListView {
                            id: lv_debug_list
                            anchors.fill: parent
                            model: ListModel {
                                id: lm_debug_list
                            }

                            delegate: CusRect {
                                width: lv_debug_list.width
                                height: 40 * dpi_ratio
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusText {
                                            text: model.goods_name
                                        }
                                    }
                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_transparent
                                        CusText {
                                            text: Number(model.goods_ratio).toFixed(6)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    Connections {
        target: cameraControl
        function onSigSendDebugList(debug_list) {
            var json_doc = JSON.parse(debug_list)
            lm_debug_list.clear()
            for (var i = 0; i < json_doc.length; ++i) {
                var cur_item = json_doc[i]
                lm_debug_list.append(cur_item)
            }
        }
    }
}
