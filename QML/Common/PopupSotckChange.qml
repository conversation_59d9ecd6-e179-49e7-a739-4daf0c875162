﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../../.."
import ".."

Item {
    id: popup_root

    function open() {
        popup_root.visible = true
        tf_stock_change.clear()
        tf_stock_change.focusMe()
    }
    function close() {
        popup_root.visible = false
        keyboard_c.closeAll()
    }

    function tryConfirm() {
        if (tf_stock_change.text == "") {
            toast.openWarn(qsTr("数量不可为空"))
            return
        }
        confirm(tf_stock_change.text)
        close()
    }

    signal confirm(var change_num)
    signal cancel

    property string title_name: "测试标题"
    property string message_info: "测试内容"

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 600 * dpi_ratio
        height: 400 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        onVisibleChanged: {
            x = (parent.width - width) / 2
            y = (parent.height - height) / 2
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        CusTextField {
                            id: tf_stock_change
                            keyboard_status: 1
                            anchors.centerIn: parent
                            width: 180 * dpi_ratio
                            height: 60 * dpi_ratio
                            validator: RegularExpressionValidator {
                                regularExpression: /^\d+(\.\d+)?$/
                            }
                            onAccepted: {
                                tryConfirm()
                            }
                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    tf_stock_change.focusMe()
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 70 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusButton {
                                text: qsTr("确认")
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                onClicked: {
                                    tryConfirm()
                                }
                            }
                            CusButton {
                                text: qsTr("取消")
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                onClicked: {
                                    cancel()
                                    close()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
