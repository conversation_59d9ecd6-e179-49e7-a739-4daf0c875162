﻿import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import EnumTool 1.0
import ".."
import "../NetOrderPage"

Item {
    width: parent.width
    height: 99 * dpi_ratio
    property bool isMaximized: false
    Component.onCompleted: {

    }
    Timer
    {
        id: updateNetOrderCount;
        interval: 2000;
        repeat: false;
        running: false;
        onTriggered:
        {
            reqRefreshNetOrderSaleCount()
        }
    }
    function reqRefreshNetOrderSaleCount() {
        netOrderControl.reqGetNetOrderCountNewByStatus(function (is_succ, data) {
            if (is_succ) {
                var json_doc = JSON.parse(data)
                var json_doc_data = json_doc["data"]
                logMgr.logDataInfo4Qml("标题栏开始更新订单数量")
                if(json_doc_data.hasOwnProperty('sendCount') && json_doc_data.hasOwnProperty('pickupCount')){//待发货
                    orderCount = json_doc_data.sendCount + json_doc_data.pickupCount;
                }
            } else {
                toast.openWarn(qsTr("获取网单小红点信息失败"))
                orderCount = 0;
            }
       }, utils4Qml.getCurDate(), utils4Qml.getCurDate(),"1")
    }
    function reqRefreshNetOrderCount() {
        netOrderControl.reqGetNetOrderCountByStatus(function (is_succ, data) {
            if (is_succ) {
                var json_doc = JSON.parse(data)
                var json_doc_data = json_doc["data"]
                for (var i = 0; i < json_doc_data.length; ++i) {
                    var cur_item = json_doc_data[i]
                    switch (cur_item.saleListHandleState) {
                    case 2:
                        //待发货
                        orderCount = json_doc_data.sendCount + json_doc_data.pickupCount;
                        break
                    }
                }

            } else {
                orderCount = 0;
            }
        }, utils4Qml.getCurDate(), utils4Qml.getCurDate())
    }
    //自定义标题栏
    CusRect {
        id: rect_back
        anchors.fill: parent
        color: ST.color_green
        visible: true
    }

    MouseArea {
        anchors.fill: parent
        property point clickPos: "0,0"
        z: -1
        onPressed: {
            clickPos = Qt.point(mouse.x, mouse.y)
        }
        onPositionChanged: {
            if (pressed) {
                var delta = Qt.point(mouse.x - clickPos.x, mouse.y - clickPos.y)
                window_root.x += delta.x
                window_root.y += delta.y
            }
        }
    }

    RowLayout {
        id: row_layout
        anchors.fill: parent
        spacing: 0

        CusRect {
            id: btn_switch
            Layout.fillHeight: true
            Layout.preferredWidth: height
            color: ST.color_transparent

            Image {
                anchors.fill: parent
                fillMode: Image.PreserveAspectFit
                source: "/Images/switch_btn.png"
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    page_switcher.open()
                }
            }
        }

        CusRect {
            id: rect_logo
            Layout.fillHeight: true
            Layout.preferredWidth: height * 2
            color: ST.color_transparent

            Image {
                id: img_logo
                anchors.left: parent.left
                anchors.leftMargin: 20 * dpi_ratio
                anchors.bottom: parent.bottom
                anchors.bottomMargin: 20 * dpi_ratio
                fillMode: Image.PreserveAspectFit
                source: "/Images/logo_image2.png"

                width: 160 * dpi_ratio
                height: 50 * dpi_ratio
            }
            CusText {
                text: qsTr("智慧收银")
                color: ST.color_white
                font {
                    bold: true
                    pixelSize: 18 * dpi_ratio
                }

                anchors.left: img_logo.left
                anchors.leftMargin: 10 * dpi_ratio
                anchors.bottom: img_logo.top
                anchors.bottomMargin: -7
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    is_mini = true
                }
            }
            Connections {
                target: mqttControl
                function onSigMqttRefreshOrderCountByStatus4Qml() {
                    logMgr.logDataInfo4Qml("网单MQTT消息到来，收银页开启更新角标显示")
                    reqRefreshNetOrderSaleCount()
                    }
                }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_transparent

            CusText {
                text: qsTr("返回收银")
                color: ST.color_white_pure
                anchors.centerIn: parent
                font.pixelSize: 32 * dpi_ratio * configTool.fontRatio
                visible: window_root.compo_index !== EnumTool.PAGE_PAY || pay_page.is_mamager_model
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    if (window_root.compo_index !== EnumTool.PAGE_PAY) {
                        window_root.compo_index = EnumTool.PAGE_PAY
                        utils4Qml.returnToPayPage()
                    } else {
                        if (pay_page.is_mamager_model) {
                            //商品管理模式激活
                            utils4Qml.returnToPayPage()
                        } else {
                            window_root.is_mini = true
                        }
                    }
                }
            }
        }

        CusSpacer {
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible: window_root.compo_index === EnumTool.PAGE_PAY && !pay_page.is_mamager_model
        }

        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: 70 * ST.dpi_ratio
            color: ST.color_transparent

            visible: configTool.isCanRecognitionDebug()

            CusRect {
                width: parent.width * .6
                height: width
                anchors.centerIn: parent
                color: configTool.isRecognitionDebug ? "#048A57" : ST.color_transparent
                radius: width / 2

                CusText {
                    anchors.centerIn: parent
                    text: "调"
                    color: ST.color_white_pure
                }
            }

            CusRect {
                anchors.centerIn: parent
                width: parent.width * .6
                height: width
                radius: width / 2
                color: ST.color_transparent
                border {
                    width: 2
                    color: ST.color_white
                }
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    configTool.isRecognitionDebug = !configTool.isRecognitionDebug
                }
            }
        }

        CusSpacer {
            Layout.preferredWidth: 20 * ST.dpi_ratio
        }

        CusRect {
            Layout.preferredWidth: 180 * dpi_ratio
            Layout.fillHeight: true
            color: ST.color_transparent

            CusText {
                anchors.centerIn: parent
                color: ST.color_white_pure
                text: qsTr("百货豆: ") + shopControl.shopBeans
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    shopControl.reqShopBeans()
                }
            }
        }

        CusSpacer {
            Layout.preferredWidth: 20 * ST.dpi_ratio
        }

        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: 70 * ST.dpi_ratio
            color: ST.color_transparent

            CusRect {
                anchors.centerIn: parent
                width: parent.width * .6
                height: width
                radius: width / 2
                color: ST.color_transparent
                border {
                    width: 2
                    color: ST.color_white
                }

                CusText {
                    text: "?"
                    color: ST.color_white
                    anchors.centerIn: parent
                    font.pixelSize: 26 * ST.dpi_ratio
                }
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    showVersionPop()
                }
            }
        }

        CusSpacer {
            Layout.preferredWidth: 20 * ST.dpi_ratio
        }
        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: 70 * ST.dpi_ratio
            color: ST.color_transparent

            Image
            {
                id: newOrderImage;
                width: 40* dpi_ratio
                height: 30*dpi_ratio
                anchors.verticalCenter: parent.verticalCenter;
                source: "/Images/newOrderImage.png";
                Rectangle
                {
                    id: newOrderImageRec;
                    width: 20* dpi_ratio
                    height: 20* dpi_ratio
                    anchors.right: parent.right;
                    anchors.rightMargin: -10* dpi_ratio
                    anchors.top: parent.top;
                    anchors.topMargin:  -10* dpi_ratio
                    color: "#ff2d11";
                    radius: 15* dpi_ratio
                    Component.onCompleted: {
                        updateNetOrderCount.start()
                    }
                    Text
                    {
                        id: newOrderImageText;
                        anchors.centerIn: parent;
                        font.family: "微软雅黑";
                        font.pointSize: 13* dpi_ratio
                        color: "#ffffff";
                        text: orderCount ;
                    }
                }
                MouseArea
                {
//                    anchors.centerIn: parent;
                    anchors.fill: parent
                    onPressed:
                    {
                    }
                    onClicked: {
                        if(payMethodControl.getNetStatus() !== "101"){
                            toast.openWarn(qsTr("离线状态下该功能不可用！"))
                        }else{
                            window_root.compo_index = EnumTool.PAGE_NET_ORDER;
                        }

                    }
                }
            }
        }

        CusSpacer {
            Layout.preferredWidth: 20 * ST.dpi_ratio
        }

        CusRect {
            Layout.preferredWidth: 140 * dpi_ratio
            Layout.fillHeight: true
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 0
                anchors.topMargin: 16 * dpi_ratio
                anchors.bottomMargin: 16 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent
                    CusText {
                        id: text_date
                        anchors.centerIn: parent
                        color: ST.color_white_pure
                        font.pixelSize: 26 * dpi_ratio
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent
                    CusText {
                        id: text_time
                        anchors.centerIn: parent
                        color: ST.color_white_pure
                        font.pixelSize: 26 * dpi_ratio
                    }
                }
            }

            Timer {
                interval: 500
                running: true
                repeat: true
                onTriggered: {
                    text_date.text = utils4Qml.getCurDate()
                    text_time.text = utils4Qml.getCurTime()
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: parent.cell_width * 1.5
            color: ST.color_transparent
            visible: false

            Image {
                anchors.centerIn: parent
                width: 70 * dpi_ratio
                height: width
                source: "/Images/yuebuzu_icon_solid.png"
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    window_root.close()
                }
            }
        }

        CusSpacer {
            Layout.fillHeight: true
            Layout.preferredWidth: 50 * dpi_ratio
        }
    }

    Connections {
        target: configTool
        function onIsRecognitionDebugChanged() {
            if (configTool.isRecognitionDebug) {
                showRealRecognitionList()
            } else {
                closeRealRecognitionList()
            }
        }
    }

    function showRealRecognitionList() {
        window_root.loader_4_real_recognition_list.sourceComponent = popup_4_real_recognizer
        window_root.loader_4_real_recognition_list.item.open()
    }
    function closeRealRecognitionList() {
        window_root.loader_4_real_recognition_list.item.close()
    }

    Component {
        id: popup_4_real_recognizer
        Popup4RealRecognizer {}
    }
}
