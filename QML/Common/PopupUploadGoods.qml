﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import ".."
import SettingEnum 1.0
import EnumTool 1.0

Item {
    id: popup_root

    function open(barcode) {
        popup_root.visible = true
        resetGoodsInfo()

        if (barcode == null || barcode == undefined) {
            is_manual_create = true
            generateGoodsBarcode()
            return
        }

        goods_barcode = barcode
        var left2 = goods_barcode.substr(0, 2)

        if (left2 == settingTool.getSetting(SettingEnum.BAR_CODE_WEIGHING_PRE_TWO_CODE)) {
            price_type = 1
        }

        goodsControl.reqGetOnlineGoodsInfo(function (is_succ, data) {
            if (is_succ) {
                var json_doc = JSON.parse(data)
                var json_data = json_doc["data"]

                goods_in_price = json_data.goods_in_price
                goods_sale_price = json_data.goods_sale_price
                goods_cus_price = json_data.goods_cus_price
                goods_name = json_data.goods_name

                goodsBrand = json_data.goods_brand
                goods_picturepath = json_data.goods_picturepath
                goods_standard = json_data.goods_standard
                goods_unit = json_data.goods_unit
            }
        }, barcode)
    }

    function close() {
        popup_root.visible = false
        sigClose()
        keyboard_c.closeAll()
    }

    function generateGoodsBarcode() {
        if (!is_manual_create)
            return
        goods_barcode = goodsManager.generateCustomBarcode(price_type == 1)
    }

    property bool is_manual_create: false

    property string goodsBrand: "" //品牌
    property string goods_picturepath: "" //图片路径
    property string goods_standard: "" //规格
    property string goods_unit: "" //单位

    property int price_type: 0 //0:计件 1:称重
    onPrice_typeChanged: {
        generateGoodsBarcode()
    }

    property string goods_barcode: ""
    property string goods_name: ""
    property string goods_in_price: ""
    property string goods_sale_price: ""
    property string goods_cus_price: ""

    signal confirm
    signal cancel
    signal sigClose

    property string title_name: "测试标题"

    onVisibleChanged: {
        if (visible) {
            logMgr.logDataInfo4Qml("custext_name.focusMe")
            custext_name.focusMe()
        }
    }

    function tryConfirm() {
        if (goods_barcode == "") {
            toast.openWarn(qsTr("条码不可为空"))
            return
        }
        if (goods_name == "") {
            toast.openWarn(qsTr("名称不可为空"))
            return
        }
        if (goods_in_price == "") {
            toast.openWarn(qsTr("进价不可为空"))
            return
        }
        if (goods_sale_price == "") {
            toast.openWarn(qsTr("售价不可为空"))
            return
        }
        uploadGoods()
        // confirm()
        close()
    }

    function resetGoodsInfo() {
        is_manual_create = false
        price_type = 0
        goods_barcode = ""
        goods_name = ""
        goods_in_price = ""
        goods_sale_price = ""
        goods_cus_price = ""
        goodsBrand = ""
        goods_picturepath = ""
        goods_standard = ""
    }

    function uploadGoods() {
        var data = ({
                        "goods_barcode": goods_barcode,
                        "goods_name": goods_name,
                        "goods_sale_price": goods_sale_price,
                        "goods_web_sale_price": goods_sale_price,
                        "goods_cus_price": goods_cus_price,
                        "goods_in_price": goods_in_price,
                        "goodsChengType": price_type,
                        "goodsBrand": goodsBrand,
                        "goods_picturepath": goods_picturepath,
                        "goods_standard": goods_standard,
                        "goods_unit": goods_unit == "" ? qsTr("无") : goods_unit,
                        "goods_kind_unique": EnumTool.ID_GOODS_KIND_COMMON_REAL,
                        "goods_contain": 1,
                        "pc_shelf_state": 1,
                        "web_shelf_state": 1
                    })

        goodsControl.reqUploadGoods_v2(function (is_succ, data) {
            var json_doc = JSON.parse(data)
            if (is_succ) {
                toast.openInfo(json_doc.msg)
                cancelGoods()
            } else {
                toast.openError(qsTr("新建商品失败"))
            }
        }, JSON.stringify([data]))
    }

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 500 * dpi_ratio
        height: 650 * dpi_ratio
        x: 230 * dpi_ratio
        y: 150 * dpi_ratio

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        property string str_num: "0"

        function addStrNum(str, str2) {
            if (str == "0" && str2 == "0")
                return "0"

            if (str2 == ".") {
                if (str.indexOf(".") == -1) {
                    return str += str2
                }
                return str
            }

            if (str == "0")
                return str2

            str += str2

            let arr = str.split(".")

            var ret_str = arr[0]

            if (arr.length == 2) {
                ret_str = arr[0] + "." + arr[1].substring(0, 2)
            }

            return ret_str
        }

        function setInput(input) {
            str_num = addStrNum(str_num, input)
        }

        function backspace() {
            str_num = str_num.substring(0, str_num.length - 1)
            if (str_num == "") {
                str_num = "0"
            }
        }

        function resetStrNum() {
            str_num = "0"
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio * configTool.fontRatio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 20 * dpi_ratio
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent
                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 0

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 15 * dpi_ratio

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 58 * dpi_ratio
                                        color: ST.color_transparent
                                        RowLayout {
                                            anchors.fill: parent

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: 120 * dpi_ratio
                                                color: ST.color_transparent
                                                CusText {
                                                    text: qsTr("条码")
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent
                                                RowLayout {
                                                    anchors.fill: parent

                                                    CusRect {
                                                        Layout.fillHeight: true
                                                        Layout.fillWidth: true
                                                        color: ST.color_transparent

                                                        CusTextField {
                                                            anchors.fill: parent
                                                            text: goods_barcode
                                                            enabled: is_manual_create

                                                            normal_keyboard_x: 750 * dpi_ratio
                                                            normal_keyboard_y: 550 * dpi_ratio

                                                            digital_keyboard_x: 750 * dpi_ratio
                                                            digital_keyboard_y: 400 * dpi_ratio

                                                            onTextEdited: {
                                                                goods_barcode = text
                                                            }
                                                        }
                                                    }

                                                    CusButton {
                                                        Layout.fillHeight: true
                                                        Layout.preferredWidth: height
                                                        visible: is_manual_create

                                                        Image {
                                                            width: 35 * ST.dpi
                                                            height: width
                                                            anchors.centerIn: parent
                                                            source: "/Images/plus.png"
                                                        }

                                                        onClicked: {
                                                            generateGoodsBarcode()
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 58 * dpi_ratio
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: 120 * dpi_ratio
                                                color: ST.color_transparent
                                                CusText {
                                                    text: qsTr("名称")
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent
                                                CusTextField {
                                                    id: custext_name
                                                    anchors.fill: parent
                                                    text: goods_name
                                                    is_clicked_select_all: true
                                                    keyboard_status: 0

                                                    normal_keyboard_x: 750 * dpi_ratio
                                                    normal_keyboard_y: 550 * dpi_ratio

                                                    digital_keyboard_x: 750 * dpi_ratio
                                                    digital_keyboard_y: 400 * dpi_ratio

                                                    onTextChanged: {
                                                        goods_name = text
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 58 * dpi_ratio
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: 120 * dpi_ratio
                                                color: ST.color_transparent
                                                CusText {
                                                    text: qsTr("计价类型")
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent
                                                RowLayout {
                                                    anchors.fill: parent
                                                    CusRadioManual {
                                                        Layout.alignment: Qt.AlignVCenter
                                                        Layout.preferredHeight: 28 * dpi_ratio
                                                        Layout.preferredWidth: 130 * dpi_ratio
                                                        text: qsTr("计件")
                                                        checked: price_type == 0
                                                        onClicked: {
                                                            price_type = 0
                                                        }
                                                    }
                                                    CusRadioManual {
                                                        Layout.alignment: Qt.AlignVCenter
                                                        Layout.preferredHeight: 28 * dpi_ratio
                                                        Layout.preferredWidth: 130 * dpi_ratio
                                                        text: qsTr("称重")
                                                        checked: price_type == 1
                                                        onClicked: {
                                                            price_type = 1
                                                        }
                                                    }
                                                    CusSpacer {
                                                        Layout.fillWidth: true
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 58 * dpi_ratio
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: 120 * dpi_ratio
                                                color: ST.color_transparent
                                                CusText {
                                                    text: qsTr("进价")
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent
                                                CusTextField {
                                                    anchors.fill: parent
                                                    text: goods_in_price
                                                    keyboard_status: 1
                                                    is_clicked_select_all: true

                                                    validator: RegularExpressionValidator {
                                                        regularExpression: /^\d+(\.\d+)?$/
                                                    }
                                                    onTextChanged: {
                                                        goods_in_price = text
                                                    }
                                                    normal_keyboard_x: 750 * dpi_ratio
                                                    normal_keyboard_y: 550 * dpi_ratio

                                                    digital_keyboard_x: 750 * dpi_ratio
                                                    digital_keyboard_y: 400 * dpi_ratio
                                                }
                                            }
                                        }
                                    }
                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 58 * dpi_ratio
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: 120 * dpi_ratio
                                                color: ST.color_transparent
                                                CusText {
                                                    text: qsTr("售价")
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent
                                                CusTextField {
                                                    anchors.fill: parent
                                                    text: goods_sale_price
                                                    keyboard_status: 1
                                                    is_clicked_select_all: true

                                                    validator: RegularExpressionValidator {
                                                        regularExpression: /^\d+(\.\d+)?$/
                                                    }
                                                    onTextChanged: {
                                                        goods_sale_price = text
                                                    }

                                                    normal_keyboard_x: 750 * dpi_ratio
                                                    normal_keyboard_y: 550 * dpi_ratio

                                                    digital_keyboard_x: 750 * dpi_ratio
                                                    digital_keyboard_y: 400 * dpi_ratio
                                                }
                                            }
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 58 * dpi_ratio
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: 120 * dpi_ratio
                                                color: ST.color_transparent
                                                CusText {
                                                    text: qsTr("会员价")
                                                    anchors.verticalCenter: parent.verticalCenter
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_transparent
                                                CusTextField {
                                                    anchors.fill: parent
                                                    text: goods_cus_price
                                                    keyboard_status: 1
                                                    is_clicked_select_all: true

                                                    validator: RegularExpressionValidator {
                                                        regularExpression: /^\d+(\.\d+)?$/
                                                    }
                                                    onTextChanged: {
                                                        goods_cus_price = text
                                                    }
                                                    normal_keyboard_x: 750 * dpi_ratio
                                                    normal_keyboard_y: 550 * dpi_ratio

                                                    digital_keyboard_x: 750 * dpi_ratio
                                                    digital_keyboard_y: 400 * dpi_ratio
                                                }
                                            }
                                        }
                                    }

                                    CusSpacer {
                                        Layout.fillHeight: true
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 58 * dpi_ratio
                                        color: ST.color_transparent

                                        RowLayout {
                                            anchors.fill: parent

                                            CusButton {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                text: qsTr("新增商品")
                                                onClicked: {
                                                    tryConfirm()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
