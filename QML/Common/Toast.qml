﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
import QtGraphicalEffects 1.15

import ".."
import EnumTool 1.0

Item {
    id: popup_root
    anchors.fill: parent
    visible: false

    property string toast_str: text_info.text

    property int toast_level: EnumTool.TOAST_LEVEL__INFO

    property string color_info: utils4Qml.getColorWithOpacity(ST.color_green, .9)
    property string color_warn: utils4Qml.getColorWithOpacity(ST.color_orange, .9)
    property string color_err: utils4Qml.getColorWithOpacity(ST.color_red, .9)

    function open(text, level_in = EnumTool.TOAST_LEVEL__INFO, time_imterval = 2000) {
        popup_root.visible = true
        text_info.text = text
        toast_level = level_in
        timer.interval = time_imterval
        timer.restart()
    }

    function close() {
        popup_root.visible = false
        text_info.text = ""
    }

    Rectangle {
        id: back_rect
        anchors.horizontalCenter: parent.horizontalCenter
        y: 70 * ST.dpi

        width: ((26 + 24) * ST.dpi + text_metrics.boundingRect.width)
        height: 40 * ST.dpi
        color: if (toast_level == EnumTool.TOAST_LEVEL__INFO) {
                   return color_info
               } else if (toast_level == EnumTool.TOAST_LEVEL__WARN) {
                   return color_warn
               } else if (toast_level == EnumTool.TOAST_LEVEL__ERROR) {
                   return color_err
               }

        radius: Style.radius

        border {
            width: 2 * ST.dpi
            color: ST.color_white
        }

        CusImg {
            id: img_toast_level
            width: 24 * ST.dpi
            height: width
            source: if (toast_level == EnumTool.TOAST_LEVEL__INFO) {
                        return "/Images/right2.png"
                    } else if (toast_level == EnumTool.TOAST_LEVEL__WARN) {
                        return "/Images/x.png"
                    } else if (toast_level == EnumTool.TOAST_LEVEL__ERROR) {
                        return "/Images/x.png"
                    }
            anchors.verticalCenter: parent.verticalCenter
            x: 10 * ST.dpi
        }

        CusText {
            id: text_info
            color: Style.color_white
            text: "测试信息"
            anchors.left: img_toast_level.right
            anchors.leftMargin: 6 * ST.dpi
            anchors.verticalCenter: parent.verticalCenter
            font.pixelSize: 20 * ST.dpi
        }

        TextMetrics {
            id: text_metrics
            font: text_info.font
            text: text_info.text
        }
    }

    Timer {
        id: timer
        interval: 2000
        repeat: false
        onTriggered: close()
    }
}
