﻿import QtQuick 2.15
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0
import Annotation 1.0
import QtGraphicalEffects 1.15

import ".."

CusRect {
    id: popup_root

    function open() {
        popup_root.visible = true
    }
    function close() {
        popup_root.visible = false
    }

    color: ST.color_transparent

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                popup_root.close()
            }
        }
    }

    CusRect {
        id: rect_pop
        width: 900 * ST.dpi
        height: 800 * ST.dpi
        color: ST.color_white_pure
        radius: ST.radius
        y: (parent.height - height) / 2
        x: (parent.width - width) / 2

        MouseArea {
            anchors.fill: parent
        }

        CusRect {
            anchors.fill: parent
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                //标题栏
                Rectangle {
                    Layout.preferredHeight: 65 * ST.dpi_ratio
                    Layout.fillWidth: true

                    Image {
                        anchors.fill: parent
                        source: "/Images/shade2.png"
                    }

                    RowLayout {
                        anchors.fill: parent
                        spacing: 0

                        Rectangle {
                            color: ST.color_transparent
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            CusText {
                                text: "设置识别区域"
                                font.pixelSize: 28 * ST.dpi_ratio
                                font.bold: true
                                color: ST.color_font
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.centerIn: parent
                            }
                        }
                    }

                    CusMoveArea2 {
                        anchors.fill: parent
                        control: rect_pop
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 20 * ST.dpi
                        spacing: 20 * ST.dpi

                        CusRect {
                            id: rect_goods_img
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusImg {
                                id: goods_img
                                anchors.fill: parent
                                source: "image://goodsImg"
                                cache: false

                                // fillMode: Image.PreserveAspectFit
                                Connections {
                                    target: cameraControl
                                    enabled: goods_img.visible
                                    function onGoodsImgChanged() {
                                        goods_img.refresh()
                                    }
                                }
                            }

                            layer.enabled: true
                            layer.effect: OpacityMask {
                                maskSource: Rectangle {
                                    width: rect_goods_img.width
                                    height: rect_goods_img.height
                                    radius: ST.radius
                                }
                            }

                            Annotation {
                                id: id_annotation
                                anchors.fill: parent

                                drawType: Annotation.POLYGON

                                MouseArea {
                                    anchors.fill: parent
                                    hoverEnabled: true
                                    focus: true
                                    Keys.enabled: true

                                    onPressed: {
                                        if (mouse.button == Qt.LeftButton) {
                                            id_annotation.mousePress(mouse.x, mouse.y)
                                        }
                                    }

                                    onPositionChanged: {
                                        id_annotation.mouseMove(mouse.x, mouse.y)
                                    }

                                    onReleased: {
                                        if (mouse.button == Qt.LeftButton) {
                                            id_annotation.mouseRelease(mouse.x, mouse.y)
                                        }
                                    }

                                    onClicked: {
                                        if (mouse.button == Qt.RightButton) {
                                            id_annotation.clear()
                                        }
                                    }

                                    Keys.onPressed: {
                                        if (event.matches(StandardKey.Undo)) {
                                            id_annotation.undo()
                                        }
                                    }
                                }

                                Component.onCompleted: {
                                    cameraControl.setAnnotation(id_annotation)
                                    initByConfig(width, height)
                                }
                            }
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 70 * ST.dpi
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 20 * ST.dpi

                                CusSpacer {
                                    Layout.fillWidth: true
                                }

                                CusButton {
                                    text: "清空"
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 255 * ST.dpi
                                    onClicked: {
                                        id_annotation.clear()
                                    }
                                }

                                CusButton {
                                    text: "确定"
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 255 * ST.dpi
                                    onClicked: {
                                        cameraControl.setRecognitionArea(rect_goods_img.width, rect_goods_img.height)
                                        cameraControl.setSelectedArea()
                                        popup_root.close()
                                    }
                                }

                                CusButton {
                                    text: "取消"
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 255 * ST.dpi
                                    onClicked: {
                                        popup_root.close()
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_root.width
                height: popup_root.height
                radius: ST.radius
            }
        }
    }
}
