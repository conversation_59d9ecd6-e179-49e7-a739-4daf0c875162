﻿import QtQuick 2.15
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0
import QtGraphicalEffects 1.15

import ".."

CusRect {
    id: popup_root

    function open() {
        popup_root.visible = true
    }
    function close() {
        popup_root.visible = false
    }

    color: ST.color_transparent

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                popup_root.close()
            }
        }
    }

    //鼠标点击坐标位置
    property var pointArray: []

    //储存鼠标开始时的坐标
    property real startX
    property real startY

    //储存鼠标结束时的坐标
    property real stopX
    property real stopY

    //是否允许鼠标移动绘制事件
    property bool isMouseMoveEnable: false

    CusRect {
        width: 900 * ST.dpi
        height: 800 * ST.dpi
        color: ST.color_white_pure
        radius: ST.radius
        y: (parent.height - height) / 2
        x: (parent.width - width) / 2

        MouseArea {
            anchors.fill: parent
        }

        CusRect {
            anchors.fill: parent
            color: ST.color_transparent
            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                //标题栏
                Rectangle {
                    Layout.preferredHeight: 65 * ST.dpi_ratio
                    Layout.fillWidth: true
                    Image {
                        anchors.fill: parent
                        source: "/Images/shade2.png"
                    }
                    RowLayout {
                        anchors.fill: parent
                        spacing: 0

                        Rectangle {
                            color: ST.color_transparent
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            CusText {
                                text: "设置识别区域"
                                font.pixelSize: 28 * ST.dpi_ratio
                                font.bold: true
                                color: ST.color_font
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.centerIn: parent
                            }
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 20 * ST.dpi
                        spacing: 20 * ST.dpi

                        CusRect {
                            id: rect_goods_img
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            CusImg {
                                id: goods_img
                                anchors.fill: parent
                                source: "image://goodsImg"
                                cache: false
                                Connections {
                                    target: cameraControl
                                    enabled: goods_img.visible
                                    function onGoodsImgChanged() {
                                        goods_img.refresh()
                                    }
                                }
                            }

                            layer.enabled: true
                            layer.effect: OpacityMask {
                                maskSource: Rectangle {
                                    width: rect_goods_img.width
                                    height: rect_goods_img.height
                                    radius: ST.radius
                                }
                            }

                            Canvas {
                                id: canvas
                                anchors.fill: parent

                                onPaint: {
                                    var ctx = getContext("2d")
                                    ctx.lineWidtn = 3 * ST.dpi
                                    ctx.strokeStyle = "blue"

                                    ctx.clearRect(0, 0, width, height) //清空所画图形

                                    ctx.beginPath()

                                    if (pointArray.length > 0) {

                                        var pre_item = pointArray[0]
                                        ctx.moveTo(pre_item.x, pre_item.y)

                                        for (var i = 1; i < pointArray.length; ++i) {
                                            var cur_item = pointArray[i]
                                            ctx.lineTo(cur_item.x, cur_item.y)
                                        }

                                        if (pointArray.length == 4) {
                                            ctx.lineTo(pre_item.x, pre_item.y)
                                        } else {
                                            ctx.lineTo(area.mouseX, area.mouseY)
                                        }
                                    }

                                    // if (!isMouseMoveEnable) {
                                    //     return
                                    // }

                                    // if (isMouseMoveEnable) {
                                    //     ctx.clearRect(0, 0, width, height)
                                    // }
                                    ctx.stroke()
                                }

                                MouseArea {
                                    id: area
                                    anchors.fill: parent
                                    acceptedButtons: Qt.LeftButton | Qt.RightButton
                                    hoverEnabled: true
                                    onClicked: {
                                        if (mouse.button == Qt.RightButton) {
                                            pointArray = []
                                        } else {
                                            if (pointArray.length >= 4) {
                                                return
                                            }

                                            pointArray.push({
                                                                "x": mouse.x,
                                                                "y": mouse.y
                                                            })
                                        }
                                        canvas.requestPaint()
                                    }
                                    onPositionChanged: {
                                        canvas.requestPaint()
                                    }
                                }
                            }
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 70 * ST.dpi
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 20 * ST.dpi

                                CusSpacer {
                                    Layout.fillWidth: true
                                }

                                CusButton {
                                    text: "确定"
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 255 * ST.dpi
                                    onClicked: {

                                    }
                                }

                                CusButton {
                                    text: "取消"
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 255 * ST.dpi
                                    onClicked: {
                                        popup_root.close()
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_root.width
                height: popup_root.height
                radius: ST.radius
            }
        }
    }
}
