﻿import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import QtGraphicalEffects 1.15

import ".."

Window {
    id: popup_root
    flags: Qt.WindowStaysOnTopHint | Qt.Window | Qt.FramelessWindowHint //Qt.WindowStaysOnTopHint |
    color: ST.color_transparent

    property bool can_close: !updateCtrl.is_updating

    width: 500 * ST.dpi
    height: 500 * ST.dpi

    CusRect {
        anchors.fill: parent
        id: update_contain_root
        color: ST.color_white_pure
        radius: ST.radius

        CusMoveArea {
            anchors.fill: parent
            control: popup_root
        }

        CusRect {
            anchors.fill: parent
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                Image {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 260 * ST.dpi
                    source: "/Images/updateBackground.png"
                    fillMode: Image.PreserveAspectFit
                    verticalAlignment: Image.AlignTop
                }

                CusSpacer {
                    Layout.fillHeight: true
                }

                CusRect {
                    Layout.preferredHeight: 48 * ST.dpi
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    CusButton {
                        width: 280 * ST.dpi
                        height: parent.height
                        text: updateCtrl.is_updating ? qsTr("下载中") : qsTr("立即更新")
                        enabled: !updateCtrl.is_updating && updateCtrl.isMaintenanceExist
                        anchors.horizontalCenter: parent.horizontalCenter
                        onClicked: {
                            if(isNeedUpdate == true){
                                updateCtrl.updateStart()
                            }else{
                                toast.openInfo(qsTr("无可更新内容!"))
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredHeight: 48 * ST.dpi
                }
            }

            CusRect {
                width: 400 * ST.dpi
                height: 180 * ST.dpi
                anchors.horizontalCenter: parent.horizontalCenter
                y: 200 * ST.dpi
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 15 * ST.dpi
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 30 * ST.dpi
                        color: ST.color_transparent

                        CusText {
                            text: updateCtrl.upgradeBrief
                            font.bold: true
                            font.pixelSize: 22 * ST.dpi
                            width: parent.width
                            height: parent.height
                            elide: Text.ElideRight
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        CusText {
                            anchors.fill: parent
                            verticalAlignment: Text.AlignTop
                            wrapMode: Text.WrapAnywhere
                            text: updateCtrl.upgradeDetail
                            font.pixelSize: 18 * ST.dpi
                        }
                    }
                }
            }

            CusText {
                text: qsTr("新版本升级")
                x: 40 * ST.dpi
                y: 35 * ST.dpi
                font.pixelSize: 36 * ST.dpi
                font.bold: true
                color: ST.color_white
            }
        }

        CusRect {
            id: progress_bar
            width: parent.width
            anchors.bottom: parent.bottom
            height: 15 * ST.dpi
            color: ST.color_grey

            property real process_ratio: updateCtrl.file_download_index / updateCtrl.file_download_total

            CusRect {
                color: ST.color_green
                height: parent.height
                width: parent.width * parent.process_ratio
            }
        }

        CusRect {
            width: 40 * ST.dpi
            height: width
            anchors.right: parent.right
            anchors.rightMargin: 20 * ST.dpi
            anchors.top: parent.top
            anchors.topMargin: 20 * ST.dpi
            radius: ST.radius
            color: ST.color_transparent

            visible: !updateCtrl.is_updating

            CusImg {
                anchors.fill: parent
                source: "/Images/x2.png"
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    popup_root.close()
                }
            }
        }

        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: update_contain_root.width
                height: update_contain_root.height
                radius: ST.radius
            }
        }
    }

    Toast {
        id: toast_root
        anchors.fill: parent
        z: 999
    }

    Connections {
        target: utils4Qml
        function onSigOpenToast(msg_in, level_in, time_in) {
            toast_root.open(msg_in, level_in, time_in * 20)
            timer_close.start()
        }
    }

    Timer {
        id: timer_close
        interval: 3000
        onTriggered: {
            popup_root.close()
        }
    }

    Connections {
        target: updateCtrl
        function onSigDownloadfailed() {
            can_close = true
            toast.openError(qsTr("下载失败!"))
        }
    }
}
