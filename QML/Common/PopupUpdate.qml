﻿import QtQuick 2.15;
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.1
import EnumTool 1.0
import SettingEnum 1.0
import QtGraphicalEffects 1.15

import ".."

Item {
    id: popup_root
    anchors.fill: parent
    function open() {
        popup_root.visible = true
    }
    function close() {
        popup_root.visible = false
    }

    property bool can_close: !updateCtrl.is_updating

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                if (can_close) {
                    popup_root.close()
                } else {

                }
            }
        }
    }

    CusRect {
        id: update_contain_root
        width: 500 * ST.dpi_ratio
        height: 500 * ST.dpi_ratio
        color: ST.color_white_pure
        radius: ST.radius
        y: (parent.height - height) / 2
        x: (parent.width - width) / 2

        MouseArea {
            anchors.fill: parent
        }

        CusRect {
            anchors.fill: parent
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                Image {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 260 * ST.dpi_ratio
                    source: "/Images/updateBackground.png"
                    fillMode: Image.PreserveAspectFit
                    verticalAlignment: Image.AlignTop
                }

                CusSpacer {
                    Layout.fillHeight: true
                }

                CusRect {
                    Layout.preferredHeight: 48 * ST.dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    CusButton {
                        width: 280 * ST.dpi_ratio
                        height: parent.height
                        text: updateCtrl.is_updating ? qsTr("更新中") : qsTr("立即更新")
                        enabled: !updateCtrl.is_updating
                        anchors.horizontalCenter: parent.horizontalCenter
                        onClicked: {
                            updateCtrl.updateStart()
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredHeight: 48 * ST.dpi_ratio
                }
            }

            CusRect {
                width: 400 * ST.dpi_ratio
                height: 180 * ST.dpi_ratio
                anchors.horizontalCenter: parent.horizontalCenter
                y: 200 * ST.dpi_ratio
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 8 * ST.dpi_ratio
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 30 * ST.dpi_ratio
                        color: ST.color_transparent

                        CusText {
                            text: qsTr("更新内容")
                            font.bold: true
                            font.pixelSize: 22 * ST.dpi_ratio
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: ST.color_transparent

                        CusText {
                            anchors.fill: parent
                            wrapMode: Text.WrapAnywhere
                            text: updateCtrl.upgradeDetail
                            font.pixelSize: 18 * ST.dpi_ratio
                        }
                    }
                }
            }

            CusText {
                text: qsTr("新版本升级")
                x: 40 * ST.dpi_ratio
                y: 35 * ST.dpi_ratio
                font.pixelSize: 36 * ST.dpi_ratio
                font.bold: true
                color: ST.color_white_pure
            }
        }

        CusRect {
            id: progress_bar
            width: parent.width
            anchors.bottom: parent.bottom
            height: 15 * ST.dpi_ratio
            color: ST.color_grey

            property real process_ratio: updateCtrl.file_download_index / updateCtrl.file_download_total

            CusRect {
                color: ST.color_green
                height: parent.height
                width: parent.width * parent.process_ratio
            }
        }

        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: update_contain_root.width
                height: update_contain_root.height
                radius: ST.radius
            }
        }
    }

    Connections {
        target: updateCtrl
        function onSigDownloadfailed() {
            can_close = true
            toast.openError(qsTr("下载失败!"))
        }
    }
}
