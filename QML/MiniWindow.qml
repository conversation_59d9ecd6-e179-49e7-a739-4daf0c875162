﻿import QtQuick 2.15
import QtQuick.Window 2.15
//import QtQuick.VirtualKeyboard 2.15
//import QtQuick.VirtualKeyboard.Styles 2.15
//import QtQuick.VirtualKeyboard.Settings 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.0

import "."

Rectangle {
    id: rect_mini

    color: ST.color_green
    radius: ST.radius
    MouseArea {
        anchors.fill: parent
        property point clickPos: "0,0"
        z: -1
        onPressed: {
            clickPos = Qt.point(mouse.x, mouse.y)
        }
        onPositionChanged: {
            if (pressed) {
                var delta = Qt.point(mouse.x - clickPos.x, mouse.y - clickPos.y)
                window_root.x += delta.x
                window_root.y += delta.y
            }
        }
    }

    RowLayout {
        anchors.fill: parent
        anchors.margins: 10 * dpi_ratio
        anchors.topMargin: 12 * dpi_ratio
        anchors.bottomMargin: 12 * dpi_ratio
        spacing: 20 * dpi_ratio
        CusText {
            Layout.alignment: Qt.AlignVCenter
            text: qsTr("收银系统")
            color: ST.color_white
            font.bold: true
            font.pixelSize: 24 * dpi_ratio * configTool.fontRatio
        }
        CusButton {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: ST.color_white
            focusPolicy: Qt.ClickFocus

            onClicked: {
                is_mini = false
                window_root.x = 0
                window_root.y = 0
            }
            text: qsTr("返回收银")
            font_color: ST.color_black
        }
    }
}
