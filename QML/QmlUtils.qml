﻿pragma Singleton

import QtQuick 2.15;

QtObject {
    function urlEncode(postMap) {
        var ret_str

        for (var key in map) {
            ret_str += key + "=" + map[key].length == 0 ? "\"\"" : map[key] + "&"
        }
        ret_str = ret_str.substring(0, ret_str.length - 1)
        return ret_str
    }

    function getTrunc2(num_in) {
        return (Math.floor(num_in * 100) / 100).toFixed(2)
    }
}
