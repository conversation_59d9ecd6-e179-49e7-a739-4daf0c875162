﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../_Refactor"
import ".."

Rectangle {
    id: login_page
    color: Style.color_green

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 50 * dpi_ratio
        spacing: 50

        Item {
            Layout.alignment: Qt.AlignLeft
            width: 500 * dpi_ratio
            height: 50 * dpi_ratio

            RowLayout {
                anchors.fill: parent
                Image {
                    id: logo_image
                    Layout.fillHeight: true
                    Layout.preferredWidth: height * 3
                    fillMode: Image.PreserveAspectFit
                    source: "/Resources/Images/logo_image2.png"
                }
                TextC {
                    text: "智慧收银系统"
                    font.bold: true
                    font.pixelSize: 30
                    color: Style.color_white_pure
                }
                TextC {
                    text: "v3.0.0.4"
                    font.bold: true
                    font.pixelSize: 30
                    color: Style.color_white_pure
                }
            }
        }

        RectDebug {
            Layout.alignment: Qt.AlignHCenter
            width: 420 * dpi_ratio
            height: 600 * dpi_ratio

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 30
                spacing: 20

                RectDebug {
                    Layout.preferredHeight: 130 * dpi_ratio
                    Layout.preferredWidth: height
                    Layout.alignment: Qt.AlignHCenter
                }

                RectDebug {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 380 * dpi_ratio

                    ColumnLayout {
                        anchors.fill: parent
                        RectDebug {
                            Layout.preferredHeight: 55 * dpi_ratio
                            Layout.fillWidth: true
                            RowLayout {
                                anchors.fill: parent

                                RectDebug {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: height
                                }
                                RectDebug {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                }
                            }
                        }
                    }
                }
                Spacer {
                    Layout.fillHeight: true
                }
            }
        }

        Spacer {
            Layout.fillHeight: true
        }

        Item {
            Layout.fillWidth: true
            height: 75 * dpi_ratio

            TextC {
                anchors.centerIn: parent
                text: "Copyright©    2016-2023 Influence Inc. 保留所有权利    丨   山东影响力智能科技有限公司" + "\n" + "山东省临沂市应用科学城B座7楼    400-7088-365"
                horizontalAlignment: Text.AlignHCenter
                color: Style.color_white_pure
                lineHeight: 1.3
            }
        }
    }

    //        Image {
    //            id: logo_image
    //            width: 102 * dpi_ratio
    //            height: 30 * dpi_ratio
    //            anchors.left: parent.left
    //            anchors.leftMargin: 40 * dpi_ratio
    //            anchors.top: parent.top
    //            anchors.topMargin: 40 * dpi_ratio
    //            source: "/Resources/Images/logo_image2.png"

    //            MouseArea {
    //                anchors.fill: parent
    //                onDoubleClicked: {
    //                    console.log(obj_test_state.test170_state)
    //                    if (obj_test_state.test170_state) {
    //                        obj_test_state.test170_state = false
    //                        txt_debug_state.text = settingPage.getTestState() ? "当前系统使用test170服务器" : "当前系统使用正式服务器"
    //                        txt_debug_state.visible = true
    //                        toast.show("显示当前服务器网络状态", 2000)
    //                    } else {
    //                        txt_debug_state.text = settingPage.getTestState() ? "当前系统使用test170服务器" : "当前系统使用正式服务器"
    //                        txt_debug_state.visible = false
    //                        toast.show("点击头像", 2000)
    //                        obj_test_state.test170_state = true
    //                    }
    //                }
    //            }
    //        }

    //        Text {
    //            id: logo_text
    //            anchors.left: logo_image.right
    //            anchors.leftMargin: 15 * dpi_ratio
    //            anchors.top: logo_image.top
    //            anchors.topMargin: -3 * dpi_ratio
    //            text: "智慧收银系统 v" + payPage.getVersionPro()
    //            font.family: "微软雅黑"
    //            font.pointSize: 16 * dpi_ratio
    //            font.bold: true
    //            color: "#ffffff"
    //        }

    //        Rectangle {
    //            id: mask
    //            color: "black"
    //            width: 90 * dpi_ratio
    //            height: 90 * dpi_ratio
    //            radius: 90 * dpi_ratio
    //            visible: false
    //            antialiasing: true
    //            smooth: true
    //        }

    //        //    OpacityMask {
    //        //        anchors.fill: employer_image
    //        //        source: employer_image
    //        //        maskSource: mask
    //        //    }
    //        DropShadow {
    //            anchors.fill: user_login_rec
    //            horizontalOffset: 3 * dpi_ratio
    //            verticalOffset: 5 * dpi_ratio
    //            radius: 8 * dpi_ratio
    //            samples: 10 * dpi_ratio
    //            fast: false
    //            color: "#448765"
    //            source: user_login_rec
    //            spread: 0.2
    //        }

    //        Rectangle {
    //            id: user_login_rec
    //            width: 285 * dpi_ratio
    //            height: 260 * dpi_ratio
    //            anchors.horizontalCenter: parent.horizontalCenter
    //            anchors.top: employer_image.bottom
    //            anchors.topMargin: 57 * dpi_ratio
    //            color: "#ffffff"
    //            radius: 8 * dpi_ratio

    //            Image {
    //                id: user_name_image
    //                width: 30 * dpi_ratio
    //                height: 30 * dpi_ratio
    //                anchors.left: user_login_rec.left
    //                anchors.leftMargin: 20 * dpi_ratio
    //                anchors.top: user_login_rec.top
    //                anchors.topMargin: 45 * dpi_ratio
    //                source: "/Resources/Images/user_name.png"
    //            }

    //            Image {
    //                id: user_pwd_image
    //                width: 30 * dpi_ratio
    //                height: 30 * dpi_ratio
    //                anchors.left: user_name_image.left
    //                anchors.top: user_name_image.bottom
    //                anchors.topMargin: 33 * dpi_ratio
    //                source: "/Resources/Images/user_pwd.png"
    //            }
    //        }

    //        Text {
    //            id: address
    //            anchors.horizontalCenter: parent.horizontalCenter
    //            anchors.bottom: parent.bottom
    //            anchors.bottomMargin: 32 * dpi_ratio
    //            text: "山东省临沂市应用科学城B座7楼    400-7088-365"
    //            font.family: "微软雅黑"
    //            font.pointSize: 11 * dpi_ratio
    //            color: "#ffffff"
    //        }

    //        Text {
    //            id: copyright
    //            anchors.horizontalCenter: parent.horizontalCenter
    //            anchors.bottom: address.top
    //            anchors.bottomMargin: 12 * dpi_ratio
    //            text: "Copyright©    2016-2021 Influence Inc. 保留所有权利    丨   山东影响力智能科技有限公司"
    //            font.family: "微软雅黑"
    //            font.pointSize: 11 * dpi_ratio
    //            color: "#ffffff"
    //        }
    //    }

    //    ConfirmBtn {
    //        id: myMsgbox8
    //        anchors.centerIn: login_page
    //        visible: false
    //        onYes: {
    //            payPage.startShengji()
    //        }
    //        onNo: {

    //            visible = false
    //            black_shadow2.visible = false
    //            enableLoginPage()
    //        }
    //    }
}
