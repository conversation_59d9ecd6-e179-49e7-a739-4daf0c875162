﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Dialogs 1.3
import "../Common"
import ".."
import EnumTool 1.0
import SettingEnum 1.0
import QtQuick.Controls 2.15


Image {
    property string imagePath: qsTr("/Images/logo.png")
    anchors.fill: parent
    source: "/Images/login_background.png"
    Component.onCompleted: {
        if(visible){
            selectedIndex = configTool.languageSelected;
        }
    }

    property int selectedIndex: 1
    property var languages: ["中文", "English","Việt Nam","แบบไทย","Русский"]

    function login() {
        shopControl.login_v2(loginWizard, text_field_account.text, text_field_passwd.text)
        keyboard_c.closeAll()
    }
    function getFlagImage(index) {

        var flagPaths = ["/Images/Chinese.png", "/Images/English.png","/Images/Vietnam.png", "/Images/Thailand.png", "/Images/Russia.png"];
        if (index >= 0 && index < flagPaths.length) {
            return flagPaths[index];
        }
        return "";
    }
    function loginWizard(is_succ, data) {
        if (is_succ) {
            var json_doc = JSON.parse(data)
            if(json_doc["msg"] == "登录成功！"){
                text_field_passwd.firstClick = true
                text_field_passwd.background.border.color = ST.color_grey_border2
                toast.openInfo(qsTr("登录成功！"))
            }
            compo_index = EnumTool.PAGE_PAY
        } else {
            toast.openWarn(qsTr("密码有误 / 服务器错误"))
        }
    }

    MouseArea {
        anchors.fill: parent
    }

    Image {
        x: 320 * dpi_ratio
        y: 380 * dpi_ratio
        source: imagePath
        width: 500 * dpi_ratio
        height: 230 * dpi_ratio
        fillMode: Image.PreserveAspectFit
    }
    Item {
        x: 66 * dpi_ratio
        y: 46 * dpi_ratio
        z: 100 * dpi_ratio

        Image {
            id: img
            width: 35 * dpi_ratio * configTool.fontRatio
            height: width
            fillMode: Image.PreserveAspectFit
            source: "/Images/customer_service.png"
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    window_root.is_mini = true
                }
            }
        }
        CusText {
            anchors.left: img.right
            anchors.leftMargin: 9 * dpi_ratio
            anchors.verticalCenter: img.verticalCenter
            text: qsTr("客服电话：400-616-8180")
            font.pixelSize: 24 * dpi_ratio * configTool.fontRatio
        }
    }
    Rectangle {
        id: dropdownButton
        width:240 *dpi_ratio
        height:60 *dpi_ratio
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.rightMargin: 25 * dpi_ratio
        anchors.topMargin: 35 * dpi_ratio
        radius:10 * dpi_ratio
        color: "#FFFFFF"

        Rectangle
        {
            anchors.fill: parent
            anchors.verticalCenter:parent.verticalCenter;
            color: "#FFFFFF";
            border.width: 1  * dpi_ratio;
            radius:8 * dpi_ratio
            border.color: "#E1E1E1";
            Image {
                id: selectedImage
                source: selectedIndex >= 0 ? getFlagImage(selectedIndex) : ""
                //source: "/Images/Thailand.png"
                width: 30 * dpi_ratio
                height: 30 * dpi_ratio
                anchors.left: parent.left
                anchors.leftMargin:  15* dpi_ratio
                anchors.verticalCenter: parent.verticalCenter
                fillMode: Image.PreserveAspectFit
            }

            Text {
                id:selectedText
                anchors.left: selectedImage.right
                anchors.leftMargin: 15 *dpi_ratio
                anchors.verticalCenter: parent.verticalCenter
                text: selectedIndex >= 0 ? languages[selectedIndex] : "Select Language"
                verticalAlignment: Text.AlignVCenter
                font.family: ST.fontFamilySiYan
                font.pointSize: 20*dpi_ratio;
//                font.weight: Font.Bold
                color: "black"
                elide: Text.ElideRight
            }
            Image
            {
                anchors.right: parent.right
                anchors.rightMargin: 15 * dpi_ratio
                anchors.verticalCenter: parent.verticalCenter
                source: "/Images/InvertedTriangle.png"
                width: 20 * dpi_ratio
                height: 20 *dpi_ratio
            }
            MouseArea
            {
                anchors.fill: parent;
                onPressed:
                {
                    popup.open()
                }
            }
        }
        Popup {
            id: popup
            x: 6
            y: 62
            width:220 *dpi_ratio
            height: 170 *dpi_ratio
            Rectangle {
                x: -18
                y: -13
                color: "#FFFFFF"
                width:240 *dpi_ratio
                height: 220 *dpi_ratio
                border.width: 1  * dpi_ratio;
                radius:8 * dpi_ratio
                border.color: "#E1E1E1";
                clip:true
                ListView {
                    anchors.fill: parent
                    spacing: 15 * dpi_ratio
                    model: ListModel {
                        ListElement { lang: "中文"; flag: "/Images/Chinese.png" }
                        ListElement { lang: "English"; flag: "/Images/English.png" }
                        ListElement { lang: "Việt Nam"; flag: "/Images/Vietnam.png" }
                        ListElement { lang: "แบบไทย"; flag: "/Images/Thailand.png" }
                        ListElement { lang: "Русский"; flag: "/Images/Russia.png" }
                    }
                    delegate: RowLayout {
                        Item {
                            width: 13 * dpi_ratio
                            height: parent.height
                        }
                        Rectangle {
                            width: 30 * dpi_ratio
                            height: 30 * dpi_ratio
                            Image {
                                source: model.flag
                                width: 30 * dpi_ratio
                                height: 30 * dpi_ratio
                            }
                        }
                        Item {
                            width: 5 * dpi_ratio
                            height: parent.height
                        }
                        Text {
                            text: model.lang
                            font.family: ST.fontFamilySiYan
                            font.pointSize: 20*dpi_ratio;
//                            font.weight: Font.Bold
                            color: "black"
                            verticalAlignment: Text.AlignVCenter
                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    selectedIndex = index
                                    switch (selectedIndex)
                                    {
                                    case 0:
                                        logMgr.logDataInfo4Qml("开始加载中文")
                                        configTool.languageSelected = "0"
                                        break;
                                    case 1:
                                        logMgr.logDataInfo4Qml("开始加载英文")
                                        configTool.languageSelected = "1"
                                        break;
                                    case 2:
                                        logMgr.logDataInfo4Qml("开始加载越南语")
                                        configTool.languageSelected = "2"
                                        break;
                                    case 3:
                                        logMgr.logDataInfo4Qml("开始加载泰文")
                                        configTool.languageSelected = "3"
                                        break;
                                    case 4:
                                        logMgr.logDataInfo4Qml("开始加载俄文")
                                        configTool.languageSelected = "4"
                                        break;
                                    default:
                                    }
                                    selectedImage.source = model.flag
                                    popup.close()
                                    //重启程序进行渲染
                                    configTool.restartProcess();
                                }
                            }
                        }
                    }
                }
         }
        }
    }
    GridLayout {
        x: 900 * dpi_ratio
        y: 360 * dpi_ratio
        width: 500 * dpi_ratio
        height: 370 * dpi_ratio
        columns: 2
        columnSpacing: 40 * dpi_ratio
        rowSpacing: 24 * dpi_ratio

        property int height_btn: 64 * dpi_ratio
        property int width_btn: 404 * dpi_ratio
        property int width_label: 90 * dpi_ratio
        property bool firstClick: true
        Text {
            text: qsTr("手机号")
            font.pixelSize: 22 * configTool.fontRatio * dpi_ratio
            horizontalAlignment: Text.AlignRight
            font.family: ST.fontFamilyYaHei
            color: ST.color_black
            Layout.preferredWidth: parent.width_label
        }
        CusTextField {
            id: text_field_account
            Layout.preferredWidth: parent.width_btn
            Layout.preferredHeight: parent.height_btn
            text: settingTool.getSetting(SettingEnum.USER_ACCOUNT)
            inputMethodHints: Qt.ImhDigitsOnly
            maximumLength: 11

            keyboard_status: 1

            normal_keyboard_x: 750 * dpi_ratio
            normal_keyboard_y: 573 * dpi_ratio

            digital_keyboard_x: 1372 * dpi_ratio
            digital_keyboard_y: 282 * dpi_ratio
        }
        Text {
            text: qsTr("密码")
            font.pixelSize: 22 * configTool.fontRatio * dpi_ratio
            horizontalAlignment: Text.AlignRight
            font.family: ST.fontFamilyYaHei
            color: ST.color_black
            Layout.preferredWidth: parent.width_label
        }
//        CusTextField {
//            id: text_field_passwd
//            Layout.preferredWidth: parent.width_btn
//            Layout.preferredHeight: parent.height_btn
//            echoMode: TextInput.Password
//            text: settingTool.getSetting(SettingEnum.USER_PASSWORD)

//            normal_keyboard_x: 750 * dpi_ratio
//            normal_keyboard_y: 573 * dpi_ratio

//            digital_keyboard_x: 1372 * dpi_ratio
//            digital_keyboard_y: 282 * dpi_ratio

//            keyboard_status: 1
//            onAccepted: {
//                login()
//            }
//            is_clicked_select_all: true
//        }
        TextField {
            id: text_field_passwd
            property bool firstClick: true
            Layout.preferredWidth: parent.width_btn
            Layout.preferredHeight: parent.height_btn
            echoMode: TextInput.Password
            font.family: ST.fontFamilyYaHei
            font.pointSize: 18 * dpi_ratio
            text: settingTool.getSetting(SettingEnum.USER_PASSWORD)

            background: Rectangle {
                color: ST.color_transparent
                radius: 5 * dpi_ratio // 设置圆角大小
                border.width: 1
                border.color: ST.color_grey_border2 // 初始边框颜色为透明
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    text_field_passwd.background.border.color = ST.color_green
                    if (text_field_passwd.firstClick) {
                        text_field_passwd.firstClick = false
                        text_field_passwd.forceActiveFocus()
                        text_field_passwd.selectAll()
                    }
                    else {
                        if(!text_field_passwd.firstClick)
                        {
                            text_field_passwd.forceActiveFocus()
                            if((mapToItem(text_field_passwd, mouse.x, mouse.y).x / text_field_passwd.contentWidth) >0.95 ){
                                text_field_passwd.cursorPosition = text_field_passwd.text.length
                            }
                            else{
                                var cursorPosition = mapToItem(text_field_passwd, mouse.x, mouse.y).x / text_field_passwd.contentWidth * text_field_passwd.text.length
                                text_field_passwd.cursorPosition = cursorPosition
                            }
                            }
                         }
                    window_root.keyboard_c.closeAll()
                    window_root.keyboard_c.setDigitalKeyboardPos(1372 * dpi_ratio, 282 * dpi_ratio)
                    window_root.keyboard_c.openDigitalKeyboard()
                }
            }
            onAccepted: {
                login()
            }
            onTextEdited: {

            }
            onFocusChanged: {
                if (!focus) {
                    text_field_passwd.firstClick = true
                    text_field_passwd.background.border.color = ST.color_grey_border2
                }
            }
        }
        CusSpacer {
            Layout.preferredWidth: parent.width_label
        }
        CusButton {
            id: btn_login
            Layout.preferredWidth: parent.width_btn
            Layout.preferredHeight: parent.height_btn
            text: qsTr("登录")
            onClicked: {
                login()
            }
        }
        CusSpacer {
            Layout.preferredWidth: parent.width_label
        }
    }

    CusText {
        text: utils4Qml.getVersion()
        horizontalAlignment: Text.AlignRight
        anchors.right: parent.right
        anchors.rightMargin: 70 * dpi_ratio
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 35 * dpi_ratio
    }

    CusButton {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.leftMargin: 60 * dpi_ratio
        anchors.bottomMargin: 60 * dpi_ratio

        color: ST.color_red

        width: 176 * dpi_ratio * configTool.fontRatio2
        height: 52 * dpi_ratio * configTool.fontRatio2

        CusImg {
            width: 28 * dpi_ratio
            height: width
            anchors.verticalCenter: parent.verticalCenter
            x: 23 * dpi_ratio * configTool.fontRatio2
            source: "/Images/power_off2.png"

            opacity: parent.font_opacity

            CusText {
                anchors.left: parent.right
                anchors.leftMargin: 10 * dpi_ratio
                anchors.verticalCenter: parent.verticalCenter
                text: qsTr("退出软件")
                font.pixelSize: 22 * configTool.fontRatio2 * dpi_ratio
                color: ST.color_white_pure
            }
        }

        onClicked: {
            showShutDownConfirm()
        }
    }

    function showShutDownConfirm() {
        window_root.loader_4_shutdown_confirm.sourceComponent = compo_shutdown_confirm
        window_root.loader_4_shutdown_confirm.item.open()
    }

    Component {
        id: compo_shutdown_confirm
        CusPopupConfirm {
            title_name: qsTr("退出软件")
            message_info: qsTr("确认要退出软件吗?")
            onConfirm: {
                //utils4Qml.shutdown()
                window_root.close()
            }
            onCancel: {

            }
        }
    }
    Connections {
        target: settingTool
        onLanguageChanged: {
            logMgr.logEvtInfo4Qml("刷新首页信号到来")
            btn_login.text = qsTr("登录"); // 确保你的QML文件已经使用了适当的翻译上下文
        }
    }
}
