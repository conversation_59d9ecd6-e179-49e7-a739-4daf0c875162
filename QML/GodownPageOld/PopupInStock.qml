﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import ".."

Item {
    id: popup_root

    signal sigOpen
    signal sigClose
    signal confirm(var str_num,var stockPriceCopy,var stockTotalPrice)
    function open(goods_barcode, goods_ratio = 1,goodStockPrice,goods_life,goods_unit,goods_cheng_type) {
        popup_root.goods_cheng_type = goods_cheng_type
        visible = true;
        popup_root.goods_barcode = goods_barcode;
        popup_root.goods_ratio = goods_ratio;
        popup_root.goods_life = goods_life;
        popup_root.inStockPrice = goodStockPrice;
        popup_root.goods_unit = goods_unit;
        if(visible){
            black_shadow.visible = true;
            stockNumber3_Text.forceActiveFocus();
        }
        originalTime = utils4Qml.getCurDate() + " 00:00"
        sigOpen();
    }
    function addDays(dateString, daysToAdd,operation) {
        var date = new Date(dateString);
        if(operation == "reduce"){
            date.setDate(date.getDate() - daysToAdd);
        }
        else if(operation == "add"){
            date.setDate(date.getDate() + daysToAdd);
        }
        return date.toISOString();
    }
    function close() {
        stockNumber3_Text.text = "";
        stockTotalPriceText.text = "";
        stockUnitPrice_Text.text = "";
        str_num = "0";
        stockPriceCopy = "0";
        stockTotalPriceCopy = "0";
        goods_life = "0.00";
        inStockPrice = "";
        goods_unit = "";
        originalTime = "";
        dueDateSelsect.text ="";
        goodsProdDateSelsect.text = "";
        dueDateSelsectCopy.text ="";
        goodsProdDateSelsectCopy.text = "";
        coverValue = false;
        popup_root.visible = false;
        black_shadow.visible = false;
        sigClose();
    }
    function addStrNum(str, str2) {
        if (str == "0" && str2 == "0")
            return "0"

        if (str2 == ".") {
            if (str.indexOf(".") == -1) {
                return str += str2
            }
            return str
        }

        if (str == "0")
            return str2

        str += str2

        let arr = str.split(".")

        var ret_str = arr[0]

        if (arr.length == 2) {
            ret_str = arr[0] + "." + arr[1].substring(0, 2)
        }

        return ret_str
    }
    function resetData(){
        if (stockUnitPrice_Text.activeFocus) {
               stockUnitPrice_Text.text = ""
           }
        if ( stockNumber3_Text.activeFocus) {
               stockNumber3_Text.text = ""
           }
        if (stockTotalPriceText.activeFocus) {
               stockTotalPriceText.text = ""
           }
    }
    function tryConfirm() {
        if (popup_root.str_num == "0" || popup_root.str_num == "") {
            toast.openWarn(qsTr("入库数不可为空!"))
            return
        }
        if (stockTotalPriceText.text == "0" || stockTotalPriceText.text == "") {
            toast.openWarn(qsTr("入库总价不可为空!"))
            return
        }
        if (stockUnitPrice_Text.text == "0" || stockUnitPrice_Text.text == "") {
            toast.openWarn(qsTr("入库单价不可为空!"))
            return
        }
        confirm(popup_root.str_num,popup_root.stockPriceCopy,popup_root.stockTotalPriceCopy);

        close()
    }
    function setInput(input) {
        if(coverValue == true){
            if (stockUnitPrice_Text.activeFocus) {
                   stockUnitPrice_Text.text = "";
                   stockUnitPrice_Text.text = addStrNum(stockUnitPrice_Text.text, input);
                   coverValue = false;
               }
        }
        else
        {
            if (stockUnitPrice_Text.activeFocus) {
                   stockUnitPrice_Text.text = addStrNum(stockUnitPrice_Text.text, input)
               }
        }
        if(coverValue == true){
            if (stockNumber3_Text.activeFocus) {
                   stockNumber3_Text.text = "";
                   stockNumber3_Text.text = addStrNum(stockNumber3_Text.text, input);
                   coverValue = false;
               }
        }
        else
        {
            if (stockNumber3_Text.activeFocus) {
                   stockNumber3_Text.text = addStrNum(stockNumber3_Text.text, input)
               }
        }
        if(coverValue == true){
            if (stockTotalPriceText.activeFocus) {
                   stockTotalPriceText.text = "";
                   stockTotalPriceText.text = addStrNum(stockTotalPriceText.text, input);
                   coverValue = false;
               }
        }
        else
        {
            if (stockTotalPriceText.activeFocus) {
                   stockTotalPriceText.text = addStrNum(stockTotalPriceText.text, input)
               }
        }
    }
    function openCalendar(date, hour, minute) {
        window_root.loader_4_calendar_day.sourceComponent = calendar_duedate
        popup_root.visible = false;
        window_root.loader_4_calendar_day.item.open()
        window_root.loader_4_calendar_day.item.set(date, hour, minute)
    }
    function openCalendarProd(date, hour, minute) {
        window_root.loader_4_calendar_day.sourceComponent = calendar_begin
        popup_root.visible = false;
        window_root.loader_4_calendar_day.item.open()
        window_root.loader_4_calendar_day.item.set(date, hour, minute)
    }
    Rectangle
    {
        id: black_shadow;
        anchors.fill: parent;
        color: "black";
        opacity: 0.6;
        visible: false;
        z: 3;
        MouseArea
        {
            anchors.fill: parent;
            onPressed:
            {
                close();
                }
            }
    }
    property string title_names;
    property string inStockPrice:"0.00";
    property string goods_prod: ""
    property string goodsExp: ""
    property string str_num: "0"
    property string stockPriceCopy: "0"
    property string goods_barcode: ""
    property string goods_cheng_type:""
    property real goods_ratio: 1
    property string goods_life:"0.00";
    property string originalTime: "";
    property string stockTotalPriceCopy: "0";
    property string goods_unit: "";
    property bool coverValue: false;
    Rectangle
    {
        id: stockMain;
        width: 936  * dpi_ratio;
        height: 875  * dpi_ratio;
        anchors.horizontalCenter: parent.horizontalCenter;
        anchors.verticalCenter: parent.verticalCenter;
        visible: true;
        radius: 20  * dpi_ratio;
        enabled: stockMain.visible;
        color: "#FFFFFF";
        z:6;
        onVisibleChanged: {
            stockNumber3_Text.forceActiveFocus();
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {

            }
        }
        Rectangle
        {
            id:title_bar
            width: 936  * dpi_ratio;
            height: 113  * dpi_ratio;
            anchors.top: parent.top;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            radius: 10  * dpi_ratio;
            Rectangle
            {
                id:title_name
                width: 260  * dpi_ratio;
                height: 50  * dpi_ratio;
                anchors.top: parent.top;
                anchors.horizontalCenter: parent.horizontalCenter;
                anchors.verticalCenter: parent.verticalCenter;
                color: "#FFFFFF";
                radius: 10  * dpi_ratio;
                Text
                {
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 24  * dpi_ratio;
                    color: "black";
                    text: title_names;
                    font.bold: true ;
                }
            }
            Rectangle
            {
                id:closeRoot
                width: 70  * dpi_ratio;
                height: 70  * dpi_ratio;
                anchors.top: parent.top;
                anchors.right: parent.right;
                color: "#FF5D40";
                radius: 10  * dpi_ratio;
                Text
                {
                    id: closeText;
                    anchors.centerIn: parent;
                    text: "X";
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 28 * dpi_ratio;
                    color: "#FFFFFF";
                }
                MouseArea
                {
                    anchors.fill: parent;

                    onPressed:
                    {
                        close();
                    }
                }
            }
        }

        Rectangle
        {
            id:stockNumber
            width: 936  * dpi_ratio;
            height: 100  * dpi_ratio;
            anchors.top: title_bar.bottom;
            anchors.left: parent.left;
            color: "#FFFFFF";
            Rectangle
            {
                id:stockNumber1;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                //anchors.horizontalCenter: parent.horizontalCenter;
                anchors.left: parent.left;
                anchors.leftMargin: 99 * dpi_ratio;
                color: "#FFFFFF";
                Text
                {
                    text: qsTr("入库数量");
                    font.family: ST.fontFamilySiYan;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#000000";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.left
                        font.pixelSize: 33 * dpi_ratio
                        visible: true
                    }
                }
            }
            Rectangle
            {
                id:stockNumber2
                width: 54  * dpi_ratio;
                height: 54  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left:stockNumber1.right;
                anchors.leftMargin: 20  * dpi_ratio;
                color: "#FFFFFF";
                radius: 10  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#999999";
                Image
                {
                    id:stockNumber2Img
                    width: 34 * dpi_ratio;
                    height: 4 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    source: "/Images/jianFlag.png";
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onPressed:
                    {
                        if(stockNumber3_Text.text != ""){
                            stockNumber3_Text.text = stockNumber3_Text.text*1 - 1;
                        }
                    }
                }
            }
            Rectangle
            {
                id:stockNumber3;
                width:430  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.left:stockNumber2.right;
                anchors.leftMargin: 20  * dpi_ratio;
                anchors.bottom: stockNumber1.bottom;
                color: "#FFFFFF";
                border.width: 1  * dpi_ratio;
                border.color: "#D8D8D8";
                radius: 10  * dpi_ratio;
                Text
                {
                    width: 140  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 18  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 17 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#999999";
                    text:qsTr("请输入入库数量");
                    visible:stockNumber3_Text.text == "";
                }
                Text
                {
                    width: 64  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 18  * dpi_ratio;
                    anchors.right: parent.right;
                    anchors.rightMargin: 10 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#000000";
                    text:goods_unit;
                }
                TextInput
                {
                    id: stockNumber3_Text;
                    width: 430  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 17 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 24 * dpi_ratio;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#333333";
                    maximumLength: 8;
                    clip: true;
                    validator: RegularExpressionValidator {
                        regularExpression: /^\d{1,4}(\.\d{1,2})?$/
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            parent.forceActiveFocus();
                            parent.selectAll();
                            coverValue = true;

                        }
                    }
                    onAccepted:
                    {

                    }
                    onTextChanged:
                    {
                        if(popup_root.goods_cheng_type == "0"){
                            if(text[text.length - 1] === '.'){
                                stockNumber3_Text.text = stockNumber3_Text.text.slice(0, -1);
                            }
                        }
                        if (stockNumber3_Text.text*1 > 99999.99) {
                            stockNumber3_Text.text = stockNumber3_Text.text.slice(0, -1);
                        }
                        if(stockNumber3_Text.text == "+")
                        {
                            stockNumber3_Text.text = "";
                        }
                        if(stockNumber3_Text.text*1 < 0){
                            stockNumber3_Text.text = 0;
                        }
                        str_num = stockNumber3_Text.text*1;
                    }
                }
            }
            Rectangle
            {
                id:stockNumber4
                width: 54  * dpi_ratio;
                height: 54  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left:stockNumber3.right;
                anchors.leftMargin: 20  * dpi_ratio;
                color: "#00BD75";
                radius: 10  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#999999";
                Image
                {
                    width: 34 * dpi_ratio;
                    height: 34 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    source: "/Images/jiaFlag.png";
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onPressed:
                    {
                        if(stockNumber3_Text.text !== ""){
                            stockNumber3_Text.text = stockNumber3_Text.text*1  +  1;
                        }
                        else if(stockNumber3_Text.text === ""){
                            stockNumber3_Text.text = 1;
                        }
                    }
                }
            }
        }
        Rectangle
        {
            id:stockPrice
            width: 936  * dpi_ratio;
            height: 80  * dpi_ratio;
            anchors.top: stockNumber.bottom;
            anchors.left: parent.left;
            color: "#FFFFFF";
            Rectangle
            {
                id:stockTotalPrice;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                //anchors.horizontalCenter: parent.horizontalCenter;
                anchors.left: parent.left;
                anchors.leftMargin: 99 * dpi_ratio;
                color: "#FFFFFF";
                Text
                {
                    text: qsTr("入库总价");
                    font.family: ST.fontFamilySiYan;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#000000";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.left
                        font.pixelSize: 33 * dpi_ratio
                        visible: true
                    }
                }
            }
            Rectangle
            {
                id:stockTotalPrice2;
                width:240  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.left:stockTotalPrice.right;
                anchors.leftMargin: 20  * dpi_ratio;
                anchors.bottom: stockTotalPrice.bottom;
                color: "#FFFFFF";
                border.width: 1  * dpi_ratio;
                border.color: "#D8D8D8";
                radius: 10  * dpi_ratio;
                Text
                {
                    width: 140  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 18  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 20 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#999999";
                    text:qsTr("请输入");
                    visible:stockTotalPriceText.text == "";
                }
                TextInput
                {
                    id: stockTotalPriceText;
                    width: 240  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 20 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 24 * dpi_ratio;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#333333";
                    maximumLength: 12;
                    enabled:stockNumber3_Text.text*1 > 0;
                    validator: RegularExpressionValidator {
                        regularExpression: /^\d{1,8}(\.\d{1,2})?$/
                    }
                    clip: true;
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            parent.forceActiveFocus();
                            parent.selectAll();
                            coverValue = true;

                        }
                    }
                    onAccepted:
                    {

                    }
                    onFocusChanged: {
                        if (!focus) {
                            if(stockTotalPriceText.text != ""  || stockUnitPrice_Text.text != ""
                                    || stockNumber3_Text.text != "" || stockNumber3_Text.text != "0")
                            {
                                stockUnitPrice_Text.text = Math.abs((stockTotalPriceText.text*1 / stockNumber3_Text.text*1)).toFixed(2);
                            }
                        }
                    }
                    onTextChanged:
                    {
                        if(stockTotalPriceText.text == "NaN")
                        {
                            stockTotalPriceText.text = "";
                        }
                        if(stockTotalPriceText.text == "0")
                        {
                            stockTotalPriceText.text = "0.00";
                        }
                        if(stockTotalPriceText.text == "0.00")
                        {
                            stockTotalPriceText.text = "";
                        }
                        stockTotalPriceCopy = stockTotalPriceText.text;
                    }
                }
            }
            Rectangle
            {
                id:stockUnitPrice;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                //anchors.horizontalCenter: parent.horizontalCenter;
                anchors.left: stockTotalPrice2.right;
                anchors.leftMargin: 32 * dpi_ratio;
                color: "#FFFFFF";
                Text
                {
                    text: qsTr("入库单价");
                    font.family: ST.fontFamilySiYan;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#000000";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.left
                        font.pixelSize: 33 * dpi_ratio
                        visible: true
                    }
                }
            }
            Rectangle
            {
                width:160  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.left:stockUnitPrice.right;
                anchors.leftMargin: 20  * dpi_ratio;
                anchors.bottom: stockUnitPrice.bottom;
                color: "#FFFFFF";
                border.width: 1  * dpi_ratio;
                border.color: "#D8D8D8";
                radius: 10  * dpi_ratio;
                Text
                {
                    width: 140  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 18  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 20 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#999999";
                    text:qsTr("请输入");
                    visible:stockUnitPrice_Text.text == "";
                }
                TextInput
                {
                    id: stockUnitPrice_Text;
                    width: 160  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 20 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 24 * dpi_ratio;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#333333";
                    maximumLength: 8;
                    validator: RegularExpressionValidator {
                        regularExpression: /^\d{1,4}(\.\d{1,2})?$/
                    }
                    enabled:stockNumber3_Text.text*1 > 0;
                    clip: true;
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            parent.forceActiveFocus();
                            parent.selectAll();
                            coverValue = true;

                        }
                    }
                    onAccepted:
                    {

                    }
                    onFocusChanged: {
                        if(!focus){
                            if(stockTotalPriceText.text != ""  || stockUnitPrice_Text.text != ""
                                    || stockNumber3_Text.text != "" || stockNumber3_Text.text != "0")
                            {
                             stockTotalPriceText.text = Math.abs((stockUnitPrice_Text.text*1 * stockNumber3_Text.text*1)).toFixed(2);
                            }
                        }
                    }

                    onTextChanged:
                    {
                        stockPriceCopy = stockUnitPrice_Text.text;
                        if(stockUnitPrice_Text.text == "NaN")
                        {
                            stockUnitPrice_Text.text = "";
                        }
                        if(stockUnitPrice_Text.text == "0")
                        {
                            stockUnitPrice_Text.text = "0.00";
                        }
                        if(stockUnitPrice_Text.text == "0.00")
                        {
                            stockUnitPrice_Text.text = "";
                        }
                    }
                }
            }
        }
        Rectangle
        {
            id:recentInStockPrice
            width: 936  * dpi_ratio;
            height: 50  * dpi_ratio;
            anchors.top: stockPrice.bottom;
            anchors.topMargin: 10 * dpi_ratio;
            anchors.left: parent.left;
            color: "#FFFFFF";
            Rectangle
            {
                id:recentInStockPrice1
                width: 350  * dpi_ratio;
                height: 50  * dpi_ratio;
                anchors.right: parent.right;
                anchors.rightMargin: 108 * dpi_ratio;
                anchors.verticalCenter: parent.verticalCenter;
                color: "#FFFFFF";
                Text{
                    color: "#FF3835";
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.right: parent.right;
                    font.pointSize: 20 * dpi_ratio;
                    text:qsTr("最近入库价: ")+inStockPrice+qsTr("元");
                }
            }

        }
        Rectangle
        {
            id:goodsProdRec
            width: 936  * dpi_ratio;
            height: 80  * dpi_ratio;
            anchors.top: recentInStockPrice.bottom;
            anchors.left: parent.left;
            color: "#FFFFFF";
            Rectangle
            {
                id:goodsProdRecText;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                //anchors.horizontalCenter: parent.horizontalCenter;
                anchors.left: parent.left;
                anchors.leftMargin: 99 * dpi_ratio;
                color: "#FFFFFF";
                Text
                {
                    text: qsTr("生产日期");
                    font.family: ST.fontFamilySiYan;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#000000";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                }
            }
            Rectangle
            {
                id:goodsProdRecText2;
                width:570  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.left:goodsProdRecText.right;
                anchors.leftMargin: 20  * dpi_ratio;
                anchors.bottom: goodsProdRecText.bottom;
                color: "#FFFFFF";
                border.width: 1  * dpi_ratio;
                border.color: "#D8D8D8";
                radius: 10  * dpi_ratio;
                Image
                {
                    id:goodsProdImg
                    width: 29 * dpi_ratio;
                    height: 29 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.left: parent.left;
                    anchors.leftMargin: 24 * dpi_ratio;
                    source: "/Images/dateSelect.png";
                }
                Text
                {
                    id:goodsProdRecText3
                    width: 190  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 18  * dpi_ratio;
                    anchors.left: goodsProdImg.right;
                    anchors.leftMargin: 24 * dpi_ratio;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#999999";
                    text:qsTr("请选择生产日期");
                    visible:goodsProdDateSelsect.text == "";
                }
                Text
                {

                    width: 240  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 18  * dpi_ratio;
                    anchors.left: goodsProdRecText3.right;
                    anchors.leftMargin: 7 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#999999";
                    text:qsTr("(保质期:")+goods_life+qsTr("天)");
                    visible:goodsProdDateSelsectCopy.text == "" &&goodsProdDateSelsect.text == "";
                }
                CusText {
                    id: goodsProdDateSelsectCopy
                    visible:false
                    anchors.centerIn: parent
                    onTextChanged: {
                        goodsProdDateSelsectCopy.text = text
                        goodsProdDateSelsect.text  = text.split(" ")[0]
                    }

                }
                CusText {
                    id: goodsProdDateSelsect
                    anchors.centerIn: parent
                    property bool is_need_init: true
                    onTextChanged: {
                        goodsProdDateSelsect.text = text
                        popup_root.goods_prod  = text
                        var dateString = text;
                        var date = new Date(dateString);
                        if (!isNaN(date)) {
                            var newDate = addDays(date, Math.floor(parseFloat(goods_life) ),"add");
                            dueDateSelsect.text = Qt.formatDate(newDate, "yyyy-MM-dd");
                        }
                    }
                    Component.onCompleted: {
                        is_need_init = true
                        //text = utils4Qml.getCurDate() + " 00:00"
                        is_need_init = false
                    }
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {

                        var date_time_vec = originalTime.split(" ")

                        if (date_time_vec.length == 2) {

                            var date_vec = date_time_vec[0].split("-")
                            var year = date_vec[0]
                            var month = date_vec[1]
                            var day = date_vec[2]

                            var time_vec = date_time_vec[1].split(":")
                            var hour = time_vec[0]
                            var minute = time_vec[1]

                            openCalendarProd(date_time_vec[0], hour, minute)
                        }
                    }
                }
                Component {
                    id: calendar_begin
                    CusDateTimePickerDay {
                        z: 100
                        is_floor: true
                        target_control: goodsProdDateSelsectCopy
                        onSigDateClose: {
                            popup_root.visible = true;
                        }
                    }
                }
            }
        }
        Rectangle
        {
            id:dueDate
            width: 936  * dpi_ratio;
            height: 100  * dpi_ratio;
            anchors.top: goodsProdRec.bottom;
            anchors.left: parent.left;
            color: "#FFFFFF";
            Rectangle
            {
                id:dueDateRecText;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left: parent.left;
                anchors.leftMargin: 99 * dpi_ratio;
                color: "#FFFFFF";
                Text
                {
                    text: qsTr("到期日期");
                    font.family: ST.fontFamilySiYan;
                    anchors.left: dueDateImg.right;
                    anchors.leftMargin: 24 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.bottom: dueDateImg.bottom;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#000000";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                }
            }
            Rectangle
            {
                id:dueDateRecText2;
                width:570  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.left:dueDateRecText.right;
                anchors.leftMargin: 20  * dpi_ratio;
                anchors.bottom: dueDateRecText.bottom;
                color: "#FFFFFF";
                border.width: 1  * dpi_ratio;
                border.color: "#D8D8D8";
                radius: 10  * dpi_ratio;
                Image
                {
                    id:dueDateImg
                    width: 29 * dpi_ratio;
                    height: 29 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.left: parent.left;
                    anchors.leftMargin: 24 * dpi_ratio;
                    source: "/Images/dateSelect.png";
                }
                Text
                {
                    id:dueDateRecText3
                    width: 140  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    font.family: ST.fontFamilySiYan;
                    font.pointSize: 18  * dpi_ratio;
                    anchors.left: dueDateImg.right;
                    anchors.leftMargin: 24 *dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#999999";
                    text:qsTr("请选择到期日期");
                    visible:dueDateSelsectCopy.text == "" &&dueDateSelsect.text == "";
                }
                CusText {
                    id: dueDateSelsectCopy
                    visible:false
                    anchors.centerIn: parent
                    onTextChanged: {
                        dueDateSelsectCopy.text = text
                        dueDateSelsect.text  = text.split(" ")[0]
                    }

                }
                CusText {
                    id: dueDateSelsect
                    anchors.centerIn: parent
                    property bool is_need_init: true
                    onTextChanged: {
                        dueDateSelsect.text = text
                        logMgr.logDataInfo4Qml("goodsProdDateSelsect===1:{}",dueDateSelsect.text)
                        popup_root.goodsExp  = text
                        logMgr.logDataInfo4Qml("goodsProdDateSelsect===2:{}",goods_life)
                        var dateString = text;
                        var date = new Date(dateString);
                        if (!isNaN(date)) {
                            var newDate = addDays(date, Math.floor(parseFloat(goods_life)),"reduce");
                            logMgr.logDataInfo4Qml("goodsProdDateSelsect===3:{}",newDate)
                            goodsProdDateSelsect.text = Qt.formatDate(newDate, "yyyy-MM-dd");
                        }
                    }
                    Component.onCompleted: {
                        is_need_init = false
                    }
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {

                        var date_time_vec = originalTime.split(" ")

                        if (date_time_vec.length == 2) {

                            var date_vec = date_time_vec[0].split("-")
                            var year = date_vec[0]
                            var month = date_vec[1]
                            var day = date_vec[2]

                            var time_vec = date_time_vec[1].split(":")
                            var hour = time_vec[0]
                            var minute = time_vec[1]

                            openCalendar(date_time_vec[0], hour, minute)
                        }
                    }
                }
                Component {
                    id: calendar_duedate
                    CusDateTimePickerDay {
                        is_floor: true
                        target_control: dueDateSelsectCopy
                        onSigDateClose: {
                            popup_root.visible = true;
                        }
                    }
                }
            }
        }

        CusRect {
            id: payKeyboardDigital;
            width: 720*dpi_ratio;
            height: 316*dpi_ratio;
            color: "#ffffff";
            visible: true;
            anchors.top: dueDate.bottom;
            anchors.left: parent.left;
            anchors.leftMargin: 99 * dpi_ratio;
            radius: 10 *dpi_ratio;

            RowLayout {
                anchors.fill: parent

                CusRect {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    color: ST.color_transparent

                    GridLayout {
                        id: gl_calc_btns

                        anchors.fill: parent
                        columns: 4
                        rows: 3

                        columnSpacing: 5 * dpi_ratio
                        rowSpacing: 5 * dpi_ratio

                        property real col_width: (width - ((columns - 1) * rowSpacing)) / columns
                        property real col_height: (height - ((rows - 1) * columnSpacing)) / rows

                        function prefWidth(item) {
                            return col_width * item.Layout.columnSpan
                        }
                        function prefHeight(item) {
                            return col_height * item.Layout.rowSpan
                        }

                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "7"


                        }

                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "8"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "9"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "0"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "4"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "5"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "6"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "."
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "1"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "2"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: "3"
                        }
                        KeyRect {
                            Layout.preferredWidth: parent.prefWidth(this)
                            Layout.preferredHeight: parent.prefHeight(this)
                            key_text: qsTr("清空")
                            onPressKey: {
                                resetData()
                            }
                        }
                    }
                }
                CusRect {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 130 * dpi_ratio
                    color: ST.color_transparent

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 10 * dpi_ratio

                        property real cell_height: gl_calc_btns.col_height

                        KeyRect2 {
                            Layout.preferredHeight: parent.cell_height
                            Layout.fillWidth: true
                            key_text: ""
                            color: "#FFFFFF"
                            text.color: "#FFFFFF"
                            Image {
                                anchors.fill: parent
                                source: "/Images/tuige.png"
                                fillMode: Image.PreserveAspectFit
                                scale: 0.5
                            }
                            onPressKey: {
                                if (stockUnitPrice_Text.activeFocus) {
                                       stockUnitPrice_Text.text = stockUnitPrice_Text.text.slice(0, -1);
                                   }
                                if (stockTotalPriceText.activeFocus) {
                                       stockTotalPriceText.text = stockTotalPriceText.text.slice(0, -1);
                                   }
                                if (stockNumber3_Text.activeFocus) {
                                       stockNumber3_Text.text = stockNumber3_Text.text.slice(0, -1);
                                   }
                            }
                        }
                        KeyRect2 {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            key_text: qsTr("入库")
                            color: "#00BD75"
                            text.color: "white"
                            onPressKey: {
//                                if (is_weight) {
//                                    shoppng_cart.addNoCodeGoodsByBarcode(EnumTool.ID_NO_CODE_WEIGHT_GOODS, unit_price_str, quantity)
//                                } else {
//                                    shoppng_cart.addNoCodeGoodsByBarcode(EnumTool.ID_NO_CODE_GOODS, unit_price_str, quantity)
//                                }
                                //close()
                            }
                        }
                    }
                }
            }
        }
        component KeyRect: CusRect {
            id: rect_key
            property string key_text: "0"
            signal pressKey(var key)
            color: ST.color_transparent
            radius: 10 *dpi_ratio;

            border {
                width: 2 * dpi_ratio
                color: "#E5E5E5"
            }

            CusText {
                id:rect_key_id
                anchors.centerIn: parent
                text: rect_key.key_text
                font.weight: Font.Bold
                font.pixelSize: 28 * dpi_ratio
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    rect_key.color = "#17262B";
                    rect_key_id.color = "white";
                    clickAmation.start();
                    rect_key.pressKey(rect_key.key_text)
                }
            }
            Timer
            {
                id: clickAmation;
                running: false;
                repeat: false;
                interval: 100
                onTriggered:
                {
                    rect_key.color = "white";
                    rect_key_id.color = "black";
                }
            }
            onPressKey: {
                setInput(key)
            }
        }
        component KeyRect2: CusRect {
            id: rect_key2
            property string key_text: "0"
            signal pressKey(var key)

            property alias text: text
            color: ST.color_transparent
            radius: 10 *dpi_ratio;

            border {
                width: 2 * dpi_ratio
                color: "#E5E5E5"
            }
            CusText {
                id: text
                anchors.centerIn: parent
                text: rect_key2.key_text
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    rect_key2.color = "#17262B";
                    text.color = "white";
                    clickAmation2.start();
                    rect_key2.pressKey(rect_key2.key_text)
                    if(rect_key2.key_text == qsTr("入库")){
                    }
                }
            }
            Timer
            {
                id: clickAmation2;
                running: false;
                repeat: false;
                interval: 100
                onTriggered:
                {
                    if(rect_key2.key_text == qsTr("入库")){
                        rect_key2.color = "#00BD75";
                        text.color = "white";
                        stockNumber3_Text.forceActiveFocus();
                        tryConfirm();
                    }
                    else{
                        rect_key2.color = "white";
                        text.color = "black";
                    }

                }
            }
        }
    }

}
