﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import "../.."
import ".."

Item {
    id: popup_root

    function open() {
        popup_root.visible = true
        resetAll()
        supplierName.focusMe()
    }
    function close() {
        popup_root.visible = false
        keyboard_c.closeAll()
        sigClose()
    }

    function resetAll() {
        supplierName.text = ""
    }

    signal confirm(var supplierName)
    signal cancel
    signal sigClose

    property string title_name: qsTr("新增供货商")

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 700 * dpi_ratio
        height: 350 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white_pure
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 100 * dpi_ratio
                Layout.fillWidth: true
                color: ST.color_transparent
                RowLayout {
                    anchors.fill: parent
                    spacing: 60

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 5 * dpi_ratio
                color: ST.color_white_pure

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 1 * dpi_ratio

                    CusRect {
                        id:supplierCus
                        Layout.fillWidth: true
                        Layout.preferredHeight: 60 * dpi_ratio
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                id:suprec
                                Layout.fillHeight: true
                                anchors.left:parent.left
                                anchors.leftMargin: 80 *dpi_ratio
                                Layout.preferredWidth: 90 * dpi_ratio
                                color: ST.color_transparent
                                CusText {
                                    text: "<font color='red'>*</font>" + qsTr("     供货商")
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }
                            CusRect {
                                id:supplirText
                                anchors.left: suprec.right
                                anchors.leftMargin: 10 *dpi_ratio
                                height:50 *dpi_ratio
                                width:450 *dpi_ratio
                                color: ST.color_transparent
                                CusTextField {
                                    id: supplierName
                                    anchors.fill: parent
                                    back_rect.border.color: "#888888"
                                    is_clicked_select_all: true
                                    keyboard_status: 1
                                    digital_keyboard_x: 1320 * dpi_ratio
                                    digital_keyboard_y: 440 * dpi_ratio
                                    placeholderText :"请输入供货商名称"
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 70 * dpi_ratio
                        anchors.top: supplierCus.bottom
                        anchors.topMargin: 30 *dpi_ratio
                        color: ST.color_transparent
                            CusButton {
                                id:cancleBtu
                                text: qsTr("取消")
                                width:160 *dpi_ratio
                                height: 50 *dpi_ratio
                                anchors.left: parent.left
                                anchors.leftMargin: 180 *dpi_ratio
                                onClicked: {
                                    cancel()
                                    close()
                                }
                            }
                            CusButton {
                                id:saveBtu
                                text: qsTr("保存")
                                height: 50 *dpi_ratio
                                width:160 *dpi_ratio
                                anchors.left: cancleBtu.right
                                anchors.leftMargin: 30 *dpi_ratio
                                onClicked: {
                                    if(supplierName.text != ""){
                                        confirm(supplierName.text)
                                        close()
                                    }else{
                                        toast.openInfo(qsTr("供应商名称不可为空!"))
                                    }
                                }
                            }
                    }
                }
            }
        }
    }

    component KeyRect: CusRect {
        id: rect_key
        property string key_text: "0"
        signal pressKey(var key)
        color: ST.color_transparent

        border {
            width: 1 * dpi_ratio
            color: ST.color_grey_border
        }

        CusText {
            anchors.centerIn: parent
            text: rect_key.key_text
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {
                rect_key.pressKey(rect_key.key_text)
            }
        }

        onPressKey: {
            setInput(key)
        }
    }
    component KeyRect2: CusRect {
        id: rect_key2
        property string key_text: "0"
        signal pressKey(var key)

        property alias text: text

        CusText {
            id: text
            anchors.centerIn: parent
            text: rect_key2.key_text
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {
                rect_key2.pressKey(rect_key2.key_text)
            }
        }
    }
}
