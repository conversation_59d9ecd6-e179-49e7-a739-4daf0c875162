﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import ".."
import EnumTool 1.0
import SortFilterProxyModel 0.2


Item {
    id: popup_root

    signal sigOpen
    signal sigClose
    signal confirm(var str_num,var stockPriceCopy,var goodBatchMessage,var stockOutColorType,var stockTotalPrice)

    function open(goods_barcode, goods_ratio = 1,GoodsBarcodes,goods_unit,good_stock_price,goods_cheng_type) {
        visible = true;
        goodsBarcodes = GoodsBarcodes;
        popup_root.goods_barcode = goods_barcode;
        popup_root.goods_ratio = goods_ratio;
        popup_root.goods_unit = goods_unit;
        popup_root.goods_cheng_type = goods_cheng_type;
        stockUnitPrice_Text.text = good_stock_price;
        if (visible) {
            resetAllInfo()
            reqStockRecord()
        }
        if(visible){
            black_shadow.visible = true;
            stockNumber3_Text.forceActiveFocus();
        }
        sigOpen();
    }
    function resetData(){
        if((!stockUnitPrice_Text.activeFocus)&&(!stockNumber3_Text.activeFocus)&&(!stockTotalPriceText.activeFocus)){
            delegateTextInputfocus = true;
        }else{
            delegateTextInputfocus = false;
        }
        if ((textInputIndex >= 0) && (textInputIndex < lv_stock_record.model.count) && (delegateTextInputfocus == true)) {
            lv_stock_record.model.setProperty(textInputIndex, "goodsBatchId", 0);
        }
        if (stockUnitPrice_Text.activeFocus) {
               stockUnitPrice_Text.text = ""
           }
        if ( stockNumber3_Text.activeFocus) {
               stockNumber3_Text.text = ""
           }
        if (stockTotalPriceText.activeFocus) {
               stockTotalPriceText.text = ""
           }
    }
    function addStrNum(str, str2) {
        if (str == "0" && str2 == "0")
            return "0"

        if (str2 == ".") {
            if (str.indexOf(".") == -1) {
                return str += str2
            }
            return str
        }

        if (str == "0")
            return str2

        str += str2

        let arr = str.split(".")

        var ret_str = arr[0]

        if (arr.length == 2) {
            ret_str = arr[0] + "." + arr[1].substring(0, 2)
        }

        return ret_str
    }
    function close() {
        stockNumber3_Text.text = "";
        stockTotalPriceText.text = "";
        stockUnitPrice_Text.text = "";
        stockOutColorType = "";
        black_shadow.visible = false;
        stockPriceCopy = "0";
        stockTotalPriceCopy = "0";
        str_num = "0";
        popup_root.visible = false;
        batchOutStock = [];
        cleanInit = false;
        delegateTextInputfocus = true;
        sigClose();
        coverValue = false;
        goods_unit = "";
    }
    function resetAllInfo() {
        lm_stock_record.clear()
    }
    function cleanGoodsBatchIdCopy(){
        for( var indexClean = 0;  indexClean < lv_stock_record.model.count; ++indexClean)
        {
            lv_stock_record.model.setProperty(indexClean, "goodsBatchId", "");
        };
    }
    function tryConfirm() {
        if (popup_root.str_num == "0" || popup_root.str_num == "") {
            toast.openWarn(qsTr("出库数不可为空!"))
            return
        }
         logMgr.logDataInfo4Qml("stockTotalPriceText.text:{}",stockTotalPriceText.text);
        if (stockTotalPriceText.text == "0" || stockTotalPriceText.text === "") {
            toast.openWarn(qsTr("出库总价不可为空!"))
            return
        }
        if (stockUnitPrice_Text.text == "0" || stockUnitPrice_Text.text === "") {
            toast.openWarn(qsTr("出库单价不可为空!"))
            return
        }
        if (stockOutColorType == "" ) {
            toast.openWarn(qsTr("出库类型未选择!"))
            return
        }
        if(batchOutStock.length > 0){
            goodBatchMessage = JSON.stringify(batchOutStock);
            logMgr.logDataInfo4Qml("goodBatchMessage:{}",goodBatchMessage)
            confirm(popup_root.str_num,popup_root.stockPriceCopy,popup_root.goodBatchMessage,popup_root.stockOutColorType,popup_root.stockTotalPriceCopy)
        }
        else{
            confirm(popup_root.str_num,popup_root.stockPriceCopy,"",popup_root.stockOutColorType,popup_root.stockTotalPriceCopy)
        }



        close()
    }
    function setInput(input) {
        if((!stockUnitPrice_Text.activeFocus)&&(!stockNumber3_Text.activeFocus)&&(!stockTotalPriceText.activeFocus)){
            delegateTextInputfocus = true;
        }else{
            delegateTextInputfocus = false;
        }
        if ((textInputIndex >= 0) && (textInputIndex < lv_stock_record.model.count) && (delegateTextInputfocus == true)) {
           logMgr.logDataInfo4Qml("input:{}",input)
           if(coverValue == true){
               lv_stock_record.model.setProperty(textInputIndex, "goodsBatchId", "0");
               coverValue = false;
           }
           var currentGoodsBatchId = (lv_stock_record.model.get(textInputIndex)).goodsBatchId;
           if(input === "."){
               if ((currentGoodsBatchId.toString()).indexOf(".") == -1) {
                   currentGoodsBatchId = currentGoodsBatchId.toString() + "."
                   lv_stock_record.model.setProperty(textInputIndex, "goodsBatchId", currentGoodsBatchId.toString());
               }else{
               }               
            }
            else{
               //logMgr.logDataInfo4Qml("currentGoodsBatchId:{}",currentGoodsBatchId.toString())
                if (isNaN(currentGoodsBatchId)) {
                    currentGoodsBatchId = input;
                } else {
                    if(Number(currentGoodsBatchId.toString() + input.toString()) <= Number((lv_stock_record.model.get(textInputIndex)).goodsCount))
                    {
                        var outStockCountSum = 0;
                        for (var i = 0; i < batchOutStock.length; i++) {
                            var item = batchOutStock[i];
                            if(item.batchUnique !== ((lv_stock_record.model.get(textInputIndex)).batchUnique)){
                                outStockCountSum += Number(item.outStockCount);
                            }

                        }
                        outStockCountSum = Number(outStockCountSum)+Number(currentGoodsBatchId.toString() +input.toString())                        
                        if(Number(outStockCountSum ) <= stockNumber3_Text.text*1){
                           currentGoodsBatchId = currentGoodsBatchId.toString() + input.toString();
                           lv_stock_record.model.setProperty(textInputIndex, "goodsBatchId", currentGoodsBatchId.toString());                           
                        }else{
                           toast.openWarn(qsTr("所有出库数之和不得大于商品出库数量!"))
                        }
                        outStockCountSum = 0;
                    }else{
                        toast.openWarn(qsTr("批次出库数不得大于库存剩余量!"))
                    }
                }
            }
        }
        if(coverValue == true){
            if (stockUnitPrice_Text.activeFocus) {
                   stockUnitPrice_Text.text = "";
                   stockUnitPrice_Text.text = addStrNum(stockUnitPrice_Text.text, input);
                   coverValue = false;
               }
        }
        else
        {
            if (stockUnitPrice_Text.activeFocus) {
                   stockUnitPrice_Text.text = addStrNum(stockUnitPrice_Text.text, input)
               }
        }
        if(coverValue == true){
            if (stockNumber3_Text.activeFocus) {
                   stockNumber3_Text.text = "";
                   stockNumber3_Text.text = addStrNum(stockNumber3_Text.text, input);
                   coverValue = false;
               }
        }
        else
        {
            if (stockNumber3_Text.activeFocus) {
                   stockNumber3_Text.text = addStrNum(stockNumber3_Text.text, input)
               }
        }
        if(coverValue == true){
            if (stockTotalPriceText.activeFocus) {
                   stockTotalPriceText.text = "";
                   stockTotalPriceText.text = addStrNum(stockTotalPriceText.text, input);
                   coverValue = false;
               }
        }
        else
        {
            if (stockTotalPriceText.activeFocus) {
                   stockTotalPriceText.text = addStrNum(stockTotalPriceText.text, input)
               }
        }
    }
    function reqStockRecord() {
        orderControl.getGoodBatchListQml(function (data) {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            lm_stock_record.clear()
            for (var i = 0; i < json_doc_data.length; ++i) {
                lm_stock_record.append(json_doc_data[i])
            }

            lv_stock_record.currentIndex = -1
        },1,100,1,popup_root.goods_barcode)
    }
    Rectangle
    {
        id: black_shadow;
        anchors.fill: parent;
        color: "black";
        opacity: 0.6;
        visible: false;
        z: 3;
        MouseArea
        {
            anchors.fill: parent;
            onPressed:
            {
                close();
                }
            }
    }
    property string title_names;
    property string goodsBarcodes;
    property var batchOutStock:[];
    property var textInputIndex;
    property bool cleanInit:false;
    property bool delegateTextInputfocus:false;
    property string stockPriceCopy: "0"
    property string stockTotalPriceCopy: "0";
    property string goods_barcode: ""
    property real goods_ratio: 1
    property string str_num: "0"
    property string stockOutColorType:"";
    property string goodBatchMessage:"";
    property bool coverValue: false;
    property string goods_unit: "";
    property string goods_cheng_type:"";

    onVisibleChanged: {
    }
    Rectangle
    {
        id: stockMain;
        width: 1700  * dpi_ratio;
        height: 734  * dpi_ratio;
        anchors.horizontalCenter: parent.horizontalCenter;
        anchors.verticalCenter: parent.verticalCenter;
        visible: true;
        radius: 20  * dpi_ratio;
        enabled: stockMain.visible;
        color: "#FFFFFF";
        z:6;
        onVisibleChanged: {
        }
        MouseArea {
            anchors.fill: parent
            onClicked: {

            }
        }
        Rectangle
        {
            id:title_bar
            width: 1700  * dpi_ratio;
            height: 113  * dpi_ratio;
            anchors.top: parent.top;
            //anchors.topMargin: 30 * dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            color: "#FFFFFF";
            radius: 10  * dpi_ratio;
            Rectangle
            {
                id:title_name
                width: 260  * dpi_ratio;
                height: 50  * dpi_ratio;
                anchors.top: parent.top;
                anchors.left: parent.left;
                anchors.horizontalCenter: parent.horizontalCenter;
                anchors.verticalCenter: parent.verticalCenter;
                color: "#FFFFFF";
                radius: 10  * dpi_ratio;
                Text
                {
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 24  * dpi_ratio;
                    color: "black";
                    text: title_names;
                    font.bold: true ;
                }
            }
            Rectangle
            {
                id:closeRoot
                width: 70  * dpi_ratio;
                height: 70  * dpi_ratio;
                anchors.top: parent.top;
                anchors.right: parent.right;
                color: "#FF5D40";
                radius: 10  * dpi_ratio;
                Text
                {
                    id: closeText;
                    anchors.centerIn: parent;
                    text: "X";
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 28 * dpi_ratio;
                    color: "#FFFFFF";
                }
                MouseArea
                {
                    anchors.fill: parent;

                    onPressed:
                    {
                        close();
                    }
                }
            }
        }
        Rectangle{
            id:stockNumberPrice
            width: 1600  * dpi_ratio;
            height: 100  * dpi_ratio;
            anchors.top: title_bar.bottom;
            anchors.left: parent.left;
            color: "#FFFFFF";
            Rectangle
            {
                id:stockNumber1;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                //anchors.horizontalCenter: parent.horizontalCenter;
                anchors.left: parent.left;
                anchors.leftMargin: 20 * dpi_ratio;
                color: "#FFFFFF";
                Text
                {
                    text: qsTr("出库数量");
                    font.family: ST.fontFamilyYaHei;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#000000";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.left
                        font.pixelSize: 33 * dpi_ratio
                        visible: true
                    }
                }
            }
            Rectangle
            {
                id:stockNumber2
                width: 54  * dpi_ratio;
                height: 54  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left:stockNumber1.right;
                anchors.leftMargin: 20  * dpi_ratio;
                color: "#FFFFFF";
                radius: 10  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#999999";
                Image
                {
                    width: 34 * dpi_ratio;
                    height: 4 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    source: "/Images/jianFlag.png";
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onPressed:
                    {
                        if(stockNumber3_Text.text != ""){
                            stockNumber3_Text.text = stockNumber3_Text.text*1 - 1;
                        }
                    }
                }
            }
            Rectangle
            {
                id:stockNumber3;
                width:430  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.left:stockNumber2.right;
                anchors.leftMargin: 17  * dpi_ratio;
                anchors.bottom: stockNumber1.bottom;
                color: "#FFFFFF";
                border.width: 1  * dpi_ratio;
                border.color: "#D8D8D8";
                radius: 10  * dpi_ratio;
                Text
                {
                    width: 140  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 18  * dpi_ratio;
                    anchors.left:parent.left;
                    anchors.leftMargin: 17  * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#999999";
                    text:qsTr("请输入出库数量");
                    visible:stockNumber3_Text.text == "";
                }
                Text
                {
                    width: 64  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 18  * dpi_ratio;
                    anchors.right: parent.right;
                    anchors.rightMargin: 10 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#000000";
                    text:goods_unit;
                }
                TextInput
                {
                    id: stockNumber3_Text;
                    width: 430  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 17  * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 24 * dpi_ratio;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#333333";
                    maximumLength: 20  * dpi_ratio;
                    validator: RegularExpressionValidator {
                        regularExpression: /^\d{1,4}(\.\d{1,2})?$/
                    }
                    clip: true;
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            parent.forceActiveFocus();
                            parent.selectAll();
                            coverValue = true;

                        }
                    }
                    onAccepted:
                    {

                    }
                    onFocusChanged: {
                    }
                    onTextChanged:
                    {
                        if(popup_root.goods_cheng_type == 0){
                            if(text[text.length - 1] === '.'){
                                stockNumber3_Text.text = stockNumber3_Text.text.slice(0, -1);
                            }
                        }
                        if(stockNumber3_Text.text == "+")
                        {
                            stockNumber3_Text.text = "";
                        }
                        if(stockNumber3_Text.text*1 < 0){
                            stockNumber3_Text.text = 0;
                        }
                        str_num = stockNumber3_Text.text*1;
                        if(stockUnitPrice_Text.text != "" && stockNumber3_Text.text !=""){
                            stockTotalPriceText.text = Math.abs((stockUnitPrice_Text.text*1 * stockNumber3_Text.text*1)).toFixed(2);
                        }
                    }
                }
            }
            Rectangle
            {
                id:stockNumber4
                width: 54  * dpi_ratio;
                height: 54  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left:stockNumber3.right;
                anchors.leftMargin: 20  * dpi_ratio;
                color: "#00BD75";
                radius: 10  * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: "#999999";
                Image
                {
                    width: 34 * dpi_ratio;
                    height: 34 * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    source: "/Images/jiaFlag.png";
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onPressed:
                    {
                        if(stockNumber3_Text.text !== ""){
                            stockNumber3_Text.text = stockNumber3_Text.text*1  +  1;
                        }
                        else if(stockNumber3_Text.text === ""){
                            stockNumber3_Text.text = 1;
                        }
                    }
                }
            }
            Rectangle
            {
                id:stockTotalPrice;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.bottom: stockNumber4.bottom;
                anchors.left: stockNumber4.right;
                anchors.leftMargin: 40 * dpi_ratio;
                color: "#FFFFFF";
                Text
                {
                    text: qsTr("出库总价");
                    font.family: ST.fontFamilyYaHei;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#000000";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                    CusText {
                        text: "*"
                        color: ST.color_red
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: parent.left
                        font.pixelSize: 33 * dpi_ratio
                        visible: true
                    }
                }
            }
            Rectangle
            {
                id:stockTotalPrice2;
                width:200  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.left:stockTotalPrice.right;
                anchors.leftMargin: 17  * dpi_ratio;
                anchors.bottom: stockTotalPrice.bottom;
                color: "#FFFFFF";
                border.width: 1  * dpi_ratio;
                border.color: "#D8D8D8";
                radius: 10  * dpi_ratio;
                Text
                {
                    width: 140  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 18  * dpi_ratio;
                    anchors.left:parent.left;
                    anchors.leftMargin: 20  * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#999999";
                    text:qsTr("请输入");
                    visible:stockTotalPriceText.text == "";
                }
                TextInput
                {
                    id: stockTotalPriceText;
                    width: 200  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    anchors.left: parent.left;
                    anchors.leftMargin: 20  * dpi_ratio;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.family: ST.fontFamilyYaHei;
                    font.pointSize: 24 * dpi_ratio;
                    verticalAlignment: Text.AlignVCenter;
                    color: "#333333";
                    enabled:stockNumber3_Text.text*1 > 0;
                    maximumLength: 20  * dpi_ratio;
                    validator: RegularExpressionValidator {
                        regularExpression: /^\d+(\.\d{1,2})?$/
                    }
                    clip: true;
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            parent.forceActiveFocus();
                            parent.selectAll();
                            coverValue = true;

                        }
                    }
                    onAccepted:
                    {

                    }
                    onFocusChanged: {
                        if (!focus) {
                            if(stockTotalPriceText.text != ""  || stockUnitPrice_Text.text != ""
                                    || stockNumber3_Text.text != "" || stockNumber3_Text.text != "0")
                            {
                                stockUnitPrice_Text.text = Math.abs((stockTotalPriceText.text*1 / stockNumber3_Text.text*1)).toFixed(2);
                            }

                        }
                    }
                    onTextChanged:
                    {
                            if(stockTotalPriceText.text == "NaN")
                            {
                                stockTotalPriceText.text = "";
                            }
                            if(stockTotalPriceText.text == "0")
                            {
                                stockTotalPriceText.text = "0.00";
                            }
                            if(stockTotalPriceText.text == "0.00")
                            {
                                stockTotalPriceText.text = "";
                            }
                            stockTotalPriceCopy = stockTotalPriceText.text;
                    }
                }
                Rectangle
                {
                    id:stockUnitPrice;
                    width: 130  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    anchors.verticalCenter:parent.verticalCenter;
                    //anchors.horizontalCenter: parent.horizontalCenter;
                    anchors.left: stockTotalPrice2.right;
                    anchors.leftMargin: 32 * dpi_ratio;
                    color: "#FFFFFF";
                    Text
                    {
                        text: qsTr("出库单价");
                        font.family: ST.fontFamilyYaHei;
                        anchors.horizontalCenter:parent.horizontalCenter;
                        anchors.verticalCenter: parent.verticalCenter;
                        font.pointSize: 18 * dpi_ratio;
                        color: "#000000";
                        horizontalAlignment: Text.AlignHCenter;
                        verticalAlignment: Text.AlignVCenter;
                        CusText {
                            text: "*"
                            color: ST.color_red
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.left
                            font.pixelSize: 33 * dpi_ratio
                            visible: true
                        }
                    }
                }
                Rectangle
                {
                    width:200  * dpi_ratio;
                    height: 64  * dpi_ratio;
                    anchors.left:stockUnitPrice.right;
                    anchors.leftMargin: 20  * dpi_ratio;
                    anchors.bottom: stockUnitPrice.bottom;
                    color: "#FFFFFF";
                    border.width: 1  * dpi_ratio;
                    border.color: "#D8D8D8";
                    radius: 10  * dpi_ratio;
                    Text
                    {
                        width: 140  * dpi_ratio;
                        height: 64  * dpi_ratio;
                        font.family: ST.fontFamilyYaHei;
                        font.pointSize: 18  * dpi_ratio;
                        anchors.left:parent.left;
                        anchors.leftMargin: 20  * dpi_ratio;
                        anchors.verticalCenter: parent.verticalCenter;
                        verticalAlignment: Text.AlignVCenter;
                        color: "#999999";
                        text:qsTr("请输入");
                        visible:stockUnitPrice_Text.text == "";
                    }
                    TextInput
                    {
                        id: stockUnitPrice_Text;
                        width: 200  * dpi_ratio;
                        height: 64  * dpi_ratio;
                        anchors.left: parent.left;
                        anchors.leftMargin: 20  * dpi_ratio;
                        anchors.verticalCenter: parent.verticalCenter;
                        font.family: ST.fontFamilyYaHei;
                        font.pointSize: 24 * dpi_ratio;
                        verticalAlignment: Text.AlignVCenter;
                        color: "#333333";
                        maximumLength: 20  * dpi_ratio;
                        enabled:stockNumber3_Text.text*1 > 0;
                        validator: RegularExpressionValidator {
                            regularExpression: /^\d+(\.\d{1,2})?$/
                        }
                        clip: true;
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                parent.forceActiveFocus();
                                parent.selectAll();
                                coverValue = true;

                            }
                        }
                        onFocusChanged: {
                            if(!focus){
                                if(stockTotalPriceText.text != ""  || stockUnitPrice_Text.text != ""
                                        || stockNumber3_Text.text != "" || stockNumber3_Text.text != "0")
                                {
                                 stockTotalPriceText.text = Math.abs((stockUnitPrice_Text.text*1 * stockNumber3_Text.text*1)).toFixed(2);
                                }

                            }

                        }
                        onTextChanged:
                        {
                            stockPriceCopy = stockUnitPrice_Text.text;
                                if(stockUnitPrice_Text.text == "NaN")
                                {
                                    stockUnitPrice_Text.text = "";
                                }
                                if(stockUnitPrice_Text.text == "0")
                                {
                                    stockUnitPrice_Text.text = "0.00";
                                }
                                if(stockUnitPrice_Text.text == "0.00")
                                {
                                    stockUnitPrice_Text.text = "";
                                }

                        }
                        onAccepted:
                        {

                        }
                    }
                    }
                }

        }
        Rectangle{
            id:stockOutType;
            width: 1600  * dpi_ratio;
            height: 70  * dpi_ratio;
            anchors.top: stockNumberPrice.bottom;
            anchors.left: parent.left;
            color: "#FFFFFF";
            Rectangle
            {
                id:stockType;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                //anchors.horizontalCenter: parent.horizontalCenter;
                anchors.left: parent.left;
                anchors.leftMargin: 20 * dpi_ratio;
                color: "#FFFFFF";
                Text
                {
                    text: qsTr("出库类型");
                    font.family: ST.fontFamilyYaHei;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#000000";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                }

            }
            Rectangle
            {
                id:transferOut;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left: stockType.right;
                anchors.leftMargin: 14 * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: stockOutColorType == qsTr("调拨出库")?"#CBF3E3":"#00BD75";
                radius: 10  * dpi_ratio;
                color: stockOutColorType == qsTr("调拨出库")?"#CBF3E3":"#FFFFFF";
                Text
                {
                    text: qsTr("调拨出库");
                    font.family: ST.fontFamilyYaHei;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#00BD75";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        stockOutColorType = qsTr("调拨出库");
                    }
                }
            }
            Rectangle
            {
                id:dyingPeriodOut;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left: transferOut.right;
                anchors.leftMargin: 14 * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: stockOutColorType == qsTr("临期出库")?"#CBF3E3":"#00BD75";
                radius: 10  * dpi_ratio;
                color: stockOutColorType == qsTr("临期出库")?"#CBF3E3":"#FFFFFF";
                Text
                {
                    text: qsTr("临期出库");
                    font.family: ST.fontFamilyYaHei;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#00BD75";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        stockOutColorType = qsTr("临期出库");
                    }
                }
            }
            Rectangle
            {
                id:frmLossOut;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left: dyingPeriodOut.right;
                anchors.leftMargin: 14 * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: stockOutColorType == qsTr("报损出库")?"#CBF3E3":"#00BD75";
                radius: 10  * dpi_ratio;
                color: stockOutColorType == qsTr("报损出库")?"#CBF3E3":"#FFFFFF";
                Text
                {
                    text: qsTr("报损出库");
                    font.family: ST.fontFamilyYaHei;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#00BD75";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        stockOutColorType = qsTr("报损出库");
                    }
                }
            }
            Rectangle
            {
                id:checkOut;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left: frmLossOut.right;
                anchors.leftMargin: 14 * dpi_ratio;
                border.width: 1  * dpi_ratio;
                border.color: stockOutColorType == qsTr("盘点出库")?"#CBF3E3":"#00BD75";
                radius: 10  * dpi_ratio;
                color: stockOutColorType == qsTr("盘点出库")?"#CBF3E3":"#FFFFFF";
                Text
                {
                    text: qsTr("盘点出库");
                    font.family: ST.fontFamilyYaHei;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#00BD75";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        stockOutColorType = qsTr("盘点出库");
                    }
                }
            }
        }
        Rectangle{
            id:stockOutBatch;
            width: 1600  * dpi_ratio;
            height: 70  * dpi_ratio;
            anchors.top: stockOutType.bottom;
            anchors.topMargin: 10 * dpi_ratio;
            anchors.left: parent.left;
            color: "#FFFFFF";
            Rectangle
            {
                id:stockBatch;
                width: 130  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left: parent.left;
                anchors.leftMargin: 20 * dpi_ratio;
                color: "#FFFFFF";
                Text
                {
                    text: qsTr("出库批次");
                    font.family: ST.fontFamilyYaHei;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#000000";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                }
            }
            Rectangle
            {
                id:stockBatchExplain;
                width: 530  * dpi_ratio;
                height: 64  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left: stockBatch.right;
                anchors.leftMargin: 16 * dpi_ratio;
                color: "#FFFFFF";
                Text
                {
                    text: qsTr("(系统将按先入先出规则，自动匹配出库批次和数量)");
                    font.family: ST.fontFamilyYaHei;
                    anchors.horizontalCenter:parent.horizontalCenter;
                    anchors.verticalCenter: parent.verticalCenter;
                    font.pointSize: 18 * dpi_ratio;
                    color: "#A8A8A8";
                    horizontalAlignment: Text.AlignHCenter;
                    verticalAlignment: Text.AlignVCenter;
                }
            }
        }
        Rectangle{
            id:bottomRec;
            width: 1600  * dpi_ratio;
            height: 320  * dpi_ratio;
            anchors.top: stockOutBatch.bottom;
            anchors.left: parent.left;
            color: "#FFFFFF";
            Rectangle
            {
                id:stockBatchList;
                width: 920  * dpi_ratio;
                height: 350  * dpi_ratio;
                anchors.verticalCenter:parent.verticalCenter;
                anchors.left: parent.left;
                anchors.leftMargin: 20 * dpi_ratio;
                color: "#FFFFFF";
                ColumnLayout {
                    anchors.fill: parent
                //标题栏
                Item {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 50 * dpi_ratio


                    RowLayout {
                        anchors.fill: parent
                        spacing: 0 * dpi_ratio


                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 210 * dpi_ratio
                            clip: true
                            color: "#EBEBEB"
                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("批次号")
                                color: "#666666"
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 250 * dpi_ratio
                            clip: true
                            color: "#EBEBEB"
                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("入库时间")
                                color: "#666666"
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 130 * dpi_ratio
                            clip: true
                            color: "#EBEBEB"

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("到期时间")
                                color: "#666666"
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 130 * dpi_ratio
                            clip: true
                            color: "#EBEBEB"

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("入库单价(元)")
                                color: "#666666"
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 120 * dpi_ratio
                            clip: true
                            color: "#EBEBEB"

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("剩余数量")
                                color: "#666666"
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 120 * dpi_ratio
                            clip: true
                            color: "#EBEBEB"

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("出库数")
                                color: "#666666"
                            }
                        }
                    }
                }
                ListModel {
                    id: lm_stock_record
                }

                ListView {
                    id: lv_stock_record
                    model: lm_stock_record
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    clip: true
                    highlightFollowsCurrentItem: true
                    highlightMoveDuration: 0
                    focus: true
                    currentIndex: -1

                    delegate: CusRect {
                        color: ST.color_white_pure
                        width: lv_stock_record.width
                        height: 50 * dpi_ratio

                        MouseArea {
                            anchors.fill: parent
                        }

                        RowLayout {
                            anchors.fill: parent
                            spacing: 2 * dpi_ratio

                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 210 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    id:batchUniques
                                    anchors.centerIn: parent
                                    text: batchUnique
                                    color: ST.color_black
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.horizontalCenter: parent.horizontalCenter
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 210 * dpi_ratio
                                clip: true
                                color: ST.color_transparent
                                CusText {
                                    anchors.centerIn: parent
                                    text: createTime
                                    color: ST.color_black
                                    anchors.verticalCenter: parent.verticalCenter
                                    //anchors.horizontalCenter: parent.horizontalCenter
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 150 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: goodsExp
                                    color: ST.color_black
                                    anchors.verticalCenter: parent.verticalCenter
                                    //anchors.horizontalCenter: parent.horizontalCenter
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: goodsInPrice
                                    color: ST.color_black
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.horizontalCenter: parent.horizontalCenter
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: goodsCount
                                    color: ST.color_black
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.horizontalCenter: parent.horizontalCenter
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                clip: true
                                color: ST.color_transparent
                                height: 50 * dpi_ratio
                                CusRect {
                                    height: parent.height - 6 * dpi_ratio
                                    width: parent.width - 2 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent
                                    border.color: "#00BD75"
                                    border.width: 1 * dpi_ratio
                                    radius: 10 * dpi_ratio
                                    anchors.horizontalCenter: parent.horizontalCenter;
                                    anchors.verticalCenter: parent.verticalCenter;

                                    Text
                                    {
                                        width: 96  * dpi_ratio;
                                        height: 40  * dpi_ratio;
                                        font.family: ST.fontFamilyYaHei;
                                        font.pointSize: 14  * dpi_ratio;
                                        anchors.horizontalCenter: parent.horizontalCenter;
                                        anchors.verticalCenter: parent.verticalCenter;
                                        verticalAlignment: Text.AlignVCenter;
                                        horizontalAlignment: Text.AlignHCenter;
                                        color: "#A8A8A8";
                                        text:qsTr("请输入");
                                        visible: TextInput.text == "";
                                    }
                                    TextInput
                                    {
                                        id:delegateTextInput
                                        width: 106  * dpi_ratio;
                                        height: 48  * dpi_ratio;
                                        anchors.left: parent.left;
                                        anchors.leftMargin: 18  * dpi_ratio;
                                        anchors.verticalCenter: parent.verticalCenter;
                                        font.family: ST.fontFamilyYaHei;
                                        font.pointSize: 14 * dpi_ratio;
                                        verticalAlignment: Text.AlignVCenter;
                                        color: ST.color_black
                                        text:goodsBatchId;
                                        enabled:stockNumber3_Text.text*1 > 0;
                                        maximumLength: 20  * dpi_ratio;
                                        clip: true;
                                        Component.onCompleted: {
                                            if(cleanInit == false){
                                                cleanGoodsBatchIdCopy();
                                                cleanInit = true;
                                            }
                                        }
                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                parent.forceActiveFocus();
                                                parent.selectAll();
                                                coverValue = true;

                                            }
                                        }
                                        onFocusChanged:
                                        {
                                            if (focus) {
                                                textInputIndex = index;
                                                delegateTextInputfocus = true;

                                            }else{
                                                delegateTextInputfocus = false;
                                            }
                                        }
                                        onTextChanged:
                                        {
                                                if (text !== "" && parseFloat(text) !== 0 && cleanInit === true) {
                                                        var newItem = {
                                                            "batchUnique": batchUniques.text,
                                                            "outStockCount": parseFloat(text)
                                                        };
                                                        var existingItem = batchOutStock.find(function(item) {
                                                            return item.batchUnique === newItem.batchUnique;
                                                        });

                                                        if (existingItem) {

                                                                existingItem.outStockCount = newItem.outStockCount;
                                                        } else {

                                                            batchOutStock.push(newItem);
                                                        }

                                                        for (var i = 0; i < batchOutStock.length; i++) {
                                                            var item = batchOutStock[i];
                                                            //logMgr.logDataInfo4Qml("[{}]:batchUniques1:{},outStockCount1:{}",i, item.batchUnique, item.outStockCount); // 读取batchUniques和outStockCount的值
                                                        }
                                                    }
                                                else if(parseFloat(text) === 0){
                                                    var newItem = {
                                                        "batchUnique": batchUniques.text,
                                                        "outStockCount": parseFloat(text)
                                                    };
                                                    var existingItem = batchOutStock.find(function(item) {
                                                        return item.batchUnique === newItem.batchUnique;
                                                    });
                                                    var index = batchOutStock.indexOf(existingItem);
                                                    if (index !== -1) {
                                                        batchOutStock.splice(index, 1);
                                                        for (var i = 0; i < batchOutStock.length; i++) {
                                                            var item = batchOutStock[i];
                                                        }
                                                    }
                                                }


                                        }
                                    }
                                }
                            }
                        }
                    }

                    highlight: CusRect {
                        width: lv_stock_record.width
                        height: 50
                        color: ST.color_orange
                        opacity: .4
                        z: 2
                    }
                }
            }
                }
            CusRect {
                id: payKeyboardDigital;
                width: 720*dpi_ratio;
                height: 350*dpi_ratio;
                color: "#ffffff";
                visible: true;
                anchors.left: stockBatchList.right;
                anchors.leftMargin: 15 *dpi_ratio;
                anchors.bottom: stockBatchList.bottom;
                radius: 10 *dpi_ratio;

                RowLayout {
                    anchors.fill: parent

                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent

                        GridLayout {
                            id: gl_calc_btns

                            anchors.fill: parent
                            columns: 4
                            rows: 3

                            columnSpacing: 5 * dpi_ratio
                            rowSpacing: 5 * dpi_ratio

                            property real col_width: (width - ((columns - 1) * rowSpacing)) / columns
                            property real col_height: (height - ((rows - 1) * columnSpacing)) / rows

                            function prefWidth(item) {
                                return col_width * item.Layout.columnSpan
                            }
                            function prefHeight(item) {
                                return col_height * item.Layout.rowSpan
                            }

                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "7"


                            }

                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "8"
                            }
                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "9"
                            }
                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "0"
                            }
                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "4"
                            }
                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "5"
                            }
                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "6"
                            }
                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "."
                            }
                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "1"
                            }
                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "2"
                            }
                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: "3"
                            }
                            KeyRectNum {
                                Layout.preferredWidth: parent.prefWidth(this)
                                Layout.preferredHeight: parent.prefHeight(this)
                                key_text: qsTr("清空")
                                onPressKey: {
                                    resetData()
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillHeight: true
                        Layout.preferredWidth: 130 * dpi_ratio
                        color: ST.color_transparent

                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio

                            property real cell_height: gl_calc_btns.col_height

                            KeyRectFlag {
                                Layout.preferredHeight: parent.cell_height
                                Layout.fillWidth: true
                                key_text: ""
                                color: "#FFFFFF"
                                text.color: "#FFFFFF"
                                Image {
                                    anchors.fill: parent
                                    source: "/Images/tuige.png"
                                    fillMode: Image.PreserveAspectFit
                                    scale: 0.5
                                }
                                onPressKey: {
                                    if((!stockUnitPrice_Text.activeFocus)&&(!stockNumber3_Text.activeFocus)&&(!stockTotalPriceText.activeFocus)){
                                        delegateTextInputfocus = true;
                                    }else{
                                        delegateTextInputfocus = false;
                                    }
                                    if ((textInputIndex >= 0) && (textInputIndex < lv_stock_record.model.count) && (delegateTextInputfocus == true)) {

                                       var currentGoodsBatchId = (lv_stock_record.model.get(textInputIndex)).goodsBatchId;
                                        if (isNaN(currentGoodsBatchId)) {
                                            currentGoodsBatchId = "";
                                        } else {
                                            currentGoodsBatchId = (currentGoodsBatchId.toString()).slice(0, -1);
                                        }
                                        lv_stock_record.model.setProperty(textInputIndex, "goodsBatchId", currentGoodsBatchId.toString());
                                    }
                                    if (stockUnitPrice_Text.activeFocus) {
                                           stockUnitPrice_Text.text = stockUnitPrice_Text.text.slice(0, -1);
                                       }
                                    if (stockTotalPriceText.activeFocus) {
                                           stockTotalPriceText.text = stockTotalPriceText.text.slice(0, -1);
                                       }
                                    if (stockNumber3_Text.activeFocus) {
                                           stockNumber3_Text.text = stockNumber3_Text.text.slice(0, -1);
                                       }
                                }
                            }
                            KeyRectFlag {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                key_text: qsTr("出库")
                                color: "#00BD75"
                                text.color: "white"
                                onPressKey: {
//                                    if (totalPayment === "0.00") {
//                                        toast.openWarn("应收金额不可为0")
//                                        return
//                                    }
                                }
                            }
                        }
                    }
                }
            }
            component KeyRectNum: CusRect {
                id: rect_key
                property string key_text: "0"
                signal pressKey(var key)
                color: ST.color_transparent
                radius: 10 *dpi_ratio;

                border {
                    width: 2 * dpi_ratio
                    color: "#E5E5E5"
                }

                CusText {
                    id:rect_key_id
                    anchors.centerIn: parent
                    text: rect_key.key_text
                    font.weight: Font.Bold
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        rect_key.color = "#17262B";
                        rect_key_id.color = "white";
                        clickAmation.start();
                        rect_key.pressKey(rect_key.key_text)
                    }
                }
                Timer
                {
                    id: clickAmation;
                    running: false;
                    repeat: false;
                    interval: 100
                    onTriggered:
                    {
                        rect_key.color = "white";
                        rect_key_id.color = "black";
                    }
                }
                onPressKey: {

                    if(key !==qsTr("清空")){
                        setInput(key)
                    }
                }
            }
            component KeyRectFlag: CusRect {
                id: rect_key2
                property string key_text: "0"
                signal pressKey(var key)

                property alias text: text
                color: ST.color_transparent
                radius: 10 *dpi_ratio;

                border {
                    width: 2 * dpi_ratio
                    color: "#E5E5E5"
                }
                CusText {
                    id: text
                    anchors.centerIn: parent
                    text: rect_key2.key_text
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        rect_key2.color = "#17262B";
                        text.color = "white";
                        clickAmation2.start();
                        rect_key2.pressKey(rect_key2.key_text)
                        if(rect_key2.key_text == qsTr("出库")){
                        }
                    }
                }
                Timer
                {
                    id: clickAmation2;
                    running: false;
                    repeat: false;
                    interval: 100
                    onTriggered:
                    {
                        if(rect_key2.key_text == qsTr("出库")){
                            rect_key2.color = "#00BD75";
                            text.color = "white";
                            stockNumber3_Text.forceActiveFocus();
                            tryConfirm();
                        }
                        else{
                            rect_key2.color = "white";
                            text.color = "black";
                        }

                    }
                }
            }

        }
    }

}
