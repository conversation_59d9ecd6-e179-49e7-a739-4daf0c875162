﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import EnumTool 1.0
import GoodsDataModel 1.0
import "../PayPage/GoodsEdit"
CusRect {
    id: godown_page_old_root
    width: 800 * dpi_ratio
    height: 500 * dpi_ratio
    color: ST.color_black_p1
    Timer {
        id:searchFocus
        interval: 100
        repeat: false
        running: true

        onTriggered: {
            tf_godown_search.focus = true
            tf_godown_search.forceActiveFocus()
        }
    }
    ListModel {
        id: lm_goods_unit
    }

    Component.onCompleted: {
        resetGoodsInfo()
        searchFocus.start()
    }

    onVisibleChanged: {
        if (visible) {
            resetGoodsInfo()
            searchFocus.start()
        }
        else if(!visible){
            initCreateDate = false
        }
    }

    property string background_img_path: basic_goods_standard.is_cloud_goods ? "/Images/goods_down_back_blue.png" : "/Images/goods_down_back.png"
    property bool initCreateDate:false;
    property string supplier_name:""
    property string supplier_nameTemp:""
    property bool searchOpen: false //弹窗控制符
    property bool selectOpen: true //弹窗控制符
    function showStockChangeIn(goods_barcode, goods_ratio = 1,good_stock_price,goods_unit,goods_cheng_type) {
        window_root.loader_4_stock_del.sourceComponent = popup_sotck_change_in
        window_root.loader_4_stock_del.item.open(goods_barcode, goods_ratio,good_stock_price,basic_goods_standard.goods_life,goods_unit,goods_cheng_type)
    }

    function showStockChangeOutFirst(goods_barcode, goods_ratio = 1,goods_unit,good_stock_price,goods_cheng_type) {
        window_root.loader_4_stock_del.sourceComponent = popup_sotck_change_out_First
        window_root.loader_4_stock_del.item.open(goods_barcode, goods_ratio,basic_goods_standard.goods_barcode,goods_unit,good_stock_price,goods_cheng_type)
    }
    function showStockChangeOutMean(goods_barcode, goods_ratio = 1,goods_unit,good_stock_price,goods_cheng_type) {

        window_root.loader_4_stock_del.sourceComponent = popup_sotck_change_out_Mean
        window_root.loader_4_stock_del.item.open(goods_barcode, goods_ratio,goods_unit,good_stock_price,goods_cheng_type)
    }

    function showDelCurGoodsConfirm(goods_barcode_in) {
        window_root.loader_4_del_cur_goods.sourceComponent = compo_del_cur_goods
        window_root.loader_4_del_cur_goods.item.goods_barcode = goods_barcode_in
        window_root.loader_4_del_cur_goods.item.open()
    }
    function openCalendarBegin(date, hour, minute) {
        window_root.loader_4_calendar_begin.sourceComponent = calendar_begin
        window_root.loader_4_calendar_begin.item.open()
        window_root.loader_4_calendar_begin.item.set(date, hour, minute)
        logMgr.logEvtInfo4Qml("打开开始日历")
    }
    // 删除商品
    function deleteGoods(goods_barcode_in = "") {

        if (goods_barcode_in == "")
            return

        // 关联商品列表
        var cur_goods_doc = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode_in))

        function callback(is_succ, data) {
            if (is_succ) {
                toast.openInfo(qsTr("删除成功"))
            } else {
                toast.openError(qsTr("删除失败"))
            }
        }

        if (cur_goods_doc.goods_contain == 1) {
            var json_doc = JSON.parse(goodsManager.getForeignGoodsJsonListByBarcode(goods_barcode_in))

            if (json_doc) {
                if (json_doc.length > 0) {
                    goodsControl.reqDelGoodsByBarcode(callback, json_doc[0].goods_barcode)
                }
                if (json_doc.length > 1) {
                    goodsControl.reqDelGoodsByBarcode(callback, json_doc[1].goods_barcode)
                }
            }
        }

        goodsControl.reqDelGoodsByBarcode(callback, goods_barcode_in)

        resetGoodsInfo()
    }

    // 设置当前商品
    function setCurGoodsByBarcode(goods_barcode) {
        // cancelGoods()
        // clearAllGoodsInfo()
        var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))

        //非基础规格,找出基础规格
        if (cur_goods.goods_contain > 1) {
            var cur_goods_basic = JSON.parse(goodsManager.getGoodsByBarcode4Qml(cur_goods.foreign_key))

            if (!cur_goods_basic) {
                toast.openError(qsTr("商品信息不完整!"))
                return
            }
            setCurGoodsAll(cur_goods_basic.goods_barcode)
        } else {
            setCurGoodsAll(cur_goods.goods_barcode)
        }
    }

    // 设置云端商品
    function setColudGoodsByBarcode(goods_barcode, callback) {
        goodsControl.reqGetOnlineGoodsInfo(function (is_succ, data) {
            var goods_json = JSON.parse(data)

            // 检查云端商品是否搜索到
            if (goods_json.status == 0) {
                goods_json = goods_json.data
                basic_goods_standard.goods_barcode = goods_json.goods_barcode
                basic_goods_standard.goods_name = goods_json.goods_name
                basic_goods_standard.goods_supplier = goods_json.default_supplier_unique
                basic_goods_standard.goods_abbr = goods_json.goods_alias
                basic_goods_standard.goods_standard = goods_json.goods_standard
                basic_goods_standard.goods_price = Number(goods_json.goods_sale_price).toFixed(2)
                basic_goods_standard.goods_member_price = Number(goods_json.goods_cus_price).toFixed(2)
                basic_goods_standard.goods_in_price = Number(goods_json.goods_in_price).toFixed(2)
                basic_goods_standard.goods_stock = Number(goods_json.goods_count).toFixed(2)
                basic_goods_standard.goods_unit = goods_json.goods_unit

                //basic_goods_standard.goods_web_price = Number(goods_json.goods_sale_price).toFixed(2)
                focusGoodsKind(goods_json.goods_kind_unique)
                focusSupplier(goods_json.default_supplier_unique)
                supplierNameGene(goods_json.default_supplier_unique)
                focusUnit(goods_json.goods_unit)

                basic_goods_standard.is_cloud_goods = true

                callback(true)
            } else {
                callback(false)
            }
        }, goods_barcode)
    }

    function trySetText2Barcode(text_in) {
        if (!isNaN(Number(text_in)) && basic_goods_standard.goods_barcode == "") {
            basic_goods_standard.is_cloud_goods = false
            basic_goods_standard.goods_barcode = text_in
        }
    }

    // 根据基础规格条码设置所有商品
    function setCurGoodsAll(cur_goods_barcode) {
        resetGoodsInfo()

        basic_goods_standard.setGoodsByBarcode(cur_goods_barcode)

        var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(cur_goods_barcode))

        // 关联商品列表
        var json_doc = JSON.parse(goodsManager.getForeignGoodsJsonListByBarcode(cur_goods_barcode))
        if (json_doc) {

            if (json_doc.length == 1) {
                medium_goods_standard.setGoodsByBarcode(json_doc[0].goods_barcode)
            } else if (json_doc.length > 1) {
                if (json_doc[0].goods_contain < json_doc[1].goods_contain) {
                    medium_goods_standard.setGoodsByBarcode(json_doc[0].goods_barcode)
                    high_goods_standard.setGoodsByBarcode(json_doc[1].goods_barcode)
                } else {
                    medium_goods_standard.setGoodsByBarcode(json_doc[1].goods_barcode)
                    high_goods_standard.setGoodsByBarcode(json_doc[0].goods_barcode)
                }
            }
        }
    }

    // 刷新数据
    function refreshData() {
        refreshUnit()
        refreshSupplier()
        refreshCategory1()
    }

    // 刷新单位
    function refreshUnit() {
        lm_goods_unit.clear()
        var str_tmp = goodsManager.getAllGoodsUnitJson()
        var json_all_goods_unit = JSON.parse(str_tmp)

        if (json_all_goods_unit == null)
            return

        for (var i = 0; i < json_all_goods_unit.length; ++i) {
            var cur_item = json_all_goods_unit[i]
            lm_goods_unit.append(cur_item)
            // lm_goods_unit.append({
            //                          "goods_unit": cur_item.goods_unit
            //                      })
        }
    }

    // 选中单位
    function focusUnit(unit_name) {

        if (unit_name == "") {
            combo_goods_unit.currentIndex = -1
            return
        }

        for (var i = 0; i < lm_goods_unit.count; ++i) {
            var cur_item = lm_goods_unit.get(i)
            if (cur_item.goods_unit == unit_name) {
                combo_goods_unit.currentIndex = i
                return
            }
        }

        lm_goods_unit.append({
                                 "goods_unit_id": -1,
                                 "goods_unit": unit_name
                             })

        combo_goods_unit.currentIndex = lm_goods_unit.count - 1
    }

    // 刷新供应商
    function refreshSupplier() {
        lm_supplier.clear()
        var json_data = JSON.parse(supplierControl.getSupplierJson())
        if (!json_data)
            return
        for (var i = 0; i < json_data.length; ++i) {
            var cur_item = json_data[i]
            lm_supplier.append(cur_item)
        }
    }
//    // 选中供应商
//    function focusSupplier(supplier_unique) {
//        for (var i = 0; i < lm_supplier.count; ++i) {
//            var cur_item = lm_supplier.get(i)

//            if (cur_item.supplier_unique == supplier_unique) {
//                combo_supplier.currentIndex = i
//                break
//            }
//        }
//    }

    // 刷新一级分类
    function refreshCategory1() {
        lm_category1.clear()
        var data_json = JSON.parse(goodsKindManager.getRootGoodsKind4Qml())
        if (!data_json)
            return
        for (var i = 0; i < data_json.length; ++i) {
            var cur_item = data_json[i]
            lm_category1.append(cur_item)
        }
    }
    // 选中一级分类
    function foucesCategory1ByKindUnique(kind_unique) {
        for (var i = 0; i < lm_category1.count; ++i) {
            var cur_goods_kind = lm_category1.get(i)

            if (cur_goods_kind.goods_kind_unique == kind_unique) {
                combo_category1.currentIndex = i
                return
            }
        }
    }

    // 刷新二级分类
    function refreshCategory2(parent_goods_kind_unique = "") {
        lm_category2.clear()

        if (parent_goods_kind_unique == "")
            return

        var data_json = JSON.parse(goodsKindManager.getSubGoodsKind4Qml(parent_goods_kind_unique))
        if (!data_json)
            return
        for (var i = 0; i < data_json.length; ++i) {
            var cur_item = data_json[i]
            lm_category2.append(cur_item)
        }
    }
    // 选中二级分类
    function focusCategory2ByKindUnique(kind_unique) {
        for (var i = 0; i < lm_category2.count; ++i) {
            var cur_goods_kind = lm_category2.get(i)
            if (cur_goods_kind.goods_kind_unique == kind_unique) {
                combo_category2.currentIndex = i
                return
            }
        }
    }

    function getIsGoodsKindEmpty() {
        return combo_category1.currentIndex == -1 && combo_category2.currentIndex == -1
    }

    // 选中商品分类
    function focusGoodsKind(goods_kind_unique) {
        if (goods_kind_unique == "")
            return

        var json_goods_kind_info = JSON.parse(goodsKindManager.getGoodsKindByKindUnique4Qml(goods_kind_unique))

        if (json_goods_kind_info == null)
            return

        foucesCategory1ByKindUnique(json_goods_kind_info.goods_kind_parunique)
        focusCategory2ByKindUnique(goods_kind_unique)
    }

    function tryFocusDefaultGoodsKind() {
        if (getIsGoodsKindEmpty()) {
            focusGoodsKind(goodsKindManager.defaultGoodsKind)
        }
    }

    // 重置当前信息
    function resetGoodsInfo() {
        refreshData()

        combo_category1.currentIndex = -1
        combo_category2.currentIndex = -1
        title_bar.resetInfo()

        tryFocusDefaultGoodsKind()

        basic_goods_standard.is_create = true
        basic_goods_standard.is_cloud_goods = false
        basic_goods_standard.goods_barcode = ""
        basic_goods_standard.goods_name = ""
        basic_goods_standard.goods_life = ""
        basic_goods_standard.goods_supplier = ""
        basic_goods_standard.goods_abbr = ""
        basic_goods_standard.goods_cheng_type = 0
        basic_goods_standard.goods_standard = ""
        basic_goods_standard.goods_price = ""
        // basic_goods_standard.goods_web_price = ""
        basic_goods_standard.goods_member_price = ""
        basic_goods_standard.goods_in_price = ""
        basic_goods_standard.goods_stock = ""
        basic_goods_standard.goods_unit = ""
        supplier_name = ""
        btn_calendar_prod.text = ""
        search_input.text =""
        medium_goods_standard.resetGoodsInfo()
        high_goods_standard.resetGoodsInfo()
        initCreateDate = false
        btn_calendar_prodCopy.text = utils4Qml.getCurDate() + " 00:00"
    }

    // 打印价签
    function printPriceTag(goods_barcode) {
        printerControl.printPriceTagByBarcode(goods_barcode)
    }
    function supplierNameGene(supplierUnique) {
        for (var i = 0; i < lm_supplier.count; ++i) {

            var cur_item = lm_supplier.get(i)
            if (cur_item.supplier_unique == supplierUnique) {
                supplier_name = cur_item.supplier_name
                search_input.text = supplier_name
                break
            }
        }
    }
    // 创建商品
    function createGoods_v2() {

        if (!title_bar.checkData()) {
            return
        }

        if (!basic_goods_standard.isHasData() || !basic_goods_standard.checkData()) {
            return
        }

        var result_json = []
        //基础规格
        result_json.push({
                             "goods_contain": "1",
                             "goods_barcode": basic_goods_standard.goods_barcode,
                             "goods_name": basic_goods_standard.goods_name,
                             "goods_standard": basic_goods_standard.goods_standard,
                             "goodsChengType": basic_goods_standard.goods_cheng_type,
                             "goods_kind_unique": title_bar.goods_kind_unique,
                             "goods_sale_price": basic_goods_standard.goods_price,
                             "goods_cus_price": basic_goods_standard.goods_member_price == "" ? basic_goods_standard.goods_price : basic_goods_standard.goods_member_price,
                             "goods_in_price": basic_goods_standard.goods_in_price,
                             "goods_unit": basic_goods_standard.goods_unit,
                             "goodsCount": basic_goods_standard.goods_stock,
                             "default_supplier_unique": basic_goods_standard.goods_supplier,
                             "goodsProd":basic_goods_standard.goods_prod,
                             "goodsLife":basic_goods_standard.goods_life
                             // "goods_web_sale_price": basic_goods_standard.goods_web_price == "" ? basic_goods_standard.goods_price : basic_goods_standard.goods_web_price,
                         })

        if (medium_goods_standard.isHasData()) {

            if (!medium_goods_standard.checkData())
                return

            result_json.push({
                                 "goods_contain": medium_goods_standard.goods_contain,
                                 "goods_barcode": medium_goods_standard.goods_barcode,
                                 "goods_name": medium_goods_standard.goods_name,
                                 "goodsChengType": medium_goods_standard.goods_cheng_type,
                                 "goods_kind_unique": title_bar.goods_kind_unique,
                                 "goods_sale_price": medium_goods_standard.goods_price,
                                 "goods_cus_price": medium_goods_standard.goods_member_price == "" ? medium_goods_standard.goods_price : medium_goods_standard.goods_member_price,
                                 "goods_in_price": medium_goods_standard.goods_in_price,
                                 "goods_standard": medium_goods_standard.goods_standard,
                                 "goods_unit": medium_goods_standard.goods_unit,
                                 "default_supplier_unique": basic_goods_standard.goods_supplier,
                                 "goodsProd":basic_goods_standard.goods_prod,
                                 "goodsLife":basic_goods_standard.goods_life
                                 // "goods_web_sale_price": medium_goods_standard.goods_web_price == "" ? medium_goods_standard.goods_price : medium_goods_standard.goods_web_price,
                             })
        }

        if (high_goods_standard.isHasData()) {

            if (!high_goods_standard.checkData())
                return

            result_json.push({
                                 "goods_contain": high_goods_standard.goods_contain,
                                 "goods_barcode": high_goods_standard.goods_barcode,
                                 "goods_name": high_goods_standard.goods_name,
                                 "goods_standard": high_goods_standard.goods_standard,
                                 "goodsChengType": high_goods_standard.goods_cheng_type,
                                 "goods_kind_unique": title_bar.goods_kind_unique,
                                 "goods_sale_price": high_goods_standard.goods_price,
                                 "goods_cus_price": high_goods_standard.goods_member_price,
                                 "goods_in_price": high_goods_standard.goods_in_price,
                                 "goods_unit": high_goods_standard.goods_unit,
                                 "default_supplier_unique": basic_goods_standard.goods_supplier,
                                 "goodsProd":basic_goods_standard.goods_prod,
                                 "goodsLife":basic_goods_standard.goods_life
                                 /// "goods_web_sale_price": high_goods_standard.goods_web_price,
                             })
        }
        goodsControl.reqUploadGoods_v2_4Old(function (is_succ, data) {
            if (is_succ) {
                if (data) {
                    var json_doc = JSON.parse(data)
                    toast.openInfo(json_doc.msg)
                } else {
                    toast.openInfo(qsTr("修改商品成功"))
                }
                resetGoodsInfo()
            } else {
                if (data) {
                    var json_doc = JSON.parse(data)
                    toast.openWarn(json_doc.msg)
                } else {
                    toast.openWarn(qsTr("修改商品失败"))
                }
            }
        }, JSON.stringify(result_json))
    }

    // 修改商品
    function updateGoods_v2() {
        if (!basic_goods_standard.checkData()) {
            return
        }

        if (combo_category2.currentIndex == -1 || !(lm_category2.get(combo_category2.currentIndex).goods_kind_unique)) {
            toast.openWarn(qsTr("商品分类有误"))
            return
        }
        var result_json = []
        //基础规格
        result_json.push({
                             "goods_contain": "1",
                             "goods_barcode": basic_goods_standard.goods_barcode,
                             "goods_name": basic_goods_standard.goods_name,
                             "goodsChengType": basic_goods_standard.goods_cheng_type,
                             "goods_kind_unique": lm_category2.get(combo_category2.currentIndex).goods_kind_unique,
                             "goods_sale_price": basic_goods_standard.goods_price,
                             "goods_cus_price": basic_goods_standard.goods_member_price,
                             "goods_in_price": basic_goods_standard.goods_in_price,
                             "goods_unit": basic_goods_standard.goods_unit,
                             "goods_standard": basic_goods_standard.goods_standard,
                             "default_supplier_unique": basic_goods_standard.goods_supplier,
                             "goodsLife":basic_goods_standard.goods_life
                             // "goods_web_sale_price": basic_goods_standard.goods_web_price,
                         })

        if (medium_goods_standard.isHasData()) {

            if (!medium_goods_standard.checkData())
                return

            result_json.push({
                                 "goods_contain": medium_goods_standard.goods_contain,
                                 "goods_barcode": medium_goods_standard.goods_barcode,
                                 "goods_name": medium_goods_standard.goods_name,
                                 "goodsChengType": medium_goods_standard.goods_cheng_type,
                                 "goods_kind_unique": lm_category2.get(combo_category2.currentIndex).goods_kind_unique,
                                 "goods_sale_price": medium_goods_standard.goods_price,
                                 "goods_cus_price": medium_goods_standard.goods_member_price,
                                 "goods_in_price": medium_goods_standard.goods_in_price,
                                 "goods_unit": medium_goods_standard.goods_unit,
                                 "goods_standard": basic_goods_standard.goods_standard,
                                 "default_supplier_unique": basic_goods_standard.goods_supplier,
                                 "goodsLife":basic_goods_standard.goods_life
                                 // "goods_web_sale_price": medium_goods_standard.goods_web_price,
                             })
        }

        if (high_goods_standard.isHasData()) {

            if (!high_goods_standard.checkData())
                return

            result_json.push({
                                 "goods_contain": high_goods_standard.goods_contain,
                                 "goods_barcode": high_goods_standard.goods_barcode,
                                 "goods_name": high_goods_standard.goods_name,
                                 "goodsChengType": high_goods_standard.goods_cheng_type,
                                 "goods_kind_unique": lm_category2.get(combo_category2.currentIndex).goods_kind_unique,
                                 "goods_sale_price": high_goods_standard.goods_price,
                                 "goods_cus_price": high_goods_standard.goods_member_price,
                                 "goods_in_price": high_goods_standard.goods_in_price,
                                 "goods_unit": high_goods_standard.goods_unit,
                                 "goods_standard": basic_goods_standard.goods_standard,
                                 "default_supplier_unique": basic_goods_standard.goods_supplier,
                                 "goodsLife":basic_goods_standard.goods_life
                                 // "goods_web_sale_price": high_goods_standard.goods_web_price,
                             })
        }

        goodsControl.reqUpdateGoodsByDataJson_v2_4Old(function (is_succ, data) {
            if (is_succ) {
                if (data) {
                    var json_doc = JSON.parse(data)
                    toast.openInfo(json_doc.msg)
                } else {
                    toast.openInfo(qsTr("修改商品成功"))
                }
                resetGoodsInfo()
            } else {
                if (data) {
                    var json_doc = JSON.parse(data)
                    toast.openWarn(json_doc.msg)
                } else {
                    toast.openWarn(qsTr("修改商品失败"))
                }
            }
        }, JSON.stringify(result_json))
    }

    RowLayout {
        anchors.fill: parent
        anchors.margins: 20 * dpi_ratio
        spacing: 10 * dpi_ratio

        CusRect {
            id: basic_goods_standard
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_white_pure
            radius: ST.radius

            property bool is_create: true //创建?
            property bool is_cloud_goods: false //云端商品?

            property string goods_barcode: "" //"商品条码"
            property string goods_name: "" //商品名
            property string goods_supplier: "" //供应商
            property string goods_abbr: "" //缩写
            property int goods_cheng_type: 0 //0、按件；1、按重量

            onGoods_cheng_typeChanged: {

                if (basic_goods_standard.is_create && basic_goods_standard.goods_barcode != "") {
                    var cur_goods_barcode = goodsManager.contrastGenerateCustomBarcode(basic_goods_standard.goods_cheng_type == 1,medium_goods_standard.goods_barcode,high_goods_standard.goods_barcode)
                    while ((cur_goods_barcode === medium_goods_standard.goods_barcode) || (cur_goods_barcode === high_goods_standard.goods_barcode)) {
                        cur_goods_barcode = goodsManager.contrastGenerateCustomBarcode(basic_goods_standard.goods_cheng_type == 1,medium_goods_standard.goods_barcode,high_goods_standard.goods_barcode)
                    }
                    basic_goods_standard.goods_barcode = cur_goods_barcode
                }

                if (medium_goods_standard.is_create && medium_goods_standard.goods_barcode != "") {
                    cur_goods_barcode = goodsManager.contrastGenerateCustomBarcode(basic_goods_standard.goods_cheng_type == 1,high_goods_standard,basic_goods_standard.goods_barcode)
                    var cur_goods_barcode_temp = cur_goods_barcode
                    while ((cur_goods_barcode === high_goods_standard) || (cur_goods_barcode === basic_goods_standard.goods_barcode)) {
                        cur_goods_barcode = goodsManager.contrastGenerateCustomBarcode(basic_goods_standard.goods_cheng_type == 1,high_goods_standard,basic_goods_standard.goods_barcode)
                    }
                    medium_goods_standard.goods_barcode = cur_goods_barcode
                }

                if (high_goods_standard.is_create && high_goods_standard.goods_barcode != "") {
                    cur_goods_barcode = goodsManager.contrastGenerateCustomBarcode(basic_goods_standard.goods_cheng_type == 1,medium_goods_standard.goods_barcode,basic_goods_standard.goods_barcode)

                    while ((cur_goods_barcode === medium_goods_standard.goods_barcode) || (cur_goods_barcode === basic_goods_standard.goods_barcode)) {
                        cur_goods_barcode = goodsManager.contrastGenerateCustomBarcode(basic_goods_standard.goods_cheng_type == 1,medium_goods_standard.goods_barcode,basic_goods_standard.goods_barcode)
                    }
                    high_goods_standard.goods_barcode = cur_goods_barcode
                }
            }

            property string goods_standard: "" //商品规格
            property string goods_price: "" //零售单价
            // property string goods_web_price: "" //网购单价
            property string goods_member_price: "" //会员单价
            property string goods_in_price: "" //采购单价
            property string good_stock_price: "" //最近入库价
            property string goods_stock: "" //商品库存
            property string goods_unit: "" //商品单位
            property string goods_prod: "" //生产日期
            property string goods_life:"" //保质期

            // 根据条码设置当前商品
            function setGoodsByBarcode(goods_barcode, is_editable = false) {
                var goods_json = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))

                basic_goods_standard.is_create = is_editable
                basic_goods_standard.goods_barcode = goods_json.goods_barcode
                basic_goods_standard.goods_name = goods_json.goods_name
                basic_goods_standard.goods_supplier = goods_json.default_supplier_unique
                basic_goods_standard.goods_abbr = goods_json.goods_alias
                basic_goods_standard.goods_cheng_type = goods_json.goodsChengType
                basic_goods_standard.goods_standard = goods_json.goods_standard
                basic_goods_standard.goods_price = Number(goods_json.goods_sale_price).toFixed(2)
                // basic_goods_standard.goods_web_price = Number(goods_json.goods_web_sale_price).toFixed(2)
                basic_goods_standard.goods_member_price = Number(goods_json.goods_cus_price).toFixed(2)
                basic_goods_standard.goods_in_price = Number(goods_json.goods_in_price).toFixed(2)
                basic_goods_standard.good_stock_price = Number(goods_json.goodStockPrice).toFixed(2)
                basic_goods_standard.goods_stock = Number(goods_json.goods_count).toFixed(2)
                basic_goods_standard.goods_unit = goods_json.goods_unit
                basic_goods_standard.goods_life =  Number(goods_json.goods_life)

                focusGoodsKind(goods_json.goods_kind_unique)
//                focusSupplier(goods_json.default_supplier_unique)
                supplierNameGene(goods_json.default_supplier_unique)
                focusUnit(goods_json.goods_unit)
            }

            function isHasData() {
                // || basic_goods_standard.goods_web_price != ""
                if (basic_goods_standard.goods_barcode != "" || basic_goods_standard.goods_name != "" || basic_goods_standard.goods_supplier != "" || basic_goods_standard.goods_abbr
                        != "" || basic_goods_standard.goods_standard != "" || basic_goods_standard.goods_price != "" || basic_goods_standard.goods_member_price
                        != "" || basic_goods_standard.goods_in_price != "" || basic_goods_standard.goods_stock != "" || basic_goods_standard.goods_unit != "") {
                    return true
                }
                toast.openWarn(qsTr("基础规格无信息"))
                return false
            }

            function checkData() {
                if (basic_goods_standard.goods_barcode == "") {
                    toast.openWarn(qsTr("基础规格" + "条码信息" + "有误"))
                    return false
                } else if (basic_goods_standard.goods_name == "") {
                    toast.openWarn(qsTr("基础规格" + "商品名" + "有误"))
                    return false
                } else if (basic_goods_standard.goods_price == "") {
                    toast.openWarn(qsTr("基础规格" + "价格" + "有误"))
                    return false
                } else if (basic_goods_standard.goods_in_price == "") {
                    toast.openWarn(qsTr("基础规格" + "进价" + "有误"))
                    return false
                }
                return true
            }

            Image {
                anchors.fill: parent
                source: background_img_path
            }

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20 * dpi_ratio
                anchors.leftMargin: 0
                anchors.rightMargin: 0
                spacing: 0

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 50 * dpi_ratio
                    color: ST.color_transparent

                    CusRect {
                        visible: basic_goods_standard.is_cloud_goods
                        anchors.fill: parent
                        color: ST.color_transparent

                        Image {
                            id: img_coloud_goods_img
                            x: 50 * dpi_ratio
                            width: 30 * dpi_ratio
                            height: width
                            source: "/Images/goods_cloud.png"
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        CusText {
                            id: t_coloud_goods_title
                            text: qsTr("云端商品")
                            anchors.left: img_coloud_goods_img.right
                            anchors.leftMargin: 20 * dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            font.pixelSize: 32 * dpi_ratio * configTool.fontRatio
                            font.bold: true
                            color: "#448DF6"
                        }

                        CusText {
                            id: t_coloud_goods_comment
                            anchors.left: t_coloud_goods_title.right
                            anchors.leftMargin: 20 * dpi_ratio
                            text: "可保存到本地"
                            anchors.verticalCenter: parent.verticalCenter
                            font.pixelSize: 28 * dpi_ratio * configTool.fontRatio
                            color: "#448DF6"
                        }
                    }

                    CusRect {
                        visible: !basic_goods_standard.is_cloud_goods
                        anchors.fill: parent
                        color: ST.color_transparent

                        Image {
                            id: local_coloud_goods_img
                            x: 50 * dpi_ratio
                            width: 30 * dpi_ratio
                            height: width
                            source: "/Images/goods_local.png"
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        CusText {
                            text: qsTr("本地商品")
                            anchors.left: local_coloud_goods_img.right
                            anchors.leftMargin: 20 * dpi_ratio
                            anchors.verticalCenter: parent.verticalCenter
                            font.pixelSize: 32 * dpi_ratio * configTool.fontRatio
                            font.bold: true
                            color: ST.color_green
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredHeight: 20 * dpi_ratio
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 72 * dpi_ratio
                    color: ST.color_transparent


                    RowLayout {
                        anchors.fill: parent
                        spacing: 0
                        CusSpacer {
                            Layout.preferredWidth: 20 * dpi_ratio
                        }
                        RectLable {
                            Layout.fillHeight: true
                            Layout.preferredWidth: 140 * dpi_ratio
                            is_required: true
                            text: qsTr("分类选择")
                        }

                        CusSpacer {
                            Layout.preferredWidth: 20 * dpi_ratio
                        }

                        CusComboBox {
                            id: combo_category1
                            Layout.preferredWidth: 255 * dpi_ratio
                            Layout.fillHeight: true
                            textRole: "goods_kind_name"
                            model: ListModel {
                                id: lm_category1
                            }
                            onCurrentIndexChanged: {
                                if (currentIndex == -1) {
                                    refreshCategory2("")
                                } else {
                                    refreshCategory2(lm_category1.get(currentIndex).goods_kind_unique)
                                }
                            }
                            font.pixelSize: 28 * dpi_ratio
                        }

                        CusSpacer {
                            Layout.preferredWidth: 30 * dpi_ratio
                        }

                        CusComboBox {
                            id: combo_category2
                            Layout.preferredWidth: 255 * dpi_ratio
                            Layout.fillHeight: true
                            textRole: "goods_kind_name"
                            model: ListModel {
                                id: lm_category2
                            }
                            font.pixelSize: 28 * dpi_ratio
                            onCurrentIndexChanged: {
                                if (currentIndex == -1) {
                                    title_bar.goods_kind_unique = ""
                                } else {
                                    title_bar.goods_kind_unique = lm_category2.get(currentIndex).goods_kind_unique
                                }
                            }
                        }

                        CusSpacer {
                            Layout.fillWidth: true
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredHeight: 10 * dpi_ratio
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 390 * dpi_ratio
                    color: ST.color_transparent
                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 0
                        anchors.leftMargin: 20 * dpi_ratio
                        anchors.rightMargin: 20 * dpi_ratio

                        CusSpacer {
                            Layout.fillHeight: true
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 90 * dpi_ratio
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 0

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 520 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio

                                        RectLable {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            is_required: true
                                            text: qsTr("商品条码")
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            RowLayout {
                                                anchors.fill: parent
                                                CusTextField {
                                                    enabled: basic_goods_standard.is_create
                                                    Layout.fillWidth: true
                                                    Layout.preferredHeight: 65 * ST.dpi
                                                    Layout.alignment: Qt.AlignVCenter
                                                    font.pixelSize: 28 * dpi_ratio
                                                    text: basic_goods_standard.goods_barcode
                                                    background_color_enabled: ST.color_white_pure
                                                    onTextEdited: {
                                                        basic_goods_standard.goods_barcode = text
                                                    }
                                                    keyboard_status: 1
                                                    normal_keyboard_x: 725 * dpi_ratio
                                                    normal_keyboard_y: 565 * dpi_ratio

                                                    digital_keyboard_x: 1160 * dpi_ratio
                                                    digital_keyboard_y: 320 * dpi_ratio

                                                    onAccepted: {
                                                        var goods_json = JSON.parse(goodsManager.getGoodsByBarcode4Qml(text))
                                                        if (goods_json) {
                                                            basic_goods_standard.setGoodsByBarcode(goods_json.goods_barcode, true)
                                                        } else {
                                                            setColudGoodsByBarcode(text, function (is_succ) {
                                                                if (!is_succ) {
                                                                    basic_goods_standard.is_cloud_goods = false
                                                                } else {

                                                                }
                                                            })
                                                        }
                                                    }
                                                }

                                                CusButton {
                                                    Layout.preferredHeight: 65 * ST.dpi
                                                    Layout.preferredWidth: height
                                                    Layout.alignment: Qt.AlignVCenter
                                                    visible: basic_goods_standard.is_create

                                                    Image {
                                                        width: 50 * ST.dpi
                                                        height: width
                                                        anchors.centerIn: parent
                                                        source: "/Images/plus.png"
                                                    }

                                                    onClicked: {
                                                        goodsStock.text = "1"
                                                        var cur_goods_barcode = goodsManager.contrastGenerateCustomBarcode(basic_goods_standard.goods_cheng_type == 1,medium_goods_standard.goods_barcode,high_goods_standard.goods_barcode)

                                                        while ((cur_goods_barcode == medium_goods_standard.goods_barcode)
                                                               || (cur_goods_barcode == high_goods_standard.goods_barcode)) {
                                                            cur_goods_barcode = goodsManager.contrastGenerateCustomBarcode(basic_goods_standard.goods_cheng_type == 1,medium_goods_standard.goods_barcode,high_goods_standard.goods_barcode)
                                                        }

                                                        basic_goods_standard.goods_barcode = cur_goods_barcode

                                                        //如果未设置分类 自动选定
                                                        if (getIsGoodsKindEmpty()) {
                                                            focusGoodsKind(goodsKindManager.defaultGoodsKind)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                CusSpacer {
                                    Layout.fillWidth: true
                                }

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 520 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio

                                        RectLable {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            is_required: true
                                            text: qsTr("商品名称")
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            CusTextField {
                                                anchors.verticalCenter: parent.verticalCenter
                                                width: parent.width
                                                height: 65 * dpi_ratio
                                                font.pixelSize: 28 * dpi_ratio
                                                text: basic_goods_standard.goods_name
                                                onTextEdited: {
                                                    basic_goods_standard.goods_name = text

                                                }
                                                background_color_enabled: ST.color_white_pure
                                                normal_keyboard_x: 619 * dpi_ratio
                                                normal_keyboard_y: 459 * dpi_ratio

                                                digital_keyboard_x: 1160 * dpi_ratio
                                                digital_keyboard_y: 234 * dpi_ratio
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        CusRect {

                            Layout.fillWidth: true
                            Layout.preferredHeight: 90 * dpi_ratio
                            color: ST.color_transparent
                            z:90;
                            Rectangle
                            {
                                id:fuzzy_search_rec
                                anchors.left:parent.left;
                                anchors.leftMargin:160 * dpi_ratio
                                anchors.top:parent.bottom;
                                anchors.topMargin: -10 * dpi_ratio;
                                visible: false;
                                width: 360 * dpi_ratio;
                                height: 203 * dpi_ratio;
                                color:ST.color_white
                                border.width: 1  * dpi_ratio;
                                border.color: "#00BD75";
                                radius: 10  * dpi_ratio;
                                z:100;
                                ListView
                                {
                                    id:textListView
                                    anchors.top: fuzzy_search_rec.top;
                                    anchors.topMargin: 4 *dpi_ratio
                                    anchors.left: parent.left;
                                    anchors.leftMargin:3 *dpi_ratio
                                    visible: fuzzy_search_rec.visible;
                                    width: 352 * dpi_ratio;
                                    clip:true
                                    height: 196 * dpi_ratio;
                                    model:fuzzy_search_model
                                    delegate: Rectangle
                                    {
                                        id: textListViewDelegate;
                                        width:  352 * dpi_ratio;
                                        height: 46 * dpi_ratio;
                                        color:"#FFFFFF"
                                        anchors.horizontalCenter:textListView.horizontalCenter;
                                        Rectangle
                                        {
                                            id:textListViewDelegate1
                                            anchors.left:textListViewDelegate.left
                                            anchors.verticalCenter: textListViewDelegate.verticalCenter
                                            width:  236 * dpi_ratio;
                                            height: 46 * dpi_ratio;
                                            Text
                                            {
                                                id:textListViewDelegateText1
                                                anchors.left: parent.left
                                                anchors.leftMargin: 15 *dpi_ratio
                                                anchors.verticalCenter: parent.verticalCenter
                                                font.family: ST.fontFamilyYaHei;
                                                font.pointSize: 20 * dpi_ratio;
                                                color: supplier_name === "新增供货商   +"? "#082CA4" : "#333333";
                                                text: supplier_name.length >10? (supplier_name.substring(0,10) + "..."):supplier_name;
                                            }
                                        }
                                        MouseArea
                                        {
                                            anchors.fill:textListViewDelegate;
                                            onClicked:
                                            {
                                                textListViewDelegateText1.color="red"
                                                fuzzy_search_timer.start()
                                                if(supplier_name.toString() =="新增供货商   +"){
                                                    showAddSupplier()
                                                    fuzzy_search_rec.visible = false
                                                }else{
                                                    for (var i = 0; i < lm_supplier.count; ++i) {
                                                        var item = lm_supplier.get(i)
                                                        supplier_nameTemp = item.supplier_name
                                                        if (supplier_name.toString() === supplier_nameTemp.toString()) {
                                                            basic_goods_standard.goods_supplier =  item.supplier_unique
                                                        }
                                                    }
                                                    search_input.text = supplier_name
                                                    fuzzy_search_rec.visible = false
                                                    fuzzy_search_model.clear()
                                                    selectOpen = false
                                                }
                                            }
                                            function showAddSupplier() {
                                                window_root.loader_4_confirm.sourceComponent = compo_show_add_supplier
                                                window_root.loader_4_confirm.item.open()
                                            }

                                            Component {
                                                id: compo_show_add_supplier

                                                PopupSuppliersManger {
                                                    id: supplier_add_confirm
                                                    title_name: qsTr("新增供货商")
                                                    onConfirm: {

                                                        supplierControl.addSupplier4Qml(function (is_succ, data) {
                                                            if (is_succ) {
                                                                supplierControl.reqSupplier4Qml(function callback(is_succ, data) {
                                                                    if (is_succ)
                                                                        refreshSupplier()
                                                                        logMgr.logEvtInfo4Qml("更新本地供货商成功")
                                                                })
                                                            } else {
                                                                var json_doc = JSON.parse(data)
                                                                var json_doc_data = json_doc["msg"]
                                                                toast.openInfo(json_doc_data);
                                                            }
                                                        }, supplierName)
                                                    }
                                                    onSigClose: {
                                                        search_input.forceActiveFocus()
                                                    }
                                                }
                                            }
                                        }
                                        Timer
                                        {
                                            id: fuzzy_search_timer;
                                            interval: 100;
                                            repeat: false;
                                            running: false;
                                            onTriggered:
                                            {
                                                textListViewDelegate1.color="#ffffff"
                                                textListViewDelegateText1.color="#333333"
                                            }
                                        }
                                    }
                                    ListModel
                                    {
                                        id:fuzzy_search_model
                                    }
                                }
                            }
                            RowLayout {
                                anchors.fill: parent
                                spacing: 0

                                CusRect {
                                    id: supplierNameCus
                                    Layout.preferredHeight: 65 * dpi_ratio
                                    Layout.preferredWidth: 520 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio

                                        RectLable {
                                            id:supplierName
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            is_required: false
                                            text: qsTr("供货商")
                                        }
                                        CusRect {
                                            id:supplierRec
                                            anchors.verticalCenter: parent.verticalCenter
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 360 * dpi_ratio
                                            color: ST.color_white
                                            border.color: ST.color_grey_border2
                                            border.width: 1 *dpi_ratio
                                            radius:ST.radius
                                            ListModel {
                                                   id: lm_supplier
                                               }
                                            Item {
                                                anchors.fill: parent
                                            Image {
                                                id: searchIcon
                                                source: "/Images/Subscript_black.png"
                                                width: 24 * dpi_ratio
                                                height: 24 * dpi_ratio
                                                anchors.right: parent.right
                                                anchors.rightMargin: 10 * dpi_ratio
                                                anchors.verticalCenter: search_input.verticalCenter
                                            }
                                            Text {
                                                id: placeholder
                                                text: "请选择"
                                                color: "#00BD75"
                                                font.family: ST.fontFamilyYaHei
                                                font.pointSize: 18 * dpi_ratio
                                                anchors.left: search_input.left
                                                anchors.leftMargin: 2 * dpi_ratio
                                                anchors.verticalCenter: search_input.verticalCenter
                                                visible: search_input.text === ""
                                            }
                                            TextInput
                                            {
                                                id: search_input;
                                                width: 490  * dpi_ratio;
                                                height: 42  * dpi_ratio;
                                                anchors.left: parent.left;
                                                anchors.leftMargin: 15 *dpi_ratio
                                                anchors.verticalCenter: parent.verticalCenter;
                                                anchors.horizontalCenter: parent.horizontalCenter;
                                                verticalAlignment: Text.AlignVCenter;
                                                horizontalAlignment: Text.AlignLeft
                                                font.family: ST.fontFamilyYaHei
                                                font.pointSize: 18 * dpi_ratio
                                                maximumLength: 30  * dpi_ratio;
                                                clip: true;
                                                text:supplier_name
                                                onFocusChanged: {
                                                    if (search_input.focus) {
                                                        supplierRec.border.color = "#00BD75"
                                                    } else {
                                                        supplierRec.border.color = ST.color_grey_border2
                                                        fuzzy_search_rec.visible = false
                                                    }
                                                }

                                                MouseArea
                                                {

                                                    anchors.fill: search_input;
                                                    onPressed:
                                                    {
                                                        window_root.keyboard_c.closeAll()
//                                                        window_root.keyboard_c.setDigitalKeyboardPos(1160 * dpi_ratio, 320 * dpi_ratio)
//                                                        window_root.keyboard_c.openDigitalKeyboard()
                                                        window_root.keyboard_c.setNormalKeyboardPos(725 * dpi_ratio, 565 * dpi_ratio)
                                                        window_root.keyboard_c.openNormalKeyboard()
                                                        search_input.focus = true;
                        //                                search_input.text = "";
                                                        searchOpen = !searchOpen
                                                        if(searchOpen){
                                                            fuzzy_search_model.clear()
                                                            fuzzy_search_model.append({
                                                                                 "supplier_name":"新增供货商   +",
                                                                                 "supplier_unique": ""
                                                                             })
                                                            for (var i = 0; i < lm_supplier.count; ++i) {

                                                                var item = lm_supplier.get(i)
                                                                supplier_nameTemp = item.supplier_name
                                                                if (supplier_nameTemp.toString().includes(search_input.text.toString())) {
                                                                    fuzzy_search_model.append({
                                                                                         "supplier_name":item.supplier_name,
                                                                                         "supplier_unique": item.supplier_unique
                                                                                     })

                                                                }
                                                            }
                                                            fuzzy_search_rec.visible = true
                                                        }
                                                        else{
                                                            fuzzy_search_rec.visible = false
                                                            fuzzy_search_model.clear()
                                                        }

                                                    }
                                                }
                                                onAccepted:
                                                {
                                                }
                                                onTextChanged:
                                                {
                                                    if(selectOpen && search_input.focus){
                                                        fuzzy_search_model.clear()
                                                        fuzzy_search_model.append({
                                                                             "supplier_name":"新增供货商   +",
                                                                             "supplier_unique": ""
                                                                         })
                                                        for (var i = 0; i < lm_supplier.count; ++i) {

                                                            var item = lm_supplier.get(i)
                                                            supplier_nameTemp = item.supplier_name
                                                            if (supplier_nameTemp.toString().includes(search_input.text.toString())) {
                                                                fuzzy_search_model.append({
                                                                                     "supplier_name":item.supplier_name,
                                                                                     "supplier_unique": item.supplier_unique
                                                                                 })

                                                            }
                                                        }
                                                        fuzzy_search_rec.visible = true
                                                    }

                                                }
                                            }
                                            }
                                            //                    CusComboBox {
                        //                        id: combo_supplier
                        //                        anchors.verticalCenter: parent.verticalCenter
                        //                        width: parent.width
                        //                        height: 65 * dpi_ratio
                        //                        font.family: ST.fontFamilyYaHei
                        //                        font.pointSize: 18 * dpi_ratio
                        //                        textRole: "supplier_name"
                        //                        model: ListModel {
                        //                            id: lm_supplier
                        //                        }
                        //                        onCurrentIndexChanged: {
                        //                            if (currentIndex == -1) {
                        //                               goods_supplier = ""
                        //                            } else {
                        //                                goods_supplier = lm_supplier.get(currentIndex).supplier_unique
                        //                            }
                        //                        }
                        //                    }
                                        }
//                                        CusRect {
//                                            Layout.fillHeight: true
//                                            Layout.fillWidth: true
//                                            color: ST.color_transparent

//                                            CusComboBox {
//                                                id: combo_supplier
//                                                anchors.verticalCenter: parent.verticalCenter
//                                                width: parent.width
//                                                height: 65 * dpi_ratio
//                                                font.pixelSize: 28 * dpi_ratio
//                                                textRole: "supplier_name"
//                                                model: ListModel {
//                                                    id: lm_supplier
//                                                }
//                                                onCurrentIndexChanged: {
//                                                    if (currentIndex == -1) {
//                                                        basic_goods_standard.goods_supplier = ""
//                                                    } else {
//                                                        basic_goods_standard.goods_supplier = lm_supplier.get(currentIndex).supplier_unique
//                                                    }
//                                                }
//                                            }
//                                        }
                                    }
                                }
                                CusSpacer {
                                    Layout.fillWidth: true
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 520 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio

                                        RectLable {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            is_required: false
                                            text: qsTr("品名缩写")
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            CusTextField {
                                                anchors.verticalCenter: parent.verticalCenter
                                                width: parent.width
                                                height: 65 * dpi_ratio
                                                font.pixelSize: 28 * dpi_ratio
                                                text: basic_goods_standard.goods_abbr
                                                onTextEdited: {
                                                    basic_goods_standard.goods_abbr = text
                                                }
                                                enabled: false
                                                normal_keyboard_x: 725 * dpi_ratio
                                                normal_keyboard_y: 565 * dpi_ratio

                                                digital_keyboard_x: 1160 * dpi_ratio
                                                digital_keyboard_y: 435 * dpi_ratio
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 90 * dpi_ratio
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 0

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 520 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            color: ST.color_transparent
                                            CusText {
                                                text: qsTr("保质期")
                                                anchors.verticalCenter: parent.verticalCenter
                                                anchors.right: parent.right
                                                font.pixelSize: 28 * dpi_ratio * configTool.fontRatio2
                                            }
                                        }
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            CusTextField {
                                                anchors.verticalCenter: parent.verticalCenter
                                                width: parent.width
                                                height: 65 * dpi_ratio
                                                font.pixelSize: 28 * dpi_ratio
                                                text: (basic_goods_standard.goods_life !== 0) ?basic_goods_standard.goods_life:"0"
                                                maximumLength: 20  * dpi_ratio;
                                                clip: true;
                                                validator: RegularExpressionValidator {
                                                    regularExpression: /^\d+$/}

                                                onTextEdited: {
                                                    basic_goods_standard.goods_life = text
                                                }
                                                keyboard_status: 1
                                                background_color_enabled: ST.color_white_pure
                                                normal_keyboard_x: 619 * dpi_ratio
                                                normal_keyboard_y: 459 * dpi_ratio

                                                digital_keyboard_x: 1160 * dpi_ratio
                                                digital_keyboard_y: 234 * dpi_ratio
                                            }

                                        }
                                    }
                                }
                                CusSpacer {
                                    Layout.fillWidth: true
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 520 * dpi_ratio
                                    color: ST.color_transparent
                                    visible: basic_goods_standard.is_create

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio
                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            color: ST.color_transparent
                                            CusText {
                                                text: qsTr("生产日期")
                                                anchors.verticalCenter: parent.verticalCenter
                                                anchors.right: parent.right
                                                font.pixelSize: 28 * dpi_ratio * configTool.fontRatio2
                                            }
                                        }

                                        CusRect {
                                            Layout.fillWidth: true
                                            color: ST.color_transparent
                                            width: parent.width
                                            height: 65 * dpi_ratio

                                            border.width: 2  * dpi_ratio;
                                            border.color: "#D8D8D8";
                                            radius: 10  * dpi_ratio;
                                            CusText {
                                                id: btn_calendar_prodCopy
                                                anchors.centerIn: parent
                                                visible:false
                                                onTextChanged: {
                                                    if(!initCreateDate){
//                                                        btn_calendar_prod.text  = text.split(" ")[0]
                                                        initCreateDate = true
                                                    }
                                                    else{
//                                                        btn_calendar_prod.text  = text.split(" ")[0]
                                                    }
                                                }
                                                Component.onCompleted: {
                                                    text = utils4Qml.getCurDate() + " 00:00"
                                                }
                                            }
                                            Image
                                            {
                                                id:goodsProdImg
                                                width: 29 * dpi_ratio;
                                                height: 29 * dpi_ratio;
                                                anchors.verticalCenter: parent.verticalCenter;
                                                anchors.left: parent.left;
                                                anchors.leftMargin: 24 * dpi_ratio;
                                                source: "/Images/dateSelect.png";
                                            }
                                            Text
                                            {
                                                id:goodsProdRecText3
                                                width: 170  * dpi_ratio;
                                                height: 64  * dpi_ratio;
                                                font.family: ST.fontFamilySiYan;
                                                font.pointSize: 18  * dpi_ratio;
                                                anchors.left: goodsProdImg.right;
                                                anchors.leftMargin: 24 * dpi_ratio;
                                                verticalAlignment: Text.AlignVCenter;
                                                color: "#999999";
                                                text:qsTr("请选择生产日期");
                                                visible:btn_calendar_prod.text == "";
                                            }
                                            CusText {
                                                id: btn_calendar_prod
                                                anchors.centerIn: parent
                                                property bool is_need_init: true
                                                onTextChanged: {
                                                    basic_goods_standard.goods_prod  = btn_calendar_prod.text
                                                }
                                            }
                                            MouseArea {
                                                anchors.fill: parent
                                                onClicked: {
                                                    var date_time_vec = btn_calendar_prodCopy.text.split(" ")

                                                    if (date_time_vec.length == 2) {

                                                        var date_vec = date_time_vec[0].split("-")
                                                        var year = date_vec[0]
                                                        var month = date_vec[1]
                                                        var day = date_vec[2]

                                                        var time_vec = date_time_vec[1].split(":")
                                                        var hour = time_vec[0]
                                                        var minute = time_vec[1]

                                                        openCalendarBegin(date_time_vec[0], hour, minute)
                                                    }
                                                }
                                            }
                                            Component {
                                                id: calendar_begin
                                                CusDateTimePickerDay {
                                                    is_floor: true
                                                    target_control: btn_calendar_prod
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 90 * dpi_ratio
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 0

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 520 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio

                                        RectLable {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            is_required: true
                                            text: qsTr("计价类型")
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            RowLayout {
                                                anchors.fill: parent

                                                CusSpacer {
                                                    Layout.fillWidth: true
                                                }

                                                CusRadioManual {
                                                    text: qsTr("计件")
                                                    Layout.preferredHeight: 40 * dpi_ratio
                                                    Layout.preferredWidth: 150 * dpi_ratio
                                                    Layout.alignment: Qt.AlignVCenter
                                                    control_text.font.pixelSize: 28 * dpi_ratio

                                                    checked: basic_goods_standard.goods_cheng_type == 0

                                                    onClicked: {
                                                        basic_goods_standard.goods_cheng_type = 0
                                                    }
                                                }

                                                CusRadioManual {
                                                    text: qsTr("计重")
                                                    Layout.preferredHeight: 40 * dpi_ratio
                                                    Layout.preferredWidth: 150 * dpi_ratio
                                                    Layout.alignment: Qt.AlignVCenter
                                                    control_text.font.pixelSize: 28 * dpi_ratio

                                                    checked: basic_goods_standard.goods_cheng_type == 1

                                                    onClicked: {
                                                        basic_goods_standard.goods_cheng_type = 1
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                CusSpacer {
                                    Layout.fillWidth: true
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 520 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio

                                        RectLable {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            is_required: false
                                            text: qsTr("商品规格")
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            CusTextField {
                                                anchors.verticalCenter: parent.verticalCenter
                                                width: parent.width
                                                height: 65 * dpi_ratio
                                                font.pixelSize: 28 * dpi_ratio
                                                text: basic_goods_standard.goods_standard
                                                onTextEdited: {
                                                    basic_goods_standard.goods_standard = text
                                                }

                                                normal_keyboard_x: 434 * dpi_ratio
                                                normal_keyboard_y: 544 * dpi_ratio

                                                digital_keyboard_x: 1160 * dpi_ratio
                                                digital_keyboard_y: 234 * dpi_ratio
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        CusSpacer {
                            Layout.fillHeight: true
                        }
                    }
                    CusSpacer {
                        Layout.fillHeight: true
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 124 * dpi_ratio
                    color: "#f1f1f1"

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 0
                        anchors.leftMargin: 20 * dpi_ratio
                        anchors.rightMargin: 20 * dpi_ratio

                        CusSpacer {
                            Layout.fillHeight: true
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 90 * dpi_ratio
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 0

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 350 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio

                                        RectLable {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            is_required: true
                                            text: qsTr("零售单价")
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            CusTextField {
                                                id:retailPrice
                                                anchors.verticalCenter: parent.verticalCenter
                                                width: parent.width
                                                height: 65 * dpi_ratio
                                                background_color: ST.color_white_pure
                                                font.pixelSize: 28 * dpi_ratio
                                                text: basic_goods_standard.goods_price
                                                comnent: qsTr("零售单价")

                                                property bool flagNull: false //如果网购单价和会员价输入框都为空则设为true，否则设置为false
                                                onTextEdited: {
                                                    basic_goods_standard.goods_price = text
                                                    //如果都为空，则价格与零售单价同步
                                                    if (flagNull) {
                                                        basic_goods_standard.goods_member_price = text
                                                        basic_goods_standard.goods_in_price = (Number(text) * (1 - configTool.normalGoodsProfitRatio)).toFixed(2)
                                                    }
                                                }

                                                onFocusChanged: {
                                                    if (!focus)
                                                        return

                                                    if (basic_goods_standard.goods_member_price == "" && basic_goods_standard.goods_in_price == "") {
                                                        flagNull = true
                                                    } else {
                                                        flagNull = false
                                                    }
                                                }

                                                is_clicked_select_all: true

                                                keyboard_status: 1

                                                normal_keyboard_x: 725 * dpi_ratio
                                                normal_keyboard_y: 565 * dpi_ratio

                                                digital_keyboard_x: 1160 * dpi_ratio
                                                digital_keyboard_y: 320 * dpi_ratio
                                            }
                                        }
                                    }
                                }

                                CusSpacer {
                                    Layout.fillWidth: true
                                }

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 350 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio

                                        RectLable {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            is_required: false
                                            text: qsTr("会员单价")
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            CusTextField {
                                                anchors.verticalCenter: parent.verticalCenter
                                                width: parent.width
                                                height: 65 * dpi_ratio
                                                background_color: ST.color_white_pure
                                                font.pixelSize: 28 * dpi_ratio
                                                text: basic_goods_standard.goods_member_price
                                                comnent: qsTr("会员单价")
                                                onTextEdited: {
                                                    basic_goods_standard.goods_member_price = text
                                                }

                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                normal_keyboard_x: 725 * dpi_ratio
                                                normal_keyboard_y: 565 * dpi_ratio

                                                digital_keyboard_x: 1160 * dpi_ratio
                                                digital_keyboard_y: 320 * dpi_ratio
                                            }
                                        }
                                    }
                                }

                                CusSpacer {
                                    Layout.fillWidth: true
                                }

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 350 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio

                                        RectLable {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: !basic_goods_standard.is_create?150* dpi_ratio :140 * dpi_ratio
                                            is_required: true
                                            text: !basic_goods_standard.is_create?qsTr("最近入库价"):qsTr("采购单价")
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            CusTextField {
                                                anchors.verticalCenter: parent.verticalCenter
                                                width: parent.width
                                                height: 65 * dpi_ratio
                                                font.pixelSize: 28 * dpi_ratio
                                                background_color: ST.color_white_pure
                                                enabled: basic_goods_standard.is_create
                                                text: !basic_goods_standard.is_create?basic_goods_standard.good_stock_price:basic_goods_standard.goods_in_price
                                                comnent: qsTr("采购单价")
                                                onTextEdited: {
                                                    basic_goods_standard.goods_in_price = text
                                                }

                                                is_clicked_select_all: true

                                                keyboard_status: 1
                                                normal_keyboard_x: 725 * dpi_ratio
                                                normal_keyboard_y: 565 * dpi_ratio

                                                digital_keyboard_x: 1160 * dpi_ratio
                                                digital_keyboard_y: 320 * dpi_ratio
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        CusSpacer {
                            Layout.fillHeight: true
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure
                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 0
                        anchors.leftMargin: 20 * dpi_ratio
                        anchors.rightMargin: 20 * dpi_ratio

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 90 * dpi_ratio
                            color: ST.color_transparent
                            //visible: configTool.isShowStock
                            visible: true

                            RowLayout {
                                anchors.fill: parent
                                spacing: 0

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 520 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 20 * dpi_ratio

                                        RectLable {
                                            Layout.fillHeight: true
                                            Layout.preferredWidth: 140 * dpi_ratio
                                            is_required: true
                                            text: qsTr("商品库存")
                                        }

                                        CusRect {
                                            Layout.fillHeight: true
                                            Layout.fillWidth: true
                                            color: ST.color_transparent

                                            RowLayout {
                                                anchors.fill: parent
                                                spacing: 10 * dpi_ratio
                                                CusTextField {
                                                    id:goodsStock
                                                    Layout.alignment: Qt.AlignVCenter
                                                    Layout.fillWidth: true
                                                    Layout.preferredHeight: 65 * dpi_ratio
                                                    background_color: ST.color_white_pure
                                                    font.pixelSize: 28 * dpi_ratio
                                                    text:basic_goods_standard.goods_stock
                                                    validator: RegularExpressionValidator {
                                                        regularExpression: /\d*(\.\d{0,2})?$/
                                                    }
                                                    onTextEdited: {
                                                        basic_goods_standard.goods_stock = text
                                                    }

                                                    enabled: basic_goods_standard.is_create

                                                    keyboard_status: 1
                                                    normal_keyboard_x: 725 * dpi_ratio
                                                    normal_keyboard_y: 565 * dpi_ratio

                                                    digital_keyboard_x: 1160 * dpi_ratio
                                                    digital_keyboard_y: 320 * dpi_ratio

                                                    is_clicked_select_all: true
                                                }
                                                CusComboBox {
                                                    id: combo_goods_unit
                                                    Layout.preferredWidth: 150 * dpi_ratio
                                                    Layout.preferredHeight: 65 * dpi_ratio
                                                    model: lm_goods_unit
                                                    textRole: "goods_unit"
                                                    font.pixelSize: 28 * dpi_ratio

                                                    onCurrentIndexChanged: {
                                                        if (currentIndex == -1)
                                                            basic_goods_standard.goods_unit = ""
                                                        else
                                                            basic_goods_standard.goods_unit = lm_goods_unit.get(currentIndex).goods_unit
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                CusSpacer {
                                    Layout.fillWidth: true
                                }

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 520 * dpi_ratio
                                    color: ST.color_transparent

                                    RowLayout {
                                        anchors.fill: parent
                                        spacing: 25 * dpi_ratio

                                        CusButton {
                                            Layout.preferredHeight: 65 * dpi_ratio
                                            Layout.preferredWidth: 164 * dpi_ratio
                                            text: qsTr("入库")
                                            Layout.alignment: Qt.AlignVCenter

                                            visible: !basic_goods_standard.is_create

                                            onClicked: {
                                                showStockChangeIn(basic_goods_standard.goods_barcode, 1,basic_goods_standard.good_stock_price,basic_goods_standard.goods_unit,basic_goods_standard.goods_cheng_type)
                                            }
                                        }

                                        CusButton {
                                            Layout.preferredHeight: 65 * dpi_ratio
                                            Layout.preferredWidth: 164 * dpi_ratio
                                            text: qsTr("出库")
                                            Layout.alignment: Qt.AlignVCenter
                                            color: ST.color_orange

                                            visible: !basic_goods_standard.is_create

                                            onClicked: {
                                                if(shopControl.goodsInPriceType == 2){
                                                   showStockChangeOutFirst(basic_goods_standard.goods_barcode, 1,basic_goods_standard.goods_unit,basic_goods_standard.good_stock_price,basic_goods_standard.goods_cheng_type);
                                                }
                                                else if(shopControl.goodsInPriceType == 1 || shopControl.goodsInPriceType == 0){
                                                   showStockChangeOutMean(basic_goods_standard.goods_barcode, 1,basic_goods_standard.goods_unit,basic_goods_standard.good_stock_price,basic_goods_standard.goods_cheng_type);
                                                }
                                            }
                                        }

                                        CusSpacer {
                                            Layout.fillWidth: true
                                        }
                                    }
                                }
                            }
                        }

                        CusSpacer {
                            Layout.fillHeight: true
                        }

                        CusRect {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 80 * dpi_ratio
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                spacing: 30 * dpi_ratio

                                CusButton {
                                    id: btn_down_to_weigh
                                    text: barcodeLabelScaleCtrl.is_transporting ? qsTr("下载到秤中") : qsTr("下载到秤")
                                    width: 180 * dpi_ratio
                                    Layout.fillHeight: true
                                    focusPolicy: Qt.ClickFocus
                                    enabled: !barcodeLabelScaleCtrl.is_transporting
                                    onClicked: {
                                        if (barcodeLabelScaleCtrl.send2BarcodeScaleWizard()) {
                                            toast.openInfo(qsTr("下载到秤成功"))
                                        } else {
                                            toast.openWarn(qsTr("下载到秤失败"))
                                        }
                                    }

                                    visible: (!basic_goods_standard.is_create) || basic_goods_standard.goods_barcode == ""
                                }

                                CusButton {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    color: ST.color_blue_deeper
                                    text: qsTr("打印价签")
                                    onClicked: {
                                        printPriceTag(basic_goods_standard.goods_barcode)
                                    }
                                    visible: basic_goods_standard.goods_barcode != "" && !basic_goods_standard.is_create
                                }

                                CusSpacer {
                                    Layout.fillWidth: true
                                }

                                CusButton {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    color: ST.color_grey_btn
                                    text: qsTr("清空")
                                    onClicked: {
                                        resetGoodsInfo()                                        
                                        tf_godown_search.forceActiveFocus()
                                    }
                                }

                                CusButton {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    color: ST.color_red
                                    text: qsTr("删除")
                                    onClicked: {
                                        showDelCurGoodsConfirm(basic_goods_standard.goods_barcode)
                                        tf_godown_search.forceActiveFocus()
                                    }
                                    visible: basic_goods_standard.goods_barcode != "" && !basic_goods_standard.is_create
                                }

                                CusButton {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    text: qsTr("保存")
                                    onClicked: {
                                        if(basic_goods_standard.goods_unit === ""){
                                            toast.openInfo(qsTr("商品单位未选择"))
                                            return
                                        }
                                        if (basic_goods_standard.is_create) {
                                            createGoods_v2()
                                        } else {
                                            updateGoods_v2()
                                        }
                                        initCreateDate = false
                                        tf_godown_search.forceActiveFocus()
                                    }
                                    visible: basic_goods_standard.goods_barcode != ""
                                }
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            Layout.fillHeight: true
            Layout.preferredWidth: 740 * dpi_ratio
            color: ST.color_transparent
            radius: ST.radius

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                CusRect {
                    id: title_bar
                    Layout.fillWidth: true
                    Layout.preferredHeight: 90 * dpi_ratio
                    radius: ST.radius
                    color: ST.color_white_pure
                    z: 100

                    function resetInfo() {
                        goods_kind_unique = ""
                    }

                    function checkData() {
                        return true
                        var is_valid = goods_kind_unique != ""
                        if (!is_valid)
                            toast.openWarn(qsTr("分类信息有误"))
                        return is_valid
                    }

                    property string goods_kind_unique: ""

                    RowLayout {
                        anchors.verticalCenter: parent.verticalCenter
                        height: 72 * dpi_ratio
                        spacing: 40 * dpi_ratio
                        anchors.left: parent.left
                        anchors.leftMargin: 45 * dpi_ratio
                        anchors.right: parent.right
                        anchors.rightMargin: 45 * dpi_ratio

                        CusSpacer {
                            Layout.fillWidth: true
                        }

                        CusRect {
                            Layout.preferredHeight: 72 * dpi_ratio
                            Layout.preferredWidth: 500 * dpi_ratio * configTool.fontRatio
                            //Layout.alignment: Qt.AlignVCenter
                            Layout.rightMargin: 200 * dpi_ratio
                            radius: ST.radius
                            color: ST.color_grey

                            RowLayout {
                                anchors.fill: parent

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: height
                                    color: ST.color_transparent
                                    Image {
                                        anchors.fill: parent
                                        source: "/Images/search_image.png"
                                        anchors.margins: 12 * dpi_ratio
                                    }
                                }

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    color: ST.color_transparent

                                    CusTextField {
                                        id: tf_godown_search
                                        placeholderText: qsTr("商品条码/商品名称")
                                        border.width: 0
                                        keyboard_status: 1
                                        anchors.fill: parent
                                        digital_keyboard_x: (440 * dpi_ratio)
                                        digital_keyboard_y: 234 * dpi_ratio

                                        normal_keyboard_x: 12 * dpi_ratio
                                        normal_keyboard_y: 234 * dpi_ratio

                                        font.pixelSize: 32 * dpi_ratio

                                        onTextChanged: {
                                            if (text == "") {
                                                model_goods_data.search_str = ""
                                                timer.stop()
                                            } else {
                                                timer.restart()
                                            }
                                        }

                                        onAccepted: {
                                            timer.stop()

                                            tf_godown_search.searchData()
                                            tf_godown_search.selectAll()

                                            window_root.keyboard_c.closeAll()

                                            var is_full_matching_goods = false

                                            //搜到商品
                                            if (model_goods_data.rowCount() > 0) {
                                                var json_tmp = JSON.parse(model_goods_data.getItemByIndex(0))
                                                if (json_tmp.goods_barcode === model_goods_data.search_str) {
                                                    is_full_matching_goods = true
                                                    godown_page_old_root.setCurGoodsByBarcode(json_tmp.goods_barcode)
                                                    tf_godown_search.clear()
                                                    window_root.keyboard_c.closeAll()
                                                }
                                            }

                                            if (!is_full_matching_goods) {
                                                // 搜索不到商品 检查是不是云端商品
                                                resetGoodsInfo()

                                                setColudGoodsByBarcode(text, function (is_succ) {
                                                    // 未搜索到且全是数字将条码添加到创建商品处
                                                    if (!is_succ) {
                                                        trySetText2Barcode(text)
                                                    }
                                                    tf_godown_search.text = ""
                                                })
                                            }
                                        }

                                        Timer {
                                            id: timer
                                            interval: 800
                                            repeat: false
                                            onTriggered: {
                                                tf_godown_search.searchData()
                                            }
                                        }

                                        function searchData() {
                                            if (tf_godown_search.text == "")
                                                return

                                            model_goods_data.search_str = tf_godown_search.text
                                        }

                                        CusRect {
                                            color: ST.color_transparent
                                            width: tf_godown_search.height
                                            height: width
                                            anchors.right: parent.right

                                            Image {
                                                width: 38 * dpi_ratio
                                                height: width
                                                anchors.centerIn: parent
                                                fillMode: Image.PreserveAspectFit
                                                visible: tf_godown_search.text != ""
                                                source: "/Images/yuebuzu_icon.png"
                                            }

                                            MouseArea {
                                                anchors.fill: parent
                                                onClicked: {
                                                    if (tf_godown_search.text != "") {
                                                        tf_godown_search.text = ""
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            GoodsDataModel {
                                id: model_goods_data
                                goods_data: goodsManager
                            }

                            CusRect {
                                id: rect_search_goods
                                anchors.top: parent.bottom
                                width: parent.width
                                height: 550 * dpi_ratio
                                color: ST.color_white_pure
                                visible: model_goods_data.search_str != ""

                                ColumnLayout {
                                    anchors.fill: parent
                                    spacing: 0

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 35 * dpi_ratio
                                        color: ST.color_grey_border

                                        RowLayout {
                                            anchors.fill: parent
                                            spacing: 1 * dpi_ratio
                                            anchors.margins: 1 * dpi_ratio

                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.preferredWidth: 220 * dpi_ratio * configTool.fontRatio
                                                color: ST.color_white_pure
                                                CusText {
                                                    text: qsTr("条码")
                                                    anchors.centerIn: parent
                                                }
                                            }
                                            CusRect {
                                                Layout.fillHeight: true
                                                Layout.fillWidth: true
                                                color: ST.color_white_pure
                                                CusText {
                                                    text: qsTr("名称")
                                                    anchors.centerIn: parent
                                                }
                                            }
                                        }
                                    }

                                    CusRect {
                                        Layout.fillWidth: true
                                        Layout.fillHeight: true
                                        color: ST.color_grey_border

                                        ListView {
                                            id: lv_search_goods
                                            model: model_goods_data
                                            clip: true
                                            anchors.fill: parent
                                            anchors.margins: 1 * dpi_ratio
                                            anchors.topMargin: 0
                                            boundsBehavior: Flickable.StopAtBounds

                                            delegate: CusRect {
                                                width: lv_search_goods.width
                                                height: 60 * dpi_ratio
                                                color: ST.color_white_pure

                                                RowLayout {
                                                    anchors.fill: parent
                                                    anchors.leftMargin: 20 * dpi_ratio
                                                    anchors.rightMargin: 20 * dpi_ratio

                                                    CusRect {
                                                        Layout.fillHeight: true
                                                        Layout.preferredWidth: 220 * dpi_ratio * configTool.fontRatio
                                                        color: ST.color_transparent

                                                        CusText {
                                                            text: GOODS_BARCODE_ROLE
                                                            anchors.verticalCenter: parent.verticalCenter
                                                        }
                                                    }

                                                    CusRect {
                                                        Layout.fillWidth: true
                                                        Layout.fillHeight: true
                                                        color: ST.color_transparent

                                                        CusText {
                                                            text: GOODS_NAME_ROLE
                                                            anchors.verticalCenter: parent.verticalCenter
                                                            width: parent.width
                                                            elide: Text.ElideMiddle
                                                        }
                                                    }
                                                }

                                                CusRect {
                                                    width: parent.width
                                                    height: 1 * dpi_ratio
                                                    color: ST.color_grey_border
                                                    anchors.bottom: parent.bottom
                                                }

                                                MouseArea {
                                                    anchors.fill: parent
                                                    onClicked: {
                                                        window_root.keyboard_c.closeAll()
                                                        godown_page_old_root.setCurGoodsByBarcode(GOODS_BARCODE_ROLE)
                                                        tf_godown_search.clear()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        CusRect {
                            Layout.fillHeight: true
                            Layout.preferredWidth: height
                            color: ST.color_transparent
                            visible: false

                            Image {
                                source: "/Images/switch.png"
                                width: 50 * dpi_ratio
                                height: width
                                anchors.centerIn: parent
                            }

                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    window_root.compo_index = EnumTool.PAGE_GODOWN
                                }
                            }
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredHeight: 10 * dpi_ratio
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    Image {
                        anchors.fill: parent
                        source: background_img_path
                    }

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 20 * dpi_ratio
                        spacing: 10 * dpi_ratio

                        HighLevelGoodsStandard {
                            id: medium_goods_standard
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            title_name: qsTr("中间包装")
                        }

                        HighLevelGoodsStandard {
                            id: high_goods_standard
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            title_name: qsTr("最大包装")
                        }
                    }
                }
            }
        }
    }

    component RectLable: CusRect {
        Layout.fillHeight: true
        Layout.preferredWidth: 140 * dpi_ratio
        color: ST.color_transparent

        property alias text: t_name.text
        property bool is_required: false

        CusText {
            text: "*"
            color: ST.color_red
            anchors.right: t_name.left
            anchors.rightMargin: 5 * dpi_ratio
            anchors.verticalCenter: parent.verticalCenter
            font.pixelSize: 28 * dpi_ratio * configTool.fontRatio
            visible: is_required
        }
        CusText {
            id: t_name
            text: qsTr("商品条码")
            anchors.verticalCenter: parent.verticalCenter
            anchors.right: parent.right
            font.pixelSize: 28 * dpi_ratio * configTool.fontRatio2
        }
    }

    component RectLable2: CusRect {
        Layout.fillHeight: true
        Layout.preferredWidth: 140 * dpi_ratio
        color: ST.color_transparent

        property alias text: t2_name.text
        property bool is_required: false

        CusText {
            text: "*"
            color: ST.color_red
            anchors.right: t2_name.left
            anchors.rightMargin: 5 * dpi_ratio
            anchors.verticalCenter: parent.verticalCenter
            font.pixelSize: 24 * dpi_ratio * configTool.fontRatio2
            visible: is_required
        }
        CusText {
            id: t2_name
            text: qsTr("商品条码")
            anchors.verticalCenter: parent.verticalCenter
            anchors.right: parent.right
            font.pixelSize: 24 * dpi_ratio
        }
    }

    component HighLevelGoodsStandard: CusRect
    {
        id: high_level_goods_standard_root

        Layout.fillWidth: true
        Layout.fillHeight: true
        color: ST.color_transparent

        property bool is_create: true //创建?

        property string title_name: "value"

        property string goods_barcode: "" //"商品条码"
        property string goods_name: "" //商品名
        property string goods_contain: "" //商品转换
        property string goods_abbr: "" //缩写
        property int goods_cheng_type: 0 //0、按件；1、按重量
        property string goods_standard: "" //商品规格
        property string goods_price: "" //零售单价
        // property string goods_web_price: "" //网购单价
        property string goods_member_price: "" //会员单价
        property string goods_in_price: "" //采购单价
        property string good_stock_price: ""//最近入库价
        property string goods_unit: "" //商品单位

        property string stock_info: "库存信息" //库存信息

        function resetGoodsInfo() {
            is_create = true
            goods_barcode = ""
            goods_name = ""
            goods_contain = ""
            goods_abbr = ""
            goods_cheng_type = 0
            goods_standard = ""
            goods_price = ""
            // goods_web_price = ""
            goods_member_price = ""
            goods_in_price = ""
            goods_unit = ""
            stock_info = ""
        }

        function refreshStockInfo() {
            var result_str = ""
            if (basic_goods_standard.goods_stock == 0) {
                result_str = qsTr("库存为空")
            }
            if (Number(high_level_goods_standard_root.goods_contain) > Number(basic_goods_standard.goods_stock)) {
                result_str = "0" + high_level_goods_standard_root.goods_unit + " " + basic_goods_standard.goods_stock + basic_goods_standard.goods_unit
            } else {
                var stock_ratio = Number(basic_goods_standard.goods_stock) / Number(high_level_goods_standard_root.goods_contain)
                var high_level_stock = Math.floor(stock_ratio)
                var basic_level_stock = (Number(basic_goods_standard.goods_stock) - Number(high_level_goods_standard_root.goods_contain) * high_level_stock).toFixed(2)
                result_str = String(high_level_stock.toFixed(2)) + high_level_goods_standard_root.goods_unit + " " + basic_level_stock + basic_goods_standard.goods_unit
            }
            stock_info = result_str
        }

        // 根据条码设置当前商品
        function setGoodsByBarcode(goods_barcode, is_editable = false) {
            var goods_json = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))

            high_level_goods_standard_root.is_create = is_editable
            high_level_goods_standard_root.goods_barcode = goods_json.goods_barcode
            high_level_goods_standard_root.goods_name = goods_json.goods_name
            high_level_goods_standard_root.goods_contain = goods_json.goods_contain
            high_level_goods_standard_root.goods_abbr = goods_json.goods_alias
            high_level_goods_standard_root.goods_cheng_type = goods_json.goodsChengType
            high_level_goods_standard_root.goods_standard = goods_json.goods_standard
            high_level_goods_standard_root.goods_price = Number(goods_json.goods_sale_price).toFixed(2)
            // high_level_goods_standard_root.goods_web_price = Number(goods_json.goods_web_sale_price).toFixed(2)
            high_level_goods_standard_root.goods_member_price = Number(goods_json.goods_cus_price).toFixed(2)
            high_level_goods_standard_root.goods_in_price = Number(goods_json.goods_in_price).toFixed(2)
            high_level_goods_standard_root.good_stock_price = Number(goods_json.goodStockPrice).toFixed(2)
            high_level_goods_standard_root.goods_unit = goods_json.goods_unit

            high_level_goods_standard_root.focusUnit(goods_json.goods_unit)
            high_level_goods_standard_root.refreshStockInfo()
        }

        // 选中单位
        function focusUnit(unit_name) {

            if (unit_name == "") {
                high_level_combo_goods_unit.currentIndex = -1
                return
            }

            for (var i = 0; i < lm_goods_unit.count; ++i) {
                var cur_item = lm_goods_unit.get(i)
                if (cur_item.goods_unit == unit_name) {
                    high_level_combo_goods_unit.currentIndex = i
                    return
                }
            }

            lm_goods_unit.append({
                                     "goods_unit_id": -1,
                                     "goods_unit": unit_name
                                 })

            high_level_combo_goods_unit.currentIndex = lm_goods_unit.count - 1
        }

        function isHasData() {
            // || high_level_goods_standard_root.goods_web_price != ""
            if (high_level_goods_standard_root.goods_barcode != "" || high_level_goods_standard_root.goods_name != "" || high_level_goods_standard_root.goods_contain
                    != "" || high_level_goods_standard_root.goods_abbr != "" || high_level_goods_standard_root.goods_standard != "" || high_level_goods_standard_root.goods_price
                    != "" || high_level_goods_standard_root.goods_member_price != "" || high_level_goods_standard_root.goods_in_price != "" || high_level_goods_standard_root.goods_unit != "") {
                return true
            }
            return false
        }

        function checkData() {
            if (high_level_goods_standard_root.goods_barcode == "") {
                toast.openWarn(title_name + qsTr("条码信息") + qsTr("有误"))
                return false
            } else if (high_level_goods_standard_root.goods_name == "") {
                toast.openWarn(title_name + qsTr("商品名") + qsTr("有误"))
                return false
            } else if (high_level_goods_standard_root.goods_contain == "" || high_level_goods_standard_root.goods_contain == "1") {
                toast.openWarn(title_name + qsTr("单位换算") + qsTr("有误"))
                return false
            } else if (high_level_goods_standard_root.goods_price == "") {
                toast.openWarn(title_name + qsTr("价格") + qsTr("有误"))
                return false
            } else if (high_level_goods_standard_root.goods_in_price == "") {
                toast.openWarn(title_name + qsTr("进价") + qsTr("有误"))
                return false
            }
            return true
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 20 * dpi_ratio

            CusRect {
                Layout.fillWidth: true
                Layout.preferredHeight: 50 * dpi_ratio
                color: ST.color_transparent
                CusText {
                    text: high_level_goods_standard_root.title_name
                    font.pixelSize: 32 * dpi_ratio
                    font.bold: true
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.preferredHeight: 46 * dpi_ratio
                color: ST.color_transparent

                RowLayout {
                    anchors.fill: parent
                    spacing: 15 * dpi_ratio
                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio

                            RectLable2 {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                text: qsTr("商品条码")
                                is_required: true
                            }

                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_transparent

                                RowLayout {
                                    anchors.fill: parent

                                    CusTextField {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        text: high_level_goods_standard_root.goods_barcode
                                        enabled: high_level_goods_standard_root.is_create
                                        onTextEdited: {
                                            high_level_goods_standard_root.goods_barcode = text
                                        }
                                        keyboard_status: 1

                                        background_color_enabled: ST.color_white_pure
                                        font.pixelSize: 22 * ST.dpi_ratio

                                        normal_keyboard_x: 10 * dpi_ratio
                                        normal_keyboard_y: 565 * dpi_ratio

                                        digital_keyboard_x: 615 * dpi_ratio
                                        digital_keyboard_y: 350 * dpi_ratio

                                        onAccepted: {
                                            var goods_json = JSON.parse(goodsManager.getGoodsByBarcode4Qml(text))
                                            if (goods_json) {
                                                high_level_goods_standard_root.setGoodsByBarcode(goods_json.goods_barcode, true)
                                            } else {
                                                goodsControl.reqGetOnlineGoodsInfo(function (is_succ, data) {
                                                    var high_level_goods_standard_rootgoods_json = JSON.parse(data)
                                                    goods_json = goods_json.data
                                                    high_level_goods_standard_root.goods_barcode = goods_json.goods_barcode
                                                    high_level_goods_standard_root.goods_name = goods_json.goods_name
                                                    high_level_goods_standard_root.goods_abbr = goods_json.goods_alias
                                                    high_level_goods_standard_root.goods_standard = goods_json.goods_standard
                                                    high_level_goods_standard_root.goods_price = Number(goods_json.goods_sale_price).toFixed(2)
                                                    high_level_goods_standard_root.goods_member_price = Number(goods_json.goods_cus_price).toFixed(2)
                                                    high_level_goods_standard_root.goods_in_price = Number(goods_json.goods_in_price).toFixed(2)
                                                    high_level_goods_standard_root.good_stock_price = Number(goods_json.goodStockPrice).toFixed(2)
                                                    high_level_goods_standard_root.goods_contain = Number(goods_json.goods_contain).toFixed(2)
                                                    high_level_goods_standard_root.goods_unit = goods_json.goods_unit
                                                    high_level_goods_standard_root.focusUnit(goods_json.goods_unit)
                                                    // high_level_goods_standard_root.goods_web_price = Number(goods_json.goods_sale_price).toFixed(2)
                                                }, text)
                                            }
                                        }
                                    }

                                    CusButton {
                                        Layout.fillHeight: true
                                        Layout.preferredWidth: height
                                        Layout.alignment: Qt.AlignVCenter
                                        visible: high_level_goods_standard_root.is_create

                                        Image {
                                            width: 35 * ST.dpi
                                            height: width
                                            anchors.centerIn: parent
                                            source: "/Images/plus.png"
                                        }

                                        onClicked: {
                                            //var cur_goods_barcode = goodsManager.generateCustomBarcode(basic_goods_standard.goods_cheng_type == 1)
                                            var cur_goods_barcode = goodsManager.contrastGenerateCustomBarcode(basic_goods_standard.goods_cheng_type == 1,medium_goods_standard.goods_barcode,basic_goods_standard.goods_barcode)
                                            while (((cur_goods_barcode == medium_goods_standard.goods_barcode) && (cur_goods_barcode == high_goods_standard.goods_barcode))
                                                   || cur_goods_barcode == basic_goods_standard.goods_barcode) {
                                                //cur_goods_barcode = goodsManager.generateCustomBarcode(basic_goods_standard.goods_cheng_type == 1)
                                                cur_goods_barcode = goodsManager.contrastGenerateCustomBarcode(basic_goods_standard.goods_cheng_type == 1,medium_goods_standard.goods_barcode,basic_goods_standard.goods_barcode)
                                            }

                                            high_level_goods_standard_root.goods_barcode = cur_goods_barcode
                                        }
                                    }
                                }
                            }
                        }
                    }

                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio

                            RectLable2 {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                text: qsTr("商品名称")
                                is_required: true
                            }

                            CusTextField {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                text: high_level_goods_standard_root.goods_name
                                onTextEdited: {
                                    high_level_goods_standard_root.goods_name = text
                                }
                                background_color_enabled: ST.color_white_pure

                                normal_keyboard_x: 375 * dpi_ratio
                                normal_keyboard_y: 380 * dpi_ratio

                                digital_keyboard_x: 988 * dpi_ratio
                                digital_keyboard_y: 316 * dpi_ratio
                            }
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.preferredHeight: 46 * dpi_ratio
                color: ST.color_transparent

                RowLayout {
                    anchors.fill: parent
                    spacing: 15 * dpi_ratio
                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio

                            RectLable2 {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                text: qsTr("销售单位")
                                is_required: true
                            }

                            CusComboBox {
                                id: high_level_combo_goods_unit
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                model: lm_goods_unit
                                textRole: "goods_unit"
                                onCurrentIndexChanged: {
                                    if (currentIndex == -1)
                                        high_level_goods_standard_root.goods_unit = ""
                                    else
                                        high_level_goods_standard_root.goods_unit = lm_goods_unit.get(currentIndex).goods_unit
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio

                            RectLable2 {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                text: qsTr("单位换算")
                                is_required: true
                            }

                            CusTextField {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                text: high_level_goods_standard_root.goods_contain
                                onTextEdited: {
                                    high_level_goods_standard_root.goods_contain = text
                                    if((text !== "") && (text !== "0")){
                                        high_level_goods_standard_root.refreshStockInfo()
                                    }
                                }

                                keyboard_status: 1
                                normal_keyboard_x: 10 * dpi_ratio
                                normal_keyboard_y: 565 * dpi_ratio

                                digital_keyboard_x: 615 * dpi_ratio
                                digital_keyboard_y: 350 * dpi_ratio
                            }
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.preferredHeight: 46 * dpi_ratio
                color: ST.color_transparent

                RowLayout {
                    anchors.fill: parent
                    spacing: 15 * dpi_ratio
                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio

                            RectLable2 {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                text: qsTr("零售单价")
                                is_required: true
                            }

                            CusTextField {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                text: high_level_goods_standard_root.goods_price
                                comnent: "零售单价"
                                onTextEdited: {
                                    high_level_goods_standard_root.goods_price = text
                                }
                                onFocusChanged: {
                                    logMgr.logEvtInfo4Qml("零售单价 focus : {}", focus)
                                }
                                keyboard_status: 1
                                normal_keyboard_x: 10 * dpi_ratio
                                normal_keyboard_y: 565 * dpi_ratio

                                digital_keyboard_x: 615 * dpi_ratio
                                digital_keyboard_y: 350 * dpi_ratio
                            }
                        }
                    }

                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio

                            RectLable2 {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                text: qsTr("会员单价")
                                is_required: false
                            }

                            CusTextField {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                text: high_level_goods_standard_root.goods_member_price
                                comnent: "会员单价"
                                onTextEdited: {
                                    high_level_goods_standard_root.goods_member_price = text
                                }
                                keyboard_status: 1
                                normal_keyboard_x: 10 * dpi_ratio
                                normal_keyboard_y: 565 * dpi_ratio

                                digital_keyboard_x: 615 * dpi_ratio
                                digital_keyboard_y: 350 * dpi_ratio
                            }
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.preferredHeight: 46 * dpi_ratio
                color: ST.color_transparent

                RowLayout {
                    anchors.fill: parent
                    spacing: 15 * dpi_ratio

                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio

                            RectLable2 {
                                Layout.fillHeight: true
                                Layout.preferredWidth: (high_level_goods_standard_root.goods_barcode != "" && !high_level_goods_standard_root.is_create)?(140 * dpi_ratio):(110 * dpi_ratio)
                                text: (high_level_goods_standard_root.goods_barcode != "" && !high_level_goods_standard_root.is_create)?qsTr("最近入库价"):qsTr("采购单价")
                                is_required: true
                            }

                            CusTextField {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                text:(high_level_goods_standard_root.goods_barcode != "" && !high_level_goods_standard_root.is_create)?high_level_goods_standard_root.good_stock_price:high_level_goods_standard_root.goods_in_price
                                comnent: qsTr("采购单价")
                                enabled: !(high_level_goods_standard_root.goods_barcode != "" && !high_level_goods_standard_root.is_create)

                                onTextEdited: {        
                                    high_level_goods_standard_root.goods_in_price = text
                                }
                                keyboard_status: 1

                                normal_keyboard_x: 10 * dpi_ratio
                                normal_keyboard_y: 565 * dpi_ratio

                                digital_keyboard_x: 615 * dpi_ratio
                                digital_keyboard_y: 350 * dpi_ratio
                            }
                        }
                    }

                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.preferredHeight: 46 * dpi_ratio
                color: ST.color_transparent
                visible: configTool.isShowStock

                RowLayout {
                    anchors.fill: parent
                    spacing: 15 * dpi_ratio
                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio

                            CusRect {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.verticalCenter: parent.verticalCenter
                                    text: high_level_goods_standard_root.stock_info
                                }
                            }
                        }
                    }
                    CusRect {
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        color: ST.color_transparent
                        RowLayout {
                            anchors.fill: parent
                            spacing: 10 * dpi_ratio
                            anchors.leftMargin: 20 * dpi_ratio
                            CusButton {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                color: ST.color_green
                                text: qsTr("入库")
                                onClicked: {
                                    logMgr.logEvtInfo4Qml("入库计件方式1：{}",high_level_goods_standard_root.goods_cheng_type)
                                    showStockChangeIn(high_level_goods_standard_root.goods_barcode, high_level_goods_standard_root.goods_contain,high_level_goods_standard_root.good_stock_price,high_level_goods_standard_root.goods_unit,high_level_goods_standard_root.goods_cheng_type)
                                }
                                visible: high_level_goods_standard_root.goods_barcode != "" && !high_level_goods_standard_root.is_create
                            }
                            CusButton {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                text: qsTr("出库")
                                onClicked: {
                                    logMgr.logDataInfo4Qml("shopControl.goodsInPriceType:{}",shopControl.goodsInPriceType)
                                    if(shopControl.goodsInPriceType == 2){
                                       //showStockChangeOutFirst(basic_goods_standard.goods_barcode, 1)
                                       showStockChangeOutFirst(high_level_goods_standard_root.goods_barcode, 1,high_level_goods_standard_root.goods_unit,high_level_goods_standard_root.good_stock_price,high_level_goods_standard_root.goods_cheng_type)
                                    }
                                    else if(shopControl.goodsInPriceType == 1 || shopControl.goodsInPriceType == 0){
                                       logMgr.logEvtInfo4Qml("")
                                       showStockChangeOutMean(high_level_goods_standard_root.goods_barcode, 1 ,high_level_goods_standard_root.goods_unit,high_level_goods_standard_root.good_stock_price,high_level_goods_standard_root.goods_cheng_type)
                                    }
                                }
                                visible: high_level_goods_standard_root.goods_barcode != "" && !high_level_goods_standard_root.is_create
                                color: ST.color_orange
                            }
                        }
                    }
                }
            }

            CusSpacer {
                Layout.fillHeight: true
            }
        }
    }

    Component {
        id: popup_sotck_change_in
        PopupInStock{
            title_names:qsTr("商品入库")
            onSigOpen: {
            }
            onSigClose: {

            }
            onConfirm: {
                goodsControl.reqStockChangeNew_v2(function (is_succ, data) {
                    var json_doc = JSON.parse(data);
                    if (is_succ) {
                        toast.openInfo(qsTr("入库成功"));
                    } else {                        
                        toast.openInfo(qsTr("入库失败!")+json_doc.msg);
                    }
                }, goods_barcode, str_num ,"1", stockPriceCopy,"",goods_prod,goodsExp,"",stockTotalPrice)
            }
        }
    }
    Component {
        id: popup_sotck_change_out_Mean
            PopupOutStockMean{
                title_names:qsTr("商品出库")
                onSigOpen: {
                }
                onSigClose: {
                }
                onConfirm: {
                    goodsControl.reqStockChangeNew_v2(function (is_succ, data) {
                        var json_doc = JSON.parse(data);
                        if (is_succ) {
                            toast.openInfo(qsTr("出库成功"));
                        } else {
                            toast.openInfo(qsTr("出库失败!")+json_doc.msg);
                        }
                    }, goods_barcode, str_num ,"2", stockPriceCopy,stockOutColorType,"","","",stockTotalPrice)
                }
            }
    }
    Component {
        id: popup_sotck_change_out_First
            PopupOutStockFirst{
                title_names:qsTr("商品出库")
                onSigOpen: {
                }
                onSigClose: {

                }
                onConfirm: {
                    //分批次出库
                    goodsControl.reqStockChangeNew_v2(function (is_succ, data) {
                        var json_doc = JSON.parse(data);
                        if (is_succ) {
                            toast.openInfo(qsTr("出库成功"));
                        } else {
                            toast.openInfo(qsTr("出库失败!")+json_doc.msg);
                        }
                    }, goods_barcode, str_num ,"2", stockPriceCopy,stockOutColorType,"","",goodBatchMessage,stockTotalPrice)
                }

            }
    }

    Component {
        id: compo_del_cur_goods
        PopupConfirm {
            title_name: qsTr("删除商品")
            message_info: qsTr("确定要删除当前商品吗?")
            property string goods_barcode: ""
            onSigClose: {
                tf_godown_search.forceActiveFocus()
            }
            onConfirm: {
                deleteGoods(goods_barcode)
            }
        }
    }
    Connections {
        target: mqttControl
        function onSigMqttRefreshGoodsByBarcode4Qml(goods_barcode) {
            var cur_goods = JSON.parse(goodsManager.getGoodsByBarcode4Qml(goods_barcode))
            if (cur_goods) {
                if (basic_goods_standard.goods_barcode == goods_barcode || medium_goods_standard.goods_barcode == goods_barcode
                        || high_goods_standard.goods_barcode == goods_barcode)
                    setCurGoodsByBarcode(cur_goods.goods_barcode)
            }
        }
    }
//    GoodsEdit {
//        id: paypageComponent
//        onSignOpenGoodsManger: {
//        }
//    }
}
