﻿pragma Singleton

import QtQuick 2.15

QtObject {
    readonly property string color_green: "#00BD75"
    //    readonly property string color_green: "#33cc66"
    readonly property string color_green2: "#99cc99"
    readonly property string color_green_light: "#edfaf1"
    readonly property string color_orange: "#FF9500"
    readonly property string color_orange_light: "#fff9f1"
    readonly property string color_orange_hot: "#FF5D40"
    readonly property string color_blue: "#2E84FF"
    readonly property string color_blue_deeper: "#0a82eb"
    readonly property string color_white: "#f6f6f6"
    readonly property string color_white_deeper: "#F5F5F5"
    readonly property string color_white_deeper2: "#e1e1e1"
    readonly property string color_white_pure: "#ffffff"
    readonly property string color_yellow: "#FFCC00"
    readonly property string color_grey: "#f0f0f0"
    readonly property string color_grey_font: "#626564"
    readonly property string color_grey_font2: "#999999"
    readonly property string color_grey_border: "#E5E8EC"
    readonly property string color_grey_border2: "#d9d9d9"
    readonly property string color_grey_btn: "#ACACAC"

    readonly property string color_grey_background: "#989c99"

    readonly property string color_transparent: "transparent"
    readonly property string color_black: "#000000"
    readonly property string color_red: "#FF3835"
    readonly property string color_red_light: "#f6d2d2"
    readonly property string color_red_keyboard: "#FF3735"
    //    readonly property string color_red: "#F6403F"
    readonly property string color_red2: "#FF2D55"
    readonly property string color_gold: "#FEA82F"
    readonly property string color_font: "#333333"

    readonly property string color_black_p1: "#1C1F26"
    readonly property string color_black_p2: "#344049"

    readonly property string color_black_2: "#172628"

    readonly property string fontFamilyYaHei: "微软雅黑"
    readonly property string fontFamilySiYan: "Source Han Sans SC"
    readonly property string fontFamilyAharoni: "Aharoni"

    readonly property int border_width: 1
    readonly property int radius: 6 * dpi_ratio
    readonly property bool is_debug: true
    readonly property int margin: 10

    readonly property real dpi_ratio: utils4Qml.getWidthRatio()
    readonly property real dpi: utils4Qml.getWidthRatio()

    readonly property font font_calc: Qt.font({
                                                  "family": fontFamilyYaHei,
                                                  "italic": false,
                                                  "pointSize": 40
                                              })
    readonly property font font_calc2: Qt.font({
                                                   "family": fontFamilyYaHei,
                                                   "italic": false,
                                                   "pointSize": 30
                                               })

    function getDebugColor() {
        return is_debug ? Qt.rgba(Math.random(), Math.random(), Math.random(), .2) : ST.color_transparent
    }

    function getOpacityColor(color_str, a) {
        var str_color_str = String(color_str)
        str_color_str = str_color_str.substring(1, str_color_str.length)
        a = Math.round(a * 255)
        var result = '#' + a.toString(16).toUpperCase().padStart(2, '0') + str_color_str
        return result
    }
}
