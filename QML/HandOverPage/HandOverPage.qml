﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import EnumTool 1.0

CusRect {
    id: hand_over_page
    width: 800 *dpi_ratio
    height: 500 *dpi_ratio
    color: ST.color_grey
    property int width_btn: 340
    property int height_btn: 70

    property string original_data_json: ""

    property var _id: ""
    property var _recharge_details: ""
    property var _name: ""
    property var _order_receivable: ""
    property var _order_received: ""
    property var _order_num: ""

    property var _received_cash: "0"
    property var _received_weixin: "0"
    property var _received_alipay: "0"
    property var _received_bank: "0"
    property var _received_vip: "0"

    property var _received: "0"
    property var _received_mini_program: "0"
    property var _received_beans: "0"
    property var _received_jinquan: "0"

    property var _succession_time: ""
    property var _handover_time: ""
    property int _unuploadRecCount: 0
    property bool isClickEnabled: true
    Timer
    {
        id: update_unupload_amount;
        interval: 2000;
        repeat: true;
        running: false;
        onTriggered:
        {
            if(visible){
                logMgr.logEvtInfo4Qml("开启定时刷新_unuploadRecCount：{}",_unuploadRecCount)
                _unuploadRecCount = payMethodControl.getUnuploadRecCount()
                getUnuploadData();
                logMgr.logEvtInfo4Qml("定时刷新_unuploadRecCount111：{}",_unuploadRecCount)
            }
        }
    }
    function getUnuploadData()
    {
        var unuploadString = payMethodControl.getDataFromUnuploadFiles();
        if(unuploadString.length == 0)
        {
            unupload_orderListModel.clear();
            return;
        }
        var unuploadCountArray = unuploadString.split("&");
        unupload_orderListModel.clear();
        for(var i = 0; i < unuploadCountArray.length; i++)
        {
            var unuploadViewArray = unuploadCountArray[i].split("^");
            unupload_orderListModel.insert(i,{unupload_order_time_detail:unuploadViewArray[0],
                                               unupload_order_id_detail:unuploadViewArray[1],
                                               unupload_order_sum_detail:unuploadViewArray[2]
                                           });
        }

        console.debug("ListView insert success");

    }
    function resetInfo() {
        original_data_json = ""

        _id = ""
        _recharge_details = ""
        _name = ""
        _order_receivable = ""
        _order_received = ""
        _order_num = ""

        _received_cash = "0"
        _received_weixin = "0"
        _received_alipay = "0"
        _received_bank = "0"
        _received_vip = "0"

        _received = "0"
        _received_mini_program = "0"
        _received_beans = "0"
        _received_jinquan = "0"

        _succession_time = ""
        _handover_time = ""
    }

    function reqHandOverInfo() {
        shopControl.reqHandOverInfo4Qml(function (data) {
            resetInfo()
            original_data_json = data
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            var json_doc_data1 = json_doc.data1
            var json_doc_address = json_doc.address

            _id = json_doc_data.staff_id
            _recharge_details = json_doc_data.cusRecharge
            _name = json_doc_data.staff_name
            _order_receivable = json_doc_data.total
            _order_received = json_doc_data.receivable
            _order_num = json_doc_data.sum

            _received_weixin
            _received_alipay
            _received_bank
            _received_vip

            _received
            _received_mini_program
            _received_beans
            _received_jinquan

            for (var i = 0; i < json_doc_address.length; ++i) {
                var cur_data = json_doc_address[i]

                //                if (cur_data.pay_ment == "微信") {

                //                }
                //                if (cur_data.pay_ment == "支付宝") {

                //                }
            }

            for (var i = 0; i < json_doc_data1.length; ++i) {
                var cur_data = json_doc_data1[i]

                if (cur_data.pay_ment == "现金") {
                    _received_cash = cur_data.payment_total
                } else if (cur_data.pay_ment == "微信") {
                    _received_weixin = cur_data.payment_total
                } else if (cur_data.pay_ment == "支付宝") {
                    _received_alipay = cur_data.payment_total
                } else if (cur_data.pay_ment == "金圈平台") {
                    _received_jinquan = cur_data.payment_total
                } else if (cur_data.pay_ment == "储值卡") {
                    _received_vip = cur_data.payment_total
                } else if (cur_data.pay_ment == "百货豆抵扣数量") {
                    _received_beans = cur_data.payment_total
                } else if (cur_data.pay_ment == "银行卡") {
                    _received_bank = cur_data.payment_total
                }
            }

            _succession_time = json_doc_data.login_datetime
            _handover_time = json_doc_data.endTime
        })
    }

    function reqHandOver() {
        shopControl.reqHandOver4Qml(function (is_succ, data) {
            isClickEnabled = true
            if (is_succ) {
                window_root.compo_index = EnumTool.PAGE_LOGIN
                toast.openInfo(qsTr("交班成功"))
            } else {
                toast.openWarn(qsTr("交班失败!"))
            }
        })
    }

    function printHandoverInfo() {
        printerControl.printHandoverInfo(hand_over_page.original_data_json)
        if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
            printerControl.printNingyuHandoverInfo(hand_over_page.original_data_json)
        }else{
            printerControl.printHandoverInfo(hand_over_page.original_data_json)
        }
    }
    function set_shift_dedail_visible(flag)
    {
        _unuploadRecCount = payMethodControl.getUnuploadRecCount()
        if(flag == true)
        {
            container.visible = true;
            buttons.visible = true;
            container_upload.visible = false;
            container_buttons.visible = false;
        }
        else
        {
            container.visible = false;
            buttons.visible = false;
            container_upload.visible = true;
            container_buttons.visible = true;
        }
    }
    Image {
        anchors.fill: parent
        source: "/Images/mainPageBackground.jpg"
    }

    Component.onCompleted: {
        reqHandOverInfo()
        logMgr.logEvtInfo4Qml("初始化触发交班更新")
        update_unupload_amount.start()
        _unuploadRecCount = payMethodControl.getUnuploadRecCount()
    }

    onVisibleChanged: {
        if (visible){
            logMgr.logEvtInfo4Qml("触发交班更新")
            //默认进入交班初始化页面
            set_shift_dedail_visible(true)
            reqHandOverInfo()
            update_unupload_amount.start()
            _unuploadRecCount = payMethodControl.getUnuploadRecCount()
        }
        else{
            update_unupload_amount.stop()
            logMgr.logEvtInfo4Qml("关闭定时刷新_unuploadRecCount")

        }
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: ST.margin
        anchors.margins: ST.margin

        CusRect {
            id: top_side
            Layout.fillWidth: true
            Layout.preferredHeight: 100 * dpi_ratio
            color: ST.color_white_pure
            radius: ST.radius

            CusText {
                anchors.centerIn: parent
                text: qsTr("交接班")
                font.pixelSize: 30 * dpi_ratio * configTool.fontRatio
            }
            Rectangle
            {
                id: shift_unupload_rec;
                width: 500* dpi_ratio
                height: 100 * dpi_ratio
                color:"#ffffff";
                anchors.right: parent.right;
                anchors.top:parent.top;
                anchors.verticalCenter: parent.verticalCenter;

                Text
                {
                    id: shift_unupload_text;
                    anchors.centerIn: parent;
                    text: qsTr("未上传服务器的订单数：")+_unuploadRecCount+qsTr("笔");
                    font.family: "微软雅黑";
                    font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                    color: "#ff0000";
                }
                MouseArea
                {
                    anchors.fill: parent
                    onClicked:
                    {
                        reqHandOverInfo()
                        if(container.visible)
                        {
                            set_shift_dedail_visible(false);
                        }
                        else
                        {
                            set_shift_dedail_visible(true);
                        }
                    }
                }
            }
        }

        CusRect {
            id: container
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_white_pure
            radius: ST.radius

            ColumnLayout {
                anchors.fill: parent
                anchors.leftMargin: 110 * dpi_ratio
                anchors.rightMargin: 110 * dpi_ratio

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        CusText {
                            Layout.fillWidth: true
                            Layout.preferredWidth: 10
                            Layout.fillHeight: true
                            text: qsTr("登录ID: ") + _id
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            verticalAlignment: Text.AlignVCenter
                        }
                        CusText {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 10
                            text: qsTr("充值详情: ") + _recharge_details
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        CusText {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 10
                            text: qsTr("员工姓名: ") + _name
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            verticalAlignment: Text.AlignVCenter
                        }
                        CusText {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 10
                            text: qsTr("订单应收: ") + _order_receivable
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        CusText {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 10
                            text: qsTr("订单数量: ") + _order_num
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            verticalAlignment: Text.AlignVCenter
                        }
                        CusText {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 10
                            text: qsTr("订单实收: ") + _order_received
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                }

                CusRect {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_transparent
                    GridLayout {
                        anchors.fill: parent
                        id: gl_hand_over_img_detail
                        columns: 5
                        columnSpacing: 50 * dpi_ratio
                        rowSpacing: 20 * dpi_ratio

                        property double col_width: width / columns

                        function prefWidth(item) {
                            return col_width * item.Layout.columnSpan
                        }
                        function prefHeight(item) {
                            return 120 * dpi_ratio
                        }

                        CusRect {
                            Layout.preferredWidth: gl_hand_over_img_detail.prefWidth(this)
                            Layout.preferredHeight: gl_hand_over_img_detail.prefHeight(this)
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.alignment: Qt.AlignHCenter | Qt.AlignVCenter

                            color: ST.color_white_pure
                            Image {
                                anchors.margins: 15 * dpi_ratio
                                source: "/Images/cash_type_image.png"
                                anchors.fill: parent
                                fillMode: Image.PreserveAspectFit
                            }
                            CusRect {
                                anchors.fill: parent
                                opacity: .3
                                color: ST.color_black
                            }
                            CusText {
                                text: _received_cash
                                anchors.centerIn: parent
                                font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                                color: ST.color_white_pure
                                font.bold: true
                            }
                        }

                        CusRect {
                            Layout.preferredWidth: gl_hand_over_img_detail.prefWidth(this)
                            Layout.preferredHeight: gl_hand_over_img_detail.prefHeight(this)
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.alignment: Qt.AlignHCenter | Qt.AlignVCenter
                            color: ST.color_transparent
                            Image {
                                anchors.margins: 15 * dpi_ratio
                                source: "/Images/wechat_image.png"
                                anchors.fill: parent
                                fillMode: Image.PreserveAspectFit
                            }
                            CusRect {
                                anchors.fill: parent
                                opacity: .3
                                color: ST.color_black
                            }
                            CusText {
                                text: _received_weixin
                                anchors.centerIn: parent
                                font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                                color: ST.color_white_pure
                                font.bold: true
                            }
                        }
                        CusRect {
                            Layout.preferredWidth: gl_hand_over_img_detail.prefWidth(this)
                            Layout.preferredHeight: gl_hand_over_img_detail.prefHeight(this)
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.alignment: Qt.AlignHCenter | Qt.AlignVCenter
                            color: ST.color_transparent
                            Image {
                                anchors.margins: 15 * dpi_ratio
                                source: "/Images/alipay_image.png"
                                anchors.fill: parent
                                fillMode: Image.PreserveAspectFit
                            }
                            CusRect {
                                anchors.fill: parent
                                opacity: .3
                                color: ST.color_black
                            }
                            CusText {
                                text: _received_alipay
                                anchors.centerIn: parent
                                font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                                color: ST.color_white_pure
                                font.bold: true
                            }
                        }
                        CusRect {
                            Layout.preferredWidth: gl_hand_over_img_detail.prefWidth(this)
                            Layout.preferredHeight: gl_hand_over_img_detail.prefHeight(this)
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.alignment: Qt.AlignHCenter | Qt.AlignVCenter
                            color: ST.color_transparent
                            Image {
                                anchors.margins: 15 * dpi_ratio
                                source: "/Images/bank_card_image.png"
                                anchors.fill: parent
                                fillMode: Image.PreserveAspectFit
                            }
                            CusRect {
                                anchors.fill: parent
                                opacity: .3
                                color: ST.color_black
                            }
                            CusText {
                                text: _received_bank
                                anchors.centerIn: parent
                                font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                                color: ST.color_white_pure
                                font.bold: true
                            }
                        }
                        CusRect {
                            Layout.preferredWidth: gl_hand_over_img_detail.prefWidth(this)
                            Layout.preferredHeight: gl_hand_over_img_detail.prefHeight(this)
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.alignment: Qt.AlignHCenter | Qt.AlignVCenter
                            color: ST.color_transparent
                            Image {
                                anchors.margins: 15 * dpi_ratio
                                source: "/Images/member_card_image.png"
                                anchors.fill: parent
                                fillMode: Image.PreserveAspectFit
                            }
                            CusRect {
                                anchors.fill: parent
                                opacity: .3
                                color: ST.color_black
                            }
                            CusText {
                                text: _received_vip
                                anchors.centerIn: parent
                                font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                                color: ST.color_white_pure
                                font.bold: true
                            }
                        }
                        CusRect {
                            Layout.preferredWidth: gl_hand_over_img_detail.prefWidth(this)
                            Layout.preferredHeight: gl_hand_over_img_detail.prefHeight(this)
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.alignment: Qt.AlignHCenter | Qt.AlignVCenter
                            color: ST.color_transparent
                            visible: false
                            Image {
                                anchors.margins: 15 * dpi_ratio
                                source: "/Images/kuaijiezhifu.png"
                                anchors.fill: parent
                                fillMode: Image.PreserveAspectFit
                            }
                            CusRect {
                                anchors.fill: parent
                                opacity: .3
                                color: ST.color_black
                            }
                            CusText {
                                text: _received
                                anchors.centerIn: parent
                                font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                                color: ST.color_white_pure
                                font.bold: true
                            }
                        }
                        CusRect {
                            Layout.preferredWidth: gl_hand_over_img_detail.prefWidth(this)
                            Layout.preferredHeight: gl_hand_over_img_detail.prefHeight(this)
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.alignment: Qt.AlignHCenter | Qt.AlignVCenter
                            color: ST.color_transparent
                            visible: false
                            Image {
                                anchors.margins: 15 * dpi_ratio
                                source: "/Images/xcxlogo.png"
                                anchors.fill: parent
                                fillMode: Image.PreserveAspectFit
                            }
                            CusRect {
                                anchors.fill: parent
                                opacity: .3
                                color: ST.color_black
                            }
                            CusText {
                                text: _received_mini_program
                                anchors.centerIn: parent
                                font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                                color: ST.color_white_pure
                                font.bold: true
                            }
                        }
                        CusRect {
                            Layout.preferredWidth: gl_hand_over_img_detail.prefWidth(this)
                            Layout.preferredHeight: gl_hand_over_img_detail.prefHeight(this)
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.alignment: Qt.AlignHCenter | Qt.AlignVCenter
                            color: ST.color_transparent
                            Image {
                                anchors.margins: 15 * dpi_ratio
                                source: "/Images/buyHooBean1.png"
                                anchors.fill: parent
                                fillMode: Image.PreserveAspectFit
                            }
                            CusRect {
                                anchors.fill: parent
                                opacity: .3
                                color: ST.color_black
                            }
                            CusText {
                                text: _received_beans
                                anchors.centerIn: parent
                                font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                                color: ST.color_white_pure
                                font.bold: true
                            }
                        }
                        CusRect {
                            Layout.preferredWidth: gl_hand_over_img_detail.prefWidth(this)
                            Layout.preferredHeight: gl_hand_over_img_detail.prefHeight(this)
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.alignment: Qt.AlignHCenter | Qt.AlignVCenter
                            color: ST.color_transparent
                            Image {
                                anchors.margins: 15 * dpi_ratio
                                source: "/Images/yitong.png"
                                anchors.fill: parent
                                fillMode: Image.PreserveAspectFit
                            }
                            CusRect {
                                anchors.fill: parent
                                opacity: .3
                                color: ST.color_black
                            }
                            CusText {
                                text: _received_jinquan
                                anchors.centerIn: parent
                                font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                                color: ST.color_white_pure
                                font.bold: true
                            }
                        }
                    }
                }
                CusRect {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100 * dpi_ratio
                    color: ST.color_transparent

                    RowLayout {
                        anchors.fill: parent
                        CusText {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 10
                            text: qsTr("接班时间: ") + _succession_time
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            verticalAlignment: Text.AlignVCenter
                        }
                        CusText {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredWidth: 10
                            text: qsTr("交班时间: ") + _handover_time
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                }
            }
        }
        CusRect {
            id: buttons
            Layout.fillWidth: true
            Layout.preferredHeight: 100 * dpi_ratio
            Layout.bottomMargin: 40 * dpi_ratio
            color: ST.color_transparent

            RowLayout {
                anchors.fill: parent
                anchors.leftMargin: 160 * dpi_ratio
                anchors.rightMargin: 160 * dpi_ratio
                CusButton {
                    text: qsTr("退出")
                    Layout.fillHeight: true
                    Layout.preferredWidth: 210 * dpi_ratio
                    color: ST.color_orange_hot
                    font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                    onClicked: {
                        if (configTool.isPowerOffWhenExit) {
                            utils4Qml.shutdown()
                        }
                        window_root.close()
                    }
                }
                CusSpacer {
                    Layout.fillWidth: true
                }
                CusButton {
                    text: qsTr("确定")
                    Layout.fillHeight: true
                    Layout.preferredWidth: 210 * dpi_ratio
                    color: ST.color_blue
                    font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                    onClicked: {
                        if(_unuploadRecCount == 0){
                            if(payMethodControl.getNetStatus() === "101"){
                                if(_id !=""){
                                    if(isClickEnabled){
                                        isClickEnabled = false
                                        //payMethodControl.uploadNotUploadOrder_offlineOrder()
                                        if(shopControl.getShopType() ==="6" ||shopControl.getShopType() ==="7"){
                                            printerControl.printNingyuHandoverInfo(original_data_json)
                                        }else{
                                            printerControl.printHandoverInfo(original_data_json)
                                        }
                                        shopCartList.clearMemberUnique()
                                        reqHandOver()
                                    }
                                }else{
                                    toast.openWarn(qsTr("获取交班信息失败!"))
                                }
                            }else{
                                toast.openWarn(qstr("离线状态无法进行交班操作!"))
                            }
                        }
                        else{
                            toast.openWarn(qstr("有未提交服务器的订单,请点击右上角查看！"))
                        }
                    }
                }
            }
        }
        CusRect {
            id: container_upload
            visible: false
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_white_pure
            radius: ST.radius

            Rectangle
            {
                id: unupload_title;
                width:1900 *dpi_ratio;
                height: 50 * dpi_ratio;
                anchors.left: container_upload.left;
                anchors.top: container_upload.top;
                anchors.topMargin: 0 * dpi_ratio;
                color: "#ffffff";

                Text
                {
                    id: listView_unupload_orderTime;
                    anchors.left: parent.left;
                    anchors.leftMargin: 400 * dpi_ratio;
                    anchors.top: parent.top;
                    anchors.topMargin: 15 * dpi_ratio;
                    text: qsTr("订单时间");
                    font.family: "微软雅黑";
                    font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                    color: "#a9a9a9";
                }
                Text
                {
                    id: listView_unupload_orderId;
                    anchors.left: listView_unupload_orderTime.right;
                    anchors.leftMargin: 400 * dpi_ratio;
                    anchors.top: parent.top;
                    anchors.topMargin: 15 * dpi_ratio;
                    text: qsTr("订单号");
                    font.family: "微软雅黑";
                    font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                    color: "#a9a9a9";
                }
                Text
                {
                    id: listView_unupload_order_sum;
                    anchors.left: listView_unupload_orderId.right;
                    anchors.leftMargin: 400 * dpi_ratio;
                    anchors.top: parent.top;
                    anchors.topMargin: 15 * dpi_ratio;
                    text: qsTr("订单金额");
                    font.family: "微软雅黑";
                    font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                    color: "#a9a9a9";
                }
            }
            Rectangle
            {
                id: unupload_detail;
                width:1900 *dpi_ratio;
                height: 640 * dpi_ratio;
                anchors.left: parent.left;
                color: "#ffffff";
                anchors.top: unupload_title.bottom;
                anchors.topMargin: 10 * dpi_ratio;
                z: 2;
                ListView
                {
                    id:unupload_rec_List;
                    width: 1900 * dpi_ratio;
                    height: 640 * dpi_ratio;
                    anchors.top: unupload_detail.top;
                    anchors.left: parent.left;
                    clip: true;
                    delegate: Rectangle
                    {
                    id: list_unupload_Rec;
                    width: 1900 * dpi_ratio;
                    height: 41 * dpi_ratio;

                    Rectangle
                    {
                        id: unupload_order_time;
                        width: 300 * dpi_ratio;
                        height: 40 * dpi_ratio;
                        anchors.left: parent.left;
                        anchors.top: parent.top;
                        anchors.leftMargin: 300 * dpi_ratio;
                        Text
                        {
                            id: unupload_order_timeText;
                            anchors.centerIn: parent;
                            horizontalAlignment:Text.AlignHCenter;
                            verticalAlignment:Text.AlignVCenter;
                            width: 300 * dpi_ratio;
                            font.family: "微软雅黑";
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            color: "#333333";
                            text: unupload_order_time_detail;
                            wrapMode:Text.Wrap;
                        }
                    }
                    Rectangle
                    {
                        id: unupload_order_id;
                        width: 280 * dpi_ratio;
                        height: 40 * dpi_ratio;
                        anchors.left: unupload_order_time.right;
                        anchors.leftMargin: 200 * dpi_ratio;
                        anchors.top: parent.top;
                        Text
                        {
                            id: unupload_order_idText;
                            anchors.centerIn: parent;
                            font.family: "微软雅黑";
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            text: unupload_order_id_detail;
                            color: "#333333";
                            elide:Text.ElideRight;
                        }

                    }



                    Rectangle
                    {
                        id: unupload_order_sum;
                        width: 300 * dpi_ratio;
                        height: 40 * dpi_ratio;
                        anchors.left: unupload_order_id.right;
                        anchors.leftMargin: 200 * dpi_ratio;
                        anchors.top: parent.top;
                        Text
                        {
                            id: unupload_order_sumText;
                            anchors.centerIn: parent;
                            font.family: "微软雅黑";
                            font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                            text: unupload_order_sum_detail;
                            color: "#333333";
                        }
                    }
                }

                model: ListModel
                {
                id: unupload_orderListModel;
            }
        }

            }
        }
        CusRect {
            id: container_buttons
            visible: false
            Layout.fillWidth: true
            Layout.preferredHeight: 100 * dpi_ratio
            Layout.bottomMargin: 40 * dpi_ratio
            color: ST.color_transparent

            onVisibleChanged: {
                    _unuploadRecCount = payMethodControl.getUnuploadRecCount()
            }
            CusButton {
                text: qsTr("一键提交")
                Layout.fillHeight: true
                width: 210 * dpi_ratio
                height: 100 * dpi_ratio
                color: ST.color_blue
                font.pixelSize: 25 * dpi_ratio * configTool.fontRatio
                anchors.right: parent.right
                anchors.rightMargin: 160 * dpi_ratio
                anchors.verticalCenter: parent.verticalCenter
                onClicked: {
                    if(payMethodControl.getNetStatus() === "101"){
                        reqHandOverInfo()
                        payMethodControl.uploadNotUploadOrder_offlineOrder()
                    }else{
                        toast.openInfo("离线状态无法上传订单!")
                    }
                }
            }
        }
        Connections {
            target: payMethodControl
            function onSigUnuploadRecCount() {
                _unuploadRecCount = payMethodControl.getUnuploadRecCount()
                logMgr.logEvtInfo4Qml("信号触发后更新离线数量为：{}",_unuploadRecCount)
            }
        }

    }
}
