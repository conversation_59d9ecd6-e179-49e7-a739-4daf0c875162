﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import EnumTool 1.0

CusRect {
    id: net_order_page
    width: 800 * dpi_ratio
    height: 500 * dpi_ratio
    color: ST.color_grey
    property int width_btn: 340 * dpi_ratio
    property int height_btn: 70 * dpi_ratio

    property alias contain_sale_record: contain_sale_record
    property alias contain_stock_record: contain_stock_record
    property alias contain_handover_record: contain_handover_record
    property int cur_query_page: EnumTool.QUERY_SALES_RECORD
    property string timeTemp: ""
    function openCalendarBegin(date, hour, minute) {
        window_root.loader_4_calendar_begin.sourceComponent = calendar_begin
        window_root.loader_4_calendar_begin.item.open()
        window_root.loader_4_calendar_begin.item.set(date, hour, minute)
        logMgr.logEvtInfo4Qml("打开开始日历")
    }

    function openCalendarEnd(date, hour, minute) {
        window_root.loader_4_calendar_end.sourceComponent = calendar_end
        window_root.loader_4_calendar_end.item.open()
        window_root.loader_4_calendar_end.item.set(date, hour, minute)
        logMgr.logEvtInfo4Qml("打开结束日历")
    }
    function isCorrectTime(btn_calendar_begin,btn_calendar_end) {
        var date_time_vec_begin = btn_calendar_begin.split(" ")
        var date_time_vec_end = btn_calendar_end.split(" ")
        if (date_time_vec_begin.length === 2) {
            var date_vec_begin = date_time_vec_begin[0].split("-")
            var year_begin = date_vec_begin[0]
            var month_begin = date_vec_begin[1]
            var day_begin = date_vec_begin[2]
            var time_vec_begin = date_time_vec_begin[1].split(":")
            var hour_begin = time_vec_begin[0]
            var minute_begin = time_vec_begin[1]
            if (date_time_vec_end.length === 2) {
                var date_vec_end = date_time_vec_end[0].split("-")
                var year_end = date_vec_end[0]
                var month_end = date_vec_end[1]
                var day_end = date_vec_end[2]
                var time_vec_end = date_time_vec_end[1].split(":")
                var hour_end = time_vec_end[0]
                var minute_end = time_vec_end[1]
                if(parseInt(year_begin) < parseInt(year_end)){
                    return true
                }
                else if(parseInt(year_begin) === parseInt(year_end)){
                    if(parseInt(month_begin) < parseInt(month_end)){
                        return ture
                    }
                    else if(parseInt(month_begin) === parseInt(month_end)){
                        if(parseInt(day_begin) < parseInt(day_end)){
                            return true
                        }
                        else if(parseInt(day_begin) === parseInt(day_end)){
                                if(parseInt(hour_begin) < parseInt(hour_end)){
                                    return true
                                }
                                else if(parseInt(hour_begin) === parseInt(hour_end)){
                                    if(parseInt(minute_begin) <= parseInt(minute_end)){
                                        return true
                                    }
                                }
                            }

                    }
                }
            }
        }
        return false
    }
    function refreshContainData() {
        switch (cur_query_page) {
        case EnumTool.QUERY_STOCK_RECORD:
            contain_stock_record.reqStockRecord()
            break
        case EnumTool.QUERY_SALES_RECORD:
            contain_sale_record.reqSaleRecord()
            break
        case EnumTool.QUERY_HANDOVER_RECORD:
            contain_handover_record.reqHanoverRecord()
            break
        case EnumTool.QUERY_GOODS_INFORMATION:
            //contain_handover_record.reqHanoverRecord()
            break
        case EnumTool.QUERY_GOODS_SALESTATISTICS:
            //contain_handover_record.reqHanoverRecord()
            break
        }
    }

    Image {
        anchors.fill: parent
        source: "/Images/mainPageBackground.jpg"
    }

    ColumnLayout {
        id: cl_net_order_page
        anchors.fill: parent
        anchors.margins: ST.margin
        spacing: ST.margin

        CusRect {
            Layout.fillWidth: true
            Layout.preferredHeight: 80 * dpi_ratio
            color: ST.color_white_pure
            Layout.alignment: Qt.AlignTop

            RowLayout {
                anchors.fill: parent
                spacing: ST.margin * 2

                ListView {
                    id: list_view

                    Layout.fillHeight: true
                    Layout.preferredWidth: list_model1.count * height * 3

                    interactive: false
                    clip: true
                    model: ListModel {
                        id: list_model1
                    }
                    orientation: ListView.Horizontal

                    delegate: RadioBtnRectC3 {
                        height: ListView.view.height
                        width: height * 2 * configTool.fontRatio

                        checked: cur_query_page == control
                        onClicked: {
                            cur_query_page = control
                        }
                    }

                    Component.onCompleted: {
                        list_model1.append({
                                               "name": qsTr("出入库记录"),
                                               "control": EnumTool.QUERY_STOCK_RECORD
                                           })
                        list_model1.append({
                                               "name": qsTr("销售记录"),
                                               "control": EnumTool.QUERY_SALES_RECORD
                                           })
                        list_model1.append({
                                               "name": qsTr("交接班记录"),
                                               "control": EnumTool.QUERY_HANDOVER_RECORD
                                           })
                        list_model1.append({
                                               "name": qsTr("商品信息列表"),
                                               "control": EnumTool.QUERY_GOODS_INFORMATION
                                           })
                        list_model1.append({
                                               "name": qsTr("商品销售统计"),
                                               "control": EnumTool.QUERY_GOODS_SALESTATISTICS
                                           })
                    }
                }

                CusSpacer {
                    Layout.fillWidth: true
                }

                CusRect {
                    Layout.alignment: Qt.AlignRight
                    Layout.fillHeight: true
                    Layout.preferredWidth: 240 * dpi_ratio * configTool.fontRatio
                    Layout.topMargin: 12 * dpi_ratio
                    Layout.bottomMargin: 12 * dpi_ratio
                    radius: ST.radius
                    color: ST.color_grey

                    CusText {
                        id: btn_calendar_begin
                        anchors.centerIn: parent
                        property bool is_need_init: true
                        onTextChanged: {
                            if(btn_calendar_end.text !="" && btn_calendar_begin.text !=""){
                                if(!isCorrectTime(btn_calendar_begin.text,btn_calendar_end.text)){
                                    btn_calendar_begin.text = timeTemp
                                    toast.openWarn("时间区间不正确!")
                                }else{
                                    if (is_need_init)
                                        return
                                    refreshContainData()
                                }
                            }
                        }
                        Component.onCompleted: {
                            is_need_init = true
                            text = utils4Qml.getCurDate() + " 00:00"
                            is_need_init = false
                        }
                    }
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {

                            //                            btn_calendar_begin.text

                            //                            openCalendarBegin()
                            var date_time_vec = btn_calendar_begin.text.split(" ")
                            timeTemp = btn_calendar_begin.text
                            if (date_time_vec.length == 2) {

                                var date_vec = date_time_vec[0].split("-")
                                var year = date_vec[0]
                                var month = date_vec[1]
                                var day = date_vec[2]

                                var time_vec = date_time_vec[1].split(":")
                                var hour = time_vec[0]
                                var minute = time_vec[1]

                                openCalendarBegin(date_time_vec[0], hour, minute)
                            }
                        }
                    }
                    Component {
                        id: calendar_begin
                        CusDateTimePicker {
                            is_floor: true
                            target_control: btn_calendar_begin
                        }
                    }
                }

                CusRect {
                    Layout.alignment: Qt.AlignRight
                    Layout.fillHeight: true
                    Layout.preferredWidth: 240 * dpi_ratio * configTool.fontRatio
                    Layout.topMargin: 12 * dpi_ratio
                    Layout.bottomMargin: 12 * dpi_ratio
                    radius: ST.radius
                    color: ST.color_grey

                    CusText {
                        id: btn_calendar_end
                        anchors.centerIn: parent
                        property bool is_need_init: true
                        onTextChanged: {
                            if(btn_calendar_end.text !="" && btn_calendar_begin.text !=""){
                                if(!isCorrectTime(btn_calendar_begin.text,btn_calendar_end.text)){
                                    btn_calendar_end.text = timeTemp
                                    toast.openWarn("时间区间不正确!")
                                }else{
                                    if (is_need_init)
                                        return
                                    refreshContainData()
                                }
                            }
                        }

                        Component.onCompleted: {
                            is_need_init = true
                            text = utils4Qml.getCurDate() + " 23:55"
                            is_need_init = false
                        }
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {

                            var date_time_vec = btn_calendar_end.text.split(" ")
                            timeTemp = btn_calendar_end.text
                            if (date_time_vec.length == 2) {

                                var date_vec = date_time_vec[0].split("-")
                                var year = date_vec[0]
                                var month = date_vec[1]
                                var day = date_vec[2]

                                var time_vec = date_time_vec[1].split(":")
                                var hour = time_vec[0]
                                var minute = time_vec[1]

                                openCalendarEnd(date_time_vec[0], hour, minute)
                            }
                        }
                    }
                    Component {
                        id: calendar_end
                        CusDateTimePicker {
                            is_floor: false
                            target_control: btn_calendar_end
                        }
                    }
                }

                CusSpacer {
                    Layout.preferredWidth: 20 * dpi_ratio
                }
            }
        }

        ContainSaleRecord {
            id: contain_sale_record
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible: cur_query_page == EnumTool.QUERY_SALES_RECORD
        }
        ContainStockRecord {
            id: contain_stock_record
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible: cur_query_page == EnumTool.QUERY_STOCK_RECORD
        }
        ContainHandoverRecord {
            id: contain_handover_record
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible: cur_query_page == EnumTool.QUERY_HANDOVER_RECORD
        }
        ContainGoodsInformation {
            id: contain_goods_information
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible: cur_query_page == EnumTool.QUERY_GOODS_INFORMATION
        }
        ContainGoodsSale {
            id: contain_goods_salestatistics
            Layout.fillHeight: true
            Layout.fillWidth: true

            visible: cur_query_page == EnumTool.QUERY_GOODS_SALESTATISTICS
        }
    }
}
