﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
//import QtQuick.Controls 2.15
import QtQuick.Controls 1.4
import QtGraphicalEffects 1.15
import ".."
import EnumTool 1.0

CusRect {

    onVisibleChanged: {
        if (visible) {
            //resetAllInfo()
                reqGoodsInformation(1)
        }
    }
    function reqHanoverRecordDetail(staff_id, start_time, end_time) {
        end_time = end_time == "---" ? "" : end_time

        lm_sale_record_detail.clear()
        orderControl.getHandoverRecordDetail4Qml(function (data) {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            var json_doc_data1 = json_doc.data1
            var json_doc_address = json_doc.address
            for (var i = 0; i < json_doc_data.length; ++i) {
                lm_sale_record_detail.append(json_doc_data[i])
            }
            resetOverview()
            for (var i = 0; i < json_doc_data1.length; ++i) {
                var cur_json_data = json_doc_data1[i]
                if (cur_json_data.pay_ment == "现金") {
                    rect_overview._cash = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "支付宝") {
                    rect_overview._alipay = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "储值卡") {
                    rect_overview._membership_card = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "百货豆抵扣") {
                    rect_overview._buyhoo_bean = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "微信") {
                    rect_overview._wechat = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "银行卡") {
                    rect_overview._bank_card = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "免密支付") {
                    rect_overview._no_password_payment = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "线上收入") {
                    rect_overview._online_pay = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "金圈平台") {
                    rect_overview._platform_payment = cur_json_data.payment_total
                }
            }

            for (var i = 0; i < json_doc_address.length; ++i) {
                var cur_json_data = json_doc_address[i]
                if (cur_json_data.rechargeMethod == "现金") {
                    rect_overview._recharge_cash = cur_json_data.rechargeMoney
                } else if (cur_json_data.rechargeMethod == "微信") {
                    rect_overview._recharge_wechat = cur_json_data.rechargeMoney
                } else if (cur_json_data.rechargeMethod == "支付宝") {
                    rect_overview._recharge_alipay = cur_json_data.rechargeMoney
                }
            }
        }, staff_id, start_time, end_time)
    }
    function reqGoodsInformation(pageIndex) {

        orderControl.getGoodsInformation4Qml(function (data) {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            lm_handover_record.clear()
            allPageSizeText.text = json_doc.data1
            for(var i = json_doc_data.length - 1,j=0; i >= 0; i--, j++){
                lm_handover_record.insert(j, {goodsName:json_doc_data[i].goodsName,
                                          goodsBarcode: json_doc_data[i].goodsBarcode,
                                          goodsSalePrice:json_doc_data[i].goodsSalePrice,
                                          goodsCount:json_doc_data[i].goodsCount,
                                          saleCount:json_doc_data[i].saleCount,
                                          saleSum:json_doc_data[i].saleSum,
                                          profit: json_doc_data[i].profit,
                                          colorRec: "#d2d2d2"});
            }
            //lm_handover_record.currentIndex = -1
        }, btn_calendar_begin.text, btn_calendar_end.text,"","",pageIndex)
    }
    RowLayout {
        anchors.fill: parent
        spacing: ST.margin

        CusRect {
            id: left_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 1400
            color: ST.color_white_pure
            radius: ST.radius

            ColumnLayout {
                anchors.fill: parent
                spacing: ST.margin

                CusRect {
                    id: order_list
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure
                    TableView
                    {
                        id: tabview_goods_list_disp;
                        anchors.left: parent.left
                        anchors.right: parent.right
                        height: 770 * dpi_ratio;
                        visible: true;

                        TableViewColumn
                        {
                            id: goods_name_column;
                            title: qsTr("商品名称");
                            role: "goodsName";
                            movable: false;
                            resizable: false;
                            width: 330 * dpi_ratio;
                        }

                        TableViewColumn
                        {
                            id: goods_barcode_column;
                            title: qsTr("商品条码");
                            role: "goodsBarcode";
                            movable: false;
                            resizable: false;
                            width: 230 * dpi_ratio;
                        }

                        TableViewColumn
                        {
                            id: goods_price_column;
                            title: qsTr("单价");
                            role: "goodsSalePrice";
                            movable: false;
                            resizable: false;
                            width: 180*  dpi_ratio;
                        }

                        TableViewColumn
                        {
                            id: goods_count_column;
                            title: qsTr("当前库存");
                            role: "goodsCount";
                            movable: false;
                            resizable: false;
                            width: 160*  dpi_ratio;
                        }

                        TableViewColumn
                        {
                            id: sale_count_column;
                            title: qsTr("售出数量");
                            role: "saleCount";
                            movable: false;
                            resizable: false;
                            width: 160*  dpi_ratio;
                        }

                        TableViewColumn
                        {
                            id: sale_sum_column;
                            title: qsTr("售出总价");
                            role: "saleSum";
                            movable: false;
                            resizable: false;
                            width: 180*  dpi_ratio;
                        }

                        TableViewColumn
                        {
                            id: profit_column;
                            title: qsTr("利润");
                            role: "profit";
                            movable: false;
                            resizable: false;
                            width: 150*  dpi_ratio;
                        }

                        model: lm_handover_record;


                        itemDelegate: Rectangle {
                            width:  100*  dpi_ratio;
                            height:  80*  dpi_ratio;
                            color: styleData.row % 2 == 0 ? "#ffffff" : "#f3f4f4";
                            property bool isSelect: styleData.pressed;
                            onIsSelectChanged: {
                                if(!isSelect)
                                {
                                    return ;
                                }
                            }
                            Text
                            {
                                text: styleData.value;
                                anchors.centerIn: parent;
                                font.bold: styleData.selected;
                                font.family: "微软雅黑";
                                font.pointSize: text.length > 18 ?10 * dpi_ratio :text.length > 14?12 *dpi_ratio :14 * dpi_ratio
                                color: "#333333";
                            }
                        }
                        rowDelegate: Rectangle {
                            width:  100*  dpi_ratio;
                            height:  75*  dpi_ratio;
                            color: styleData.row % 2 == 0 ? "#ffffff" : "#f3f4f4";
                            Text{
                                text: styleData.value
                                anchors.left: parent.left;
                                anchors.leftMargin: 20*  dpi_ratio;
                                font.family: "微软雅黑";
                                font.pointSize: 16*  dpi_ratio;
                                color: "#333333";
                            }
                        }
                        headerDelegate: Rectangle{
                            implicitWidth:  50*  dpi_ratio;
                            implicitHeight:  70*  dpi_ratio;
                            color: "#00bd74";
                            Text{
                                text: styleData.value
                                anchors.centerIn: parent
                                font.family: "微软雅黑";
                                font.pointSize: 18*  dpi_ratio;
                                color: "#ffffff";
                            }
                            Rectangle
                            {
                                width: 3*  dpi_ratio;
                                height: 50*  dpi_ratio;
                                color: "#333333";
                                anchors.verticalCenter: parent.verticalCenter;
                                anchors.right: parent.right;
                            }
                        }
                        ListModel
                        {
                            id: lm_handover_record;
                        }
                    }
                    Rectangle
                    {
                        id: firstPage;
                        width: 100*  dpi_ratio;
                        height: 50*  dpi_ratio;
                        color: "#cccccc";
                        radius: 3*  dpi_ratio;
                        anchors.verticalCenter: currentPageText.verticalCenter;
                        anchors.right: lastPage.left;
                        anchors.rightMargin: 30 * dpi_ratio;
                        visible: true;
                        Text
                        {
                            id: firstPageText;
                            anchors.centerIn: parent;
                            font.family: "微软雅黑";
                            font.pointSize: 18*  dpi_ratio;
                            text: qsTr("首页");
                            color: "#333333";
                        }
                        Timer
                        {
                            id: firstPageTimer;
                            interval: 100;
                            running: false;
                            repeat: false;
                            onTriggered:
                            {
                                firstPage.color = "#cccccc";
                                firstPageText.color = "#333333";
                            }
                        }

                        MouseArea
                        {
                            anchors.fill: parent;
                            onClicked:
                            {
                                firstPageTimer.start();
                                firstPage.color = "#ffffff";
                                firstPageText.color = "#333333";
                                currentPageText.text = "1";

                                reqGoodsInformation(1)
                            }
                        }
                    }
                    Rectangle
                    {
                        id: lastPage;
                        width: 100*  dpi_ratio;
                        height: 50*  dpi_ratio;
                        color: "#cccccc";
                        radius: 3*  dpi_ratio;
                        anchors.verticalCenter: currentPageText.verticalCenter;
                        anchors.right: currentPageText.left;
                        anchors.rightMargin: 30*  dpi_ratio;
                        Text
                        {
                            id: lastPageText;
                            anchors.centerIn: parent;
                            font.family: "微软雅黑";
                            font.pointSize: 18*  dpi_ratio;
                            text: qsTr("上一页");
                            color: "#333333";
                        }
                        Timer
                        {
                            id: lastPageTimer;
                            interval: 100;
                            running: false;
                            repeat: false;
                            onTriggered:
                            {
                                lastPage.color = "#cccccc";
                                lastPageText.color = "#333333";
                            }
                        }

                        MouseArea
                        {
                            anchors.fill: parent;
                            onClicked:
                            {
                                lastPageTimer.start();
                                lastPage.color = "#ffffff";
                                lastPageText.color = "#333333";


                                if (currentPageText.text*1 > 1)
                                {
                                    currentPageText.text = (currentPageText.text*1-1).toFixed(0);
                                }
                                reqGoodsInformation(currentPageText.text*1)
                            }
                        }
                    }

                    Text
                    {
                        id: currentPageText;
                        anchors.horizontalCenter: parent.horizontalCenter;
                        anchors.bottom: parent.bottom;
                        anchors.bottomMargin: 30*  dpi_ratio;
                        font.family: "微软雅黑";
                        font.pointSize: 18*  dpi_ratio;
                        text: "1";
                        color: "#333333";
                    }

                    Text
                    {
                        id: allPageSizeTextTip;
                        anchors.verticalCenter: currentPageText.verticalCenter;
                        anchors.left: currentPageText.right;
                        font.family: "微软雅黑";
                        font.pointSize: 18*  dpi_ratio;
                        color: "#333333";
                        text: "/"
                    }

                    Text
                    {
                        id: allPageSizeText;
                        anchors.verticalCenter: currentPageText.verticalCenter;
                        anchors.left: allPageSizeTextTip.right;
                        font.family: "微软雅黑";
                        font.pointSize: 18*  dpi_ratio;
                        color: "#333333";
                    }

                    Rectangle
                    {
                        id: nextPage;
                        width: 100*  dpi_ratio;
                        height: 50*  dpi_ratio;
                        color: "#cccccc";
                        radius: 3*  dpi_ratio;
                        anchors.verticalCenter: currentPageText.verticalCenter;
                        anchors.left: allPageSizeText.right;
                        anchors.leftMargin: 20*  dpi_ratio;
                        Text
                        {
                            id: nextPageText;
                            anchors.centerIn: parent;
                            font.family: "微软雅黑";
                            font.pointSize: 18*  dpi_ratio;
                            text: qsTr("下一页");
                            color: "#333333";
                        }

                        Timer
                        {
                            id: nextPageTimer;
                            interval: 100;
                            running: false;
                            repeat: false;
                            onTriggered:
                            {
                                nextPage.color = "#cccccc";
                                nextPageText.color = "#333333";
                            }
                        }

                        MouseArea
                        {
                            anchors.fill: parent;
                            onClicked:
                            {
                                nextPageTimer.start();
                                nextPage.color = "#ffffff";
                                nextPageText.color = "#333333";


                                if (currentPageText.text*1 < 10000)//TODO这里限制总页数
                                {
                                    currentPageText.text = (currentPageText.text*1+1).toFixed(0);
                                }
                                if (currentPageText.text*1 > allPageSizeText.text*1)
                                {
                                    currentPageText.text = allPageSizeText.text;
                                }
                                reqGoodsInformation(currentPageText.text *1)
                            }
                        }
                    }

                    Rectangle
                    {
                        id: goPage;
                        width: 100*  dpi_ratio;
                        height: 50*  dpi_ratio;
                        color: "#cccccc";
                        radius: 3*  dpi_ratio;
                        anchors.verticalCenter: currentPageText.verticalCenter;
                        anchors.left: nextPage.right;
                        anchors.leftMargin: 30*  dpi_ratio;
                        Text
                        {
                            id: goPageText;
                            anchors.centerIn: parent;
                            font.family: "微软雅黑";
                            font.pointSize: 18*  dpi_ratio;
                            text: qsTr("跳转");
                            color: "#333333";
                        }

                        Timer
                        {
                            id: goPageTimer;
                            interval: 100;
                            running: false;
                            repeat: false;
                            onTriggered:
                            {
                                goPage.color = "#cccccc";
                                goPageText.color = "#333333";
                            }
                        }

                        MouseArea
                        {
                            anchors.fill: parent;
                            onClicked:
                            {
                                goPageTimer.start();
                                goPage.color = "#ffffff";
                                goPageText.color = "#333333";

                                lm_handover_record.clear();
                                currentPageText.text = goPageTextContent.text;
                                reqGoodsInformation(currentPageText.text *1);

                            }
                        }
                    }

                    Rectangle
                    {
                        id: goPageTextContentRec;
                        width: 80*  dpi_ratio;
                        height: 50*  dpi_ratio;
                        anchors.left: goPage.right;
                        anchors.leftMargin: 15*  dpi_ratio;
                        anchors.verticalCenter: goPage.verticalCenter;
                        border.color: "#999999";
                        border.width: 1;
                        visible: tabview_goods_list_disp.visible;
                        TextInput
                        {
                            id: goPageTextContent;
                            width: 80*  dpi_ratio;
                            height: 50*  dpi_ratio;
                            anchors.centerIn: parent;
                            font.family: "微软雅黑";
                            font.pointSize: 18*  dpi_ratio;
                            color: "#999999";
                            text: "1";
                            verticalAlignment: Text.AlignVCenter;
                            horizontalAlignment: Text.AlignHCenter;
                            validator: IntValidator{
                                bottom: 1; top:99999
                            }
                            onTextChanged:
                            {
                                if(goPageTextContent.text == "0")
                                {
                                    goPageTextContent.text = "";
                                }
                                if(goPageTextContent.text == "+")
                                {
                                    goPageTextContent.text = "";
                                }
                            }

                            MouseArea
                            {
                                anchors.fill: parent;
                                onClicked:
                                {
                                    goPageTextContent.text = "";
                                    goPageTextContent.forceActiveFocus();
                                    window_root.keyboard_c.openDigitalKeyboard()
                                    digital_keyboard_x: 873 * dpi_ratio
                                    digital_keyboard_y: 374 * dpi_ratio
                                }
                            }
                            onAccepted:
                            {
                                //lm_handover_record.clear();
                                currentPageText.text = goPageTextContent.text;

                            }
                        }
                    }

                    Text
                    {
                        id: goPageTextContentText;
                        anchors.left: goPageTextContentRec.right;
                        anchors.leftMargin: 15*  dpi_ratio;
                        anchors.verticalCenter: goPageTextContentRec.verticalCenter;
                        font.family: "微软雅黑";
                        font.pointSize: 18*  dpi_ratio;
                        text: qsTr("页");
                        color: "#333333";
                    }
                }
            }
        }

        CusRect {
            id: right_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 500
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: ST.margin

                CusRect {
                    id: rect_overview
                    Layout.preferredHeight: 370 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    property var _cash: "0"
                    property var _alipay: "0"
                    property var _wechat: "0"
                    property var _bank_card: "0"
                    property var _no_password_payment: "0"
                    property var _membership_card: "0"
                    property var _online_pay: "0"
                    property var _buyhoo_bean: "0"
                    property var _platform_payment: "0"

                    property var _recharge_cash: "0"
                    property var _recharge_alipay: "0"
                    property var _recharge_wechat: "0"

                    ColumnLayout {
                        anchors.fill: parent
                        CusRect {
                            Layout.preferredHeight: 45 * dpi_ratio
                            Layout.fillWidth: true
                            color: ST.color_green

                            CusText {
                                text: qsTr("充值统计")
                                anchors.verticalCenter: parent.verticalCenter
                                x: 14 * dpi_ratio
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.leftMargin: 15 * dpi_ratio
                            Layout.rightMargin: 15 * dpi_ratio
                            Layout.preferredHeight: 75 * dpi_ratio
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                anchors.leftMargin: 20 * dpi_ratio
                                anchors.rightMargin: 20 * dpi_ratio
                                spacing: 20 * dpi_ratio
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true

                                    Image {
                                        source: "/Images/cash_type_image.png"
                                        anchors.fill: parent
                                        fillMode: Image.PreserveAspectFit
                                        anchors.margins: 7 * dpi_ratio
                                    }

                                    CusRect {
                                        anchors.fill: parent
                                        color: ST.color_black
                                        opacity: .4
                                    }
                                    CusText {
                                        anchors.centerIn: parent
                                        text: rect_overview._recharge_cash //充值-现金
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Image {
                                        source: "/Images/alipay_image.png"
                                        anchors.fill: parent
                                        fillMode: Image.PreserveAspectFit
                                        anchors.margins: 7 * dpi_ratio
                                    }
                                    CusRect {
                                        anchors.fill: parent
                                        color: ST.color_black
                                        opacity: .4
                                    }
                                    CusText {
                                        anchors.centerIn: parent
                                        text: rect_overview._recharge_alipay //充值-支付宝
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Image {
                                        source: "/Images/wechat_image.png"
                                        anchors.fill: parent
                                        fillMode: Image.PreserveAspectFit
                                        anchors.margins: 7 * dpi_ratio
                                    }
                                    CusRect {
                                        anchors.fill: parent
                                        color: ST.color_black
                                        opacity: .4
                                    }
                                    CusText {
                                        anchors.centerIn: parent
                                        text: rect_overview._recharge_wechat //充值-微信
                                        color: ST.color_white_pure
                                    }
                                }
                            }
                        }
                        CusRect {
                            Layout.preferredHeight: 45 * dpi_ratio
                            Layout.fillWidth: true
                            color: ST.color_green
                            CusText {
                                text: qsTr("收银统计")
                                anchors.verticalCenter: parent.verticalCenter
                                x: 14 * dpi_ratio
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.leftMargin: 15 * dpi_ratio
                            Layout.rightMargin: 15 * dpi_ratio
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            GridLayout {
                                anchors.fill: parent
                                columns: 2

                                columnSpacing: 5 * dpi_ratio
                                rowSpacing: 5 * dpi_ratio

                                property double col_width: (width - (columns - 1) * rowSpacing) / columns
                                property double col_height: (height - (rows - 1) * columnSpacing) / rows

                                CusText {
                                    text: qsTr("现金: ") + rect_overview._cash
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("支付宝: ") + rect_overview._alipay
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("微信: ") + rect_overview._wechat
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("银行卡: ") + rect_overview._bank_card
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                // CusText {
                                //     text: "免密: " + rect_overview._no_password_payment
                                //     Layout.leftMargin: 0
                                //     Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                //     font.pixelSize: 20 * dpi_ratio
                                //     Layout.fillWidth: true
                                //     Layout.preferredWidth: 20
                                // }
                                CusText {
                                    text: qsTr("储值卡: ") + rect_overview._membership_card
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("线上收入: ") + rect_overview._online_pay
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("百货豆抵: ") + rect_overview._buyhoo_bean
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("免密: ") + rect_overview._platform_payment
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                            }
                        }
                    }
                }

                CusRect {
                    id: goods_list
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    ColumnLayout {
                        anchors.fill: parent
                        //标题栏
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio

                            RowLayout {
                                anchors.fill: parent
                                spacing: 1 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("订单时间")
                                        color: ST.color_black
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 200 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("订单编号")
                                        color: ST.color_black
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("销售金额")
                                        color: ST.color_black
                                    }
                                }
                            }
                        }

                        ListView {
                            id: lv_sale_record_detail
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            clip: true
                            model: ListModel {
                                id: lm_sale_record_detail
                            }

                            delegate: Item {
                                width: lv_sale_record_detail.width
                                height: 50 * dpi_ratio

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 2 * dpi_ratio

                                    //订单时间
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        id: rect_order_time

                                        property string sale_list_date: sale_list_datetime.split(" ")[0]
                                        property string sale_list_time: sale_list_datetime.split(" ")[1]

                                        ColumnLayout {
                                            anchors.fill: parent
                                            anchors.leftMargin: 15 * dpi_ratio
                                            anchors.rightMargin: 15 * dpi_ratio
                                            anchors.topMargin: 5 * dpi_ratio
                                            anchors.bottomMargin: 5 * dpi_ratio
                                            spacing: 0

                                            CusText {
                                                x: 15 * dpi_ratio
                                                width: parent.width - 15 * 2 * dpi_ratio
                                                text: rect_order_time.sale_list_date
                                                color: ST.color_black
                                                elide: Text.ElideRight
                                                font.pixelSize: 16 * dpi_ratio
                                                Layout.alignment: Qt.AlignHCenter
                                            }

                                            CusText {
                                                x: 15 * dpi_ratio
                                                width: parent.width - 15 * 2 * dpi_ratio
                                                text: rect_order_time.sale_list_time
                                                color: ST.color_black
                                                elide: Text.ElideRight
                                                font.pixelSize: 16 * dpi_ratio
                                                Layout.alignment: Qt.AlignHCenter
                                            }
                                        }
                                    }
                                    //订单编号
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 200 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: sale_list_unique
                                            color: ST.color_black
                                            //                                            anchors.verticalCenter: parent.verticalCenter
                                            font.pixelSize: 17 * dpi_ratio
                                        }
                                    }
                                    //销售金额
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            text: sale_list_total
                                            color: ST.color_black
                                            //                                            anchors.verticalCenter: parent.verticalCenter
                                            font.pixelSize: 18 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
