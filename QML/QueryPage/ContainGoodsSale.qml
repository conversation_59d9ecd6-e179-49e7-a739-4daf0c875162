﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
//import QtQuick.Controls 2.15
import QtQuick.Controls 1.4
import QtGraphicalEffects 1.15
import ".."
import "../Utils2"
import EnumTool 1.0

CusRect {
    id:rootRec

    property string goods_big_kind: "" //商品大类
    property string goods_small_kind: "" //商品小类
    property string goods_supplier: "" //供应商
    property var statisticalData: [] //data1
    property var orderDetails: [] //data

    function processJsonPara(value) {
        if (value !== "") {
            return value;
        }
    }

    Component.onCompleted: {
        goods_big_kind = ""
        goods_small_kind = ""
        goods_supplier = ""
        refreshSupplier()
        refreshCategory()
    }
    onVisibleChanged: {
        if (visible) {
            goods_big_kind = ""
            goods_small_kind = ""
            goods_supplier = ""
            reqGoodsSaleStatistics(1)
            refreshSupplier()
            refreshCategory()
            tf_search_order.forceActiveFocus()
        }
    }
    function showSalesDetails() {
        window_root.loader_4_stock_del.sourceComponent = popup_sales_details
        window_root.loader_4_stock_del.item.open(statisticalData,orderDetails)
    }
    Component {
        id: popup_sales_details
        PopupSalesDetails{
            onSigClose: {

                statisticalData =  [] //data1
                orderDetails = [] //data
            }

        }
    }
    //商品销售明细
    function reqGoodsSaleDetail( goodsBarcode,goodSaleListName) {
        orderControl.getGoodsSaleDetail4Qml(function (data) {
            var json_doc = JSON.parse(data)
            var json_doc_data1 = json_doc.data1
            var json_doc_data = json_doc.data
            if (json_doc_data1 !== undefined && json_doc_data1 !== null)
             {
                statisticalData.push(json_doc_data1.saleCount.toFixed(3));
                statisticalData.push(json_doc_data1.saleSum.toFixed(2));
                statisticalData.push(json_doc_data1.purSum.toFixed(2));
                statisticalData.push(goodSaleListName);
                statisticalData.push(json_doc.address);
             }else{

             }
             for (var i = 0; i < json_doc_data.length; ++i) {
                 orderDetails.push([])
                 if (processJsonPara(json_doc_data[i].saleListUnique) !== undefined){
                     orderDetails[i].push(json_doc_data[i].saleListUnique)
                 }
                 if (processJsonPara(json_doc_data[i].saleListDetailCount) !== undefined){
                     orderDetails[i].push(json_doc_data[i].saleListDetailCount)
                 }
                 if (processJsonPara(json_doc_data[i].saleSum) !== undefined){
                     orderDetails[i].push(json_doc_data[i].saleSum)
                 }
                 if (processJsonPara(json_doc_data[i].purSum) !== undefined){
                     orderDetails[i].push(json_doc_data[i].purSum)
                 }

                 if (processJsonPara(json_doc_data[i].saleListDatetime) !== undefined){
                     orderDetails[i].push(json_doc_data[i].saleListDatetime)
                 }
                 if (processJsonPara(json_doc_data[i].goodsName) !== undefined){
                     orderDetails[i].push(json_doc_data[i].goodsName)
                 }
                 if (processJsonPara(json_doc_data[i].goodsBarcode) !== undefined){
                     orderDetails[i].push(json_doc_data[i].goodsBarcode)
                 }
                 //lm_handover_record.append(json_doc_data[i])
             }
             //商品销售明细
             showSalesDetails()

        },goodsBarcode,btn_calendar_begin.text, btn_calendar_end.text,"1","14")
    }
    // 刷新一级分类
    function refreshCategory() {
        lm_category1.clear()
        var data_json = JSON.parse(goodsKindManager.getRootGoodsKind4Qml())
        if (!data_json)
            return
        var cur_item
        for (var i = 0; i < data_json.length; ++i) {
            cur_item = data_json[i]
            if(i == 0){
                lm_category1.append({"goods_kind_name": qsTr("请选择大类")})
                lm_category1.append(cur_item)
            }else{
                lm_category1.append(cur_item)

            }
        }
    }
    // 刷新供应商
    function refreshSupplier() {
        lm_supplier.clear()
        var json_data = JSON.parse(supplierControl.getSupplierJson())
        if (!json_data)
            return
        for (var i = 0; i < json_data.length; ++i) {
            var cur_item = json_data[i]
            if(i == 0){
                lm_supplier.append({"supplier_name": "请选择供货商"})
                lm_supplier.append(cur_item)
            }else{
                lm_supplier.append(cur_item)

            }
        }
    }
    function reqHanoverRecordDetail(staff_id, start_time, end_time) {
        end_time = end_time == "---" ? "" : end_time

        lm_sale_record_detail.clear()
        orderControl.getHandoverRecordDetail4Qml(function (data) {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            var json_doc_data1 = json_doc.data1
            var json_doc_address = json_doc.address
            for (var i = 0; i < json_doc_data.length; ++i) {
                lm_sale_record_detail.append(json_doc_data[i])
            }

            resetOverview()
            for (var i = 0; i < json_doc_data1.length; ++i) {
                var cur_json_data = json_doc_data1[i]
                if (cur_json_data.pay_ment == "现金") {
                    rect_overview._cash = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "支付宝") {
                    rect_overview._alipay = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "储值卡") {
                    rect_overview._membership_card = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "百货豆抵扣") {
                    rect_overview._buyhoo_bean = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "微信") {
                    rect_overview._wechat = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "银行卡") {
                    rect_overview._bank_card = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "免密支付") {
                    rect_overview._no_password_payment = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "线上收入") {
                    rect_overview._online_pay = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "金圈平台") {
                    rect_overview._platform_payment = cur_json_data.payment_total
                }
            }

            for (var i = 0; i < json_doc_address.length; ++i) {
                var cur_json_data = json_doc_address[i]
                if (cur_json_data.rechargeMethod == "现金") {
                    rect_overview._recharge_cash = cur_json_data.rechargeMoney
                } else if (cur_json_data.rechargeMethod == "微信") {
                    rect_overview._recharge_wechat = cur_json_data.rechargeMoney
                } else if (cur_json_data.rechargeMethod == "支付宝") {
                    rect_overview._recharge_alipay = cur_json_data.rechargeMoney
                }
            }
        }, staff_id, start_time, end_time)
    }
    function reqGoodsSaleStatistics(pageNum) {

        saleTotalMoneyText.text = "0.00";
        saleTotalAmountText.text =  "0.00";
        saleTotalCostText.text =  "0.00";
        saleTotalProfitText.text = "0.00";

        orderControl.getGoodsSaleStatistics4Qml(function (data) {
            var json_doc = JSON.parse(data)
            var json_doc_data1 = json_doc.data1
            allPageSizeText.text = json_doc.address
             if (json_doc.data1 && Array.isArray(json_doc.data1) && json_doc.data1.length > 0)
             {
                saleTotalMoneyText.text = json_doc_data1[0].saleSum.toFixed(2);
                saleTotalAmountText.text =  json_doc_data1[0].saleCount.toFixed(3);
                saleTotalCostText.text =  json_doc_data1[0].purSum.toFixed(2);
                saleTotalProfitText.text = json_doc_data1[0].grossProfit.toFixed(2);
                goodsSaleModel.clear()
                var json_doc_data = json_doc.data

                 for(var i = 0;i < json_doc_data.length; i++)
                 {
                     goodsSaleModel.insert(i,{goodSaleListUnique:json_doc_data[i].goodsBarcode,
                                               goodSaleListName:json_doc_data[i].goodsName,
                                               goodSaleListCount:json_doc_data[i].saleCount,
                                               goodSaleListGive:json_doc_data[i].giftCount,
                                               goodSaleListMoney:json_doc_data[i].saleSum,
                                               goodSaleListCost:json_doc_data[i].purSum,
                                               goodSaleListGrossProfit:json_doc_data[i].grossProfit+"%"})
                     logMgr.logEvtInfo4Qml("json_doc_data[i].goodsBarcode:{}",json_doc_data[i].goodsBarcode)
                 }
             }else{
                goodsSaleModel.clear()
             }
        }, btn_calendar_begin.text, btn_calendar_end.text,tf_search_order.text,pageNum,10,rootRec.goods_big_kind,rootRec.goods_small_kind,rootRec.goods_supplier)
    }
    // 刷新二级分类
    function refreshCategory2(parent_goods_kind_unique = "") {
        lm_category2.clear()

        if (parent_goods_kind_unique == "")
            return

        var data_json = JSON.parse(goodsKindManager.getSubGoodsKind4Qml(parent_goods_kind_unique))
        if (!data_json)
            return
        for (var i = 0; i < data_json.length; ++i) {
            var cur_item = data_json[i]
            if(i == 0){
                lm_category2.append({"goods_kind_name": qsTr("请选择小类")})
                lm_category2.append(cur_item)
            }else{
                lm_category2.append(cur_item)

            }
        }
    }
    Timer
    {
        id: goodsSaleListQueryTimer;
        interval: 200;
        running: false;
        repeat: false;
        onTriggered:
        {
            searchCus.color = "#ffffff";
            searchCusTex.color = "#333333";
        }
    }
    RowLayout {
        anchors.fill: parent
        spacing: ST.margin

        CusRect {
            id: left_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 1400 *dpi_ratio
            color: ST.color_white_pure
            radius: ST.radius

            ColumnLayout {
                anchors.fill: parent
                spacing: ST.margin

                CusRect {
                    id: order_list
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure

                    ColumnLayout {
                        anchors.fill: parent
                        //菜单选择栏
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio
                            RowLayout {
                                anchors.fill: parent
                                spacing: 1 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 225 * dpi_ratio
                                    clip: true
                                    color: ST.color_white_pure
                                    CusText {
                                        id:goodsBigcate
                                        anchors.verticalCenter:parent.verticalCenter;
                                        text: qsTr("商品大类: ")
                                        font.pixelSize: text.length > 6 ?16 * configTool.fontRatio * dpi_ratio:22 * configTool.fontRatio * dpi_ratio
                                        color: ST.color_black
                                        anchors.left:parent.left
                                        anchors.leftMargin: 20 * dpi_ratio
                                        Layout.preferredWidth: 80 * dpi_ratio
                                    }

                                    CusComboBox2 {
                                        id: cb_category1
                                        Layout.preferredWidth: 150 * dpi_ratio
                                        Layout.fillHeight: true
                                        anchors.verticalCenter:parent.verticalCenter
                                        anchors.left:goodsBigcate.right
                                        textRole: "goods_kind_name"
                                        qsTrString: qsTr("请选择大类")
                                        model: ListModel {
                                            id: lm_category1
                                        }

                                        onCurrentIndexChanged: {
                                            if (currentIndex == -1 ||currentIndex == 0) {

                                                refreshCategory2("")
                                                rootRec.goods_big_kind = ""
                                            } else {
                                                refreshCategory2(lm_category1.get(currentIndex).goods_kind_unique)
                                                rootRec.goods_big_kind = lm_category1.get(currentIndex).goods_kind_unique
                                            }
                                        }
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 245 * dpi_ratio
                                    clip: true
                                    color: ST.color_white_pure
                                    CusText {
                                        id:goodsSmallcate
                                        anchors.verticalCenter:parent.verticalCenter;
                                        text: qsTr("商品小类: ")
                                        font.pixelSize: text.length > 6 ?16 * configTool.fontRatio * dpi_ratio:22 * configTool.fontRatio * dpi_ratio
                                        color: ST.color_black
                                        anchors.left:parent.left
                                        anchors.leftMargin: 2 * dpi_ratio
                                        Layout.preferredWidth: 80 * dpi_ratio
                                    }
                                    CusComboBox2 {
                                        id: cb_category2
                                        Layout.preferredWidth: 150 * dpi_ratio
                                        Layout.fillHeight: true
                                        anchors.verticalCenter:parent.verticalCenter
                                        anchors.left:goodsSmallcate.right
                                        textRole: "goods_kind_name"
                                        qsTrString: qsTr("请选择小类")
                                        model: ListModel {
                                            id: lm_category2
                                        }
                                        onCurrentIndexChanged: {
                                            if (currentIndex == -1 || currentIndex == 0) {
                                                rootRec.goods_small_kind = ""
                                            } else {
                                                rootRec.goods_small_kind = lm_category2.get(currentIndex).goods_kind_unique

                                            }
                                        }
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 210 * dpi_ratio
                                    clip: true
                                    color: ST.color_white_pure

                                    CusText {
                                        id:supplierText
                                        anchors.verticalCenter:parent.verticalCenter;
                                        text: qsTr("供货商: ")
                                        font.pixelSize: text.length > 6 ?16 * configTool.fontRatio * dpi_ratio:22 * configTool.fontRatio * dpi_ratio
                                        color: ST.color_black
                                        anchors.left:parent.left
                                        anchors.leftMargin: 10 * dpi_ratio
                                        Layout.preferredWidth: 80 * dpi_ratio
                                    }
                                    CusComboBox2 {
                                        id: supplierBox
                                        Layout.preferredWidth: 150 * dpi_ratio
                                        Layout.fillHeight: true
                                        anchors.verticalCenter:parent.verticalCenter
                                        anchors.left:supplierText.right
                                        textRole: "supplier_name"
                                        qsTrString: qsTr("请选择供货商")
                                        model: ListModel {
                                            id: lm_supplier
                                        }
                                        onCurrentIndexChanged: {
                                            if (currentIndex == -1 || currentIndex == 0) {
                                                rootRec.goods_supplier = ""
                                            } else {
                                                rootRec.goods_supplier = lm_supplier.get(currentIndex).supplier_unique
                                            }
                                        }
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 260 * dpi_ratio
                                    clip: true
                                    color: ST.color_white_pure

                                    CusText {
                                        id:goodsSearch
                                        anchors.verticalCenter:parent.verticalCenter;
                                        text: qsTr("商品信息: ")
                                        font.pixelSize: text.length > 6 ?16 * configTool.fontRatio * dpi_ratio:22 * configTool.fontRatio * dpi_ratio
                                        color: ST.color_black
                                        anchors.left:parent.left
                                        anchors.leftMargin: 2 * dpi_ratio
                                        Layout.preferredWidth: 70 * dpi_ratio
                                    }
                                    CusTextField {
                                        id: tf_search_order
                                        Layout.fillHeight: true
                                        width: 240 * dpi_ratio
                                        keyboard_status: 1
                                        font.pixelSize: 22 * configTool.fontRatio * dpi_ratio
                                        font.family: ST.fontFamilyYaHei
                                        color: ST.color_black
                                        anchors.verticalCenter:parent.verticalCenter
                                        anchors.left:goodsSearch.right
                                        placeholderText: qsTr("名称、条码或首字母")
                                        back_rect.border.color: "#D8D8D8";
                                        back_rect.border.width: 2 * dpi_ratio
                                        back_rect.height: 40 *dpi_ratio
                                        back_rect.width: 240 * dpi_ratio
                                        back_rect.anchors.verticalCenter: tf_search_order.verticalCenter
                                        onTextChanged: {

                                        }
                                    }
                                }
                                CusRect {
                                    id:searchCus
                                    Layout.preferredHeight: 40 * dpi_ratio
                                    Layout.preferredWidth: 80 * dpi_ratio
                                    border.width: 2  * dpi_ratio;
                                    border.color: "#D8D8D8";
                                    anchors.right: parent.right
                                    anchors.rightMargin: 10 *dpi_ratio
                                    color: ST.color_white_pure
                                    radius: ST.radius

                                    CusText {
                                        id:searchCusTex
                                        anchors.centerIn: parent
                                        text: qsTr("搜索")
                                        color: ST.color_black
                                    }
                                    MouseArea
                                    {
                                        anchors.fill: parent
                                        onClicked:
                                        {
                                            searchCus.color = "#999999";
                                            searchCusTex.color = "#ffffff";
                                            goodsSaleListQueryTimer.start();
                                            reqGoodsSaleStatistics(1);
                                        }
                                    }
                                }

                            }
                        }
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 10 * dpi_ratio
                            Rectangle
                            {
                                id: goodsSaleListLine;
                                width: 1350* dpi_ratio;
                                height: 1* dpi_ratio;
                                color: "#999999";
                                anchors.horizontalCenter: parent.horizontalCenter;
                            }
                        }
                        //统计菜单
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio
                            RowLayout {
                                id:middleRec
                                anchors.fill: parent
                                spacing: 1 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_white_pure
                                    CusText {
                                        id:saleTotalAmount
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.left: parent.left
                                        anchors.leftMargin: 10 * dpi_ratio
                                        text: qsTr("销售总数量: ")
                                        color:"#999999"
                                    }
                                    Text
                                    {
                                        id:saleTotalAmountText
                                        anchors.verticalCenter: parent.verticalCenter;
                                        anchors.left: saleTotalAmount.right;
                                        font.family: "微软雅黑";
                                        font.pointSize: 18 * dpi_ratio;
                                        color: "#333333";
                                        text: "0.00";
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_white_pure
                                    CusText {
                                        id:saleTotalMoney
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.left: parent.left
                                        anchors.leftMargin: 20 * dpi_ratio
                                        text: qsTr("销售总金额: ")
                                        color:"#999999"
                                    }
                                    Text
                                    {
                                        id:saleTotalMoneyText
                                        anchors.verticalCenter: parent.verticalCenter;
                                        anchors.left: saleTotalMoney.right;
                                        font.family: "微软雅黑";
                                        font.pointSize: 18 * dpi_ratio;
                                        color: "#333333";
                                        text: "0.00";
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_white_pure
                                    CusText {
                                        id:saleTotalCost
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.left: parent.left
                                        anchors.leftMargin: 20 * dpi_ratio
                                        text: qsTr("销售总成本: ")
                                        color:"#999999"
                                    }
                                    Text
                                    {
                                        id:saleTotalCostText
                                        anchors.verticalCenter: parent.verticalCenter;
                                        anchors.left: saleTotalCost.right;
                                        font.family: "微软雅黑";
                                        font.pointSize: 18 * dpi_ratio;
                                        color: "#333333";
                                        text: "0.00";
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_white_pure
                                    CusText {
                                        id:saleTotalProfit
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.left: parent.left
                                        anchors.leftMargin: 20 * dpi_ratio
                                        text: qsTr("销售总利润: ")
                                        color:"#999999"
                                    }
                                    Text
                                    {
                                        id:saleTotalProfitText
                                        anchors.verticalCenter: parent.verticalCenter;
                                        anchors.left: saleTotalProfit.right;
                                        font.family: "微软雅黑";
                                        font.pointSize: 18 * dpi_ratio;
                                        color: "#333333";
                                        text: "0.00";
                                    }
                                }
                            }
                        }
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_white_pure
                            width: 1394 * dpi_ratio;
                            height: 780 * dpi_ratio;
                            anchors.top: middleRec.bottom;
                            TableView
                            {
                                id: tabview_goods_sale_list;
                                anchors.left: parent.left
                                anchors.right: parent.right
                                height: 640 * dpi_ratio;
                                anchors.horizontalCenter: parent.horizontalCenter;
                                visible: true;

                                TableViewColumn
                                {
                                    id: goodSaleListDatetimeColumn;
                                    title: qsTr("商品条码");
                                    role: "goodSaleListUnique";
                                    movable: false;
                                    resizable: false;
                                    width: 230 * dpi_ratio;
                                }
                                TableViewColumn
                                {
                                    id: goodSaleListDatetimeColumn2;
                                    title: qsTr("商品名称");
                                    role: "goodSaleListName";
                                    movable: false;
                                    resizable: false;
                                    width: 310*    dpi_ratio;
                                }
                                TableViewColumn
                                {
                                    id: goodSaleListDatetimeColumn3;
                                    title: qsTr("销售数量");
                                    role: "goodSaleListCount";
                                    movable: false;
                                    resizable: false;
                                    width: 180*    dpi_ratio;
                                }
                                TableViewColumn
                                {
                                    id: goodSaleListDatetimeColumn4;
                                    title: qsTr("赠送数量");
                                    role: "goodSaleListGive";
                                    movable: false;
                                    resizable: false;
                                    width: 180*    dpi_ratio;
                                }
                                TableViewColumn
                                {
                                    id: goodSaleListDatetimeColumn5;
                                    title: qsTr("销售金额");
                                    role: "goodSaleListMoney";
                                    movable: false;
                                    resizable: false;
                                    width: 170*    dpi_ratio;
                                }
                                TableViewColumn
                                {
                                    id: goodSaleListDatetimeColumn6;
                                    title: qsTr("销售成本");
                                    role: "goodSaleListCost";
                                    movable: false;
                                    resizable: false;
                                    width: 170*   dpi_ratio;
                                }
                                TableViewColumn
                                {
                                    id: goodSaleListDatetimeColumn7;
                                    title: qsTr("销售毛利润");
                                    role: "goodSaleListGrossProfit";
                                    movable: false;
                                    resizable: false;
                                    width: 160*    dpi_ratio;
                                }

                                model: goodsSaleModel;
                                itemDelegate: Rectangle {
                                    width:  100*    dpi_ratio;
                                    height:  80*  dpi_ratio;
                                    color: styleData.row % 2 == 0 ? "#ffffff" : "#f3f4f4";
                                    property bool isSelect: styleData.pressed;
                                    onIsSelectChanged: {
                                        if(!isSelect)
                                        {
                                            return ;
                                        }
                                        if(isSelect){
                                            reqGoodsSaleDetail(goodsSaleModel.get(styleData.row).goodSaleListUnique, goodsSaleModel.get(styleData.row).goodSaleListName);
                                        }
                                    }
                                    Text
                                    {
                                        text: styleData.value;
                                        anchors.centerIn: parent;
                                        font.bold: styleData.selected;
                                        font.family: "微软雅黑";
                                        font.pointSize: text.length > 18 ?10 * dpi_ratio :text.length > 14?12 *dpi_ratio :14 * dpi_ratio
                                        color: "#333333";
                                    }
                                }
                                rowDelegate: Rectangle {
                                    width:  100*    dpi_ratio;
                                    height:  75*  dpi_ratio;
                                    color: styleData.row % 2 == 0 ? "#ffffff" : "#f3f4f4";
                                    Text{
                                        text: styleData.value
                                        anchors.left: parent.left;
                                        anchors.leftMargin: 20* dpi_ratio;
                                        font.family: "微软雅黑";
                                        font.pointSize: 16* dpi_ratio;
                                        color: "#333333";
                                    }
                                }
                                headerDelegate: Rectangle{
                                    implicitWidth:  50*    dpi_ratio;
                                    implicitHeight:  70*  dpi_ratio;
                                    color: "#00bd74";
                                    Text{
                                        text: styleData.value
                                        anchors.centerIn: parent
                                        font.family: "微软雅黑";
                                        font.pointSize: 16*    dpi_ratio;
                                        color: "#ffffff";
                                    }
                                    Rectangle
                                    {
                                        width: 3*    dpi_ratio;
                                        height: 50*  dpi_ratio;
                                        color: "#333333";
                                        anchors.verticalCenter: parent.verticalCenter;
                                        anchors.right: parent.right;
                                    }
                                }
                                ListModel
                                {
                                    id: goodsSaleModel;
                                }
                            }
                            Rectangle
                            {
                                id: firstPage;
                                width: 100*  dpi_ratio;
                                height: 50*  dpi_ratio;
                                color: "#cccccc";
                                radius: 3*  dpi_ratio;
                                anchors.verticalCenter: currentPageText.verticalCenter;
                                anchors.right: lastPage.left;
                                anchors.rightMargin: 30 * dpi_ratio;
                                visible: true;
                                Text
                                {
                                    id: firstPageText;
                                    anchors.centerIn: parent;
                                    font.family: "微软雅黑";
                                    font.pointSize: 18*  dpi_ratio;
                                    text: qsTr("首页");
                                    color: "#333333";
                                }
                                Timer
                                {
                                    id: firstPageTimer;
                                    interval: 100;
                                    running: false;
                                    repeat: false;
                                    onTriggered:
                                    {
                                        firstPage.color = "#cccccc";
                                        firstPageText.color = "#333333";
                                    }
                                }

                                MouseArea
                                {
                                    anchors.fill: parent;
                                    onClicked:
                                    {
                                        firstPageTimer.start();
                                        firstPage.color = "#ffffff";
                                        firstPageText.color = "#333333";
                                        currentPageText.text = "1";
                                        reqGoodsSaleStatistics(1);
                                    }
                                }
                            }
                            Rectangle
                            {
                                id: lastPage;
                                width: 100*  dpi_ratio;
                                height: 50*  dpi_ratio;
                                color: "#cccccc";
                                radius: 3*  dpi_ratio;
                                anchors.verticalCenter: currentPageText.verticalCenter;
                                anchors.right: currentPageText.left;
                                anchors.rightMargin: 30*  dpi_ratio;
                                Text
                                {
                                    id: lastPageText;
                                    anchors.centerIn: parent;
                                    font.family: "微软雅黑";
                                    font.pointSize: 18*  dpi_ratio;
                                    text: qsTr("上一页");
                                    color: "#333333";
                                }
                                Timer
                                {
                                    id: lastPageTimer;
                                    interval: 100;
                                    running: false;
                                    repeat: false;
                                    onTriggered:
                                    {
                                        lastPage.color = "#cccccc";
                                        lastPageText.color = "#333333";
                                    }
                                }

                                MouseArea
                                {
                                    anchors.fill: parent;
                                    onClicked:
                                    {
                                        lastPageTimer.start();
                                        lastPage.color = "#ffffff";
                                        lastPageText.color = "#333333";


                                        if (currentPageText.text*1 > 1)
                                        {
                                            currentPageText.text = (currentPageText.text*1-1).toFixed(0);
                                        }
                                        reqGoodsSaleStatistics(currentPageText.text*1)
                                    }
                                }
                            }

                            Text
                            {
                                id: currentPageText;
                                anchors.horizontalCenter: parent.horizontalCenter;
                                anchors.bottom: parent.bottom;
                                anchors.bottomMargin: 30*  dpi_ratio;
                                font.family: "微软雅黑";
                                font.pointSize: 18*  dpi_ratio;
                                text: "1";
                                color: "#333333";
                            }

                            Text
                            {
                                id: allPageSizeTextTip;
                                anchors.verticalCenter: currentPageText.verticalCenter;
                                anchors.left: currentPageText.right;
                                font.family: "微软雅黑";
                                font.pointSize: 18*  dpi_ratio;
                                color: "#333333";
                                text: "/"
                            }

                            Text
                            {
                                id: allPageSizeText;
                                anchors.verticalCenter: currentPageText.verticalCenter;
                                anchors.left: allPageSizeTextTip.right;
                                font.family: "微软雅黑";
                                font.pointSize: 18*  dpi_ratio;
                                color: "#333333";
                            }

                            Rectangle
                            {
                                id: nextPage;
                                width: 100*  dpi_ratio;
                                height: 50*  dpi_ratio;
                                color: "#cccccc";
                                radius: 3*  dpi_ratio;
                                anchors.verticalCenter: currentPageText.verticalCenter;
                                anchors.left: allPageSizeText.right;
                                anchors.leftMargin: 20*  dpi_ratio;
                                Text
                                {
                                    id: nextPageText;
                                    anchors.centerIn: parent;
                                    font.family: "微软雅黑";
                                    font.pointSize: 18*  dpi_ratio;
                                    text: qsTr("下一页");
                                    color: "#333333";
                                }

                                Timer
                                {
                                    id: nextPageTimer;
                                    interval: 100;
                                    running: false;
                                    repeat: false;
                                    onTriggered:
                                    {
                                        nextPage.color = "#cccccc";
                                        nextPageText.color = "#333333";
                                    }
                                }

                                MouseArea
                                {
                                    anchors.fill: parent;
                                    onClicked:
                                    {
                                        nextPageTimer.start();
                                        nextPage.color = "#ffffff";
                                        nextPageText.color = "#333333";


                                        if (currentPageText.text*1 < 10000)//TODO这里限制总页数
                                        {
                                            currentPageText.text = (currentPageText.text*1+1).toFixed(0);
                                        }
                                        if (currentPageText.text*1 > allPageSizeText.text*1)
                                        {
                                            currentPageText.text = allPageSizeText.text;
                                        }
                                        reqGoodsSaleStatistics(currentPageText.text*1)
                                    }
                                }
                            }

                            Rectangle
                            {
                                id: goPage;
                                width: 100*  dpi_ratio;
                                height: 50*  dpi_ratio;
                                color: "#cccccc";
                                radius: 3*  dpi_ratio;
                                anchors.verticalCenter: currentPageText.verticalCenter;
                                anchors.left: nextPage.right;
                                anchors.leftMargin: 30*  dpi_ratio;
                                Text
                                {
                                    id: goPageText;
                                    anchors.centerIn: parent;
                                    font.family: "微软雅黑";
                                    font.pointSize: 18*  dpi_ratio;
                                    text: qsTr("跳转");
                                    color: "#333333";
                                }

                                Timer
                                {
                                    id: goPageTimer;
                                    interval: 100;
                                    running: false;
                                    repeat: false;
                                    onTriggered:
                                    {
                                        goPage.color = "#cccccc";
                                        goPageText.color = "#333333";
                                    }
                                }

                                MouseArea
                                {
                                    anchors.fill: parent;
                                    onClicked:
                                    {
                                        goPageTimer.start();
                                        goPage.color = "#ffffff";
                                        goPageText.color = "#333333";
                                        goodsSaleModel.clear();
                                        currentPageText.text = goPageTextContent.text;
                                        reqGoodsSaleStatistics(currentPageText.text*1)

                                    }
                                }
                            }

                            Rectangle
                            {
                                id: goPageTextContentRec;
                                width: 80*  dpi_ratio;
                                height: 50*  dpi_ratio;
                                anchors.left: goPage.right;
                                anchors.leftMargin: 15*  dpi_ratio;
                                anchors.verticalCenter: goPage.verticalCenter;
                                border.color: "#999999";
                                border.width: 1;
                                TextInput
                                {
                                    id: goPageTextContent;
                                    width: 80*  dpi_ratio;
                                    height: 50*  dpi_ratio;
                                    anchors.centerIn: parent;
                                    font.family: "微软雅黑";
                                    font.pointSize: 18*  dpi_ratio;
                                    color: "#999999";
                                    text: "1";
                                    verticalAlignment: Text.AlignVCenter;
                                    horizontalAlignment: Text.AlignHCenter;
                                    validator: IntValidator{
                                        bottom: 1; top:99999
                                    }
                                    onTextChanged:
                                    {
                                        if(goPageTextContent.text == "0")
                                        {
                                            goPageTextContent.text = "";
                                        }
                                        if(goPageTextContent.text == "+")
                                        {
                                            goPageTextContent.text = "";
                                        }
                                    }

                                    MouseArea
                                    {
                                        anchors.fill: parent;
                                        onClicked:
                                        {
                                            goPageTextContent.text = "";
                                            goPageTextContent.forceActiveFocus();
                                            window_root.keyboard_c.openDigitalKeyboard()
                                            digital_keyboard_x: 873 * dpi_ratio
                                            digital_keyboard_y: 374 * dpi_ratio
                                        }
                                    }
                                    onAccepted:
                                    {
                                        currentPageText.text = goPageTextContent.text;

                                    }
                                }
                            }

                            Text
                            {
                                id: goPageTextContentText;
                                anchors.left: goPageTextContentRec.right;
                                anchors.leftMargin: 15*  dpi_ratio;
                                anchors.verticalCenter: goPageTextContentRec.verticalCenter;
                                font.family: "微软雅黑";
                                font.pointSize: 18*  dpi_ratio;
                                text: qsTr("页");
                                color: "#333333";
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            id: right_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 500 *dpi_ratio
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: ST.margin

                CusRect {
                    id: rect_overview
                    Layout.preferredHeight: 370 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    property var _cash: "0"
                    property var _alipay: "0"
                    property var _wechat: "0"
                    property var _bank_card: "0"
                    property var _no_password_payment: "0"
                    property var _membership_card: "0"
                    property var _online_pay: "0"
                    property var _buyhoo_bean: "0"
                    property var _platform_payment: "0"

                    property var _recharge_cash: "0"
                    property var _recharge_alipay: "0"
                    property var _recharge_wechat: "0"

                    ColumnLayout {
                        anchors.fill: parent
                        CusRect {
                            Layout.preferredHeight: 45 * dpi_ratio
                            Layout.fillWidth: true
                            color: ST.color_green

                            CusText {
                                text: qsTr("充值统计")
                                anchors.verticalCenter: parent.verticalCenter
                                x: 14 * dpi_ratio
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.leftMargin: 15 * dpi_ratio
                            Layout.rightMargin: 15 * dpi_ratio
                            Layout.preferredHeight: 75 * dpi_ratio
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                anchors.leftMargin: 20 * dpi_ratio
                                anchors.rightMargin: 20 * dpi_ratio
                                spacing: 20 * dpi_ratio
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true

                                    Image {
                                        source: "/Images/cash_type_image.png"
                                        anchors.fill: parent
                                        fillMode: Image.PreserveAspectFit
                                        anchors.margins: 7 * dpi_ratio
                                    }

                                    CusRect {
                                        anchors.fill: parent
                                        color: ST.color_black
                                        opacity: .4
                                    }
                                    CusText {
                                        anchors.centerIn: parent
                                        text: rect_overview._recharge_cash //充值-现金
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Image {
                                        source: "/Images/alipay_image.png"
                                        anchors.fill: parent
                                        fillMode: Image.PreserveAspectFit
                                        anchors.margins: 7 * dpi_ratio
                                    }
                                    CusRect {
                                        anchors.fill: parent
                                        color: ST.color_black
                                        opacity: .4
                                    }
                                    CusText {
                                        anchors.centerIn: parent
                                        text: rect_overview._recharge_alipay //充值-支付宝
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Image {
                                        source: "/Images/wechat_image.png"
                                        anchors.fill: parent
                                        fillMode: Image.PreserveAspectFit
                                        anchors.margins: 7 * dpi_ratio
                                    }
                                    CusRect {
                                        anchors.fill: parent
                                        color: ST.color_black
                                        opacity: .4
                                    }
                                    CusText {
                                        anchors.centerIn: parent
                                        text: rect_overview._recharge_wechat //充值-微信
                                        color: ST.color_white_pure
                                    }
                                }
                            }
                        }
                        CusRect {
                            Layout.preferredHeight: 45 * dpi_ratio
                            Layout.fillWidth: true
                            color: ST.color_green
                            CusText {
                                text: qsTr("收银统计")
                                anchors.verticalCenter: parent.verticalCenter
                                x: 14 * dpi_ratio
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.leftMargin: 15 * dpi_ratio
                            Layout.rightMargin: 15 * dpi_ratio
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            GridLayout {
                                anchors.fill: parent
                                columns: 2

                                columnSpacing: 5 * dpi_ratio
                                rowSpacing: 5 * dpi_ratio

                                property double col_width: (width - (columns - 1) * rowSpacing) / columns
                                property double col_height: (height - (rows - 1) * columnSpacing) / rows

                                CusText {
                                    text: qsTr("现金: ") + rect_overview._cash
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("支付宝: ") + rect_overview._alipay
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("微信: ") + rect_overview._wechat
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("银行卡: ") + rect_overview._bank_card
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                // CusText {
                                //     text: "免密: " + rect_overview._no_password_payment
                                //     Layout.leftMargin: 0
                                //     Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                //     font.pixelSize: 20 * dpi_ratio
                                //     Layout.fillWidth: true
                                //     Layout.preferredWidth: 20
                                // }
                                CusText {
                                    text: qsTr("储值卡: ") + rect_overview._membership_card
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("线上收入: ") + rect_overview._online_pay
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("百货豆抵: ") + rect_overview._buyhoo_bean
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("免密: ") + rect_overview._platform_payment
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                            }
                        }
                    }
                }

                CusRect {
                    id: goods_list
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    ColumnLayout {
                        anchors.fill: parent
                        //标题栏
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio

                            RowLayout {
                                anchors.fill: parent
                                spacing: 1 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("订单时间")
                                        color: ST.color_black
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 200 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("订单编号")
                                        color: ST.color_black
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("销售金额")
                                        color: ST.color_black
                                    }
                                }
                            }
                        }

                        ListView {
                            id: lv_sale_record_detail
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            clip: true
                            model: ListModel {
                                id: lm_sale_record_detail
                            }

                            delegate: Item {
                                width: lv_sale_record_detail.width
                                height: 50 * dpi_ratio

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 2 * dpi_ratio

                                    //订单时间
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        id: rect_order_time

                                        property string sale_list_date: sale_list_datetime.split(" ")[0]
                                        property string sale_list_time: sale_list_datetime.split(" ")[1]

                                        ColumnLayout {
                                            anchors.fill: parent
                                            anchors.leftMargin: 15 * dpi_ratio
                                            anchors.rightMargin: 15 * dpi_ratio
                                            anchors.topMargin: 5 * dpi_ratio
                                            anchors.bottomMargin: 5 * dpi_ratio
                                            spacing: 0

                                            CusText {
                                                x: 15 * dpi_ratio
                                                width: parent.width - 15 * 2 * dpi_ratio
                                                text: rect_order_time.sale_list_date
                                                color: ST.color_black
                                                elide: Text.ElideRight
                                                font.pixelSize: 16 * dpi_ratio
                                                Layout.alignment: Qt.AlignHCenter
                                            }

                                            CusText {
                                                x: 15 * dpi_ratio
                                                width: parent.width - 15 * 2 * dpi_ratio
                                                text: rect_order_time.sale_list_time
                                                color: ST.color_black
                                                elide: Text.ElideRight
                                                font.pixelSize: 16 * dpi_ratio
                                                Layout.alignment: Qt.AlignHCenter
                                            }
                                        }
                                    }
                                    //订单编号
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 200 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: sale_list_unique
                                            color: ST.color_black
                                            //                                            anchors.verticalCenter: parent.verticalCenter
                                            font.pixelSize: 17 * dpi_ratio
                                        }
                                    }
                                    //销售金额
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            text: sale_list_total
                                            color: ST.color_black
                                            font.pixelSize: 18 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
