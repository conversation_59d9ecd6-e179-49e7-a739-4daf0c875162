﻿import QtQuick 2.12
import QtQuick.Layouts 1.12
import QtQuick.Controls 2.12
import QtQuick.Window 2.12
import QtGraphicalEffects 1.0
import ".."
import EnumTool 1.0

CusRect {
    id: rect_root
    color: ST.color_white

    RowLayout {
        spacing: 0
        anchors.fill: parent
        CusRect {
            color: ST.color_transparent
            Layout.preferredWidth: rect_root.height
            Layout.fillHeight: true
            Image {
                anchors.margins: 15
                anchors.fill: parent
                fillMode: Image.PreserveAspectFit
                source: "/Images/codeInput.png"
            }
        }
        CusRect {
            Layout.preferredWidth: 100
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_transparent
            CusTextField {
                anchors.fill: parent
                placeholderText: qsTr("请输入商品名称/条码")
                border.width: 0

                onAccepted: {
                    
                }
            }
        }
        CusRect {
            color: ST.color_transparent
            Layout.preferredWidth: rect_root.height
            Layout.fillHeight: true
            Image {
                anchors.margins: 13
                anchors.fill: parent
                fillMode: Image.PreserveAspectFit
                source: "/Images/search_image.png"
            }
        }
    }
}
