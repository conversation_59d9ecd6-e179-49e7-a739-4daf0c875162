﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import ".."
import EnumTool 1.0

Item {
    id: popup_root

    property bool is_refund_all: false
    property double jqPayAmounts: 0.00

    function open(is_refund_all_in,jqPayAmount) {
        popup_root.visible = true
        is_refund_all = is_refund_all_in
        ret_actual_amount = (is_refund_all ? sale_list_total : ret_original_amount)
        jqPayAmounts = jqPayAmount
    }

    function close() {
        popup_root.visible = false
        keyboard_c.closeAll()
    }

    function tryConfirm() {
        if (popup_contain_root.str_num == "0" || popup_contain_root.str_num == "") {
            toast.openWarn(qsTr("不可为空"))
            return
        }
        confirm(popup_contain_root.str_num)
        close()
    }

    signal confirm(var str_num)
    signal cancel

    property string title_name: qsTr("退款确认")

    property real ret_actual_amount: 0

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 1000 * dpi_ratio
        height: 700 * dpi_ratio
        x: (parent.width - width) / 2
        y: 120 * dpi_ratio

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35 * dpi_ratio
                color: ST.color_transparent

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 10 * dpi_ratio

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 40 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 130 * dpi_ratio * configTool.fontRatio
                                color: ST.color_transparent
                                CusText {
                                    anchors.verticalCenter: parent.verticalCenter
                                    text: qsTr("收款金额: ")
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio * configTool.fontRatio
                                color: ST.color_transparent

                                CusText {
                                    anchors.verticalCenter: parent.verticalCenter
                                    text: (is_refund_all ? sale_list_total : ret_original_amount)
                                }
                            }

                            CusSpacer {
                                Layout.fillWidth: true
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 40 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 130 * dpi_ratio * configTool.fontRatio
                                color: ST.color_transparent
                                CusText {
                                    anchors.verticalCenter: parent.verticalCenter
                                    text: qsTr("退款金额: ")
                                }
                            }

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio * configTool.fontRatio
                                color: ST.color_transparent

                                CusTextField {
                                    id:refundAmounts
                                    anchors.fill: parent
                                    text: ret_actual_amount
                                    validator: RegularExpressionValidator {
                                        regularExpression: /^\d+(\.\d+)?$/
                                    }
                                    keyboard_status: 1
                                    onTextChanged: {
                                        ret_actual_amount = text
                                        cur_pay_method = EnumTool.PAY_METHOD__UNKNOW
                                        if (Number(ret_actual_amount) > Number(is_refund_all ? sale_list_total : ret_original_amount)) {
                                            ret_actual_amount = Number(is_refund_all ? sale_list_total : ret_original_amount)
                                        }

                                    }
                                }
                            }
                            CusRect {
                                id:refundmethodExp
                                Layout.fillHeight: true
                                Layout.preferredWidth: 130 * dpi_ratio * configTool.fontRatio
                                color: ST.color_transparent
                                visible: (ret_original_amount > jqPayAmounts) &&(!is_refund_all) &&(jqPayAmounts != 0.00)
                                CusText {
                                    anchors.verticalCenter: parent.verticalCenter
                                    font.pixelSize: 18 * configTool.fontRatio * dpi_ratio
                                    color: "red"
                                    text: qsTr(" (初始退款金额高于使用金圈方式支付金额！不支持金圈退款)")
                                }
                            }
                            CusSpacer {
                                Layout.fillWidth: true
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 40 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 130 * dpi_ratio * configTool.fontRatio
                                color: ST.color_transparent
                                CusText {
                                    anchors.verticalCenter: parent.verticalCenter
                                    text: qsTr("收款方式: ")
                                }
                            }

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio * configTool.fontRatio
                                color: ST.color_transparent

                                CusText {
                                    anchors.verticalCenter: parent.verticalCenter
                                    text: original_pay_method_name
                                }
                            }

                            CusSpacer {
                                Layout.fillWidth: true
                            }
                        }
                    }
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 40 * dpi_ratio * configTool.fontRatio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 100 * dpi_ratio * configTool.fontRatio
                                color: ST.color_transparent
                                CusText {
                                    anchors.verticalCenter: parent.verticalCenter
                                    text: qsTr("退款方式: ")
                                }
                            }

                            CusSpacer {
                                Layout.fillWidth: true
                            }
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 75 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            spacing: 21 * dpi_ratio
                            CusRect {
                                id: rect_jinquan
                                Layout.fillHeight: true
                                Layout.preferredWidth: 170 * dpi_ratio
                                Layout.fillWidth: true
                                radius: ST.radius
                                color: ST.color_white_pure
                                enabled: (is_refund_all == true && original_pay_method_name == "金圈平台") || (is_refund_all == false &&ret_original_amount <= jqPayAmounts)

                                property bool is_cur_item: cur_pay_method == EnumTool.PAY_METHOD__JINQUAN

                                CusText {
                                    text: qsTr("金圈退款")
                                    anchors.centerIn: parent
                                    color: parent.is_cur_item ? ST.color_green : ST.color_black
                                }

                                border {
                                    width: is_cur_item ? 2 * dpi_ratio : 1
                                    color: is_cur_item ? ST.color_green : ST.color_grey_border
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        cur_pay_method = EnumTool.PAY_METHOD__JINQUAN
                                    }
                                }
                                visible: (original_pay_method == EnumTool.PAY_METHOD__JINQUAN || original_pay_method_name == "组合支付(在线)")
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 170 * dpi_ratio
                                Layout.fillWidth: true
                                radius: ST.radius
                                color: ST.color_white_pure

                                CusText {
                                    text: qsTr("现金退款")
                                    anchors.centerIn: parent
                                    color: parent.is_cur_item ? ST.color_green : ST.color_black
                                }
                                property bool is_cur_item: cur_pay_method == EnumTool.PAY_METHOD__CASH
                                border {
                                    width: is_cur_item ? 2 * dpi_ratio : 1
                                    color: is_cur_item ? ST.color_green : ST.color_grey_border
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        cur_pay_method = EnumTool.PAY_METHOD__CASH
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 170 * dpi_ratio
                                Layout.fillWidth: true
                                radius: ST.radius
                                color: ST.color_white_pure
                                visible: is_enabled
                                CusText {
                                    text: qsTr("微信退款")
                                    anchors.centerIn: parent
                                    color: parent.is_cur_item ? ST.color_green : ST.color_black
                                }
                                property bool is_cur_item: cur_pay_method == EnumTool.PAY_METHOD_WECHAT
                                property bool is_enabled: true
                                border {
                                    width: is_cur_item ? 2 * dpi_ratio : 1
                                    color: is_cur_item ? ST.color_green : ST.color_grey_border
                                }
                                function refreshStatus() {
                                    var ret = payMethodControl.getPayMethodStatus(EnumTool.PAY_METHOD_WECHAT)
                                    is_enabled = ret
                                }
                                Component.onCompleted: {
                                    refreshStatus()
                                }
                                onVisibleChanged: {
                                    if (visible)
                                        refreshStatus()
                                }
                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        cur_pay_method = EnumTool.PAY_METHOD_WECHAT
                                    }
                                }
                            }

                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 170 * dpi_ratio
                                Layout.fillWidth: true
                                visible: is_enabled
                                radius: ST.radius
                                color: ST.color_white_pure
                                property bool is_cur_item: cur_pay_method == EnumTool.PAY_METHOD_ALIPAY
                                property bool is_enabled: true
                                CusText {
                                    text: qsTr("支付宝退款")
                                    anchors.centerIn: parent
                                    color: parent.is_cur_item ? ST.color_green : ST.color_black
                                }
                                border {
                                    width: is_cur_item ? 2 * dpi_ratio : 1
                                    color: is_cur_item ? ST.color_green : ST.color_grey_border
                                }
                                function refreshStatus() {
                                    var ret = payMethodControl.getPayMethodStatus(EnumTool.PAY_METHOD_ALIPAY)
                                    is_enabled = ret
                                }
                                Component.onCompleted: {
                                    refreshStatus()
                                }
                                onVisibleChanged: {
                                    if (visible)
                                        refreshStatus()
                                }
                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        cur_pay_method = EnumTool.PAY_METHOD_ALIPAY
                                    }
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 170 * dpi_ratio
                                Layout.fillWidth: true
                                radius: ST.radius
                                property bool is_cur_item: cur_pay_method == EnumTool.PAY_METHOD__VIPCARD
                                color: ST.color_white_pure
                                CusText {
                                    text: qsTr("储值卡退款")
                                    anchors.centerIn: parent
                                    color: parent.is_cur_item ? ST.color_green : ST.color_black
                                }
                                border {
                                    width: is_cur_item ? 2 * dpi_ratio : 1
                                    color: is_cur_item ? ST.color_green : ST.color_grey_border
                                }
                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        cur_pay_method = EnumTool.PAY_METHOD__VIPCARD
                                    }
                                }
                                visible: original_pay_method == EnumTool.PAY_METHOD__VIPCARD
                            }
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 100 * dpi_ratio
                        color: ST.color_transparent

                        CusText {
                            text: ret_method_tips
                            anchors.verticalCenter: parent.verticalCenter
                            width: parent.width
                            wrapMode: Text.WrapAnywhere
                        }
                    }

                    CusSpacer {
                        Layout.fillHeight: true
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 80 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent

                            CusSpacer {
                                Layout.fillWidth: true
                            }

                            CusButton {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 360 * dpi_ratio
                                text: qsTr("取消")
                                onClicked: {
                                    close()
                                }
                            }

                            CusSpacer {
                                Layout.fillWidth: true
                            }

                            CusButton {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 360 * dpi_ratio
                                text: qsTr("确认退货")
                                onClicked: {
                                    if (!checkPayMethod()) {
                                        toast.openWarn(qsTr("请选择退货方式"))
                                        return
                                    }
                                    reqRetGoods(ret_actual_amount, is_refund_all)
                                    close()
                                }
                            }

                            CusSpacer {
                                Layout.fillWidth: true
                            }
                        }
                    }
                }
            }
        }
    }
}
