﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import ".."
import EnumTool 1.0
import SortFilterProxyModel 0.2

Item {

    onVisibleChanged: {
        if (visible) {
            resetAllInfo()
            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_QUERY__STOCK_CHANGE_LIST)) {
                reqStockRecord()
            } else {
                toast.openWarn("无此权限")
            }
        }
    }

    function resetAllInfo() {
        lm_stock_record.clear()
        overview._in_stock_money = "0"
        overview._out_of_stock_money = "0"
    }

    function reqStockRecord() {
        orderControl.getStockRecord4Qml(function (data) {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            lm_stock_record.clear()

            if (json_doc.object == null) {
                overview._in_stock_money = "0"
                overview._out_of_stock_money = "0"
            } else {
                overview._in_stock_money = json_doc.object.entryTotal
                overview._out_of_stock_money = json_doc.object.outTotal
            }

            for (var i = 0; i < json_doc_data.length; ++i) {
                lm_stock_record.append(json_doc_data[i])
            }

            lv_stock_record.currentIndex = -1
        }, btn_calendar_begin.text, btn_calendar_end.text)
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: ST.margin

        CusRect {
            id: overview
            Layout.preferredHeight: 60 * dpi_ratio
            Layout.fillWidth: true
            color: ST.color_white_pure
            property var _in_stock_money: "0"
            property var _out_of_stock_money: "0"
            radius: ST.radius

            CusText {
                anchors.centerIn: parent
                text: qsTr("入库总钱数: ") + Number(overview._in_stock_money).toFixed(2).toString() + "　　" + qsTr("出库总钱数: ") + Number(overview._out_of_stock_money).toFixed(2).toString()
            }
        }

        CusRect {
            Layout.fillWidth: true
            Layout.preferredHeight: 60 * dpi_ratio
            color: ST.color_white_pure
            radius: ST.radius

            RowLayout {
                anchors.fill: parent
                anchors.topMargin: 6 * dpi_ratio
                anchors.bottomMargin: 6 * dpi_ratio

                CusSpacer {
                    Layout.fillWidth: true
                }

                CusTextField {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 300 * dpi_ratio * configTool.fontRatio
                    keyboard_status: 1
                    placeholderText: qsTr("请输入条码/名称")
                    onTextChanged: {
                        order_list.filter_barcode = text
                    }
                    normal_keyboard_x: 670 * dpi_ratio
                    normal_keyboard_y: 560 * dpi_ratio

                    digital_keyboard_x: 1135 * dpi_ratio
                    digital_keyboard_y: 320 * dpi_ratio
                }

                CusRadioBtnRectManual {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 110 * dpi_ratio * configTool.fontRatio
                    text: qsTr("入库")
                    control_mark: qsTr("入库")
                    checked: order_list.filter_stock_type == control_mark
                    onClicked: {
                        order_list.filter_stock_type = control_mark
                    }
                }
                CusRadioBtnRectManual {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 110 * dpi_ratio * configTool.fontRatio
                    text: qsTr("出库")
                    control_mark: qsTr("出库")
                    checked: order_list.filter_stock_type == control_mark
                    onClicked: {
                        order_list.filter_stock_type = control_mark
                    }
                }
                CusRadioBtnRectManual {
                    Layout.fillHeight: true
                    Layout.preferredWidth: 110 * dpi_ratio * configTool.fontRatio
                    text: qsTr("全部")
                    control_mark: ""
                    checked: order_list.filter_stock_type == control_mark
                    onClicked: {
                        order_list.filter_stock_type = control_mark
                    }
                }
                CusSpacer {
                    Layout.preferredWidth: 25 * dpi_ratio * configTool.fontRatio
                }
            }
        }

        CusRect {
            id: order_list
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ST.color_white_pure

            property string filter_stock_type: ""
            property string filter_barcode: ""

            ColumnLayout {
                anchors.fill: parent

                //标题栏
                Item {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 50 * dpi_ratio

                    RowLayout {
                        anchors.fill: parent
                        spacing: 1 * dpi_ratio

                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 250 * dpi_ratio
                            clip: true
                            color: ST.color_green
                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("商品名称")
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 200 * dpi_ratio
                            clip: true
                            color: ST.color_green
                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("商品条码")
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 220 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("操作时间")
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 110 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("操作前数量")
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 110 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("修改数量")
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 110 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("操作后数量")
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 130 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("操作源")
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 110 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("出入库类型")
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 110 * dpi_ratio
                            clip: true
                            color: ST.color_green

                            CusText {
                                anchors.centerIn: parent
                                text: qsTr("操作方式")
                                color: ST.color_white_pure
                            }
                        }
                    }
                }

                ListModel {
                    id: lm_stock_record
                }

                ListView {
                    id: lv_stock_record
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    clip: true
                    highlightFollowsCurrentItem: true
                    highlightMoveDuration: 0
                    focus: true
                    currentIndex: -1

                    model: SortFilterProxyModel {
                        id: lm_goods_kind_detail_filter
                        sourceModel: lm_stock_record
                        filters: [
                            RegExpFilter {
                                roleName: "stockType"
                                pattern: order_list.filter_stock_type
                                caseSensitivity: Qt.CaseInsensitive
                                enabled: order_list.filter_stock_type != ""
                            },
                            AnyOf {
                                enabled: order_list.filter_barcode != ""
                                RegExpFilter {
                                    roleName: "goodsBarcode"
                                    pattern: order_list.filter_barcode
                                    caseSensitivity: Qt.CaseInsensitive
                                }
                                RegExpFilter {
                                    roleName: "goodsName"
                                    pattern: order_list.filter_barcode
                                    caseSensitivity: Qt.CaseInsensitive
                                }
                            }
                        ]
                    }

                    delegate: CusRect {
                        color: ST.color_white_pure
                        width: lv_stock_record.width
                        height: 50 * dpi_ratio

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                lv_stock_record.currentIndex = index
                            }
                        }

                        RowLayout {
                            anchors.fill: parent
                            spacing: 2 * dpi_ratio

                            //商品名称
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 250 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    x: 15 * dpi_ratio
                                    width: parent.width - 15 * 2 * dpi_ratio
                                    text: goodsName
                                    color: ST.color_black
                                    elide: Text.ElideRight
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }
                            //商品条码
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 200 * dpi_ratio
                                clip: true
                                color: ST.color_transparent
                                CusText {
                                    anchors.centerIn: parent
                                    text: goodsBarcode
                                    color: ST.color_black
                                }
                            }
                            //操作时间
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 220 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: stockTime
                                    color: ST.color_black
                                }
                            }
                            //操作前数量
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: originalCount
                                    color: ST.color_black
                                }
                            }
                            //修改数量
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: goodsCount
                                    color: ST.color_black
                                }
                            }
                            //操作后数量
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: stockCount
                                    color: ST.color_black
                                }
                            }
                            //操作源
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 130 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: stockOrigin
                                    color: ST.color_black
                                }
                            }
                            //出入库类型
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: stockType
                                    color: ST.color_black
                                }
                            }
                            //操作方式
                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                Layout.preferredWidth: 110 * dpi_ratio
                                clip: true
                                color: ST.color_transparent

                                CusText {
                                    anchors.centerIn: parent
                                    text: stockSource
                                    color: ST.color_black
                                }
                            }
                        }
                    }

                    highlight: CusRect {
                        width: lv_stock_record.width
                        height: 50
                        color: ST.color_orange
                        opacity: .4
                        z: 2
                    }
                }
            }
        }
    }
}
