﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import ".."
import EnumTool 1.0

CusRect {

    onVisibleChanged: {
        if (visible) {
            resetAllInfo()
            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_QUERY__HAND_OVER_LIST))
                reqHanoverRecord()
            else
                toast.openWarn("无此权限")
        }
    }

    function resetAllInfo() {
        resetOverview()
        lm_sale_record_detail.clear()
    }

    function reqHanoverRecord() {
        orderControl.getHandoverRecord4Qml(function (data) {
            var json_doc = JSON.parse(data)

            var json_doc_data = json_doc.data
            lm_handover_record.clear()
            for (var i = 0; i < json_doc_data.length; ++i) {
                lm_handover_record.append(json_doc_data[i])
            }
            lm_handover_record.currentIndex = -1
        }, btn_calendar_begin.text, btn_calendar_end.text)
    }

    function resetOverview() {
        rect_overview._cash = "0"
        rect_overview._alipay = "0"
        rect_overview._wechat = "0"
        rect_overview._bank_card = "0"
        rect_overview._no_password_payment = "0"
        rect_overview._membership_card = "0"
        rect_overview._online_pay = "0"
        rect_overview._buyhoo_bean = "0"
        rect_overview._platform_payment = "0"

        rect_overview._recharge_cash = "0"
        rect_overview._recharge_alipay = "0"
        rect_overview._recharge_wechat = "0"
    }

    function reqHanoverRecordDetail(staff_id, start_time, end_time) {
        end_time = end_time == "---" ? "" : end_time

        lm_sale_record_detail.clear()
        orderControl.getHandoverRecordDetail4Qml(function (data) {
            var json_doc = JSON.parse(data)
            var json_doc_data = json_doc.data
            var json_doc_data1 = json_doc.data1
            var json_doc_address = json_doc.address
            for (var i = 0; i < json_doc_data.length; ++i) {
                lm_sale_record_detail.append(json_doc_data[i])
            }
            resetOverview()
            for (var i = 0; i < json_doc_data1.length; ++i) {

                var cur_json_data = json_doc_data1[i]
                logMgr.logDataInfo4Qml("cur_json_data.pay_ment[{}]:{}",i,cur_json_data.pay_ment)
                if (cur_json_data.pay_ment == "现金") {
                    rect_overview._cash = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "支付宝") {
                    rect_overview._alipay = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "储值卡") {
                    rect_overview._membership_card = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "百货豆抵扣") {
                    rect_overview._buyhoo_bean = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "微信") {
                    rect_overview._wechat = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "银行卡") {
                    rect_overview._bank_card = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "免密支付") {
                    rect_overview._no_password_payment = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "线上收入") {
                    rect_overview._online_pay = cur_json_data.payment_total
                } else if (cur_json_data.pay_ment == "金圈平台") {
                    rect_overview._platform_payment = cur_json_data.payment_total
                }
            }

            for (var i = 0; i < json_doc_address.length; ++i) {
                var cur_json_data = json_doc_address[i]
                if (cur_json_data.rechargeMethod == "现金") {
                    rect_overview._recharge_cash = cur_json_data.rechargeMoney
                } else if (cur_json_data.rechargeMethod == "微信") {
                    rect_overview._recharge_wechat = cur_json_data.rechargeMoney
                } else if (cur_json_data.rechargeMethod == "支付宝") {
                    rect_overview._recharge_alipay = cur_json_data.rechargeMoney
                }
            }
        }, staff_id, start_time, end_time)
    }

    RowLayout {
        anchors.fill: parent
        spacing: ST.margin

        CusRect {
            id: left_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 1400
            color: ST.color_white_pure
            radius: ST.radius

            ColumnLayout {
                anchors.fill: parent
                spacing: ST.margin

                CusRect {
                    id: order_list
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure

                    ColumnLayout {
                        anchors.fill: parent

                        //标题栏
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio
                            RowLayout {
                                anchors.fill: parent
                                spacing: 1 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 300 * dpi_ratio
                                    clip: true
                                    color: ST.color_green
                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("接班时间")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 300 * dpi_ratio
                                    clip: true
                                    color: ST.color_green
                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("交班时间")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 220 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("员工编号")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 220 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("员工名称")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 220 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("订单数量")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 220 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("营业额")
                                        color: ST.color_white_pure
                                    }
                                }
                            }
                        }

                        ListView {
                            id: listview_sale_record
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            clip: true
                            highlightFollowsCurrentItem: true
                            highlightMoveDuration: 0
                            focus: true
                            currentIndex: -1
                            model: ListModel {
                                id: lm_handover_record
                            }

                            delegate: CusRect {
                                color: ST.color_white_pure

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        listview_sale_record.currentIndex = index
                                    }
                                }

                                // 单行内容
                                height: 50 * dpi_ratio
                                width: listview_sale_record.width

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 2 * dpi_ratio

                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 300 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            text: loginDatetime // "接班时间"
                                            color: ST.color_black
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 300 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            text: signOutDatetime // "交班时间"
                                            color: ST.color_black
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 220 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: staffId //"员工编号"
                                            color: ST.color_black
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 220 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: staffName // "员工名称"
                                            color: ST.color_black
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 220 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: saleCount //"订单数量"
                                            color: ST.color_black
                                        }
                                    }
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 220 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: saleTotal //"营业额"
                                            color: ST.color_black
                                        }
                                    }
                                }
                            }

                            highlight: CusRect {
                                width: listview_sale_record.width
                                height: 50 * dpi_ratio
                                color: ST.color_orange
                                opacity: .4
                                z: 2
                            }

                            onCurrentIndexChanged: {
                                if (currentIndex < 0)
                                    return
                                var cur_model = lm_handover_record.get(currentIndex)
                                reqHanoverRecordDetail(cur_model.staffId, cur_model.loginDatetime, cur_model.signOutDatetime)
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            id: right_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 500
            color: ST.color_transparent

            ColumnLayout {
                anchors.fill: parent
                spacing: ST.margin

                CusRect {
                    id: rect_overview
                    Layout.preferredHeight: 370 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    property var _cash: "0"
                    property var _alipay: "0"
                    property var _wechat: "0"
                    property var _bank_card: "0"
                    property var _no_password_payment: "0"
                    property var _membership_card: "0"
                    property var _online_pay: "0"
                    property var _buyhoo_bean: "0"
                    property var _platform_payment: "0"

                    property var _recharge_cash: "0"
                    property var _recharge_alipay: "0"
                    property var _recharge_wechat: "0"

                    ColumnLayout {
                        anchors.fill: parent
                        CusRect {
                            Layout.preferredHeight: 45 * dpi_ratio
                            Layout.fillWidth: true
                            color: ST.color_green

                            CusText {
                                text: qsTr("充值统计")
                                anchors.verticalCenter: parent.verticalCenter
                                x: 14 * dpi_ratio
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.leftMargin: 15 * dpi_ratio
                            Layout.rightMargin: 15 * dpi_ratio
                            Layout.preferredHeight: 75 * dpi_ratio
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                anchors.leftMargin: 20 * dpi_ratio
                                anchors.rightMargin: 20 * dpi_ratio
                                spacing: 20 * dpi_ratio
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true

                                    Image {
                                        source: "/Images/cash_type_image.png"
                                        anchors.fill: parent
                                        fillMode: Image.PreserveAspectFit
                                        anchors.margins: 7 * dpi_ratio
                                    }

                                    CusRect {
                                        anchors.fill: parent
                                        color: ST.color_black
                                        opacity: .4
                                    }
                                    CusText {
                                        anchors.centerIn: parent
                                        text: rect_overview._recharge_cash //充值-现金
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Image {
                                        source: "/Images/alipay_image.png"
                                        anchors.fill: parent
                                        fillMode: Image.PreserveAspectFit
                                        anchors.margins: 7 * dpi_ratio
                                    }
                                    CusRect {
                                        anchors.fill: parent
                                        color: ST.color_black
                                        opacity: .4
                                    }
                                    CusText {
                                        anchors.centerIn: parent
                                        text: rect_overview._recharge_alipay //充值-支付宝
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Image {
                                        source: "/Images/wechat_image.png"
                                        anchors.fill: parent
                                        fillMode: Image.PreserveAspectFit
                                        anchors.margins: 7 * dpi_ratio
                                    }
                                    CusRect {
                                        anchors.fill: parent
                                        color: ST.color_black
                                        opacity: .4
                                    }
                                    CusText {
                                        anchors.centerIn: parent
                                        text: rect_overview._recharge_wechat //充值-微信
                                        color: ST.color_white_pure
                                    }
                                }
                            }
                        }
                        CusRect {
                            Layout.preferredHeight: 45 * dpi_ratio
                            Layout.fillWidth: true
                            color: ST.color_green
                            CusText {
                                text: qsTr("收银统计")
                                anchors.verticalCenter: parent.verticalCenter
                                x: 14 * dpi_ratio
                                color: ST.color_white_pure
                            }
                        }
                        CusRect {
                            Layout.leftMargin: 15 * dpi_ratio
                            Layout.rightMargin: 15 * dpi_ratio
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            color: ST.color_transparent

                            GridLayout {
                                anchors.fill: parent
                                columns: 2

                                columnSpacing: 5 * dpi_ratio
                                rowSpacing: 5 * dpi_ratio

                                property double col_width: (width - (columns - 1) * rowSpacing) / columns
                                property double col_height: (height - (rows - 1) * columnSpacing) / rows

                                CusText {
                                    text: qsTr("现金: ") + rect_overview._cash
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("支付宝: ") + rect_overview._alipay
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("微信: ") + rect_overview._wechat
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("银行卡: ") + rect_overview._bank_card
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                // CusText {
                                //     text: "免密: " + rect_overview._no_password_payment
                                //     Layout.leftMargin: 0
                                //     Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                //     font.pixelSize: 20 * dpi_ratio
                                //     Layout.fillWidth: true
                                //     Layout.preferredWidth: 20
                                // }
                                CusText {
                                    text: qsTr("储值卡: ") + rect_overview._membership_card
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("线上收入: ") + rect_overview._online_pay
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("百货豆抵: ") + rect_overview._buyhoo_bean
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                                CusText {
                                    text: qsTr("免密: ") + rect_overview._platform_payment
                                    Layout.leftMargin: 0
                                    Layout.alignment: Qt.AlignTop | Qt.AlignLeft
                                    font.pixelSize: 20 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 20
                                }
                            }
                        }
                    }
                }

                CusRect {
                    id: goods_list
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    ColumnLayout {
                        anchors.fill: parent
                        //标题栏
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio

                            RowLayout {
                                anchors.fill: parent
                                spacing: 1 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("订单时间")
                                        color: ST.color_black
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 200 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("订单编号")
                                        color: ST.color_black
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("销售金额")
                                        color: ST.color_black
                                    }
                                }
                            }
                        }

                        ListView {
                            id: lv_sale_record_detail
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            clip: true
                            model: ListModel {
                                id: lm_sale_record_detail
                            }

                            delegate: Item {
                                width: lv_sale_record_detail.width
                                height: 50 * dpi_ratio

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 2 * dpi_ratio

                                    //订单时间
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        id: rect_order_time

                                        property string sale_list_date: sale_list_datetime.split(" ")[0]
                                        property string sale_list_time: sale_list_datetime.split(" ")[1]

                                        ColumnLayout {
                                            anchors.fill: parent
                                            anchors.leftMargin: 15 * dpi_ratio
                                            anchors.rightMargin: 15 * dpi_ratio
                                            anchors.topMargin: 5 * dpi_ratio
                                            anchors.bottomMargin: 5 * dpi_ratio
                                            spacing: 0

                                            CusText {
                                                x: 15 * dpi_ratio
                                                width: parent.width - 15 * 2 * dpi_ratio
                                                text: rect_order_time.sale_list_date
                                                color: ST.color_black
                                                elide: Text.ElideRight
                                                font.pixelSize: 16 * dpi_ratio
                                                Layout.alignment: Qt.AlignHCenter
                                            }

                                            CusText {
                                                x: 15 * dpi_ratio
                                                width: parent.width - 15 * 2 * dpi_ratio
                                                text: rect_order_time.sale_list_time
                                                color: ST.color_black
                                                elide: Text.ElideRight
                                                font.pixelSize: 16 * dpi_ratio
                                                Layout.alignment: Qt.AlignHCenter
                                            }
                                        }
                                    }
                                    //订单编号
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 200 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: sale_list_unique
                                            color: ST.color_black
                                            //                                            anchors.verticalCenter: parent.verticalCenter
                                            font.pixelSize: 17 * dpi_ratio
                                        }
                                    }
                                    //销售金额
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            text: sale_list_total
                                            color: ST.color_black
                                            //                                            anchors.verticalCenter: parent.verticalCenter
                                            font.pixelSize: 18 * dpi_ratio
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
