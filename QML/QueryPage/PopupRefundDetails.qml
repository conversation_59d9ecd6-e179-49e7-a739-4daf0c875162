﻿import QtQuick 2.15;
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtGraphicalEffects 1.15
import ".."
import EnumTool 1.0

Item {
    id: popup_root

    property int original_pay_method: EnumTool.PAY_METHOD__UNKNOW
    onOriginal_pay_methodChanged: {
        cur_pay_method = original_pay_method
    }
    property string original_pay_method_name: ""

    property real sale_list_total
    property double jqPayAmount:0.00

    property int cur_pay_method: EnumTool.PAY_METHOD__UNKNOW
    onCur_pay_methodChanged: {
        switch (cur_pay_method) {
        case EnumTool.PAY_METHOD__UNKNOW:
            ret_method_tips = ""
            break
        case EnumTool.PAY_METHOD__CASH:
            ret_method_tips = qsTr("使用现金的方式退款给客户")
            break
        case EnumTool.PAY_METHOD__ALIPAY_OFFLINE:
            ret_method_tips = qsTr("扫描客户支付宝收款码，付款给客户退款金额")
            break
        case EnumTool.PAY_METHOD__WECHAT_OFFLINE:
            ret_method_tips = qsTr("扫描客户微信收款码，付款给客户退款金额")
            break
        case EnumTool.PAY_METHOD__BANK_CARD:
            ret_method_tips = ""
            break
        case EnumTool.PAY_METHOD__VIPCARD:
            ret_method_tips = qsTr("退款至客户储值卡")
            break
        case EnumTool.PAY_METHOD__JINQUAN:
            ret_method_tips = qsTr("收款方式为使用扫码枪扫描客户微信、支付宝、小程序付款码收款，资金将原路退回")
            break
        }
    }
    property string ret_method_tips: ""

    property var sale_list_unique

    property real ret_original_amount: 0

    property bool is_refunded: false

    onVisibleChanged: {
        resetPayMethod()
    }

    function resetPayMethod() {
        original_pay_method = EnumTool.PAY_METHOD__UNKNOW
        cur_pay_method = EnumTool.PAY_METHOD__UNKNOW
    }

    function showSaleOrderRefundPopup(is_refund_all = false,jqPayAmount) {
        window_root.loader_4_sale_order_refund.sourceComponent = compo_sale_order_refund
        window_root.loader_4_sale_order_refund.item.open(is_refund_all,jqPayAmount)
    }

    function checkPayMethod() {
        return cur_pay_method != EnumTool.PAY_METHOD__UNKNOW && cur_pay_method != EnumTool.PAY_METHOD__COMBINED
    }

    function open(goods_list_model) {
        popup_root.visible = true

        keyboard_c.closeAll()

        var json_doc = JSON.parse(goods_list_model)
        var json_doc_data = json_doc.data
        sale_list_unique = json_doc_data.sale_list_unique
        var json_doc_data_listDetail = json_doc_data.listDetail
        var json_doc_data_payDetail = json_doc_data.payDetail
        //获取金圈支付金额
        for (var i = 0; i < json_doc_data_payDetail.length; ++i) {
            var cur_pay_detail = json_doc_data_payDetail[i]
            if(cur_pay_detail.payMethod === "金圈平台"){
              jqPayAmount  = cur_pay_detail.payMoney
            }
        }

        var refunded_num = 0
        for (var i = 0; i < json_doc_data_listDetail.length; ++i) {
            var cur_list_detail = json_doc_data_listDetail[i]

            cur_list_detail["ret_subtotal"] = cur_list_detail.retCount * cur_list_detail.sale_list_detail_price

            refunded_num += cur_list_detail.retCount

            cur_list_detail["ret_count"] = 0
            lm_sale_record.append(cur_list_detail)
        }

        sale_list_total = json_doc_data.sale_list_total

        if (refunded_num > 0) {
            is_refunded = true
            refreshRetAmount()
        } else {
            is_refunded = false
        }
        original_pay_method_name = ""
        var json_doc_data_paydetail = json_doc_data.payDetail
        if (json_doc_data_paydetail.length == 1) {
            var cur_pay_method = json_doc_data_paydetail[0].payMethod

            original_pay_method_name = cur_pay_method

            if (cur_pay_method == qsTr("现金")) {
                original_pay_method = EnumTool.PAY_METHOD__CASH
            } else if (cur_pay_method == qsTr("支付宝")) {
                original_pay_method = EnumTool.PAY_METHOD__ALIPAY_OFFLINE
            } else if (cur_pay_method == qsTr("微信")) {
                original_pay_method = EnumTool.PAY_METHOD__WECHAT_OFFLINE
            } else if (cur_pay_method == qsTr("银行卡")) {
                original_pay_method = EnumTool.PAY_METHOD__BANK_CARD
            } else if (cur_pay_method == qsTr("储值卡")) {
                original_pay_method = EnumTool.PAY_METHOD__VIPCARD
            } else if (cur_pay_method == qsTr("金圈平台")) {
                original_pay_method = EnumTool.PAY_METHOD__JINQUAN
            } else {
                // 未知支付方式
                original_pay_method = EnumTool.PAY_METHOD__UNKNOW
            }
        } else {
            original_pay_method_name = qsTr("组合支付")
            // 组合支付
            //            original_pay_method = EnumTool.PAY_METHOD__COMBINED
            original_pay_method = EnumTool.PAY_METHOD__COMBINED

            for (var i = 0; i < json_doc_data_paydetail.length; ++i) {
                var cur_pay_method = json_doc_data_paydetail[i].payMethod

                if (cur_pay_method == qsTr("金圈平台")) {
                    original_pay_method_name = qsTr("组合支付(在线)")
                    break
                }
                if (cur_pay_method == qsTr("储值卡")) {
                    original_pay_method = EnumTool.PAY_METHOD__VIPCARD
                    break
                }
            }
        }
    }

    function close() {
        popup_root.visible = false
        lm_sale_record.clear()
        keyboard_c.closeAll()
    }

    function refreshRetAmount() {
        var result = 0
        for (var i = 0; i < lm_sale_record.count; ++i) {
            var cur_item = lm_sale_record.get(i)
            result += Number(cur_item.ret_subtotal)
        }
        ret_original_amount = result.toFixed(2)
    }

    function reqRetGoods(ret_actual_amount, is_ret_all = false) {
        var goodsMessage = ""
        if (is_ret_all) {
            for (var i = 0; i < lm_sale_record.count; ++i) {
                goodsMessage += lm_sale_record.get(i).sale_list_detail_id
                goodsMessage += ":"
                goodsMessage += lm_sale_record.get(i).sale_list_detail_count
                goodsMessage += ":"
                goodsMessage += lm_sale_record.get(i).sale_list_detail_price
                goodsMessage += ";"
            }
        } else {
            for (var i = 0; i < lm_sale_record.count; ++i) {
                if (lm_sale_record.get(i).ret_count <= 0)
                    continue

                var tmp_goods_info = ""
                tmp_goods_info += lm_sale_record.get(i).sale_list_detail_id
                tmp_goods_info += ":"
                tmp_goods_info += lm_sale_record.get(i).ret_count
                tmp_goods_info += ":"
                tmp_goods_info += lm_sale_record.get(i).sale_list_detail_price
                tmp_goods_info += ";"
                goodsMessage += tmp_goods_info
            }
        }

        var data_tmp = {
            "goodsMessage": goodsMessage,
            "retListRemarks": "",
            "retListTotalMoney": ret_actual_amount,
            "saleListUnique": sale_list_unique,
            "retMoneyType": cur_pay_method
        }

        orderControl.reqRefundOrder(function (is_succ, data) {
            var json_doc = JSON.parse(data)
            if (is_succ) {
                if(json_doc.msg == "已提交退款，请勿重复提交"){
                    toast.openInfo(json_doc.msg)
                }else if(json_doc.msg == "退款处理中！"){
                    toast.openInfo(json_doc.msg)
                }
                else{
                    toast.openInfo(qsTr("退货成功!"))
                    reqSaleRecord()
                }
            } else {
                // toast.openError(json_doc.msg)
                toast.openError(qsTr("退款失败"))
            }
        }, JSON.stringify(data_tmp))
        close()
    }

    property string title_name: qsTr("退款详情")

    Rectangle {
        anchors.fill: parent
        color: ST.color_black
        opacity: .5
        MouseArea {
            anchors.fill: parent
            onClicked: {
                close()
            }
        }
    }

    Rectangle {
        id: popup_contain_root
        width: 1600 * dpi_ratio
        height: 850 * dpi_ratio
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2

        color: ST.color_white
        layer.enabled: true
        layer.effect: OpacityMask {
            maskSource: Rectangle {
                width: popup_contain_root.width
                height: popup_contain_root.height
                radius: ST.radius
            }
        }

        CusMoveArea {
            anchors.fill: parent
            control: popup_contain_root
            onMove: {
                popup_contain_root.x += xOffset
                popup_contain_root.y += yOffset
            }
        }

        ColumnLayout {
            anchors.fill: parent
            spacing: 0

            //标题栏
            Rectangle {
                Layout.preferredHeight: 65 * dpi_ratio
                Layout.fillWidth: true
                Image {
                    anchors.fill: parent
                    source: "/Images/shade2.png"
                }
                RowLayout {
                    anchors.fill: parent
                    spacing: 0

                    Rectangle {
                        color: ST.color_transparent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        CusText {
                            text: title_name
                            font.pixelSize: 28 * dpi_ratio
                            font.bold: true
                            color: ST.color_font
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            CusRect {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 35 * dpi_ratio
                color: ST.color_transparent

                MouseArea {
                    anchors.fill: parent
                }

                ColumnLayout {
                    anchors.fill: parent

                    //标题栏
                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 50 * dpi_ratio

                        RowLayout {
                            anchors.fill: parent
                            spacing: 2 * dpi_ratio

                            CusRect {
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                clip: true
                                color: ST.color_green
                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("商品名称")
                                    color: ST.color_white_pure
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                clip: true
                                color: ST.color_green
                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("商品条码")
                                    color: ST.color_white_pure
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                clip: true
                                color: ST.color_green

                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("商品单价")
                                    color: ST.color_white_pure
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                clip: true
                                color: ST.color_green

                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("商品数量")
                                    color: ST.color_white_pure
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                clip: true
                                color: ST.color_green

                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("商品总价")
                                    color: ST.color_white_pure
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                clip: true
                                color: ST.color_green

                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("已退数量")
                                    color: ST.color_white_pure
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                clip: true
                                color: ST.color_green

                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("退货数量")
                                    color: ST.color_white_pure
                                }
                            }
                            CusRect {
                                Layout.fillHeight: true
                                Layout.preferredWidth: 180 * dpi_ratio
                                clip: true
                                color: ST.color_green

                                CusText {
                                    anchors.centerIn: parent
                                    text: qsTr("退货小计")
                                    color: ST.color_white_pure
                                }
                            }
                        }
                    }

                    ListView {
                        id: listview_sale_record
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        clip: true
                        highlightFollowsCurrentItem: true
                        highlightMoveDuration: 0
                        focus: true
                        currentIndex: -1
                        spacing: 2 * dpi_ratio
                        //                        model: 10
                        model: ListModel {
                            id: lm_sale_record
                        }
                        delegate: CusRect {
                            width: listview_sale_record.width
                            height: 50 * dpi_ratio
                            color: ST.color_white_pure
                            RowLayout {
                                anchors.fill: parent
                                spacing: 2 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    clip: true
                                    color: ST.color_transparent
                                    CusText {
                                        anchors.centerIn: parent
                                        width: parent.width
                                        text: goods_name //"商品名称"
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent
                                    CusText {
                                        anchors.centerIn: parent
                                        text: goods_barcode //"商品条码"
                                        width: parent.width
                                        elide: Text.ElideLeft
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: sale_list_detail_price // "商品单价"
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: sale_list_detail_count //"商品数量"
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: sub_total //"商品总价"
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: retCount //"已退数量"
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusTextField {
                                        anchors.fill: parent
                                        text: ret_count
                                        keyboard_status: 1
                                        onTextChanged: {
                                            if (Number(sale_list_detail_count) < Number(text)) {
                                                text = sale_list_detail_count
                                                ret_count = sale_list_detail_count
                                                ret_subtotal = sale_list_detail_count * sale_list_detail_price
                                            } else {
                                                ret_count = text
                                                ret_subtotal = text * sale_list_detail_price
                                            }
                                            refreshRetAmount()
                                        }
                                        enabled: !is_refunded

                                        digital_keyboard_x: 745 * dpi_ratio
                                        digital_keyboard_y: 440 * dpi_ratio
                                    }

                                    CusRect {
                                        anchors.fill: parent
                                        color: ST.color_black
                                        visible: is_refunded
                                        opacity: .3
                                        radius: ST.radius
                                        z: 5
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.preferredWidth: 180 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: Number(ret_subtotal).toFixed(2)
                                    }
                                }
                            }
                        }
                        highlight: CusRect {
                            width: listview_sale_record.width
                            height: 50 * dpi_ratio
                            color: ST.color_orange
                            opacity: .4
                            z: 2
                        }
                    }

                    CusRect {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 70 * dpi_ratio
                        color: ST.color_transparent

                        RowLayout {
                            anchors.fill: parent
                            CusRect {
                                Layout.preferredWidth: 350 * dpi_ratio
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                CusText {
                                    id: text_ret_amount
                                    text: qsTr("退款合计: ") + ret_original_amount
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }

                            CusSpacer {
                                Layout.fillWidth: true
                            }

                            CusRect {
                                Layout.preferredWidth: 400 * dpi_ratio
                                Layout.fillHeight: true
                                color: ST.color_transparent
                                RowLayout {
                                    anchors.fill: parent
                                    CusButton {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 58 * dpi_ratio
                                        Layout.alignment: Qt.AlignVCenter
                                        Layout.preferredWidth: 1
                                        text: qsTr("退货")
                                        onClicked: {
                                            if (is_refunded) {
                                                toast.openWarn(qsTr("不可重复退款"))
                                                return
                                            }

                                            if (ret_original_amount == 0) {
                                                toast.openWarn(qsTr("请选择退货商品"))
                                                return
                                            }
                                            showSaleOrderRefundPopup(false,jqPayAmount)
                                        }
                                    }
                                    CusButton {
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 58 * dpi_ratio
                                        Layout.alignment: Qt.AlignVCenter
                                        Layout.preferredWidth: 1
                                        text: qsTr("整单退货")

                                        onClicked: {
                                            if (is_refunded) {
                                                toast.openWarn(qsTr("不可重复退款"))
                                                return
                                            }
                                            showSaleOrderRefundPopup(true,jqPayAmount)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    Component {
        id: compo_sale_order_refund

        PopupRefundDetailsPaymethod {}
    }
}
