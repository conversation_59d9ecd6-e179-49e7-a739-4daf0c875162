﻿import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import ".."
import EnumTool 1.0
import SortFilterProxyModel 0.2

CusRect {
    color: ST.color_transparent

    onVisibleChanged: {
        if (visible) {
            resetAllInfo()
            if (permissionCtrl.isHavePermission(EnumTool.PERMISSION_PAGE_L2_QUERY__SALE_LIST))
                reqSaleRecord()
            else
                toast.openWarn("无此权限")
        }
    }

    property alias order_list: order_list
    property alias tf_search_order: tf_search_order

    function resetAllInfo() {
        resetOverview()
        lm_sale_record.clear()
    }

    function reqSaleRecord() {
        resetOverview()
        right_side.sales_record_detail_jstr = ""

        orderControl.getSalesRecord4Qml(function (data) {
            var json_doc = JSON.parse(data)

            var json_doc_address = json_doc.address
            var json_doc_cus_data = json_doc.cus_data
            var json_doc_data1 = json_doc.data1
            var json_doc_data = json_doc.data
            for (var i = 0; i < json_doc_address.length; ++i) {

                //现金
                if (json_doc_address[i].pay_method == 1) {
                    overview._cash = json_doc_address[i].payMoney
                }

                //支付宝
                if (json_doc_address[i].pay_method == 2) {
                    overview._alipay = json_doc_address[i].payMoney
                }

                //微信
                if (json_doc_address[i].pay_method == 3) {
                    overview._weixin = json_doc_address[i].payMoney
                }

                //银行卡
                if (json_doc_address[i].pay_method == 4) {
                    overview._bank_cards = json_doc_address[i].payMoney
                }

                //免密
                if (json_doc_address[i].pay_method == 13) {
                    overview._free_password_payment = json_doc_address[i].payMoney
                }

                //*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\

                //欠款
                if (json_doc_address[i].pay_method == -1) {
                    overview._debt = json_doc_address[i].payMoney
                }

                //订单数量
                if (json_doc_address[i].pay_method == -2) {
                    overview._order_quantity = json_doc_address[i].payMoney
                }

                //营业额
                if (json_doc_address[i].pay_method == -3) {
                    overview._turnover = json_doc_address[i].payMoney
                }

                //小程序
                if (json_doc_address[i].pay_method == -4) {
                    var cur_item = json_doc_address[i]

                    overview._online_income = cur_item.payMoney
                    // overview._online_income = cur_item.payMoney
                }

                //订单利润
                if (json_doc_address[i].pay_method == -5) {
                    overview._order_profit = json_doc_address[i].payMoney
                }
            }

            // overview._order_quantity = json_doc_data.length

            overview._member_recharge = json_doc_cus_data.sum_recharge_money
            overview._member_spending = json_doc_cus_data.sum_sale_money
            overview._point_products = json_doc_cus_data.sum_point_goods_money
            overview._sum_return_money = json_doc_cus_data.sum_return_money
            overview._platform_coupon = json_doc_cus_data.plat_coupon_money
            overview._platform_subsidy_beans = json_doc_cus_data.plat_beans
            overview._shop_coupon = json_doc_cus_data.shop_coupon_money
            overview._shop_subsidy_beans = json_doc_cus_data.shop_beans

            overview._pending_deposit = json_doc_cus_data.not_arrived_money
            overview._withdrawn = json_doc_cus_data.cash_money
            overview._pending_withdrawal = json_doc_cus_data.not_cash_money

            lm_sale_record.clear()
            for (var i = json_doc_data.length - 1; i >= 0; --i) {
                lm_sale_record.append(json_doc_data[i])
            }

            listview_sale_record.currentIndex = -1
        }, btn_calendar_begin.text, btn_calendar_end.text)
    }

    function reqSaleRecordDetail(sale_list_unique) {
        lm_sale_record_detail.clear()

        orderControl.getSalesRecordDetail4Qml(function (data) {
            lm_sale_record_detail.clear()

            right_side.sales_record_detail_jstr = data
            var json_doc = JSON.parse(data)
            cur_sales_record_detail = data
            json_doc = json_doc.data

            var ret_num_count = 0

            var json_doc_listDetail = json_doc.listDetail
            for (var i = 0; i < json_doc_listDetail.length; ++i) {
                var cur_list_detail = json_doc_listDetail[i]
                lm_sale_record_detail.append({
                                                 "goods_name": cur_list_detail.goods_name,
                                                 "sale_list_detail_count": cur_list_detail.sale_list_detail_count,
                                                 "sale_list_detail_price": cur_list_detail.sale_list_detail_price,
                                                 "retCount": cur_list_detail.retCount,
                                                 "sub_total": cur_list_detail.sub_total,
                                                 "goods_cheng_type": cur_list_detail.goods_cheng_type
                                             })
                ret_num_count += Number(cur_list_detail.retCount)
            }

            if (ret_num_count == 0) {
                goods_list.is_refund = false
            } else {
                goods_list.is_refund = true
            }

            date_selecter._sale_list_datetime = json_doc.sale_list_datetime
            date_selecter._payDetailStr = json_doc.payDetailStr
            date_selecter._sale_list_total = json_doc.sale_list_total
            date_selecter._sale_list_unique = json_doc.sale_list_unique
            date_selecter.ret_list_total_money = json_doc.hasOwnProperty("ret_list_total_money") ? json_doc.ret_list_total_money : ""
        }, sale_list_unique)
    }

    function resetOverview() {
        overview._turnover = "0"
        overview._order_quantity = "0"
        overview._order_profit = "0"

        overview._member_recharge = "0"
        overview._weixin = "0"
        overview._member_spending = "0"
        overview._free_password_payment = "0"
        overview._point_products = "0"
        overview._cash = "0"
        overview._sum_return_money = "0"
        overview._debt = "0"
        overview._alipay = "0"
        overview._bank_cards = "0"

        overview._online_income = "0"
        overview._platform_coupon = "0"
        overview._pending_deposit = "0"
        overview._platform_subsidy_beans = "0"
        overview._withdrawn = "0"
        overview._shop_coupon = "0"
        overview._pending_withdrawal = "0"
        overview._shop_subsidy_beans = "0"
    }

    function showRefundDetail(lm_sale_record_detail) {
        window_root.loader_4_refund_details.sourceComponent = compo_refund_detail
        window_root.loader_4_refund_details.item.open(lm_sale_record_detail)
    }

    property string cur_sales_record_detail: ""

    RowLayout {
        anchors.fill: parent
        spacing: ST.margin

        CusRect {
            id: left_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 1400 *dpi_ratio
            color: ST.color_white_pure
            radius: ST.radius

            ColumnLayout {
                anchors.fill: parent
                spacing: ST.margin
                Rectangle {
                    id: overview
                    property bool is_overview_mini: true

                    property real btn_height: 45 * dpi_ratio

                    Layout.preferredHeight: is_overview_mini ? (80 * dpi_ratio + btn_height) : (250 * dpi_ratio + btn_height)

                    Layout.fillWidth: true
                    color: ST.color_white_pure

                    property var _turnover: "0" //营业额
                    property var _order_quantity: "0" //订单数量
                    property var _order_profit: "0" //订单利润

                    property var _member_recharge: "0" //充值金额
                    property var _weixin: "0"
                    property var _member_spending: "0" //会员消费
                    property var _free_password_payment: "0"
                    property var _point_products: "0" //积分商品
                    property var _cash: "0"
                    property var _sum_return_money: "0" //退款金额
                    property var _debt: "0"
                    property var _alipay: "0" //支付宝
                    property var _bank_cards: "0" //银行卡

                    property var _online_income: "0" //线上收入
                    property var _platform_coupon: "0" //平台优惠券
                    property var _pending_deposit: "0" //待到账
                    property var _platform_subsidy_beans: "0" //平台补贴豆
                    property var _withdrawn: "0" //已提现
                    property var _shop_coupon: "0" //店铺优惠券
                    property var _pending_withdrawal: "0" //待提现
                    property var _shop_subsidy_beans: "0" //店铺补贴豆

                    ColumnLayout {
                        anchors.fill: parent

                        CusRect {
                            visible: overview.is_overview_mini
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 20 * dpi_ratio
                                spacing: 18 * dpi_ratio

                                CusRect {
                                    id: rect_sales_overview_mini
                                    //                                    Layout.preferredWidth: 480
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    color: ST.color_white_pure
                                    border {
                                        width: 2 * dpi_ratio
                                        color: ST.color_grey
                                    }

                                    layer.enabled: true
                                    layer.effect: OpacityMask {
                                        maskSource: Rectangle {
                                            width: rect_sales_overview_mini.width
                                            height: rect_sales_overview_mini.height
                                            radius: ST.radius
                                        }
                                    }

                                    ColumnLayout {
                                        anchors.fill: parent

                                        //标题栏
                                        CusRect {
                                            Layout.preferredHeight: 40 * dpi_ratio
                                            Layout.fillWidth: true
                                            color: ST.color_green
                                            Image {
                                                anchors.fill: parent
                                                source: "/Images/shade2.png"
                                            }
                                            CusText {
                                                text: qsTr("销售总览")
                                                x: 10 * dpi_ratio
                                                anchors.verticalCenter: parent.verticalCenter
                                                color: ST.color_white_pure
                                            }
                                        }
                                        CusRect {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            color: ST.color_transparent

                                            GridLayout {
                                                id: gl_sales_overview_mini
                                                anchors.fill: parent
                                                columns: 2
                                                columnSpacing: 0
                                                rowSpacing: 0
                                                property double col_width: width / columns
                                                property double row_height: height / rows

                                                function prefWidth(item) {
                                                    return col_width * item.Layout.columnSpan
                                                }
                                                function prefHeight(item) {
                                                    return 40 * dpi_ratio
                                                }

                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_sales_overview_mini.prefWidth(this)
                                                    Layout.preferredHeight: gl_sales_overview_mini.prefHeight(this)
                                                    Layout.leftMargin: 10 * dpi_ratio
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("营业额: ") + overview._turnover
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_sales_overview_mini.prefWidth(this)
                                                    Layout.preferredHeight: gl_sales_overview_mini.prefHeight(this)
                                                    Layout.leftMargin: 10 * dpi_ratio
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("订单数量: ") + overview._order_quantity
                                                }

                                                CusSpacer {
                                                    Layout.fillHeight: true
                                                    Layout.fillWidth: true
                                                }
                                            }
                                        }
                                    }
                                }

                                CusRect {
                                    id: rect_mini_programs_overview_mini
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_white_pure
                                    border {
                                        width: 2 * dpi_ratio
                                        color: ST.color_grey
                                    }

                                    layer.enabled: true
                                    layer.effect: OpacityMask {
                                        maskSource: Rectangle {
                                            width: rect_mini_programs_overview_mini.width
                                            height: rect_mini_programs_overview_mini.height
                                            radius: ST.radius
                                        }
                                    }

                                    ColumnLayout {
                                        anchors.fill: parent

                                        //标题栏
                                        CusRect {
                                            Layout.preferredHeight: 40 * dpi_ratio
                                            Layout.fillWidth: true
                                            color: ST.color_green
                                            Image {
                                                anchors.fill: parent
                                                source: "/Images/shade2.png"
                                            }
                                            CusText {
                                                text: qsTr("小程序概况")
                                                x: 10 * dpi_ratio
                                                anchors.verticalCenter: parent.verticalCenter
                                                color: ST.color_white_pure
                                            }
                                        }
                                        CusRect {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            color: ST.color_transparent

                                            GridLayout {
                                                id: gl_mini_programs_overview_mini
                                                anchors.fill: parent
                                                columns: 2
                                                columnSpacing: 0
                                                rowSpacing: 0
                                                property double col_width: width / columns
                                                property double row_height: height / rows

                                                function prefWidth(item) {
                                                    return col_width * item.Layout.columnSpan
                                                }
                                                function prefHeight(item) {
                                                    return 40 * dpi_ratio
                                                }

                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_mini_programs_overview_mini.prefWidth(this)
                                                    Layout.preferredHeight: gl_mini_programs_overview_mini.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("平台优惠券: ") + overview._platform_coupon
                                                }

                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_mini_programs_overview_mini.prefWidth(this)
                                                    Layout.preferredHeight: gl_mini_programs_overview_mini.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("平台补贴豆: ") + overview._platform_subsidy_beans
                                                }

                                                CusSpacer {
                                                    Layout.fillHeight: true
                                                    Layout.fillWidth: true
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        CusRect {
                            visible: !overview.is_overview_mini
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 20 * dpi_ratio
                                spacing: 18 * dpi_ratio
                                CusRect {
                                    id: rect_sales_overview
                                    Layout.preferredWidth: 330 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_white_pure

                                    border {
                                        width: 2 * dpi_ratio
                                        color: ST.color_grey
                                    }

                                    layer.enabled: true
                                    layer.effect: OpacityMask {
                                        maskSource: Rectangle {
                                            width: rect_sales_overview.width
                                            height: rect_sales_overview.height
                                            radius: ST.radius
                                        }
                                    }

                                    ColumnLayout {
                                        anchors.fill: parent
                                        //标题栏
                                        CusRect {
                                            Layout.preferredHeight: 40 * dpi_ratio
                                            Layout.fillWidth: true
                                            color: ST.color_green
                                            Image {
                                                anchors.fill: parent
                                                source: "/Images/shade2.png"
                                            }
                                            CusText {
                                                text: qsTr("销售总览")
                                                x: 10 * dpi_ratio
                                                anchors.verticalCenter: parent.verticalCenter
                                                color: ST.color_white_pure
                                            }
                                        }
                                        CusRect {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            color: ST.color_transparent

                                            ColumnLayout {
                                                anchors.fill: parent

                                                CusRect {
                                                    Layout.fillWidth: true
                                                    Layout.preferredHeight: 40 * dpi_ratio
                                                    color: ST.color_transparent

                                                    CusText {
                                                        text: qsTr("营业额: ") + overview._turnover
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        x: 10 * dpi_ratio
                                                    }
                                                }
                                                CusRect {
                                                    Layout.fillWidth: true
                                                    Layout.preferredHeight: 40 * dpi_ratio
                                                    color: ST.color_transparent
                                                    CusText {
                                                        text: qsTr("订单数量: ") + overview._order_quantity
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        x: 10 * dpi_ratio
                                                    }
                                                }
                                                CusRect {
                                                    Layout.fillWidth: true
                                                    Layout.preferredHeight: 40 * dpi_ratio
                                                    color: ST.color_transparent
                                                    CusText {
                                                        text: qsTr("订单利润: ") + overview._order_profit
                                                        anchors.verticalCenter: parent.verticalCenter
                                                        x: 10 * dpi_ratio
                                                    }
                                                }
                                                CusSpacer {
                                                    Layout.fillHeight: true
                                                }
                                            }
                                        }
                                    }
                                }

                                CusRect {
                                    id: rect_business_overview
                                    Layout.preferredWidth: 480 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_white_pure
                                    border {
                                        width: 2 * dpi_ratio
                                        color: ST.color_grey
                                    }

                                    layer.enabled: true
                                    layer.effect: OpacityMask {
                                        maskSource: Rectangle {
                                            width: rect_business_overview.width
                                            height: rect_business_overview.height
                                            radius: ST.radius
                                        }
                                    }

                                    ColumnLayout {
                                        anchors.fill: parent

                                        //标题栏
                                        CusRect {
                                            Layout.preferredHeight: 40 * dpi_ratio
                                            Layout.fillWidth: true
                                            color: ST.color_green
                                            Image {
                                                anchors.fill: parent
                                                source: "/Images/shade2.png"
                                            }
                                            CusText {
                                                text: qsTr("营业概况")
                                                x: 10 * dpi_ratio
                                                anchors.verticalCenter: parent.verticalCenter
                                                color: ST.color_white_pure
                                            }
                                        }
                                        CusRect {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            color: ST.color_transparent

                                            GridLayout {
                                                id: gl_business_overview
                                                anchors.fill: parent
                                                columns: 2
                                                columnSpacing: 0
                                                rowSpacing: 0

                                                property double col_width: width / columns
                                                property double row_height: height / rows

                                                function prefWidth(item) {
                                                    return col_width * item.Layout.columnSpan
                                                }
                                                function prefHeight(item) {
                                                    return 40 * dpi_ratio
                                                }

                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_business_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_business_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("会员充值: ") + overview._member_recharge
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_business_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_business_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("微信: ") + overview._weixin
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_business_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_business_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("会员消费: ") + overview._member_spending
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_business_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_business_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("免密: ") + overview._free_password_payment
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_business_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_business_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("积分商品: ") + overview._point_products
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_business_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_business_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("现金: ") + overview._cash
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_business_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_business_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("退款: ") + overview._sum_return_money
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_business_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_business_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("支付宝: ") + overview._alipay
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_business_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_business_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("欠款: ") + overview._debt
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_business_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_business_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("银行卡: ") + overview._bank_cards
                                                }
                                                CusSpacer {
                                                    Layout.fillHeight: true
                                                }
                                            }
                                        }
                                    }
                                }

                                CusRect {
                                    id: rect_mini_programs_overview
                                    Layout.preferredWidth: 480 * dpi_ratio
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: ST.color_white_pure
                                    border {
                                        width: 2 * dpi_ratio
                                        color: ST.color_grey
                                    }

                                    layer.enabled: true
                                    layer.effect: OpacityMask {
                                        maskSource: Rectangle {
                                            width: rect_mini_programs_overview.width
                                            height: rect_mini_programs_overview.height
                                            radius: ST.radius
                                        }
                                    }

                                    ColumnLayout {
                                        anchors.fill: parent

                                        //标题栏
                                        CusRect {
                                            Layout.preferredHeight: 40 * dpi_ratio
                                            Layout.fillWidth: true
                                            color: ST.color_green
                                            Image {
                                                anchors.fill: parent
                                                source: "/Images/shade2.png"
                                            }
                                            CusText {
                                                text: qsTr("小程序概况")
                                                x: 10 * dpi_ratio
                                                anchors.verticalCenter: parent.verticalCenter
                                                color: ST.color_white_pure
                                            }
                                        }
                                        CusRect {
                                            Layout.fillWidth: true
                                            Layout.fillHeight: true
                                            color: ST.color_transparent

                                            GridLayout {
                                                id: gl_mini_programs_overview
                                                anchors.fill: parent
                                                columns: 2
                                                columnSpacing: 0
                                                rowSpacing: 0
                                                property double col_width: width / columns
                                                property double row_height: height / rows

                                                function prefWidth(item) {
                                                    return col_width * item.Layout.columnSpan
                                                }
                                                function prefHeight(item) {
                                                    return 40 * dpi_ratio
                                                }

                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_mini_programs_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_mini_programs_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("线上收入: ") + overview._online_income
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_mini_programs_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_mini_programs_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("平台优惠券: ") + overview._platform_coupon
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_mini_programs_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_mini_programs_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("待到账: ") + overview._pending_deposit
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_mini_programs_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_mini_programs_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("平台补贴豆: ") + overview._platform_subsidy_beans
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_mini_programs_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_mini_programs_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("已提现: ") + overview._withdrawn
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_mini_programs_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_mini_programs_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("店铺优惠券: ") + overview._shop_coupon
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_mini_programs_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_mini_programs_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    verticalAlignment: Text.AlignVCenter
                                                    text: qsTr("待提现: ") + overview._pending_withdrawal
                                                }
                                                CusText {
                                                    Layout.columnSpan: 1
                                                    Layout.preferredWidth: gl_mini_programs_overview.prefWidth(this)
                                                    Layout.preferredHeight: gl_mini_programs_overview.prefHeight(this)
                                                    Layout.leftMargin: 10
                                                    text: qsTr("店铺补贴豆: ") + overview._shop_subsidy_beans
                                                    verticalAlignment: Text.AlignVCenter
                                                }
                                                CusSpacer {
                                                    Layout.fillHeight: true
                                                    Layout.fillWidth: true
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }  
                    }

                }
                Item {
                    width: 200 * dpi_ratio
                    height: 50 * dpi_ratio
                    CusRect {
                        id:queryCus
                        color: ST.color_transparent
                        Layout.preferredHeight: 50 * dpi_ratio
                        Layout.fillWidth: true
                        Layout.rightMargin: 20 * dpi_ratio
                        Layout.leftMargin: 20 * dpi_ratio

                        CusRect {
                            id:cusPayment
                            anchors.left: parent.left
                            anchors.leftMargin: 25 * dpi_ratio
                            width: 300 *dpi_ratio
                            color: ST.color_transparent

                            CusComboBox {
                                id: combo_payment
                                width: 300 *dpi_ratio
                                Layout.fillHeight: false
                                textRole: "payment_name"
                                font.pixelSize: 24 * dpi_ratio * configTool.fontRatio
                                model: ListModel {
                                    id: lm_payment
                                    ListElement {
                                        payment_name: "全部支付方式"
                                    }
                                    ListElement {
                                        payment_name: "现金"
                                    }
                                    ListElement {
                                        payment_name: "支付宝"
                                    }
                                    ListElement {
                                        payment_name: "微信"
                                    }
                                    ListElement {
                                        payment_name: "储值卡"
                                    }
                                    ListElement {
                                        payment_name: "混合支付"
                                    }
                                    ListElement {
                                        payment_name: "金圈平台"
                                    }
                                }
                                currentIndex: 0
                                onCurrentIndexChanged: {
                                                        if (currentIndex == 0) {
                                                            logMgr.logEvtInfo4Qml("currentIndex==-1:{}",currentIndex)
                                                            logMgr.logEvtInfo4Qml("currentIndex==-1:{}",lm_payment.get(currentIndex).payment_name)
                                                            order_list.pay_type = ""
//                                                            refreshCategory2("")
                                                        } else {
                                                            logMgr.logEvtInfo4Qml("currentIndex:{}",currentIndex)
                                                            order_list.pay_type = lm_payment.get(currentIndex).payment_name
//                                                            refreshCategory2(lm_category1.get(currentIndex).goods_kind_unique)
                                                        }
                                }
                                }

    //                                            delegate: CusRadioBtnRectManual {
    //                                                Layout.fillHeight: true
    //                                                Layout.preferredWidth: 125 * dpi_ratio
    //                                                text: role_name ==="现金"?qsTr("现金"):role_name ==="支付宝"?qsTr("支付宝"):role_name ==="微信"?qsTr("微信"):
    //                                                role_name ==="储值卡"?qsTr("储值卡"):role_name ==="混合支付"?qsTr("混合支付"):role_name ==="金圈平台"?qsTr("金圈平台"):""
    //                                                checked: order_list.pay_type == text
    //                                                onClicked: {
    //                                                    logMgr.logEvtInfo4Qml("text:{}",text)
    //                                                    order_list.pay_type = text
    //                                                    listview_sale_record.currentIndex = -1
    //                                                }
    //                                            }

    //                                            function refreshPayMethod() {
    //                                                lm_pay_radio.clear()
    //                                                var json_doc = JSON.parse(payMethodControl.getValidPayMethodJson4Query())
    //                                                for (var i = 0; i < json_doc.length; ++i) {
    //                                                    var cur_item = json_doc[i]
    //                                                    lm_pay_radio.append(cur_item)
    //                                                }
    //                                            }

    //                                            Component.onCompleted: {
    //                                                refreshPayMethod()
    //                                            }
    //                                            onVisibleChanged: {
    //                                                refreshPayMethod()
    //                                            }
    //                                        }

    //                                        CusRadioBtnRectManual {
    //                                            Layout.fillHeight: true
    //                                            Layout.preferredWidth: 110 * dpi_ratio
    //                                            text: qsTr("全部")
    //                                            checked: order_list.pay_type == ""
    //                                            onClicked: {
    //                                                order_list.pay_type = ""
    //                                                listview_sale_record.currentIndex = -1
    //                                            }
    //                                        }
                            }
                        CusRect {
                            id:custerminal
                            anchors.left: cusPayment.right
                            anchors.leftMargin: 40 *dpi_ratio
                            Layout.fillHeight: true
                            width: 300 *dpi_ratio
                            color: ST.color_transparent
                                    CusComboBox {
                                        id: combo_terminal
                                        width: 300 *dpi_ratio
                                        Layout.fillHeight: true
                                        textRole: "terminal_name"
                                        font.pixelSize: 24 * dpi_ratio * configTool.fontRatio
                                        model: ListModel {
                                            id: lm_terminal
                                            ListElement {
                                                terminal_name: "全部终端"
                                            }
                                            ListElement {
                                                terminal_name: "PC收银机"
                                            }
                                            ListElement {
                                                terminal_name: "PC收银机插件"
                                            }
                                            ListElement {
                                                terminal_name: "移动APP"
                                            }
                                            ListElement {
                                                terminal_name: "一刻钟到家小程序"
                                            }
                                            ListElement {
                                                terminal_name: "手持POS机"
                                            }
                                            ListElement {
                                                terminal_name: "其他"
                                            }
                                        }
                                        currentIndex: 0
                                        onCurrentIndexChanged: {
                                            if (currentIndex == 0) {
                                                logMgr.logEvtInfo4Qml("currentIndex==-1:{}",currentIndex)

                                                order_list.source_type = ""
//                                                            refreshCategory2("")
                                            } else {
                                                logMgr.logEvtInfo4Qml("currentIndex:{}",currentIndex)

                                                order_list.source_type = currentIndex

//                                                            refreshCategory2(lm_category1.get(currentIndex).goods_kind_unique)
                                            }
                                        }
                                    }
                        }
                        CusTextField {
                            id: tf_search_order
                            height: 40 *dpi_ratio
                            anchors.left: custerminal.right
                            anchors.leftMargin: 40 * dpi_ratio
                            width:400 * dpi_ratio
    /*                                Layout.preferredWidth: 250 * dpi_ratio*/
                            text: order_list.search_str
                            keyboard_status: 1
                            placeholderText: qsTr("会员卡/手机号/订单号/会员名称")
                            onTextChanged: {
                                if (text == "") {
                                    order_list.search_str = ""
                                }
                            }

                            normal_keyboard_x: 670 * dpi_ratio
                            normal_keyboard_y: 560 * dpi_ratio

                            digital_keyboard_x: 873 * dpi_ratio
                            digital_keyboard_y: 374 * dpi_ratio
                            onAccepted: {
                                order_list.search_str = text
                            }

                            CusRect {
                                color: ST.color_transparent
                                width: tf_search_order.height
                                height: width
                                anchors.right: parent.right

                                Image {
                                    width: 30 * dpi_ratio
                                    height: width
                                    anchors.centerIn: parent
                                    fillMode: Image.PreserveAspectFit
                                    visible: tf_search_order.text != ""
                                    source: "/Images/yuebuzu_icon.png"
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        if (tf_search_order.text != "") {
                                            tf_search_order.text = ""
                                            listview_sale_record.currentIndex = -1
                                        }
                                    }
                                }
                            }
                        }
                        CusRect {
                            height:40 * dpi_ratio
                            Layout.preferredWidth: 100 * dpi_ratio
                            anchors.left: tf_search_order.right
                            anchors.leftMargin: 160 *dpi_ratio
                            CusButton {
                                    id:moreCus
                                    height:40 * dpi_ratio
                                    Layout.preferredWidth: 100 * dpi_ratio
                                    text: overview.is_overview_mini ? qsTr("更多") : qsTr("精简")
                                    onClicked: {
                                        overview.is_overview_mini = !overview.is_overview_mini
                                    }
                                }
                    }
                    }
                }
                CusRect {
                    id: order_list
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure
                    property string search_str: ""
                    property string pay_type: ""
                    property string source_type:""

                    ColumnLayout {
                        anchors.fill: parent

                        //标题栏
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio
                            RowLayout {
                                anchors.fill: parent
                                spacing: 1 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 270 * dpi_ratio
                                    clip: true
                                    color: ST.color_green
                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("订单时间")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 150 * dpi_ratio
                                    clip: true
                                    color: ST.color_green
                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("收件人")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 260 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("订单号")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 200 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("付款方式")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 145 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("终端类型")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 135 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("总金额")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 145 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("数量")
                                        color: ST.color_white_pure
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 190 * dpi_ratio
                                    clip: true
                                    color: ST.color_green

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("手机号")
                                        color: ST.color_white_pure
                                    }
                                }
                            }
                        }

                        ListModel {
                            id: lm_sale_record
                        }

                        SortFilterProxyModel {
                            id: lm_goods_kind_detail_filter
                            sourceModel: lm_sale_record
                            filters: [
                                AnyOf {
                                    RegExpFilter {
                                        roleName: "sale_list_unique"
                                        pattern: order_list.search_str
                                        caseSensitivity: Qt.CaseInsensitive
                                    }
                                    RegExpFilter {
                                        roleName: "sale_list_phone"
                                        pattern: order_list.search_str
                                        caseSensitivity: Qt.CaseInsensitive
                                    }
                                    RegExpFilter {
                                        roleName: "sale_list_name"
                                        pattern: order_list.search_str
                                        caseSensitivity: Qt.CaseInsensitive
                                    }
                                    RegExpFilter {
                                        roleName: "cus_unique"
                                        pattern: order_list.search_str
                                        caseSensitivity: Qt.CaseInsensitive
                                    }

                                },
                                RegExpFilter {
                                    roleName: "payMent"
                                    enabled: order_list.pay_type != ""
                                    pattern: order_list.pay_type
                                    caseSensitivity: Qt.CaseInsensitive
                                },
                                RegExpFilter {
                                    roleName: "source_type"
                                    enabled: order_list.source_type != ""
                                    pattern: order_list.source_type
                                    caseSensitivity: Qt.CaseInsensitive
                                }
                            ]
                        }

                        ListView {
                            id: listview_sale_record
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            clip: true
                            highlightFollowsCurrentItem: true
                            highlightMoveDuration: 0
                            focus: true
                            currentIndex: -1
                            model: lm_goods_kind_detail_filter

                            delegate: CusRect {
                                readonly property var _payMent: payMent
                                readonly property var _sale_list_datetime: sale_list_datetime
                                readonly property var _sale_list_name: sale_list_name
                                readonly property var _sale_list_phone: sale_list_phone
                                readonly property var _sale_list_total: sale_list_total
                                readonly property var _sale_list_totalCount: sale_list_totalCount
                                readonly property var _sale_list_unique: sale_list_unique
                                readonly property var _sale_list_unique_str: sale_list_unique_str
                                readonly property string _source_type: {
                                    switch (source_type) {
                                        case 1: return "PC收银机";
                                        case 2: return "PC收银机插件";
                                        case 3: return "移动APP";
                                        case 4: return "一刻钟到家小程序";
                                        case 5: return "手持POS机";
                                        case 6: return "其他";
                                        default: return source_type.toString();
                                    }
                                }

                                color: ST.color_white_pure

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        listview_sale_record.currentIndex = index
                                    }
                                }

                                // 单行内容
                                height: 50 * dpi_ratio
                                width: listview_sale_record.width

                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 2 * dpi_ratio

                                    //订单时间
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 260 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            text: _sale_list_datetime
                                            color: ST.color_black
                                            elide: Text.ElideLeft
                                            width: parent.width
                                            horizontalAlignment: Text.AlignHCenter
                                        }
                                    }
                                    //收件人
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            text: _sale_list_name
                                            color: ST.color_black
                                        }
                                    }
                                    //订单号
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 270 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: _sale_list_unique_str
                                            color: ST.color_black
                                            elide: Text.ElideLeft
                                            width: parent.width
                                        }
                                    }
                                    //付款方式
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 150 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: _payMent
                                            color: ST.color_black
                                        }
                                    }
                                    //终端类型
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 160 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: _source_type
                                            color: ST.color_black
                                        }
                                    }
                                    //总金额
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 115 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: sale_list_total
                                            color: ST.color_black
                                        }
                                    }
                                    //数量
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 145 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: _sale_list_totalCount
                                            color: ST.color_black
                                        }
                                    }
                                    //手机号
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 190 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            anchors.centerIn: parent
                                            text: _sale_list_phone
                                            color: ST.color_black
                                        }
                                    }
                                }
                            }

                            highlight: CusRect {
                                width: listview_sale_record.width
                                height: 50 * dpi_ratio
                                color: ST.color_orange
                                opacity: .4
                                z: 2
                            }

                            onCurrentIndexChanged: {
                                if (currentIndex < 0) {
                                    right_side.clearInfo()
                                    return
                                }
                                reqSaleRecordDetail(lm_goods_kind_detail_filter.get(currentIndex).sale_list_unique_str)
                            }
                        }
                    }
                }
            }
        }

        CusRect {
            id: right_side
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredWidth: 500 *dpi_ratio
            color: ST.color_transparent

            function clearInfo() {
                sales_record_detail_jstr = ""
                date_selecter._sale_list_unique = ""
                date_selecter._sale_list_datetime = ""
                date_selecter._sale_list_total = ""
                date_selecter._payDetailStr = ""
                date_selecter.ret_list_total_money = ""
            }

            property string sales_record_detail_jstr

            ColumnLayout {
                anchors.fill: parent
                spacing: ST.margin

                CusRect {
                    id: date_selecter
                    Layout.preferredHeight: 240 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    property var _sale_list_unique: ""
                    property var _sale_list_datetime: ""
                    property var _sale_list_total: ""
                    property var _payDetailStr: ""
                    property var ret_list_total_money: ""

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.leftMargin: 15 * dpi_ratio

                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            Layout.preferredHeight: 1
                            color: ST.color_transparent
                            CusText {
                                text: qsTr("订单编号: ") + date_selecter._sale_list_unique
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            Layout.preferredHeight: 1
                            CusText {
                                text: qsTr("下单日期: ") + date_selecter._sale_list_datetime
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            Layout.preferredHeight: 1
                            CusText {
                                text: qsTr("订单金额: ") + date_selecter._sale_list_total
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            Layout.preferredHeight: 1
                            visible: date_selecter.ret_list_total_money != ""
                            CusText {
                                text: qsTr("退款金额: ") + date_selecter.ret_list_total_money
                                anchors.verticalCenter: parent.verticalCenter
                                //wrapMode: Text.Wrap
                                anchors.fill: parent
                            }
                        }
                        CusRect {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: ST.color_transparent
                            Layout.preferredHeight: 2
                            CusText {
                                text: qsTr("支付详情: ") + date_selecter._payDetailStr
                                anchors.verticalCenter: parent.verticalCenter
                                wrapMode: Text.Wrap
                                anchors.fill: parent
                            }
                        }
                    }
                }

                CusRect {
                    id: goods_list
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: ST.color_white_pure
                    radius: ST.radius

                    property bool is_refund: false

                    ColumnLayout {
                        anchors.fill: parent
                        //标题栏
                        Item {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50 * dpi_ratio
                            Rectangle {
                                anchors.fill: parent
                                color: "#f3f4f4"
                            }
                            RowLayout {
                                anchors.fill: parent
                                spacing: 2 * dpi_ratio

                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 210 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("商品名称")
                                        color: ST.color_black
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        id: goods_num
                                        text: qsTr("商品数量")
                                        color: ST.color_black
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.horizontalCenter: parent.horizontalCenter
                                        anchors.horizontalCenterOffset: goods_list.is_refund ? -26 * dpi_ratio : 0
                                    }
                                    CusText {
                                        text: qsTr(" (退)")
                                        visible: goods_list.is_refund
                                        color: ST.color_red
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.left: goods_num.right
                                    }
                                }
                                CusRect {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true
                                    Layout.preferredWidth: 140 * dpi_ratio
                                    clip: true
                                    color: ST.color_transparent

                                    CusText {
                                        anchors.centerIn: parent
                                        text: qsTr("商品单价")
                                        color: ST.color_black
                                    }
                                }
                            }
                        }

                        ListView {
                            id: lv_sale_record_detail
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            clip: true
                            model: ListModel {
                                id: lm_sale_record_detail
                            }

                            delegate: Item {
                                id: it_sale_record_detail

                                width: lv_sale_record_detail.width
                                height: 50 * dpi_ratio

                                readonly property var _goods_name: goods_name
                                readonly property var _sale_list_detail_count: sale_list_detail_count
                                readonly property var _sale_list_detail_price: sale_list_detail_price
                                readonly property var _retCount: retCount
                                readonly property var _goods_cheng_type: goods_cheng_type
                                // 根据索引设置背景颜色
                                Rectangle {
                                    anchors.fill: parent
                                    color: index % 2 === 0 ?"#ffffff" : "#f3f4f4"
                                }
                                RowLayout {
                                    anchors.fill: parent
                                    spacing: 2 * dpi_ratio

                                    //商品名称
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 210 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        Image {
                                            anchors.left: parent.left
                                            anchors.leftMargin: 2 * dpi_ratio
                                            anchors.top: parent.top
                                            anchors.topMargin: 3 * dpi_ratio
                                            width: 18 *dpi_ratio
                                            height: 18 *dpi_ratio
                                            source: "/Images/weight.svg"
                                            visible: it_sale_record_detail._goods_cheng_type === 1
                                        }
                                        CusText {
                                            id: goods_num_
                                            x: 15 * dpi_ratio
                                            width: parent.width - 20 * 2 * dpi_ratio
                                            text:  it_sale_record_detail._goods_name
                                            color: ST.color_black
                                            elide: Text.ElideRight
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                        }
                                    //商品数量
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent

                                        CusText {
                                            id: text_goods_num
                                            text: it_sale_record_detail._sale_list_detail_count
                                            color: ST.color_black

                                            anchors.verticalCenter: parent.verticalCenter
                                            anchors.horizontalCenter: parent.horizontalCenter
                                            anchors.horizontalCenterOffset: it_sale_record_detail._retCount > 0 ? -20 * dpi_ratio : 0
                                        }

                                        CusText {
                                            anchors.left: text_goods_num.right
                                            anchors.verticalCenter: parent.verticalCenter
                                            text: " (" + it_sale_record_detail._retCount + ")"
                                            color: ST.color_red
                                            visible: it_sale_record_detail._retCount > 0
                                        }
                                    }
                                    //商品单价
                                    CusRect {
                                        Layout.fillHeight: true
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 140 * dpi_ratio
                                        clip: true
                                        color: ST.color_transparent
                                        CusText {
                                            anchors.centerIn: parent
                                            text: it_sale_record_detail._sale_list_detail_price
                                            color: ST.color_black
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                CusRect {
                    id: buttons
                    Layout.preferredHeight: 90 * dpi_ratio
                    Layout.fillWidth: true
                    color: ST.color_transparent
                    RowLayout {
                        anchors.fill: parent
                        spacing: 5 * dpi_ratio

                        CusButton {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            color: ST.color_red
                            visible: cur_sales_record_detail.length > 0
                            text: qsTr("退货")
                            onClicked: {
                                if(date_selecter._payDetailStr == "未支付:0.0")
                                {
                                    toast.openInfo("订单未支付！")
                                }else{
                                    showRefundDetail(right_side.sales_record_detail_jstr)
                                }
                            }
                        }

                        CusButton {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            Layout.preferredWidth: 1
                            text: qsTr("打印")
                            visible: cur_sales_record_detail.length > 0
                            onClicked: {
                                printerControl.printQueryOrder(cur_sales_record_detail)
                            }
                        }
                    }
                }
            }

            CusRect {
                anchors.fill: parent
                radius: ST.radius
                color: ST.color_white_pure
                visible: right_side.sales_record_detail_jstr == ""
                CusText {
                    anchors.centerIn: parent
                    text: qsTr("请选择订单")
                }
                MouseArea {
                    anchors.fill: parent
                }
            }
        }
    }

    Component {
        id: compo_refund_detail
        PopupRefundDetails {}
    }
}
