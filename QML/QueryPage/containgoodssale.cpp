﻿#include "containgoodssale.h"

ContainGoodsSale::ContainGoodsSale(QObject *parent)
    : QAbstractItemModel(parent)
{
}

QVariant ContainGoodsSale::headerData(int section, Qt::Orientation orientation, int role) const
{
    // FIXME: Implement me!
}

QModelIndex ContainGoodsSale::index(int row, int column, const QModelIndex &parent) const
{
    // FIXME: Implement me!
}

QModelIndex ContainGoodsSale::parent(const QModelIndex &index) const
{
    // FIXME: Implement me!
}

int ContainGoodsSale::rowCount(const QModelIndex &parent) const
{
    if (!parent.isValid())
        return 0;

    // FIXME: Implement me!
}

int ContainGoodsSale::columnCount(const QModelIndex &parent) const
{
    if (!parent.isValid())
        return 0;

    // FIXME: Implement me!
}

QVariant ContainGoodsSale::data(const QModelIndex &index, int role) const
{
    if (!index.isValid())
        return QVariant();

    // FIXME: Implement me!
    return QVariant();
}
