﻿import QtQuick 2.15;
import QtQuick.Layouts 1.15
import QtQuick.Controls 1.4
import QtGraphicalEffects 1.15
import QtQuick.Dialogs 1.2
import ".."
import "../Utils2"
import EnumTool 1.0

Item {

    id: popup_root
    visible: false
    signal sigClose
    property var currentpageName:""
    property string pageNum:"0";
    property string currentpageUnique:"";
    property string currentPageNum:"0";

    function close()
    {

       popup_root.visible = false
       black_shadow.visible = false;
       saleDetailsText1.text ="";
       saleDetailsText2.text ="";
       saleDetailsText3.text ="";
       saleDetailsText4.text ="";
       currentpageName = ""
       pageListModel.clear()
       saleDetailsModel.clear()
       currentpageUnique = "0";
       pageNum = ""
       sigClose()
    }
    Timer
    {
        id: blackShadowTipTimer;
        interval: 1500;
        repeat: false;
        running: false;
        onTriggered:
        {
            black_shadow_tip.visible = false;
        }
    }
    //商品销售明细
    function reqGoodsSaleDetail(pageNum) {
        saleDetailsModel.clear()
        orderControl.getGoodsSaleDetail4Qml(function (data) {

                                        var json_doc = JSON.parse(data)
                                        var json_doc_data1 = json_doc.data1
                                        var json_doc_data = json_doc.data
                                        if(json_doc.status === 1)
                                        {
                                            for(var i = 0; i <pageListModel.count ;i++)
                                            {
                                                pageListModel.set(i,{colorText: "#999999",
                                                                      colorRec: "#ffffff"});

                                            }
                                            pageListModel.set(pageNum-1,{colorText: "#ffffff",
                                                                  colorRec: "#999999"})

                                            if (Array.isArray(json_doc.data) && json_doc.data.length > 0)
                                            {
                                               statisticalData.push(goodsSaleModel.get(json_doc.address));
                                                saleDetailsText1.text = json_doc_data1.saleCount.toFixed(3);
                                                saleDetailsText2.text = json_doc_data1.saleSum.toFixed(2);
                                                saleDetailsText3.text = json_doc_data1.purSum.toFixed(2);
                                                saleDetailsText4.text = (json_doc_data1.saleSum.toFixed(2)*1 - json_doc_data1.purSum.toFixed(2)*1).toFixed(2);

                                               for (var i = 0; i < json_doc_data.length; ++i) {
                                                   saleDetailsModel.insert(i,{saleListUnique: json_doc_data[i].saleListUnique,
                                                                               saleListDetailCount: json_doc_data[i].saleListDetailCount,
                                                                               saleSum: json_doc_data[i].saleSum,
                                                                               purSum: json_doc_data[i].purSum,
                                                                               saleListDatetime: json_doc_data[i].saleListDatetime});
                                                   if(currentpageUnique == json_doc_data[i].goodsBarcode)
                                                   {

                                                   }
                                                   else
                                                   {
                                                       currentpageName = json_doc_data[i].currentpageName;
                                                       currentpageUnique = json_doc_data[i].goodsBarcode;
                                                   }
                                               }
                                            }
                                        }
                                        else if(json_doc.status === 0){
                                            if(sales_details_page.visible == true)
                                            {
                                                saleDetailsModel.clear()
                                            }
                                            else
                                            {
                                                goodsSaleModel.clear();
                                                saleListText1.text = "0.00";//总数量
                                                saleListText2.text = "0.00";//总金额
                                                saleListText3.text = "0.00";//总成本
                                                saleListText4.text = "0.00";//总利润
                                            }

                                            black_shadow_tip.visible = true;
                                            black_shadow_tip_text.text = json_doc.msg;
                                            blackShadowTipTimer.start();
                                        }

        },currentpageUnique,btn_calendar_begin.text, btn_calendar_end.text,pageNum,"14")
    }
    function open(statisticalData,orderDetails) {
        visible = true;
        saleDetailsText1.text = statisticalData[0];
        saleDetailsText2.text = statisticalData[1];
        saleDetailsText3.text = statisticalData[2];
        saleDetailsText4.text = (statisticalData[1]*1 - statisticalData[2]*1).toFixed(2);
        currentpageName = statisticalData[3];
        for (var j = 0; j<orderDetails.length ;j++)
        {
            saleDetailsModel.insert(j,{saleListUnique: orderDetails[j][0],
                                        saleListDetailCount: orderDetails[j][1],
                                        saleSum: orderDetails[j][2],
                                        purSum: orderDetails[j][3],
                                        saleListDatetime: orderDetails[j][4]});
            if(currentpageUnique === orderDetails[j][6])
            {

            }
            else
            {
                currentpageName = orderDetails[j][5];
                currentpageUnique = orderDetails[j][6];
            }
        }
        var pageNumlist = statisticalData[4];
        if(pageNum == pageNumlist)
        {
        }
        else
        {
            for(var i = 1; i <=pageNumlist*1; i++)
            {
                pageListModel.insert(i-1,{num:i,
                                         colorRec: "#ffffff",
                                         colorText: "#999999"});
            }
            pageListModel.set(0,{colorRec: "#999999",
                                  colorText: "#ffffff"});
            pageNum = (pageNumlist*1).toFixed(0);
        }
        if(visible){
            black_shadow.visible = true;
        }
    }
    Rectangle
    {
        id: black_shadow;
        anchors.fill: parent;
        color: "black";
        opacity: 0.6;
        visible: false;
        z: 3;
        MouseArea
        {
            anchors.fill: parent;
            onPressed:
            {
                close();
                }
            }
    }
    CusRect {
        id: sales_details_page
        width: 1400 * dpi_ratio
        height: 850 * dpi_ratio
        anchors.horizontalCenter: parent.horizontalCenter;
        anchors.bottom:  parent.bottom;
        anchors.bottomMargin: 60 *dpi_ratio
        color: "#FFFFFF";
        radius: 10 * dpi_ratio
        z: 6;
        MouseArea {
            anchors.fill: parent
            onClicked: {

            }
        }
        CusRect {
            id:salesDetailsTop
            anchors.top: parent.top
            anchors.left: parent.left
            width:1400 * dpi_ratio
            height:70 * dpi_ratio
            color: "#00bd74"
            radius: 10 * dpi_ratio
            CusRect {
                id: top_side
                Layout.fillWidth: true
                height: 65 * dpi_ratio
                CusText{
                    anchors.left: parent.left
                    Layout.preferredWidth: 65 *dpi_ratio
                    anchors.leftMargin: 40 * dpi_ratio
                    height: 65 * dpi_ratio
                    color: "#ffffff"
                    text: qsTr("销售明细——") + currentpageName
                    font.pixelSize: 28 * dpi_ratio
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Rectangle
            {
                id: salesDetailsQuit;
                width: 50*dpi_ratio;
                height: 50*dpi_ratio;
                anchors.top: parent.top;
                anchors.topMargin: 10*dpi_ratio;
                anchors.right: parent.right;
                anchors.rightMargin: 10*dpi_ratio;
                radius: width/2
                color: "#999999"
                z: 5;
                Text
                {
                    id: salesDetailsQuitText;
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.verticalCenterOffset: -2*dpi_ratio;
                    anchors.horizontalCenter: parent.horizontalCenter;
                    anchors.horizontalCenterOffset: 3*dpi_ratio;
                    text:"+";
                    font.family: "微软雅黑";
                    font.bold: true;
                    font.pointSize: 30 *dpi_ratio;
                    rotation: 45
                    color:"#ffffff"
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onPressed:
                    {
                        close();
                    }
                }
            }
            Rectangle{
                anchors.bottom: parent.bottom
                anchors.left: parent.left
                width:1400 *dpi_ratio
                height: 8 *dpi_ratio
                color: "#00BD74"
            }
        }
        CusRect {

            anchors.top: salesDetailsTop.bottom;
            anchors.topMargin: 10*dpi_ratio;
            anchors.left: parent.left
            width:1400 * dpi_ratio
            height:70 * dpi_ratio
            color: "#FFFFFF"
            radius: 10 * dpi_ratio
            Rectangle
            {
                id: saleDetailsRec1;
                width: 3*dpi_ratio;
                height: 22*dpi_ratio;
                color: "#00bd74";
                anchors.left: parent.left;
                anchors.leftMargin: 90*dpi_ratio;
                anchors.top: parent.top;
                anchors.topMargin: 10*dpi_ratio;
            }
            Text
            {
                id: saleDetailsText1Tip;
                anchors.verticalCenter: saleDetailsRec1.verticalCenter
                anchors.left: saleDetailsRec1.right;
                anchors.leftMargin: 5*dpi_ratio;
                font.family: "微软雅黑";
                font.pointSize: 18*dpi_ratio;
                color: "#999999";
                text: qsTr("销售总数量：");
            }
            Text
            {
                id: saleDetailsText1;
                anchors.verticalCenter: saleDetailsText1Tip.verticalCenter;
                anchors.left: saleDetailsText1Tip.right;
                font.family: "微软雅黑";
                font.pointSize: 18*dpi_ratio;
                color: "#999999";
                text: "0.00";
            }

            Rectangle
            {
                id: saleDetailsRec2;
                width: 3*dpi_ratio;
                height: 22*dpi_ratio;
                color: "#00bd74";
                anchors.left: parent.left;
                anchors.leftMargin: 350*dpi_ratio;
                anchors.top: saleDetailsRec1.top;
            }
            Text
            {
                id: saleDetailsText2Tip;
                anchors.verticalCenter: saleDetailsRec2.verticalCenter
                anchors.left: saleDetailsRec2.right;
                anchors.leftMargin: 5*dpi_ratio;
                font.family: "微软雅黑";
                font.pointSize: 18*dpi_ratio;
                color: "#999999";
                text: qsTr("销售总金额：");
            }
            Text
            {
                id: saleDetailsText2;
                anchors.verticalCenter: saleDetailsText2Tip.verticalCenter;
                anchors.left: saleDetailsText2Tip.right;
                font.family: "微软雅黑";
                font.pointSize: 18*dpi_ratio;
                color: "#999999";
                text: "0.00";
            }

            Rectangle
            {
                id: saleDetailsRec3;
                width: 3*dpi_ratio;
                height: 22*dpi_ratio;
                color: "#00bd74";
                anchors.left: parent.left;
                anchors.leftMargin: 620*dpi_ratio;
                anchors.top: saleDetailsRec1.top;
            }
            Text
            {
                id: saleDetailsText3Tip;
                anchors.verticalCenter: saleDetailsRec3.verticalCenter
                anchors.left: saleDetailsRec3.right;
                anchors.leftMargin: 5*dpi_ratio;
                font.family: "微软雅黑";
                font.pointSize: 18*dpi_ratio;
                color: "#999999";
                text: qsTr("销售总成本：");
            }
            Text
            {
                id: saleDetailsText3;
                anchors.verticalCenter: saleDetailsText3Tip.verticalCenter;
                anchors.left: saleDetailsText3Tip.right;
                font.family: "微软雅黑";
                font.pointSize: 18*dpi_ratio;
                color: "#999999";
                text: "0.00";
            }

            Rectangle
            {
                id: saleDetailsRec4;
                width: 3*dpi_ratio;
                height: 22*dpi_ratio;
                color: "#00bd74";
                anchors.left: parent.left;
                anchors.leftMargin: 890*dpi_ratio;
                anchors.top: saleDetailsRec1.top;
            }
            Text
            {
                id: saleDetailsText4Tip;
                anchors.verticalCenter: saleDetailsRec4.verticalCenter
                anchors.left: saleDetailsRec4.right;
                anchors.leftMargin: 5*dpi_ratio;
                font.family: "微软雅黑";
                font.pointSize: 18*dpi_ratio;
                color: "#999999";
                text: qsTr("销售总利润：");
            }
            Text
            {
                id: saleDetailsText4;
                anchors.verticalCenter: saleDetailsText4Tip.verticalCenter;
                anchors.left: saleDetailsText4Tip.right;
                font.family: "微软雅黑";
                font.pointSize: 18*dpi_ratio;
                color: "#999999";
                text: "0.00";
            }
            Rectangle
            {
                id: saleDetailsLine;
                width: 1320*dpi_ratio;
                height: 2 *dpi_ratio;
                anchors.horizontalCenter: parent.horizontalCenter;
                anchors.bottom: saleDetailsRec1.bottom;
                anchors.bottomMargin: -10*dpi_ratio;
                color: "#cccccc"
            }
        }
        Rectangle
        {
            id: black_shadow_tip;
            width: 400* dpi_ratio;
            height: 100*dpi_ratio;
            anchors.centerIn: parent;
            color: "black";
            opacity: 0.8;
            radius: 5*dpi_ratio;
            visible: false;
            z: 100;
            Text
            {
                id: black_shadow_tip_text;
                anchors.centerIn: parent
                font.family: "微软雅黑";
                font.pointSize: 16*dpi_ratio;
                text: "";
                color: "#ffffff";
            }

            MouseArea
            {
                anchors.fill: parent
                onClicked:
                {
                    black_shadow_tip.visible = false;
                }
            }
        }
        TableView
        {
            id: tabview_saledetails;
            width: 1320*dpi_ratio;
            height: 620*dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 100*dpi_ratio;
            model: saleDetailsModel;

            TableViewColumn
            {
                id: saleDetailsColumn1;
                title: qsTr("订单编号");
                role: "saleListUnique";
                movable: false;
                resizable: false;
                width: 264*dpi_ratio;
            }
            TableViewColumn
            {
                id: saleDetailsColumn2;
                title: qsTr("销售数量");
                role: "saleListDetailCount";
                movable: false;
                resizable: false;
                width: 264*dpi_ratio;
            }
            TableViewColumn
            {
                id: saleDetailsColumn3;
                title: qsTr("销售金额");
                role: "saleSum";
                movable: false;
                resizable: false;
                width: 264*dpi_ratio;
            }
            TableViewColumn
            {
                id: saleDetailsColumn4;
                title: qsTr("销售成本");
                role: "purSum";
                movable: false;
                resizable: false;
                width: 264*dpi_ratio;
            }
            TableViewColumn
            {
                id: saleDetailsColumn6;
                title: qsTr("销售时间");
                role: "saleListDatetime";
                movable: false;
                resizable: false;
                width: 264*dpi_ratio;
            }
            itemDelegate: Rectangle {
                width:  100*dpi_ratio;
                height:  50*dpi_ratio;
                color: styleData.row % 2 == 0 ? "#ffffff" : "#f3f4f4";
                property bool isSelect: styleData.pressed;
                onIsSelectChanged: {
                    if(!isSelect)
                    {
                        return ;
                    }
//                    console.log("row = " + styleData.row);
//                    console.log("isSelect = " + isSelect);能使用
                }
                Text
                {
                    text: styleData.value;
                    anchors.centerIn: parent;
                    font.bold: styleData.selected;
                    font.family: "微软雅黑";
                    font.pointSize: 16*dpi_ratio;
                    color: "#333333";
                }
            }
            rowDelegate: Rectangle {
                width:  100*dpi_ratio;
                height:  40*dpi_ratio;
                color: styleData.row % 2 == 0 ? "#ffffff" : "#f3f4f4";
                Text{
                    text: styleData.value
                    anchors.left: parent.left;
                    anchors.leftMargin: 20*dpi_ratio;
                    font.family: "微软雅黑";
                    font.pointSize: 23*dpi_ratio;
                    color: "#333333";
                }
            }
            headerDelegate: Rectangle{
                implicitWidth:  160*dpi_ratio;
                implicitHeight:  40*dpi_ratio;
                color: "#f4f4f4";
                Text{
                    text: styleData.value
                    anchors.centerIn: parent
                    font.family: "微软雅黑";
                    font.pointSize: 16*dpi_ratio;
                    color: "#333333";
                }
                Rectangle
                {
                    width: 1*dpi_ratio;
                    height: 40*dpi_ratio;
                    color: "#999999";
                    anchors.verticalCenter: parent.verticalCenter;
                    anchors.right: parent.right;
                }
                Rectangle
                {
                    width: 264*dpi_ratio;
                    height: 1*dpi_ratio;
                    color: "#999999";
                    anchors.horizontalCenter: parent.horizontalCenter;
                    anchors.bottom: parent.bottom;
                }
            }
            ListModel
            {
                id: saleDetailsModel;
            }
        }
        Rectangle
        {
            id: saleDetailsQueryRec;
            width: 1400*dpi_ratio;
            height: 100*dpi_ratio;
            anchors.bottom: parent.bottom ;
            anchors.bottomMargin: 3*dpi_ratio;
            anchors.horizontalCenter: parent.horizontalCenter;
            Rectangle
            {
                id: saleDetailsPageUp;
                width: 55*dpi_ratio;
                height: 55*dpi_ratio;
                anchors.left: parent.left;
                anchors.leftMargin: 250*dpi_ratio;
                anchors.verticalCenter: parent.verticalCenter
                border.color: "#999999"
                border.width: 1*dpi_ratio;
                Text
                {
                    id: saleDetailsPageUpText;
                    text: "<"
                    anchors.centerIn: parent;
                    font.pointSize: 35*dpi_ratio;
                    color: "#999999"
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onClicked:
                    {
                        for(var i = 0; i <pageListModel.count-1 ;i++)
                        {
                            if(pageListModel.get(i+1).colorText == "#ffffff")
                            {
                                pageListModel.set(i,{colorText: "#ffffff",
                                                      colorRec: "#999999"});
                                pageListModel.set(i+1,{colorText: "#999999",
                                                      colorRec: "#ffffff"});
                                currentPageNum = pageListModel.get(i).num
                                reqGoodsSaleDetail(currentPageNum);
                                if(i > 1 && i < pageListModel.count-2)
                                {
                                    saleDetailsPageList.positionViewAtIndex(i-2,ListView.Beginning);
                                }
                            }
                            else if(i >0)
                            {
                                pageListModel.set(i,{colorText: "#999999",
                                                      colorRec: "#ffffff"});
                            }
                        }
                    }
                }
            }
            ListView
            {
                id: saleDetailsPageList;
                width: pageListModel.count > 3? 3*55*dpi_ratio:pageListModel.count*55*dpi_ratio;
                height: 55*dpi_ratio;
                anchors.left: saleDetailsPageUp.right;
                anchors.leftMargin: 5*dpi_ratio;
                anchors.verticalCenter: parent.verticalCenter
                clip: true;
                interactive: false;
                model: ListModel
                {
                id: pageListModel;
            }

            orientation: ListView.Horizontal
            delegate: Rectangle
            {
                id: saleDetailsPageRec;
                width: 55*dpi_ratio;
                height: 55*dpi_ratio;
                border.color: "#999999"
                border.width: 1*dpi_ratio;
                color: colorRec

                Text
                {
                    id: saleDetailsPageText;
                    text: num
                    anchors.centerIn: parent;
                    font.pointSize: 28*dpi_ratio;
                    color: colorText
                }
                MouseArea
                {
                    anchors.fill: parent;
                    onClicked:
                    {
                        for(var i = 0; i <pageListModel.count ;i++)
                        {
                            pageListModel.set(i,{colorText: "#999999",
                                                  colorRec: "#ffffff"});

                        }
                        logMgr.logDataInfo4Qml("index===:{}",index)
                        pageListModel.set(index,{colorText: "#ffffff",
                                              colorRec: "#999999"})
                        if(index*1 > 1 && index*1 < pageListModel.count-2)
                        {
                            saleDetailsPageList.positionViewAtIndex(index-2,ListView.Beginning);
                        }
                        currentPageNum = (index*1+1).toFixed(0);
                        reqGoodsSaleDetail(currentPageNum);


                    }
                }
        }
        }

    Rectangle
    {
        id: saleDetailsPageBack;
        width: 55*dpi_ratio;
        height: 55*dpi_ratio;
        anchors.left: saleDetailsPageList.right;
        anchors.leftMargin: 5*dpi_ratio;
        anchors.verticalCenter: parent.verticalCenter
        border.color: "#999999"
        border.width: 1*dpi_ratio;
        Text
        {
            id: saleDetailsPageBackText;
            text: ">"
            anchors.centerIn: parent;
            font.pointSize: 35*dpi_ratio;
            color: "#999999"
        }
        MouseArea
        {
            anchors.fill: parent;
            onClicked:
            {
                for(var i = pageListModel.count-1; i > 0 ;i--)
                {
                    if(pageListModel.get(i-1).colorText == "#ffffff")
                    {
                        pageListModel.set(i,{colorText: "#ffffff",
                                              colorRec: "#999999"});
                        pageListModel.set(i-1,{colorText: "#999999",
                                              colorRec: "#ffffff"});
                        currentPageNum = pageListModel.get(i).num
                        reqGoodsSaleDetail(currentPageNum);
                        if(i > 1 && i < pageListModel.count-2)
                        {
                            saleDetailsPageList.positionViewAtIndex(i-2,ListView.Beginning);
                        }
                    }
                    else if(i <pageListModel.count-1)
                    {
                        pageListModel.set(i,{colorText: "#999999",
                                              colorRec: "#ffffff"});
                    }
                }

            }
        }
    }
    Rectangle
    {
        id: saleDetailsQuery;
        width: 90*dpi_ratio;
        height: 45*dpi_ratio;
        anchors.verticalCenter: parent.verticalCenter;
        anchors.left: parent.left
        anchors.leftMargin: 800*dpi_ratio;
        border.color: "#333333"
        border.width: 1*dpi_ratio;
        TextInput
        {
            id: saleDetailsQueryInput;
            width: 90*dpi_ratio;
            height: 45*dpi_ratio;
            anchors.centerIn: parent
            verticalAlignment: Text.AlignVCenter;
            horizontalAlignment: Text.AlignHCenter;
            font.family: "微软雅黑";
            font.pointSize: 22*dpi_ratio;
            color: "#333333";
            clip: true;
            validator: IntValidator{bottom: 1; top: pageNum*1}
            onAccepted:
            {

                if(saleDetailsQueryInput.text != "")
                {
                    currentPageNum = saleDetailsQueryInput.text;
                    reqGoodsSaleDetail(saleDetailsQueryInput.text);            
                }
            }
            onTextChanged:
            {
                if(saleDetailsQueryInput.text == "0")
                {
                    saleDetailsQueryInput.text = "";
                }
                if(saleDetailsQueryInput.text == "+")
                {
                    saleDetailsQueryInput.text = "";
                }
            }
        }
        MouseArea
        {
            anchors.fill: parent
            onClicked:
            {
                saleDetailsQueryInput.forceActiveFocus();
                saleDetailsQueryInput.text = "";
                window_root.keyboard_c.openDigitalKeyboard();
            }
        }
    }
    Text
    {
        id:saleDetailsQueryPageNum;
        text: qsTr("页")
        font.family: "微软雅黑"
        font.pointSize: 24*dpi_ratio;
        anchors.verticalCenter: parent.verticalCenter
        anchors.left: saleDetailsQuery.right;
        anchors.leftMargin: 5*dpi_ratio;
    }

    Rectangle
    {
        id: saleDetailsQueryEnter;
        width: 140*dpi_ratio;
        height: 55*dpi_ratio;
        anchors.verticalCenter: parent.verticalCenter;
        anchors.left: saleDetailsQueryPageNum.right;
        anchors.leftMargin: 10*dpi_ratio;
        radius: 10*dpi_ratio;
        border.color: "#333333"
        border.width: 1*dpi_ratio;
        Text
        {
            id: saleDetailsQueryEnterText;
            text: qsTr("确认");
            font.family: "微软雅黑";
            font.pointSize: 24*dpi_ratio;
            anchors.centerIn: parent
            color: "#333333";
        }
        MouseArea
        {
            anchors.fill: parent
            onClicked:
            {
                saleDetailsQueryEnterText.color = "#ffffff";
                saleDetailsQueryEnter.color = "#999999";
                if(saleDetailsQueryInput.text != "")
                {
                    currentPageNum = saleDetailsQueryInput.text;
                    reqGoodsSaleDetail(saleDetailsQueryInput.text);
                }
                saleDetailsQueryEnterTimer.start();
            }
        }
    }
    Timer
    {
        id: saleDetailsQueryEnterTimer;
        interval: 200;
        running: false;
        repeat: false;
        onTriggered:
        {
            saleDetailsQueryEnterText.color = "#333333";
            saleDetailsQueryEnter.color = "#ffffff";
        }
    }
    Text
    {
        id: allPageText;
        text: qsTr("共 ")+pageNum+qsTr(" 页")
        font.family: "微软雅黑"
        font.pointSize: 24*dpi_ratio;
        anchors.verticalCenter: saleDetailsQueryRec.verticalCenter
        anchors.left: saleDetailsQueryEnter.right
        anchors.leftMargin: 40 *dpi_ratio
//        anchors.right: parent.right;
//        anchors.rightMargin: 150* dpi_ratio;
    }
    }
    }


}
