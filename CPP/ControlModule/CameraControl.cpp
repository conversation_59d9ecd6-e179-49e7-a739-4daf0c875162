﻿#include "CameraControl.h"

#include <QCameraInfo>
#include <QImage>
#include <opencv2/core.hpp>
#include <opencv2/opencv.hpp>
#include <tuple>
#include <utility>
#include "AdapterModule/ImageProvider.h"
#include "ControlModule/ControlManager.h"
#include "ControlModule/GoodsDataModel.h"
#include "ControlModule/WeightingScaleControl.h"
#include "LogManager.h"
#include "Utils/Utils.h"
#include "Utils/Utils4Qml.h"
#include "WorkerModule/CameraWorker2.h"
#include "WorkerModule/FaceRecognizer.h"
#include "WorkerModule/RecognitionWorker.h"
#include "opencv2/highgui.hpp"
#include "opencv2/objdetect/objdetect.hpp"
#include "qobject.h"
#include "qobjectdefs.h"
#include <iostream>
#include <chrono>

using namespace cv;
using namespace std;

QList<QCameraInfo> CameraControl::available_cameras_;

#ifdef _RECOGNITION_
std::atomic_int CameraControl::goods_recognition_status_ = -1;
#endif

CameraControl::CameraControl(QObject *parent) : QObject{parent}
{
    camera_worker_face_           = new CameraWorker2(this);
    qRegisterMetaType<std::vector<std::tuple<int, float>>>("std::vector<std::tuple<int,float>>");

    refreshAvailableCameras();

    camera_4_face_  = ConfigTool::getInstance()->getSetting(ConfigEnum::kCamera4Face).toString();
    camera_4_goods_ = ConfigTool::getInstance()->getSetting(ConfigEnum::kCamera4Goods).toString();
}

CameraControl::~CameraControl()
{
    delete camera_worker_face_;
    if (camera_worker_goods_)
    {
        disconnect(camera_worker_goods_, &CameraWorker2::sendImg, nullptr, nullptr);
    }
    disconnect(this, &CameraControl::sigSendGoodsResult, nullptr, nullptr);

    emit sigCloseFaceCamera();
    emit sigCloseGoodsCamera();

    if (camera_worker_goods_thread_)
    {
        camera_worker_goods_thread_->quit();
        camera_worker_goods_thread_->wait();
    }

    QThread::msleep(100);
}
const std::string getCurrentSystemTime()
{

  time_t time_seconds = time(0);
  struct tm now_time;
  localtime_s(&now_time, &time_seconds);
  std::string timestamp = std::to_string(now_time.tm_year + 1900) + "-" +
          std::to_string(now_time.tm_mon + 1) + "-" +
          std::to_string(now_time.tm_mday) + "-" +
          std::to_string(now_time.tm_hour) + ":" +
          std::to_string(now_time.tm_min) + ":" +
          std::to_string(now_time.tm_sec);

  return timestamp;
}
void CameraControl::init()
{

    ///-------------------------------------------| 人脸摄像头 |-------------------------------------------
    //
    const string face_cascade_name = Utils::getAppDirPath().toStdString() + "/haarcascade_frontalface_alt.xml";

    if (!face_cascade_.load(face_cascade_name))
    {
        LOG_EVT_INFO("Error loading cascade file. Exiting!");
    }

    image_provider_face_    = new ImageProvider;
    image_provider_4_goods_ = new ImageProvider;

    image_provider_4_goods_selected_area_ = new ImageProvider;

    camera_worker_face_thread_ = new QThread();
    //camera_worker_face_        = new CameraWorker2;
    LOG_EVT_INFO(">>>> camera_worker_face_thread_ 对象创建 >>>");
    connect(camera_worker_face_thread_, &QThread::finished, camera_worker_face_, &CameraWorker2::deleteLater);   // 工作线程停止,销毁工作对象
    connect(camera_worker_face_, &CameraWorker2::destroyed, camera_worker_face_thread_, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(this, &CameraControl::sigOpenFaceCamera, camera_worker_face_, &CameraWorker2::process);
    connect(this, &CameraControl::sigCloseFaceCamera, camera_worker_face_, &CameraWorker2::stop);
    connect(camera_worker_face_, &CameraWorker2::sendImg, this, // 接收到线程处理的数据,让线程退出
            [&](QImage img)
            {
                if (!img.isNull())
                {
                    std::lock_guard lock(mutex_original_face_img_);
                    image_provider_face_->img_ = img;
                    last_original_face_img_    = img;
                    emit sigSendFaceImg();
                    if (!face_cascade_.empty())
                    {
                        auto face_quantity = FaceRecognizer::imageProcess(QImageToMat(img), face_cascade_);
                        LOG_EVT_INFO("face_quantity:{}",face_quantity);
                        if (face_quantity > 0)
                        {
                            ++detected_num_;
                            LOG_EVT_INFO("detected_num_:{}",detected_num_);
                            if (detected_num_ > 10)
                            {
                                emit sigFaceDetected(face_quantity);
                            }
                        }
                        else
                        {
                            detected_num_ = 0;
                        }
                    }
                }
            });
    camera_worker_face_->moveToThread(camera_worker_face_thread_);
    camera_worker_face_thread_->start();
    LOG_EVT_INFO(">>>>> camera_worker_face_thread_ 线程开启 >>>>>>");
    //
    ///-------------------------------------------| 人脸摄像头 |-------------------------------------------


#ifdef _RECOGNITION_
    ///-------------------------------------------| 商品摄像头 |-------------------------------------------
    //
    camera_worker_goods_        = new CameraWorker2();
    camera_worker_goods_thread_ = new QThread();

    RecognitionWorker::initGoodsList();

    at::set_num_threads(2);
    static torch::Device              device = torch::Device(torch::kCPU);
    static torch::jit::script::Module model  = torch::jit::load("resnext101_GaussianBlur_9_1.0_10.0-0.900.pt");
    model.to(device);
    model.eval();

    connect(camera_worker_goods_thread_, &QThread::finished, camera_worker_goods_, &CameraWorker::deleteLater);
    connect(camera_worker_goods_, &CameraWorker2::destroyed, camera_worker_goods_thread_, &QThread::deleteLater);

    connect(this, &CameraControl::sigOpenGoodsCamera, camera_worker_goods_, &CameraWorker2::process, Qt::ConnectionType::QueuedConnection);
    connect(this, &CameraControl::sigCloseGoodsCamera, camera_worker_goods_, &CameraWorker2::stop, Qt::ConnectionType::QueuedConnection);
    connect(this, &CameraControl::sigCaptureNow, camera_worker_goods_, &CameraWorker2::captureNow, Qt::ConnectionType::QueuedConnection);
    connect(
        camera_worker_goods_, &CameraWorker2::sendImg, this,
        [&](QImage img)
        {
            if (!img.isNull())
            {
                vector<std::tuple<int, float>> result_list;

                image_provider_4_goods_->img_ = img;

                Utils::mkMutiDir("D:/ZXY/");

                cv::Mat tmp_img = QImageToMat(img);
                cv::Mat selected_mat;

                getSelectedAreaMat(tmp_img, selected_mat);

                image_provider_4_goods_selected_area_->img_ = MatToQImage(selected_mat);

                // cv::imshow("selected_mat", selected_mat);
                {
                    RecognitionWorker::processImgMat(selected_mat, device, model, result_list);
                    emit sigSendGoodsResult(result_list);
                }
            }
        },
        Qt::ConnectionType::DirectConnection);

    static auto weight_scale_ctrl = ControlManager::getInstance()->getWeightingScaleControl();

    static QTimer timer_scale;
    static bool   is_scale_timeout = false;
    timer_scale.setInterval(800);
    connect(&timer_scale, &QTimer::timeout, this,
            [&]
            {
                if (is_scale_timeout)
                    return;
                is_scale_timeout = true;
            });

    connect(weight_scale_ctrl, &WeightingScaleControl::sendWeight, this,
            [&]()
            {
                if (!ConfigTool::getInstance()->isUseRecognition())
                    return;

                if (weight_scale_ctrl->getLastWeight() <= 0)
                {
                    GoodsDataModel::goods_data_model_recognition_->clearGoods4Recognition();
                    return;
                }

                if (!is_scale_timeout)
                    return;
                is_scale_timeout = false;


                const auto weight_scale_ctrl = ControlManager::getInstance()->getWeightingScaleControl();

                int is_need_capture = 1;


                if (weight_scale_ctrl->isStable())
                {
                    if (CameraControl::goods_recognition_status_ == 0)
                    {
                        if (DataManager::getInstance()->getGoodsMgr()->barcode_tag_ratio_map_vec_.size() >= 3)
                        {
                            return;
                        }
                    }

                    CameraControl::goods_recognition_status_ = 0;
                }
                else
                {
                    CameraControl::goods_recognition_status_ = -1;
                }

                if (is_need_capture == 1)
                {
                    emit sigCaptureNow();
                }
            });

    connect(this, &CameraControl::sigSendGoodsResult, this,
            [&](std::vector<std::tuple<int, float>> result_list)
            {
                emit goodsImgChanged();
                LOG_OPT_WARN("识别结果 -begin");

                TagRatioVec tag_ratio_vec;

                {
                    Json result_json;

                    for (auto result_list_item : result_list)
                    {
                        Json json_item;

                        auto goods_name          = RecognitionWorker::class_names_[std::get<0>(result_list_item)];
                        json_item["goods_name"]  = goods_name;
                        auto goods_ratio         = std::get<1>(result_list_item);
                        json_item["goods_ratio"] = goods_ratio;

                        tag_ratio_vec.push_back(std::make_tuple(goods_name, goods_ratio));

                        result_json.push_back(json_item);
                    }

                    emit sigSendDebugList(QString::fromStdString(result_json.dump()));
                }

                //如果最匹配的是手指 丢弃
                if (!result_list.empty())
                {
                    auto goods_name = RecognitionWorker::class_names_[std::get<0>(*result_list.begin())];

                    if ("00figer" == goods_name)
                    {
                        return;
                    }
                }

                std::vector<std::tuple<string, float>> valid_goods_list;

                bool is_has_decided_goods = false;

                for (auto &[goods_name_index, goods_ratio] : result_list)
                {
                    if (goods_name_index >= RecognitionWorker::class_names_.size())
                        return;

                    auto goods_name = RecognitionWorker::class_names_[goods_name_index];

                    if (valid_goods_list.size() > 3)
                        break;

                    if (goods_ratio > .4)
                    {
                        valid_goods_list.push_back({goods_name, goods_ratio});
                        is_has_decided_goods = true;
                    }
                    else if (!is_has_decided_goods) //&& valid_goods_list.size() < 5)
                    {
                        valid_goods_list.emplace_back(goods_name, goods_ratio);
                    }
                }

                string tmp;
                for (auto &goods : valid_goods_list)
                {
                    tmp += fmt::format("valid_goods: {} {}\n", get<0>(goods), get<1>(goods));
                }
                if (tmp.length() > 0)
                {
                    tmp.pop_back();
                    LOG_OPT_INFO("\n{}", tmp);
                }

                if (GoodsDataModel::goods_data_model_recognition_ != nullptr)
                {
                    GoodsDataModel::goods_data_model_recognition_->refreshGoodsPtrMapByRecognition(valid_goods_list);
                }

                auto goods_mgr = DataManager::getInstance()->getGoodsMgr();
                goods_mgr->setTagRatioVec(tag_ratio_vec);


                LOG_OPT_WARN("识别结果 -end");

                // emit sigRecognitionChange();
            });

    connect(camera_worker_goods_, &CameraWorker2::sigWorkStart, this,
            [&]
            {
                setIsOpeningGoodsCamera(false);
                timer_scale.start();
            });
    connect(camera_worker_goods_, &CameraWorker2::sigWorkStop, this,
            [&]
            {
                setIsOpeningGoodsCamera(false);
                timer_scale.stop();
            });
    connect(camera_worker_goods_, &CameraWorker2::sigCameraError, this,
            [&]()
            {
                setIsOpeningGoodsCamera(false);
                timer_scale.stop();
                setIsGoodsRecognition(false);

                Utils4Qml::getInstance()->sendToast("相机错误", (int)EnumTool::ToastLevelEnum::TOAST_LEVEL__ERROR);
            });

    connect(camera_worker_goods_, &CameraWorker2::sigCameraOpened, this,
            [&]()
            {
                setIsGoodsCameraOpened(true);
            });
    connect(camera_worker_goods_, &CameraWorker2::sigCameraClosed, this,
            [&]()
            {
                setIsGoodsCameraOpened(false);
            });

    camera_worker_goods_->moveToThread(camera_worker_goods_thread_);
    camera_worker_goods_thread_->start();
    openGoodsCamera();
    //
    ///-------------------------------------------| 商品摄像头 |-------------------------------------------
#endif //_RECOGNITION_

}

void CameraControl::openFaceCamera(int camera_index)
{
    image_provider_face_->img_.fill(QColor(255, 255, 255));
    emit sigOpenFaceCamera(camera_4_face_);
}

void CameraControl::closeFaceCamera()
{
    emit sigCloseFaceCamera();
    emit sigSendFaceImg();
}

void CameraControl::openGoodsCamera()
{
    emit sigOpenGoodsCamera(camera_4_goods_);
}

void CameraControl::closeGoodsCamera()
{
    emit sigCloseGoodsCamera();
}

QImage CameraControl::getLastOriginalImg()
{
    return last_original_face_img_;
}

void CameraControl::refreshAvailableCameras()
{
    available_cameras_ = QCameraInfo::availableCameras();
}

QString CameraControl::getAvailableCameras()
{
    refreshAvailableCameras();

    QString result;
    Json    result_j;

    for (const auto &camera : available_cameras_)
    {
        result_j.push_back(camera.description().toStdString());
    }

    result = QString::fromStdString(result_j.dump());

    return result;
}

QString CameraControl::camera4Face()
{
    return camera_4_face_;
}

void CameraControl::setCamera4Face(const QString &c_name)
{
    camera_4_face_ = c_name;
    ConfigTool::getInstance()->setSetting(ConfigEnum::kCamera4Face, c_name);
    emit camera4FaceChanged();
}

QString CameraControl::camera4Goods()
{
    return camera_4_goods_;
}

void CameraControl::setCamera4Goods(const QString &c_name)
{
    camera_4_goods_ = c_name;
    ConfigTool::getInstance()->setSetting(ConfigEnum::kCamera4Goods, c_name);
    emit camera4GoodsChanged();

    closeGoodsCamera();
    openGoodsCamera();
}

bool CameraControl::isGoodsRecognition()
{
    return is_goods_recognition_;
}

void CameraControl::setIsGoodsRecognition(bool is_goods_recognition)
{
    is_goods_recognition_ = is_goods_recognition;
    emit isGoodsRecognitionChanged();

    setIsOpeningGoodsCamera(true);

    if (is_goods_recognition_)
    {
        vector<std::tuple<int, float>> result_list;

        emit sigSendGoodsResult(result_list);

        openGoodsCamera();
    }
    else
    {
        closeGoodsCamera();
    }
}

bool CameraControl::isOpeningGoodsCamera()
{
    return is_opening_goods_camera_;
}

void CameraControl::setIsOpeningGoodsCamera(bool is_opening)
{
    is_opening_goods_camera_ = is_opening;
    emit isOpeningGoodsCameraChanged();
}

bool CameraControl::isGoodsCameraOpened()
{
    return is_goods_camera_opened_;
}

void CameraControl::setIsGoodsCameraOpened(bool is_open)
{
    is_goods_camera_opened_ = is_open;
    emit isGoodsCameraOpenedChanged();
}

QImage CameraControl::MatToQImage(const cv::Mat &mat)
{
    if (mat.type() == CV_8UC1)
    {
        QImage image(mat.cols, mat.rows, QImage::Format_Indexed8);
        // Set the color table (used to translate colour indexes to qRgb values)
        image.setColorCount(256);
        for (int i = 0; i < 256; i++)
        {
            image.setColor(i, qRgb(i, i, i));
        }
        // Copy input Mat
        uchar *pSrc = mat.data;
        for (int row = 0; row < mat.rows; row++)
        {
            uchar *pDest = image.scanLine(row);
            memcpy(pDest, pSrc, mat.cols);
            pSrc += mat.step;
        }
        return image;
    }
    // 8-bits unsigned, NO. OF CHANNELS = 3
    else if (mat.type() == CV_8UC3)
    {
        // Copy input Mat
        const uchar *pSrc = (const uchar *)mat.data;
        // Create QImage with same dimensions as input Mat
        QImage image(pSrc, mat.cols, mat.rows, mat.step, QImage::Format_RGB888);
        return image.rgbSwapped();
    }
    else if (mat.type() == CV_8UC4)
    {
        // Copy input Mat
        const uchar *pSrc = (const uchar *)mat.data;
        // Create QImage with same dimensions as input Mat
        QImage image(pSrc, mat.cols, mat.rows, mat.step, QImage::Format_ARGB32);
        return image.copy();
    }
    else
    {
        // MessageInfo("ERROR: Mat could not be converted to QImage.", 1);
        // emit sig_RunInfo("ERROR: Mat could not be converted to QImage.", 1);
        // if (!globalPara.IsInlineRun) Runstateinfo("ERROR: Mat could not be converted to QImage.", 1);
        return QImage();
    }
}

Mat CameraControl::QImageToMat(QImage image)
{
    cv::Mat mat;
    switch (image.format())
    {
    case QImage::Format_ARGB32:
    case QImage::Format_RGB32:
    case QImage::Format_ARGB32_Premultiplied:
        mat = cv::Mat(image.height(), image.width(), CV_8UC4, (void *)image.constBits(), image.bytesPerLine());
        break;
    case QImage::Format_RGB888:
        mat = cv::Mat(image.height(), image.width(), CV_8UC3, (void *)image.constBits(), image.bytesPerLine());
        cv::cvtColor(mat, mat, COLOR_BGR2RGB);
        break;
    case QImage::Format_Indexed8:
        mat = cv::Mat(image.height(), image.width(), CV_8UC1, (void *)image.constBits(), image.bytesPerLine());
        break;
    }
    return mat;
}

void CameraControl::setRecognitionArea(int width, int height)
{
    recognition_area_.setWidth(width);
    recognition_area_.setHeight(height);
}

void CameraControl::setAnnotation(Annotation *annotation)
{
    annotation_ = annotation;
}

bool CameraControl::saveSelectedArea(cv::Mat src_img)
{
    if (annotation_ == nullptr)
    {
        return false;
    }

    if (recognition_area_.width() <= 0 && recognition_area_.height() <= 0)
    {
        return false;
    }

    auto draws = annotation_->getDraws();
    if (draws.size() != 1)
    {
        return false;
    }

    auto points = draws[0]->getPoints();
    if (points.size() != 4)
    {
        return false;
    }

    vector<cv::Point2f> result_ratio_vec;

    for (auto cur_point : points)
    {
        auto x_ratio = (double)cur_point.x() / recognition_area_.width();
        auto y_ratio = (double)cur_point.y() / recognition_area_.height();

        result_ratio_vec.push_back(cv::Point2f(x_ratio, y_ratio));
    }

    auto config_tool = ConfigTool::getInstance();

    config_tool->setRecognitionRatioP1(QPointF(result_ratio_vec[0].x, result_ratio_vec[0].y));
    config_tool->setRecognitionRatioP2(QPointF(result_ratio_vec[1].x, result_ratio_vec[1].y));
    config_tool->setRecognitionRatioP3(QPointF(result_ratio_vec[2].x, result_ratio_vec[2].y));
    config_tool->setRecognitionRatioP4(QPointF(result_ratio_vec[3].x, result_ratio_vec[3].y));

    return true;
}

bool CameraControl::getSelectedAreaMat(cv::Mat src_img, cv::Mat& out_img)
{
    auto config_tool = ConfigTool::getInstance();

    vector<vector<Point2i>> contours;

    double min_ratio_x = -1;
    double min_ratio_y = -1;
    double max_ratio_x = -1;
    double max_ratio_y = -1;

    std::vector<QPointF> points;

    points.push_back(config_tool->recognitionRatioP1());
    points.push_back(config_tool->recognitionRatioP2());
    points.push_back(config_tool->recognitionRatioP3());
    points.push_back(config_tool->recognitionRatioP4());

    vector<cv::Point>   result_points_vec;
    for (auto cur_point : points)
    {
        auto x_ratio = cur_point.x();
        auto y_ratio = cur_point.y();

        ///-------------------------------------------| 左上/右下 比例 |-------------------------------------------
        //
        if (min_ratio_x < 0 || x_ratio < min_ratio_x)
        {
            min_ratio_x = x_ratio;
        }
        if (min_ratio_y < 0 || y_ratio < min_ratio_y)
        {
            min_ratio_y = y_ratio;
        }
        if (max_ratio_x < 0 || x_ratio > max_ratio_x)
        {
            max_ratio_x = x_ratio;
        }
        if (max_ratio_y < 0 || y_ratio > max_ratio_y)
        {
            max_ratio_y = y_ratio;
        }
        //
        ///-------------------------------------------| 左上/右下 比例 |-------------------------------------------

        auto tmp_point = cv::Point2i(cur_point.x() * src_img.cols, cur_point.y() * src_img.rows);
        result_points_vec.push_back(tmp_point);
    }

    contours.push_back(result_points_vec);

    Mat mask = Mat::zeros(src_img.size(), CV_8UC1);

    drawContours(mask, contours, 0, Scalar::all(255), -1);
    src_img.copyTo(out_img, mask);

    if (points.size() != 4)
    {
        return false;
    }

    int min_x = src_img.cols * min_ratio_x;
    int min_y = src_img.rows * min_ratio_y;

    int max_x = src_img.cols * max_ratio_x;
    int max_y = src_img.rows * max_ratio_y;

    // 定义要裁剪的区域
    cv::Rect roi(min_x, min_y, max_x - min_x, max_y - min_y);

    // 裁剪图片
    out_img = out_img(roi);
    //
    ///-------------------------------------------| 根据比例计算图片中实际的点 |-------------------------------------------
    // imshow("out_img", out_img);

    return true;
}

void CameraControl::setSelectedArea()
{
    cv::Mat src_img = QImageToMat(image_provider_4_goods_->img_);
    cv::Mat out_img;
    saveSelectedArea(src_img);
    getSelectedAreaMat(src_img, out_img);
}

