﻿#include "BarcodeLabelScale.h"
#include <QDir>
#include <QFile>
#include <QLibrary>
#include <QProcess>
#include <QTextCodec>
#include <fstream>
#include <libloaderapi.h>
#include <string>
#include <synchapi.h>
#include "ConfModule/ConfigTool.h"
#include "ControlModule/ControlManager.h"
#include "DataModule/DataManager.h"
#include "LogManager.h"
#include "Utils/Utils.h"
#include "pugixml/pugixml.hpp"
#include "qdatetime.h"
#include "qfileinfo.h"
#include "qglobal.h"

using namespace std;
using namespace pugi;
#pragma optimize("", off)
BarcodeLabelScale::BarcodeLabelScale(QObject *parent) : QObject{parent}
{
    auto app_dir_path = Utils::getAppDirPath();

    QString lib_path_dahua = app_dir_path + "/DhScalePluNet.dll";
    lib_dahua_.setFileName(lib_path_dahua);
    lib_dahua_.load();
    if (!lib_dahua_.isLoaded())
    {
        LOG_EVT_ERROR("大华库加载失败");
    }
    else
    {
        LOG_EVT_INFO("大华库加载成功");
    }

    QString lib_path_toledo = app_dir_path + "/MTScaleAPI.dll";
    lib_toledo_.setFileName(lib_path_toledo);
    lib_toledo_.load();
    if (!lib_toledo_.isLoaded())
    {
        LOG_EVT_ERROR("托利多库加载失败");
    }
    else
    {
        LOG_EVT_INFO("托利多库加载成功");
    }

    h_module_dingjian_ = LoadLibrary(L"AclasSDK.dll");
    if (!h_module_dingjian_)
    {
        LOG_EVT_ERROR("顶尖库加载失败");
    }
    else
    {
        LOG_EVT_INFO("顶尖库加载成功");
    }

    barcode_scale_value_map_.insert(make_pair(BarcodeScaleEnum::BARCODE_LABLE_SCALE_DAHUA, tr("大华条码秤")));
    barcode_scale_value_map_.insert(make_pair(BarcodeScaleEnum::BARCODE_LABLE_SCALE_DING_JIAN_AI, tr("顶尖AI条码秤")));
    barcode_scale_value_map_.insert(make_pair(BarcodeScaleEnum::BARCODE_LABLE_SCALE_TOLEDO, tr("托利多条码秤")));

    initBarcodeScaleInfo();

    barcode_weight_prefix_ = ConfigTool::getInstance()->getSetting(ConfigEnum::BAR_CODE_WEIGHING_PRE_TWO_CODE).toString();
}

void BarcodeLabelScale::initBarcodeScaleInfo()
{
    auto json_var              = ConfigTool::getInstance()->getSetting(ConfigToolEnum::ConfigEnum::BARCODE_LABEL_SCALE_INFO_JSON);
    auto barcode_info_json_str = json_var.toString().toStdString();
    if (Json::accept(barcode_info_json_str))
    {
        Json json_doc = Json::parse(barcode_info_json_str);
        barcode_scale_vec_.clear();
        if (json_doc.is_array())
        {
            for (auto json_item : json_doc)
            {
                BarcodeScaleItem barcode_scale_item = json_item.get<BarcodeScaleItem>();
                barcode_scale_vec_.push_back(barcode_scale_item);
            }
        }
    }
}

void BarcodeLabelScale::saveBarcodeScaleInfo()
{
    Json result_json;
    for (auto barcode_scale : barcode_scale_vec_)
    {
        Json json_tmp = barcode_scale;
        result_json.push_back(json_tmp);
    }
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::BARCODE_LABEL_SCALE_INFO_JSON, QString::fromStdString(result_json.dump()));
}

bool BarcodeLabelScale::getIsTransporting()
{
    return is_transporting_;
}

bool BarcodeLabelScale::send2BarcodeScaleWizard()
{
    is_transporting_ = true;
    emit sigIsTransportingChanged();
    LOG_EVT_INFO("开始发送到秤函数处理");
    try
    {
        std::vector<NetInfoObj> dahua_ip_vec;
        std::vector<NetInfoObj> dingjian_ai_ip_vec;
        std::vector<NetInfoObj> toledo_ip_vec;
        for (const auto &barcode_scale_vec_item : barcode_scale_vec_)
        {
            if (!barcode_scale_vec_item.is_enabled)
            {
                is_transporting_ = false;
                emit sigIsTransportingChanged();
                return false;
            }
            if (!Utils::Network::isValidIp(barcode_scale_vec_item.net_info.ip))
            {
                LOG_OPT_ERROR("IP无效:{} port:{} 秤类型{}", barcode_scale_vec_item.net_info.ip.toStdString(), barcode_scale_vec_item.net_info.port,
                              barcode_scale_value_map_[barcode_scale_vec_item.barcode_scale_type].toStdString());
                is_transporting_ = false;
                emit sigIsTransportingChanged();
                return false;
            }
            switch (barcode_scale_vec_item.barcode_scale_type)
            {
            case BarcodeScaleEnum::BARCODE_LABLE_SCALE_DAHUA:
                dahua_ip_vec.push_back(barcode_scale_vec_item.net_info);
                break;
            case BarcodeScaleEnum::BARCODE_LABLE_SCALE_DING_JIAN_AI:
                dingjian_ai_ip_vec.push_back(barcode_scale_vec_item.net_info);
                break;
            case BarcodeScaleEnum::BARCODE_LABLE_SCALE_TOLEDO:
                toledo_ip_vec.push_back(barcode_scale_vec_item.net_info);
                break;
            default:
                is_transporting_ = false;
                emit sigIsTransportingChanged();
                return false;
            }
        }
        if (dahua_ip_vec.size())
        {
            LOG_EVT_INFO("大华秤任务");
            if (!setConfigFiles_DaHua(dahua_ip_vec))
            {
                is_transporting_ = false;
                emit sigIsTransportingChanged();
                return false;
            }
            if (!send2DaHuaScaleWizard())
            {
                is_transporting_ = false;
                emit sigIsTransportingChanged();
                return false;
            }
        }
        if (dingjian_ai_ip_vec.size())
        {
            LOG_EVT_INFO("顶尖秤任务");
            if (!send2DingJianAiScaleWizard(dingjian_ai_ip_vec))
            {
                is_transporting_ = false;
                emit sigIsTransportingChanged();
                return false;
            }
        }

        if (toledo_ip_vec.size())
        {
            LOG_EVT_INFO("托利多秤任务");
            createToledoXmlPath();
            if (!send2ToledoScaleWizard(toledo_ip_vec))
            {
                is_transporting_ = false;
                emit sigIsTransportingChanged();
                return false;
            }
        }
        is_transporting_ = false;
        emit sigIsTransportingChanged();
        return true;
    }
    catch (...)
    {
        SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_evt_, "发送到秤错误!");
        return false;
    }
}

bool BarcodeLabelScale::sendSingleGoods2BarcodeScaleWizard(QString goods_barcode)
{
    return false;

    try
    {
        std::vector<NetInfoObj> dahua_ip_vec;
        std::vector<NetInfoObj> dingjian_ai_ip_vec;
        std::vector<NetInfoObj> toledo_ip_vec;

        for (const auto &barcode_scale_vec_item : barcode_scale_vec_)
        {

            if (!barcode_scale_vec_item.is_enabled)
                continue;

            if (!Utils::Network::isValidIp(barcode_scale_vec_item.net_info.ip))
            {
                SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_evt_, "IP无效:{} 秤类型{}", barcode_scale_vec_item.net_info.ip.toStdString(),
                                    barcode_scale_value_map_[barcode_scale_vec_item.barcode_scale_type].toStdString());
                continue;
            }

            switch (barcode_scale_vec_item.barcode_scale_type)
            {
            case BarcodeScaleEnum::BARCODE_LABLE_SCALE_DAHUA:
                dahua_ip_vec.push_back(barcode_scale_vec_item.net_info);
                break;
            case BarcodeScaleEnum::BARCODE_LABLE_SCALE_DING_JIAN_AI:
                dingjian_ai_ip_vec.push_back(barcode_scale_vec_item.net_info);
                break;
            case BarcodeScaleEnum::BARCODE_LABLE_SCALE_TOLEDO:
                toledo_ip_vec.push_back(barcode_scale_vec_item.net_info);
                break;
            default:
                return false;
            }
        }

        if (dahua_ip_vec.size())
        {
            if (!setConfigFiles_DaHua(dahua_ip_vec))
            {
                return false;
            }
            if (!sendSingleGoods2DaHuaScaleWizard(goods_barcode))
            {
                return false;
            }
        }

        if (dingjian_ai_ip_vec.size())
        {
            if (!send2DingJianAiScaleSingleGoodsWizard(dingjian_ai_ip_vec, goods_barcode))
                return false;
        }

        if (toledo_ip_vec.size())
        {
            if (!send2ToledoScaleSingleGoodsWizard(dingjian_ai_ip_vec, goods_barcode))
                return false;
        }

        return true;
    }
    catch (...)
    {
        SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_evt_, "发送到秤错误!");
    }
}

QVariant BarcodeLabelScale::getBarcodeScaleTypeListJson()
{
    Json json_result;

    for (auto barcode_scale_item : barcode_scale_value_map_)
    {
        Json json_item;
        json_item["scale_type_key"]  = (int)barcode_scale_item.first;
        json_item["scale_type_name"] = barcode_scale_item.second.toStdString();
        json_result.push_back(json_item);
    }

    return QString::fromStdString(json_result.dump());
}

QVariant BarcodeLabelScale::getBarcodeScaleListJson()
{
    Json json_result;

    for (auto barcode_scale_item : barcode_scale_vec_)
    {
        Json tmp = barcode_scale_item;
        json_result.push_back(tmp);
    }
    return QString::fromStdString(json_result.dump());
}

QVariant BarcodeLabelScale::addBarcodeScaleItem()
{
    BarcodeScaleItem barcode_scale_item;
    barcode_scale_vec_.push_back(barcode_scale_item);
    Json json_tmp = barcode_scale_item;
    saveBarcodeScaleInfo();
    return QString::fromStdString(json_tmp.dump());
}

bool BarcodeLabelScale::delBarcodeScaleItemByTimestamp(QVariant timestamp)
{
    for (auto barcode_scale_vec_iter = barcode_scale_vec_.begin(); barcode_scale_vec_iter != barcode_scale_vec_.end(); ++barcode_scale_vec_iter)
    {
        if (barcode_scale_vec_iter->timestamp == timestamp.toString())
        {
            barcode_scale_vec_.erase(barcode_scale_vec_iter);
            saveBarcodeScaleInfo();
            return true;
        }
    }
    return false;
}

bool BarcodeLabelScale::setBarcodeScaleItemEnableByTimestamp(QVariant timestamp, QVariant is_enable)
{
    for (auto barcode_scale_vec_iter = barcode_scale_vec_.begin(); barcode_scale_vec_iter != barcode_scale_vec_.end(); ++barcode_scale_vec_iter)
    {
        if (barcode_scale_vec_iter->timestamp == timestamp.toString())
        {
            barcode_scale_vec_iter->is_enabled = is_enable.toBool();
            saveBarcodeScaleInfo();
            return true;
        }
    }
    return false;
}

bool BarcodeLabelScale::setBarcodeScaleItemScaleTypeByTimestamp(QVariant timestamp, QVariant scale_type)
{
    auto ret_iter = std::find_if(barcode_scale_vec_.begin(), barcode_scale_vec_.end(),
                                 [&](const BarcodeScaleItem &barcode_scale_item)
                                 {
                                     return barcode_scale_item.timestamp == timestamp.toString();
                                 });

    if (ret_iter != barcode_scale_vec_.end())
    {
        ret_iter->barcode_scale_type = static_cast<BarcodeScaleEnum>(scale_type.toInt());
        saveBarcodeScaleInfo();
        return true;
    }

    return false;
}

bool BarcodeLabelScale::setBarcodeScaleItemIpByTimestamp(QVariant timestamp, QVariant scale_ip)
{
    auto ret_iter = std::find_if(barcode_scale_vec_.begin(), barcode_scale_vec_.end(),
                                 [&](const BarcodeScaleItem &barcode_scale_item)
                                 {
                                     return barcode_scale_item.timestamp == timestamp.toString();
                                 });

    if (ret_iter != barcode_scale_vec_.end())
    {
        ret_iter->net_info.ip = scale_ip.toString();
        saveBarcodeScaleInfo();
        return true;
    }
    return false;
}

bool BarcodeLabelScale::setBarcodeScaleItemPortByTimestamp(QVariant timestamp, QVariant scale_port)
{
    auto ret_iter = std::find_if(barcode_scale_vec_.begin(), barcode_scale_vec_.end(),
                                 [&](const BarcodeScaleItem &barcode_scale_item)
                                 {
                                     return barcode_scale_item.timestamp == timestamp.toString();
                                 });

    if (ret_iter != barcode_scale_vec_.end())
    {
        ret_iter->net_info.port = scale_port.toInt();
        saveBarcodeScaleInfo();
        return true;
    }
    return false;
}

bool BarcodeLabelScale::send2DaHuaScaleWizard4Qml(QJSValue callback)
{
    if (!callback.isCallable())
        return false;

    if (send2DaHuaScaleWizard())
    {
        QJSValueList arglist;
        arglist.push_back(true);
        callback.call(arglist);
    }
    else
    {
        QJSValueList arglist;
        arglist.push_back(false);
        callback.call(arglist);
    }

    return true;
}

bool BarcodeLabelScale::send2DaHuaScaleWizard()
{
    if (!clearPluFiles_DaHua())
        return false;
    if (!saveAllPlu2File_DaHua())
        return false;
    if (!sendPluFile2Scale_DaHua())
        return false;
    return true;
}

bool BarcodeLabelScale::sendSingleGoods2DaHuaScaleWizard(QString goods_barcode)
{
    if (!clearPluFiles_DaHua())
        return false;
    if (!saveSingleGoodsPlu2File_DaHua(goods_barcode))
        return false;
    if (!sendPluFile2Scale_DaHua())
        return false;
    return true;
}

bool BarcodeLabelScale::setConfigFiles_DaHua(std::vector<NetInfoObj> ip_vec)
{
    const auto app_dir_path = Utils::getAppDirPath();

    {
        fstream stream_ip;
        stream_ip.open((app_dir_path).toStdString() + "/dhip.ini", ios_base::in | ios_base::out | ios_base::trunc);

        QString prefix_str =
            R"(秤型说明:	1 代表	TMBZ_5位单价条码秤
		2 代表	TMBZ_6位单价条码秤
		3 代表	TMA_07年新版条码秤
每个IP地址后面的对应该秤秤型,以空格或Tab格开

IP地址		秤型
)";
        stream_ip << prefix_str.toLocal8Bit().toStdString();


        for (auto ip_vec_item : ip_vec)
        {
            stream_ip << ip_vec_item.ip.toStdString() << "\t3"
                      << "\n";
        }

        stream_ip.close();
    }

    //*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\

    {
        fstream stream_plu_path;
        stream_plu_path.open((app_dir_path).toStdString() + "/dhplupathname.ini", ios_base::in | ios_base::out | ios_base::trunc);

        QString prefix_str_plu_path =
            R"(PLU路径说明:	在下面的_PLU_PATH_后写入要下载的PLU路径名和文件名

_PLU_PATH_	)";

        auto full_str = prefix_str_plu_path.toLocal8Bit().toStdString() + (app_dir_path).toLocal8Bit().toStdString() + "/" +
            plu_file_name__da_hua.toLocal8Bit().toStdString();

        stream_plu_path << full_str;
        stream_plu_path.close();
    }

    return true;
}

bool BarcodeLabelScale::clearPluFiles_DaHua()
{
    auto  app_dir_path = Utils::getAppDirPath();
    QFile file(app_dir_path + "/" + plu_file_name__da_hua);
    if (!file.open(QIODevice::ReadWrite | QIODevice::Text | QIODevice::Truncate))
    {
        LOG_OPT_ERROR("PLU文件创建失败");
        return false;
    }
    else
    {
        LOG_OPT_INFO("PLU文件创建成功");
    }
    file.close();
    return true;
}

bool BarcodeLabelScale::saveAllPlu2File_DaHua()
{
    QString result_plu;
    auto    goods_data = DataManager::getInstance()->getGoodsMgr();
    for (const auto &[goods_key, goods_item] : *goods_data->getGoodsData())
    {
        if ((goods_item.goods_barcode.left(2) == barcode_weight_prefix_) && (goods_item.goods_barcode.length() == 7))
        {
            auto plu_tmp = goods_item.goods_barcode.right(plu_digit_);
            result_plu += generatePluStr_DaHua(plu_tmp, goods_item.goods_barcode, QString::number(goods_item.goods_sale_price), barcode_weight_prefix_,
                                               goods_item.goods_name, QString::number(goods_item.goodsChengType)) +
                "\n";
        }
    }

    auto  app_dir_path = Utils::getAppDirPath();
    QFile file(app_dir_path + "/" + plu_file_name__da_hua);
    if (!file.open(QIODevice::ReadWrite | QIODevice::Text | QIODevice::Append))
    {
        LOG_OPT_ERROR("PLU文件创建失败");
        return false;
    }
    LOG_OPT_INFO("PLU文件创建成功");

    //INFO 要用ANSI编码!
    QTextCodec *utf8       = QTextCodec::codecForName("UTF-8");
    QTextCodec *gbk        = QTextCodec::codecForName("gbk");
    QString     strUnicode = utf8->toUnicode(result_plu.toLocal8Bit().data());
    //2. unicode -> gbk, 得到QByteArray
    QByteArray gb_bytes = gbk->fromUnicode(strUnicode);
    char      *p        = gb_bytes.data(); //获取其char *

    file.write(p);
    file.close();
    return true;
}

bool BarcodeLabelScale::saveSingleGoodsPlu2File_DaHua(QString goods_barcode)
{
    QString result_plu;
    auto    goods_data = DataManager::getInstance()->getGoodsMgr();
    auto    all_goods  = goods_data->getGoodsData();

    auto iter_tmp = all_goods->find(goods_barcode);

    if (iter_tmp == all_goods->end())
        return false;

    if ((iter_tmp->second.goods_barcode.left(2) == barcode_weight_prefix_) && (iter_tmp->second.goods_barcode.length() == 7))
    {
        auto plu_tmp = iter_tmp->second.goods_barcode.right(plu_digit_);
        result_plu += generatePluStr_DaHua(plu_tmp, iter_tmp->second.goods_barcode, QString::number(iter_tmp->second.goods_sale_price), barcode_weight_prefix_,
                                           iter_tmp->second.goods_name, QString::number(iter_tmp->second.goodsChengType)) +
            "\n";
    }

    auto  app_dir_path = Utils::getAppDirPath();
    QFile file(app_dir_path + "/" + plu_file_name__da_hua);
    if (!file.open(QIODevice::ReadWrite | QIODevice::Text | QIODevice::Append))
    {
        SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_opt_, "PLU文件创建失败");
        return false;
    }

    LOG_OPT_INFO("PLU文件创建成功");

    //INFO 要用ANSI编码!
    QTextCodec *utf8       = QTextCodec::codecForName("UTF-8");
    QTextCodec *gbk        = QTextCodec::codecForName("gbk");
    QString     strUnicode = utf8->toUnicode(result_plu.toLocal8Bit().data());
    //2. unicode -> gbk, 得到QByteArray
    QByteArray gb_bytes = gbk->fromUnicode(strUnicode);
    char      *p        = gb_bytes.data(); //获取其char *

    //INFO 要用ANSI编码!
    file.write(p);
    file.close();
    return true;
}

void BarcodeLabelScale::createToledoData()
{
    xml_document doc;

    pugi::xml_node decl               = doc.prepend_child(pugi::node_declaration);
    decl.append_attribute("version")  = "1.0";
    decl.append_attribute("encoding") = "UTF-8";

    auto node_Data = doc.append_child("Data");

    auto goods_data = DataManager::getInstance()->getGoodsMgr();

    for (const auto &[goods_key, goods_item] : *goods_data->getGoodsData())
    {
        if (goods_item.goods_barcode.left(2) == barcode_weight_prefix_ && goods_item.goods_barcode.length() == 7)
        {
            auto node_Item = node_Data.append_child("Item");

            auto plu_tmp = goods_item.goods_barcode.right(plu_digit_).toInt();

            node_Item.append_child("PLU").text().set(plu_tmp);

            node_Item.append_child("AlternativeItemIDs").append_child("AlternativeItemID").text().set(plu_tmp);

            {
                auto node_Descriptions = node_Item.append_child("Descriptions");

                auto node_Description                         = node_Descriptions.append_child("Description");
                node_Description.append_attribute("ID")       = "0";
                node_Description.append_attribute("Language") = "zho";
                node_Description.append_attribute("Type")     = "ItemName";
                node_Description.append_attribute("Index")    = "0";
                node_Description.text().set(goods_item.goods_name.toUtf8().data());

                auto node_Description2                         = node_Descriptions.append_child("Description");
                node_Description2.append_attribute("ID")       = "1";
                node_Description2.append_attribute("Language") = "zho";
                node_Description2.append_attribute("Type")     = "ExtraText";
            }


            //node_Item.append_child("CategoryIDs").append_child("CategoryID").text().set(0);

            //node_Item.append_child("ItemGroupID").text().set(plu_tmp);

            //node_Item.append_child("Tares").append_child("TareID").text().set(plu_tmp);


            {
                auto node_ItemPrices = node_Item.append_child("ItemPrices");

                auto node_ItemPrice = node_ItemPrices.append_child("ItemPrice");

                node_ItemPrice.append_attribute("Index")             = 0;
                node_ItemPrice.append_attribute("UnitOfMeasureCode") = "KGM";
                node_ItemPrice.append_attribute("PriceOverrideFlag") = "true";
                node_ItemPrice.append_attribute("DiscountFlag")      = "true";
                // node_ItemPrice.append_attribute("Quantity") = "0";
                node_ItemPrice.append_attribute("Currency") = "CNY";
                node_ItemPrice.text().set(goods_item.goods_sale_price);
            }

            //node_Item.append_child("Taxes").append_child("TaxRuleID").text().set(1);

            //{
            //    auto node_Dates = node_Item.append_child("Dates");

            //    {
            //        auto node_DateOffset                             = node_Dates.append_child("DateOffset");
            //        node_DateOffset.append_attribute("Type")         = "BestBefore";
            //        node_DateOffset.append_attribute("UnitOfOffset") = "day";
            //        node_DateOffset.append_attribute("PrintFormat")  = "MMDD";
            //        node_DateOffset.text().set(1);
            //    }

            //    {
            //        auto node_DateOffset                             = node_Dates.append_child("DateOffset");
            //        node_DateOffset.append_attribute("Type")         = "SellBy";
            //        node_DateOffset.append_attribute("UnitOfOffset") = "day";
            //        node_DateOffset.append_attribute("PrintFormat")  = "DDMMYY";
            //        node_DateOffset.text().set(2);
            //    }

            //    {
            //        auto node_DateOffset                             = node_Dates.append_child("DateOffset");
            //        node_DateOffset.append_attribute("Type")         = "PackedDate";
            //        node_DateOffset.append_attribute("UnitOfOffset") = "date";
            //        node_DateOffset.append_attribute("PrintFormat")  = "YYMMDD";
            //        node_DateOffset.text().set("2014-11-11");
            //    }
            //}

            //node_Item.append_child("TraceabilityFlag").text().set(false);


            {
                auto node_LabelFormats = node_Item.append_child("LabelFormats");

                {
                    auto node_LabelFormatID                      = node_LabelFormats.append_child("LabelFormatID");
                    node_LabelFormatID.append_attribute("Index") = "0";
                    node_LabelFormatID.text().set(1);
                }

                //{
                //    auto node_LabelFormatID                      = node_LabelFormats.append_child("LabelFormatID");
                //    node_LabelFormatID.append_attribute("Index") = "1";
                //    node_LabelFormatID.text().set(11);
                //}
            }

            //node_Item.append_child("Barcodes").append_child("BarcodeID").text().set(2);

            //{
            //    auto node_FixedQuantity = node_Item.append_child("FixedQuantity");
            //    node_FixedQuantity.append_attribute("UnitOfMeasureCode") = "KGM";
            //    node_FixedQuantity.append_attribute("EntryRequiredFlag") = "false";
            //    node_FixedQuantity.text().set("10.000000");
            //}
        }
    }

    if (!doc.save_file((getToledoXmlPath() + "/" + "Data1.xml").toUtf8().data()))
    {
        LOG_OPT_ERROR("托利多 Data1.xml 保存失败");
    }
}

bool BarcodeLabelScale::createToledoDataSingle(QString goods_barcode)
{
    xml_document doc;

    pugi::xml_node decl               = doc.prepend_child(pugi::node_declaration);
    decl.append_attribute("version")  = "1.0";
    decl.append_attribute("encoding") = "UTF-8";

    auto node_Data = doc.append_child("Data");

    auto goods_data = DataManager::getInstance()->getGoodsMgr();

    for (const auto &[goods_key, goods_item] : *goods_data->getGoodsData())
    {
        //if (goods_item.goods_barcode.left(2) == barcode_weight_prefix_ && goods_item.goods_barcode.length() == 7)
        if (goods_item.goods_barcode == goods_barcode)
        {
            auto node_Item = node_Data.append_child("Item");
            auto plu_tmp   = goods_item.goods_barcode.right(plu_digit_).toInt();

            node_Item.append_child("PLU").text().set(plu_tmp);
            node_Item.append_child("AlternativeItemIDs").append_child("AlternativeItemID").text().set(plu_tmp);

            {
                auto node_Descriptions = node_Item.append_child("Descriptions");

                auto node_Description                         = node_Descriptions.append_child("Description");
                node_Description.append_attribute("ID")       = "0";
                node_Description.append_attribute("Language") = "zho";
                node_Description.append_attribute("Type")     = "ItemName";
                node_Description.append_attribute("Index")    = "0";
                node_Description.text().set(goods_item.goods_name.toUtf8().data());

                auto node_Description2                         = node_Descriptions.append_child("Description");
                node_Description2.append_attribute("ID")       = "1";
                node_Description2.append_attribute("Language") = "zho";
                node_Description2.append_attribute("Type")     = "ExtraText";
            }

            {
                auto node_ItemPrices = node_Item.append_child("ItemPrices");
                auto node_ItemPrice  = node_ItemPrices.append_child("ItemPrice");

                node_ItemPrice.append_attribute("Index")             = 0;
                node_ItemPrice.append_attribute("UnitOfMeasureCode") = "KGM";
                node_ItemPrice.append_attribute("PriceOverrideFlag") = "true";
                node_ItemPrice.append_attribute("DiscountFlag")      = "true";
                node_ItemPrice.append_attribute("Currency")          = "CNY";
                node_ItemPrice.text().set(goods_item.goods_sale_price);
            }

            {
                auto node_LabelFormats = node_Item.append_child("LabelFormats");

                {
                    auto node_LabelFormatID                      = node_LabelFormats.append_child("LabelFormatID");
                    node_LabelFormatID.append_attribute("Index") = "0";
                    node_LabelFormatID.text().set(1);
                }
            }

            break;
        }
    }

    if (!doc.save_file((getToledoXmlPath() + "/" + "Data1.xml").toUtf8().data()))
    {
        LOG_OPT_ERROR("托利多 Data1.xml 保存失败");
    }
    return true;
}

void BarcodeLabelScale::createToledoCommand()
{
    xml_document doc;

    pugi::xml_node decl               = doc.prepend_child(pugi::node_declaration);
    decl.append_attribute("version")  = "1.0";
    decl.append_attribute("encoding") = "UTF-8";

    auto node_Command = doc.append_child("Commands").append_child("Command");

    node_Command.append_child("CommandText").text().set("Item");
    node_Command.append_child("CommandID").text().set("1392f2df-e76b-46bf-9ff2-46bbc8e71b93");
    node_Command.append_child("Control").text().set("Update");
    node_Command.append_child("ClearData").text().set("false");
    node_Command.append_child("DataFile").text().set("Data1.xml");

    if (!doc.save_file((getToledoXmlPath() + "/" + "Command1.xml").toUtf8().data()))
    {
        LOG_OPT_ERROR("托利多 Command1.xml 保存失败");
    }
}

//TODO 优化多种秤类型
void BarcodeLabelScale::createToledoDeviceList(std::vector<NetInfoObj> ip_vec)
{
    xml_document doc;

    pugi::xml_node decl               = doc.prepend_child(pugi::node_declaration);
    decl.append_attribute("version")  = "1.0";
    decl.append_attribute("encoding") = "UTF-8";

    auto node_Devices = doc.append_child("Devices");

    int scale_index = 0;

    for (auto cur_ip : ip_vec)
    {
        ++scale_index;

        auto node_Scale = node_Devices.append_child("Scale");

        node_Scale.append_child("DeviceID").text().set(scale_index);
        node_Scale.append_child("ScaleNo").text().set(scale_index);
        node_Scale.append_child("ScaleType").text().set("bCom");
        node_Scale.append_child("ConnectType").text().set("Network");

        {
            auto node_ConnectParams = node_Scale.append_child("ConnectParams");

            auto node_NetworkParams                        = node_ConnectParams.append_child("NetworkParams");
            node_NetworkParams.append_attribute("Type")    = "Network";
            node_NetworkParams.append_attribute("Address") = cur_ip.ip.toUtf8().data();
            node_NetworkParams.append_attribute("Port")    = cur_ip.port;
        }

        node_Scale.append_child("DecimalDigits").text().set(2);
        node_Scale.append_child("DataFile").text().set("Command1.xml");
    }

    if (!doc.save_file((getToledoXmlPath() + "/" + "DeviceList.xml").toUtf8().data()))
    {
        LOG_OPT_ERROR("托利多 DeviceList.xml 保存失败");
    }
}

void BarcodeLabelScale::createToledoTask()
{
    auto task_name = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");

    task_name_tmp_ = task_name;

    xml_document doc;

    auto node_MTTask = doc.append_child("MTTask");

    node_MTTask.append_child("TaskID").text().set(task_name.toStdString().c_str());
    node_MTTask.append_child("TaskType").text().set(0);
    node_MTTask.append_child("DataFile").text().set("DeviceList.xml");

    if (!doc.save_file((getToledoXmlPath() + "/" + "Task.xml").toUtf8().data()))
    {
        LOG_OPT_ERROR("托利多 Task.xml 保存失败");
    }
}

bool BarcodeLabelScale::execToledoTask()
{
    if (lib_toledo_.isLoaded()) //判断是否正确加载
    {
        LOG_OPT_INFO("MTScaleAPI.dll 加载成功");

        ExecuteTaskInFileApi ExecuteTaskInFile = (ExecuteTaskInFileApi)lib_toledo_.resolve("ExecuteTaskInFile");
        QueryTaskApi         QueryTask         = (QueryTaskApi)lib_toledo_.resolve("QueryTask");

        QString tmp_str = (getToledoXmlPath() + "/Task.xml");

        static auto tmp_str_std = tmp_str.toStdString();

        static const char *task_xml_path = tmp_str_std.c_str();

        //const char *task_xml_path = tmp_str.toUtf8().data();
        //xml_document doc;
        //doc.load_file((getToledoXmlPath() + "/" + "Command1.xml").toUtf8().data());

        if (ExecuteTaskInFile)
        {
            static auto        tmp_std_str = task_name_tmp_.toStdString();
            static const char *task_id     = tmp_std_str.c_str();

            bool result = ExecuteTaskInFile(task_id, task_xml_path, "toledo_result.txt", true);
            LOG_OPT_INFO("发送到托利多称: id:{} task:{}", task_id, task_xml_path);

            if (result == true)
            {
                LOG_OPT_INFO("托利多秤数据已发送\n返回数据\n{}\n", QueryTask(task_id));
            }
            else
            {
                LOG_OPT_ERROR("托利多秤数据发送失败");
                return false;
            }
        }
        else
        {
            LOG_OPT_ERROR("ExecuteTaskInFile 函数解析失败");
            return false;
        }
    }
    else
    {
        LOG_OPT_ERROR("MTScaleAPI.dll 加载失败");
        return false;
    }
    return true;
}

bool BarcodeLabelScale::send2DingJianAiScaleWizard(std::vector<NetInfoObj> ip_vec)
{
    if (!saveAllPlu2File_DingJianAi())
        return false;

    if (sendPluFile2Scale_DingJianAiVec(ip_vec) > 0)
        return false;

    return true;
}

bool BarcodeLabelScale::send2DingJianAiScaleSingleGoodsWizard(std::vector<NetInfoObj> ip_vec, QString goods_barcode)
{
    return false;

    if (!saveSinglePlu2File_DingJianAi(goods_barcode))
        return false;

    if (sendPluFile2Scale_DingJianAiVec(ip_vec) > 0)
        return false;

    return true;
}

bool BarcodeLabelScale::saveAllPlu2File_DingJianAi()
{
    auto  app_dir = Utils::getAppDirPath();
    QFile file_info(app_dir + "/" + ding_jian_ai_plu_name);
    if (!file_info.open(QIODevice::ReadWrite | QIODevice::Text | QIODevice::Truncate))
        return false;
    file_info.resize(0);

    QTextCodec* codec = QTextCodec::codecForName("gb2312");
    // QTextCodec* codec = QTextCodec::codecForName("Windows-1252");
    // QTextCodec* codec = QTextCodec::codecForName("GBK");

    QTextStream file_stream(&file_info);
    file_stream.setCodec(codec);
    file_stream << "ID\tItemCode\tDepartmentID\tGroupID\tName1\tPrice\tBarcodeType1\tUnitID\n";

    auto goods_data = DataManager::getInstance()->getGoodsMgr();

    QString result_plu;
    for (const auto &[goods_key, goods_item] : *goods_data->getGoodsData())
    {
        if ((goods_item.goods_barcode.left(2) == barcode_weight_prefix_) && (goods_item.goods_barcode.length() == 7))
        {
            QString goods_name    = goods_item.goods_name;
            QString goods_barcode = goods_item.goods_barcode;
            auto    goods_price   = goods_item.goods_sale_price;

            file_stream << goods_barcode << "\t" << goods_barcode.right(5) << "\t" << goods_barcode.left(2) << "\t"
                        << "1"
                        << "\t" << goods_name << "\t" << goods_price << "\t"
                        << "02"
                        << "\t"
                        << "4"
                        << "\n";
        }
    }
    file_info.close();
    return true;
}

bool BarcodeLabelScale::saveSinglePlu2File_DingJianAi(QString goods_barcode)
{
    auto  app_dir = Utils::getAppDirPath();
    QFile file_info(app_dir + "/" + ding_jian_ai_plu_name);
    if (!file_info.open(QIODevice::ReadWrite | QIODevice::Text | QIODevice::Truncate))
        return false;
    file_info.resize(0);
    QTextStream file_stream(&file_info);
    file_stream << "ID\tItemCode\tDepartmentID\tGroupID\tName1\tPrice\tBarcodeType1\tUnitID\n";

    auto goods_data = DataManager::getInstance()->getGoodsMgr();

    QString result_plu;

    auto all_goods = goods_data->getGoodsData();

    auto iter_tmp = all_goods->find(goods_barcode);

    if (iter_tmp != all_goods->end())

        if ((iter_tmp->second.goods_barcode.left(2) == barcode_weight_prefix_) && (iter_tmp->second.goods_barcode.length() == 7))
        {
            QString goods_name    = iter_tmp->second.goods_name;
            QString goods_barcode = iter_tmp->second.goods_barcode;
            auto    goods_price   = iter_tmp->second.goods_sale_price;

            file_stream << goods_barcode << "\t" << goods_barcode.right(5) << "\t" << goods_barcode.left(2) << "\t"
                        << "1"
                        << "\t" << goods_name << "\t" << goods_price << "\t"
                        << "02"
                        << "\t"
                        << "4"
                        << "\n";
        }
    file_info.close();
    return true;
}

int BarcodeLabelScale::sendPluFile2Scale_DingJianAiVec(std::vector<NetInfoObj> ip_vec)
{
    int error_num = 0;
    for (auto ip : ip_vec)
    {
        if (!sendPluFile2Scale_DingJianAiSingleIp(ip.ip))
        {
            ++error_num;
        }
    }
    return error_num;
}

bool BarcodeLabelScale::sendPluFile2Scale_DingJianAiSingleIp(QString ip)
{
    auto app_dir = Utils::getAppDirPath();
    BOOL sta     = false;
    BOOL ref     = false;

    if (h_module_dingjian_)
    {
        // Initialize
        pAclasSDKInitialize Initialize = (pAclasSDKInitialize)GetProcAddress(h_module_dingjian_, "AclasSDK_Initialize");
        char               *str        = NULL;
        sta                            = Initialize(str);
        if (!sta)
        {
            SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_evt_, "顶尖条码标签秤初始化失败");
        }

        // Get Device Information
        //获取不到设备信息会卡住
        pGetDevicesInfo getDevicesInfo = (pGetDevicesInfo)GetProcAddress(h_module_dingjian_, "AclasSDK_GetDevicesInfo");
        struct DviIn   *info;
        info = (struct DviIn *)malloc(sizeof(struct DviIn));

        UINT addr = MakehostToDword(const_cast<char *>(ip.toStdString().c_str()));

        ref = getDevicesInfo(addr, 0, 0, info);

        FP fp = ongress;

        HANDLE      handle;
        char       *userdata = NULL;
        std::string path     = app_dir.toStdString() + "/" + ding_jian_ai_plu_name.toStdString();

        // ASync call
        pAclasSDKExecTask    exectask    = (pAclasSDKExecTask)GetProcAddress(h_module_dingjian_, "AclasSDK_ExecTaskA");
        pAclasSDKWaitForTask waitfortask = (pAclasSDKWaitForTask)GetProcAddress(h_module_dingjian_, "AclasSDK_WaitForTask");

        //同步执行
        // handle = waitfortask(exectask(addr, 5002, info->ProtocolType, 0, 0x0000, (char *)path.c_str(), fp, userdata));

        //异步执行
        exectask(addr, 5002, info->ProtocolType, 0, 0x0000, (char *)path.c_str(), fp, userdata);

        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "顶尖条码标签秤 handle {}", handle);

        return true;
    }
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "顶尖条码标签秤 库加载失败");
    return false;
}

QString BarcodeLabelScale::getToledoXmlPath()
{
    return "C:/Data/TestData/Input";
}

bool BarcodeLabelScale::createToledoXmlPath()
{
    auto      dir_path = getToledoXmlPath();
    QFileInfo file_info;
    file_info.setFile(dir_path);
    if (!file_info.exists())
    {
        auto created_path = Utils::mkMutiDir(dir_path);
        if (dir_path != created_path)
        {
            LOG_OPT_ERROR("创建托利多XML路径失败");
            return false;
        }
    }
    return true;
}

bool BarcodeLabelScale::send2ToledoScaleWizard(std::vector<NetInfoObj> ip_vec)
{
    createToledoData();
    createToledoCommand();
    createToledoDeviceList(ip_vec);
    createToledoTask();
    if(!execToledoTask()){
        return false;
    }

    return true;
}

bool BarcodeLabelScale::send2ToledoScaleSingleGoodsWizard(std::vector<NetInfoObj> ip_vec, QString goods_barcode)
{
    createToledoDataSingle(goods_barcode);
    createToledoCommand();
    createToledoDeviceList(ip_vec);
    createToledoTask();
    execToledoTask();

    return true;
}

bool BarcodeLabelScale::sendPluFile2Scale_DaHua()
{
    int result = -1;

    typedef int (*Fun)();
    // 判断是否正确加载
    if (!lib_dahua_.isLoaded())
    {
        LOG_OPT_ERROR("动态库加载失败{}", lib_dahua_.errorString().toStdString());
        return false;
    }
    else
    {
        LOG_OPT_INFO("动态库加载成功{}");
    }

    Fun open = (Fun)lib_dahua_.resolve("dhSendPluDefault");
    // 是否成功连接上 add() 函数
    if (open)
    {
        LOG_OPT_INFO("发送大华PLU到秤");
        result = open(); // 这里函数指针调用dll中的 add() 函数
        LOG_OPT_INFO("来自dll的返回 {}", result);
        return result == 0;
    }
    return false;
}


QString BarcodeLabelScale::generatePluStr_DaHua(QString plu_str, QString goods_barcode, QString goods_price, QString prefix_2, QString goods_name,
                                                QString chegn_type)
{
    QString strTmp;
    strTmp += "1P";

    strTmp += Utils::String::fillZeroFront(plu_str, plu_digit_);

    strTmp += "A";
    strTmp += goods_barcode;

    strTmp += Utils::String::fillZeroFront(QString::number(goods_price.toDouble() * 100, 'f', 0), 6);

    if (chegn_type == "0")
    {
        strTmp += "1"; // 计件
    }
    else
    {
        strTmp += "0"; // 计重量
    }

    strTmp += "000000000";
    strTmp += prefix_2;
    strTmp += "000000000000000000000000000000000000000000000000##";
    strTmp += goods_name;
    return strTmp;
}


void WINAPI ongress(UINT32 Eorrorcode, UINT32 index, UINT32 Total, char *userdata)
{
    switch (Eorrorcode)
    {
    case 0x0000:
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "complete");
        break;
    case 0x0001:
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "{}/{}", (unsigned int)index, (unsigned int)Total);
        break;
    }
}

UINT MakehostToDword(char *host)
{
    UINT  result;
    UINT  a[4];
    char *p1 = NULL;

    char str[20];
    strcpy(str, host);
    p1     = strtok(str, ".");
    a[0]   = atoi(p1);
    result = a[0] << 24;
    for (int i = 1; i < 4; i++)
    {
        p1   = strtok(NULL, ".");
        a[i] = atoi(p1);
        result += a[i] << ((3 - i) * 8);
    }
    return result;
}
#pragma optimize("", on);
