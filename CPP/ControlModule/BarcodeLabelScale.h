﻿#ifndef BARCODELABELSCALE_H
#define BARCODELABELSCALE_H

#include <QJSValue>
#include <QLibrary>
#include <QTimer>
#include <hash_map>
#include <mutex>
#include <vector>
#include <windows.h>
#include "BarcodeLabelScaleStruct.h"

class BarcodeLabelScale : public QObject
{
    Q_OBJECT

    Q_PROPERTY(bool is_transporting READ getIsTransporting NOTIFY sigIsTransportingChanged)

public:
    explicit BarcodeLabelScale(QObject *parent = nullptr);

    ///-------------------------------------------| 加载/保存 |-------------------------------------------
    //
    //初始化 条码秤列表
    void initBarcodeScaleInfo();
    //保存 条码秤列表
    void saveBarcodeScaleInfo();
    //
    ///-------------------------------------------| 加载/保存 |-------------------------------------------

    //获取传输状态
    bool getIsTransporting();

    ///-------------------------------------------| common |-------------------------------------------
    //
    /*!
     * \brief send2BarcodeScaleWizard4Qml 发送信息到条码秤
     * \param callback QML回调
     */
    Q_INVOKABLE bool send2BarcodeScaleWizard();

    Q_INVOKABLE bool sendSingleGoods2BarcodeScaleWizard(QString goods_barcode);

    /*!
     * \brief getBarcodeScaleList4Qml 获取条码秤类型列表Json
     * \return 条码秤列表Json
     */
    Q_INVOKABLE QVariant getBarcodeScaleTypeListJson();

    /*!
     * \brief getBarcodeScaleListJson 获取条码秤列表Json
     * \return 条码秤列表Json
     */
    Q_INVOKABLE QVariant getBarcodeScaleListJson();

    /*!
     * \brief addBarcodeScaleItem 添加一项条码秤
     * \return 添加的条码秤JSON
     */
    Q_INVOKABLE QVariant addBarcodeScaleItem();

    /*!
     * \brief delBarcodeScaleItemByTimestamp 根据时间戳删除条码秤元素
     * \param timestamp 时间戳
     * \return 成功?
     */
    Q_INVOKABLE bool delBarcodeScaleItemByTimestamp(QVariant timestamp);

    /*!
     * \brief setBarcodeScaleItemEnableByTimestamp 根据时间戳设置秤启用状态
     * \param timestamp 时间戳
     * \param is_enable 启用?
     * \return 成功?
     */
    Q_INVOKABLE bool setBarcodeScaleItemEnableByTimestamp(QVariant timestamp, QVariant is_enable);

    //根据时间戳设置条码秤类型
    Q_INVOKABLE bool setBarcodeScaleItemScaleTypeByTimestamp(QVariant timestamp, QVariant scale_type);

    /*!
     * \brief setBarcodeScaleItemIpByTimestamp 根据时间戳设置条码秤IP
     * \param timestamp 时间戳
     * \param scale_ip 条码秤IP
     * \return 成功?
     */
    Q_INVOKABLE bool setBarcodeScaleItemIpByTimestamp(QVariant timestamp, QVariant scale_ip);

    //根据时间戳设置条码秤端口
    Q_INVOKABLE bool setBarcodeScaleItemPortByTimestamp(QVariant timestamp, QVariant scale_port);
    //
    ///-------------------------------------------| common |-------------------------------------------


    ///-------------------------------------------| 大华 |-------------------------------------------
    //

    /*!
     * \brief send2DaHuaScaleWizard4Qml 发送到大华秤全流程 QML
     * \param callback QML回调
     */
    Q_INVOKABLE bool send2DaHuaScaleWizard4Qml(QJSValue callback);

    /*!
     * \brief send2DaHuaScaleWizard 发送到大华秤全流程
     */
    bool send2DaHuaScaleWizard();

    bool sendSingleGoods2DaHuaScaleWizard(QString goods_barcode);

    /*!
     * \brief setConfigFiles_DaHua 设置配置文件 - 大华
     * \return
     */
    bool setConfigFiles_DaHua(std::vector<NetInfoObj> ip_vec);

    /*!
     * \brief clearPluFile 清空PLU文件 - 大华
     */
    bool clearPluFiles_DaHua();

    /*!
     * \brief sendPluToSacle4DaHua 保存PLU到文件 - 大华
     */
    bool saveAllPlu2File_DaHua();

    bool saveSingleGoodsPlu2File_DaHua(QString goods_barcode);

    /*!
     * \brief sendPluFile2Scale_DaHua 发送PLU文件到秤 -大华
     */
    bool sendPluFile2Scale_DaHua();

    /*!
     * \brief generateDaHuaPluStr 生成PLU字符串 - 大华
     * \param plu_str plu (扫描的条码后五位?)
     * \param goods_barcode 商品条码
     * \param goods_price 商品售价
     * \param prefix_2 前缀(2位)
     * \param goods_name 商品名
     * \param chegn_type 称重类型
     * \return 大华PLU字符串
     */
    QString generatePluStr_DaHua(QString plu_str, QString goods_barcode, QString goods_price, QString prefix_2, QString goods_name, QString chegn_type);

    QString plu_file_name__da_hua = "dhplu.txt";
    //
    ///-------------------------------------------| 大华 |-------------------------------------------


    ///-------------------------------------------| 顶尖AI |-------------------------------------------
    //

    /*!
     * \brief send2DingJianAiScaleWizard 发送到顶尖AI秤全流程
     * \return 成功?
     */
    bool send2DingJianAiScaleWizard(std::vector<NetInfoObj> ip_vec);

    /*!
     * \brief send2DingJianAiScaleSingleGoodsWizard 发送单个商品到顶尖AI秤全流程
     * \param ip_vec ip列表
     * \param goods_barcode 商品条码
     * \return 成功?
     */
    bool send2DingJianAiScaleSingleGoodsWizard(std::vector<NetInfoObj> ip_vec, QString goods_barcode);

    /*!
     * \brief saveAllPlu2File_DingJianAi 保存商品信息到PLU文件 顶尖AI
     * \return 成功?
     */
    bool saveAllPlu2File_DingJianAi();

    /*!
     * \brief saveSinglePlu2File_DingJianAi 保存指定的商品信息到PLU文件 顶尖AI
     * \param goods_barcode 商品条码
     * \return 成功?
     */
    bool saveSinglePlu2File_DingJianAi(QString goods_barcode);

    /*!
     * \brief sendPluFile2Scale_DingJianAiVec 发送PLU到秤 顶尖AI
     * \param ip_vec 秤IP vec
     * \return 失败数量
     */
    int sendPluFile2Scale_DingJianAiVec(std::vector<NetInfoObj> ip_vec);

    /*!
     * \brief sendPluFile2Scale_DingJianAiSingle 发送PLU到秤 顶尖AI (单个秤)
     * \param ip 秤IP
     * \return 成功?
     */
    bool sendPluFile2Scale_DingJianAiSingleIp(QString ip);

    QString ding_jian_ai_plu_name = "ding_jian_ai_plu.txt";
    bool    ding_jian_is_sending_ = false;

    QTimer timer_ding_jian_;
    //
    ///-------------------------------------------| 顶尖AI |-------------------------------------------


    ///-------------------------------------------| 托利多 |-------------------------------------------
    //

    //获取托利多XML路径
    static QString getToledoXmlPath();

    //创建托利多XML路径
    static bool createToledoXmlPath();

    // 发送到托利多秤全流程
    bool send2ToledoScaleWizard(std::vector<NetInfoObj> ip_vec);

    //发送单个商品到托利多秤
    bool send2ToledoScaleSingleGoodsWizard(std::vector<NetInfoObj> ip_vec, QString goods_barcode);

    //创建商品数据
    void createToledoData();

    bool createToledoDataSingle(QString goods_barcode);


    //创建命令
    void createToledoCommand();

    //创建设备列表
    void createToledoDeviceList(std::vector<NetInfoObj> ip_vec);

    //创建任务
    void createToledoTask();

    //运行任务
    bool execToledoTask();

    QString task_name_tmp_;
    //
    ///-------------------------------------------| 托利多 |-------------------------------------------

    QString barcode_weight_prefix_ = "21";

    QLibrary lib_dahua_;
    QLibrary lib_toledo_;
    HMODULE  h_module_dingjian_ = 0;

private:
    int plu_digit_ = 5;

    std::unordered_map<BarcodeScaleEnum, QString> barcode_scale_value_map_;

    std::vector<BarcodeScaleItem> barcode_scale_vec_; // 条码秤列表

    bool is_transporting_ = false;

signals:
    void sigIsTransportingChanged();
};

#endif // BARCODELABELSCALE_H
