﻿#ifndef GOODSPROMOTIONCTRL_H
#define GOODSPROMOTIONCTRL_H


#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <mutex>
#include <QNetworkAccessManager>

class GoodsPromotionCtrl : public QObject
{
    Q_OBJECT
public:

    static std::unique_ptr<GoodsPromotionCtrl> singleton_;
    static std::mutex                  mutex_;


    explicit GoodsPromotionCtrl(QObject *parent = nullptr);
    ~GoodsPromotionCtrl();
    static GoodsPromotionCtrl *getInstance();
    Q_INVOKABLE std::vector<QString> queryGoodsPromotionForQml(QString goodMessage);//TODO 本地调取
    Q_INVOKABLE QString queryGoodsPromotionForQml2(QString goodMessage);//TODO 本地调取
    Q_INVOKABLE QString queryGoodsPromotionForQml1(QString goodMessage);//TODO 本地调取
    void deleteGoodsPromotion();//删除满计件打几折，删除买几件送几件
    void deleteGoodsPromotion2();//删除过期的
    void deleteGoodsPromotion3();//删除第几件打几折

    void queryGoodsPromotionRequest();//写入本地
    void queryGoodsPromotionRequestFinished(QNetworkReply *reply, bool isError, QByteArray data);

    void queryPromotionGoodsSingle();//商品促销，第一件打几折，第二件打几折
    void queryPromotionGoodsSingleFinished(QNetworkReply *reply, bool isError, QByteArray data);

    void queryGoodsPromotionSql(QString str);
    void queryGoodsPromotionSql2(QString str);
    void queryGoodsPromotionSql3(QString str);

    void queryOrderPromotionForQml();
    void queryOrderPromotionForQmlFinished(QNetworkReply *reply, bool isError, QByteArray data);

    Q_INVOKABLE void pcSaleGoodsSearchForQml(QString goodsBarcode);//捆绑信息查询
signals:
    //void sendGoodsPromotion(QString data1,QString data2,QString data3);
    void sendBindingStr(QVariant goodsStr, QVariant unique);
//    void sigDowloadFile(QString file_name);
//    void sigDowloadFailedFile(QString file_name);
//    void sigDowloadIndex(int index);
//    void workStart(); // 工作开始
};

#endif // GOODSPROMOTIONCTRL_H
