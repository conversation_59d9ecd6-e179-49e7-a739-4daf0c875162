﻿#ifndef PRINTERCONTROL_H
#define PRINTERCONTROL_H

#include <QObject>
#include <Windows.h>
#include <qzint.h>
#include <stdio.h>
#include "ConfModule/ConfigTool.h"
#include "PrinterStruct.h"
#include "ShopCartControl.h"

class PrinterWorker;
// 开钱箱
bool openCashBox();

class printInfo
{
public:
    std::vector<printItemInfo> print_item_vec;
};

class PrinterControl : public QObject
{
    Q_OBJECT

    Q_PROPERTY(bool isUseFoodTicket READ getIsUseFoodTicket WRITE setIsUseFoodTicket NOTIFY sigIsUseFoodTicketChanged)
    Q_PROPERTY(unsigned long foodTicketIndex READ getFoodTicketIndex WRITE setFoodTicketIndex NOTIFY sigFoodTicketIndexChanged)
    Q_PROPERTY(int curPriceTagType READ curPriceTagType WRITE setCurPriceTagType NOTIFY curPriceTagTypeChanged FINAL)

public:
    explicit PrinterControl(QObject *parent = nullptr);

    //获取打印机设备列表
    Q_INVOKABLE static QStringList getPrinterNames();

    Q_INVOKABLE void setTicketPrinterByName(QString name);
    Q_INVOKABLE void setOnlinePrinterByName(QString name);
    Q_INVOKABLE void setPriceTagPrinterByName(QString name);
    QString          getTicketPrinterName();
    QString          getTicketsRemarkInfo();

    Q_INVOKABLE int getTicketPrinterNum();
    Q_INVOKABLE int getOnlinePrinterNum();
    Q_INVOKABLE int getPriceTagPrinterNum();

    Q_INVOKABLE void setTicketPrinterNum(int num);
    Q_INVOKABLE void setOnlinePrinterNum(int num);
    Q_INVOKABLE void setPriceTagPrinterNum(int num);

    /*!
     * \brief setPriceTagPrintFormatByJson 设置价签打印格式
     * \param json_str 价签打印格式JSON
     */
    Q_INVOKABLE bool setPriceTagPrintFormatByJson(QVariant json_str);

    /*!
     * \brief reprintLastOrder 重复打印上一单
     * \return
     */
    Q_INVOKABLE bool reprintLastOrder();


    /*!
     * \brief printTest 打印测试
     */
    Q_INVOKABLE void printTest();

    /*!
     * \brief Text2BarcodeImg 字符串转条码
     * \param str 要转换的字符串
     * \return 条码图片
     */
    static QImage Text2BarcodeImg(const QString &str);

    /*!
     * \brief printOrder 打印订单
     * \param shop_cart_list 购物车指针
     */
    void printOrderThread(ShopCartList *shop_cart_list, int print_num = -1, bool is_reprint = false, std::string addition_info = "");
    void printOrderThreadCombine(ShopCartList *shop_cart_list, int print_num = -1, bool is_reprint = false, std::string addition_info = "",double combined_online_pay_cash_=0,double combined_online_pay_online_ = 0,double totalPay = 0);
    void printOrder(ShopCartList *shop_cart_list, int print_num = -1, std::string addition_info = "");
    void printOrder4Food(ShopCartList *shop_cart_list, int print_num = -1, bool is_reprint = false, std::string addition_info = "");

    /*!
     * \brief printPriceTagByBarcode 根据条码打印价签
     * \param goods_barcode 条码
     */
    Q_INVOKABLE void printPriceTagByBarcode(QVariant goods_barcode);

    void printPriceTag40x30(GoodsInfo goods_info);
    void printPriceTag95x38(GoodsInfo goods_info);
    void printPriceTag60x35(GoodsInfo goods_info);

    QString             getPriceTagInfo(PriceTagTemplateEnum price_tag_template_enum);
    Q_INVOKABLE QString getPriceTagInfo(int price_tag_template_enum);

    /*!
     * \brief printPriceTagTest 打印检测
     */
    Q_INVOKABLE void printPriceTagTest();

    /*!
     * \brief printQueryOrder 打印查询单
     * \param json_data 查询单数据 json
     * \return
     */
    Q_INVOKABLE void printQueryOrder(QVariant json_data);

    /*!
     * \brief printPendingOrder 打印挂单
     * \param json_data 挂单数据 json
     */
    Q_INVOKABLE void printPendingOrder(ShopCartList *shop_cart_list);

    /*!
     * \brief printNetOrder 打印网单
     * \param json_data 网单数据 json
     * \return
     */
    Q_INVOKABLE void printNetOrder(QVariant json_data);
    /*!
     * \brief printRefundNetOrder 打印退款网单
     * \param json_data
     */
    Q_INVOKABLE void printRefundNetOrder(QVariant json_data);
    //打印交班信息
    Q_INVOKABLE bool printHandoverInfo(QVariant json_data_str, int print_num = -1);
    //打印宁宇交班信息
    Q_INVOKABLE bool printNingyuHandoverInfo(QVariant json_data_str, int print_num = -1);

    /*!
     * \brief getIsPrintTicket 获取是否打印订单小票
     * \return 是否打印订单小票
     */
    Q_INVOKABLE bool getIsPrintTicket();

    /*!
     * \brief setIsPrintTicket 设置是否打印订单小票
     * \param is_print 是否打印订单小票
     */
    Q_INVOKABLE void setIsPrintTicket(QVariant is_print);

    Q_INVOKABLE void openCashBox();

    static float dpi2Mm(float dpi);
    static float mm2Dpi(float mm);

    bool getIsUseFoodTicket();
    void setIsUseFoodTicket(bool is_use);

    unsigned long    getFoodTicketIndex();
    void             setFoodTicketIndex(unsigned long index);
    Q_INVOKABLE void resetFoodTicketIndex();

    int  curPriceTagType();
    void setCurPriceTagType(int tag_type);

    static bool isPrinterValid(QString printer_name);;

    static QStringList invalid_printer_list_;

    PrinterWorker *printer_worker_;
    QThread       *printer_worker_thread_ = nullptr;
    
private:
    QString printer_name_;  // 打印机名
    int     print_num_ = 1; // 打印数量

    bool          is_use_food_ticket_ = false;
    unsigned long food_ticket_index_  = 0;

    QString ticket_printer_name_;
    int     ticket_printer_num_      = 1;
    bool    ticket_printer_is_print_ = true;

    QString online_printer_name_;
    int     online_printer_num_ = 1;

    QString price_tag_printer_name_;
    int     price_tag_printer_num_ = 1;
    QString tickets_remark_info_;
    PriceTagTemplateEnum cur_price_tag_type_;

    PriceTag40x30 price_tag_40x30_;
    PriceTag95x38 price_tag_95x38_;
    PriceTag60x35 price_tag_60x35_;

signals:
    void sigIsUseFoodTicketChanged();
    void sigFoodTicketIndexChanged();
    void curPriceTagTypeChanged();

    void sigPrintOrder(std::shared_ptr<ShopCartList> shop_cart_list, int print_num, std::string addition_info = "");
    void sigPrintOrderCombine(std::shared_ptr<ShopCartList> shop_cart_list, int print_num, std::string addition_info = "", double cash = 0, double online = 0,double totalPay=0);
    void sigPrintOrder4Food(std::shared_ptr<ShopCartList> shop_cart_list, int print_num = -1, bool is_reprint = false, std::string addition_info = "");
    void sigPrintOrderNingyu(std::shared_ptr<ShopCartList> shop_cart_list, int print_num = -1, bool is_reprint = false, std::string addition_info = "");
};

#endif // PRINTERCONTROL_H
