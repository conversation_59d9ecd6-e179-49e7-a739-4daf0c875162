﻿#include "ZintControl.h"
#include <qzint.h>
#include "ControlModule/ControlManager.h"
#include "ControlModule/ShopControl.h"

ZintControl::ZintControl(QObject *parent) : QObject{parent}
{
}

QImage ZintControl::Text2BarcodeImg(const QString &str)
{
    Zint::QZint qzint;
    qzint.setSymbol(BARCODE_CODE128);
    qzint.setOption1(-1);
    qzint.setOption2(0);
    qzint.setOption3(0);
    //    qzint.setReaderInit(true);
    qzint.setScale(1);
    qzint.setText(str);
    qzint.setShowText(false);
    QImage img(550, 200, QImage::Format_RGB888);
    img.fill(QColor(255, 255, 255));
    QPainter painter(&img);
    QRectF   paintRect(0, 0, 550, 200);
    qzint.render(painter, paintRect);
    // img.save("test_barcode.jpg");
    return img;
}

QImage ZintControl::Text2QRCodeImg(const QString &str)
{

    Zint::QZint qzint;
    qzint.setSymbol(BARCODE_QRCODE);
    qzint.setOption1(-1);
    qzint.setOption2(0);
    qzint.setOption3(0);
    //    qzint.setReaderInit(true);
    qzint.setScale(1);
    qzint.setText(str);
    qzint.setShowText(false);
    QImage img(550, 550, QImage::Format_RGB888);
    img.fill(QColor(255, 255, 255));
    QPainter painter(&img);
    QRectF   paintRect(0, 0, 550, 550);
    qzint.render(painter, paintRect);
    // img.save("test_qrcode.jpg");
    return img;
}
