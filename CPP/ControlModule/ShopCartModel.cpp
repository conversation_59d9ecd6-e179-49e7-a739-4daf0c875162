﻿#include "ShopCartModel.h"
#include <DataModule/DataManager.h>
#include "ControlManager.h"
#include "ShopCartControl.h"
#include "Utils/Utils.h"

ShopCartModel::ShopCartModel(QObject *parent) : QAbstractListModel(parent), shop_cart_list_(nullptr)
{
}

int ShopCartModel::rowCount(const QModelIndex &parent) const
{
    // For list models only the root node (an invalid parent) should return the list's size. For all
    // other (valid) parents, rowCount() should return 0 so that it does not become a tree model.
    if (parent.isValid() || !shop_cart_list_)
        return 0;

    QVector<ShopCartItem> items = shop_cart_list_->items();

    return items.size();
}

QVariant ShopCartModel::data(const QModelIndex &index, int role) const
{
    //LOG_EVT_INFO("=====================role：{}",role);
    if (!index.isValid() || !shop_cart_list_)
        return QVariant();

    const auto        &goodsMgr = DataManager::getInstance()->getGoodsMgr();
    GoodsInfo          goods_info;
    const ShopCartItem item = shop_cart_list_->items().at(index.row());

    goodsMgr->getGoodsByBarcode(item.goods_barcode_, goods_info);

    switch (role)
    {
    case GOODS_BARCODE:
        return goods_info.goods_barcode;
    case GOODS_NAME_ROLE:
        return item.getGoodsName();
         //return goods_info.goods_name;
    case GOODS_PRICE_ROLE:
        return Utils::String::getClearNumStr(item.getPrice());

    case GOODS_AMOUNT_ROLE:
        return Utils::String::getClearNumStr(item.getNum());
        // return QVariant(item.goods_num_);
    case GOODS_TOTAL_PRICE_ROLE:
        {
            QString result = Utils::String::getClearNumStr(item.getSubtotal());
            return result;
        }
    case GOODS_DATE_TIME_ROLE:
        return QVariant(item.add_date_time_);
    case GOODS_IS_WEIGHT:
        return QVariant(goods_info.goodsChengType == 1);
    }

    return QVariant();
}

bool ShopCartModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    // 此函数不使用
    return false;
}

Qt::ItemFlags ShopCartModel::flags(const QModelIndex &index) const
{
    if (!index.isValid())
        return Qt::NoItemFlags;

    return Qt::ItemIsEditable;
}

QHash<int, QByteArray> ShopCartModel::roleNames() const
{
    QHash<int, QByteArray> names;
    names[GOODS_BARCODE]          = "goods_barcode";
    names[GOODS_NAME_ROLE]        = "goods_name_role";
    names[GOODS_PRICE_ROLE]       = "goods_price_role";
    names[GOODS_PRICE_ROLE_JIN]   = "goods_price_role_jin";
    names[GOODS_AMOUNT_ROLE]      = "goods_amount_role";
    names[GOODS_TOTAL_PRICE_ROLE] = "goods_total_price_role";
    names[GOODS_DATE_TIME_ROLE]   = "goods_date_time_role";
    names[GOODS_IS_WEIGHT]        = "GOODS_IS_WEIGHT";
    return names;
}

ShopCartList *ShopCartModel::list() const
{
    return shop_cart_list_;
}

void ShopCartModel::setList(ShopCartList *list)
{
    beginResetModel();

    if (shop_cart_list_)
        shop_cart_list_->disconnect(this);

    shop_cart_list_ = list;

    if (shop_cart_list_)
    {
        connect(shop_cart_list_, &ShopCartList::preItemAppended, this,
                [=]()
                {
                    const int index = shop_cart_list_->items().size();
                    beginInsertRows(QModelIndex(), index, index);
                });
        connect(shop_cart_list_, &ShopCartList::postItemAppended, this,
                [=]()
                {
                    endInsertRows();
                    emit itemCountChanged();
                });

        connect(shop_cart_list_, &ShopCartList::preItemInsert, this,
                [=](int index)
                {
                    beginInsertRows(QModelIndex(), index, index);
                });
        connect(shop_cart_list_, &ShopCartList::postItemInserted, this,
                [=]()
                {
                    endInsertRows();
                    emit itemCountChanged();
                });

        connect(shop_cart_list_, &ShopCartList::preItemRemoved, this,
                [=](int index)
                {
                    beginRemoveRows(QModelIndex(), index, index);
                });
        connect(shop_cart_list_, &ShopCartList::postItemRemoved, this,
                [=]()
                {
                    endRemoveRows();
                    emit itemCountChanged();
                });

        connect(shop_cart_list_, &ShopCartList::preItemReset, this,
                [=]()
                {
                    beginResetModel();
                });
        connect(shop_cart_list_, &ShopCartList::postItemReset, this,
                [=]()
                {
                    endResetModel();
                    emit itemCountChanged();
                });

        connect(shop_cart_list_, &ShopCartList::postRowChanged, this,
                [=](int row)
                {
            std::cout<<"sfsdfsdfsdfs===============:"<<row<<std::endl;
                    for (int i = 0; i < roleNames().size(); ++i)
                    {
                        // ERROR QT BUG 不能如期函数功能
                        std::cout<<"sfsdfsdfsdfsweqqwq:"<<i<<std::endl;
                        dataChanged(index(row, i), index(row, i));
                    }
                    std::cout<<"==sfsdfsdfsdfsgdfgfdgd==:"<<std::endl;
                });
    }
    endResetModel();
}

unsigned long long ShopCartModel::itemCount()
{
    if (!shop_cart_list_)
        return 0;

    return shop_cart_list_->items().size();
}

void ShopCartModel::refreshView()
{
    beginResetModel();
    endResetModel();
}
