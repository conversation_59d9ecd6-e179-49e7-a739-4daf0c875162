﻿#include "WeightingScaleControl.h"
#include <iomanip>
#include <iostream>
#include <sstream>
#include "ConfModule/ConfigTool.h"
#include "LogManager.h"
#include "qserialportinfo.h"

WeightingScaleControl *WeightingScaleControl::pinstance_ = nullptr;
std::mutex             WeightingScaleControl::mutex_;

WeightingScaleControl::WeightingScaleControl(QObject *parent) : QObject{parent}
{
    com_  = ConfigTool::getInstance()->getSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_COM).toString();
    baud_ = ConfigTool::getInstance()->getSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_BAUD).toString();

    auto pase_type_tmp = ConfigTool::getInstance()->getSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_DATA_PARSE_TYPE).toInt();
    parse_type_ = static_cast<WeightingScaleControl::ParseType>(pase_type_tmp);

    auto tmp = (int)parse_type_;

    is_kg_ = ConfigTool::getInstance()->getSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_SCALE_IS_KG).toBool();

    weighting_scale_ = new QSerialPort(this);
    weighting_scale_->setDataBits(QSerialPort::Data8);
    weighting_scale_->setParity(QSerialPort::NoParity);
    weighting_scale_->setStopBits(QSerialPort::OneStop);
    connect(weighting_scale_, &QSerialPort::readyRead, this, &WeightingScaleControl::readDataSlot);
    weighting_scale_->setBaudRate(baud_.toInt());
    weighting_scale_->setPortName(com_);
    weighting_scale_->open(QIODevice::ReadOnly);

    timer_stable_.setInterval(333);

    if (!weighting_scale_->isOpen())
    {
        LOG_EVT_ERROR("串口打开失败");
        setIsConnected(false);
    }
    else
    {
        LOG_EVT_INFO("串口打开成功");
        setIsConnected(true);
    }

    bauds_.append("1200");
    bauds_.append("2400");
    bauds_.append("4800");
    bauds_.append("9600");
    bauds_.append("14400");
    bauds_.append("19200");

    parse_types_.append(tr("方式1"));
    parse_types_.append(tr("方式2"));
    parse_types_.append(tr("方式3"));
    parse_types_.append(tr("方式4"));

    connect(&weight_stable_timer_, &QTimer::timeout,
            [&]()
            {
                auto last_weight = getLastWeight();
                if (last_weight > 0)
                {
                    setIsStable(true);
                }
            });
}

WeightingScaleControl::~WeightingScaleControl()
{
    weighting_scale_->close();
}

QStringList WeightingScaleControl::getAvailablePorts()
{
    QStringList list;
    for (auto &serial : QSerialPortInfo::availablePorts())
    {
        list.append(serial.portName().toUpper());
    }
    return list;
}

void WeightingScaleControl::setCom(QString com)
{
    auto setting_tool = ConfigTool::getInstance();

    emit sendWeight("0");
    setFinalWeight(0);

    com_ = com;
    weighting_scale_->setPortName(com_);

    setting_tool->setSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_COM, com);

    if (reOpen())
    {
    }
    //        setting_tool->setSetting(SettingToolEnum::SettingEnum::scale  )
    //        ConfigTool::getInstance()->setSetting(ConfigTool::ConfigEnum::SCALE_COM, com_);
}

QString WeightingScaleControl::getCom()
{
    return com_;
}

void WeightingScaleControl::setBaud(QString baud)
{
    baud_ = baud;
    weighting_scale_->setBaudRate(baud_.toInt());
    emit sendWeight("0");
    setFinalWeight(0);

    reOpen();
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_BAUD, baud);
}

QString WeightingScaleControl::getBaud()
{
    return baud_;
}

bool WeightingScaleControl::setParseType(QString type)
{
    int index = parse_types_.indexOf(type);
    if (index == -1)
        return false;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_DATA_PARSE_TYPE, index);
    parse_type_ = static_cast<ParseType>(index);
    emit sendWeight("0");
    setFinalWeight(0);
    return true;
}

QString WeightingScaleControl::getParseType()
{
    auto index = static_cast<int>(parse_type_);
    if (index < parse_types_.size())
        return parse_types_.at(index);
    return "";
}

QStringList WeightingScaleControl::getParseTypes()
{
    return parse_types_;
}
void WeightingScaleControl::setQupi(double weight)
{
    qupi_ = weight;
}
void WeightingScaleControl::setIsQupi(bool is_qupi)
{
    if (is_qupi)
    {
        qupi_ = last_origin_weight_ - zero_out_;
    }
    else
    {
        qupi_ = 0;
    }
}
void WeightingScaleControl::setZeroOut()
{
    zero_out_ = last_origin_weight_ - qupi_;
}
double WeightingScaleControl::getQupi()
{
    return qupi_;
}
double WeightingScaleControl::getLastWeightKg()
{
    return last_weight_zero_out_qupi_;
}

bool WeightingScaleControl::reOpen()
{
    weighting_scale_->close();
    //    emit Utils4Qml::getInstance()->sendWeight("0");
    if (!weighting_scale_->open(QIODevice::ReadOnly))
    {
        setIsConnected(false);
        qDebug() << "reOpen error";
        return false;
    }
    setIsConnected(true);
    return true;
}

bool WeightingScaleControl::isWeightScaleOpened()
{
    return weighting_scale_->isOpen();
}

void WeightingScaleControl::setIsKg(bool type)
{
    is_kg_ = type;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_SCALE_IS_KG, type);
}

bool WeightingScaleControl::getIsKg()
{
    return is_kg_;
}

void WeightingScaleControl::setStableWeight(QString weight, QString up_max, QString down_max)
{
    weight_stable_ = weight.toDouble();

    if (weight_stable_ == 0)
    {
        timer_stable_.stop();
        return;
    }
    up_max_   = up_max.toDouble();
    down_max_ = down_max.toDouble();

    trigger_num_ = 8; // 稳定时间:trigger_num * 0.33s

    stable_list_.clear();
    connect(&timer_stable_, &QTimer::timeout,
            [&, this]()
            {
                stable_list_.push_back(last_weight_zero_out_qupi_);
                while (stable_list_.size() > trigger_num_)
                    stable_list_.erase(stable_list_.begin());

                if (is_need_zero_)
                {
                    // emit Utils4Qml::getInstance()->sendIsNeed2Zero();
                }

                if ((last_weight_zero_out_qupi_ - up_max_ <= weight_stable_ && last_weight_zero_out_qupi_ + down_max_ >= weight_stable_) &&
                    !this->is_need_zero_)
                {
                    if (stable_list_.size() == trigger_num_)
                    {
                        for (auto stable_it = stable_list_.begin() + 1; stable_it != stable_list_.end(); ++stable_it)
                        {
                            auto origin1 = (*stable_list_.begin());
                            auto fabs1   = std::fabs(origin1 - *stable_it);

                            if (fabs1 > .001)
                            {
                                stable_list_.clear();
                                return;
                            }
                        }
                        emit sendWeightStable(QVariant::fromValue(getLastWeight()));
                        timer_stable_.stop();
                        timer_stable_.disconnect();
                        stable_list_.clear();
                        this->is_need_zero_ = true;
                    }
                }
            });
    timer_stable_.start();
}

void WeightingScaleControl::stopStableWeightDetection()
{
    timer_stable_.stop();
    timer_stable_.disconnect();
    stable_list_.clear();
}

void WeightingScaleControl::detectWeightStable()
{
    weight_stable_timer_.start(weight_stable_timer_trigger_time_msec_);
}

void WeightingScaleControl::setIsNeedZero(bool is_need_zero)
{
    is_need_zero_ = is_need_zero;
}
bool WeightingScaleControl::getIsNeedZero()
{
    return is_need_zero_;
}

void WeightingScaleControl::setLastWeight(double weight)
{
    last_origin_weight_   = weight;
    last_weight_zero_out_ = last_origin_weight_ - zero_out_;
    std::stringstream tmp_str_stream;
    tmp_str_stream << std::fixed << std::setprecision(3) << (last_weight_zero_out_ - qupi_);
    std::string tmp_weight_str;
    tmp_str_stream >> tmp_weight_str;
    auto cur_weight = std::atof(tmp_weight_str.c_str());
    if (cur_weight != last_weight_zero_out_qupi_)
    {
        setIsStable(false);
        detectWeightStable();
    }
    if (cur_weight <= 0)
    {
        emit sendWeightZero();
    }
    last_weight_zero_out_qupi_ = cur_weight;

    auto conf_tool = ConfigTool::getInstance();

    if (conf_tool->isUseJinWeightScale())
    {
        last_weight_zero_out_qupi_ *= 2;
    }

    emit sendWeight(QString::number(last_weight_zero_out_qupi_));
    setFinalWeight(last_weight_zero_out_qupi_);
}

double WeightingScaleControl::getLastWeight()
{
    return last_weight_zero_out_qupi_;
}

double WeightingScaleControl::finalWeight()
{
    return final_weight_;
}

void WeightingScaleControl::setFinalWeight(double final_weight)
{
    final_weight_ = final_weight;
    emit finalWeightChanged();
}

bool WeightingScaleControl::isConnected()
{
    return is_conn_;
}

void WeightingScaleControl::setIsConnected(bool is_conn)
{
    is_conn_ = is_conn;
    emit isConnectedChanged();
}

bool WeightingScaleControl::isStable()
{
    return is_stable_;
}

void WeightingScaleControl::setIsStable(bool is_stable)
{
    if (is_stable_ == is_stable)
        return;

    is_stable_ = is_stable;
    emit isStableChanged();
}

QByteArray WeightingScaleControl::dataToHex(const QByteArray &data, const QString &separator, const QString &prefix)
{
    if (separator.isEmpty() && prefix.isEmpty())
    {
        return data.toHex().toUpper();
    }

    QStringList list;
    auto        len = data.count();
    for (int i = 0; i < len; i++)
    {
        list.append(prefix);
        auto hex = QString::number(data.at(i) & 0xFF, 16).toUpper();
        while (hex.size() < 2)
        {
            hex.prepend('0');
        }
        list.append(hex);
        if (i < len - 1)
        {
            list.append(separator);
        }
    }
    return list.join("").toUtf8();
}

QByteArray WeightingScaleControl::dataFromHex(const QString &data)
{
    QRegExp     rx("([0-9a-fA-F]{2})");
    QStringList list;
    auto        pos = 0;
    while ((pos = rx.indexIn(data, pos)) != -1)
    {
        list << rx.cap(1);
        pos += rx.matchedLength();
    }

    if (list.isEmpty())
    {
        return {};
    }
    auto line = list.join("").toLatin1();
    return QByteArray::fromHex(line);
}

void WeightingScaleControl::readDataSlot()
{
    QByteArray data = weighting_scale_->readAll();

    if (!data.size())
        return;

    hex_str_tmp_ += dataToHex(data);
    str_tmp_ += data;

    if (hex_str_tmp_.length() > 2000)
    {
        hex_str_tmp_.clear();
    }

    switch (parse_type_)
    {
    case ParseType::TYPE1:
        {
            if (hex_str_tmp_.indexOf("0D") != hex_str_tmp_.lastIndexOf("0D"))
            {
                QStringList hex_list = hex_str_tmp_.split("0D");
                hex_str_tmp_.clear();
                QString cur_hex_str = hex_list[hex_list.size() - 2] + "0D";

                if (last_origin_weight_ < .01)
                    is_need_zero_ = false;

                QString dec_str    = dataFromHex(cur_hex_str);
                auto    weight_str = dec_str.left(5);
                auto    weight     = QString::number(weight_str.toDouble() / 1000);

                setLastWeight(weight.toDouble());
                break;
            }
            break;
        }
    case ParseType::TYPE2:
        {
            QString receiveBuf = data;

            QStringList trueStr;
            QString     zhongliangStr;
            if (data.size() != 16 && data.size() != 8)
                return;

            if (data.size() == 16)
            {
                trueStr = receiveBuf.split(".");
                if (trueStr.size() > 1)
                    zhongliangStr = QString::number(trueStr.at(0).right(2).toDouble() + trueStr.at(1).left(3).toDouble() / 1000);
            }
            else if (data.size() == 8)
            {
                if (receiveBuf.contains("."))
                {
                    trueStr = receiveBuf.split(".");
                    if (trueStr.size() > 1)
                        chengzhongbyte_ = trueStr.at(0).right(2) + trueStr.at(1);
                }
                else if (receiveBuf.contains("kg"))
                {
                    trueStr = receiveBuf.split("kg");
                    if (trueStr.size() > 1)
                    {
                        chengzhongbyte_ += trueStr.at(0);
                        zhongliangStr   = QString::number(chengzhongbyte_.toDouble() / 1000);
                        chengzhongbyte_ = "";

                        setLastWeight(zhongliangStr.toDouble());

                        if (last_origin_weight_ < .01)
                            is_need_zero_ = false;
                    }
                }
            }
            break;
        }
    case ParseType::TYPE3:
        {
            if (str_tmp_.indexOf("kg") != str_tmp_.lastIndexOf("kg"))
            {
                QStringList hex_list = str_tmp_.split("kg");
                str_tmp_.clear();
                QString cur_str_tmp_ = hex_list[hex_list.size() - 2];

                auto weight_str = cur_str_tmp_.right(7);
                auto weight     = QString::number(weight_str.toDouble());
                setLastWeight(weight.toDouble());

                if (last_origin_weight_ < .01)
                    is_need_zero_ = false;
                break;
            }
            break;
        }
    case ParseType::TYPE4:
        break;
    }
}
