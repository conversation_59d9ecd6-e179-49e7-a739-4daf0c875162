﻿#include "ShopCartControl.h"
#include <QDateTime>
#include <math.h>
#include <sstream>
#include <QObject>
#include <QCoreApplication>
#include "ConfModule/ConfigTool.h"
#include "ControlModule/ControlManager.h"
#include "ControlModule/OrderControl.h"
#include "ControlModule/ShopCartControl.h"
#include "ControlModule/ShopControl.h"
#include "DataModule/DataManager.h"
#include "DataModule/OrderData.h"
#include "LogModule/LogManager.h"
#include <QMutex>
#include "NetModule/HttpWorker.h"
#include "Utils/Utils.h"
#include "GoodsControl.h"
#include "GoodsPromotionCtrl.h"
#include "../DataModule/GoodsData.h"

std::vector<QString>  gift_vector;
bool    is_gift_goods = false;
double    lastTemp   = .0;
QString gift_num      = "";
QString gift_barcode  = "";
QMutex mutex;

ShopCartList::ShopCartList(QObject *parent) : QObject(parent)
{
}

QVector<ShopCartItem> ShopCartList::items() const
{
    return shop_cart_Items_;
}

void ShopCartList::resetAllInfo()
{
    LOG_EVT_INFO("开始清空购物车及会员信息！");
    clearAllGoods();
    clearMemberUnique();
    emit sigInitState();
}

void ShopCartList::resetOrderInfo()
{
    LOG_EVT_INFO("触发订单号生成");
    shop_cart_list_data_.order_unique_ = OrderControl::generateOrderId();
    QDateTime dateTime(QDateTime::currentDateTime());
    shop_cart_list_data_.order_date_ = dateTime.toString("yyyy-MM-dd");
    shop_cart_list_data_.order_time_ = dateTime.toString("hh:mm:ss");

    shop_cart_list_data_.is_cus_order_price_ = false;
    shop_cart_list_data_.cus_order_price_    = .0;

    setWipeZeroType(ConfigTool::getInstance()->defaultWipeZeroType());
    // setWipeZeroType((int)WipeZeroTypeEnum::WIPE_ZERO__1_JIAO);

    shop_cart_list_data_.is_credit_     = false;
    shop_cart_list_data_.cash_received_ = .0;
    shop_cart_list_data_.payment        = 0;
    //    shop_cart_list_data_.member_unique           = "";
    setHighlightTimeStamp("");
    // shop_cart_list_data_.last_add_item_timestamp = "";
    shop_cart_list_data_.recharge_change = 0;
}

void ShopCartList::resetMemberInfo()
{
    setMemberUnique("");
    setMemberName("");
    setMemberBlance("");
    setMemberPoint("");
}

QString ShopCartList::getPendingOrderInfoJson()
{
    Json json_pending_order;

    json_pending_order["sale_list_unique"] = shop_cart_list_data_.order_unique_.toStdString(); // 销售单唯一标识
    json_pending_order["sale_list_datetime"] =
        shop_cart_list_data_.order_date_.toStdString() + " " + shop_cart_list_data_.order_time_.toStdString(); // 订单日期时间
    json_pending_order["sale_list_time"]     = shop_cart_list_data_.order_time_.toStdString();                 // 订单时间
    json_pending_order["sale_list_date"]     = shop_cart_list_data_.order_date_.toStdString();                 // 订单日期
    json_pending_order["is_cus_order_price"] = shop_cart_list_data_.is_cus_order_price_;                       // 自定义订单价格?
    json_pending_order["cus_order_price"]    = shop_cart_list_data_.cus_order_price_;                          // 自定义订单价格
    json_pending_order["member_unique"]      = shop_cart_list_data_.member_unique.toStdString();               //
    json_pending_order["member_name"]        = shop_cart_list_data_.member_name.toStdString();                 //
    json_pending_order["order_price"]        = getTotalGoodsPrice();                                           // 订单价格
    json_pending_order["quantity"]           = orderQuantity();                                                // 订单商品数量

    return QString::fromStdString(json_pending_order.dump());
}

QString ShopCartList::getPendingOrderDetailInfoJson()
{
    Json json_pending_order_detail;
    auto shop_cart_list = this;

    for (const ShopCartItem &shop_cart_item : shop_cart_list->items())
    {
        Json      json_tmp;
        GoodsInfo goods_info;
        DataManager::getInstance()->getGoodsMgr()->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);

        json_tmp["goods_barcode"]  = shop_cart_item.goods_barcode_;       // 商品ID
        json_tmp["goods_name"]     = goods_info.goods_name.toStdString(); // 商品名称
        json_tmp["goods_num"]      = shop_cart_item.goods_num_;           // 商品数量
        bool is_member             = !shop_cart_list->shop_cart_list_data_.member_unique.isEmpty();
        json_tmp["goods_price"]    = shop_cart_item.getPrice(is_member);    // 商品价格
        json_tmp["goods_subtotal"] = shop_cart_item.getSubtotal(is_member); // 小计

        json_pending_order_detail.push_back(json_tmp);
    }
    return QString::fromStdString(json_pending_order_detail.dump());
}

void ShopCartList::sendRefreshSignal()
{
    emit preItemReset();
    emit postItemReset();
}

void ShopCartList::resetOrderUnique()
{
    LOG_EVT_INFO("触发订单号生成");
    shop_cart_list_data_.order_unique_ = OrderControl::generateOrderId();
}

const QString ShopCartList::getOrderUnique() const
{
    return shop_cart_list_data_.order_unique_;
}

void ShopCartList::tryResetPaidOrder()
{
    LOG_EVT_INFO("进入tryResetPaidOrder,shop_cart_list_data_.is_paid:{}",shop_cart_list_data_.is_paid);
    if (shop_cart_list_data_.is_paid)
    {
        LOG_EVT_INFO("进入tryResetPaidOrder中");
        shop_cart_list_data_.is_paid = false;
        resetAllInfo();
        LOG_EVT_INFO("结束tryResetPaidOrder");
    }
}

void ShopCartList::setCurItemTimestamp(QVariant timestamp)
{
    setHighlightTimeStamp(timestamp.toString());
}

unsigned int ShopCartList::getTimestampIndex(QVariant timestamp)
{
    auto result_index = 0;
    for (auto &shop_cart_Items_item : shop_cart_Items_)
    {
        if (shop_cart_Items_item.add_date_time_ == timestamp.toString())
        {
            return result_index;
        }
        ++result_index;
    }
    return 0;
}

QString ShopCartList::getMemberInfoJson()
{
    Json result_json;
    result_json["member_unique"]  = shop_cart_list_data_.member_unique.toStdString();
    result_json["member_name"]    = shop_cart_list_data_.member_name.toStdString();
    result_json["member_balance"] = shop_cart_list_data_.member_balance.toStdString();
    result_json["member_point"]   = shop_cart_list_data_.member_point.toStdString();

    return QString::fromStdString(result_json.dump());
}

void ShopCartList::setCurMemberUnique(QVariant member_unique, QVariant member_name, QVariant member_blance, QVariant member_point)
{
    setMemberUnique(member_unique.toString());
    setMemberName(member_name.toString());
    setMemberBlance(member_blance.toString());
    setMemberPoint(member_point.toString());
    sendRefreshSignal();
}

void ShopCartList::clearMemberUnique()
{
    setMemberUnique("");
    setMemberName("");
    setMemberBlance("");
    setMemberPoint("");

    sendRefreshSignal();
    emit sigClearMemberUnique();
}
void ShopCartList::appendGiftItemOrderPromotionByBarcode(QString goods_barcode, bool is_force, bool is_add_2_tag, QString giftInfo)
{
    ShopCartItem item;
    is_gift_goods = true;
    LOG_EVT_INFO("当前处理满赠商品条码为is_gift_goods:{}",is_gift_goods == true);
    auto it = std::find(gift_vector.begin(), gift_vector.end(), goods_barcode);
    LOG_EVT_INFO("当前处理满赠商品条码为:{}",goods_barcode.toStdString());
    LOG_EVT_INFO("giftInfo为:{}",giftInfo.toStdString());
    QStringList tmpArray = giftInfo.split(",");
    LOG_EVT_INFO("tmpArray:{}",tmpArray.size());
    gift_num =tmpArray[11] ; //测试数量
    LOG_EVT_INFO("item.gift_num:{}",gift_num.toStdString());
    if (it == gift_vector.end()) {
        gift_vector.push_back(goods_barcode);
        gift_vector.push_back(gift_num);
    }else{
        auto next_it = std::next(it);
        if (next_it != gift_vector.end()) {
            LOG_EVT_INFO("重新设置满赠商品数量前:{}",(*next_it).toStdString());
            (*next_it) = gift_num;
            LOG_EVT_INFO("重新设置满赠商品数量后:{}",(*next_it).toStdString());
            }
    }
    tryResetPaidOrder();
    auto goods_mgr                   = DataManager::getInstance()->getGoodsMgr();
    auto barcode_scale               = ControlManager::getInstance()->getBarcodeLabelScale();
    bool is_normal_barcode_and_exist = false;

    auto config_tool = ConfigTool::getInstance();

    GoodsInfo goods_info;
        LOG_EVT_INFO("当前处理满赠商品条码为11111:{}",goods_barcode.toStdString());
    if (goods_mgr->getGoodsByBarcode(goods_barcode, goods_info))
    {
        LOG_EVT_INFO("is_normal_barcode_and_exist设置为true");
        is_normal_barcode_and_exist = true;
    }
    else if (goods_barcode.length() == 13 && goods_barcode.left(2) == barcode_scale->barcode_weight_prefix_)
    {
        LOG_EVT_INFO("是不是条码秤条码判断");
        // 是不是条码秤条码(EAN13)
        bool is_valid = true;

        if (config_tool->getSetting(ConfigEnum::IS_VERIFY_EAN13).toBool())
            is_valid = Utils::Barcode::isEan13Valid(goods_barcode);

        if (!is_valid)
            return;

        if (is_valid)
        {
            LOG_EVT_INFO("is_valid is true");
            auto cur_goods_barcode = goods_barcode.left(7);
            if (goods_mgr->getGoodsData()->find(cur_goods_barcode) != goods_mgr->getGoodsData()->end())
            {
                LOG_EVT_INFO("goods_mgr->getGoodsData()->find(cur_goods_barcode) != goods_mgr->getGoodsData()->end()");
                goods_info = (goods_mgr->getGoodsData())->at(cur_goods_barcode);

                emit preItemAppended();

                ShopCartItem item;
                item.goods_barcode_ = cur_goods_barcode;
                item.setCusSubtotal(goods_barcode.right(6).left(5).toDouble() / 100.00);
                item.is_barcode_scale_goods_ = true;
                item.goods_num_              = item.getNum();

                if(true){
                    QMutexLocker locker(&mutex);
                    if (shop_cart_Items_.empty())
                        resetOrderInfo();
                    shop_cart_Items_.append(item);
                }
                setHighlightTimeStamp(item.add_date_time_);

                emit postItemAppended();
                emit sigItemNumChanged(item.goods_barcode_);

                emit focusItem(getTimestampIndex(highlightTimeStamp()));

                return;
            }
            else if (is_force)
            {
                LOG_EVT_INFO("is_force is true");
                goods_info = goods_mgr-> getNoCodeGoods(true);

                emit preItemAppended();

                ShopCartItem item;
                item.goods_barcode_ = goods_info.goods_barcode;
                item.setCusSubtotal(goods_barcode.right(6).left(5).toDouble() / 100.00);
                item.is_barcode_scale_goods_ = true;
                item.goods_num_              = item.getNum();

                if(true){
                    QMutexLocker locker(&mutex);
                    if (shop_cart_Items_.empty())
                        resetOrderInfo();
                    shop_cart_Items_.append(item);
                }
                setHighlightTimeStamp(item.add_date_time_);

                emit postItemAppended();
                emit sigItemNumChanged(item.goods_barcode_);

                emit focusItem(getTimestampIndex(highlightTimeStamp()));

                return;
            }
        }
    }
    // 无法添加
    if (!is_normal_barcode_and_exist)
    {
        return;
    }
    cancelCusOrderPrice();
    bool is_exist_at_shop_cart = false;

    goods_mgr->getGoodsByBarcode(goods_barcode, goods_info);
    bool is_weight = goods_info.isWeight(); //|| goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS;

    bool is_no_code_goods =
        goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_GOODS || goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS;
    // 非称重且不是无码商品
    if (!is_weight && !is_no_code_goods)
    {
        LOG_EVT_INFO("非称重且不是无码商品 shop_cart_Items_.size():{}",shop_cart_Items_.size());
        LOG_EVT_INFO("goods_barcode:{}",goods_barcode.toStdString());
        for (int i = 0; i < shop_cart_Items_.size(); ++i)
        {
            QString numTemp;
            QString numChangeTemp;
            LOG_EVT_INFO("非称重且不是无码商品1111");
            auto &shop_cart_Item = shop_cart_Items_[i];
            numTemp = QString::number(shop_cart_Item.goods_num_);
            numChangeTemp = tmpArray[11];
            LOG_EVT_INFO("shop_cart_Item.goods_barcode_:{}",shop_cart_Item.goods_barcode_.toStdString());
            LOG_EVT_INFO("numTemp:{}",numTemp.toStdString());
            LOG_EVT_INFO("numChangeTemp:{}",numChangeTemp.toStdString());
            LOG_EVT_INFO("numTemp != numChangeTemp:{}",(numTemp != numChangeTemp));
            if (shop_cart_Item.goods_barcode_ == goods_barcode )
            {
                if(numTemp != numChangeTemp){
                   LOG_EVT_INFO("非称重且不是无码商品 购物车存在该商品且该商品数量需要变更===");
                   //++shop_cart_Item.goods_num_;
                   shop_cart_Item.goods_num_ = tmpArray[11].toDouble();
                   setHighlightTimeStamp(shop_cart_Item.add_date_time_);
                   emit postRowChanged(i);
                   emit sigItemNumChanged(shop_cart_Item.goods_barcode_);
                   is_exist_at_shop_cart = true;
                   LOG_EVT_INFO("非称重且不是无码商品11114444===");
                   emit focusItem(getTimestampIndex(highlightTimeStamp()));

   #ifdef _RECOGNITION_
                   if (!is_no_code_goods && is_add_2_tag)
                   {
                       goods_mgr->tryAddTagByRecognition(goods_barcode);
                   }
   #endif
                   break;
                }else{
                   is_exist_at_shop_cart = true;
                }
            }
        }
    }

    // 称重商品 || 无码称重
    if (is_weight || is_no_code_goods)
    {
        LOG_EVT_INFO(" 称重商品 || 无码称重");
        emit preItemAppended();

        ShopCartItem item;
        item.goods_barcode_ = goods_barcode;

        item.goods_num_ = ControlManager::getInstance()->getWeightingScaleControl()->getLastWeightKg();

        if (item.goods_num_ <= 0)
        {
            item.goods_num_ = 1;
        }

        if(true){
            QMutexLocker locker(&mutex);
            if (shop_cart_Items_.empty())
                resetOrderInfo();
            shop_cart_Items_.append(item);
        }
        setHighlightTimeStamp(item.add_date_time_);
        emit focusItem(getTimestampIndex(highlightTimeStamp()));
        emit postItemAppended(); //前端更新
        emit sigItemNumChanged(item.goods_barcode_); //数量更新

#ifdef _RECOGNITION_
        if (!is_no_code_goods && is_add_2_tag)
        {
            goods_mgr->tryAddTagByRecognition(goods_barcode);
        }
#endif
    }
    // 购车内不存在 (非称重)
    else if (!is_exist_at_shop_cart)
    {
        LOG_EVT_INFO("购物车中不存在该商品时进行添加");
        emit preItemAppended();

        ShopCartItem item;
        item.goods_barcode_ = goods_barcode;
        item.goods_num_ = tmpArray[11].toDouble(); //测试数量
        item.cus_subtotal_  = 0.00;
        item.cus_price_ = 0.00;
        if(true){
            QMutexLocker locker(&mutex);
            if (shop_cart_Items_.empty())
                resetOrderInfo();
            shop_cart_Items_.append(item);
        }
        setHighlightTimeStamp(item.add_date_time_);
        emit focusItem(getTimestampIndex(highlightTimeStamp()));

        emit postItemAppended();
        emit sigItemNumChanged(item.goods_barcode_);
        LOG_EVT_INFO("购物车中不存在该商品时进行添加成功！");
#ifdef _RECOGNITION_
        //LOG_EVT_INFO("未定义");
        if (!is_no_code_goods && is_add_2_tag)
        {
            goods_mgr->tryAddTagByRecognition(goods_barcode);
        }
#endif
    }
}
void ShopCartList::appendGiftDiscountItemByBarcode(QString goods_barcode, bool is_force, bool is_add_2_tag, QString gift_num)
{
    gift_barcode = "";
    ShopCartItem item;
    is_gift_goods = true;
    LOG_EVT_INFO("满减满赠商品--当前处理满赠商品条码为is_gift_goods:{}",is_gift_goods == true);
    auto it = std::find(gift_vector.begin(), gift_vector.end(), goods_barcode);
    gift_barcode = goods_barcode;
    LOG_EVT_INFO("满减满赠商品--当前处理满赠商品条码为:{}",goods_barcode.toStdString());
    std::cout<<"barcodee:"<<goods_barcode.toStdString()<<std::endl;

    LOG_EVT_INFO("满减满赠商品--item.gift_num:{}",gift_num.toStdString());
    if (it == gift_vector.end()) {
        gift_vector.push_back(goods_barcode);
        gift_vector.push_back(gift_num);
    }else{
        auto next_it = std::next(it);
        if (next_it != gift_vector.end()) {
            LOG_EVT_INFO("满减满赠商品--重新设置满赠商品数量前:{}",(*next_it).toStdString());
            (*next_it) = gift_num;
            LOG_EVT_INFO("满减满赠商品--重新设置满赠商品数量后:{}",(*next_it).toStdString());
            }
    }
    tryResetPaidOrder();
    auto goods_mgr                   = DataManager::getInstance()->getGoodsMgr();
    auto barcode_scale               = ControlManager::getInstance()->getBarcodeLabelScale();
    bool is_normal_barcode_and_exist = false;

    auto config_tool = ConfigTool::getInstance();

    GoodsInfo goods_info;
    if (goods_mgr->getGoodsByBarcode(goods_barcode, goods_info))
    {
        LOG_EVT_INFO("满减满赠商品--is_normal_barcode_and_exist设置为true");
        is_normal_barcode_and_exist = true;
    }
    else if (goods_barcode.length() == 13 && goods_barcode.left(2) == barcode_scale->barcode_weight_prefix_)
    {
        LOG_EVT_INFO("是不是条码秤条码判断");
        // 是不是条码秤条码(EAN13)
        bool is_valid = true;

        if (config_tool->getSetting(ConfigEnum::IS_VERIFY_EAN13).toBool())
            is_valid = Utils::Barcode::isEan13Valid(goods_barcode);

        if (!is_valid)
            return;

        if (is_valid)
        {
            LOG_EVT_INFO("is_valid is true");
            auto cur_goods_barcode = goods_barcode.left(7);
            if (goods_mgr->getGoodsData()->find(cur_goods_barcode) != goods_mgr->getGoodsData()->end())
            {
                LOG_EVT_INFO("goods_mgr->getGoodsData()->find(cur_goods_barcode) != goods_mgr->getGoodsData()->end()");
                goods_info = (goods_mgr->getGoodsData())->at(cur_goods_barcode);

                emit preItemAppended();

                ShopCartItem item;
                item.goods_barcode_ = cur_goods_barcode;
                item.setCusSubtotal(goods_barcode.right(6).left(5).toDouble() / 100.00);
                item.is_barcode_scale_goods_ = true;
                item.goods_num_              = item.getNum();

                if(true){
                    QMutexLocker locker(&mutex);
                    if (shop_cart_Items_.empty())
                        resetOrderInfo();
                    shop_cart_Items_.append(item);
                }
                setHighlightTimeStamp(item.add_date_time_);

                emit postItemAppended();
                emit sigItemNumChanged(item.goods_barcode_);

                emit focusItem(getTimestampIndex(highlightTimeStamp()));

                return;
            }
            else if (is_force)
            {
                LOG_EVT_INFO("is_force is true");
                goods_info = goods_mgr->getNoCodeGoods(true);

                emit preItemAppended();

                ShopCartItem item;
                item.goods_barcode_ = goods_info.goods_barcode;
                item.setCusSubtotal(goods_barcode.right(6).left(5).toDouble() / 100.00);
                item.is_barcode_scale_goods_ = true;
                item.goods_num_              = item.getNum();
                if(true){
                    QMutexLocker locker(&mutex);
                    if (shop_cart_Items_.empty())
                        resetOrderInfo();
                    shop_cart_Items_.append(item);
                }
                setHighlightTimeStamp(item.add_date_time_);

                emit postItemAppended();
                emit sigItemNumChanged(item.goods_barcode_);

                emit focusItem(getTimestampIndex(highlightTimeStamp()));

                return;
            }
        }
    }
    // 无法添加
    if (!is_normal_barcode_and_exist)
    {
        LOG_EVT_INFO("满减满赠商品--is_normal_barcode_and_exist设置为 false");
        return;
    }
    cancelCusOrderPrice();
    bool is_exist_at_shop_cart = false;

    goods_mgr->getGoodsByBarcode(goods_barcode, goods_info);
    bool is_weight = goods_info.isWeight(); //|| goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS;

    bool is_no_code_goods =
        goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_GOODS || goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS;
    // 非称重且不是无码商品
    if (!is_weight && !is_no_code_goods)
    {
        LOG_EVT_INFO("满减满赠商品--非称重且不是无码商品 ");
        std::cout<<"543254ii444555非称重且不是无码商品"<<std::endl;
        LOG_EVT_INFO("goods_barcode:{}",goods_barcode.toStdString());
        for (int i = 0; i < shop_cart_Items_.size(); ++i)
        {
            std::cout<<"34543534ii444555:"<<i<<std::endl;
            auto &shop_cart_Item = shop_cart_Items_[i];
            std::cout<<"345345345455597897897897:"<<std::endl;
            std::cout<<"shop_cart_Item.goods_barcode_:"<<shop_cart_Item.goods_barcode_.toStdString()<<std::endl;
            std::cout<<"goods_barcode:"<<goods_barcode.toStdString()<<std::endl;
            std::cout<<"gift_num:"<<gift_num.toStdString()<<std::endl;
            if (shop_cart_Item.goods_barcode_ == goods_barcode)
            {
                std::cout<<"ii444:"<<i<<std::endl;
                LOG_EVT_INFO("满减满赠商品--非称重且不是无码商品=========:{}",shop_cart_Item.goods_barcode_.toStdString());
                //++shop_cart_Item.goods_num_;
                shop_cart_Item.goods_num_ = gift_num.toDouble();
                LOG_EVT_INFO("满减满赠商品--非称重且不是无码商品111=========:{}",goods_barcode.toStdString());
                setHighlightTimeStamp(shop_cart_Item.add_date_time_);
                LOG_EVT_INFO("满减满赠商品--非称重且不是无码商品=========34213");
                std::cout<<"满减满赠商品--ii:"<<i<<std::endl;
                emit postRowChanged(i);
                std::cout<<"ii444353454525234525435353353"<<std::endl;
                LOG_EVT_INFO("满减满赠商品--非称重且不是无码商品=========2224 ");
                emit sigItemNumChanged(shop_cart_Item.goods_barcode_);
                std::cout<<"ii44435345452524234412334523353"<<std::endl;
                LOG_EVT_INFO("满减满赠商品--非称重且不是无码商品=========2226 ");
                is_exist_at_shop_cart = true;
                std::cout<<"ii444353454525234523353"<<std::endl;
                emit focusItem(getTimestampIndex(highlightTimeStamp()));
                LOG_EVT_INFO("满减满赠商品--非称重且不是无码商品=========2227 ");
#ifdef _RECOGNITION_
                if (!is_no_code_goods && is_add_2_tag)
                {
                    goods_mgr->tryAddTagByRecognition(goods_barcode);
                }
#endif
                std::cout<<"ii44435345353"<<std::endl;
                break;
            }
            else{
                LOG_EVT_INFO("购物车内无该商品");
                std::cout<<"111111111111111111111111111111"<<std::endl;
            }
        }
    }

    // 称重商品 || 无码称重
    if (is_weight || is_no_code_goods)
    {
        LOG_EVT_INFO("称重商品 || 无码称重");
        LOG_EVT_INFO("goods_barcode:{}",goods_barcode.toStdString());
        for (int i = 0; i < shop_cart_Items_.size(); ++i)
        {
            LOG_EVT_INFO("goods_barcode1:{}",goods_barcode.toStdString());
            auto &shop_cart_Item = shop_cart_Items_[i];
            LOG_EVT_INFO("goods_barcode2:{}",shop_cart_Item.goods_barcode_.toStdString());
            if (shop_cart_Item.goods_barcode_ == goods_barcode)
            {
                LOG_EVT_INFO("goods_barcode2:{}",shop_cart_Item.goods_barcode_.toStdString());
                //++shop_cart_Item.goods_num_;
                shop_cart_Item.goods_num_ = gift_num.toDouble();
                setHighlightTimeStamp(shop_cart_Item.add_date_time_);
                emit postRowChanged(i);
                emit sigItemNumChanged(shop_cart_Item.goods_barcode_);
                is_exist_at_shop_cart = true;
                emit focusItem(getTimestampIndex(highlightTimeStamp()));
#ifdef _RECOGNITION_
                if (!is_no_code_goods && is_add_2_tag)
                {
                    goods_mgr->tryAddTagByRecognition(goods_barcode);
                }
#endif
                break;
            }else{
                if(i == shop_cart_Items_.size() -1){
                    is_exist_at_shop_cart = false;
                }
            }
        }
    }
    // 购车内不存在 (非称重)
    if (!is_exist_at_shop_cart)
    {
        std::cout<<"22222222222222222222222222222"<<std::endl;
        LOG_EVT_INFO("满减满赠商品--购物车中不存在该商品时进行添加");

        emit preItemAppended();

        ShopCartItem item;
        item.goods_barcode_ = goods_barcode;
        item.goods_num_ = gift_num.toDouble(); //测试数量
        item.cus_subtotal_  = 0.00;
        item.cus_price_ = 0.00;
        LOG_EVT_INFO("加锁前数据查看 item.goods_barcode_:{};item.goods_num_:{}",item.goods_barcode_.toStdString(),item.goods_num_);
        if(true){
            QMutexLocker locker(&mutex);
            if (shop_cart_Items_.empty())
                resetOrderInfo();
            std::cout<< "length111:" << shop_cart_Items_.size()<<std::endl;
            shop_cart_Items_.append(item);
            std::cout<< "length222:" << shop_cart_Items_.size()<<std::endl;
        }
            std::cout<< "=== shop_cart list ==="<<std::endl;
            std::cout<< "length:" << shop_cart_Items_.size()<<std::endl;

            for (auto it = shop_cart_Items_.begin(); it != shop_cart_Items_.end(); ++it) {
                std::cout<< "goods_barcode_:" << it->goods_barcode_.toStdString()
                         << "cus_subtotal_:" << it->cus_subtotal_
                         << "goods_num_:" << it->goods_num_
                         << "cus_price_:" << it->cus_price_<<std::endl;
            }
        std::cout<<"222222222222222222222222222227777777777777"<<std::endl;
        LOG_EVT_INFO("满减满赠商品--添加商品：{}",goods_barcode.toStdString());
        setHighlightTimeStamp(item.add_date_time_);
        LOG_EVT_INFO("满减满赠商品--添加商品111：{}",goods_barcode.toStdString());
        emit focusItem(getTimestampIndex(highlightTimeStamp()));
        emit postItemAppended();
        LOG_EVT_INFO("满减满赠商品--添加商品333：{}",goods_barcode.toStdString());

    }
}
void ShopCartList::appendGiftItemByBarcode(QString goods_barcode, bool is_force, bool is_add_2_tag, QString giftInfo)
{
    gift_barcode = "";
    ShopCartItem item;
    is_gift_goods = true;
    LOG_EVT_INFO("当前处理满赠商品条码为is_gift_goods:{}",is_gift_goods == true);
    auto it = std::find(gift_vector.begin(), gift_vector.end(), goods_barcode);
    gift_barcode = goods_barcode;
    LOG_EVT_INFO("当前处理满赠商品条码为:{}",goods_barcode.toStdString());

    LOG_EVT_INFO("giftInfo为:{}",giftInfo.toStdString());
    QStringList tmpArray = giftInfo.split(",");
    LOG_EVT_INFO("tmpArray:{}",tmpArray.size());
    gift_num =tmpArray[11] ; //测试数量
    LOG_EVT_INFO("item.gift_num:{}",gift_num.toStdString());
    if (it == gift_vector.end()) {
        gift_vector.push_back(goods_barcode);
        gift_vector.push_back(gift_num);
    }else{
        auto next_it = std::next(it);
        if (next_it != gift_vector.end()) {
            LOG_EVT_INFO("重新设置满赠商品数量前:{}",(*next_it).toStdString());
            (*next_it) = gift_num;
            LOG_EVT_INFO("重新设置满赠商品数量后:{}",(*next_it).toStdString());
            }
    }
    tryResetPaidOrder();
    auto goods_mgr                   = DataManager::getInstance()->getGoodsMgr();
    auto barcode_scale               = ControlManager::getInstance()->getBarcodeLabelScale();
    bool is_normal_barcode_and_exist = false;

    auto config_tool = ConfigTool::getInstance();

    GoodsInfo goods_info;
    if (goods_mgr->getGoodsByBarcode(goods_barcode, goods_info))
    {
        LOG_EVT_INFO("is_normal_barcode_and_exist设置为true");
        is_normal_barcode_and_exist = true;
    }
    else if (goods_barcode.length() == 13 && goods_barcode.left(2) == barcode_scale->barcode_weight_prefix_)
    {
        LOG_EVT_INFO("是不是条码秤条码判断");
        // 是不是条码秤条码(EAN13)
        bool is_valid = true;

        if (config_tool->getSetting(ConfigEnum::IS_VERIFY_EAN13).toBool())
            is_valid = Utils::Barcode::isEan13Valid(goods_barcode);

        if (!is_valid)
            return;

        if (is_valid)
        {
            LOG_EVT_INFO("is_valid is true");
            auto cur_goods_barcode = goods_barcode.left(7);
            if (goods_mgr->getGoodsData()->find(cur_goods_barcode) != goods_mgr->getGoodsData()->end())
            {
                LOG_EVT_INFO("goods_mgr->getGoodsData()->find(cur_goods_barcode) != goods_mgr->getGoodsData()->end()");
                goods_info = (goods_mgr->getGoodsData())->at(cur_goods_barcode);

                emit preItemAppended();

                ShopCartItem item;
                item.goods_barcode_ = cur_goods_barcode;
                item.setCusSubtotal(goods_barcode.right(6).left(5).toDouble() / 100.00);
                item.is_barcode_scale_goods_ = true;
                item.goods_num_              = item.getNum();

                if(true){
                    QMutexLocker locker(&mutex);
                    if (shop_cart_Items_.empty())
                        resetOrderInfo();
                    shop_cart_Items_.append(item);
                }
                setHighlightTimeStamp(item.add_date_time_);

                emit postItemAppended();
                emit sigItemNumChanged(item.goods_barcode_);

                emit focusItem(getTimestampIndex(highlightTimeStamp()));

                return;
            }
            else if (is_force)
            {
                LOG_EVT_INFO("is_force is true");
                goods_info = goods_mgr->getNoCodeGoods(true);

                emit preItemAppended();

                ShopCartItem item;
                item.goods_barcode_ = goods_info.goods_barcode;
                item.setCusSubtotal(goods_barcode.right(6).left(5).toDouble() / 100.00);
                item.is_barcode_scale_goods_ = true;
                item.goods_num_              = item.getNum();

                if(true){
                    QMutexLocker locker(&mutex);
                    if (shop_cart_Items_.empty())
                        resetOrderInfo();
                    shop_cart_Items_.append(item);
                }
                setHighlightTimeStamp(item.add_date_time_);

                emit postItemAppended();
                emit sigItemNumChanged(item.goods_barcode_);

                emit focusItem(getTimestampIndex(highlightTimeStamp()));

                return;
            }
        }
    }
    // 无法添加
    if (!is_normal_barcode_and_exist)
    {
        return;
    }
    cancelCusOrderPrice();
    bool is_exist_at_shop_cart = false;

    goods_mgr->getGoodsByBarcode(goods_barcode, goods_info);
    bool is_weight = goods_info.isWeight(); //|| goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS;

    bool is_no_code_goods =
        goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_GOODS || goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS;
    // 非称重且不是无码商品
    if (!is_weight && !is_no_code_goods)
    {
        LOG_EVT_INFO("非称重且不是无码商品 ");
        LOG_EVT_INFO("goods_barcode:{}",goods_barcode.toStdString());
        for (int i = 0; i < shop_cart_Items_.size(); ++i)
        {
            auto &shop_cart_Item = shop_cart_Items_[i];
            if (shop_cart_Item.goods_barcode_ == goods_barcode)
            {
                //++shop_cart_Item.goods_num_;
                shop_cart_Item.goods_num_ = tmpArray[11].toDouble();
                setHighlightTimeStamp(shop_cart_Item.add_date_time_);
                emit postRowChanged(i);
                emit sigItemNumChanged(shop_cart_Item.goods_barcode_);
                is_exist_at_shop_cart = true;
                emit focusItem(getTimestampIndex(highlightTimeStamp()));
#ifdef _RECOGNITION_
                if (!is_no_code_goods && is_add_2_tag)
                {
                    goods_mgr->tryAddTagByRecognition(goods_barcode);
                }
#endif
                break;
            }
        }
    }

    // 称重商品 || 无码称重
    if (is_weight || is_no_code_goods)
    {
        LOG_EVT_INFO("称重商品 || 无码称重");
        LOG_EVT_INFO("goods_barcode:{}",goods_barcode.toStdString());
        for (int i = 0; i < shop_cart_Items_.size(); ++i)
        {
            LOG_EVT_INFO("goods_barcode1:{}",goods_barcode.toStdString());
            auto &shop_cart_Item = shop_cart_Items_[i];
            LOG_EVT_INFO("goods_barcode2:{}",shop_cart_Item.goods_barcode_.toStdString());
            if (shop_cart_Item.goods_barcode_ == goods_barcode)
            {
                LOG_EVT_INFO("goods_barcode2:{}",shop_cart_Item.goods_barcode_.toStdString());
                //++shop_cart_Item.goods_num_;
                shop_cart_Item.goods_num_ = tmpArray[11].toDouble();
                setHighlightTimeStamp(shop_cart_Item.add_date_time_);
                emit postRowChanged(i);
                emit sigItemNumChanged(shop_cart_Item.goods_barcode_);
                is_exist_at_shop_cart = true;
                emit focusItem(getTimestampIndex(highlightTimeStamp()));
#ifdef _RECOGNITION_
                if (!is_no_code_goods && is_add_2_tag)
                {
                    goods_mgr->tryAddTagByRecognition(goods_barcode);
                }
#endif
                break;
            }else{
                if(i == shop_cart_Items_.size() -1){
                    is_exist_at_shop_cart = false;
                }
            }
        }
//        emit preItemAppended();

//        ShopCartItem item;
//        item.goods_barcode_ = goods_barcode;

//        item.goods_num_ = ControlManager::getInstance()->getWeightingScaleControl()->getLastWeightKg();

//        if (item.goods_num_ <= 0)
//        {
//            item.goods_num_ = 1;
//        }

//        if (shop_cart_Items_.empty())
//            resetOrderInfo();

//        shop_cart_Items_.append(item); //数据更新
//        setHighlightTimeStamp(item.add_date_time_);
//        emit focusItem(getTimestampIndex(highlightTimeStamp()));
//        emit postItemAppended(); //前端更新
//        emit sigItemNumChanged(item.goods_barcode_); //数量更新

//#ifdef _RECOGNITION_
//        if (!is_no_code_goods && is_add_2_tag)
//        {
//            goods_mgr->tryAddTagByRecognition(goods_barcode);
//        }
//#endif
    }
    // 购车内不存在 (非称重)
    if (!is_exist_at_shop_cart)
    {
        LOG_EVT_INFO("购物车中不存在该商品时进行添加");
        emit preItemAppended();

        ShopCartItem item;
        item.goods_barcode_ = goods_barcode;
        item.goods_num_ = tmpArray[11].toDouble(); //测试数量
        item.cus_subtotal_  = 0.00;
        item.cus_price_ = 0.00;
        if(true){
            QMutexLocker locker(&mutex);
            if (shop_cart_Items_.empty())
                resetOrderInfo();
            shop_cart_Items_.append(item);
        }
        LOG_EVT_INFO("添加商品：{}",goods_barcode.toStdString());
        setHighlightTimeStamp(item.add_date_time_);
        emit focusItem(getTimestampIndex(highlightTimeStamp()));

        emit postItemAppended();
        //emit sigItemNumChanged(item.goods_barcode_);

#ifdef _RECOGNITION_
        //LOG_EVT_INFO("未定义");
        if (!is_no_code_goods && is_add_2_tag)
        {
            goods_mgr->tryAddTagByRecognition(goods_barcode);
        }
#endif
    }
}
void ShopCartList::appendIndepItemByBarcode(QString goods_barcode, bool is_force, bool is_add_2_tag,QString goods_binding_unique )
{
    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart_Item = shop_cart_Items_[i];

        if (shop_cart_Item.goods_barcode_ == goods_barcode)
        {
            shop_cart_Item.is_barcode_scale_goods_ = false;
            shop_cart_Item.resetPriceType();
        }
    }
    LOG_EVT_INFO("当前处理商品条码为:{}",goods_barcode.toStdString());
    tryResetPaidOrder();

    auto goods_mgr                   = DataManager::getInstance()->getGoodsMgr();
    bool is_normal_barcode_and_exist = false;


    GoodsInfo goods_info;
    if (goods_mgr->getGoodsByBarcode(goods_barcode, goods_info))
    {
        LOG_EVT_INFO("is_normal_barcode_and_exist设置为true");
        is_normal_barcode_and_exist = true;
    }
    // 无法添加
    if (!is_normal_barcode_and_exist)
    {
        return;
    }

    cancelCusOrderPrice();

    goods_mgr->getGoodsByBarcode(goods_barcode, goods_info);

    goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_GOODS || goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS;

    LOG_EVT_INFO("捆绑商品-当前购物车行数:{}",shop_cart_Items_.size());
    emit preItemAppended();
    LOG_EVT_INFO("捆绑商品:{}",shop_cart_Items_.size());
    if(shop_cart_Items_.size() <= 0 ){
        LOG_EVT_INFO("购物列表没有数据，直接添加捆绑数据到购物车！");
        ShopCartItem item;
        item.goods_barcode_ = goods_barcode;
        item.goods_binding_unique = goods_binding_unique;
        item.goods_num_ = 0;
        if(true){
            QMutexLocker locker(&mutex);
            if (shop_cart_Items_.empty())
                resetOrderInfo();
            shop_cart_Items_.append(item);
        }
        setHighlightTimeStamp(item.add_date_time_);
        emit focusItem(getTimestampIndex(highlightTimeStamp()));
        emit postItemAppended(); //前端更新
        emit sigItemNumChanged(item.goods_barcode_); //数量更新
    }else
    {
        bool findCart = false;
        for (int i = 0; i < shop_cart_Items_.size(); i++)
        {
            LOG_EVT_INFO("购物列表已存在捆绑商品行数：{}",shop_cart_Items_.size());
            LOG_EVT_INFO("当前购物车检索ID:{}",i);
            auto &shop_cart_Item = shop_cart_Items_[i];
            LOG_EVT_INFO("商品信息goods_barcode:{}；goods_binding_unique：{}",goods_barcode.toStdString(),goods_binding_unique.toStdString());
            LOG_EVT_INFO("检索行信息goods_barcode:{}；goods_binding_unique：{}",shop_cart_Item.goods_barcode_.toStdString(),shop_cart_Item.goods_binding_unique.toStdString());
            if (shop_cart_Item.goods_barcode_ == goods_barcode && shop_cart_Item.goods_binding_unique == goods_binding_unique)
            {
                 LOG_EVT_INFO("购物列表第{}行，已存在捆绑商品",i);
                 findCart = true;
            }
        }
        if(!findCart){
            LOG_EVT_INFO("购物列表存在数据但不存在捆绑数据");
            ShopCartItem items;
            items.goods_barcode_ = goods_barcode;
            items.goods_binding_unique = goods_binding_unique;
            items.goods_num_ = 0;
            if(true){
                QMutexLocker locker(&mutex);
                if (shop_cart_Items_.empty())
                    resetOrderInfo();
                shop_cart_Items_.append(items);
            }
            LOG_EVT_INFO("购物列表存在数据但不存在捆绑数据31");
            setHighlightTimeStamp(items.add_date_time_);
            emit focusItem(getTimestampIndex(highlightTimeStamp()));
            emit postItemAppended(); //前端更新
            emit sigItemNumChanged(items.goods_barcode_); //数量更新
            LOG_EVT_INFO("购物列表存在数据但不存在捆绑数据 捆绑数据添加成功！");
        }
    }
}
void ShopCartList::appendItemByBarcode(QString goods_barcode, bool is_force, bool is_add_2_tag,QString goods_binding_unique)
{
    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart_Item = shop_cart_Items_[i];

        if (shop_cart_Item.goods_barcode_ == goods_barcode)
        {
            shop_cart_Item.is_barcode_scale_goods_ = false;
            shop_cart_Item.resetPriceType();
        }
    }
    LOG_EVT_INFO("1当前处理商品条码为:{}",goods_barcode.toStdString());
    tryResetPaidOrder();

    auto goods_mgr                   = DataManager::getInstance()->getGoodsMgr();
    auto barcode_scale               = ControlManager::getInstance()->getBarcodeLabelScale();
    bool is_normal_barcode_and_exist = false;

    auto config_tool = ConfigTool::getInstance();

    GoodsInfo goods_info;
    if (goods_mgr->getGoodsByBarcode(goods_barcode, goods_info))
    {
        LOG_EVT_INFO("is_normal_barcode_and_exist设置为true");
        is_normal_barcode_and_exist = true;
    }
    else if (goods_barcode.length() == 13 && goods_barcode.left(2) == barcode_scale->barcode_weight_prefix_)
    {
        LOG_EVT_INFO("是不是条码秤条码判断");
        // 是不是条码秤条码(EAN13)
        bool is_valid = true;

        if (config_tool->getSetting(ConfigEnum::IS_VERIFY_EAN13).toBool())
            is_valid = Utils::Barcode::isEan13Valid(goods_barcode);

        if (!is_valid){
            //校验失败语音提示
            LOG_EVT_INFO("商品码校验失败");
            ControlManager::getInstance()->getTtsControl()->say("商品码校验失败");
            LOG_EVT_INFO("商品码校验失败1");
            return;
         }
        if (is_valid)
        {
            LOG_EVT_INFO("is_valid is true");
            auto cur_goods_barcode = goods_barcode.left(7);
            if (goods_mgr->getGoodsData()->find(cur_goods_barcode) != goods_mgr->getGoodsData()->end())
            {
                LOG_EVT_INFO("goods_mgr->getGoodsData()->find(cur_goods_barcode) != goods_mgr->getGoodsData()->end()");
                goods_info = (goods_mgr->getGoodsData())->at(cur_goods_barcode);

                emit preItemAppended();

                ShopCartItem item;
                item.goods_barcode_ = cur_goods_barcode;
                item.setCusSubtotal(goods_barcode.right(6).left(5).toDouble() / 100.00);
                item.is_barcode_scale_goods_ = true;
                item.goods_num_              = item.getNum();

                if(true){
                    QMutexLocker locker(&mutex);
                    if (shop_cart_Items_.empty())
                        resetOrderInfo();
                    shop_cart_Items_.append(item);
                }
                setHighlightTimeStamp(item.add_date_time_);

                emit postItemAppended();
                emit sigItemNumChanged(item.goods_barcode_);

                emit focusItem(getTimestampIndex(highlightTimeStamp()));

                return;
            }
            else if (is_force)
            {
                LOG_EVT_INFO("is_force is true");
                goods_info = goods_mgr->getNoCodeGoods(true);

                emit preItemAppended();

                ShopCartItem item;
                item.goods_barcode_ = goods_info.goods_barcode;
                item.setCusSubtotal(goods_barcode.right(6).left(5).toDouble() / 100.00);
                item.is_barcode_scale_goods_ = true;
                item.goods_num_              = item.getNum();

                if(true){
                    QMutexLocker locker(&mutex);
                    if (shop_cart_Items_.empty())
                        resetOrderInfo();
                    shop_cart_Items_.append(item);
                }
                setHighlightTimeStamp(item.add_date_time_);

                emit postItemAppended();
                emit sigItemNumChanged(item.goods_barcode_);

                emit focusItem(getTimestampIndex(highlightTimeStamp()));

                return;
            }
        }
    }

    // 无法添加
    if (!is_normal_barcode_and_exist)
    {
        return;
    }

    cancelCusOrderPrice();
    bool is_exist_at_shop_cart = false;

    goods_mgr->getGoodsByBarcode(goods_barcode, goods_info);
    bool is_weight = goods_info.isWeight(); //|| goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS;

    bool is_no_code_goods =
        goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_GOODS || goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS;

    // 非称重且不是无码商品
    if (!is_weight && !is_no_code_goods)
    {
        LOG_EVT_INFO("非称重且不是无码商品 ");
        for (int i = 0; i < shop_cart_Items_.size(); ++i)
        {
            auto &shop_cart_Item = shop_cart_Items_[i];
            LOG_EVT_INFO(":{}:{}:{}",shop_cart_Item.is_cus_origin_price_,shop_cart_Item.is_cus_origin_price_,shop_cart_Item.cus_price_);
            LOG_EVT_INFO(" ==非捆绑商品信息== goods_barcode:{}；goods_binding_unique：{}",goods_barcode.toStdString(),goods_binding_unique.toStdString());
            LOG_EVT_INFO("检索行信息goods_barcode:{}；goods_binding_unique：{}",shop_cart_Item.goods_barcode_.toStdString(),shop_cart_Item.goods_binding_unique.toStdString());
            if (shop_cart_Item.goods_barcode_ == goods_barcode  && shop_cart_Item.goods_binding_unique == goods_binding_unique)
            {
                ++shop_cart_Item.goods_num_;
                setHighlightTimeStamp(shop_cart_Item.add_date_time_);

                emit postRowChanged(i);
                emit sigItemNumChanged(shop_cart_Item.goods_barcode_);
                is_exist_at_shop_cart = true;
                emit focusItem(getTimestampIndex(highlightTimeStamp()));

#ifdef _RECOGNITION_
                if (!is_no_code_goods && is_add_2_tag)
                {
                    goods_mgr->tryAddTagByRecognition(goods_barcode);
                }
#endif
                break;
            }
        }
    }

    // 称重商品 || 无码称重
    if (is_weight || is_no_code_goods)
    {
        LOG_EVT_INFO("称重商品 || 无码称重");
        emit preItemAppended();

        ShopCartItem item;
        item.goods_barcode_ = goods_barcode;

        item.goods_num_ = ControlManager::getInstance()->getWeightingScaleControl()->getLastWeightKg();

        if (item.goods_num_ <= 0)
        {
            item.goods_num_ = 1;
        }

        if(true){
            QMutexLocker locker(&mutex);
            if (shop_cart_Items_.empty())
                resetOrderInfo();
            shop_cart_Items_.append(item);
        }
        setHighlightTimeStamp(item.add_date_time_);
        emit focusItem(getTimestampIndex(highlightTimeStamp()));
        emit postItemAppended(); //前端更新
        emit sigItemNumChanged(item.goods_barcode_); //数量更新

#ifdef _RECOGNITION_
        if (!is_no_code_goods && is_add_2_tag)
        {
            goods_mgr->tryAddTagByRecognition(goods_barcode);
        }
#endif
    }
    // 购车内不存在 (非称重)
    else if (!is_exist_at_shop_cart)
    {
        LOG_EVT_INFO("购物车中不存在该商品时进行添加");
        emit preItemAppended();

        ShopCartItem item;
        item.goods_barcode_ = goods_barcode;

        if(true){
            QMutexLocker locker(&mutex);
            if (shop_cart_Items_.empty())
                resetOrderInfo();
            shop_cart_Items_.append(item);
        }
        setHighlightTimeStamp(item.add_date_time_);
        emit focusItem(getTimestampIndex(highlightTimeStamp()));

        emit postItemAppended();
        //emit sigItemNumChanged(item.goods_barcode_);

#ifdef _RECOGNITION_
        //LOG_EVT_INFO("未定义");
        if (!is_no_code_goods && is_add_2_tag)
        {
            goods_mgr->tryAddTagByRecognition(goods_barcode);
        }
#endif
    }


}
void ShopCartList::appendNoCodeItemByBarcode(QVariant goods_barcode, QVariant price, QVariant num)
{
    tryResetPaidOrder();
    cancelCusOrderPrice();
    auto      goods_mgr = DataManager::getInstance()->getGoodsMgr();
    GoodsInfo goods_info;
    goods_mgr->getGoodsByBarcode(goods_barcode.toString(), goods_info);
    bool is_weight        = goods_info.isWeight();
    bool is_no_code_goods = goods_info.goods_id == (int)EnumTool::UidEnum::ID_NO_CODE_GOODS;

    // 无码称重商品
    if (is_weight)
    {
        emit preItemAppended();

        ShopCartItem item;
        item.goods_barcode_ = goods_barcode.toString();
        auto weight         = ControlManager::getInstance()->getWeightingScaleControl()->getLastWeightKg();
        item.goods_num_     = weight == 0 ? 1 : weight;
        item.setCusOriginPrice(price.toDouble());

        if(true){
            QMutexLocker locker(&mutex);
            if (shop_cart_Items_.empty())
                resetOrderInfo();
            shop_cart_Items_.append(item);
        }
        emit postItemAppended();
        emit sigItemNumChanged(item.goods_barcode_);

        return;
    }
    // 无码商品
    else if (is_no_code_goods)
    {
        emit preItemAppended();

        ShopCartItem item;
        item.goods_barcode_ = goods_barcode.toString();
        item.goods_num_     = num.toDouble();
        item.setCusOriginPrice(price.toDouble());

        if(true){
            QMutexLocker locker(&mutex);
            if (shop_cart_Items_.empty())
                resetOrderInfo();
            shop_cart_Items_.append(item);
        }
        emit postItemAppended();

        emit sigItemNumChanged(item.goods_barcode_);

        return;
    }
    throw "";
}

void ShopCartList::setItemPriceByBarcode(QVariant goods_barcode, QVariant price, QVariant date_time)
{
    cancelCusOrderPrice();
    bool is_exist = false;

    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart_Item = shop_cart_Items_[i];

        if (shop_cart_Item.goods_barcode_ == goods_barcode.toString() && shop_cart_Item.add_date_time_ == date_time.toString())
        {
            shop_cart_Item.setCusPrice(price.toDouble());
            emit postRowChanged(i);
            is_exist = true;
            break;
        }
    }
}
void ShopCartList::setItemsubtotalNotimeByBarcode(QVariant goods_barcode, QVariant subtotal,QString goods_binding_unique)
{
    cancelCusOrderPrice();

    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart_Item = shop_cart_Items_[i];

        if (shop_cart_Item.goods_barcode_ == goods_barcode.toString() && shop_cart_Item.goods_binding_unique == goods_binding_unique)
        {
            shop_cart_Item.setCusSubAcctotal(subtotal.toDouble());
            emit postRowChanged(i);
        }
    }
}
void ShopCartList::setItemNumNoTimeByBarcode(QVariant goods_barcode, QVariant num,QString goods_binding_unique)
{
    cancelCusOrderPrice();

    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart_Item = shop_cart_Items_[i];

        if (shop_cart_Item.goods_barcode_ == goods_barcode && shop_cart_Item.goods_binding_unique == goods_binding_unique)
        {
            if (shop_cart_Item.is_barcode_scale_goods_ == true)
            {
                shop_cart_Item.is_barcode_scale_goods_ = false;
                shop_cart_Item.resetPriceType();
            }
            shop_cart_Item.goods_num_ += num.toDouble();
            shop_cart_Item.price_type_             = ShopCartPriceType::CUS_DISCOUNT;
            emit postRowChanged(i);
            emit sigItemNumChanged(shop_cart_Item.goods_barcode_);
            break;
        }
    }
}
void ShopCartList::setItemNumByBarcode(QVariant goods_barcode, QVariant num, QVariant date_time)
{
    auto config_tool = ConfigTool::getInstance();
    auto goods_promotion_ctrl = GoodsPromotionCtrl::getInstance();
    cancelCusOrderPrice();
    bool is_exist = false;

    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart_Item = shop_cart_Items_[i];

        if (shop_cart_Item.goods_barcode_ == goods_barcode && shop_cart_Item.add_date_time_ == date_time)
        {
            if (shop_cart_Item.is_barcode_scale_goods_ == true)
            {
                shop_cart_Item.is_barcode_scale_goods_ = false;
                shop_cart_Item.resetPriceType();
            }
            shop_cart_Item.goods_num_ = num.toDouble();
//            shop_cart_Item.price_type_             = ShopCartPriceType::CUS_DISCOUNT;
            // 当扫完码之后，如果有网络就请求满减的接口

            if (config_tool->getSetting(ConfigToolEnum::ConfigEnum::IS_GOODS_MANJIANMANZENG).toBool())
            {
                 LOG_EVT_INFO("开始商品<<第几件打几折>>");
                 //
//                 shop_cart_Item.price_type_             = ShopCartPriceType::CUS_DISCOUNT;
//                 double pricesTemp = Utils::roundDecimal(shop_cart_Item.getPrice(), 2);
//                 shop_cart_Item.price_type_             = ShopCartPriceType::CUS_SUBTOTAL;
//                 shop_cart_Item.setCusSubtotal(((pricesTemp)*(num.toDouble()))) ;

                 auto      goods_mgr = DataManager::getInstance()->getGoodsMgr();
                 GoodsInfo goods_info;
                 goods_mgr->getGoodsByBarcode(goods_barcode.toString(), goods_info);
                 std::vector<QString> stringVector = goods_promotion_ctrl->queryGoodsPromotionForQml( QString::number(goods_info.goods_id));
                 LOG_EVT_INFO("此处展示函数返回的stringVector数据：{}",stringVector.size());
                 QString data1, data2, data3;
                 if (stringVector.size() == 3) {
                    data1 = stringVector[0];
                    data2 = stringVector[1];
                    data3 = stringVector[2];
                 }
                 LOG_EVT_INFO("data1:{}",data1.toStdString());
                 LOG_EVT_INFO("data2:{}",data2.toStdString());
                 LOG_EVT_INFO("data3:{}",data3.toStdString());
                 stringVector.clear();
                 LOG_EVT_INFO("stringVector清理后数据：{}data3.length:{}",stringVector.size(),data3.length());
                 if (data3.length() > 5)
                 {
                     QStringList manjianArray5;
                     QStringList manjianArray6 = data3.split(";");
                     LOG_EVT_INFO("manjianArray6.length():{}",manjianArray6.length());
                     for (int m = 0; m < manjianArray6.length(); m++)
                     {
                         manjianArray5 = manjianArray6[m].split("^");
//                         for (int i = 0; i < location; i++)
//                         {
//                             if (listModel.get(i).isGift === "1")
//                             {
//                                 continue;
//                             }
                         LOG_EVT_INFO("QString::number(goods_info.goods_id):{}",QString::number(goods_info.goods_id).toStdString());
                         LOG_EVT_INFO("manjianArray5[0]:{}",manjianArray5[0].toStdString());
                             if (manjianArray5.length() > 0 && (manjianArray5[0].toStdString() == QString::number(goods_info.goods_id).toStdString()))
                             {
                                 LOG_EVT_INFO("进入打折操作");
                                 //加入商品打折处理，此处更新小计 第二件起一折  1、会员价格不支持折扣
                                 ShopCartItem item;
                                 shop_cart_Item.price_type_             = ShopCartPriceType::CUS_DISCOUNT;
                                 double priceTemp = Utils::roundDecimal(shop_cart_Item.getPrice(), 2);
                                 shop_cart_Item.price_type_             = ShopCartPriceType::CUS_SUBTOTAL;

                                // priceTmp = listModel.get(i).costPrice;
                                 if (manjianArray5[1] != "" && manjianArray5[2] == "" && manjianArray5[3] == "" && num.toInt() >= manjianArray5[1].toInt()*1)
                                 {
                                     LOG_EVT_INFO("开始打折操作1");
                                     //shop_cart_Item.setCusSubtotal(Utils::roundDecimal((((num.toDouble() - 1) *0.1) + 1) * priceTemp));
                                     LOG_EVT_INFO("manjianArray5[1]:{}manjianArray5[4]:{}",manjianArray5[1].toStdString(),manjianArray5[4].toStdString());
                                     shop_cart_Item.setCusSubtotal(priceTemp*(manjianArray5[1].toInt()-1) + priceTemp*(num.toDouble() - manjianArray5[1].toInt()+1) * manjianArray5[4].toInt()/10 );
                                     //allPriceTmp = priceTmp*(manjianArray5[1]-1) + priceTmp*(listModel.get(i).counts-manjianArray5[1]+1)*manjianArray5[4]/10;
//                                     listModel.set(i, {allPrice: (allPriceTmp*1).toFixed(2)});
//                                     listModel.set(i, {price: (allPriceTmp/listModel.get(i).counts*1).toFixed(2)});
                                 }
                                 else if (manjianArray5[1] != "" && manjianArray5[2] != "" && manjianArray5[3] == "" && num.toInt() >= manjianArray5[1].toInt()*1
                                          && num.toInt() < manjianArray5[2].toInt()*1)
                                 {
                                     LOG_EVT_INFO("开始打折操作2");
                                     shop_cart_Item.setCusSubtotal(priceTemp*(manjianArray5[1].toInt()-1)
                                             + priceTemp*(num.toDouble() - manjianArray5[1].toInt()+1) * manjianArray5[4].toInt()/10 );
//                                     allPriceTmp = priceTmp*(manjianArray5[1]-1) +
//                                             priceTmp*(listModel.get(i).counts-manjianArray5[1]+1)*manjianArray5[4]/10;
//                                     listModel.set(i, {allPrice: (allPriceTmp*1).toFixed(2)});
//                                     listModel.set(i, {.price =  (allPriceTmp/listModel.get(i).counts*1).toFixed(2)});
                                 }
                                 else if (manjianArray5[1] != "" && manjianArray5[2] != "" && manjianArray5[3] == "" && num.toInt() >= manjianArray5[2].toInt()*1)
                                 {
                                     LOG_EVT_INFO("开始打折操作3");
                                     shop_cart_Item.setCusSubtotal(priceTemp*(manjianArray5[1].toInt()-1)
                                             + priceTemp*(manjianArray5[2].toInt() - manjianArray5[1].toInt())*manjianArray5[4].toInt()/10
                                             + priceTemp*(num.toDouble() - manjianArray5[2].toInt()+1) * manjianArray5[5].toInt()/10 );
//                                     allPriceTmp = priceTmp*(manjianArray5[1]-1)
//                                             + priceTmp*(manjianArray5[2]-manjianArray5[1])*manjianArray5[4]/10
//                                             + priceTmp*(listModel.get(i).counts-manjianArray5[2]+1)*manjianArray5[5]/10;
//                                     listModel.set(i, {allPrice: (allPriceTmp*1).toFixed(2)});
//                                     listModel.set(i, {price: (allPriceTmp/listModel.get(i).counts*1).toFixed(2)});
                                 }
                                 else if (manjianArray5[1] != "" && manjianArray5[2] != "" && manjianArray5[3] != "" && num.toInt()*1 >= manjianArray5[1].toInt()*1
                                         && num.toInt()*1 < manjianArray5[2].toInt()*1)
                                 {
                                     LOG_EVT_INFO("开始打折操作4");
                                     LOG_EVT_INFO("manjianArray5[1]:{}manjianArray5[4]:{}",manjianArray5[1].toStdString(),manjianArray5[4].toStdString());
                                     LOG_EVT_INFO("priceTemp:{}manjianArray5[4]:{}",priceTemp,manjianArray5[4].toStdString());
                                     shop_cart_Item.setCusSubtotal(priceTemp*(manjianArray5[1].toInt()-1) + priceTemp*(num.toDouble() - manjianArray5[1].toInt()+1) * manjianArray5[4].toInt()/10 );
                                     LOG_EVT_INFO("===================111:{}",priceTemp*(manjianArray5[1].toInt()-1) + priceTemp*(num.toDouble() - manjianArray5[1].toInt()+1) * manjianArray5[4].toInt()/10 );
//                                     allPriceTmp = priceTmp*(manjianArray5[1]-1) + priceTmp*(listModel.get(i).counts-manjianArray5[1]+1)*manjianArray5[4]/10;
                                 }
                                 else if (manjianArray5[1] != "" && manjianArray5[2] != "" && manjianArray5[3] != "" && num.toInt()*1 >= manjianArray5[2].toInt()*1
                                         && num.toInt()*1 < manjianArray5[3].toInt()*1)
                                 {
                                     LOG_EVT_INFO("开始打折操作5");
                                     shop_cart_Item.setCusSubtotal(priceTemp*(manjianArray5[1].toInt()-1) +
                                             priceTemp*(manjianArray5[2].toInt() - manjianArray5[1].toInt())*(manjianArray5[4].toInt())/10 +
                                             priceTemp*(num.toInt()*1 - manjianArray5[2].toInt()+1)*(manjianArray5[5].toInt())/10);
//                                     allPriceTmp = priceTmp*(manjianArray5[1]-1)
//                                             + priceTmp*(manjianArray5[2]-manjianArray5[1])*manjianArray5[4]/10
//                                             + priceTmp*(listModel.get(i).counts-manjianArray5[2]+1)*manjianArray5[5]/10;
//                                     listModel.set(i, {allPrice: (allPriceTmp*1).toFixed(2)});
//                                     listModel.set(i, {price: (allPriceTmp/listModel.get(i).counts*1).toFixed(2)});
                                 }
                                 else if (manjianArray5[1] != "" && manjianArray5[2] != "" && manjianArray5[3] != "" && num.toInt()*1 >= manjianArray5[3].toInt()*1)
                                 {
                                     LOG_EVT_INFO("开始打折操作6");
                                     shop_cart_Item.setCusSubtotal(priceTemp*(manjianArray5[1].toInt()-1) +
                                             priceTemp*(manjianArray5[2].toInt() - manjianArray5[1].toInt())*(manjianArray5[4].toInt())/10 +
                                             priceTemp*(manjianArray5[3].toInt() - manjianArray5[2].toInt())*(manjianArray5[5].toInt())/10 +
                                             priceTemp*(num.toInt()*1 - manjianArray5[3].toInt()+1)*(manjianArray5[6].toInt())/10);

//                                     allPriceTmp = priceTmp*(manjianArray5[1]-1)
//                                             + priceTmp*(manjianArray5[2]-manjianArray5[1])*manjianArray5[4]/10
//                                             + priceTmp*(manjianArray5[3]-manjianArray5[2])*manjianArray5[5]/10
//                                             + priceTmp*(listModel.get(i).counts-manjianArray5[3]+1)*manjianArray5[6]/10;
//                                     listModel.set(i, {allPrice: (allPriceTmp*1).toFixed(2)});
//                                     listModel.set(i, {.price =  (allPriceTmp/listModel.get(i).counts*1).toFixed(2)});
                                 }
                                 else{
                                     LOG_EVT_INFO("开始打折操作后发现全部不符合！");
                                 }
                             }
                             else{
                                 LOG_EVT_INFO("商品码不符合！");
                             }
//                         }
                     }
                 }
                 else{
                     LOG_EVT_INFO("该商品不存在第几件打几折！");
                 }
            }
//            if(0){
//            //加入商品打折处理，此处更新小计 第二件起一折  1、会员价格不支持折扣
//            ShopCartItem item;
//            shop_cart_Item.price_type_             = ShopCartPriceType::CUS_DISCOUNT;
//            double priceTemp = Utils::roundDecimal(shop_cart_Item.getPrice(), 2);
//            shop_cart_Item.price_type_             = ShopCartPriceType::CUS_SUBTOTAL;
//            shop_cart_Item.setCusSubtotal(Utils::roundDecimal((((num.toDouble() - 1) *0.1) + 1) * priceTemp));
//            }

//            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "购物车数量设置2:{}：{}",shop_cart_Item.goods_num_,shop_cart_Item.cus_subtotal_);
            emit postRowChanged(i);
            emit sigItemNumChanged(shop_cart_Item.goods_barcode_);
            is_exist = true;
            break;
        }
    }
    // sendRefreshSignal();
}

void ShopCartList::setItemDiscountByBarcode(QVariant goods_barcode, QVariant discount, QVariant date_time)
{
    cancelCusOrderPrice();
    bool is_exist = false;

    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart_Item = shop_cart_Items_[i];

        if (shop_cart_Item.goods_barcode_ == goods_barcode.toString() && shop_cart_Item.add_date_time_ == date_time.toString())
        {
            shop_cart_Item.setCusDiscountRatio(discount.toDouble());
            emit postRowChanged(i);
            is_exist = true;
            break;
        }
    }
}

void ShopCartList::setItemSubtotalByBarcode(QVariant goods_barcode, QVariant subtotal, QVariant date_time)
{
    bool is_exist = false;

    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart_Item = shop_cart_Items_[i];

        if (shop_cart_Item.goods_barcode_ == goods_barcode.toString() && shop_cart_Item.add_date_time_ == date_time.toString())
        {
            shop_cart_Item.setCusSubtotal(subtotal.toDouble());
            emit postRowChanged(i);
            is_exist = true;
            break;
        }
    }
}

void ShopCartList::setOrderDiscount(QVariant discount)
{
    emit preItemReset();
    shop_cart_list_data_.is_cus_order_price_ = false;
    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart_Item = shop_cart_Items_[i];
        shop_cart_Item.setCusDiscountRatio(discount.toDouble());
    }
    emit postItemReset();
}

void ShopCartList::removeItemById(QVariant goods_barcode, QVariant date_time)
{

    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart = shop_cart_Items_[i];
        if (shop_cart.goods_barcode_ == goods_barcode.toString() && shop_cart.add_date_time_ == date_time.toString())
        {
            auto item_barcode = shop_cart.goods_barcode_;
            if (--shop_cart.goods_num_ <= 0)
            {
                emit preItemRemoved(i);
                shop_cart_Items_.removeAt(i);
                emit postItemRemoved();
            }
            else
            {
                emit postRowChanged(i);
            }
            emit sigItemNumChanged(item_barcode);
            break;
        }
    }
}

QVariant ShopCartList::getItemNumByBarcode(QVariant goods_barcode)
{
    const auto &goodsMgr = DataManager::getInstance()->getGoodsMgr();
    GoodsInfo   goods_info;
    goodsMgr->getGoodsByBarcode(goods_barcode.toString(), goods_info);
    bool is_weight        = goods_info.isWeight();
    bool is_no_code_goods = goods_barcode == (int)EnumTool::UidEnum::ID_NO_CODE_GOODS || goods_barcode == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS;

    if (is_weight || is_no_code_goods)
    {
        int cur_weight_goods_num = 0;
        for (ShopCartItem item : shop_cart_Items_)
        {
            if (goods_barcode.toString() == item.goods_barcode_)
                ++cur_weight_goods_num;
        }
        return cur_weight_goods_num;
    }
    else
    {
        for (ShopCartItem item : shop_cart_Items_)
        {
            if (goods_barcode.toString() == item.goods_barcode_)
                return item.goods_num_;
        }
    }
    return 0;
}

void ShopCartList::clearAllGoods()
{
    emit preItemReset();
    LOG_EVT_INFO("开始执行购物车清空");
    shop_cart_Items_.clear();
    LOG_EVT_INFO("结束执行购物车清空");
    cancelCusOrderPrice();
    emit postItemReset();

    emit sigInitState();
}

QString ShopCartList::getAllGoodsInfo()
{
    QString result;

    Json shop_cart_info = *this;

    result = QString::fromStdString(shop_cart_info.dump());

    return result;
}

void ShopCartList::clearGoodsByBarcode(QVariant goods_barcode, QVariant date_time)
{
    cancelCusOrderPrice();
    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart = shop_cart_Items_[i];
        if (shop_cart.goods_barcode_ == goods_barcode.toString() && shop_cart.add_date_time_ == date_time.toString())
        {
            emit preItemRemoved(i);
            auto item_barcode = shop_cart_Items_.at(i).goods_barcode_;
            shop_cart_Items_.removeAt(i);
            emit postItemRemoved();
            emit sigItemNumChanged(item_barcode);
            return;
        }
    }
}
void ShopCartList::clearGiftGoodsByBarcode(QVariant goods_barcode)
{
    cancelCusOrderPrice();
    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        auto &shop_cart = shop_cart_Items_[i];
        if (shop_cart.goods_barcode_ == goods_barcode.toString())
        {
            emit preItemRemoved(i);
            auto item_barcode = shop_cart_Items_.at(i).goods_barcode_;
            shop_cart_Items_.removeAt(i);
            emit postItemRemoved();
            emit sigItemNumChanged(item_barcode);
            return;
        }
    }
}
QVariant ShopCartList::getTotalGoodsQuantity()
{
    double ret_goods_quantity = 0;

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();
    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        const auto &shop_cart = shop_cart_Items_[i];

        GoodsInfo cur_goods_info;
        goods_mgr->getGoodsByBarcode(shop_cart.goods_barcode_, cur_goods_info);
        if (cur_goods_info.isWeight())
        {
            ++ret_goods_quantity;
        }
        else
        {
            ret_goods_quantity += shop_cart.goods_num_;
        }
    }
    return QVariant::fromValue(Utils::floorDecimalStr(Utils::roundDecimal(ret_goods_quantity)));
}
QString getBarcode(QString giftbarid){
    QString giftbarcode ="";
    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();
    QVariant result = goods_mgr->getGoodsByGoodId4Qml(giftbarid);
    QString jsonString = result.toString();
    LOG_EVT_INFO("obtain jsonString:{}",jsonString.toStdString());
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
    if (doc.isObject()) {
        QJsonObject obj = doc.object();
        if (obj.contains("goods_barcode")) {
            giftbarcode = obj["goods_barcode"].toString();
        }
    }
    return giftbarcode;
}
void ShopCartList::handleDiscountsGift(){
    std::cout<<"start handleDiscountsGift"<<std::endl;
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "handleDiscountsGifthandleDiscountsGifthandleDiscountsGifthandleDiscountsGift");
        double ret_price = .0;
        if (shop_cart_list_data_.is_cus_order_price_)
        {
            ret_price = shop_cart_list_data_.cus_order_price_;
        }
        else
        {
            for (int i = 0; i < shop_cart_Items_.size(); ++i)
            {
                LOG_EVT_INFO("shop_cart.getSubtotal()：{}",ret_price);
                const auto &shop_cart = shop_cart_Items_[i];
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "shop_cart.getSubtotal()：{}", QString::number(shop_cart.getSubtotal()).toStdString());
                ret_price += shop_cart.getSubtotal();
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "ret_price44666:{}:{}", i,QString::number(ret_price).toStdString());
            }
        }
        //订单促销
        //LOG_EVT_INFO("订单满减满赠是否开启:{}",ConfigTool::getInstance()->getSetting(ConfigEnum::IS_ORDER_MANJIANMANZENG));
        if(ConfigTool::getInstance()->getSetting(ConfigEnum::IS_ORDER_MANJIANMANZENG) == "true"){
            LOG_EVT_INFO("==1开始判断购物车是否满足订单促销满减钱数== 购物车总价为：{}",ret_price);
            double orderJian = 0;
            int jianIndex = 0;
            int promotionIndex = 0;
            int giftGoodNum = 0;
            QString giftGoodId = 0;
            QString giftbarcode = 0;
            int giftnum1 = 0;
            QString giftbarcode1 = 0;
            int giftnum2 = 0;
            QString giftbarcode2 = 0;
            int giftnum3 = 0;
            QString giftbarcode3 = 0;
            QString startTime = "";
            QString endTime = "";
            auto oreder_promotion = ConfigTool::getInstance()->getSetting(ConfigEnum::ORDER_PROMOTION_INFO).toString();
            if(oreder_promotion.size() >5 ){
                auto orderPromotion = oreder_promotion.split("#");
                for (int m = 0; m < orderPromotion.size(); m++)
                {
                    auto discountData = orderPromotion[m].split("^");

                    for(int i = 0 ;i < discountData.size(); i++){
                        LOG_EVT_INFO("discountData[{}]:{}",i,discountData[i].toStdString());
                    }
                    giftbarcode1 = getBarcode(discountData[6]);
                    giftnum1 = discountData[9].toDouble() ;
                    giftbarcode2 = getBarcode(discountData[7]);
                    giftnum2 = discountData[10].toDouble() ;
                    giftbarcode3 = getBarcode(discountData[8]);
                    giftnum3 = discountData[11].toDouble() ;
                    //使用符合促销活动的满减最大值
                    for(int h=0;h<3;h++)
                    {

                        if(ret_price >= discountData[h].toDouble() ){
                            if(orderJian < discountData[h+3].toDouble()){
                               orderJian = discountData[h+3].toDouble();
                               jianIndex = h;
                               promotionIndex = m;
                               giftGoodId = discountData[h+6];
                               giftGoodNum = discountData[h+9].toInt();
                               startTime = discountData[12];
                               endTime = discountData[13];
                            }
                        }
                    }
                }
            }
            LOG_EVT_INFO("获取满减满赠商品活动结束！");
            if(lastTemp != ret_price){
                lastTemp = ret_price;
                if(orderJian > 0){                    

                    LOG_EVT_INFO("giftGoodId:{};giftGoodNum:{}",giftGoodId.toStdString(),giftGoodNum);
                    ret_price = ret_price - orderJian;
                    LOG_EVT_INFO("==============当前订单总价符合订单满减活动要求! promotionIndex:{};jianIndex:{};orderJian:{}",promotionIndex,jianIndex,orderJian);
                    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();
                    QVariant result = goods_mgr->getGoodsByGoodId4Qml(giftGoodId);
                    QString jsonString = result.toString();
                    LOG_EVT_INFO("===jsonString:{}",jsonString.toStdString());
                    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                    if (doc.isObject()) {
                        QJsonObject obj = doc.object();
                        if (obj.contains("goods_barcode")) {
                            giftbarcode = obj["goods_barcode"].toString();
                            LOG_EVT_INFO("===得到的barcode:{}",giftbarcode.toStdString());
                            LOG_EVT_INFO("===startTime:{};endTime:{}",startTime.toStdString(),endTime.toStdString());
                            QDateTime currentTime = QDateTime::currentDateTime();  // 当前时间
                            QDateTime start = QDateTime::fromString(startTime, "yyyy-MM-dd hh:mm:ss");
                            QDateTime end = QDateTime::fromString(endTime, "yyyy-MM-dd hh:mm:ss");
                            if (!start.isValid() || !end.isValid()) {
                                LOG_EVT_INFO("时间格式解析错误!");
                            }else{
                                if (currentTime >= start && currentTime <= end) {
                                    LOG_EVT_INFO("当前时间在促销区间内");
                                    QDateTime now = QDateTime::currentDateTime();
                                    std::string timeStr = now.toString("yyyy-MM-dd hh:mm:ss").toStdString();
                                    std::cout<<"start"<<timeStr<<std::endl;
                                    //Sleep(4000);
                                    QDateTime now1 = QDateTime::currentDateTime();
                                    std::string timeStr1 = now1.toString("yyyy-MM-dd hh:mm:ss").toStdString();
                                    std::cout<<"end"<<timeStr1<<std::endl;
                                    appendGiftDiscountItemByBarcode(giftbarcode, false, false,QString::number(giftGoodNum));
                                    //
                                    if(giftbarcode == giftbarcode2){
                                        clearGiftGoodsByBarcode(giftbarcode1);

                                    }else if(giftbarcode == giftbarcode3){
                                        clearGiftGoodsByBarcode(giftbarcode1);
                                        clearGiftGoodsByBarcode(giftbarcode2);
                                    }
                                    //

                                } else {
                                    LOG_EVT_INFO("当前时间不在促销区间内");
                                }
                            }
                        } else {
                             LOG_EVT_INFO("goods_barcode不存在");
                        }
                    } else {
                        LOG_EVT_INFO("JSON 格式错误或不是对象类型");
                    }
                }
            }
        }
}
double ShopCartList::getTotalGoodsPrice()
{
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "getTotalGoodsPricegetTotalGoodsPricegetTotalGoodsPricegetTotalGoodsPricegetTotalGoodsPrice");
    double ret_price = .0;
        if (shop_cart_list_data_.is_cus_order_price_)
        {
            ret_price = shop_cart_list_data_.cus_order_price_;
        }
        else
        {
            for (int i = 0; i < shop_cart_Items_.size(); ++i)
            {
                LOG_EVT_INFO("shop_cart.getSubtotal()：{}",ret_price);
                const auto &shop_cart = shop_cart_Items_[i];
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "shop_cart.getSubtotal()：{}", QString::number(shop_cart.getSubtotal()).toStdString());
                ret_price += shop_cart.getSubtotal();
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "ret_price44555:{}:{}", i,QString::number(ret_price).toStdString());
            }
        }
        //订单促销
        //LOG_EVT_INFO("订单满减满赠是否开启:{}",ConfigTool::getInstance()->getSetting(ConfigEnum::IS_ORDER_MANJIANMANZENG));
        if(ConfigTool::getInstance()->getSetting(ConfigEnum::IS_ORDER_MANJIANMANZENG) == "true"){
            LOG_EVT_INFO("==2开始判断购物车是否满足订单促销满减钱数== 购物车总价为：{}",ret_price);
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "开始判断购物车是否满足订单促销满减钱数");
            double orderJian = 0;
            int jianIndex = 0;
            int promotionIndex = 0;
            int giftGoodNum = 0;
            QString giftGoodId = 0;
            QString giftbarcode = 0;
            auto oreder_promotion = ConfigTool::getInstance()->getSetting(ConfigEnum::ORDER_PROMOTION_INFO).toString();
            if(oreder_promotion.size() >5 ){
                auto orderPromotion = oreder_promotion.split("#");
                for (int m = 0; m < orderPromotion.size(); m++)
                {
                    auto discountData = orderPromotion[m].split("^");

                    for(int i = 0 ;i < discountData.size(); i++){
                        //LOG_EVT_INFO("discountData[{}]:{}",i,discountData[i].toStdString());
                    }
                    //使用符合促销活动的满减最大值
                    for(int h=0;h<3;h++)
                    {

                        if(ret_price >= discountData[h].toDouble() ){
                            if(orderJian < discountData[h+3].toDouble()){
                               orderJian = discountData[h+3].toDouble();
                               jianIndex = h;
                               promotionIndex = m;
                               giftGoodId = discountData[h+6];
                               giftGoodNum = discountData[h+9].toInt();
                            }
                        }
                    }
                }
            }
            if(orderJian > 0){
                LOG_EVT_INFO("giftGoodId:{};giftGoodNum:{}",giftGoodId.toStdString(),giftGoodNum);
                ret_price = ret_price - orderJian;
                LOG_EVT_INFO("当前订单总价符合订单满减活动要求! promotionIndex:{};jianIndex:{};orderJian:{}",promotionIndex,jianIndex,orderJian);
            }else{
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "当前订单总价bubub符合订单满减活动要求");
            }
        }else{
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "==开始判断购物车是否满足订单促销满减钱数== 购物车总价为43132132");
            LOG_EVT_INFO("==开始判断购物车是否满足订单促销满减钱数== 购物车总价为43132132");
            LOG_EVT_INFO("==3开始判断购物车是否满足订单促销满减钱数== 购物车总价为：{}",ret_price);
        }
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "==开始判断购物车是否满足订单促销满减钱数== 购物车总价为4313253453453132:{}",ret_price);
        LOG_EVT_INFO("==开始判断购物车是否满足订单促销满减钱数== 购物车总价为4313253453453132");
    return QString::number(ret_price, 'f', 2).toDouble();
}

double ShopCartList::getFinalTotalGoodsPrice(int pay_method_enum)
{
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "==开始判断购物车是否满足订单促销满减钱数== 购物车总价为4313253453453132423423432");
    auto total_goods_price = getTotalGoodsPrice();
    if (pay_method_enum == (int)EnumTool::PayMethodEnum::PAY_METHOD__CASH)
    {
        auto wipe_zero_type = shop_cart_list_data_.wipe_zero_type_;

        if (wipe_zero_type == WipeZeroTypeEnum::WIPE_ZERO__OFF)
        {
            total_goods_price;
        }
        if(shop_cart_list_data_.cash_method == "0"){
            if (wipe_zero_type == WipeZeroTypeEnum::WIPE_ZERO__1_YUAN)
            {
                total_goods_price = floor(total_goods_price);
            }
            else if (wipe_zero_type == WipeZeroTypeEnum::WIPE_ZERO__5_JIAO)
            {
                total_goods_price = floor(total_goods_price * 2) / 2;
            }
            else if (wipe_zero_type == WipeZeroTypeEnum::WIPE_ZERO__1_JIAO)
            {
                total_goods_price = Utils::floorDecimal2(total_goods_price, 1);
            }
        }else if(shop_cart_list_data_.cash_method == "1"){
            total_goods_price;
        }
    }
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "getFinalTotalGoodsPrice===:{}",total_goods_price);
    return QString::number(total_goods_price, 'f', 2).toDouble();
}

double ShopCartList::getTotalGoodsPriceNoDiscount()
{
    double ret_goods_price = 0;

    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        const auto &shop_cart = shop_cart_Items_[i];
        ret_goods_price += shop_cart.getSubtotalNoDiscount();
    }
    return ret_goods_price;
}

void ShopCartList::setCusOrderPrice(QVariant cus_order_price)
{
    setOrderDiscount(1);
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "========================设置折扣价格============================：");
    shop_cart_list_data_.is_cus_order_price_ = true;
    shop_cart_list_data_.cus_order_price_    = cus_order_price.toDouble();

    emit refreshOrderTotal();
}

void ShopCartList::cancelCusOrderPrice()
{
    shop_cart_list_data_.is_cus_order_price_ = false;
}

OrderInfo ShopCartList::generateOrderInfoByShopCart(int payment, int sync_type)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();
    // auto sale_list_unique = OrderControl::generateOrderId();
    auto sale_list_unique = shop_cart_list_data_.order_unique_;

    auto goods_mgr      = DataManager::getInstance()->getGoodsMgr();
    auto shop_cart_list = this;

    // const auto cur_date_time_str = Utils::DateTime::getCurDateTimeStr();
    const auto cur_date_time_str = shop_cart_list_data_.order_date_ + " " + shop_cart_list_data_.order_time_;

    OrderInfo order_info;
    double    sale_list_total = shop_cart_list->getFinalTotalGoodsPrice(payment); // 订单总价格
    // double    sale_list_total = shop_cart_list->getTotalGoodsPriceNoDiscount(); // 订单总价格

    double        sale_list_pur         = 0.0; // 订单进货价
    double        sale_list_total_count = 0.0; // 商品总数量
    double        commission_sum        = 0.0; // 提成总和
    unsigned long goods_kind_count      = 0;   // 几种商品
    for (ShopCartItem &shop_cart_item : shop_cart_list->items())
    {
        GoodsInfo goods_info = shop_cart_item.getGoodsInfo();

        OrderDetailInfo order_detail_info;

        order_detail_info.sale_list_unique          = sale_list_unique.toULongLong(); // 销售单唯一标识
        order_detail_info.goods_barcode             = goods_info.goods_barcode;       // 商品条形码
        order_detail_info.goods_name                = goods_info.goods_name;          // 商品名称
        order_detail_info.goods_picturepath         = goods_info.goods_picturepath;   // 商品图片路径
        order_detail_info.sale_list_detail_count    = shop_cart_item.goods_num_;      // 商品数量
        order_detail_info.sale_list_detail_price    = shop_cart_item.getPrice();      // 商品购买的价格
        order_detail_info.sale_list_detail_subtotal = shop_cart_item.getSubtotal();   // 金额小计
        order_detail_info.goods_id                  = goods_info.goods_id;            // 商品id
        if (shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_GOODS) ||
            shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS))
        {
            order_detail_info.goods_purprice =
                shop_cart_item.getPrice() * (1.0 - ConfigTool::getInstance()->getSetting(ConfigEnum::NOCODE_GOODS_PROFIT_RATIO).toFloat()); // 商品进价
            order_detail_info.goods_old_price = shop_cart_item.getPrice();                                                                  // 商品原价
        }
        else
        {
            order_detail_info.goods_purprice  = goods_info.goods_in_price;   // 商品进价
            order_detail_info.goods_old_price = goods_info.goods_sale_price; // 商品原价
        }
        order_detail_info.commission_total = goods_info.getcommissionNum(); // 提成小计
        order_detail_info.goods_beans_count;                                // 供货商赠送百货豆
        order_detail_info.shop_beans_count = goods_info.getShopBeansNum();  // 商家赠送百货豆
        order_detail_info.sale_list_express_id;                             // 快递关联ID
        order_detail_info.saveTime = cur_date_time_str;                     // 时间

        sale_list_pur += goods_info.goods_in_price;
        sale_list_total_count += shop_cart_item.goods_num_;
        ++goods_kind_count;

        commission_sum += goods_info.getcommissionNum() * shop_cart_item.goods_num_;

        order_info.order_detail_infos.push_back(order_detail_info);
    }
    {
        order_info.sale_list_unique     = sale_list_unique.toULongLong();
        order_info.shop_unique          = shop_control->getShopUnique();
        order_info.sale_list_datetime   = cur_date_time_str;
        order_info.sale_list_total      = sale_list_total;
        order_info.sale_list_pur        = sale_list_pur;
        order_info.sale_list_totalCount = sale_list_total_count;
        order_info.cus_unique           = shop_cart_list->shop_cart_list_data_.member_unique;
        order_info.sale_type            = 0;
        order_info.sale_list_name;
        order_info.sale_list_phone;
        order_info.sale_list_address;
        order_info.sale_list_delfee;
        order_info.shop_subsidy_delfee;
        order_info.sale_list_discount;
        order_info.sale_list_state = 3;
        order_info.sale_list_handlestate;
        order_info.sale_list_payment = payment; // 支付方式：1-现金 2-支付宝 3-微信 4-银行卡  5-储值卡 6-美团外卖7-饿了么外卖 8-混合支付
        // 9-免密支付 10-积分兑换 11-百货豆 12-拉卡拉 13-易通付款码支付 14-合利宝刷卡
        order_info.sale_list_remarks;
        order_info.sale_list_flag;
        order_info.receipt_datetime;
        order_info.send_datetime;
        order_info.sale_list_number;
        order_info.sale_list_cashier   = shop_control->getPersonInfo().cashier_id;
        order_info.sale_list_same_type = sync_type; // 1-PC已同步 2-PC未同步 默认为2
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "111:{}，112:{}，113:{}", QString::number(shop_cart_list->shop_cart_list_data_.cash_received_, 'f', 2).toStdString(),
                   QString::number(sale_list_total).toStdString(),QString::number(sale_list_total).toStdString());
        if (payment == (int)PayEnum::PAY_METHOD__CASH)
        {
            if (shop_cart_list->shop_cart_list_data_.cash_received_ < sale_list_total)
            {
                order_info.sale_list_actually_received = QString::number(shop_cart_list->shop_cart_list_data_.cash_received_, 'f', 2).toDouble();
            }
            else
            {
                order_info.sale_list_actually_received = sale_list_total;
            }
        }
        else
        {
            order_info.sale_list_actually_received = sale_list_total;
        }
        order_info.machine_num;
        order_info.evaluate_point;
        order_info.commission_sum = commission_sum;
        order_info.pay_time       = cur_date_time_str;
        order_info.trade_no;
        order_info.cancle_reason;
        order_info.refund_money;
        order_info.refund_reason;
        order_info.refunt_operate_reason;
        order_info.cus_id;
        order_info.addr_latitude;
        order_info.addr_longitude;
        order_info.goods_kind_count;
        order_info.shipping_method;
        order_info.point_deduction;
        order_info.card_deduction;
        order_info.label_val;
        order_info.coupon_amount;
        order_info.beans_get;
        order_info.beans_use;
        order_info.shop_coupon_id;
        order_info.point_val;
        order_info.beans_money;
        order_info.points_get;
        order_info.delivery_type;
        order_info.cancel_time;
        order_info.goods_weight;
        order_info.formId;
        order_info.add_shop_balance_status;
        order_info.delivery_error;
        order_info.platform_shop_beans;
        order_info.head_image;
        order_info.cus_face_token;
        order_info.sup_give_beans;
        order_info.platform_cus_beans;
        order_info.return_price;
        order_info.verify_staff_id;
        order_info.shop_give_beans;

        order_info.local_points_ratio  = ConfigTool::getInstance()->pointsRatio();
        order_info.local_member_unique = shop_cart_list->shop_cart_list_data_.member_unique;
    }

    return order_info;
}

void ShopCartList::setIsCredit(bool is_credit)
{
    shop_cart_list_data_.is_credit_ = is_credit;
    emit refreshOrderTotal();
}

bool ShopCartList::getIsCredit()
{
    return shop_cart_list_data_.is_credit_;
}

void ShopCartList::setRechargeChangeAmount(QVariant amount)
{
    shop_cart_list_data_.recharge_change = amount.toDouble();
}

QVariant ShopCartList::getRechargeChangeAmount()
{
    return QVariant::fromValue(shop_cart_list_data_.recharge_change);
}

void ShopCartList::setReceivedCash(double cash_received)
{

    shop_cart_list_data_.cash_received_ = cash_received;

    emit refreshOrderTotal();
}

void ShopCartList::setOrderPaid()
{
    shop_cart_list_data_.is_paid = true;
    emit ControlManager::getInstance()->getShopCartList()->sigPayFinished();
}


QString ShopCartList::memberUnique()
{
    return shop_cart_list_data_.member_unique;
}

void ShopCartList::setMemberUnique(QString value_in)
{
    shop_cart_list_data_.member_unique = value_in;
    emit memberUniqueChanged();
}

QString ShopCartList::memberName()
{
    return shop_cart_list_data_.member_name;
}

void ShopCartList::setMemberName(QString value_in)
{
    shop_cart_list_data_.member_name = value_in;
    emit memberNameChanged();
}

QString ShopCartList::memberBlance()
{
    return shop_cart_list_data_.member_balance;
}

void ShopCartList::setMemberBlance(QString value_in)
{
    shop_cart_list_data_.member_balance = value_in;
    emit memberBlanceChanged();
}
QString ShopCartList::cashMethod()
{
    return shop_cart_list_data_.cash_method;
}
void ShopCartList::setCashMethod(QString value_in)
{
    shop_cart_list_data_.cash_method = value_in;
}
QString ShopCartList::memberPoint()
{
    return shop_cart_list_data_.member_point;
}

void ShopCartList::setMemberPoint(QString value_in)
{
    shop_cart_list_data_.member_point = value_in;
    emit memberPointChanged();
}

QString ShopCartList::highlightTimeStamp()
{
    return shop_cart_list_data_.last_add_item_timestamp;
}

void ShopCartList::setHighlightTimeStamp(QString timestamp)
{
    shop_cart_list_data_.last_add_item_timestamp = timestamp;
    emit highlightTimeStampChanged();
}

int ShopCartList::wipeZeroType()
{
    return static_cast<int>(shop_cart_list_data_.wipe_zero_type_);
}

void ShopCartList::setWipeZeroType(int wipe_zero_type)
{
    shop_cart_list_data_.wipe_zero_type_ = static_cast<WipeZeroTypeEnum>(wipe_zero_type);
    emit wipeZeroTypeChanged();
    emit refreshOrderTotal();
}

double ShopCartList::orderTotal()
{
    double ret_price = .0;

    if (shop_cart_list_data_.is_cus_order_price_)
    {
        ret_price = shop_cart_list_data_.cus_order_price_;
    }
    else
    {
        for (int i = 0; i < shop_cart_Items_.size(); ++i)
        {
            const auto &shop_cart = shop_cart_Items_[i];
            ret_price += shop_cart.getSubtotal();
        }
    }

    return QString::number(ret_price, 'f', 2).toDouble();
}

double ShopCartList::orderNoDiscountTotal()
{
    double ret_goods_price = 0;

    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        const auto &shop_cart = shop_cart_Items_[i];
        ret_goods_price += shop_cart.getSubtotalNoDiscount();
    }

    return QString::number(ret_goods_price, 'f', 2).toDouble();
}

double ShopCartList::orderQuantity()
{
    double ret_goods_quantity = 0;

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();
    for (int i = 0; i < shop_cart_Items_.size(); ++i)
    {
        const auto &shop_cart = shop_cart_Items_[i];

        GoodsInfo cur_goods_info;
        goods_mgr->getGoodsByBarcode(shop_cart.goods_barcode_, cur_goods_info);
        if (cur_goods_info.isWeight())
        {
            ++ret_goods_quantity;
        }
        else
        {
            ret_goods_quantity += shop_cart.goods_num_;
        }
    }

    return Utils::floorDecimalStr(Utils::roundDecimal(ret_goods_quantity)).toDouble();
}

ShopCartItem::ShopCartItem() : add_date_time_(Utils::DateTime::getTimestampMSecs())
{
}

void ShopCartItem::resetPriceType()
{
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "============*******resetPriceType************************");
    price_type_             = ShopCartPriceType::CUS_DISCOUNT;
    is_barcode_scale_goods_ = false;
}

bool ShopCartItem::getIsWeight()
{
    auto      goods_mgr = DataManager::getInstance()->getGoodsMgr();
    GoodsInfo goods_info;
    goods_mgr->getGoodsByBarcode(goods_barcode_, goods_info);

    if (goods_info.goodsChengType == 1)
        return true;

    return false;
}


void ShopCartItem::setCusDiscountRatio(double ratio)
{
    resetPriceType();
    ratio = Utils::roundDecimal(ratio, 3); //可能有 0.895

    cus_discount_ratio_ = ratio;
    price_type_         = ShopCartPriceType::CUS_DISCOUNT;
}
void ShopCartItem::setCusSubtotal(double subtotal)
{
    resetPriceType();
    subtotal      = Utils::roundDecimal(subtotal, precision_shop_cart);
    cus_subtotal_ = subtotal;
    price_type_   = ShopCartPriceType::CUS_SUBTOTAL;
}
void ShopCartItem::setCusSubAcctotal(double subtotal)
{
    resetPriceType();
    LOG_EVT_INFO("小计获取-进入原价获取3126666:{}",cus_subtotal_);
    LOG_EVT_INFO("小计获取-进入原价获取312======:{}",subtotal);
    subtotal      = Utils::roundDecimal(subtotal, precision_shop_cart);
    cus_subtotal_ += subtotal;
    LOG_EVT_INFO("小计获取-进入原价获取3126668886:{}",cus_subtotal_);
    price_type_   = ShopCartPriceType::CUS_SUBTOTAL;
    LOG_EVT_INFO("小计获取-进入原价获取312666888633:{}",price_type_ == ShopCartPriceType::CUS_SUBTOTAL);
}
void ShopCartItem::setCusPrice(double price)
{
    resetPriceType();

    if (price == 0)
    {
        setCusDiscountRatio(1);
        return;
    }

    price = Utils::roundDecimal(price, precision_shop_cart);

    cus_price_  = price;
     SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "=============cus_price_:{}",QString::number(cus_price_).toStdString());
     price_type_ = ShopCartPriceType::CUS_PRICE;
     SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "=============price_type_:{}",QString::number(price_type_ == ShopCartPriceType::CUS_PRICE).toStdString());
}

void ShopCartItem::setCusOriginPrice(double price)
{
    price                = Utils::roundDecimal(price, precision_shop_cart);
    cus_origin_price_    = price;
    is_cus_origin_price_ = true;
}

QString ShopCartItem::getGoodsName() const
{
    auto      goods_mgr = DataManager::getInstance()->getGoodsMgr();
    GoodsInfo goods_info;
    goods_mgr->getGoodsByBarcode(goods_barcode_, goods_info);
    if(is_gift_goods && (gift_barcode == goods_barcode_)){
        QString goodsNames = QString("(赠")+gift_num+")"+goods_info.goods_name;
        is_gift_goods = false;
        //gift_num = "";
        return  goodsNames;
    }
    if (is_cus_origin_price_)
    {
        goods_info.goods_sale_price = cus_origin_price_;
        goods_info.goods_cus_price  = QString::number(cus_origin_price_);
    }

    if (goods_info.goods_barcode == QString::number((int)UidEnum::ID_NO_CODE_GOODS) ||
        goods_info.goods_barcode == QString::number((int)UidEnum::ID_NO_CODE_WEIGHT_GOODS))
    {
        auto price_str = Utils::String::getClearNumStr(getPrice());
        QString names_la = QCoreApplication::translate("ShopCartControl", "元商品");
        return price_str + names_la;
    }
    else
    {
        return goods_info.goods_name;
    }
}

double ShopCartItem::getPrice() const
{
    auto shop_cart_list = ControlManager::getInstance()->getShopCartList();
    return getPrice(!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty());
}
double ShopCartItem::getPrice(bool is_member) const
{
    auto      goods_mgr = DataManager::getInstance()->getGoodsMgr();
    GoodsInfo goods_info;
    goods_mgr->getGoodsByBarcode(goods_barcode_, goods_info);
    if (is_cus_origin_price_)
    {
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "单价获取-进入单价自定义获取===============================");
        goods_info.goods_sale_price = cus_origin_price_;
        goods_info.goods_cus_price  = QString::number(cus_origin_price_);
    }

    double price = .0;

    if (is_cus_origin_price_){
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "单价获取-原始值");
        price = cus_origin_price_;
    }
    if (price_type_ == ShopCartPriceType::CUS_PRICE)
    {
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "单价获取-进入单价自定义获取2");
        price = cus_price_;
    }
    else if (price_type_ == ShopCartPriceType::CUS_SUBTOTAL  )
    {
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "单价获取-进入小计自定义获取2");
        if (goods_num_ == 0)
        {
            price = 0;
        }
        else
        {
            if (is_barcode_scale_goods_)
            {
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "is_barcode_scale_goods_:1");
                if (is_member)
                {
                    price = goods_info.goods_cus_price.toDouble();
                }
                else
                {
                    price = goods_info.goods_sale_price;
                }
            }
            else
            {
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "is_barcode_scale_goods_:0");
                price = cus_subtotal_ / getNum();
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "单价获取小计自定义获取-price:{}",price);
            }
        }
    }
    else if (price_type_ == ShopCartPriceType::CUS_DISCOUNT)
    {
        //LOG_EVT_INFO("单价获取-进入默认折扣自定义获取2");
        auto it = std::find(gift_vector.begin(), gift_vector.end(), goods_barcode_);
        if (it != gift_vector.end()) {
            auto next_it = std::next(it);
            if (next_it != gift_vector.end()) {
                LOG_EVT_INFO("符合满赠商品");
                LOG_EVT_INFO("单价获取-设置为满赠品:{}",(*next_it).toStdString());
                price = 0.00;
                }
            else{
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数量超出不符合满赠商品");
                if (is_member)
                    price = Utils::roundDecimal(Utils::roundDecimal(goods_info.goods_cus_price.toFloat(), 2) * cus_discount_ratio_, 2);
                else
                    price = Utils::roundDecimal(Utils::roundDecimal(goods_info.goods_sale_price, 2) * cus_discount_ratio_, 2);
                    //SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "单价获取-进入默认折扣自定义获取2：{}",Utils::roundDecimal(Utils::roundDecimal(goods_info.goods_sale_price, 2) * cus_discount_ratio_, 2));

            }
        } else {
           //SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "不符合满赠商品");
           if (is_member)
               price = Utils::roundDecimal(Utils::roundDecimal(goods_info.goods_cus_price.toFloat(), 2) * cus_discount_ratio_, 2);
           else
               price = Utils::roundDecimal(Utils::roundDecimal(goods_info.goods_sale_price, 2) * cus_discount_ratio_, 2);
               //SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "单价获取-进入默认折扣自定义获取2：{}",Utils::roundDecimal(Utils::roundDecimal(goods_info.goods_sale_price, 2) * cus_discount_ratio_, 2));

        }
    }
    else
    {
        throw "未知状态";
    }
    return price;
}

double ShopCartItem::getNum() const
{
    auto shop_cart_list = ControlManager::getInstance()->getShopCartList();
    return getNum(!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty());
}

double ShopCartItem::getNum(bool is_member) const
{
    auto goods_info = getGoodsInfo();

    if (price_type_ == ShopCartPriceType::CUS_SUBTOTAL)
    {
        // 自定义小计
        if (is_barcode_scale_goods_)
        {
            if (is_member)
            {
                return cus_subtotal_ / goods_info.goods_cus_price.toDouble();
            }
            else
            {
                return cus_subtotal_ / goods_info.goods_sale_price;
            }
        }
        else
        {            
            return goods_num_;
        }
    }
    else
    {
        return goods_num_;
    }
}

double ShopCartItem::getSubtotal() const
{
    auto      goods_mgr = DataManager::getInstance()->getGoodsMgr();
    GoodsInfo goods_info;
    goods_mgr->getGoodsByBarcode(goods_barcode_, goods_info);

    double subtotal = .0;

    if (is_cus_origin_price_)
    {
        subtotal = Utils::roundDecimal(cus_origin_price_ * goods_num_);
    }

    if (price_type_ == ShopCartPriceType::CUS_PRICE)
    { // 自定义单价
        subtotal = Utils::roundDecimal(cus_price_ * goods_num_);
    }
    else if (price_type_ == ShopCartPriceType::CUS_SUBTOTAL)
    { // 自定义小计
        subtotal = cus_subtotal_;
    }
    else if (price_type_ == ShopCartPriceType::CUS_DISCOUNT)
    {
        subtotal = Utils::roundDecimal(getPrice() * goods_num_);
    }
    else
    {
        throw "未知状态";
    }
    return subtotal;
}

double ShopCartItem::getSubtotal(bool is_member) const
{
    auto      goods_mgr = DataManager::getInstance()->getGoodsMgr();
    GoodsInfo goods_info;
    goods_mgr->getGoodsByBarcode(goods_barcode_, goods_info);

    double subtotal = .0;

    if (is_cus_origin_price_)
        subtotal = Utils::roundDecimal(cus_origin_price_ * goods_num_);

    if (price_type_ == ShopCartPriceType::CUS_PRICE)
    { // 自定义单价
        subtotal = Utils::roundDecimal(cus_price_ * goods_num_);
    }
    else if (price_type_ == ShopCartPriceType::CUS_SUBTOTAL)
    { // 自定义小计
        subtotal = cus_subtotal_;
    }
    else if (price_type_ == ShopCartPriceType::CUS_DISCOUNT)
    { // 自定义折扣
        subtotal = Utils::roundDecimal(getPrice(is_member) * goods_num_);
    }
    else
    {
        throw "未知状态";
    }

    return subtotal;
}

double ShopCartItem::getSubtotalNoDiscount() const
{
    auto      goods_mgr = DataManager::getInstance()->getGoodsMgr();
    GoodsInfo goods_info;
    goods_mgr->getGoodsByBarcode(goods_barcode_, goods_info);

    double subtotal_no_discount = .0;
    LOG_EVT_INFO( "=======cus_origin_price_:{}cus_price_:{}goods_info.goods_sale_price:{}",cus_origin_price_,cus_price_,goods_info.goods_sale_price);
    if (is_cus_origin_price_)
    { // 自定义原价
        subtotal_no_discount = Utils::roundDecimal(cus_origin_price_ * goods_num_);
    }
    else
    {
        if(cus_price_ != 0){
            subtotal_no_discount = Utils::roundDecimal(cus_price_ * goods_num_);
        }else{
        subtotal_no_discount = Utils::roundDecimal(goods_info.goods_sale_price * goods_num_);
        }

    }
 SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "subtotal_no_discount:{}",subtotal_no_discount);
    return subtotal_no_discount;
}

GoodsInfo ShopCartItem::getGoodsInfo() const
{
    const auto &goods_mgr = DataManager::getInstance()->getGoodsMgr();
    GoodsInfo   goods_info;
    goods_mgr->getGoodsByBarcode(goods_barcode_, goods_info);
    return goods_info;
}
