﻿#include "PayMethodControl.h"
#include <string>
#include "CameraControl.h"
#include "ControlModule/ControlManager.h"
#include "ControlModule/MqttControl.h"
#include "ControlModule/OrderControl.h"
#include "ControlModule/PrinterControl.h"
#include "ControlModule/ShopControl.h"
#include "ControlModule/TtsControl.h"
#include "ControlModule/ZintControl.h"
#include "DataModule/DataManager.h"
#include "DataModule/OrderData.h"
#include "LogModule/LogManager.h"
#include "NetModule/HttpCallback.h"
#include "NetModule/HttpClient.h"
#include "NetModule/HttpWorker.h"
#include "NetModule/NetGlobal.h"
#include "Utils/Utils.h"
#include "Utils/Utils4Qml.h"
#include "json-qt.hpp"
#include "qobjectdefs.h"
#include "qurl.h"
#include <QDateTime>
#include <QString>
#include <QFile>
#include <QTextStream>
#include <QCoreApplication>
#include <QDir>
#include <QMap>


using namespace std;
using namespace AeaQt;

std::mutex PayMethodControl::upload_order_mutex_v2_;
extern double saleList_actually_received_Temp;

QString getCurrentTimeFormatted() {
    QDateTime currentDateTime = QDateTime::currentDateTime();
    QString format = "yyyy-MM-dd hh:mm:ss";
    return currentDateTime.toString(format);
}
PayMethodControl::PayMethodControl(QObject *parent) : QObject{parent}
{
    timer_online_pay_.setInterval(1000);
    timer_combined_online_pay_.setInterval(1000);
    timer_mini_program_pay_.setInterval(1000);

    initPayMethodStatusList();
    reFreshPayMethodList();
}
QString numberToChinese(int number) {
    std::unordered_map<int, QString> numberToChineseMap = {
        {0, "零"},
        {1, "一"},
        {2, "二"},
        {3, "三"},
        {4, "四"},
        {5, "五"},
        {6, "六"},
        {7, "七"},
        {8, "八"},
        {9, "九"}
    };
    if (numberToChineseMap.find(number) != numberToChineseMap.end()) {
        return numberToChineseMap[number];
    } else {
         LOG_EVT_INFO("数字不存在映射范围内!");
        return "";
    }
}
QString numberToEnglish(double number) {
    if(ConfigTool::getInstance()->languageSelected().toInt() == 0){
        std::locale loc("C");
        std::ostringstream oss;
        oss.imbue(loc);
        QString lakh = "十万";
        if((number != 100000) ){
            int integerPart = static_cast<int>(floor(number));
            double decimalPart = std::round((number - integerPart) *100);
            if(decimalPart != 0.00){
                if(decimalPart < 10){
                    if(integerPart == 100000){
                        QString formattedNumber = lakh + ".0" + QString::number(decimalPart) ;
                        return formattedNumber;
                    }
                    else{
                        QString formattedNumber = QString::number(integerPart) + ".0" + QString::number(decimalPart) ;
                        return formattedNumber;
                    }
                }else{
                    if(integerPart == 100000){
                        int intNumber = static_cast<int>(std::floor(decimalPart));
                        int tens = intNumber / 10;
                        int units = intNumber % 10;
                        QString formattedNumber =  lakh + "点" + numberToChinese(tens) + numberToChinese(units) ;
                        return formattedNumber;
                    }
                    else
                    {
                        QString formattedNumber = QString::number(integerPart) + "." + QString::number(decimalPart) ;
                        return formattedNumber;
                    }
                }
            }
            else{
                QString formattedNumber = QString::number(number) ;
                return formattedNumber;
            }
        }
        oss << std::fixed << std::setprecision(2) << number;
        QString formattedNumber = QString::fromStdString(oss.str());
        if (formattedNumber == "100000.00") {
            return "十万";
        }
    }
    if (number == 0) return QObject::tr("zero");
    if (number < 0) return QObject::tr("negative ") + numberToEnglish(-number);

    QString words;
    int integerPart = static_cast<int>(number);
    double decimalPart = number - integerPart;
    if ((integerPart / 1000000) > 0) {
        words += numberToEnglish(integerPart / 1000000) + QObject::tr(" million ");
        integerPart %= 1000000;
    }
    if ((integerPart / 1000) > 0) {
        words += numberToEnglish(integerPart / 1000) + QObject::tr(" thousand ");
        integerPart %= 1000;
    }
    if ((integerPart / 100) > 0) {
        words += numberToEnglish(integerPart / 100) + QObject::tr(" hundred ");
        integerPart %= 100;
    }
    if (integerPart > 0) {
//        if (words != "") words += ("and ");
        if (integerPart < 20) {
            words += QString::number(integerPart) == "1" ? QObject::tr("one") :
                     QString::number(integerPart) == "2" ? QObject::tr("two") :
                     QString::number(integerPart) == "3" ? QObject::tr("three") :
                     QString::number(integerPart) == "4" ? QObject::tr("four") :
                     QString::number(integerPart) == "5" ? QObject::tr("five") :
                     QString::number(integerPart) == "6" ? QObject::tr("six") :
                     QString::number(integerPart) == "7" ? QObject::tr("seven") :
                     QString::number(integerPart) == "8" ? QObject::tr("eight") :
                     QString::number(integerPart) == "9" ? QObject::tr("nine") :
                     QString::number(integerPart) == "10" ? QObject::tr("ten") :
                     QString::number(integerPart) == "11" ? QObject::tr("eleven") :
                     QString::number(integerPart) == "12" ? QObject::tr("twelve") :
                     QString::number(integerPart) == "13" ? QObject::tr("thirteen") :
                     QString::number(integerPart) == "14" ? QObject::tr("fourteen") :
                     QString::number(integerPart) == "15" ? QObject::tr("fifteen") :
                     QString::number(integerPart) == "16" ? QObject::tr("sixteen") :
                     QString::number(integerPart) == "17" ? QObject::tr("seventeen") :
                     QString::number(integerPart) == "18" ? QObject::tr("eighteen") :
                     QString::number(integerPart) == "19" ? QObject::tr("nineteen") : "";
        } else {
            if ((integerPart / 10) > 0) {
                words += QString::number(integerPart / 10) == "2" ? QObject::tr("twenty") :
                         QString::number(integerPart / 10) == "3" ? QObject::tr("thirty") :
                         QString::number(integerPart / 10) == "4" ? QObject::tr("forty") :
                         QString::number(integerPart / 10) == "5" ? QObject::tr("fifty") :
                         QString::number(integerPart / 10) == "6" ? QObject::tr("sixty") :
                         QString::number(integerPart / 10) == "7" ? QObject::tr("seventy") :
                         QString::number(integerPart / 10) == "8" ? QObject::tr("eighty") :
                         QString::number(integerPart / 10) == "9" ? QObject::tr("ninety") : "";
                integerPart %= 10;
                if (integerPart > 0) words += "-" + numberToEnglish(integerPart);
            } else if (integerPart > 0) {
                words += numberToEnglish(integerPart);
            }
        }
    }
    if (decimalPart > 0) {
        int decimalInt = static_cast<int>(round(decimalPart * 100));
        words += QObject::tr(" point ");
        if ((decimalInt / 10) > 0) {
            words += numberToEnglish(decimalInt / 10) + " ";
        }
        words += numberToEnglish(decimalInt % 10);
    }

    return words;
}

void PayMethodControl::initCtrl()
{
    //   CameraControl *camera_ctrl = ControlManager::getInstance()->getCameraControl();

    //    connect(camera_ctrl, &CameraControl::sigFaceDetected, this,
    //            [&](int num)
    //            {

    //                if (face_pay_info_.is_need_pay && !face_pay_info_.is_paying)
    //                {
    //                    face_pay_info_.is_paying = true;

    //                    getMemberInfoByLastFaceImg();
    //                }
    //            });
}

void PayMethodControl::initPayMethodStatusList()
{
    pay_method_kv_map_.clear();
    pay_method_kv_map_.insert({PayEnum::PAY_METHOD__UNKNOW, tr("未知")});
    pay_method_kv_map_.insert({PayEnum::PAY_METHOD__CASH, tr("现金")});
    pay_method_kv_map_.insert({PayEnum::PAY_METHOD__ALIPAY_OFFLINE, tr("支付宝")});
    pay_method_kv_map_.insert({PayEnum::PAY_METHOD__WECHAT_OFFLINE, tr("微信")});
    pay_method_kv_map_.insert({PayEnum::PAY_METHOD__BANK_CARD, tr("银行卡")});
    pay_method_kv_map_.insert({PayEnum::PAY_METHOD__VIPCARD, tr("储值卡")});
    pay_method_kv_map_.insert({PayEnum::PAY_METHOD__JINQUAN, tr("金圈平台")});
    pay_method_kv_map_.insert({PayEnum::PAY_METHOD__COMBINED, tr("组合支付")});
    pay_method_kv_map_.insert({PayEnum::PAY_METHOD__FACE, tr("人脸支付")});
    pay_method_kv_map_.insert({PayEnum::PAY_METHOD__GOODS_MANAGER, tr("商品管理")});
}

const std::list<PayMethodStatus> &PayMethodControl::getPayMethodStatusList()
{
    return pay_method_status_list_;
}

QString PayMethodControl::getPayMethodStatusListJson()
{
    Json result_json;

    for (auto &status_item : pay_method_status_list_)
    {
        Json tmp_json         = status_item;
        tmp_json["name_role"] = getPayMethodStr(status_item.pay_enum).toStdString();
        result_json.push_back(tmp_json);
    }

    return QString::fromStdString(result_json.dump());
}

QString PayMethodControl::getEnabledPayMethodStatusListJson()
{
    Json result_json;

    for (auto &status_item : pay_method_status_list_)
    {
        if (!status_item.is_enabled)
            continue;

        Json tmp_json         = status_item;
        tmp_json["name_role"] = getPayMethodStr(status_item.pay_enum).toStdString();
        result_json.push_back(tmp_json);
    }

    return QString::fromStdString(result_json.dump());
}

void PayMethodControl::setPayMethodStatus(int pay_enum, bool is_enabled)
{
    setPayMethodStatus(static_cast<PayEnum>(pay_enum), is_enabled);
}

void PayMethodControl::setPayMethodStatus(PayEnum pay_enum, bool is_enabled)
{
    for (auto &status_item : pay_method_status_list_)
    {
        if (status_item.pay_enum == pay_enum)
        {
            status_item.is_enabled = is_enabled;
        }
    }
}

bool PayMethodControl::getPayMethodStatus(PayEnum pay_enum)
{
    for (auto &status_item : pay_method_status_list_)
    {
        if (status_item.pay_enum == pay_enum)
        {
            return status_item.is_enabled;
        }
    }
    return false;
}

bool PayMethodControl::getPayMethodStatus(int pay_enum)
{
    return getPayMethodStatus(static_cast<PayEnum>(pay_enum));
}

QString PayMethodControl::getPayMethodStr(PayEnum pay_enum)
{
    auto ret_iter = pay_method_kv_map_.find(pay_enum);

    if (ret_iter != pay_method_kv_map_.end())
        return ret_iter->second;

    return "未知";
}

bool PayMethodControl::savePayMethodStatusListByJson(QVariant json_data)
{
    Json json_doc = Json::parse(json_data.toString().toStdString());

    pay_method_status_list_.clear();

    if (json_doc.is_array())
    {
        for (auto cur_item : json_doc)
        {
            auto pay_method_status = cur_item.get<PayMethodStatus>();
            pay_method_status_list_.push_back(pay_method_status);
        }
    }

    FillPayMethodList();

    ConfigTool::getInstance()->setSetting(ConfigEnum::PAY_METHOD_CONFIG, QString::fromStdString(json_doc.dump()));

    return true;
}

void PayMethodControl::payByPaymentCode(QJSValue callback, QString payment_code)
{
    PayEnum pay_method = getPayMethodByPaymentCode(payment_code);

    LOG_EVT_INFO("payByPaymentCode : {}", (int)pay_method);

    switch (pay_method)
    {
    case PayEnum::PAY_METHOD__ALIPAY_ONLINE:
    case PayEnum::PAY_METHOD__WECHAT_ONLINE:
    case PayEnum::PAY_METHOD__YI_TONG:
    case PayEnum::PAY_METHOD__UNIONPAY:
        LOG_EVT_INFO("enter PAY_METHOD__UNIONPAY");
        onlinePay4Qml(callback, payment_code, pay_method);
        break;
    case PayEnum::PAY_METHOD__MINI_PROGRAM:
        payByMiniProgram_v2(callback, payment_code);
        break;
    default:
        break;
    }
}

void PayMethodControl::payByPayMethod(QJSValue callback, int pay_method)
{
    EnumTool::PayMethodEnum pay_method_e = (EnumTool::PayMethodEnum)pay_method;

    switch (pay_method_e)
    {
    case EnumTool::PayMethodEnum::PAY_METHOD__CASH:
        cashPay4Qml(callback);
        break;
    case EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE:
        alipay4Qml(callback);
        break;
    case EnumTool::PayMethodEnum::PAY_METHOD__WECHAT_OFFLINE:
        wechat4Qml(callback);
        break;
    case EnumTool::PayMethodEnum::PAY_METHOD__FACE:
        break;
    default:
        break;
    }
}
void PayMethodControl::payByMemberBalance_v2_ningyu(QJSValue callback, QVariant member_unique,QVariant sale_list_payment,QVariant storeAmount,QVariant cashAmount,QVariant jinQuan)
{
    ShopCartList *shop_cart_list                 = ControlManager::getInstance()->getShopCartList();
    auto          shop_control                   = ControlManager::getInstance()->getShopControl();
    shop_cart_list->shop_cart_list_data_.payment = (int)PayMethodEnum::PAY_METHOD__VIPCARD;

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    double saleList_total_price       = shop_cart_list->getTotalGoodsPrice(); // 订单总价格
    double saleList_actually_received = shop_cart_list->getTotalGoodsPrice();           // 实收总价格
    auto   sale_list_unique           = shop_cart_list->shop_cart_list_data_.order_unique_;
    Json   post_json;
//    post_json["payType"]         = 1;                                        // 店铺编号
    post_json["shopUnique"]      = shop_control->getShopUnique();            // 店铺编号
    post_json["saleListUnique"]  = sale_list_unique.toStdString();           // 订单号
    post_json["machine_num"]      = 1;                                        // 默认1
//    post_json["saleListPayment"] = 5;                                        // 收银方式：1 现金  2 支付宝  3 微信支付  4 银行卡  5 储值卡
    post_json["type"]            = 2;                                        // 默认1 设备时间 2 使用系统时间
    post_json["saleListCashier"] = shop_control->getPersonInfo().cashier_id; // 收银员ID
    // 付款状态-1货到付款未付款，2网上订单未付款，3已付款 ,4赊账 ，5申请退款 ，6同意退款 7拒绝退款 8自助收银未付款
    post_json["saleListState"] = 3;
    post_json["machineTime"] =
        (shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_).toStdString(); // 上传时间

    Json goodsList_json;
    for (auto &shop_cart_item : shop_cart_list->shop_cart_Items_)
    {
        GoodsInfo goods_info;
        goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);
        Json tmp_json;
        tmp_json["goodsId"] = std::to_string(goods_info.goods_id);
        tmp_json["goodsBarcode"] = goods_info.goods_barcode.toStdString();
        tmp_json["goodsName"]    = goods_info.goods_name.toStdString();

        if (shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_GOODS) ||
            shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS))
        {
            tmp_json["goodsPurprice"] = shop_cart_item.getPrice() * (1.0 - ConfigTool::getInstance()->getSetting(ConfigEnum::NOCODE_GOODS_PROFIT_RATIO).toFloat());
            tmp_json["goods_old_price"] = shop_cart_item.getPrice();
            tmp_json["saleListDetailPrice"] = shop_cart_item.getPrice();
        }
        else
        {
            tmp_json["goodsPurprice"]       = goods_info.goods_in_price;
            tmp_json["goods_old_price"] = goods_info.goods_sale_price;
            tmp_json["saleListDetailPrice"] = shop_cart_item.getPrice();
        }

        tmp_json["saleListDetailCount"] = shop_cart_item.goods_num_;
        tmp_json["subtotal"] = shop_cart_item.getSubtotal();

        goodsList_json.push_back(tmp_json);
    }
    post_json["goodsList"]     = goodsList_json;       // 订单详情
    post_json["saleListTotal"] = saleList_total_price; // 订单总金额
    Json payDetailList_json;

    if(storeAmount.toDouble() > 0){
        Json tmp_pay_detail_list;
        tmp_pay_detail_list["pay_method"] = 5;
        tmp_pay_detail_list["pay_money"] = storeAmount;
        payDetailList_json.push_back(tmp_pay_detail_list);
    }
    if(cashAmount.toDouble() > 0){
        Json tmp_pay_detail_list;
        tmp_pay_detail_list["pay_method"] = 1;
        tmp_pay_detail_list["pay_money"] = cashAmount;
        payDetailList_json.push_back(tmp_pay_detail_list);
    }
    if(jinQuan.toDouble() > 0){
        Json tmp_pay_detail_list;
        tmp_pay_detail_list["pay_method"] = 13;
        tmp_pay_detail_list["pay_money"] = jinQuan;
        payDetailList_json.push_back(tmp_pay_detail_list);
    }
    post_json["payDetailList"]     = payDetailList_json;       // 支付详情


    post_json["saleListActuallyReceived"] = saleList_actually_received; // 实收金额
//    post_json["storedCardMoney"] = saleList_actually_received;               // 会员卡消费金额
    post_json["cusUnique"]      = member_unique.toString().toStdString();   // 会员卡号
    post_json["saleListRemarks"]  = "";   // 订单备注
    post_json["memberCard"]      = storeAmount;   // 储值卡支付金额
    post_json["sale_list_payment"]      = sale_list_payment;   // 支付方式
    post_json["pointsRatio"]     = ConfigTool::getInstance()->pointsRatio(); // 积分比例

    auto cur_thread = new QThread(this);
    saleList_actually_received_Temp = saleList_total_price;
    LOG_EVT_INFO("===payByMemberBalance_v2_ningyu设置saleList_actually_received_Temp为:{}",saleList_actually_received_Temp);

    QString req_url = req_host_prefix + "harricane/payOnline/v2/cashierPay_ny.do";

    HttpWorker *cur_http_worker = new HttpWorker(req_url, QString::fromStdString(post_json.dump()).toUtf8());
    cur_http_worker->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http_worker, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);

                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_WARN(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "{}", json_doc.dump());

                    auto received_amount = QString::number(saleList_actually_received, 'f', 2);
                    Json json_tmp        = {{"received_amount", received_amount.toStdString()},
                                            {"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                            {"change_amount", QString::number(saleList_actually_received - saleList_total_price, 'f', 2).toStdString()},
                                            {"sn", __LINE__}};

                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "payByMemberBalance_v2_ning {}", json_tmp.dump());

                    int status = json_doc["status"];

                    if (status == 1)
                    {
                        if (json_doc.contains("cusData"))
                        {
                            shop_cart_list->shop_cart_list_data_.cash_received_ = shop_cart_list->getTotalGoodsPrice();
                            ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list, -1, false, json_doc["cusData"].dump());
                        }
                        else
                        {
                            ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);
                        }
                        payFinishWizard();
                        if (callback.isCallable())
                        {

                            if (ConfigTool::getInstance()->isPlayMemberTts())
                                ControlManager::getInstance()->getTtsControl()->say(tr("会员余额支付成功") + QString::number(saleList_actually_received, 'f', 2) +
                                                                                    tr("元"));
                            auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                       EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD, json_tmp.dump());
                            callback.call(arg_list);
                        }
                    }
                    else
                    {
                        // status ==0
                        if (callback.isCallable())
                        {
                            auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR, EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD,
                                                                       json_tmp.dump());
                            callback.call(arg_list);
                            if (ConfigTool::getInstance()->isPlayMemberTts())
                                ControlManager::getInstance()->getTtsControl()->say(tr("会员余额支付失败"));
                        }
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
}
void PayMethodControl::payByMemberBalance_v2(QJSValue callback, QVariant member_unique)
{
    ShopCartList *shop_cart_list                 = ControlManager::getInstance()->getShopCartList();
    auto          shop_control                   = ControlManager::getInstance()->getShopControl();
    shop_cart_list->shop_cart_list_data_.payment = (int)PayMethodEnum::PAY_METHOD__VIPCARD;

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    double saleList_total_price       = shop_cart_list->getTotalGoodsPrice(); // 订单总价格
    double saleList_actually_received = shop_cart_list->getTotalGoodsPrice();           // 实收总价格
    auto   sale_list_unique           = shop_cart_list->shop_cart_list_data_.order_unique_;
    Json   post_json;
    post_json["payType"]         = 1;                                        // 店铺编号
    post_json["shopUnique"]      = shop_control->getShopUnique();            // 店铺编号
    post_json["saleListUnique"]  = sale_list_unique.toStdString();           // 订单号
    post_json["machineNum"]      = 1;                                        // 默认1
    post_json["saleListPayment"] = 5;                                        // 收银方式：1 现金  2 支付宝  3 微信支付  4 银行卡  5 储值卡
    post_json["type"]            = 2;                                        // 默认1 设备时间 2 使用系统时间
    post_json["saleListCashier"] = shop_control->getPersonInfo().cashier_id; // 收银员ID
    // 付款状态-1货到付款未付款，2网上订单未付款，3已付款 ,4赊账 ，5申请退款 ，6同意退款 7拒绝退款 8自助收银未付款
    post_json["saleListState"] = 3;
    post_json["machineTime"] =
        (shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_).toStdString(); // 上传时间

    Json goodsList_json;
    for (auto &shop_cart_item : shop_cart_list->shop_cart_Items_)
    {
        GoodsInfo goods_info;
        goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);
        Json tmp_json;
        tmp_json["goodsId"] = std::to_string(goods_info.goods_id);
        tmp_json["barcode"] = goods_info.goods_barcode.toStdString();
        tmp_json["name"]    = goods_info.goods_name.toStdString();

        if (shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_GOODS) ||
            shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS))
        {
            tmp_json["price_in"] = shop_cart_item.getPrice() * (1.0 - ConfigTool::getInstance()->getSetting(ConfigEnum::NOCODE_GOODS_PROFIT_RATIO).toFloat());
            tmp_json["price_original"] = shop_cart_item.getPrice();
        }
        else
        {
            tmp_json["price_in"]       = goods_info.goods_in_price;
            tmp_json["price_original"] = shop_cart_item.getPrice();
        }

        tmp_json["sold_num"] = shop_cart_item.goods_num_;
        tmp_json["subtotal"] = shop_cart_item.getSubtotal();

        goodsList_json.push_back(tmp_json);
    }
    post_json["goodsList"]     = goodsList_json;       // 订单详情
    post_json["saleListTotal"] = saleList_total_price; // 订单总金额

    post_json["saleListActuallyReceived"] = saleList_actually_received; // 实收金额

    post_json["storedCard"]      = member_unique.toString().toStdString();   // 会员卡号
    post_json["storedCardMoney"] = saleList_actually_received;               // 会员卡消费金额
    post_json["memberCard"]      = member_unique.toString().toStdString();   // 会员卡号
    post_json["pointsRatio"]     = ConfigTool::getInstance()->pointsRatio(); // 积分比例

    auto cur_thread = new QThread(this);
    saleList_actually_received_Temp = saleList_total_price;
    LOG_EVT_INFO("===payByMemberBalance_v2设置saleList_actually_received_Temp为:{}",saleList_actually_received_Temp);

    QString req_url = req_host_prefix + "harricane/payOnline/v2/cashierPay.do";

    HttpWorker *cur_http_worker = new HttpWorker(req_url, QString::fromStdString(post_json.dump()).toUtf8());
    cur_http_worker->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http_worker, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);

                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_WARN(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "{}", json_doc.dump());

                    auto received_amount = QString::number(saleList_actually_received, 'f', 2);
                    Json json_tmp        = {{"received_amount", received_amount.toStdString()},
                                            {"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                            {"change_amount", QString::number(saleList_actually_received - saleList_total_price, 'f', 2).toStdString()},
                                            {"sn", __LINE__}};

                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "payByMemberBalance_v2 {}", json_tmp.dump());

                    int status = json_doc["status"];

                    if (status == 1)
                    {
                        if (json_doc.contains("cusData"))
                        {
                            shop_cart_list->shop_cart_list_data_.cash_received_ = shop_cart_list->getTotalGoodsPrice();
                            ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list, -1, false, json_doc["cusData"].dump());
                        }
                        else
                        {
                            ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);
                        }
                        payFinishWizard();
                        if (callback.isCallable())
                        {

                            if (ConfigTool::getInstance()->isPlayMemberTts())
                                ControlManager::getInstance()->getTtsControl()->say(tr("会员余额支付成功") + QString::number(saleList_actually_received, 'f', 2) +
                                                                                    tr("元"));
                            auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                       EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD, json_tmp.dump());
                            callback.call(arg_list);
                        }
                    }
                    else
                    {
                        // status ==0
                        if (callback.isCallable())
                        {
                            auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR, EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD,
                                                                       json_tmp.dump());
                            callback.call(arg_list);
                            if (ConfigTool::getInstance()->isPlayMemberTts())
                                ControlManager::getInstance()->getTtsControl()->say(tr("会员余额支付失败"));
                        }
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
}

void PayMethodControl::getMemberInfoByLastFaceImg(QJSValue callback, double saleListTotal)
{
    QImage last_img = ControlManager::getInstance()->getCameraControl()->getLastOriginalImg();
    getMemberInfoByFaceImg2(callback, last_img, saleListTotal);
}

void PayMethodControl::getMemberInfoByFaceImg2(QJSValue callback, QImage face_img, double saleListTotal)
{
    QString req_url           = req_host_prefix_face + "shopmanager/app/shop/searchCustomerMsg.do";
    face_pay_info_.detect_img = face_img;
    QVariantMap body_map;

    auto img_data   = HttpWorker::img2Base64(face_img);
    auto shopUnique = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());

    body_map.insert("shopUnique", shopUnique);
    body_map.insert("imageMsg", img_data);
    body_map.insert("imgFormat", "jpg");
    body_map.insert("onlineType", "1");      // 1线上会员
    body_map.insert("saleListTotal", saleListTotal); // 无效字段

    HttpClient *http_client = new HttpClient();
    auto      &&request     = http_client->post(req_url);
    request.bodyWithFormUrlencoded(body_map);

    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    Json json_doc = Json::parse(data);

                                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "人脸查询结果:{}", json_doc.dump());

                                    auto status = getDataFromJson(json_doc, "status").toInt();


                                    if (!(json_doc.contains("status") && json_doc.contains("data") && json_doc["data"].contains("cusName") &&
                                          json_doc["data"].contains("cusPhone") && json_doc["data"].contains("cus_balance")))
                                    {
                                        // 找不到人脸
                                        // setIsFacePaying(false);

                                        if (callback.isCallable())
                                        {
                                            QJSValueList arglist;
                                            arglist.push_back((int)FacePayStatus::FACE_PAY_STATUS__FACE_NOT_FOUND);
                                            callback.call(arglist);
                                        }
                                        return;
                                    }

                                    auto &json_doc_data = json_doc["data"];

                                    if (status == 1)
                                    {
                                        // 找到人脸
                                        face_pay_info_.cusName     = getDataFromJson(json_doc_data, "cusName").toString();
                                        face_pay_info_.cusPhone    = getDataFromJson(json_doc_data, "cusPhone").toString();
                                        face_pay_info_.cus_balance = getDataFromJson(json_doc_data, "cus_balance").toDouble();
                                        face_pay_info_.cusUnique   = getDataFromJson(json_doc_data, "cusUnique").toString();

                                        reqPayByOnlineMember(callback, face_pay_info_.cusUnique);
                                    }
                                    else
                                    {
                                        // 找不到人脸
                                        // setIsFacePaying(false);

                                        if (callback.isCallable())
                                        {
                                            QJSValueList arglist;
                                            arglist.push_back((int)FacePayStatus::FACE_PAY_STATUS__FACE_NOT_FOUND);
                                            callback.call(arglist);
                                        }
                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    // 网络错误
                                    // setIsFacePaying(false);

                                    if (callback.isCallable())
                                    {
                                        QJSValueList arglist;
                                        arglist.push_back((int)FacePayStatus::FACE_PAY_STATUS__REQ_ERROR);
                                        arglist.push_back(QString::fromStdString(data));
                                        callback.call(arglist);
                                    }
                                    break;
                                }
                            }
                        });
}

void PayMethodControl::reqPayByOnlineMember(QJSValue callback, QString online_member_unique)
{
    auto shop_cart_ctrl = ControlManager::getInstance()->getShopCartList();
    auto shop_ctrl      = ControlManager::getInstance()->getShopControl();
    auto goods_mgr      = DataManager::getInstance()->getGoodsMgr();

    shop_cart_ctrl->shop_cart_list_data_.payment = 102;

    auto url = req_host_prefix_mini_program + "/goBuy/cart/collectMoneyPay.do";

    QVariantMap post_map;
    post_map.insert("sale_list_unique", shop_cart_ctrl->getOrderUnique());
    post_map.insert("cus_unique", online_member_unique.toULongLong());
    post_map.insert("shop_unique", shop_ctrl->getShopUnique());

    post_map.insert("sale_list_total", shop_cart_ctrl->getTotalGoodsPrice());
    post_map.insert("sale_list_actually_received", shop_cart_ctrl->getFinalTotalGoodsPrice());
    post_map.insert("shop_coupon_id", 0);
    post_map.insert("point_val", 0);
    post_map.insert("point_deduction", 0);
    post_map.insert("beans_use", 0);
    post_map.insert("beans_money", 0);
    post_map.insert("card_deduction", 0);
    post_map.insert("saleListRemarks", 8);
    post_map.insert("sale_type", 7);
    post_map.insert("sale_list_paysment", 8);
    saleList_actually_received_Temp = shop_cart_ctrl->getFinalTotalGoodsPrice();
    LOG_EVT_INFO("===reqPayByOnlineMember设置saleList_actually_received_Temp为:{}",saleList_actually_received_Temp);
    Json saleListPayDetail;
    {
        Json pay_method_info;
        pay_method_info["pay_method"] = 5;
        pay_method_info["pay_money"]  = shop_cart_ctrl->getFinalTotalGoodsPrice();
        saleListPayDetail.push_back(pay_method_info);
    }

    post_map.insert("saleListPayDetail", QString::fromStdString(saleListPayDetail.dump()).replace("\"", "\\\"")); // 支付信息

    Json goods_list;
    for (auto &shop_cart_item : shop_cart_ctrl->shop_cart_Items_)
    {
        Json goods_item;

        GoodsInfo goods_info;

        if (!goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info))
        {
            LOG_EVT_ERROR("获取商品信息失败");
            continue;
        }

        goods_item["goods_barcode"]          = goods_info.goods_barcode.toStdString();
        goods_item["goods_name"]             = goods_info.goods_name.toStdString();
        goods_item["sale_list_detail_count"] = shop_cart_item.goods_num_;
        goods_item["sale_list_detail_price"] = shop_cart_item.getSubtotal();
        goods_item["goods_id"]               = to_string(goods_info.goods_id);
        goods_item["goods_purprice"]         = goods_info.goods_in_price;
        goods_item["goods_old_price"]        = goods_info.goods_sale_price;

        goods_list.push_back(goods_item);
    }

    auto goods_list_str = QString::fromStdString(goods_list.dump());
    goods_list_str.replace("\"", "\\\"");


    post_map.insert("goods_list", goods_list_str);
    post_map.insert("sale_list_cashier", QString::number(shop_ctrl->getUserId()));

    HttpClient *http_client = new HttpClient();

    auto &&request = http_client->post(url);
    request.bodyWithFormUrlencoded(post_map);

    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    Json json_doc = Json::parse(data);

                                    auto status = getDataFromJson(json_doc, "status").toInt();

                                    if (status == 1)
                                    {
                                        // 支付成功
                                        ControlManager::getInstance()->getPrinterControl()->printOrderThread(ControlManager::getInstance()->getShopCartList());
                                        payFinishWizard();
                                        if (callback.isCallable())
                                        {
                                            QJSValueList arglist;
                                            arglist.push_back((int)FacePayStatus::FACE_PAY_STATUS__SUCC);
                                            arglist.push_back(QString::fromStdString(data));
                                            callback.call(arglist);
                                            if (ConfigTool::getInstance()->isPlayCashTts())
                                            {
                                                ControlManager::getInstance()->getTtsControl()->say(tr("刷脸支付成功") + numberToEnglish(QString::number(saleList_actually_received_Temp).toDouble()) + tr("元"));
                                            }
                                        }
                                    }
                                    else
                                    {
                                        // 余额不足
                                        if (callback.isCallable())
                                        {
                                            QJSValueList arglist;
                                            arglist.push_back((int)FacePayStatus::FACE_PAY_STATUS__BALANCE_INSUFFICIENT);
                                            arglist.push_back(QString::fromStdString(data));
                                            callback.call(arglist);
                                            if (ConfigTool::getInstance()->isPlayCashTts())
                                            {
                                                ControlManager::getInstance()->getTtsControl()->say(tr("刷脸支付余额不足") );
                                            }
                                        }
                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    // 网络错误
                                    if (callback.isCallable())
                                    {
                                        QJSValueList arglist;
                                        arglist.push_back((int)FacePayStatus::FACE_PAY_STATUS__REQ_ERROR);
                                        arglist.push_back(QString::fromStdString(data));
                                        callback.call(arglist);
                                    }
                                    break;
                                }
                            }
                            // setIsFacePaying(false);
                            http_client->deleteLater();
                        });
}

bool PayMethodControl::getIsNeedFacePay()
{
    return face_pay_info_.is_need_pay;
}

void PayMethodControl::setIsNeedFacePay(bool is_need)
{
    if (face_pay_info_.is_need_pay == is_need)
        return;

    face_pay_info_.is_need_pay = is_need;
    emit sigIsNeedFacePayCHG();
}

bool PayMethodControl::getIsFacePaying()
{
    return face_pay_info_.is_paying;
}

void PayMethodControl::setIsFacePaying(bool is_paying)
{
    if (face_pay_info_.is_paying == is_paying)
        return;

    face_pay_info_.is_paying = is_paying;
    emit sigIsFacePayingCHG();
}

void PayMethodControl::cashPay4Qml(QJSValue callback)
{
    ShopCartList *shop_cart_list                 = ControlManager::getInstance()->getShopCartList();
    shop_cart_list->shop_cart_list_data_.payment = static_cast<int>(PayMethodEnum::PAY_METHOD__CASH);

    //有会员要直接上传
    if (!shop_cart_list->memberUnique().isEmpty())
    {
        LOG_EVT_INFO("会员现金支付");
        auto *http_client = new HttpClient();

        uploadOrder_v2(shop_cart_list, http_client,
                       [=](HttpHandleType http_handle_type, std::string data) mutable
                       {
                           switch (http_handle_type)
                           {
                           case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                               {
                                   auto json_doc = Json::parse(data, nullptr, false);
                                   if (json_doc.is_discarded())
                                   {
                                       LOG_NET_ERROR("数据解析错误");
                                   }
                                   else
                                   {
                                       if (json_doc["status"] == 1)
                                       {
                                           double goods_price     = shop_cart_list->getFinalTotalGoodsPrice(static_cast<int>(PayMethodEnum::PAY_METHOD__CASH));
                                           double cash_received   = shop_cart_list->shop_cart_list_data_.cash_received_;
                                           double recharge_change = shop_cart_list->shop_cart_list_data_.recharge_change;

                                           if (json_doc.contains("cusData"))
                                           {
                                               ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list, -1, false,
                                                                                                                    json_doc["cusData"].dump());
                                               payFinishWizard();
                                               if (callback.isCallable())
                                               {
                                                   QJSValueList arglist;
                                                   arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS)); // 支付状态
                                                   arglist.push_back(static_cast<int>(EnumTool::PayMethodEnum::PAY_METHOD__CASH));    // 支付方式

                                                   const Json json_tmp = {
                                                       {"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                       {"received_amount", QString::number(saleList_actually_received_Temp, 'f', 2).toDouble()},
                                                       {"change_amount", QString::number(cash_received - goods_price - recharge_change, 'f', 2).toStdString()},
                                                       {"sn", __LINE__}};

                                                   LOG_EVT_INFO("[cashPay4Qml]{}", json_tmp.dump());

                                                   arglist.push_back(QString::fromStdString(json_tmp.dump())); // 支付详情

                                                   callback.call(arglist);
                                               }
                                               if (ConfigTool::getInstance()->isPlayCashTts())
                                               {
                                                   ControlManager::getInstance()->getTtsControl()->say(tr("现金支付成功") + numberToEnglish(QString::number(saleList_actually_received_Temp).toDouble()) + tr("元"));
                                               }
                                               SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "========================现金支付接口完成============================：");
                                           }
                                           else
                                           {
                                               Utils4Qml::getInstance()->sendToast("上传会员订单失败", static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__ERROR));
                                           }
                                       }
                                   }
                                   break;
                               }
                           case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                           case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                               {
                                   Utils4Qml::getInstance()->sendToast("上传会员订单失败", static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__ERROR));
                                   break;
                               }
                           }
                           http_client->deleteLater();
                       });
    }
    else
    {
        LOG_EVT_INFO("非会员现金支付");
        OrderInfo order_info = shop_cart_list->generateOrderInfoByShopCart(static_cast<int>(PayMethodEnum::PAY_METHOD__CASH));

        ControlManager::getInstance()->getOrderControl()->addOrder(order_info);
        ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);
        uploadNotUploadOrder_v2_CallBack(
            [=](int payment_status) mutable
            {
              if(payment_status == 1){
                  double goods_price   = shop_cart_list->getFinalTotalGoodsPrice();
                  double cash_received = shop_cart_list->getFinalTotalGoodsPrice();
                  payFinishWizard();
                  if (callback.isCallable())
                  {
                      QJSValueList arglist;
                      arglist.push_back((int)EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS);        // 支付状态
                      arglist.push_back((int)EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE); // 支付方式

                      Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                       {"received_amount", saleList_actually_received_Temp},
                                       {"change_amount", QString::number(cash_received - goods_price, 'f', 2).toStdString()},
                                       {"sn", __LINE__}};

                      arglist.push_back(QString::fromStdString(json_tmp.dump())); // 支付详情
                      auto ret = callback.call(arglist);
                      if (ConfigTool::getInstance()->isPlayOfflineTTs())
                      {
                          ControlManager::getInstance()->getTtsControl()->say(tr("现金支付成功") + numberToEnglish(saleList_actually_received_Temp) + tr("元"));
                      }

                  }
              }
              else if(payment_status == 2 ||payment_status == 3){
                  if (callback.isCallable())
                  {
                      QJSValueList arglist;
                      arglist.push_back((int)EnumTool::PayStatusEnum::PAY_STATUS__ERROR);        // 支付状态
                      arglist.push_back((int)EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE); // 支付方式
                      Json json_tmp = {{"receivable_amount", 0.00},
                                       {"received_amount", 0.00},
                                       {"change_amount", 0.00},
                                       {"sn", __LINE__}};

                      arglist.push_back(QString::fromStdString(json_tmp.dump())); // 支付详情
                      auto ret = callback.call(arglist);
                  }
                  if (ConfigTool::getInstance()->isPlayOfflineTTs() )
                  {
                      if(payment_status == 2){
                          ControlManager::getInstance()->getTtsControl()->say("单个商品售价超过上限");
                      }else{
                          ControlManager::getInstance()->getTtsControl()->say("支付失败");
                      }
                  }
              }
        });
    }
}

void PayMethodControl::alipay4Qml(QJSValue callback)
{
    ShopCartList *shop_cart_list                 = ControlManager::getInstance()->getShopCartList();
    auto shop_control   = ControlManager::getInstance()->getShopControl();
    shop_cart_list->shop_cart_list_data_.payment = static_cast<int>(PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE);
    //有会员要直接上传
    if (!shop_cart_list->memberUnique().isEmpty())
    {
//        if(shop_control->getShopType() =="6" ||shop_control->getShopType() =="7"){
//            std::cout<<" huiyuan ningyu no quanxian "<<std::endl;
//        }else{

        auto *http_client = new HttpClient();

        uploadOrder_v2(
            shop_cart_list, http_client,
            [=](HttpHandleType http_handle_type, std::string data) mutable
            {
                switch (http_handle_type)
                {
                case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                    {
                        auto json_doc = Json::parse(data, nullptr, false);
                        if (json_doc.is_discarded())
                        {
                            LOG_NET_ERROR("数据解析错误");
                        }
                        else
                        {
                            if (json_doc["status"] == 1)
                            {
                                double cash_received = shop_cart_list->getFinalTotalGoodsPrice();
                                double goods_price     = shop_cart_list->getFinalTotalGoodsPrice(static_cast<int>(PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE));

                                if (json_doc.contains("cusData"))
                                {
                                    shop_cart_list->setReceivedCash(cash_received);
                                    ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list, -1, false, json_doc["cusData"].dump());
                                    if (callback.isCallable())
                                    {
                                        QJSValueList arglist;
                                        arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS)); // 支付状态
                                        arglist.push_back(static_cast<int>(EnumTool::PayMethodEnum::PAY_METHOD__CASH));    // 支付方式
                                        LOG_EVT_INFO("[实收金额]{}", shop_cart_list->shop_cart_list_data_.cash_received_);
                                        const Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                               {"received_amount", saleList_actually_received_Temp},
                                                               {"change_amount", QString::number(cash_received - goods_price, 'f', 2).toStdString()},
                                                               {"sn", __LINE__}};

                                        LOG_EVT_INFO("[cashPay4Qml]{}", json_tmp.dump());

                                        arglist.push_back(QString::fromStdString(json_tmp.dump())); // 支付详情

                                        callback.call(arglist);
                                    }

                                    if (ConfigTool::getInstance()->isPlayCashTts())
                                    {
                                        ControlManager::getInstance()->getTtsControl()->say(tr("线下支付宝支付成功") + numberToEnglish(QString::number(saleList_actually_received_Temp).toDouble()) + tr("元"));
                                    }

                                    payFinishWizard();
                                }
                                else
                                {
                                    Utils4Qml::getInstance()->sendToast("上传会员订单失败", static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__ERROR));
                                }
                            }
                            else
                            {
                                Utils4Qml::getInstance()->sendToast("上传会员订单失败", static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__ERROR));
                            }
                        }
                        break;
                    }
                case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                    {
                        Utils4Qml::getInstance()->sendToast("上传会员订单失败", static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__ERROR));
                        break;
                    }
                }
                http_client->deleteLater();
            });
    }
    else
    {
        OrderInfo order_info = shop_cart_list->generateOrderInfoByShopCart(static_cast<int>(PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE));

        ControlManager::getInstance()->getOrderControl()->addOrder(order_info);
        ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);

        //uploadNotUploadOrder_v2();
        uploadNotUploadOrder_v2_CallBack(
            [=](int payment_status) mutable
            {
              if(payment_status == 1){
                  double goods_price   = shop_cart_list->getFinalTotalGoodsPrice();
                  double cash_received = shop_cart_list->getFinalTotalGoodsPrice();
                  payFinishWizard();
                  if (callback.isCallable())
                  {
                      QJSValueList arglist;
                      arglist.push_back((int)EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS);        // 支付状态
                      arglist.push_back((int)EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE); // 支付方式

                      Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                       {"received_amount", saleList_actually_received_Temp},
                                       {"change_amount", QString::number(cash_received - goods_price, 'f', 2).toStdString()},
                                       {"sn", __LINE__}};

                      arglist.push_back(QString::fromStdString(json_tmp.dump())); // 支付详情
                      auto ret = callback.call(arglist);
                      if (ConfigTool::getInstance()->isPlayOfflineTTs())
                      {
                          ControlManager::getInstance()->getTtsControl()->say(tr("线下支付宝支付成功") + numberToEnglish(saleList_actually_received_Temp) + tr("元"));
                      }
                  }
              }
              else if(payment_status == 2 ||payment_status == 3){
                  if (callback.isCallable())
                  {
                      QJSValueList arglist;
                      arglist.push_back((int)EnumTool::PayStatusEnum::PAY_STATUS__ERROR);        // 支付状态
                      arglist.push_back((int)EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE); // 支付方式
                      Json json_tmp = {{"receivable_amount", 0.00},
                                       {"received_amount", 0.00},
                                       {"change_amount", 0.00},
                                       {"sn", __LINE__}};

                      arglist.push_back(QString::fromStdString(json_tmp.dump())); // 支付详情
                      auto ret = callback.call(arglist);
                  }
                  if (ConfigTool::getInstance()->isPlayOfflineTTs() )
                  {
                      if(payment_status == 2){
                          ControlManager::getInstance()->getTtsControl()->say("单个商品售价超过上限");
                      }else{
                          ControlManager::getInstance()->getTtsControl()->say("支付失败");
                      }
                  }
              }
        });
    }
}

void PayMethodControl::wechat4Qml(QJSValue callback)
{
    ShopCartList *shop_cart_list                 = ControlManager::getInstance()->getShopCartList();
    shop_cart_list->shop_cart_list_data_.payment = (int)PayMethodEnum::PAY_METHOD__WECHAT_OFFLINE;

    //有会员要直接上传
    if (!shop_cart_list->memberUnique().isEmpty())
    {
        auto *http_client = new HttpClient();

        uploadOrder_v2(
            shop_cart_list, http_client,
            [=](HttpHandleType http_handle_type, std::string data) mutable
            {
                switch (http_handle_type)
                {
                case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                    {
                        auto json_doc = Json::parse(data, nullptr, false);
                        if (json_doc.is_discarded())
                        {
                            LOG_NET_ERROR("数据解析错误");
                        }
                        else
                        {
                            if (json_doc["status"] == 1)
                            {
                                double cash_received = shop_cart_list->getFinalTotalGoodsPrice(static_cast<int>(PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE));
                                double goods_price   = shop_cart_list->getFinalTotalGoodsPrice(static_cast<int>(PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE));

                                if (json_doc.contains("cusData"))
                                {
                                    shop_cart_list->setReceivedCash(cash_received);
                                    ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list, -1, false, json_doc["cusData"].dump());

                                    if (callback.isCallable())
                                    {
                                        QJSValueList arglist;
                                        arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS)); // 支付状态
                                        arglist.push_back(static_cast<int>(EnumTool::PayMethodEnum::PAY_METHOD__CASH));    // 支付方式

                                        const Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                               {"received_amount", cash_received},
                                                               {"change_amount", QString::number(cash_received - goods_price, 'f', 2).toStdString()},
                                                               {"sn", __LINE__}};

                                        LOG_EVT_INFO("[cashPay4Qml]{}", json_tmp.dump());

                                        arglist.push_back(QString::fromStdString(json_tmp.dump())); // 支付详情

                                        callback.call(arglist);
                                    }

                                    if (ConfigTool::getInstance()->isPlayCashTts())
                                    {
                                        ControlManager::getInstance()->getTtsControl()->say(tr("线下微信支付成功") + numberToEnglish(QString::number(saleList_actually_received_Temp).toDouble()) + tr("元"));
                                    }

                                    payFinishWizard();
                                }
                                else
                                {
                                    ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);

                                    if (callback.isCallable())
                                    {
                                        QJSValueList arglist;
                                        arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS)); // 支付状态
                                        arglist.push_back(static_cast<int>(EnumTool::PayMethodEnum::PAY_METHOD__CASH));    // 支付方式

                                        const Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                               {"received_amount", saleList_actually_received_Temp},
                                                               {"change_amount", QString::number(cash_received - goods_price, 'f', 2).toStdString()},
                                                               {"sn", __LINE__}};

                                        LOG_EVT_INFO("[cashPay4Qml]{}", json_tmp.dump());

                                        arglist.push_back(QString::fromStdString(json_tmp.dump())); // 支付详情

                                        callback.call(arglist);
                                    }

                                    if (ConfigTool::getInstance()->isPlayCashTts())
                                    {
                                        ControlManager::getInstance()->getTtsControl()->say(tr("线下微信支付成功") + numberToEnglish(QString::number(saleList_actually_received_Temp).toDouble()) + tr("元"));
                                    }

                                    payFinishWizard();
                                }
                            }
                            else
                            {
                                Utils4Qml::getInstance()->sendToast("上传会员订单失败", static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__ERROR));
                            }
                        }
                        break;
                    }
                case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                    {
                        Utils4Qml::getInstance()->sendToast("上传会员订单失败", static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__ERROR));
                        break;
                    }
                }
                http_client->deleteLater();
            });
    }
    else
    {
        OrderInfo order_info = shop_cart_list->generateOrderInfoByShopCart((int)PayMethodEnum::PAY_METHOD__WECHAT_OFFLINE);

        ControlManager::getInstance()->getOrderControl()->addOrder(order_info);
        ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);

        uploadNotUploadOrder_v2_CallBack(
            [=](int payment_status) mutable
            {
              if(payment_status == 1){
                  double goods_price   = shop_cart_list->getFinalTotalGoodsPrice();
                  double cash_received = shop_cart_list->getFinalTotalGoodsPrice();
                  payFinishWizard();
                  if (callback.isCallable())
                  {
                      QJSValueList arglist;
                      arglist.push_back((int)EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS);        // 支付状态
                      arglist.push_back((int)EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE); // 支付方式

                      Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                       {"received_amount", saleList_actually_received_Temp},
                                       {"change_amount", QString::number(cash_received - goods_price, 'f', 2).toStdString()},
                                       {"sn", __LINE__}};

                      arglist.push_back(QString::fromStdString(json_tmp.dump())); // 支付详情
                      auto ret = callback.call(arglist);
                      if (ConfigTool::getInstance()->isPlayOfflineTTs())
                      {
                          LOG_EVT_INFO("WEIXIN:{}",QString::number(cash_received).toStdString());
                          ControlManager::getInstance()->getTtsControl()->say(tr("线下微信支付成功") + numberToEnglish(saleList_actually_received_Temp) + tr("元"));
                      }
                  }
              }
              else if(payment_status == 2 ||payment_status == 3){
                  if (callback.isCallable())
                  {
                      QJSValueList arglist;
                      arglist.push_back((int)EnumTool::PayStatusEnum::PAY_STATUS__ERROR);        // 支付状态
                      arglist.push_back((int)EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE); // 支付方式
                      Json json_tmp = {{"receivable_amount", 0.00},
                                       {"received_amount", 0.00},
                                       {"change_amount", 0.00},
                                       {"sn", __LINE__}};

                      arglist.push_back(QString::fromStdString(json_tmp.dump())); // 支付详情
                      auto ret = callback.call(arglist);
                  }
                  if (ConfigTool::getInstance()->isPlayOfflineTTs() )
                  {
                      if(payment_status == 2){
                          ControlManager::getInstance()->getTtsControl()->say("单个商品售价超过上限");
                      }else{
                          ControlManager::getInstance()->getTtsControl()->say("支付失败");
                      }
                  }
              }
        });
    }
}
void PayMethodControl::rechargeMemberByPaymentCodeNingyuQml(QJSValue callback, QVariant payment_code, QVariant cusUnique, QVariant money,QVariant give_money)
{
    ShopControl *shop_control        = ControlManager::getInstance()->getShopControl();
    auto        *shop_shop_cart_list = ControlManager::getInstance()->getShopCartList();

    QString shopUnique      = QString::number(shop_control->getShopUnique());
//    QString saleListUnique  = shop_shop_cart_list->shop_cart_list_data_.order_unique_;
    QString saleListUnique  = OrderControl::generateOrderId();
    QString saleListCashier = QString::number(shop_control->getPersonInfo().cashier_id);
    QString machine_num     = "1";

    QMap<QString, QString> postMap;
    postMap.insert("sale_list_unique", saleListUnique);
    postMap.insert("cusUnique", cusUnique.toString());
    postMap.insert("shopUnique", shopUnique);
    postMap.insert("money", QString::number(money.toFloat()));
    postMap.insert("type", "1"); // 1:充值 2:取现
    postMap.insert("saleListCashier", saleListCashier);
    if(payment_code == ""){
        postMap.insert("recharge_method", "1");              // 1:现金 2:微信 3:支付宝 4：存零 5:退款 6:免密
    }else{
        postMap.insert("recharge_method", "6");              // 1:现金 2:微信 3:支付宝 4：存零 5:退款 6:免密
    }
    postMap.insert("auth_code", payment_code.toString()); // 付款码
    postMap.insert("give_money", QString::number(give_money.toFloat())); //赠送
    QString url_str    = req_host_prefix + "/harricane/cuscheckout/recharge_ny.do";
    auto    cur_thread = new QThread(this); // 工作线程

    ////////////////////

    HttpWorker *http_get_cloud_goods_info = new HttpWorker(url_str, HttpWorker::urlEncode(postMap), "application/x-www-form-urlencoded");
    http_get_cloud_goods_info->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_get_cloud_goods_info, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_get_cloud_goods_info, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_cloud_goods_info, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_cloud_goods_info, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    if (json_doc["status"] == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS));
                            arglist.push_back("会员充值成功");
                            auto ret = callback.call(arglist);
                        }
                        if (ConfigTool::getInstance()->isPlayMemberTts())
                            ControlManager::getInstance()->getTtsControl()->say(tr("会员余额充值成功") + money.toString() + tr("元"));
                    }
                    else if (json_doc["status"] == 0)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__PAYING));
                            arglist.push_back(QString::fromStdString(json_doc["msg"]));
                            auto ret = callback.call(arglist);
                        }
                        // 如果顾客需要手动输入密码支付    宁宇版本此状态为失败不在查询
//                        startCheckRechargeMember(callback, saleListUnique);
//                        if (ConfigTool::getInstance()->isPlayMemberTts())
//                            ControlManager::getInstance()->getTtsControl()->say(tr("请输入支付密码"));
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求在线支付接口");

#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
}
void PayMethodControl::rechargeMemberByPaymentCode4Qml(QJSValue callback, QVariant payment_code, QVariant cusUnique, QVariant money)
{
    ShopControl *shop_control        = ControlManager::getInstance()->getShopControl();
    auto        *shop_shop_cart_list = ControlManager::getInstance()->getShopCartList();

    QString shopUnique      = QString::number(shop_control->getShopUnique());
//    QString saleListUnique  = shop_shop_cart_list->shop_cart_list_data_.order_unique_;
    QString saleListUnique  = OrderControl::generateOrderId();
    QString saleListCashier = QString::number(shop_control->getPersonInfo().cashier_id);
    QString machine_num     = "1";

    QMap<QString, QString> postMap;
    postMap.insert("sale_list_unique", saleListUnique);
    postMap.insert("cusUnique", cusUnique.toString());
    postMap.insert("shopUnique", shopUnique);
    postMap.insert("money", QString::number(money.toFloat()));
    postMap.insert("type", "1"); // 1:充值 2:取现
    postMap.insert("saleListCashier", saleListCashier);
    postMap.insert("recharge_method", "6");               // 1:现金 2:微信 3:支付宝 4：存零 5:退款 6:免密
    postMap.insert("auth_code", payment_code.toString()); // 付款码

    QString url_str    = req_host_prefix + "harricane/cuscheckout/recharge.do";
    auto    cur_thread = new QThread(this); // 工作线程

    ////////////////////

    HttpWorker *http_get_cloud_goods_info = new HttpWorker(url_str, HttpWorker::urlEncode(postMap), "application/x-www-form-urlencoded");
    http_get_cloud_goods_info->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_get_cloud_goods_info, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_get_cloud_goods_info, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_cloud_goods_info, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_cloud_goods_info, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    if (json_doc["status"] == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS));
                            auto ret = callback.call(arglist);
                        }
                        if (ConfigTool::getInstance()->isPlayMemberTts())
                            ControlManager::getInstance()->getTtsControl()->say(tr("会员余额充值成功") + money.toString() + tr("元"));
                    }
                    else if (json_doc["status"] == 0)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__PAYING));
                            auto ret = callback.call(arglist);
                        }
                        // 如果顾客需要手动输入密码支付
                        startCheckRechargeMember(callback, saleListUnique);
                        if (ConfigTool::getInstance()->isPlayMemberTts())
                            ControlManager::getInstance()->getTtsControl()->say(tr("请输入支付密码"));
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求在线支付接口");

#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
}
void PayMethodControl::rechargeMemberByCash4Qml(QJSValue callback, QVariant cusUnique, QVariant money)
{

    ShopControl *shop_control        = ControlManager::getInstance()->getShopControl();
    auto        *shop_shop_cart_list = ControlManager::getInstance()->getShopCartList();

    QString shopUnique      = QString::number(shop_control->getShopUnique());
    QString saleListUnique  = shop_shop_cart_list->shop_cart_list_data_.order_unique_;
    QString saleListCashier = QString::number(shop_control->getPersonInfo().cashier_id);
    QString machine_num     = "1";

    QMap<QString, QString> postMap;
    postMap.insert("sale_list_unique", OrderControl::generateOrderId());
    postMap.insert("cusUnique", cusUnique.toString());
    postMap.insert("shopUnique", shopUnique);
    postMap.insert("money", QString::number(money.toFloat()));
    postMap.insert("type", "1"); // 1:充值 2:取现
    postMap.insert("saleListCashier", saleListCashier);
    postMap.insert("recharge_method", "1"); // 1:现金 2:微信 3:支付宝 4：存零 5:退款 6:免密
    postMap.insert("auth_code", "");        // 付款码

    QString url_str    = req_host_prefix + "harricane/cuscheckout/recharge.do";
    auto    cur_thread = new QThread(this); // 工作线程

    ////////////////////

    HttpWorker *http_get_cloud_goods_info = new HttpWorker(url_str, HttpWorker::urlEncode(postMap), "application/x-www-form-urlencoded");
    http_get_cloud_goods_info->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_get_cloud_goods_info, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_get_cloud_goods_info, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_cloud_goods_info, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程

    connect(http_get_cloud_goods_info, &HttpWorker::sendReply, this, // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    if (json_doc["status"] == 1)
                    {

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS));
                            auto ret = callback.call(arglist);
                        }
                        if (ConfigTool::getInstance()->isPlayMemberTts())
                            ControlManager::getInstance()->getTtsControl()->say(tr("会员余额充值成功" )+ money.toString() + tr("元"));
                    }
                    else if (json_doc["status"] == 0)
                    {

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__ERROR));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求在线支付接口");

#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
}
void PayMethodControl::reqRefundMemberByChangeNingyuQml(QJSValue callback, QVariant cusUnique, QVariant money){

    ShopControl *shop_control        = ControlManager::getInstance()->getShopControl();
    auto        *shop_shop_cart_list = ControlManager::getInstance()->getShopCartList();
    QString shopUnique      = QString::number(shop_control->getShopUnique());
    QString saleListUnique  = shop_shop_cart_list->shop_cart_list_data_.order_unique_;
    QString employee_Id = QString::number(shop_control->getPersonInfo().cashier_id);

    QMap<QString, QString> postMap;
    postMap.insert("shopUnique", shopUnique); //店铺编号
    postMap.insert("cus_id", cusUnique.toString());   //客户编号
    postMap.insert("money", QString::number(money.toFloat())); //退款申请金额
    postMap.insert("remarks", ""); //退款申请原因
    QString url_str    = req_host_prefix + "/harricane/cus/returnNY.do";
    auto    cur_thread = new QThread(this); // 工作线程
    ////////////////////

    HttpWorker *http_get_cloud_goods_info = new HttpWorker(url_str, HttpWorker::urlEncode(postMap), "application/x-www-form-urlencoded");
    http_get_cloud_goods_info->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_get_cloud_goods_info, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_get_cloud_goods_info, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_cloud_goods_info, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_cloud_goods_info, &HttpWorker::sendReply, this, // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!is_succ)
                {
                    // 失败
                    Json         json_tmp = {{"msg", "会员退费失败"}};
                    QJSValueList arglist  = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR,
                                                                       EnumTool::PayMethodEnum::PAY_METHOD__CASH, json_tmp.dump());
                    auto         ret      = callback.call(arglist);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    if (json_doc["status"] == 1)
                    {

                        if (callback.isCallable())
                        {
                            //shop_shop_cart_list->setRechargeChangeAmount(money);

                            // 回调qml中的函数
                            Json         json_tmp = {{"msg", "会员退费成功"}};
                            QJSValueList arglist  = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                               EnumTool::PayMethodEnum::PAY_METHOD__CASH, json_tmp.dump());
                            auto         ret      = callback.call(arglist);
                        }
                        if (ConfigTool::getInstance()->isPlayMemberTts())
                            ControlManager::getInstance()->getTtsControl()->say(tr("会员退费成功") + QString::number(money.toDouble(), 'f', 2) + tr("元"));
                    }
                    else if (json_doc["status"] == 0)
                    {
                        // 失败
                        Json         json_tmp = {{"msg", "会员退费失败"}};
                        QJSValueList arglist  = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR,
                                                                           EnumTool::PayMethodEnum::PAY_METHOD__CASH, json_tmp.dump());
                        auto         ret      = callback.call(arglist);
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求会员退费接口");
#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
}
void PayMethodControl::reqRefundMemberByChange4Qml(QJSValue callback, QVariant cusUnique, QVariant money){

    ShopControl *shop_control        = ControlManager::getInstance()->getShopControl();
    auto        *shop_shop_cart_list = ControlManager::getInstance()->getShopCartList();
    QString shopUnique      = QString::number(shop_control->getShopUnique());
    QString saleListUnique  = shop_shop_cart_list->shop_cart_list_data_.order_unique_;
    QString employee_Id = QString::number(shop_control->getPersonInfo().cashier_id);

    QMap<QString, QString> postMap;
    postMap.insert("shopUnique", shopUnique); //店铺编号
    postMap.insert("cusUnique", cusUnique.toString());   //客户编号
    postMap.insert("refundMoney", QString::number(money.toFloat())); //退款申请金额
    postMap.insert("refundReason", ""); //退款申请原因
    postMap.insert("refundGiftMoney", ""); //退款申请赠送金额
    postMap.insert("cus_status", "1");
    postMap.insert("cashierId", employee_Id);//操作员工编号
    QString url_str    = req_host_prefix + "/harricane/cus/memberRefund.do";
    auto    cur_thread = new QThread(this); // 工作线程
    ////////////////////

    HttpWorker *http_get_cloud_goods_info = new HttpWorker(url_str, HttpWorker::urlEncode(postMap), "application/x-www-form-urlencoded");
    http_get_cloud_goods_info->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_get_cloud_goods_info, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_get_cloud_goods_info, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_cloud_goods_info, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_cloud_goods_info, &HttpWorker::sendReply, this, // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!is_succ)
                {
                    // 失败
                    Json         json_tmp = {{"msg", "会员退费失败"}};
                    QJSValueList arglist  = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR,
                                                                       EnumTool::PayMethodEnum::PAY_METHOD__CASH, json_tmp.dump());
                    auto         ret      = callback.call(arglist);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    if (json_doc["status"] == 1)
                    {

                        if (callback.isCallable())
                        {
                            //shop_shop_cart_list->setRechargeChangeAmount(money);

                            // 回调qml中的函数
                            Json         json_tmp = {{"msg", "会员退费成功"}};
                            QJSValueList arglist  = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                               EnumTool::PayMethodEnum::PAY_METHOD__CASH, json_tmp.dump());
                            auto         ret      = callback.call(arglist);
                        }
                        if (ConfigTool::getInstance()->isPlayMemberTts())
                            ControlManager::getInstance()->getTtsControl()->say(tr("会员退费成功") + QString::number(money.toDouble(), 'f', 2) + tr("元"));
                    }
                    else if (json_doc["status"] == 0)
                    {
                        // 失败
                        Json         json_tmp = {{"msg", "会员退费失败"}};
                        QJSValueList arglist  = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR,
                                                                           EnumTool::PayMethodEnum::PAY_METHOD__CASH, json_tmp.dump());
                        auto         ret      = callback.call(arglist);
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求会员退费接口");
#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
}
void PayMethodControl::reqRechargeMemberByChange4Qml(QJSValue callback, QVariant cusUnique, QVariant money)
{
    if (!mutex__recharge_member_by_change.try_lock())
        return;

    ShopControl *shop_control        = ControlManager::getInstance()->getShopControl();
    auto        *shop_shop_cart_list = ControlManager::getInstance()->getShopCartList();

    QString shopUnique      = QString::number(shop_control->getShopUnique());
    QString saleListUnique  = shop_shop_cart_list->shop_cart_list_data_.order_unique_;
    QString saleListCashier = QString::number(shop_control->getPersonInfo().cashier_id);
    QString machine_num     = "1";

    QMap<QString, QString> postMap;
    postMap.insert("sale_list_unique", OrderControl::generateOrderId());
    postMap.insert("cusUnique", cusUnique.toString());
    postMap.insert("shopUnique", shopUnique);
    postMap.insert("money", QString::number(money.toFloat()));
    postMap.insert("type", "1"); // 1:充值 2:取现
    postMap.insert("saleListCashier", saleListCashier);
    postMap.insert("recharge_method", "4"); // 1:现金 2:微信 3:支付宝 4：存零 5:退款 6:免密

    QString url_str    = req_host_prefix + "harricane/cuscheckout/recharge.do";
    auto    cur_thread = new QThread(this); // 工作线程

    ////////////////////

    HttpWorker *http_get_cloud_goods_info = new HttpWorker(url_str, HttpWorker::urlEncode(postMap), "application/x-www-form-urlencoded");
    http_get_cloud_goods_info->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_get_cloud_goods_info, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_get_cloud_goods_info, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_cloud_goods_info, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程

    connect(http_get_cloud_goods_info, &HttpWorker::sendReply, this, // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                mutex__recharge_member_by_change.unlock();
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    if (json_doc["status"] == 1)
                    {

                        if (callback.isCallable())
                        {
                            shop_shop_cart_list->setRechargeChangeAmount(money);

                            // 回调qml中的函数
                            Json         json_tmp = {{"received_amount", money.toString().toStdString()}, {"sn", __LINE__}};
                            QJSValueList arglist  = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                               EnumTool::PayMethodEnum::PAY_METHOD__CHANGE, json_tmp.dump());
                            auto         ret      = callback.call(arglist);
                        }
                        if (ConfigTool::getInstance()->isPlayMemberTts())
                            ControlManager::getInstance()->getTtsControl()->say(tr("会员余额存零成功") + QString::number(money.toDouble(), 'f', 2) + tr("元"));
                    }
                    else if (json_doc["status"] == 0)
                    {
                        // 失败
                        Json         json_tmp = {{"received_amount", money.toString().toStdString()}, {"sn", __LINE__}};
                        QJSValueList arglist  = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR,
                                                                           EnumTool::PayMethodEnum::PAY_METHOD__CHANGE, json_tmp.dump());
                        auto         ret      = callback.call(arglist);
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求在线支付接口");
#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
}

void PayMethodControl::reqMemberPointExchange4Qml(QJSValue callback, QVariant member_unique, QVariant points_num, QVariant goods_barcode)
{

    ShopControl *shop_control        = ControlManager::getInstance()->getShopControl();
    auto        *shop_shop_cart_list = ControlManager::getInstance()->getShopCartList();

    QString shopUnique      = QString::number(shop_control->getShopUnique());
    QString saleListUnique  = shop_shop_cart_list->shop_cart_list_data_.order_unique_;
    QString saleListCashier = QString::number(shop_control->getPersonInfo().cashier_id);
    QString machine_num     = "1";

    auto shop_ctrl = ControlManager::getInstance()->getShopControl();
    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    GoodsInfo goods_info;
    goods_mgr->getGoodsByBarcode(goods_barcode.toString(), goods_info);


    QMap<QString, QString> postMap;
    postMap.insert("saleListUnique", OrderControl::generateOrderId());                   // 订单号
    postMap.insert("goodsPurprice", QString::number(goods_info.goods_in_price));         // 商品进价
    postMap.insert("saleListDetailPrice", QString::number(goods_info.goods_sale_price)); // 商品售价
    postMap.insert("saleListDetailCount", "1");                                          // 商品数量
    postMap.insert("goodsName", goods_info.goods_name);                                  // 商品名称
    postMap.insert("goodsBarcode", goods_info.goods_barcode);                            // 商品条码
    postMap.insert("goodsId", QString::number(goods_info.goods_id));                     // 商品id
    postMap.insert("shopUnique", QString::number(shop_ctrl->getShopUnique()));           // 店铺id
    postMap.insert("saleListTotal", "0");                                                // 订单总金额
    postMap.insert("saleListCashier", QString::number(shop_ctrl->getUserId()));          // 收银员id
    postMap.insert("saleListRemarks", "");                                               // 订单备注
    postMap.insert("saleListActuallyReceived", "0");                                     // 实收金额
    postMap.insert("memberCard", member_unique.toString());                              // 会员卡号
    postMap.insert("machine_num", "1");                                                  // 机器编号
    postMap.insert("type", "1");                                                         // 0:用服务器时间 1:用机器时间
    postMap.insert("machine_num", "1");                                                  // 机器编号
    postMap.insert("sale_list_payment", "10"); // 1、现金；2、支付宝；3、微信支付；4、银行卡,5储值卡,8混合支付 9 免密支付 10-积分兑换
    postMap.insert("machineTime", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")); // 当前时间

    QString saleListPayDetail = "[{\"pay_method\":10,\"pay_money\":";
    saleListPayDetail += points_num.toString();
    saleListPayDetail += "}]";
    postMap.insert("saleListPayDetail", saleListPayDetail); // 支付详情


    QString url_str    = req_host_prefix + "harricane/payOnline/pointsUse.do";
    auto    cur_thread = new QThread(this); // 工作线程

    ////////////////////

    HttpWorker *http_get_cloud_goods_info = new HttpWorker(url_str, HttpWorker::urlEncode(postMap), "application/x-www-form-urlencoded");
    http_get_cloud_goods_info->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_get_cloud_goods_info, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_get_cloud_goods_info, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_cloud_goods_info, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程

    connect(http_get_cloud_goods_info, &HttpWorker::sendReply, this, // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    if (json_doc["status"] == 1)
                    {
                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QString::fromStdString(reply_data_c));
                        if (callback.isCallable())
                        {
                            // 回调qml中的函数
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        arglist.push_back(QString::fromStdString(reply_data_c));
                        if (callback.isCallable())
                        {
                            // 回调qml中的函数
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求在线支付接口");
#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
}

void PayMethodControl::reqCombinedPay4Qml(QJSValue callback, QVariant member_unique, QVariant pay_detail)
{
    ShopCartList *shop_cart_list                 = ControlManager::getInstance()->getShopCartList();
    auto          shop_control                   = ControlManager::getInstance()->getShopControl();
    shop_cart_list->shop_cart_list_data_.payment = 8;
    QString saleListActuallyReceived_yuan        = QString::number(ControlManager::getInstance()->getShopCartList()->getFinalTotalGoodsPrice());

    OrderInfo cur_order_info         = shop_cart_list->generateOrderInfoByShopCart(1);
    cur_order_info.sale_list_payment = 8; // 1、现金；2、支付宝；3、微信支付；4、银行卡,5储值卡,8混合支付 9 免密支付
    cur_order_info.sale_list_state   = 3;

    unsigned long long shopUnique               = shop_control->getShopUnique(); // 店铺ID
    double             saleListTotal            = .0;                            // 订单总价格
    double             saleListActuallyReceived = .0;                            // 实收总价格

    QString goodsPurprice;                                                     // 商品进价列表
    QString goods_old_price;                                                   // 商品原价列表
    QString saleListDetailPrice;                                               // 商品折后价列表
    QString saleListDetailCount;                                               // 商品个数列表
    QString goodsName;                                                         // 商品名称列表
    QString goodsBarcode;                                                      // 商品条码列表
    QString goodsId;                                                           // 商品ID列表
    QString saleListRemarks;                                                   // 备注
    QString payType       = QString::number(cur_order_info.sale_list_payment); // 支付类型
    QString saleListState = QString::number(cur_order_info.sale_list_state);   // 付款状态

    for (auto &order_detail_info : cur_order_info.order_detail_infos)
    {
        goodsPurprice += QString::number(order_detail_info.goods_purprice) + ";";
        goods_old_price += QString::number(order_detail_info.goods_old_price) + ";";
        saleListDetailCount += QString::number(order_detail_info.sale_list_detail_count) + ";";
        goodsName += order_detail_info.goods_name + ";";
        goodsBarcode += order_detail_info.goods_barcode + ";";
        goodsId += QString::number(order_detail_info.goods_id) + ";";
        saleListTotal += order_detail_info.sale_list_detail_count * order_detail_info.goods_old_price;
        saleListActuallyReceived += order_detail_info.sale_list_detail_price * order_detail_info.sale_list_detail_count;
        saleListDetailPrice += QString::number(order_detail_info.sale_list_detail_subtotal / order_detail_info.sale_list_detail_count) + ";";
    }

    goodsPurprice.chop(1);
    goods_old_price.chop(1);
    saleListDetailCount.chop(1);
    goodsName.chop(1);
    goodsBarcode.chop(1);
    goodsId.chop(1);
    saleListDetailPrice.chop(1);

    auto order_data_time      = cur_order_info.sale_list_datetime;
    auto saleListCashier      = QString::number(cur_order_info.sale_list_cashier);
    auto sale_list_unique     = QString::number(cur_order_info.sale_list_unique);
    auto sale_list_unique_num = cur_order_info.sale_list_unique;

    QMap<QString, QString> body_map;
    body_map.insert("saleListUnique", sale_list_unique);
    body_map.insert("goodsPurprice", goodsPurprice);
    body_map.insert("goods_old_price", goods_old_price);
    body_map.insert("saleListDetailPrice", saleListDetailPrice);
    body_map.insert("saleListDetailCount", saleListDetailCount);
    body_map.insert("goodsName", goodsName);
    body_map.insert("goodsBarcode", goodsBarcode);
    body_map.insert("goodsId", goodsId);
    body_map.insert("shopUnique", QString::number(shopUnique));
    body_map.insert("saleListTotal", QString::number(saleListTotal));
    //    body_map.insert("saleListTotal", QString::number(saleListTotal));
    body_map.insert("saleListActuallyReceived", QString::number(saleListActuallyReceived));
    body_map.insert("saleListCashier", saleListCashier);
    body_map.insert("saleListRemarks", saleListRemarks);
    body_map.insert("machineTime", order_data_time);
    body_map.insert("sale_list_payment", payType);
    body_map.insert("type", "1");
    body_map.insert("saleListState", saleListState);
    body_map.insert("saleListPayDetail", pay_detail.toString());

    Json json_doc = Json::parse(pay_detail.toString().toStdString());
    if (json_doc.is_array())
    {
        for (auto &json_doc_item : json_doc)
        {
            if (json_doc_item.at("pay_method") == 5)
            {
                float pay_money = json_doc_item.at("pay_money");
                body_map.insert("isCusPassword", "0");                          // 是否使用密码
                body_map.insert("storedCard", member_unique.toString());        // 储值卡号
//                body_map.insert("memberCard", member_unique.toString());        // 储值卡号
                body_map.insert("storedCardMoney", QString::number(pay_money)); // 储值卡支付金额
                break;
            }
        }
    }
    body_map.insert("wholesale_phone", ""); // 批发客户的手机号??????
    saleList_actually_received_Temp = saleListActuallyReceived;
    LOG_EVT_INFO("===reqCombinedPay4Qml设置saleList_actually_received_Temp为:{}",saleList_actually_received_Temp);
    //若网络处于离线状态
    if(net_status_global != "101")
    {
        //Json json_tmp = {{"received_amount", saleListActuallyReceived_yuan.toStdString()},
        Json json_tmp = {{"received_amount", QString::number(saleList_actually_received_Temp).toStdString()},
                         {"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                         {"change_amount", 0},
                         {"sn", __LINE__}};
        insertSaleListCombineData(body_map,sale_list_unique);
        double money = shop_cart_list->getTotalGoodsPrice();
        if (ConfigTool::getInstance()->isPlayOfflineTTs())
            ControlManager::getInstance()->getTtsControl()->say(tr("组合支付成功") + numberToEnglish(QString::number(saleList_actually_received_Temp).toDouble()) + tr("元"));

        payFinishWizard(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS);
        if (callback.isCallable())
        {
            auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                       EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD, json_tmp.dump());
            callback.call(arg_list);
        }
        ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);

    }else{
        auto cur_thread = new QThread(this);

        QString req_url = req_host_prefix + "harricane/payOnline/cashierPay.do";

        HttpWorker *cur_http_worker = new HttpWorker(req_url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
        cur_http_worker->moveToThread(cur_thread);
        connect(cur_thread, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
        connect(cur_thread, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
        connect(cur_http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
        connect(cur_http_worker, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
                [=](QNetworkReply *reply) mutable
                {
                    QByteArray reply_data   = reply->readAll();
                    QUrl       request_url  = reply->url();
                    bool       is_succ      = reply->error() == QNetworkReply::NoError;
                    auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                    if (!Json::accept(reply_data_c))
                    {
                        SPDLOG_LOGGER_WARN(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                    }
                    else
                    {
                        Json json_doc = Json::parse(reply_data_c);
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "{}", json_doc.dump());

                        Json json_tmp = {{"received_amount", QString::number(saleList_actually_received_Temp).toStdString()},
                                         {"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                         {"change_amount", 0},
                                         {"sn", __LINE__}};

                        int status = json_doc["status"];

                        if (status == 1)
                        {
                            shop_cart_list->shop_cart_list_data_.cash_received_ = saleList_actually_received_Temp;
                            ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);
                            double money = shop_cart_list->getTotalGoodsPrice();
                            if (ConfigTool::getInstance()->isPlayOfflineTTs())
                                ControlManager::getInstance()->getTtsControl()->say(tr("在线组合支付成功") + numberToEnglish(QString::number(saleList_actually_received_Temp).toDouble()) + tr("元"));
                            payFinishWizard(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS);
                            if (callback.isCallable())
                            {
                                auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                           EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD, json_tmp.dump());
                                callback.call(arg_list);
                            }
                        }
                        else
                        { // status ==0
                            if (callback.isCallable())
                            {
                                auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR, EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD,
                                                                           json_tmp.dump());
                                callback.call(arg_list);
                                if (ConfigTool::getInstance()->isPlayOfflineTTs())
                                    ControlManager::getInstance()->getTtsControl()->say(tr("组合支付失败"));
                                payFinishWizard(EnumTool::PayStatusEnum::PAY_STATUS__ERROR);
                            }
                        }
                    }
                    cur_thread->quit();
                    cur_thread->wait();
                });
        // 启动线程
        is_stop_check_online_pay_ = false;

    #ifdef HIGH_PRIORITY
        cur_thread->start(QThread::TimeCriticalPriority);
    #else
        cur_thread->start();
    #endif
    }
}

void PayMethodControl::checkRechargeMemberByPaymentCode()
{
    auto cur_thread = new QThread(this);

    QString req_url = req_host_prefix + "harricane/payOnline/queryOrderYT.do";

    QMap<QString, QString> body_map;

    auto shopUnique = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());

    body_map.insert("shopUnique", shopUnique);
    body_map.insert("out_trade_no", recharge_member_order_);

    auto body_data = HttpWorker::urlEncode(body_map);

    HttpWorker *http_worker = new HttpWorker(req_url, body_data, "application/x-www-form-urlencoded");
    http_worker->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_worker, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply)                                                // [&, cur_thread](QNetworkReply *reply)
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);

                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_WARN(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "查询结果:{}", json_doc.dump());

                    //                    string trade_state_str = json_doc["data"]["trade_state"];
                    //                    if (trade_state_str == "USERPAYING") {
                    //                        // 订单支付中
                    //                        QJSValueList arglist;
                    //                        arglist.push_back(static_cast<int>(EnumTool::OnlinePayEnum::PAY_STATUS__PAYING));
                    //                        yi_tong_pay_callback_.call(arglist);
                    //                    } else if (trade_state_str == "NOTPAY") {
                    //                        // 订单未支付
                    //                        QJSValueList arglist;
                    //                        arglist.push_back(static_cast<int>(EnumTool::OnlinePayEnum::PAY_STATUS__SUCCESS));
                    //                        yi_tong_pay_callback_.call(arglist);
                    //                        timer_yitong_pay_.stop();
                    //                    } else if (trade_state_str == "SUCCESS") {
                    //                        // 订单已支付
                    //                        ShopCartList *shop_cart_list = ControlManager::getInstance()->getShopCartList();
                    //                        shop_cart_list->shop_cart_list_data_.payment = (int)EnumTool::PayMethodEnum::PAY_METHOD__YI_TONG;
                    //                        ControlManager::getInstance()->getPrinterControl()->printOrder(shop_cart_list);

                    //                        QJSValueList arglist;
                    //                        arglist.push_back(static_cast<int>(EnumTool::OnlinePayEnum::PAY_STATUS__SUCCESS)); // 支付状态

                    //                        shop_cart_list->clearAllGoods();
                    //                        yi_tong_pay_callback_.call(arglist);
                    //                        timer_yitong_pay_.stop();
                    //                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });

    // 启动线程
#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
    ++timer_recharge_member_pay_index_;
    if (timer_recharge_member_pay_index_ > (60 * 3))
    {
        stopCheckRechargeMember();
        if (recharge_member_pay_callback_.isCallable())
        {
            QJSValueList arglist;
            arglist.push_back(static_cast<int>(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS));
            auto ret = recharge_member_pay_callback_.call(arglist);
        }
    }
}

void PayMethodControl::startCheckRechargeMember(QJSValue callback, QString sale_list_unique)
{
    recharge_member_order_             = sale_list_unique;
    timer_recharge_member_pay_index_   = 0;
    recharge_member_pay_callback_      = callback;
    is_stop_check_recharge_member_pay_ = false;

    timer_recharge_member_pay_.disconnect();
    connect(&timer_recharge_member_pay_, &QTimer::timeout, this, &PayMethodControl::checkRechargeMemberByPaymentCode);
    timer_recharge_member_pay_.start();
}

void PayMethodControl::stopCheckRechargeMember()
{
    timer_recharge_member_pay_.stop();
    timer_recharge_member_pay_.disconnect();
    timer_recharge_member_pay_index_ = 0;
    ShopCartList *shop_cart_list     = ControlManager::getInstance()->getShopCartList();
    shop_cart_list->resetOrderUnique();
    is_stop_check_recharge_member_pay_ = true;
}

void PayMethodControl::payByMiniProgram_v2(QJSValue callback, QString payment_code)
{
    ShopControl *shop_control   = ControlManager::getInstance()->getShopControl();
    auto        *shop_cart_list = ControlManager::getInstance()->getShopCartList();

    shop_cart_list->shop_cart_list_data_.payment = 101;

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    double saleList_total_price       = shop_cart_list->getTotalGoodsPrice(); // 订单总价格
    double saleList_actually_received = shop_cart_list->getTotalGoodsPrice();           // 实收总价格

    auto sale_list_unique = shop_cart_list->shop_cart_list_data_.order_unique_;

    Json post_json;
    post_json["authCode"]        = payment_code.toStdString();               // 支付码
    post_json["shopUnique"]      = shop_control->getShopUnique();            // 店铺编号
    post_json["saleListUnique"]  = sale_list_unique.toStdString();           // 订单号
    post_json["saleListCashier"] = shop_control->getPersonInfo().cashier_id; // 收银员ID

    Json goodsList_json;
    for (auto &shop_cart_item : shop_cart_list->shop_cart_Items_)
    {
        GoodsInfo goods_info;
        goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);
        Json tmp_json;
        tmp_json["goodsId"]        = std::to_string(goods_info.goods_id);
        tmp_json["barcode"]        = goods_info.goods_barcode.toStdString();
        tmp_json["name"]           = goods_info.goods_name.toStdString();
        tmp_json["price_in"]       = goods_info.goods_in_price;
        tmp_json["price_original"] = shop_cart_item.getPrice();
        tmp_json["sold_num"]       = shop_cart_item.goods_num_;
        tmp_json["subtotal"]       = shop_cart_item.getSubtotal();

        goodsList_json.push_back(tmp_json);
    }

    post_json["goodsList"]     = goodsList_json;             // 订单详情
    post_json["saleListTotal"] = saleList_actually_received; // 订单总金额(付款价格)

    QString url_str       = req_host_prefix_mini_program + "goBuy/cart/v2/queryPlatformCusInfo.do";
    auto    scoped_thread = new QThread(this); // 工作线程

    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "payByMiniProgram_v2 : {}", url_str.toStdString());
    saleList_actually_received_Temp = saleList_actually_received;
    LOG_EVT_INFO("===payByMiniProgram_v2设置saleList_actually_received_Temp为:{}",saleList_actually_received_Temp);
    HttpWorker *scoped_http_worker = new HttpWorker(url_str, QString::fromStdString(post_json.dump()).toUtf8());
    scoped_http_worker->moveToThread(scoped_thread);

    connect(scoped_thread, &QThread::started, scoped_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(scoped_thread, &QThread::finished, scoped_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(scoped_http_worker, &HttpWorker::destroyed, scoped_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程

    connect(scoped_http_worker, &HttpWorker::sendReply, this, // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    auto status = json_doc["status"];
                    // status 1成功 2等待支付 3订单已付款(已提交过?)
                    if (status == 1)
                    {
                        ShopCartList *shop_cart_list = ControlManager::getInstance()->getShopCartList();
                        // shop_cart_list->shop_cart_list_data_.payment = (int)EnumTool::PayMethodEnum::PAY_METHOD__MINI_PROGRAM;
                        ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);

                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "开始解析小程序{}订单数据详情", sale_list_unique.toStdString());
                        //                        querySaleListDetail(sale_list_unique);
                        if (json_doc.contains("data") && json_doc["data"].contains("sale_list_unique"))
                        {
                            unsigned long long order_data = json_doc["data"]["sale_list_unique"];
                            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "获取到data中的数据{}", order_data);
                        }
                        payFinishWizard();

                        // 回调qml中的函数
                        if (callback.isCallable())
                        {
                            querySaleListDetail(callback, sale_list_unique);

                            if (!json_doc.contains("data"))
                            {
                                LOG_NET_ERROR("小程序支付返回数据解析错误");
                                return;
                            }

                            LOG_NET_INFO("小程序支付成功");

                            Json json_tmp = {{"receivable_amount", saleList_total_price}, {"received_amount", saleList_actually_received}, {"sn", __LINE__}};

                            auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                       EnumTool::PayMethodEnum::PAY_METHOD__MINI_PROGRAM, json_tmp.dump());

                            callback.call(arg_list);
                        }
                        if (ConfigTool::getInstance()->isPlayMiniProgram())
                            ControlManager::getInstance()->getTtsControl()->say(tr("小程序支付成功") + QString::number(saleList_actually_received) + tr("元"));
                    }
                    else if (status == 2)
                    {
                        // 如果顾客需要手动输入密码支付
                        QVariant var_tmp;

                        if (json_doc.contains("data") && json_doc["data"].contains("sale_list_unique"))
                        {
                            unsigned long long order_data = json_doc["data"]["sale_list_unique"];
                            callback_mini_program_pay_    = callback;
                            order_mini_program_pay_       = sale_list_unique;
                            timer_mini_program_pay_.disconnect();
                            connect(&timer_mini_program_pay_, &QTimer::timeout, this, &PayMethodControl::checkMiniProgramPay);
                            timer_mini_program_pay_.start();

                            if (ConfigTool::getInstance()->isPlayMiniProgram())
                                ControlManager::getInstance()->getTtsControl()->say(tr("请输入支付密码"));
                        }
                    }
                    else if (status == 3)
                    {
                        payFinishWizard();

                        if (callback.isCallable())
                        {
                            Json json_tmp = {{"received_amount", saleList_actually_received}, {"sn", __LINE__}};
                            auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAID,
                                                                       EnumTool::PayMethodEnum::PAY_METHOD__MINI_PROGRAM, json_tmp.dump());

                            auto ret = callback.call(arg_list);
                        }
                        if (ConfigTool::getInstance()->isPlayMiniProgram())
                            ControlManager::getInstance()->getTtsControl()->say(tr("订单重复提交"));
                    }
                }
                scoped_thread->quit();
                scoped_thread->wait();
            });
    // 启动线程
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求在线支付接口");
#ifdef HIGH_PRIORITY
    scoped_thread->start(QThread::TimeCriticalPriority);
#else
    scoped_thread->start();
#endif
    if (callback.isCallable())
    {
        Json json_tmp = {{"receivable_amount", saleList_total_price},
                         {"received_amount", saleList_actually_received},
                         {"change_amount", QString::number((saleList_total_price - saleList_actually_received), 'f', 2).toStdString()},
                         {"sn", __LINE__}};
        auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING, EnumTool::PayMethodEnum::PAY_METHOD__YI_TONG, json_tmp.dump());
        callback.call(arg_list);
    }
}

void PayMethodControl::querySaleListDetail(QJSValue callback, QString sale_list_unique)
{
    QString url = req_host_prefix + "/goBuy/my/querySaleListDetail.do";
    bodyMap body_map;
    body_map["shop_unique"]      = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());
    body_map["sale_list_unique"] = sale_list_unique;

    auto cur_thread = new QThread(); // 工作线程

    HttpWorker *http_worker = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_worker->moveToThread(cur_thread);

    auto *shop_shop_cart_list = ControlManager::getInstance()->getShopCartList();

    double saleList_total_price       = shop_shop_cart_list->getTotalGoodsPrice(); // 订单总价格
    double saleList_actually_received = shop_shop_cart_list->getTotalGoodsPrice();           // 实收总价格

    connect(cur_thread, &QThread::started, http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程

    connect(http_worker, &HttpWorker::sendReply, this, // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    int status = json_doc["status"];

                    if (status == 1)
                    {
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "status {}", status);
                        if (callback.isCallable())
                        {
                            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "callback.isCallable() {}", status);

                            auto &json_doc_data = json_doc["data"];

                            auto mini_program_discount   = getDataFromJson(json_doc_data, "coupon_amount").toDouble();
                            auto mini_program_beans_use  = getDataFromJson(json_doc_data, "beans_money").toDouble();
                            auto mini_program_beans_give = getDataFromJson(json_doc_data, "beans_get").toDouble();
                            auto mini_program_balance    = getDataFromJson(json_doc_data, "card_deduction").toDouble();
                            auto mini_program_wechat     = getDataFromJson(json_doc_data, "balance_money").toDouble();

                            Json json_tmp = {

                                {"receivable_amount", saleList_total_price},
                                {"received_amount", saleList_actually_received},
                                {"mini_program_discount", mini_program_discount},
                                {"mini_program_beans_use", mini_program_beans_use},
                                {"mini_program_beans_give", mini_program_beans_give},
                                {"mini_program_balance", mini_program_balance},
                                {"mini_program_wechat", mini_program_wechat},
                                {"sn", __LINE__}

                            };

                            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "callback.isCallable() {}", json_tmp.dump());

                            auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                       EnumTool::PayMethodEnum::PAY_METHOD__MINI_PROGRAM, json_tmp.dump());

                            auto ret = callback.call(arg_list);
                        }
                    }
                }
            });
    // 启动线程
    cur_thread->start();
}

void PayMethodControl::checkMiniProgramPay()
{
    auto &cur_index    = index_mini_program_pay_;
    auto &cur_timer    = timer_mini_program_pay_;
    auto &cur_callback = callback_mini_program_pay_;

    auto  *shop_shop_cart_list        = ControlManager::getInstance()->getShopCartList();
    double saleList_total_price       = shop_shop_cart_list->getTotalGoodsPrice(); // 订单总价格
    double saleList_actually_received = shop_shop_cart_list->getTotalGoodsPrice();           // 实收总价格

    QMap<QString, QString> postMap;
    postMap.insert("sale_list_unique", order_mini_program_pay_);

    QString url_str = req_host_prefix_mini_program + "goBuy/my/querySaleListDetailStatus.do";

    auto cur_thread = new QThread(this); // 工作线程

    QString saleListActuallyReceived_yuan = QString::number(ControlManager::getInstance()->getShopCartList()->getFinalTotalGoodsPrice());

    HttpWorker *http_get_cloud_goods_info = new HttpWorker(url_str, HttpWorker::urlEncode(postMap), "application/x-www-form-urlencoded");
    http_get_cloud_goods_info->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_get_cloud_goods_info, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_get_cloud_goods_info, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_cloud_goods_info, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程

    connect(http_get_cloud_goods_info, &HttpWorker::sendReply, this, // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    auto status = json_doc.value("status", 0);

                    // 订单状态：2待发货-3待收货-4已完成-5已取消-6待评论-7配送单待确认8-待付款9-待自提?????????????????
                    //  status 1成功
                    if (status == 1)
                    {
                        if (json_doc.contains("data") && json_doc["data"].contains("sale_list_handlestate"))
                        {
                            QVariant variant_tmp;
                            tryFromJson(json_doc["data"], "sale_list_handlestate", variant_tmp);

                            auto sale_list_handlestate = variant_tmp.toString();

                            if (sale_list_handlestate == "8")
                            {
                                // 顾客正在支付",
                                // 回调qml中的函数
                                if (cur_callback.isCallable())
                                {
                                    Json json_tmp = {
                                        {"received_amount", saleListActuallyReceived_yuan.toStdString()},
                                        {"receivable_amount", QString::number(saleList_total_price, 'f', 2).toStdString()},
                                        {"change_amount", QString::number((saleList_total_price - saleList_actually_received), 'f', 2).toStdString()},
                                        {"sn", __LINE__}};
                                    auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING,
                                                                               EnumTool::PayMethodEnum::PAY_METHOD__MINI_PROGRAM, json_tmp.dump());

                                    auto ret = cur_callback.call(arg_list);
                                }
                            }
                            else if (sale_list_handlestate == "5")
                            {
                                // 顾客已取消
                                stopCheckMiniProgramPay();

                                // 回调qml中的函数
                                if (cur_callback.isCallable())
                                {
                                    Json json_tmp = {{"received_amount", saleListActuallyReceived_yuan.toStdString()}, {"sn", __LINE__}};
                                    auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__CANCEL,
                                                                               EnumTool::PayMethodEnum::PAY_METHOD__MINI_PROGRAM, json_tmp.dump());

                                    auto ret = cur_callback.call(arg_list);
                                }
                                if (ConfigTool::getInstance()->isPlayMiniProgram())
                                    ControlManager::getInstance()->getTtsControl()->say(tr("小程序支付失败") + saleListActuallyReceived_yuan + tr("元"));
                            }
                            else if (sale_list_handlestate == "6")
                            {
                                // 顾客已支付
                                stopCheckMiniProgramPay();

                                payFinishWizard();

                                // 回调qml中的函数
                                if (cur_callback.isCallable())
                                {
                                    auto sale_list_unique = shop_shop_cart_list->shop_cart_list_data_.order_unique_;
                                    querySaleListDetail(cur_callback, sale_list_unique);

                                    Json json_tmp = {
                                        {"receivable_amount", saleList_total_price}, {"received_amount", saleList_actually_received}, {"sn", __LINE__}};


                                    auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                               EnumTool::PayMethodEnum::PAY_METHOD__MINI_PROGRAM, json_tmp.dump());
                                    auto ret      = cur_callback.call(arg_list);
                                }
                                if (ConfigTool::getInstance()->isPlayMiniProgram())
                                    ControlManager::getInstance()->getTtsControl()->say(tr("小程序支付成功") + saleListActuallyReceived_yuan + tr("元"));
                            }
                            else
                            {
                                // 未知
                                stopCheckMiniProgramPay();

                                // 回调qml中的函数
                                if (cur_callback.isCallable())
                                {
                                    Json json_tmp = {{"received_amount", saleListActuallyReceived_yuan.toStdString()}, {"sn", __LINE__}};
                                    auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__UNKNOW,
                                                                               EnumTool::PayMethodEnum::PAY_METHOD__MINI_PROGRAM, json_tmp.dump());
                                    auto ret      = cur_callback.call(arg_list);
                                }
                            }
                        }
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "检查小程序订单状态");
#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
    ++cur_index;
    if (cur_index > (60 * 3))
    {
        cur_index = 0;
        cur_timer.stop();

        if (cur_callback.isCallable())
        {
            Json json_tmp = {{"received_amount", saleListActuallyReceived_yuan.toStdString()}, {"sn", __LINE__}};
            auto arg_list =
                generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__TIME_OUT, EnumTool::PayMethodEnum::PAY_METHOD__MINI_PROGRAM, json_tmp.dump());
            auto ret = cur_callback.call(arg_list);
        }
    }
}

void PayMethodControl::stopCheckMiniProgramPay()
{
    index_mini_program_pay_ = 0;
    timer_mini_program_pay_.stop();
}
QMap<QString, QString> PayMethodControl::readTxtToQMap(const QString& filePath) {
    QMap<QString, QString> resultMap;
    QFile file(filePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream in(&file);
        while (!in.atEnd()) {
            QString line = in.readLine();
            int separatorPos = line.indexOf(':');
            if (separatorPos != -1) {
                QString key = line.left(separatorPos).trimmed();
                QString value = line.mid(separatorPos + 1).trimmed(); // 去除':'后的空格
                resultMap[key] = value;
            }
        }
        file.close();
    } else {
        LOG_EVT_INFO("Failed to open file!!!");
    }

    return resultMap;
}

bool PayMethodControl::insertSaleListData(QString dataString,QString saleListUnique)
{
    auto        *shop_cart_list = ControlManager::getInstance()->getShopCartList();
    QDir dir = QDir(QCoreApplication::applicationDirPath() + "/OfflineSalesData");

    if (!dir.exists())
    {
        dir.mkdir(dir.absolutePath());
    }

    QFile file(dir.path() + "/" + saleListUnique + ".txt");
    if (!file.open(QIODevice::ReadWrite | QIODevice::Text | QIODevice::Truncate))
        return false;
    QByteArray data = "";
    data.append(dataString);
    file.write(data, qstrlen(data));
    file.close();
    return true;
}
bool PayMethodControl::insertSaleListCombineData(QMap<QString, QString> body_map, QString saleListUnique) {
    QDir dir(QCoreApplication::applicationDirPath() + "/OfflineSalesData");
    if (!dir.exists()) {
        dir.mkpath(dir.absolutePath());
    }

    QFile file(dir.path() + "/" + saleListUnique + ".txt");
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text | QIODevice::Truncate)) {
        return false;
    }

    QTextStream out(&file);
    for (auto it = body_map.begin(); it != body_map.end(); ++it) {
        out << it.key() << ": " << it.value() << "\n";
    }
    file.close();
    return true;
}



void PayMethodControl::uploadNotUploadOrder_v2(bool is_online_pay)
{
    LOG_EVT_INFO("==进入订单上传接口==");
    scoped_lock<mutex> lock(upload_order_mutex_v2_);

    auto shop_control   = ControlManager::getInstance()->getShopControl();
    auto shop_cart_list = ControlManager::getInstance()->getShopCartList();
    auto order_mgr      = DataManager::getInstance()->getOrderDataMgr();

    auto not_upload_orders = order_mgr->getNotUploadOrders();
    LOG_EVT_INFO("==not_upload_orders============：{}==",not_upload_orders.size());
    for (auto &cur_not_upload_order : not_upload_orders)
    {
        double sale_list_total             = .0; // 订单总价格
        double sale_list_actually_received = .0; // 实收总价格
        auto   sale_list_unique_num        = cur_not_upload_order.sale_list_unique;
        Json   post_json;
        post_json["payType"]        = is_online_pay ? 2 : 1;
        post_json["shopUnique"]     = shop_control->getShopUnique();         // 店铺编号
        post_json["saleListUnique"] = cur_not_upload_order.sale_list_unique; // 订单号
        post_json["machineNum"]     = 1;                                     // 默认1
        // 收银方式：1-现金 2-支付宝 3-微信 4-银行卡  5-储值卡 6-美团外卖7-饿了么外卖 8-混合支付
        // 9-免密支付 10-积分兑换 11-百货豆 12-拉卡拉 13-易通付款码支付 14-合利宝刷卡
        post_json["saleListPayment"] = cur_not_upload_order.sale_list_payment;

        post_json["type"]            = 2;                                      // 默认1 设备时间 2 使用系统时间
        post_json["saleListCashier"] = cur_not_upload_order.sale_list_cashier; // 收银员ID

        // 付款状态-1货到付款未付款，2网上订单未付款，3已付款 ,4赊账 ，5申请退款 ，6同意退款 7拒绝退款 8自助收银未付款
        post_json["saleListState"] = cur_not_upload_order.sale_list_state;
        post_json["machineTime"]   = cur_not_upload_order.sale_list_datetime.toStdString(); // 上传时间

        Json goodsList_json;
        LOG_EVT_INFO("==cur_not_upload_order.order_detail_infos：{}==",cur_not_upload_order.order_detail_infos.size());
        for (auto &order_detail_info : cur_not_upload_order.order_detail_infos)
        {
            Json tmp_json;
            tmp_json["goodsId"]        = std::to_string(order_detail_info.goods_id);
            tmp_json["barcode"]        = order_detail_info.goods_barcode.toStdString();
            tmp_json["name"]           = order_detail_info.goods_name.toStdString();
            tmp_json["price_in"]       = order_detail_info.goods_purprice; // goods_old_price;
            tmp_json["price_original"] = order_detail_info.sale_list_detail_price;
            tmp_json["sold_num"]       = order_detail_info.sale_list_detail_count;
            tmp_json["subtotal"]       = order_detail_info.sale_list_detail_subtotal;

            goodsList_json.push_back(tmp_json);

            sale_list_total += order_detail_info.sale_list_detail_count * order_detail_info.goods_old_price;
        }
        sale_list_actually_received = cur_not_upload_order.sale_list_actually_received;

        post_json["goodsList"]                = goodsList_json;                                 // 订单详情
        post_json["saleListTotal"]            = shop_cart_list->getTotalGoodsPrice(); // 订单总金额
        post_json["saleListActuallyReceived"] = sale_list_actually_received;                    // 实收金额
        post_json["pointsRatio"] = cur_not_upload_order.local_points_ratio;                // 积分比例
        post_json["memberCard"]  = cur_not_upload_order.local_member_unique.toStdString(); // 会员卡号
        //post_json["storedCard"]  = cur_not_upload_order.local_member_unique.toStdString(); // 储值卡号或手机号

        if (cur_not_upload_order.order_pay_method_vec.size())
        {
            Json pay_detail_array;
            for (auto &order_pay_method : cur_not_upload_order.order_pay_method_vec)
            {
                Json pay_detail;
                pay_detail["pay_method"] = order_pay_method.pay_method;
                pay_detail["pay_money"]  = order_pay_method.pay_price;
                pay_detail_array.push_back(pay_detail);
            }
            post_json["saleListPayDetail"] = pay_detail_array.dump();
        }
        saleList_actually_received_Temp = shop_cart_list->getTotalGoodsPrice();
        LOG_EVT_INFO("===uploadNotUploadOrder_v2设置saleList_actually_received_Temp为:{}",saleList_actually_received_Temp);
        LOG_EVT_INFO("==进入订单上传接口 判断网络前==");
        //若网络处于离线状态
        if(net_status_global != "101")
        {
           post_json["type"] = 1;
           post_json["machineTime"]   = getCurrentTimeFormatted().toStdString(); //更新时间为当前时间
           if(insertSaleListData(QString::fromStdString(post_json.dump()),QString::number(cur_not_upload_order.sale_list_unique))){
               order_mgr->getOrderById(cur_not_upload_order.sale_list_unique).sale_list_same_type = 1;
           }
        }
        else{
            LOG_EVT_INFO("==网络连接成功 开启实时订单上传1==");
            QString       url         = req_host_prefix + "harricane/payOnline/v2/cashierPay.do";
            HttpClient *  http_client = new HttpClient();
            HttpRequest &&request     = http_client->post(url);

            request.bodyWithJson(post_json.dump());

            execAndCallback4Cpp(request,
                                [=](HttpHandleType http_handle_type, std::string data) mutable
                                {
                                    switch (http_handle_type)
                                    {
                                    case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                        {
                                            auto json_doc = Json::parse(data, nullptr, false);
                                            if (json_doc.is_discarded())
                                            {
                                                LOG_NET_ERROR("数据解析错误");
                                            }
                                            else
                                            {
                                                if (json_doc["status"] == 1)
                                                {
                                                    order_mgr->getOrderById(sale_list_unique_num).sale_list_same_type = 1;
                                                    LOG_EVT_INFO( "{} sale_list_same_type = 1", sale_list_unique_num);
                                                }
                                                else if(json_doc["status"] == 2){
                                                    LOG_EVT_INFO( "实时订单返回数据status为2，单个商品售价超过上限");
                                                }
                                                else{
                                                    LOG_EVT_INFO( "实时订单返回数据status不为1、2，提交失败");
                                                }
                                            }
                                            break;
                                        }
                                    case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                                    case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                        {
                                            break;
                                        }
                                    }
                                    http_client->deleteLater();
                                });
//            uploadNotUploadOrder_offlineOrder();关闭该入口
        }
    }
}
QString PayMethodControl::getNetStatus(){
    return net_status_global;
}
void extractMachineTime(const QString &strLineContent) {
    if (strLineContent.contains("saleListPayDetail")) {
        int startTimeIndex = strLineContent.indexOf("machineTime:") + strlen("machineTime:");
        if (startTimeIndex != -1) {
            int endTimeIndex = strLineContent.indexOf(";", startTimeIndex);
            if (endTimeIndex != -1) {
                QString machineTime = strLineContent.mid(startTimeIndex, endTimeIndex - startTimeIndex);
                qDebug() << "Extracted machineTime:" << machineTime;
            }
        }
    } else {
        qDebug() << "saleListPayDetail field not found.";
    }
}
QString PayMethodControl::getDataFromUnuploadFiles()
{
    QString strLineContent;
    QString strReturnContent;
    QString strReturnContentTemp;

    QDir          appDir     = QCoreApplication::applicationDirPath();
    QString       appDirPath = appDir.path() + "/OfflineSalesData/";
    QDir          sourceDir(appDirPath);
    QFileInfoList fileInfoList = sourceDir.entryInfoList();
    foreach (QFileInfo fileInfo, fileInfoList)
    {
        if (fileInfo.fileName() == "." || fileInfo.fileName() == "..")
            continue;
        QFile       file(fileInfo.filePath());
        QString     strOrderNum     = fileInfo.fileName();
        QStringList strTempOrderNum = strOrderNum.split(".txt");
        QString     orderNum        = strTempOrderNum.at(0);
        if (!file.open(QIODevice::ReadWrite | QIODevice::Text))
        {
            LOG_EVT_INFO("Can't open the file!");
        }
        QTextStream stream(&file);
        stream.setCodec("UTF-8");
        strLineContent = "";
        while (!stream.atEnd())
        {
            QString str = stream.readLine();
            strLineContent += str + ";";
        }
        LOG_EVT_INFO("strLineContent:{}",strLineContent.toStdString());

        if (strLineContent.contains("saleListPayDetail")) {
            QJsonDocument doc = QJsonDocument::fromJson(strLineContent.toUtf8());
            QJsonObject obj = doc.object();
            QString orderDateTime ,orderSum;
            int startTimeIndex = strLineContent.indexOf("machineTime:") + strlen("machineTime:");
            if (startTimeIndex != -1) {
                int endTimeIndex = strLineContent.indexOf(";", startTimeIndex);
                if (endTimeIndex != -1) {
                    orderDateTime = strLineContent.mid(startTimeIndex, endTimeIndex - startTimeIndex);
                    LOG_EVT_INFO("Extracted orderDateTime:{}",orderDateTime.toStdString());
                }
            }

            int startTimeIndex1 = strLineContent.indexOf("saleListActuallyReceived:") + strlen("saleListActuallyReceived:");
            if (startTimeIndex1 != -1) {
                int endTimeIndex1 = strLineContent.indexOf(";", startTimeIndex1);
                if (endTimeIndex1 != -1) {
                    orderSum = strLineContent.mid(startTimeIndex1, endTimeIndex1 - startTimeIndex1);
                    LOG_EVT_INFO("Extracted orderSum:{}",orderSum.toStdString());
                }
            }
            strReturnContentTemp      = orderDateTime + "^" + orderNum + "^" + orderSum + "&";
            strReturnContent += strReturnContentTemp;

        }
        else {
            LOG_EVT_INFO("saleListPayDetail field not found.");
            strLineContent.remove(";");
            QJsonDocument doc = QJsonDocument::fromJson(strLineContent.toUtf8());
            QJsonObject obj = doc.object();

            QString orderDateTime = obj["machineTime"].toString();
            double soldNum = obj["saleListActuallyReceived"].toDouble();
            LOG_EVT_INFO("soldNum:{}",soldNum);
            QString orderSum = QString::number(soldNum, 'f', 2);
            LOG_EVT_INFO("orderSum:{}",orderSum.toStdString());
            strReturnContentTemp      = orderDateTime + "^" + orderNum + "^" + orderSum + "&";
            strReturnContent += strReturnContentTemp;
        }
    }
    strReturnContent = strReturnContent.left(strReturnContent.length() - 1);
    LOG_EVT_INFO("strReturnContent:{}",strReturnContent.toStdString());
    return strReturnContent;
}
int PayMethodControl::getUnuploadRecCount()
{
    int         saleListCount = 0;
    QDir        appDir        = QCoreApplication::applicationDirPath();
    QString     saleListPath  = appDir.path() + "/OfflineSalesData/";
    QDir       *dir           = new QDir(saleListPath);
    QStringList filter;
    filter << "*.txt";
    dir->setNameFilters(filter);                    // 过滤文件类型
    QList<QFileInfo> *fileInfo = new QList<QFileInfo>(dir->entryInfoList(filter));
    saleListCount              = fileInfo->count(); // 文件个数

    LOG_EVT_INFO("未上传服务器的订单位置:{}数量:{}",saleListPath.toStdString(),saleListCount);

    return saleListCount;
}
void PayMethodControl::sleepForSeconds(int seconds)
{
    QThread::sleep(seconds);
}
int PayMethodControl::uploadNotUploadOrder_offlineOrder()
{
    auto order_mgr      = DataManager::getInstance()->getOrderDataMgr();
    LOG_EVT_INFO("==进入离线订单上传接口==");
    QDir dir = QDir(QCoreApplication::applicationDirPath() + "/OfflineSalesData");
    QFileInfoList fileInfoList = dir.entryInfoList();
    QStringList strTmp;
    QString fileContain;
    foreach(QFileInfo fileInfo, fileInfoList)
    {

        if(fileInfo.fileName() == "." || fileInfo.fileName() == ".." || fileInfo.isDir())
        {
            LOG_EVT_INFO("不存在离线订单");
            continue;
            return -1;
        }
        else
        {
            LOG_EVT_INFO("fileInfo.fileName:{}", fileInfo.fileName().toStdString());
            QDir dir2 = QDir(QCoreApplication::applicationDirPath() + "/OfflineSalesData");
            QFile file(dir2.path()+ "/" + fileInfo.fileName());
            QString filePath = QCoreApplication::applicationDirPath() + "/OfflineSalesData/" + fileInfo.fileName();
            if (!file.open(QIODevice::ReadWrite | QIODevice::Text ))
                return -1;
            strTmp.clear();
            fileContain = file.readAll();
            LOG_EVT_INFO("fileContain:{}",fileContain.toStdString());
            if (fileContain.isEmpty())
            {
                file.remove();
                file.close();
                continue;
            }else{
                bool containsSaleListPayDetail = false;

                QStringList lines = fileContain.split('\n');
                for (const QString &line : lines) {
                    QStringList parts = line.split(':');
                    if (parts.size() >= 2 &&parts[0].trimmed() == "saleListPayDetail") {
                        containsSaleListPayDetail = true;
                        break;
                    }
                }
                if (containsSaleListPayDetail) {
                    LOG_EVT_INFO("File contains saleListPayDetail.");
                } else {
                    LOG_EVT_INFO("File does not contain saleListPayDetail.");
                }
                if(containsSaleListPayDetail){
                    if(net_status_global == "101"){
                        LOG_EVT_INFO("==网络连接成功 开启组合离线订单上传==");
                        QMap<QString, QString> body_map = readTxtToQMap(filePath);
                        QString saleListUnique = "";
                        if (!body_map.isEmpty()) {
                            // 数据读取成功，现在可以使用dataMap
                            // 例如，访问某个键的值
                            saleListUnique = body_map["saleListUnique"];
                            LOG_EVT_INFO("读取到的离线订单号为:{}",saleListUnique.toStdString());
                        } else {
                            LOG_EVT_INFO("未能成功读取文件或文件为空");
                        }

                        auto cur_thread = new QThread(this);

                        QString req_url = req_host_prefix + "harricane/payOnline/cashierPay.do";

                        HttpWorker *cur_http_worker = new HttpWorker(req_url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
                        cur_http_worker->moveToThread(cur_thread);
                        connect(cur_thread, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
                        connect(cur_thread, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
                        connect(cur_http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
                        connect(cur_http_worker, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
                                [=](QNetworkReply *reply) mutable
                                {

                                    QByteArray reply_data   = reply->readAll();
                                    QUrl       request_url  = reply->url();
                                    bool       is_succ      = reply->error() == QNetworkReply::NoError;
                                    auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "组合离线订单-请求数据是否成功{}", is_succ);
                                    if (!Json::accept(reply_data_c))
                                    {
                                        SPDLOG_LOGGER_WARN(LogMgr::getInst()->logger_net_, "组合离线订单-数据解析错误{}", reply_data_c);
                                    }
                                    else
                                    {
                                        Json json_doc = Json::parse(reply_data_c);
                                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "{}", json_doc.dump());

                                        int status = json_doc["status"];
                                        QString sale_list_unique = json_doc["sale_list_unique"];
                                        LOG_EVT_INFO("离线订单上传成功，订单号为：{}",sale_list_unique.toStdString());
                                        LOG_EVT_INFO("离线订单上传成功，TXT中的订单号为：{}",saleListUnique.toStdString());
                                        QDir dir3 = QDir(QCoreApplication::applicationDirPath() + "/OfflineSalesData");
                                        QFile file3(dir3.path()+ "/" + fileInfo.fileName());
                                        if (status == 1)
                                        {
                                            if(sale_list_unique == saleListUnique){
                                                LOG_EVT_INFO("订单号匹配成功");
                                                LOG_EVT_INFO("组合上传离线文件成功，删除订单文件:{}",fileInfo.fileName().toStdString());
                                                file3.remove();
                                            }
                                            else{
                                                LOG_EVT_INFO("订单号匹配失败");
                                            }
                                        }
                                        else
                                        {
                                            LOG_EVT_INFO("====组合离线订单上传失败==");
                                        }
                                    }
                                    cur_thread->quit();
                                    cur_thread->wait();
                                });
                        // 启动线程
                        is_stop_check_online_pay_ = false;

                    #ifdef HIGH_PRIORITY
                        cur_thread->start(QThread::TimeCriticalPriority);
                    #else
                        cur_thread->start();
                    #endif
                    }
                }else{
                    //开启订单上传
                    if(net_status_global == "101"){
                        LOG_EVT_INFO("==网络连接成功 开启非组合离线订单上传==filePath：{}",filePath.toStdString());

                        QRegularExpression re(QString(R"("saleListUnique":.*?,"shopUnique":)"));
                        QRegularExpressionMatch match = re.match(fileContain);
                        QString saleListUnique;
                        if (match.hasMatch()) {
                            saleListUnique = match.captured(0);
                            saleListUnique.remove(0, QString("\"saleListUnique\":").size());
                            saleListUnique.chop(QString(",\"shopUnique\":").size());
                            qDebug() << "Data between 'saleListUnique' and 'shopUnique': " << saleListUnique;
                        } else {
                            qDebug() << "Cannot find the range between 'saleListUnique' and 'shopUnique'.";
                        }
                        LOG_EVT_INFO("读取到的离线订单号为:{}",saleListUnique.toStdString());
                        //对离线订单内容进行判断处理
                        QString       url         = req_host_prefix + "harricane/payOnline/v2/cashierPay.do";
                        HttpClient *  http_client = new HttpClient();
                        HttpRequest &&request     = http_client->post(url);
                        request.bodyWithJson(fileContain);
                        execAndCallback4Cpp(request,
                                            [=](HttpHandleType http_handle_type, std::string data) mutable
                                            {
                                                switch (http_handle_type)
                                                {
                                                case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                                    {
                                                        auto json_doc = Json::parse(data, nullptr, false);
                                                        if (json_doc.is_discarded())
                                                        {
                                                            LOG_NET_ERROR("数据解析错误");
                                                        }
                                                        else
                                                        {
                                                            QDir dir3 = QDir(QCoreApplication::applicationDirPath() + "/OfflineSalesData");
                                                            QFile file3(dir3.path()+ "/" + fileInfo.fileName());
                                                            if (json_doc["sale_list_unique"].is_null()) {
                                                                LOG_EVT_INFO( "sale_list_unique is null.");
                                                                break;
                                                            } else {
                                                            }
                                                            QString sale_list_unique = json_doc["sale_list_unique"];
                                                            LOG_EVT_INFO("离线订单上传成功，订单号为：{}",sale_list_unique.toStdString());
                                                            LOG_EVT_INFO("离线订单上传成功，TXT中的订单号为：{}",saleListUnique.toStdString());
                                                            if (json_doc["status"] == 1)
                                                            {
                                                                if(sale_list_unique == saleListUnique){
                                                                    LOG_EVT_INFO("订单号匹配成功");
                                                                    LOG_EVT_INFO("非组合上传离线文件成功，删除订单文件:{}",fileInfo.fileName().toStdString());
                                                                    file3.remove();
                                                                    order_mgr->getOrderById(std::stoull(json_doc["sale_list_unique"].get<std::string>())).sale_list_same_type = 1;
                                                                    LOG_EVT_INFO( "{} sale_list_same_type = 1", json_doc["sale_list_unique"]);
                                                                }
                                                                else{
                                                                    LOG_EVT_INFO("订单号匹配失败");
                                                                    //文件夹筛选是否存该订单进行操作
                                                                    foreach(QFileInfo fileInfo1, fileInfoList)
                                                                    {
                                                                        if(fileInfo1.fileName() != sale_list_unique) {
                                                                            continue;
                                                                        } else {
                                                                            LOG_EVT_INFO("订单匹配失败，从文件夹中找到未删除的该订单并进行删除");
                                                                            QFile file4(dir3.path()+ "/" + fileInfo1.fileName());
                                                                            file4.remove();
                                                                        }
                                                                    }

                                                                }
                                                            }
                                                            else{
                                                                LOG_EVT_INFO("非组合上传离线文件失败! 请再次上传");
                                                                /*if(json_doc["msg"] == "订单已提交，请勿重复提交"){
                                                                    if(sale_list_unique == saleListUnique){
                                                                        LOG_EVT_INFO("重复订单号匹配成功");
                                                                        LOG_EVT_INFO("订单号重复，删除重复订单文件:{}",fileInfo.fileName().toStdString());
                                                                        file3.remove();
                                                                    }
                                                                    else{
                                                                        LOG_EVT_INFO("重复订单号匹配失败");
                                                                        //文件夹筛选是否存该订单进行操作
                                                                        foreach(QFileInfo fileInfo2, fileInfoList)
                                                                        {
                                                                            if(fileInfo2.fileName() != sale_list_unique) {
                                                                                continue;
                                                                            } else {
                                                                                LOG_EVT_INFO("重复订单匹配失败，从文件夹中找到未删除的该订单并进行删除");
                                                                                QFile file4(dir3.path()+ "/" + fileInfo2.fileName());
                                                                                file4.remove();
                                                                            }
                                                                        }
                                                                    }
                                                                }*/
                                                            }
                                                        }
                                                        break;
                                                    }
                                                case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                                                case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                                    {
                                                        break;
                                                    }
                                                }
                                                http_client->deleteLater();
                                            });

                    }
                }

            }
            //订单上传结束
            file.close();
        }
    }
    emit sigUnuploadRecCount();
    return getUnuploadRecCount();
}
void PayMethodControl::uploadNotUploadOrder_v2_CallBack(CallbackPaymentStatus callBackStatus)
{
    LOG_EVT_INFO("==进入订单上传接口==");
    scoped_lock<mutex> lock(upload_order_mutex_v2_);

    auto shop_control   = ControlManager::getInstance()->getShopControl();
    auto shop_cart_list = ControlManager::getInstance()->getShopCartList();
    auto order_mgr      = DataManager::getInstance()->getOrderDataMgr();

    auto not_upload_orders = order_mgr->getNotUploadOrders();
    LOG_EVT_INFO("==not_upload_orders============：{}==",not_upload_orders.size());
    for (auto &cur_not_upload_order : not_upload_orders)
    {
        double sale_list_total             = .0; // 订单总价格
        double sale_list_actually_received = .0; // 实收总价格
        auto   sale_list_unique_num        = cur_not_upload_order.sale_list_unique;
        Json   post_json;
        post_json["payType"]        = 1;
        post_json["shopUnique"]     = shop_control->getShopUnique();         // 店铺编号
        post_json["saleListUnique"] = cur_not_upload_order.sale_list_unique; // 订单号
        post_json["machineNum"]     = 1;                                     // 默认1
        // 收银方式：1-现金 2-支付宝 3-微信 4-银行卡  5-储值卡 6-美团外卖7-饿了么外卖 8-混合支付
        // 9-免密支付 10-积分兑换 11-百货豆 12-拉卡拉 13-易通付款码支付 14-合利宝刷卡
        post_json["saleListPayment"] = cur_not_upload_order.sale_list_payment;

        post_json["type"]            = 2;                                      // 默认1 设备时间 2 使用系统时间
        post_json["saleListCashier"] = cur_not_upload_order.sale_list_cashier; // 收银员ID

        // 付款状态-1货到付款未付款，2网上订单未付款，3已付款 ,4赊账 ，5申请退款 ，6同意退款 7拒绝退款 8自助收银未付款
        post_json["saleListState"] = cur_not_upload_order.sale_list_state;
        post_json["machineTime"]   = cur_not_upload_order.sale_list_datetime.toStdString(); // 上传时间

        Json goodsList_json;
        LOG_EVT_INFO("==cur_not_upload_order.order_detail_infos：{}==",cur_not_upload_order.order_detail_infos.size());
        for (auto &order_detail_info : cur_not_upload_order.order_detail_infos)
        {
            Json tmp_json;
            tmp_json["goodsId"]        = std::to_string(order_detail_info.goods_id);
            tmp_json["barcode"]        = order_detail_info.goods_barcode.toStdString();
            tmp_json["name"]           = order_detail_info.goods_name.toStdString();
            tmp_json["price_in"]       = order_detail_info.goods_purprice; // goods_old_price;
            tmp_json["price_original"] = order_detail_info.sale_list_detail_price;
            tmp_json["sold_num"]       = order_detail_info.sale_list_detail_count;
            tmp_json["subtotal"]       = order_detail_info.sale_list_detail_subtotal;

            goodsList_json.push_back(tmp_json);

            sale_list_total += order_detail_info.sale_list_detail_count * order_detail_info.goods_old_price;
        }
        sale_list_actually_received = cur_not_upload_order.sale_list_actually_received;

        post_json["goodsList"]                = goodsList_json;                                 // 订单详情
        post_json["saleListTotal"]            = shop_cart_list->getTotalGoodsPrice(); // 订单总金额
        post_json["saleListActuallyReceived"] = sale_list_actually_received;                    // 实收金额

        post_json["pointsRatio"] = cur_not_upload_order.local_points_ratio;                // 积分比例
        post_json["memberCard"]  = cur_not_upload_order.local_member_unique.toStdString(); // 会员卡号
        //post_json["storedCard"]  = cur_not_upload_order.local_member_unique.toStdString(); // 储值卡号或手机号

        if (cur_not_upload_order.order_pay_method_vec.size())
        {
            Json pay_detail_array;
            for (auto &order_pay_method : cur_not_upload_order.order_pay_method_vec)
            {
                Json pay_detail;
                pay_detail["pay_method"] = order_pay_method.pay_method;
                pay_detail["pay_money"]  = order_pay_method.pay_price;
                pay_detail_array.push_back(pay_detail);
            }
            post_json["saleListPayDetail"] = pay_detail_array.dump();
        }
        saleList_actually_received_Temp = sale_list_actually_received;
        LOG_EVT_INFO("===uploadNotUploadOrder_v2_CallBack设置saleList_actually_received_Temp为:{}",saleList_actually_received_Temp);

        LOG_EVT_INFO("==进入订单上传接口 判断网络前==");
        //若网络处于离线状态
        if(net_status_global != "101")
        {
           post_json["type"] = 1;
           post_json["machineTime"]   = getCurrentTimeFormatted().toStdString(); //更新时间为当前时间
           if(insertSaleListData(QString::fromStdString(post_json.dump()),QString::number(cur_not_upload_order.sale_list_unique))){
               order_mgr->getOrderById(cur_not_upload_order.sale_list_unique).sale_list_same_type = 1;
               callBackStatus(1);
           }
        }
        else{
            LOG_EVT_INFO("==网络连接成功 开启实时订单上传2==");
//            QString       url         = req_host_prefix + "harricane/payOnline/v2/cashierPay.do";
            QString       url         = "";
            if(shop_control->getShopType() =="6" ||shop_control->getShopType() =="7"){
//                url         = req_host_prefix + "harricane/payOnline/cashierPay_ny.do";
                url         = req_host_prefix + "harricane/payOnline/v2/cashierPay.do";
            }else{
                url         = req_host_prefix + "harricane/payOnline/v2/cashierPay.do";
            }
            HttpClient *  http_client = new HttpClient();
            HttpRequest &&request     = http_client->post(url);

            request.bodyWithJson(post_json.dump());
            execAndCallback4Cpp(request,
                                [=](HttpHandleType http_handle_type, std::string data) mutable
                                {
                                    switch (http_handle_type)
                                    {
                                    case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                        {
                                            auto json_doc = Json::parse(data, nullptr, false);
                                            if (json_doc.is_discarded())
                                            {
                                                LOG_NET_ERROR("数据解析错误");
                                            }
                                            else
                                            {
                                                if (json_doc["status"] == 1)
                                                {
                                                    order_mgr->getOrderById(sale_list_unique_num).sale_list_same_type = 1;
                                                    LOG_EVT_INFO( "{} sale_list_same_type = 1", sale_list_unique_num);
                                                    callBackStatus(1);
                                                }
                                                else if(json_doc["status"] == 2){
                                                    order_mgr->getOrderById(sale_list_unique_num).sale_list_same_type = 1; //订单提交失败，更新订单号
                                                    LOG_EVT_INFO( "实时订单返回数据status为2，单个商品售价超过上限");
                                                    callBackStatus(2);
                                                }
                                                else{
                                                    order_mgr->getOrderById(sale_list_unique_num).sale_list_same_type = 1; //订单提交失败，更新订单号
                                                    LOG_EVT_INFO( "实时订单返回数据status不为1、2，提交失败");
                                                    callBackStatus(3);
                                                }
                                            }
                                            break;
                                        }
                                    case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                                    case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                        {
                                            break;
                                        }
                                    }
                                    http_client->deleteLater();
                                });
//            uploadNotUploadOrder_offlineOrder();关闭该入口
        }
    }
}
void PayMethodControl::uploadOrder_v2(ShopCartList *shop_cart_list, AeaQt::HttpClient *http_client, CppCallbackStd callback_std)
{
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "进入现金支付接口");
    scoped_lock<mutex> lock(upload_order_mutex_v2_);

    const auto shop_control = ControlManager::getInstance()->getShopControl();
    auto       order_mgr    = DataManager::getInstance()->getOrderDataMgr();

    OrderInfo cur_not_upload_order = shop_cart_list->generateOrderInfoByShopCart(shop_cart_list->shop_cart_list_data_.payment);

    double sale_list_total             = .0; // 订单总价格
    double sale_list_actually_received = .0; // 实收总价格
    auto   sale_list_unique_num        = cur_not_upload_order.sale_list_unique;
    Json   post_json;
    post_json["payType"]        = 1;                                     //线下支付
    post_json["shopUnique"]     = shop_control->getShopUnique();         // 店铺编号
    post_json["saleListUnique"] = cur_not_upload_order.sale_list_unique; // 订单号
    post_json["machineNum"]     = 1;                                     // 默认1

    // 收银方式：1-现金 2-支付宝 3-微信 4-银行卡  5-储值卡 6-美团外卖7-饿了么外卖 8-混合支付
    // 9-免密支付 10-积分兑换 11-百货豆 12-拉卡拉 13-易通付款码支付 14-合利宝刷卡
    post_json["saleListPayment"] = cur_not_upload_order.sale_list_payment;

    post_json["type"]            = 2;                                      // 默认1 设备时间 2 使用系统时间
    post_json["saleListCashier"] = cur_not_upload_order.sale_list_cashier; // 收银员ID

    // 付款状态-1货到付款未付款，2网上订单未付款，3已付款 ,4赊账 ，5申请退款 ，6同意退款 7拒绝退款 8自助收银未付款
    post_json["saleListState"] = cur_not_upload_order.sale_list_state;
    post_json["machineTime"]   = cur_not_upload_order.sale_list_datetime.toStdString(); // 上传时间

    Json goodsList_json;
    for (auto &order_detail_info : cur_not_upload_order.order_detail_infos)
    {
        Json tmp_json;
        tmp_json["goodsId"]        = std::to_string(order_detail_info.goods_id);
        tmp_json["barcode"]        = order_detail_info.goods_barcode.toStdString();
        tmp_json["name"]           = order_detail_info.goods_name.toStdString();
        tmp_json["price_in"]       = order_detail_info.goods_purprice; // goods_old_price;
        tmp_json["price_original"] = order_detail_info.sale_list_detail_price;
        tmp_json["sold_num"]       = order_detail_info.sale_list_detail_count;
        tmp_json["subtotal"]       = order_detail_info.sale_list_detail_subtotal;

        goodsList_json.push_back(tmp_json);

        sale_list_total += order_detail_info.sale_list_detail_count * order_detail_info.goods_old_price;
    }
    sale_list_actually_received = cur_not_upload_order.sale_list_actually_received;

    post_json["goodsList"]                = goodsList_json;                                 // 订单详情
    post_json["saleListTotal"]            = shop_cart_list->getTotalGoodsPrice(); // 订单总金额
    post_json["saleListActuallyReceived"] = sale_list_actually_received;                    // 实收金额

    post_json["pointsRatio"] = cur_not_upload_order.local_points_ratio;                // 积分比例
    post_json["memberCard"]  = cur_not_upload_order.local_member_unique.toStdString(); // 会员卡号
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "实际收取金额:{}",QString::number(sale_list_actually_received).toStdString());
    if (!cur_not_upload_order.order_pay_method_vec.empty())
    {
        Json pay_detail_array;
        for (auto &order_pay_method : cur_not_upload_order.order_pay_method_vec)
        {
            Json pay_detail;
            pay_detail["pay_method"] = order_pay_method.pay_method;
            pay_detail["pay_money"]  = order_pay_method.pay_price;
            pay_detail_array.push_back(pay_detail);
        }
        post_json["saleListPayDetail"] = pay_detail_array.dump();
    }
    if(shop_control->getShopType() =="6" ||shop_control->getShopType() =="7"){
        const QString url     = req_host_prefix + "payOnline/v2/cashierPay_ny.do";
        LOG_EVT_INFO("宁宇会员收款");
        HttpRequest &&request = http_client->post(url);
        LOG_EVT_INFO("POST 请求的 JSON 数据: " + post_json.dump());
        request.bodyWithJson(post_json.dump());
        request.timeout(3);
        saleList_actually_received_Temp = sale_list_actually_received;
        LOG_EVT_INFO("===uploadOrder_v2设置saleList_actually_received_Temp为:{}",saleList_actually_received_Temp);
        execAndCallback4Cpp(request, std::move(callback_std));
    }else{
        const QString url     = req_host_prefix + "harricane/payOnline/v2/cashierPay.do";
        LOG_EVT_INFO("非宁宇会员收款");
        HttpRequest &&request = http_client->post(url);
        request.bodyWithJson(post_json.dump());
        request.timeout(3);
        saleList_actually_received_Temp = sale_list_actually_received;
        LOG_EVT_INFO("===uploadOrder_v2设置saleList_actually_received_Temp为:{}",saleList_actually_received_Temp);
        execAndCallback4Cpp(request, std::move(callback_std));
    }
}

void PayMethodControl::onlinePay4Qml(QJSValue callback, QString payment_code, PayMethodEnum pay_method_enum)
{
    ShopControl *shop_control        = ControlManager::getInstance()->getShopControl();
    auto        *shop_shop_cart_list = ControlManager::getInstance()->getShopCartList();
    auto         goods_mgr           = DataManager::getInstance()->getGoodsMgr();

    QString shop_unique = QString::number(shop_control->getShopUnique());

    double   saleList_total_price       = shop_shop_cart_list->getTotalGoodsPrice(); // 订单总价格
    double   saleList_actually_received = shop_shop_cart_list->getTotalGoodsPrice();           // 实收总价格
    QVariant change_amount              = QString::number(saleList_total_price - saleList_actually_received, 'f', 2);

    LOG_EVT_INFO("[onlinePay4Qml]change_amount {}", change_amount.toString().toStdString());

    auto sale_list_unique = shop_shop_cart_list->shop_cart_list_data_.order_unique_;
    Json post_json;
    post_json["authCode"]        = payment_code.toStdString();               // 支付码
    post_json["payType"]         = 2;                                        // 店铺编号
    post_json["shopUnique"]      = shop_control->getShopUnique();            // 店铺编号
    post_json["saleListUnique"]  = sale_list_unique.toStdString();           // 订单号
    post_json["machineNum"]      = 1;                                        // 默认1
    post_json["saleListPayment"] = 13;                                       // 收银方式：1 现金  2 支付宝  3 微信支付  4 银行卡  5 储值卡
    post_json["type"]            = 2;                                        // 默认1 设备时间 2 使用系统时间
    post_json["saleListCashier"] = shop_control->getPersonInfo().cashier_id; // 收银员ID
    // 付款状态-1货到付款未付款，2网上订单未付款，3已付款 ,4赊账 ，5申请退款 ，6同意退款 7拒绝退款 8自助收银未付款
    post_json["saleListState"] = 3;
    post_json["machineTime"] =
        (shop_shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_shop_cart_list->shop_cart_list_data_.order_time_).toStdString(); // 上传时间

    Json goodsList_json;
    for (auto &shop_cart_item : shop_shop_cart_list->shop_cart_Items_)
    {
        GoodsInfo goods_info;
        goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);
        Json tmp_json;
        tmp_json["goodsId"] = std::to_string(goods_info.goods_id);
        tmp_json["barcode"] = goods_info.goods_barcode.toStdString();
        tmp_json["name"]    = goods_info.goods_name.toStdString();
        if (shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_GOODS) ||
            shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS))
        {
            tmp_json["price_in"] = shop_cart_item.getPrice() * (1.0 - ConfigTool::getInstance()->getSetting(ConfigEnum::NOCODE_GOODS_PROFIT_RATIO).toFloat());
            tmp_json["price_original"] = shop_cart_item.getPrice();
        }
        else
        {
            tmp_json["price_in"]       = goods_info.goods_in_price;
            tmp_json["price_original"] = shop_cart_item.getPrice();
        }
        tmp_json["sold_num"] = shop_cart_item.goods_num_;
        tmp_json["subtotal"] = shop_cart_item.getSubtotal();

        goodsList_json.push_back(tmp_json);
    }

    post_json["goodsList"]                = goodsList_json;                                                        // 订单详情
    post_json["saleListTotal"]            = QString::number(saleList_total_price * 100, 'f', 0).toUInt();          // 订单总金额
    post_json["saleListActuallyReceived"] = QString::number(saleList_actually_received * 100, 'f', 0).toUInt();    // 实收金额
    post_json["pointsRatio"]              = ConfigTool::getInstance()->pointsRatio();                              // 积分比例
    post_json["memberCard"]               = shop_shop_cart_list->shop_cart_list_data_.member_unique.toStdString(); // 会员卡号

    QString url        = req_host_prefix + "harricane/payOnline/v2/cashierPay.do";
    auto    cur_thread = new QThread(this); // 工作线程
    ////////////////////
    HttpClient *http_client = new HttpClient();
    auto      &&request     = http_client->post(url);
    request.bodyWithJson(post_json.dump());
    saleList_actually_received_Temp = saleList_total_price;
    LOG_EVT_INFO("===onlinePay4Qml设置saleList_actually_received_Temp为:{}",saleList_actually_received_Temp);
    LOG_NET_INFO("请求在线支付接口1");
    execAndCallback4Cpp(
        request,
        [=](HttpHandleType http_handle_type, std::string data) mutable
        {
            switch (http_handle_type)
            {
            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                {
                    auto json_doc = Json::parse(data, nullptr, false);
                    if (json_doc.is_discarded())
                    {
                        LOG_NET_ERROR("数据解析错误");
                    }
                    else
                    {
                        QVariant var_tmp;
                        tryFromJson(json_doc, "status", var_tmp);
                        auto status = var_tmp.toInt();

                        // 返回结果状态码 1：成功
                        if (status == 1)
                        {
                            ShopCartList *shop_cart_list                 = ControlManager::getInstance()->getShopCartList();
                            shop_cart_list->shop_cart_list_data_.payment = 13;


                            if (json_doc.contains("cusData"))
                            {
                                ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list, -1, false, json_doc["cusData"].dump());
                            }
                            else
                            {
                                ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);
                            }

                            payFinishWizard();

                            if (callback.isCallable())
                            {
                                // 回调qml中的函数
                                Json json_tmp = {{"receivable_amount", saleList_total_price},
                                                 {"received_amount", saleList_actually_received},
                                                 {"change_amount", change_amount},
                                                 {"sn", __LINE__}};
                                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "回调qml中的函数 {}", json_tmp.dump());
                                auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS, pay_method_enum, json_tmp.dump());
                                auto ret      = callback.call(arg_list);
                            }
                            if (ConfigTool::getInstance()->isPlayOnlineTts())
                                ControlManager::getInstance()->getTtsControl()->say(tr("在线支付成功") + QString::number(saleList_actually_received) + tr("元"));
                        }
                        else if (status == 0)
                        {
                            // 如果顾客需要手动输入密码支付
                            if (!json_doc.contains("data") || json_doc["data"].is_null())
                            {
                                if (json_doc.contains("msg"))
                                {
                                    Utils4Qml::getInstance()->sendToast(QString::fromStdString(json_doc["msg"]));
                                }
                                return;
                            }
                            startCheckOnlinePay(callback, sale_list_unique, pay_method_enum);
                            if (ConfigTool::getInstance()->isPlayOnlineTts())
                                ControlManager::getInstance()->getTtsControl()->say(tr("请输入支付密码"));
                        }
                    }
                    break;
                }
            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                {
                    // 网络错误
                    if (callback.isCallable())
                    {
                        auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR, pay_method_enum, data);
                        auto ret      = callback.call(arg_list);
                    }
                    break;
                }
            }
            http_client->deleteLater();
        });

    if (callback.isCallable())
    {
        Json json_tmp = {
            {"receivable_amount", saleList_total_price}, {"received_amount", saleList_actually_received}, {"change_amount", change_amount}, {"sn", __LINE__}};
        auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING, pay_method_enum, json_tmp.dump());

        LOG_EVT_INFO("主线程PAY_STATUS__PAYING {}", json_tmp.dump());

        callback.call(arg_list);
    }
}

void PayMethodControl::checkOnlinePay()
{
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "支付测试 --- CPP 支付检查 begin");

    QString url            = req_host_prefix + "harricane/payOnline/queryOrderYT.do";
    auto    shopUnique     = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());
    auto    shop_cart_list = ControlManager::getInstance()->getShopCartList();

    QString saleListActuallyReceived_yuan = QString::number(shop_cart_list->getFinalTotalGoodsPrice());

    QVariantMap body_map;
    body_map.insert("shopUnique", shopUnique);
    body_map.insert("out_trade_no", online_pay_order_);

    HttpClient *http_client = new HttpClient();
    auto      &&request     = http_client->post(url);
    request.bodyWithFormUrlencoded(body_map);

    execAndCallback4Cpp(
        request,
        [=](HttpHandleType http_handle_type, std::string data) mutable
        {
            switch (http_handle_type)
            {
            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "支付测试 --- CPP 支付检查 网络返回");

                    if (is_stop_check_online_pay_)
                        return;

                    auto json_doc = Json::parse(data, nullptr, false);
                    if (json_doc.is_discarded())
                    {
                        LOG_NET_ERROR("数据解析错误");
                    }
                    else
                    {
                        auto *shop_cart_list = ControlManager::getInstance()->getShopCartList();
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据成功");

                        if (is_stop_check_online_pay_)
                        {
                            SPDLOG_LOGGER_WARN(LogMgr::getInst()->logger_net_, "手动停止了易通支付");
                        }

                        if (json_doc.contains("data") && !json_doc["data"].contains("trade_state"))
                        {
                            // 订单支付中
                            if (online_pay_callback_.isCallable())
                            {
                                Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                 {"received_amount", saleListActuallyReceived_yuan.toStdString()},
                                                 {"change_amount", 0},
                                                 {"sn", __LINE__}};
                                auto arg_list =
                                    generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING, pay_method_enum_online_, json_tmp.dump());
                                online_pay_callback_.call(arg_list);
                            }
                        }
                        else if (!json_doc.contains("data") || !json_doc["data"].contains("trade_state"))
                        {
                            // 支付出现问题
                            if (online_pay_callback_.isCallable())
                            {
                                Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                 {"received_amount", saleListActuallyReceived_yuan.toStdString()},
                                                 {"change_amount", 0},
                                                 {"sn", __LINE__}};
                                auto arg_list =
                                    generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR, pay_method_enum_online_, json_tmp.dump());
                                online_pay_callback_.call(arg_list);
                            }
                            stopCheckOnlinePay();
                            // timer_online_pay_.stop();
                            if (ConfigTool::getInstance()->isPlayOnlineTts())
                                ControlManager::getInstance()->getTtsControl()->say(tr("支付错误"));
                            shop_cart_list->resetOrderUnique();
                            return;
                        }
                        else if (json_doc.contains("data") && json_doc["data"].contains("trade_state"))
                        {
                            string trade_state_str = json_doc["data"]["trade_state"];
                            if (trade_state_str == "USERPAYING")
                            {
                                // 订单支付中
                                if (online_pay_callback_.isCallable())
                                {
                                    Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                     {"received_amount", saleListActuallyReceived_yuan.toStdString()},
                                                     {"change_amount", 0},
                                                     {"sn", __LINE__}};
                                    auto arg_list =
                                        generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING, pay_method_enum_online_, json_tmp.dump());
                                    online_pay_callback_.call(arg_list);
                                }
                            }
                            else if (trade_state_str == "NOTPAY")
                            {
                                // 订单未支付
                                if (online_pay_callback_.isCallable())
                                {
                                    Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                     {"received_amount", saleListActuallyReceived_yuan.toStdString()},
                                                     {"change_amount", 0},
                                                     {"sn", __LINE__}};
                                    auto arg_list =
                                        generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__CANCEL, pay_method_enum_online_, json_tmp.dump());
                                    online_pay_callback_.call(arg_list);
                                }
                                timer_online_pay_.stop();

                                shop_cart_list->resetOrderUnique();
                                if (ConfigTool::getInstance()->isPlayOnlineTts())
                                    ControlManager::getInstance()->getTtsControl()->say(tr("用户取消在线支付"));
                            }
                            else if (trade_state_str == "SUCCESS")
                            {
                                // 订单已支付
                                timer_online_pay_.stop();

                                ShopCartList *shop_cart_list                 = ControlManager::getInstance()->getShopCartList();
                                shop_cart_list->shop_cart_list_data_.payment = (int)pay_method_enum_online_;

                                if (json_doc.contains("cusData"))
                                {
                                    ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list, -1, false, json_doc["cusData"].dump());
                                }
                                else
                                {
                                    ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);
                                }


                                payFinishWizard();

                                if (online_pay_callback_.isCallable())
                                {
                                    Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                     {"received_amount", saleListActuallyReceived_yuan.toStdString()},
                                                     {"change_amount", 0},
                                                     {"sn", __LINE__}};
                                    auto arg_list =
                                        generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS, pay_method_enum_online_, json_tmp.dump());
                                    online_pay_callback_.call(arg_list);

                                    if (ConfigTool::getInstance()->isPlayOnlineTts())
                                        ControlManager::getInstance()->getTtsControl()->say(tr("在线支付成功") + saleListActuallyReceived_yuan + tr("元"));
                                }
                            }
                            else if (trade_state_str == "FAIL")
                            {
                                ShopCartList *shop_cart_list                 = ControlManager::getInstance()->getShopCartList();
                                shop_cart_list->shop_cart_list_data_.payment = (int)pay_method_enum_online_;

                                timer_online_pay_.stop();
                                shop_cart_list->resetOrderUnique();
                                if (online_pay_callback_.isCallable())
                                {
                                    Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                     {"received_amount", saleListActuallyReceived_yuan.toStdString()},
                                                     {"change_amount", 0},
                                                     {"sn", __LINE__}};
                                    auto arg_list =
                                        generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR, pay_method_enum_online_, json_tmp.dump());
                                    online_pay_callback_.call(arg_list);

                                    if (ConfigTool::getInstance()->isPlayOnlineTts())
                                        ControlManager::getInstance()->getTtsControl()->say(tr("在线支付错误"));
                                }
                            }
                        }
                    }
                    break;
                }
            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                {
                    // 网络错误
                    if (online_pay_callback_.isCallable())
                    {
                        auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__TIME_OUT, pay_method_enum_online_, QString(""));
                        online_pay_callback_.call(arg_list);
                    }
                    break;
                }
            }
            http_client->deleteLater();
        });
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "支付测试 --- CPP 支付检查 网络请求");

    ++timer_online_pay_index_;
    if (timer_online_pay_index_ > (60 * 3))
    {
        stopCheckOnlinePay();
        if (online_pay_callback_.isCallable())
        {
            Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                             {"received_amount", saleListActuallyReceived_yuan},
                             {"change_amount", 0},
                             {"sn", __LINE__}};
            auto arg_list =
                generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__TIME_OUT, EnumTool::PayMethodEnum::PAY_METHOD__YI_TONG, json_tmp.dump());
            online_pay_callback_.call(arg_list);
        }
    }
}

void PayMethodControl::startCheckOnlinePay(QJSValue callback, QString sale_list_unique, PayMethodEnum pay_method_enum)
{
    pay_method_enum_online_   = pay_method_enum;
    is_stop_check_online_pay_ = false;
    online_pay_callback_      = callback;
    online_pay_order_         = sale_list_unique;
    timer_online_pay_.disconnect();
    connect(&timer_online_pay_, &QTimer::timeout, this, &PayMethodControl::checkOnlinePay);
    timer_online_pay_.start();
}

void PayMethodControl::stopCheckOnlinePay()
{
    timer_online_pay_index_ = 0;
    timer_online_pay_.stop();
    ShopCartList *shop_cart_list = ControlManager::getInstance()->getShopCartList();
    shop_cart_list->resetOrderUnique();
    is_stop_check_online_pay_ = true;
}
void PayMethodControl::combinedOnlineVipPay(QJSValue callback, QVariant payment_code, QVariant online_pay, QVariant cash_pay,QVariant total_pay)
{
    QString url            = req_host_prefix + "harricane/payOnline/yiTongPaySale.do";
    auto    shop_cart_list = ControlManager::getInstance()->getShopCartList();
    auto    shopUnique     = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());

    QVariantMap body_map;
    auto        combined_online_pay_order        = shop_cart_list->getOrderUnique();
    shop_cart_list->shop_cart_list_data_.payment = 8;
    body_map.insert("sale_list_unique", combined_online_pay_order);
    body_map.insert("shop_unique", shopUnique);
    body_map.insert("money", online_pay.toString());
    body_map.insert("auth_code", payment_code.toString());
    auto body_data = HttpWorker::urlEncode(body_map);

    combined_online_pay_cash_   = cash_pay.toDouble();
    combined_online_pay_online_ = online_pay.toDouble();
    combined_online_pay_total_ = total_pay.toDouble();
    if (callback.isCallable())
    {
        auto pay_money = online_pay.toDouble() + cash_pay.toDouble();

        Json json_tmp = {
            {"receivable_amount", shop_cart_list->getTotalGoodsPrice()}, {"received_amount", pay_money}, {"change_amount", 0}, {"sn", __LINE__}};
        auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING, EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
        callback.call(arg_list);
    }

    HttpClient *http_client = new HttpClient();
    auto      &&request     = http_client->post(url);
    request.bodyWithFormUrlencoded(body_map);

    saleList_actually_received_Temp = combined_online_pay_online_;

    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    auto json_doc = Json::parse(data, nullptr, false);
                                    if (json_doc.is_discarded())
                                    {
                                        LOG_NET_ERROR("数据解析错误");
                                    }
                                    else
                                    {
                                        auto status = getDataFromJson(json_doc, "status").toInt();

                                        if (status == 1)
                                        {
                                            shop_cart_list->shop_cart_list_data_.cash_received_ = saleList_actually_received_Temp;
                                            auto order_ctrl                = ControlManager::getInstance()->getOrderControl();
                                            auto combined_online_pay_order = generateCombinedOnlineVipPayOrder(online_pay.toDouble(), cash_pay.toDouble(),total_pay.toDouble());
                                            order_ctrl->addOrder(combined_online_pay_order);
                                            uploadNotUploadOrder_v2();
                                            //ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);
                                            ControlManager::getInstance()->getPrinterControl()->printOrderThreadCombine(shop_cart_list,1,false,"",combined_online_pay_cash_,combined_online_pay_online_,total_pay.toDouble());
                                            if (callback.isCallable())
                                            {
                                                auto pay_money = combined_online_pay_cash_ + combined_online_pay_online_;

                                                Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                 {"received_amount", pay_money},
                                                                 {"change_amount", 0},
                                                                 {"sn", __LINE__}};

                                                auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                                           EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                callback.call(arg_list);
                                                if (ConfigTool::getInstance()->isPlayMemberTts()){
                                                    if((cash_pay.toDouble() == 0.00) && (online_pay.toDouble() != 0.00)){
                                                         ControlManager::getInstance()->getTtsControl()->say(tr("储值卡收款成功") + QString::number(online_pay.toDouble()) + tr("元"));
                                                    }else{
                                                        ControlManager::getInstance()->getTtsControl()->say(tr("组合支付成功"));
                                                    }

                                                }
                                            }
                                            payFinishWizard(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS, EnumTool::PayMethodEnum::PAY_METHOD__COMBINED);
                                        }
                                        else
                                        {
                                            if (callback.isCallable())
                                            {
                                                auto pay_money = online_pay.toDouble() + cash_pay.toDouble();

                                                Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                 {"received_amount", pay_money},
                                                                 {"change_amount", 0},
                                                                 {"sn", __LINE__}};

                                                auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING,
                                                                                           EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                callback.call(arg_list);
                                            }
                                            auto combined_online_pay_order_OrderInfo = generateCombinedOnlineVipPayOrder(online_pay.toDouble(), cash_pay.toDouble(),total_pay.toDouble());
                                            combined_online_pay_order_orderInfo = combined_online_pay_order_OrderInfo;
                                            //startCheckCombinedOnlinePay(callback, combined_online_pay_order);
                                            startCheckCombinedOnlineVipPay(callback, combined_online_pay_order);
                                        }
                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    // 网络错误
                                    if (callback.isCallable())
                                    {
                                        auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR,
                                                                                   EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, QString(""));
                                        callback.call(arg_list);
                                        if (ConfigTool::getInstance()->isPlayMemberTts()){
                                            ControlManager::getInstance()->getTtsControl()->say(tr("组合支付未知错误"));
                                        }
                                    }
                                    break;
                                }
                            }
                            http_client->deleteLater();
                        });
}
void PayMethodControl::combinedOnlinePay(QJSValue callback, QVariant payment_code, QVariant online_pay, QVariant cash_pay)
{
    QString url            = req_host_prefix + "harricane/payOnline/yiTongPaySale.do";
    auto    shop_cart_list = ControlManager::getInstance()->getShopCartList();
    auto    shopUnique     = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());

    QVariantMap body_map;
    auto        combined_online_pay_order        = shop_cart_list->getOrderUnique();
    shop_cart_list->shop_cart_list_data_.payment = 8;
    body_map.insert("sale_list_unique", combined_online_pay_order);
    body_map.insert("shop_unique", shopUnique);
    body_map.insert("money", online_pay.toString());
    body_map.insert("auth_code", payment_code.toString());
    auto body_data = HttpWorker::urlEncode(body_map);

    combined_online_pay_cash_   = cash_pay.toDouble();
    combined_online_pay_online_ = online_pay.toDouble();
    if (callback.isCallable())
    {
        auto pay_money = online_pay.toDouble() + cash_pay.toDouble();

        Json json_tmp = {
            {"receivable_amount", shop_cart_list->getTotalGoodsPrice()}, {"received_amount", pay_money}, {"change_amount", 0}, {"sn", __LINE__}};
        auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING, EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
        callback.call(arg_list);
    }

    HttpClient *http_client = new HttpClient();
    auto      &&request     = http_client->post(url);
    request.bodyWithFormUrlencoded(body_map);

    saleList_actually_received_Temp = combined_online_pay_online_;

    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    auto json_doc = Json::parse(data, nullptr, false);
                                    if (json_doc.is_discarded())
                                    {
                                        LOG_NET_ERROR("数据解析错误");
                                    }
                                    else
                                    {
                                        auto status = getDataFromJson(json_doc, "status").toInt();

                                        if (status == 1)
                                        {
                                            shop_cart_list->shop_cart_list_data_.cash_received_ = saleList_actually_received_Temp;
                                            auto order_ctrl                = ControlManager::getInstance()->getOrderControl();
                                            auto combined_online_pay_order = generateCombinedOnlinePayOrder(online_pay.toDouble(), cash_pay.toDouble());
                                            order_ctrl->addOrder(combined_online_pay_order);
                                            uploadNotUploadOrder_v2();
                                            ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);
                                            if (callback.isCallable())
                                            {
                                                auto pay_money = combined_online_pay_cash_ + combined_online_pay_online_;

                                                Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                 {"received_amount", pay_money},
                                                                 {"change_amount", 0},
                                                                 {"sn", __LINE__}};

                                                auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                                           EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                callback.call(arg_list);
                                                if (ConfigTool::getInstance()->isPlayMemberTts()){
                                                    if((cash_pay.toDouble() == 0.00) && (online_pay.toDouble() != 0.00)){
                                                         ControlManager::getInstance()->getTtsControl()->say(tr("储值卡收款成功") + QString::number(online_pay.toDouble()) + tr("元"));
                                                    }else{
                                                        ControlManager::getInstance()->getTtsControl()->say(tr("组合支付成功"));
                                                    }

                                                }
                                            }
                                            payFinishWizard(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS, EnumTool::PayMethodEnum::PAY_METHOD__COMBINED);
                                        }
                                        else
                                        {
                                            if (callback.isCallable())
                                            {
                                                auto pay_money = online_pay.toDouble() + cash_pay.toDouble();

                                                Json json_tmp = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                 {"received_amount", pay_money},
                                                                 {"change_amount", 0},
                                                                 {"sn", __LINE__}};

                                                auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING,
                                                                                           EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                callback.call(arg_list);
                                            }
                                            auto combined_online_pay_order_OrderInfo = generateCombinedOnlinePayOrder(online_pay.toDouble(), cash_pay.toDouble());
                                            combined_online_pay_order_orderInfo = combined_online_pay_order_OrderInfo;
                                            startCheckCombinedOnlinePay(callback, combined_online_pay_order);
                                        }
                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    // 网络错误
                                    if (callback.isCallable())
                                    {
                                        auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR,
                                                                                   EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, QString(""));
                                        callback.call(arg_list);
                                        if (ConfigTool::getInstance()->isPlayMemberTts()){
                                            ControlManager::getInstance()->getTtsControl()->say(tr("组合支付未知错误"));
                                        }
                                    }
                                    break;
                                }
                            }
                            http_client->deleteLater();
                        });
}
void PayMethodControl::checkCombinedOnlineVipPay()
{
    QString url        = req_host_prefix + "harricane/payOnline/yiTongPaySaleStatus.do";

    auto shop_cart_list = ControlManager::getInstance()->getShopCartList();

    QVariantMap body_map;
    body_map.insert("sale_list_unique", combined_online_pay_order_);

    auto callback4Error = [this]()
    {
        if (combined_online_pay_callback_.isCallable())
        {
            auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR, EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, QString(""));
            combined_online_pay_callback_.call(arg_list);
            stopCheckCombinedOnlineVipPay();
        }
    };

    HttpClient *http_client = new HttpClient();
    auto      &&request     = http_client->post(url);
    request.bodyWithFormUrlencoded(body_map);

    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    if (is_stop_check_combined_online_pay_)
                                        return;

                                    auto json_doc = Json::parse(data, nullptr, false);
                                    if (json_doc.is_discarded())
                                    {
                                        LOG_NET_ERROR("数据解析错误");
                                        callback4Error();
                                    }
                                    else
                                    {
                                        auto status = getDataFromJson(json_doc, "status").toInt();

                                        if (status == 0)
                                        {
                                            auto msg = getDataFromJson(json_doc, "msg").toString();
                                            if(msg == "订单支付中"){
                                                if (combined_online_pay_callback_.isCallable())
                                                {
                                                    auto pay_money = combined_online_pay_cash_ + combined_online_pay_online_;
                                                    Json json_tmp  = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                      {"received_amount", pay_money},
                                                                      {"change_amount", 0},
                                                                      {"sn", __LINE__}};
                                                    LOG_NET_INFO("==订单支付中==");
                                                    auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING,
                                                                                               EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                    combined_online_pay_callback_.call(arg_list);
                                                }
                                            }
                                            if(msg == "不存在"){
                                                if (combined_online_pay_callback_.isCallable())
                                                {
                                                    auto pay_money = combined_online_pay_cash_ + combined_online_pay_online_;
                                                    Json json_tmp  = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                      {"received_amount", pay_money},
                                                                      {"change_amount", 0},
                                                                      {"sn", __LINE__}};
                                                    LOG_NET_INFO("==不存在==");
                                                    auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__UNKNOW,
                                                                                               EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                    combined_online_pay_callback_.call(arg_list);
                                                    timer_combined_online_pay_index_ = 200;
                                                }
                                            }
                                            if(msg == "订单未支付"){
                                                if (combined_online_pay_callback_.isCallable())
                                                {
                                                    auto pay_money = combined_online_pay_cash_ + combined_online_pay_online_;
                                                    Json json_tmp  = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                      {"received_amount", pay_money},
                                                                      {"change_amount", 0},
                                                                      {"sn", __LINE__}};
                                                    LOG_NET_INFO("==订单未支付==");
                                                    auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__CANCEL,
                                                                                               EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                    combined_online_pay_callback_.call(arg_list);
                                                    timer_combined_online_pay_index_ = 200;
                                                }
                                            }
                                        }
                                        else if (status == 1)
                                        {
                                            auto order_ctrl                = ControlManager::getInstance()->getOrderControl();
                                            order_ctrl->addOrder(combined_online_pay_order_orderInfo);
                                            uploadNotUploadOrder_v2();
                                            if (ConfigTool::getInstance()->isPlayMemberTts()){
                                                    ControlManager::getInstance()->getTtsControl()->say(tr("组合支付成功"));
                                            }
                                            ControlManager::getInstance()->getPrinterControl()->printOrderThreadCombine(shop_cart_list,1,false,"",combined_online_pay_cash_,combined_online_pay_online_,combined_online_pay_total_);
                                            //ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);
                                            payFinishWizard(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS, EnumTool::PayMethodEnum::PAY_METHOD__COMBINED);
                                            if (combined_online_pay_callback_.isCallable())
                                            {
                                                auto pay_money = combined_online_pay_cash_ + combined_online_pay_online_;
                                                Json json_tmp  = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                  {"received_amount", pay_money},
                                                                  {"change_amount", 0},
                                                                  {"sn", __LINE__}};

                                                auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                                           EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                combined_online_pay_callback_.call(arg_list);
                                                stopCheckCombinedOnlineVipPay();
                                            }
                                        }
                                        LOG_EVT_INFO("查询结果:{}", json_doc.dump());
                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    callback4Error();
                                    break;
                                }
                            }
                            http_client->deleteLater();
                        });

    ++timer_combined_online_pay_index_;
    if (timer_combined_online_pay_index_ > (60 *2))
    {
        stopCheckCombinedOnlineVipPay();
        if (combined_online_pay_callback_.isCallable())
        {
            QJSValueList arglist;
            arglist.push_back(false);
            combined_online_pay_callback_.call(arglist);
        }
    }
}
void PayMethodControl::checkCombinedOnlinePay()
{
    QString url        = req_host_prefix + "harricane/payOnline/yiTongPaySaleStatus.do";

    auto shop_cart_list = ControlManager::getInstance()->getShopCartList();

    QVariantMap body_map;
    body_map.insert("sale_list_unique", combined_online_pay_order_);

    auto callback4Error = [this]()
    {
        if (combined_online_pay_callback_.isCallable())
        {
            auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__ERROR, EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, QString(""));
            combined_online_pay_callback_.call(arg_list);
            stopCheckCombinedOnlinePay();
        }
    };

    HttpClient *http_client = new HttpClient();
    auto      &&request     = http_client->post(url);
    request.bodyWithFormUrlencoded(body_map);

    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    if (is_stop_check_combined_online_pay_)
                                        return;

                                    auto json_doc = Json::parse(data, nullptr, false);
                                    if (json_doc.is_discarded())
                                    {
                                        LOG_NET_ERROR("数据解析错误");
                                        callback4Error();
                                    }
                                    else
                                    {
                                        auto status = getDataFromJson(json_doc, "status").toInt();

                                        if (status == 0)
                                        {
                                            auto msg = getDataFromJson(json_doc, "msg").toString();
                                            if(msg == "订单支付中"){
                                                if (combined_online_pay_callback_.isCallable())
                                                {
                                                    auto pay_money = combined_online_pay_cash_ + combined_online_pay_online_;
                                                    Json json_tmp  = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                      {"received_amount", pay_money},
                                                                      {"change_amount", 0},
                                                                      {"sn", __LINE__}};
                                                    LOG_NET_INFO("==订单支付中==");
                                                    auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING,
                                                                                               EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                    combined_online_pay_callback_.call(arg_list);
                                                }
                                            }
                                            if(msg == "不存在"){
                                                if (combined_online_pay_callback_.isCallable())
                                                {
                                                    auto pay_money = combined_online_pay_cash_ + combined_online_pay_online_;
                                                    Json json_tmp  = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                      {"received_amount", pay_money},
                                                                      {"change_amount", 0},
                                                                      {"sn", __LINE__}};
                                                    LOG_NET_INFO("==不存在==");
                                                    auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__UNKNOW,
                                                                                               EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                    combined_online_pay_callback_.call(arg_list);
                                                    timer_combined_online_pay_index_ = 200;
                                                }
                                            }
                                            if(msg == "订单未支付"){
                                                if (combined_online_pay_callback_.isCallable())
                                                {
                                                    auto pay_money = combined_online_pay_cash_ + combined_online_pay_online_;
                                                    Json json_tmp  = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                      {"received_amount", pay_money},
                                                                      {"change_amount", 0},
                                                                      {"sn", __LINE__}};
                                                    LOG_NET_INFO("==订单未支付==");
                                                    auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__CANCEL,
                                                                                               EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                    combined_online_pay_callback_.call(arg_list);
                                                    timer_combined_online_pay_index_ = 200;
                                                }
                                            }
                                        }
                                        else if (status == 1)
                                        {
                                            auto order_ctrl                = ControlManager::getInstance()->getOrderControl();
                                            order_ctrl->addOrder(combined_online_pay_order_orderInfo);
                                            uploadNotUploadOrder_v2();
                                            if (ConfigTool::getInstance()->isPlayMemberTts()){
                                                    ControlManager::getInstance()->getTtsControl()->say(tr("组合支付成功"));
                                            }
                                            ControlManager::getInstance()->getPrinterControl()->printOrderThread(shop_cart_list);
                                            payFinishWizard(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS, EnumTool::PayMethodEnum::PAY_METHOD__COMBINED);
                                            if (combined_online_pay_callback_.isCallable())
                                            {
                                                auto pay_money = combined_online_pay_cash_ + combined_online_pay_online_;
                                                Json json_tmp  = {{"receivable_amount", shop_cart_list->getTotalGoodsPrice()},
                                                                  {"received_amount", pay_money},
                                                                  {"change_amount", 0},
                                                                  {"sn", __LINE__}};

                                                auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                                                                                           EnumTool::PayMethodEnum::PAY_METHOD__COMBINED, json_tmp.dump());
                                                combined_online_pay_callback_.call(arg_list);
                                                stopCheckCombinedOnlinePay();
                                            }
                                        }
                                        LOG_EVT_INFO("查询结果:{}", json_doc.dump());
                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    callback4Error();
                                    break;
                                }
                            }
                            http_client->deleteLater();
                        });

    ++timer_combined_online_pay_index_;
    if (timer_combined_online_pay_index_ > (60 *2))
    {
        stopCheckCombinedOnlinePay();
        if (combined_online_pay_callback_.isCallable())
        {
            QJSValueList arglist;
            arglist.push_back(false);
            combined_online_pay_callback_.call(arglist);
        }
    }
}

void PayMethodControl::startCheckCombinedOnlinePay(QJSValue callback, QString sale_list_unique)
{
    is_stop_check_combined_online_pay_ = false;
    combined_online_pay_callback_      = callback;
    combined_online_pay_order_         = sale_list_unique;
    timer_combined_online_pay_.disconnect();
    connect(&timer_combined_online_pay_, &QTimer::timeout, this, &PayMethodControl::checkCombinedOnlinePay);
    timer_combined_online_pay_.start();
}
void PayMethodControl::startCheckCombinedOnlineVipPay(QJSValue callback, QString sale_list_unique)
{
    is_stop_check_combined_online_pay_ = false;
    combined_online_pay_callback_      = callback;
    combined_online_pay_order_         = sale_list_unique;
    timer_combined_online_pay_.disconnect();
    connect(&timer_combined_online_pay_, &QTimer::timeout, this, &PayMethodControl::checkCombinedOnlineVipPay);
    timer_combined_online_pay_.start();
}
void PayMethodControl::stopCheckCombinedOnlineVipPay()
{
    timer_combined_online_pay_index_ = 0;
    timer_combined_online_pay_.stop();
    ShopCartList *shop_cart_list = ControlManager::getInstance()->getShopCartList();
    shop_cart_list->resetOrderUnique();
    is_stop_check_combined_online_pay_ = true;
    combined_online_pay_cash_          = .0;
    combined_online_pay_online_        = .0;
}
void PayMethodControl::stopCheckCombinedOnlinePay()
{
    timer_combined_online_pay_index_ = 0;
    timer_combined_online_pay_.stop();
    ShopCartList *shop_cart_list = ControlManager::getInstance()->getShopCartList();
    shop_cart_list->resetOrderUnique();
    is_stop_check_combined_online_pay_ = true;
    combined_online_pay_cash_          = .0;
    combined_online_pay_online_        = .0;
}

OrderInfo PayMethodControl::generateCombinedOnlinePayOrder(double online_pay, double cash_pay)
{
    auto shop_control   = ControlManager::getInstance()->getShopControl();
    auto shop_cart_list = ControlManager::getInstance()->getShopCartList();

    auto shop_cart_list_data_ = shop_cart_list->shop_cart_list_data_;
    auto sale_list_unique     = shop_cart_list_data_.order_unique_;
    auto goods_mgr            = DataManager::getInstance()->getGoodsMgr();

    const auto cur_date_time_str = shop_cart_list_data_.order_date_ + " " + shop_cart_list_data_.order_time_;

    OrderInfo order_info;
    double    sale_list_total = shop_cart_list->getTotalGoodsPrice(); // 订单总价格

    double        sale_list_pur         = 0.0; // 订单进货价
    double        sale_list_total_count = 0.0; // 商品总数量
    double        commission_sum        = 0.0; // 提成总和
    unsigned long goods_kind_count      = 0;   // 几种商品
    for (ShopCartItem &shop_cart_item : shop_cart_list->items())
    {
        GoodsInfo goods_info = shop_cart_item.getGoodsInfo();

        OrderDetailInfo order_detail_info;

        order_detail_info.sale_list_unique          = sale_list_unique.toULongLong(); // 销售单唯一标识
        order_detail_info.goods_barcode             = goods_info.goods_barcode;       // 商品条形码
        order_detail_info.goods_name                = goods_info.goods_name;          // 商品名称
        order_detail_info.goods_picturepath         = goods_info.goods_picturepath;   // 商品图片路径
        order_detail_info.sale_list_detail_count    = shop_cart_item.goods_num_;      // 商品数量
        order_detail_info.sale_list_detail_price    = shop_cart_item.getPrice();      // 商品购买的价格
        order_detail_info.sale_list_detail_subtotal = shop_cart_item.getSubtotal();   // 金额小计
        order_detail_info.goods_id                  = goods_info.goods_id;            // 商品id
        if (shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_GOODS) ||
            shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS))
        {
            order_detail_info.goods_purprice =
                shop_cart_item.getPrice() * (1.0 - ConfigTool::getInstance()->getSetting(ConfigEnum::NOCODE_GOODS_PROFIT_RATIO).toFloat()); // 商品进价
            order_detail_info.goods_old_price = shop_cart_item.getPrice();                                                                  // 商品原价
        }
        else
        {
            order_detail_info.goods_purprice  = goods_info.goods_in_price;   // 商品进价
            order_detail_info.goods_old_price = goods_info.goods_sale_price; // 商品原价
        }
        order_detail_info.commission_total = goods_info.getcommissionNum(); // 提成小计
        order_detail_info.goods_beans_count;                                // 供货商赠送百货豆
        order_detail_info.shop_beans_count = goods_info.getShopBeansNum();  // 商家赠送百货豆
        order_detail_info.sale_list_express_id;                             // 快递关联ID
        order_detail_info.saveTime = cur_date_time_str;                     // 时间

        sale_list_pur += goods_info.goods_in_price;
        sale_list_total_count += shop_cart_item.goods_num_;
        ++goods_kind_count;

        commission_sum += goods_info.getcommissionNum() * shop_cart_item.goods_num_;

        order_info.order_detail_infos.push_back(order_detail_info);
    }
    {
        order_info.sale_list_unique     = sale_list_unique.toULongLong();
        order_info.shop_unique          = shop_control->getShopUnique();
        order_info.sale_list_datetime   = cur_date_time_str;
        order_info.sale_list_total      = sale_list_total;
        order_info.sale_list_pur        = sale_list_pur;
        order_info.sale_list_totalCount = sale_list_total_count;
        order_info.cus_unique           = shop_cart_list->shop_cart_list_data_.member_unique;
        order_info.sale_type            = 0;
        order_info.sale_list_name;
        order_info.sale_list_phone;
        order_info.sale_list_address;
        order_info.sale_list_delfee;
        order_info.shop_subsidy_delfee;
        order_info.sale_list_discount;
        order_info.sale_list_state = 3;
        order_info.sale_list_handlestate;
        order_info.sale_list_payment = 8; // 支付方式：1-现金 2-支付宝 3-微信 4-银行卡  5-储值卡 6-美团外卖7-饿了么外卖 8-混合支付
        // 9-免密支付 10-积分兑换 11-百货豆 12-拉卡拉 13-易通付款码支付 14-合利宝刷卡
        order_info.sale_list_remarks;
        order_info.sale_list_flag;
        order_info.receipt_datetime;
        order_info.send_datetime;
        order_info.sale_list_number;
        order_info.sale_list_cashier   = shop_control->getPersonInfo().cashier_id;
        order_info.sale_list_same_type = 2; // 1-PC已同步 2-PC未同步 默认为2

        order_info.sale_list_actually_received = QString::number(online_pay + cash_pay, 'f', 2).toDouble();
        //shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment);
        // order_info.sale_list_actually_received = shop_cart_list->shop_cart_list_data_.cash_received_;

        order_info.machine_num;
        order_info.evaluate_point;
        order_info.commission_sum = commission_sum;
        order_info.pay_time       = cur_date_time_str;
        order_info.trade_no;
        order_info.cancle_reason;
        order_info.refund_money;
        order_info.refund_reason;
        order_info.refunt_operate_reason;
        order_info.cus_unique = shop_cart_list->shop_cart_list_data_.member_unique;
        order_info.addr_latitude;
        order_info.addr_longitude;
        order_info.goods_kind_count;
        order_info.shipping_method;
        order_info.point_deduction;
        order_info.card_deduction;
        order_info.label_val;
        order_info.coupon_amount;
        order_info.beans_get;
        order_info.beans_use;
        order_info.shop_coupon_id;
        order_info.point_val;
        order_info.beans_money;
        order_info.points_get;
        order_info.delivery_type;
        order_info.cancel_time;
        order_info.goods_weight;
        order_info.formId;
        order_info.add_shop_balance_status;
        order_info.delivery_error;
        order_info.platform_shop_beans;
        order_info.head_image;
        order_info.cus_face_token;
        order_info.sup_give_beans;
        order_info.platform_cus_beans;
        order_info.return_price;
        order_info.verify_staff_id;
        order_info.shop_give_beans;
        order_info.local_points_ratio  = ConfigTool::getInstance()->pointsRatio();
        order_info.local_member_unique = shop_cart_list->shop_cart_list_data_.member_unique;

        OrderPayMethod order_pay_method_cash;
        order_pay_method_cash.pay_method = static_cast<int>(PayMethodEnum::PAY_METHOD__CASH);
        order_pay_method_cash.pay_price  = cash_pay;

        OrderPayMethod order_pay_method_online;
        order_pay_method_online.pay_method = static_cast<int>(PayMethodEnum::PAY_METHOD__YI_TONG);
        order_pay_method_online.pay_price  = online_pay;

        order_info.order_pay_method_vec.push_back(order_pay_method_cash);
        order_info.order_pay_method_vec.push_back(order_pay_method_online);
    }

    return order_info;
}
OrderInfo PayMethodControl::generateCombinedOnlineVipPayOrder(double online_pay, double cash_pay,double total_pay)
{
    auto shop_control   = ControlManager::getInstance()->getShopControl();
    auto shop_cart_list = ControlManager::getInstance()->getShopCartList();

    auto shop_cart_list_data_ = shop_cart_list->shop_cart_list_data_;
    auto sale_list_unique     = shop_cart_list_data_.order_unique_;
    auto goods_mgr            = DataManager::getInstance()->getGoodsMgr();

    const auto cur_date_time_str = shop_cart_list_data_.order_date_ + " " + shop_cart_list_data_.order_time_;

    OrderInfo order_info;
    double    sale_list_total = shop_cart_list->getTotalGoodsPrice(); // 订单总价格

    double        sale_list_pur         = 0.0; // 订单进货价
    double        sale_list_total_count = 0.0; // 商品总数量
    double        commission_sum        = 0.0; // 提成总和
    unsigned long goods_kind_count      = 0;   // 几种商品
    for (ShopCartItem &shop_cart_item : shop_cart_list->items())
    {
        GoodsInfo goods_info = shop_cart_item.getGoodsInfo();

        OrderDetailInfo order_detail_info;

        order_detail_info.sale_list_unique          = sale_list_unique.toULongLong(); // 销售单唯一标识
        order_detail_info.goods_barcode             = goods_info.goods_barcode;       // 商品条形码
        order_detail_info.goods_name                = goods_info.goods_name;          // 商品名称
        order_detail_info.goods_picturepath         = goods_info.goods_picturepath;   // 商品图片路径
        order_detail_info.sale_list_detail_count    = shop_cart_item.goods_num_;      // 商品数量
        order_detail_info.sale_list_detail_price    = shop_cart_item.getPrice();      // 商品购买的价格
        order_detail_info.sale_list_detail_subtotal = shop_cart_item.getSubtotal();   // 金额小计
        order_detail_info.goods_id                  = goods_info.goods_id;            // 商品id
        if (shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_GOODS) ||
            shop_cart_item.goods_barcode_.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS))
        {
            order_detail_info.goods_purprice =
                shop_cart_item.getPrice() * (1.0 - ConfigTool::getInstance()->getSetting(ConfigEnum::NOCODE_GOODS_PROFIT_RATIO).toFloat()); // 商品进价
            order_detail_info.goods_old_price = shop_cart_item.getPrice();                                                                  // 商品原价
        }
        else
        {
            order_detail_info.goods_purprice  = goods_info.goods_in_price;   // 商品进价
            order_detail_info.goods_old_price = goods_info.goods_sale_price; // 商品原价
        }
        order_detail_info.commission_total = goods_info.getcommissionNum(); // 提成小计
        order_detail_info.goods_beans_count;                                // 供货商赠送百货豆
        order_detail_info.shop_beans_count = goods_info.getShopBeansNum();  // 商家赠送百货豆
        order_detail_info.sale_list_express_id;                             // 快递关联ID
        order_detail_info.saveTime = cur_date_time_str;                     // 时间

        sale_list_pur += goods_info.goods_in_price;
        sale_list_total_count += shop_cart_item.goods_num_;
        ++goods_kind_count;

        commission_sum += goods_info.getcommissionNum() * shop_cart_item.goods_num_;

        order_info.order_detail_infos.push_back(order_detail_info);
    }
    {
        order_info.sale_list_unique     = sale_list_unique.toULongLong();
        order_info.shop_unique          = shop_control->getShopUnique();
        order_info.sale_list_datetime   = cur_date_time_str;
        order_info.sale_list_total      = sale_list_total;
        order_info.sale_list_pur        = sale_list_pur;
        order_info.sale_list_totalCount = sale_list_total_count;
        order_info.cus_unique           = shop_cart_list->shop_cart_list_data_.member_unique;
        order_info.sale_type            = 0;
        order_info.sale_list_name;
        order_info.sale_list_phone;
        order_info.sale_list_address;
        order_info.sale_list_delfee;
        order_info.shop_subsidy_delfee;
        order_info.sale_list_discount;
        order_info.sale_list_state = 3;
        order_info.sale_list_handlestate;
        order_info.sale_list_payment = 8; // 支付方式：1-现金 2-支付宝 3-微信 4-银行卡  5-储值卡 6-美团外卖7-饿了么外卖 8-混合支付
        // 9-免密支付 10-积分兑换 11-百货豆 12-拉卡拉 13-易通付款码支付 14-合利宝刷卡
        order_info.sale_list_remarks;
        order_info.sale_list_flag;
        order_info.receipt_datetime;
        order_info.send_datetime;
        order_info.sale_list_number;
        order_info.sale_list_cashier   = shop_control->getPersonInfo().cashier_id;
        order_info.sale_list_same_type = 2; // 1-PC已同步 2-PC未同步 默认为2

        order_info.sale_list_actually_received = QString::number(total_pay, 'f', 2).toDouble();
        //shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment);
        // order_info.sale_list_actually_received = shop_cart_list->shop_cart_list_data_.cash_received_;

        order_info.machine_num;
        order_info.evaluate_point;
        order_info.commission_sum = commission_sum;
        order_info.pay_time       = cur_date_time_str;
        order_info.trade_no;
        order_info.cancle_reason;
        order_info.refund_money;
        order_info.refund_reason;
        order_info.refunt_operate_reason;
        order_info.cus_unique = shop_cart_list->shop_cart_list_data_.member_unique;
        order_info.addr_latitude;
        order_info.addr_longitude;
        order_info.goods_kind_count;
        order_info.shipping_method;
        order_info.point_deduction;
        order_info.card_deduction;
        order_info.label_val;
        order_info.coupon_amount;
        order_info.beans_get;
        order_info.beans_use;
        order_info.shop_coupon_id;
        order_info.point_val;
        order_info.beans_money;
        order_info.points_get;
        order_info.delivery_type;
        order_info.cancel_time;
        order_info.goods_weight;
        order_info.formId;
        order_info.add_shop_balance_status;
        order_info.delivery_error;
        order_info.platform_shop_beans;
        order_info.head_image;
        order_info.cus_face_token;
        order_info.sup_give_beans;
        order_info.platform_cus_beans;
        order_info.return_price;
        order_info.verify_staff_id;
        order_info.shop_give_beans;
        order_info.local_points_ratio  = ConfigTool::getInstance()->pointsRatio();
        order_info.local_member_unique = shop_cart_list->shop_cart_list_data_.member_unique;

        OrderPayMethod order_pay_method_cash;
        order_pay_method_cash.pay_method = static_cast<int>(PayMethodEnum::PAY_METHOD__CASH);
        order_pay_method_cash.pay_price  = cash_pay;

        OrderPayMethod order_pay_method_online;
        order_pay_method_online.pay_method = static_cast<int>(PayMethodEnum::PAY_METHOD__YI_TONG);
        order_pay_method_online.pay_price  = online_pay;

        OrderPayMethod order_pay_method_vipcard;
        order_pay_method_vipcard.pay_method = static_cast<int>(PayMethodEnum::PAY_METHOD__VIPCARD);
        order_pay_method_vipcard.pay_price  =round((total_pay - cash_pay - online_pay) * 100) / 100;

        order_info.order_pay_method_vec.push_back(order_pay_method_cash);
        order_info.order_pay_method_vec.push_back(order_pay_method_online);
        order_info.order_pay_method_vec.push_back(order_pay_method_vipcard);
    }

    return order_info;
}

PayEnum PayMethodControl::getPayMethodByPaymentCode(QString payment_code)
{
    auto left2 = payment_code.left(2).toInt();
    if (left2 >= 10 && left2 <= 15)
    {
        return PayEnum::PAY_METHOD__WECHAT_ONLINE;
    }
    else if (left2 >= 25 && left2 <= 30)
    {
        return PayEnum::PAY_METHOD__ALIPAY_ONLINE;
    }
    else if (left2 == 62)
    {
        return PayEnum::PAY_METHOD__UNIONPAY;
        // payState = "3"; // 银联
    }
    else if (left2 == 36)
    {
        return PayEnum::PAY_METHOD__MINI_PROGRAM;
    }
    return PayEnum::PAY_METHOD__UNKNOW;
}

void PayMethodControl::reqMiniProgramCode()
{
    auto cur_thread = new QThread(this);

    QString req_url = req_host_prefix + "shopmanager/pc/createQRCode.do";

    QMap<QString, QString> body_map;

    auto shopUnique = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());

    body_map.insert("shopUnique", shopUnique);

    auto body_data = HttpWorker::urlEncode(body_map);

    HttpWorker *http_worker = new HttpWorker(req_url, body_data, "application/x-www-form-urlencoded");
    http_worker->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_worker, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply)
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);

                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_WARN(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "查询结果:{}", json_doc.dump());

                    int status = json_doc.value("status", 0);
                    if (status == 1)
                    {
                        if (json_doc.contains("data") && json_doc["data"].contains("codePath"))
                        {
                            string code_path     = json_doc["data"]["codePath"];
                            mini_program_img_url = req_host_prefix.chopped(1) + QString::fromStdString(code_path);
                            emit sigRefreshMiniProgramCode();
                        }
                    }
                    else
                    {
                        SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_net_, "获取小程序码失败!");
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });

    // 启动线程
#ifdef HIGH_PRIORITY
    cur_thread->start(QThread::TimeCriticalPriority);
#else
    cur_thread->start();
#endif
}

QVariant PayMethodControl::getMiniProgramCodeUrl()
{
    return mini_program_img_url;
}

void PayMethodControl::payFinishWizard(EnumTool::PayStatusEnum pay_status, EnumTool::PayMethodEnum pay_type)
{
    switch (pay_status)
    {
    case EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS:
        auto order_control = ControlManager::getInstance()->getOrderControl();
        order_control->backupOrder();
        break;
    }
    //ControlManager::getInstance()->getShopCartList()->resetAllInfo(); 注释，用于打印展示
    // ControlManager::getInstance()->getShopCartList()->setOrderPaid();
}

QJSValueList PayMethodControl::generateQmlCallbackArgList(EnumTool::PayStatusEnum pay_status, EnumTool::PayMethodEnum pay_method, QString json_detail)
{
    QJSValueList arglist;
    arglist.push_back((int)pay_status); // 支付状态
    arglist.push_back((int)pay_method); // 支付方式
    arglist.push_back(json_detail);     // 支付详情
    return arglist;
}

QJSValueList PayMethodControl::generateQmlCallbackArgList(EnumTool::PayStatusEnum pay_status, EnumTool::PayMethodEnum pay_method, std::string json_detail)
{
    return generateQmlCallbackArgList(pay_status, pay_method, QString::fromStdString(json_detail));
}

QVariant PayMethodControl::getAllPayMethodJson()
{
    Json result = {{{"role_name", "现金"}, {"role_enum", EnumTool::PayMethodEnum::PAY_METHOD__CASH}},
                   {{"role_name", "支付宝"}, {"role_enum", EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE}},
                   {{"role_name", "微信"}, {"role_enum", EnumTool::PayMethodEnum::PAY_METHOD__WECHAT_OFFLINE}},
                   {{"role_name", "储值卡"}, {"role_enum", EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD}},
                   {{"role_name", "银行卡"}, {"role_enum", EnumTool::PayMethodEnum::PAY_METHOD__BANK_CARD}},
                   {{"role_name", "金圈平台"}, {"role_enum", EnumTool::PayMethodEnum::PAY_METHOD__JINQUAN}}};

    return QString::fromStdString(result.dump());
}

QVariant PayMethodControl::getValidPayMethodJson()
{
    Json result;

    for (auto &pay_method_status_list_item : pay_method_status_list_)
    {
        if (pay_method_status_list_item.is_enabled)
        {
            result.push_back(
                Json{{"role_name", getPayMethodStr(pay_method_status_list_item.pay_enum).toStdString()}, {"role_enum", pay_method_status_list_item.pay_enum}});
        }
    }

    return QString::fromStdString(result.dump());
}

QString PayMethodControl::getValidPayMethodJson4Query()
{
    Json result = {{{"role_name", "现金"}, {"role_enum", (int)(EnumTool::PayMethodEnum::PAY_METHOD__CASH)}},
                   {{"role_name", "支付宝"}, {"role_enum", (int)(EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE)}},
                   {{"role_name", "微信"}, {"role_enum", (int)(EnumTool::PayMethodEnum::PAY_METHOD__WECHAT_OFFLINE)}},
                   {{"role_name", "储值卡"}, {"role_enum", (int)(EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD)}},
                   {{"role_name", "混合支付"}, {"role_enum", (int)(EnumTool::PayMethodEnum::PAY_METHOD__COMBINED)}},
                   {{"role_name", "金圈平台"}, {"role_enum", (int)(EnumTool::PayMethodEnum::PAY_METHOD__JINQUAN)}}};

    return QString::fromStdString(result.dump(-1, ' ', false, Json::error_handler_t::ignore));
}

QString PayMethodControl::payMethodList()
{
    return "";
}

void PayMethodControl::setPayMethodList(QString list_in)
{
    Json json_doc = Json::parse(list_in.toStdString());


    emit payMethodListChanged();
}

void PayMethodControl::FillPayMethodList()
{
    auto insertItem = [](std::list<PayMethodStatus> &pay_method_status_list_in, PayMethodStatus pay_method_status)
    {
        auto ret_iter = std::find_if(pay_method_status_list_in.begin(), pay_method_status_list_in.end(),
                                     [&](const PayMethodStatus &pay_method_status_item)
                                     {
                                         return pay_method_status_item.pay_enum == pay_method_status.pay_enum;
                                     });

        if (ret_iter != pay_method_status_list_in.end())
            return;

        pay_method_status_list_in.push_back(pay_method_status);
    };

    insertItem(pay_method_status_list_, PayMethodStatus{false, PayEnum::PAY_METHOD__CASH});
    insertItem(pay_method_status_list_, PayMethodStatus{false, PayEnum::PAY_METHOD__ALIPAY_OFFLINE});
    insertItem(pay_method_status_list_, PayMethodStatus{false, PayEnum::PAY_METHOD__WECHAT_OFFLINE});

    insertItem(pay_method_status_list_, PayMethodStatus{false, PayEnum::PAY_METHOD__FACE});

    insertItem(pay_method_status_list_, PayMethodStatus{false, PayEnum::PAY_METHOD__VIPCARD});
    insertItem(pay_method_status_list_, PayMethodStatus{false, PayEnum::PAY_METHOD__COMBINED});
    insertItem(pay_method_status_list_, PayMethodStatus{false, PayEnum::PAY_METHOD__GOODS_MANAGER});
}

void PayMethodControl::reFreshPayMethodList()
{
    auto pay_method_j_str = ConfigTool::getInstance()->getSetting(ConfigEnum::PAY_METHOD_CONFIG).toString().toStdString();

    Json json_doc = Json::parse(pay_method_j_str, nullptr, false);

    if (!json_doc.is_discarded() && json_doc.is_array())
    {
        for (auto cur_item : json_doc)
        {
            auto pay_method_status = cur_item.get<PayMethodStatus>();
            pay_method_status_list_.push_back(pay_method_status);
        }
    }

    FillPayMethodList();
}
