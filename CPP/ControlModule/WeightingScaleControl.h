﻿#ifndef WEIGHTINGSCALECONTROL_H
#define WEIGHTINGSCALECONTROL_H

#include <QObject>
#include <QTimer>
#include <QVariant>
#include <QtSerialPort/QSerialPort>
#include <mutex>

class WeightingScaleControl : public QObject
{
    Q_OBJECT

    Q_PROPERTY(double finalWeight READ finalWeight WRITE setFinalWeight NOTIFY finalWeightChanged FINAL)
    Q_PROPERTY(bool isConnected READ isConnected WRITE setIsConnected NOTIFY isConnectedChanged FINAL)

    Q_PROPERTY(bool isStable READ isStable WRITE setIsStable NOTIFY isStableChanged FINAL)

public:
    explicit WeightingScaleControl(QObject *parent = nullptr);
    ~WeightingScaleControl();

    Q_INVOKABLE static QStringList getAvailablePorts();

    Q_INVOKABLE void    setCom(QString com);
    Q_INVOKABLE QString getCom();

    Q_INVOKABLE void    setBaud(QString baud);
    Q_INVOKABLE QString getBaud();

    Q_INVOKABLE bool    setParseType(QString type);
    Q_INVOKABLE QString getParseType();

    Q_INVOKABLE QStringList getParseTypes();

    Q_INVOKABLE void setQupi(double weight);
    Q_INVOKABLE void setIsQupi(bool is_qupi);

    Q_INVOKABLE void setZeroOut();

    Q_INVOKABLE double getQupi();

    Q_INVOKABLE double getLastWeightKg();

    bool             reOpen();
    Q_INVOKABLE bool isWeightScaleOpened();

    void setIsKg(bool type);
    bool getIsKg();

    Q_INVOKABLE void setStableWeight(QString weight, QString up_max, QString down_max);
    Q_INVOKABLE void stopStableWeightDetection();

    void   detectWeightStable();
    QTimer weight_stable_timer_;
    int    weight_stable_timer_trigger_time_msec_ = 1000;

    Q_INVOKABLE void setIsNeedZero(bool is_need_zero);
    Q_INVOKABLE bool getIsNeedZero();

    // 保存最后一次重量
    void setLastWeight(double weight);

    double getLastWeight();

    double finalWeight();
    void   setFinalWeight(double final_weight);

    bool isConnected();
    void setIsConnected(bool is_conn);

    bool isStable();
    void setIsStable(bool is_stable);

    bool is_conn_ = true;

    size_t              trigger_num_   = 0;
    double              up_max_        = .0;
    double              down_max_      = .0;
    double              weight_stable_ = -1.0;
    QTimer              timer_stable_;
    QTimer              timer_wait_;
    std::vector<double> stable_list_;
    bool                is_need_zero_ = false; // 是否需要归零

    QStringList bauds_;
    QStringList parse_types_;
    double      last_weight_zero_out_qupi_ = .0; // 原始重量 - (置零+去皮)
    double      last_weight_zero_out_      = .0; // 原始重量 - 置零
    double      last_origin_weight_        = .0; // 原始重量
    double      qupi_                      = .0; // 去皮
    double      zero_out_                  = .0; // 置零
    enum class ParseType : int
    {
        TYPE1 = 0,
        TYPE2,
        TYPE3,
        TYPE4,
    };
    QString    hex_str_tmp_;
    QString    str_tmp_;
    QByteArray dataToHex(const QByteArray &data, const QString &separator = " ", const QString &prefix = "");
    QByteArray dataFromHex(const QString &data);

    bool is_stable_ = false;

private:
    QSerialPort *weighting_scale_ = nullptr;
    void         readDataSlot();

    WeightingScaleControl::ParseType parse_type_ = ParseType::TYPE1;

    bool    is_kg_ = true;
    QString chengzhongbyte_;

    double final_weight_ = .0;

private:
    static WeightingScaleControl *pinstance_;
    static std::mutex             mutex_;
    QString                       com_  = "COM1";
    QString                       baud_ = "9600";

signals:
    void sendWeightStable(QVariant weight);
    void sendWeightZero();
    void sendWeight(QString weight_str);

    void finalWeightChanged();

    void isConnectedChanged();

    void isStableChanged();
};


#endif // WEIGHTINGSCALECONTROL_H
