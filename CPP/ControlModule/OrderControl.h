﻿#ifndef ORDERCONTROL_H
#define ORDERCONTROL_H

#include <QDateTime>
#include <QJSEngine>
#include <QJSValue>
#include <QObject>
#include "DataModule/OrderData.h"
#include "ShopCartControl.h"
#include "Utils/Utils.h"

/*!
 * \brief The OrderControl class 订单管理
 */
class OrderControl : public QObject
{
    Q_OBJECT
public:
    OrderControl(QObject *parent = nullptr);
    ~OrderControl();

    static QString getPendingOrderPath();

    void initDataByConfig();
    void savePendingOrder2Config();

    /*!
     * \brief addOrder 添加订单
     * \param order_info 订单信息
     */
    bool addOrder(const OrderInfo &order_info);

    /*!
     * \brief getStockRecord4Qml 获取出入库记录
     * \param callback 结束回调
     */
    Q_INVOKABLE void getStockRecord4Qml(QJSValue callback, QString begin_time = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm"),
                                        QString end_time = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm"));
    /*!
     * \brief getGoodBatchListQml
     * \param callback
     * \param stockType
     * \param pageSize
     * \param pageIndex
     */
    Q_INVOKABLE void getGoodBatchListQml(QJSValue callback, QString stockType,int pageSize,int pageIndex,QString goodsBarcodes);

    /*!
     * \brief getSalesRecord4Qml 获取销售记录
     * \param callback qml回调
     * \param begin_time 开始时间
     * \param end_time 结束时间
     * \return
     */
    Q_INVOKABLE void getSalesRecord4Qml(QJSValue callback, QString begin_time = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm"),
                                        QString end_time = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm"));

    /*!
     * \brief getSalesRecordDetail4Qml 获取销售记录详情
     * \param callback qml回调
     * \param saleUnique 订单唯一序号
     * \return
     */
    Q_INVOKABLE void getSalesRecordDetail4Qml(QJSValue callback, QVariant saleUnique);

    /*!
     * \brief getHandoverRecord4Qml 获取交接班记录
     * \param callback
     * \param begin_time
     * \param end_time
     * \return
     */
    Q_INVOKABLE void getHandoverRecord4Qml(QJSValue callback, QString begin_time = QDateTime::currentDateTime().toString("yyyy-MM-dd"),
                                           QString end_time = QDateTime::currentDateTime().toString("yyyy-MM-dd"));
    /*!
     * \brief getGoodsInformation4Qml 获取商品信息列表
     * \param callback
     * \param begin_time
     * \param end_time
     */
    Q_INVOKABLE void getGoodsInformation4Qml(QJSValue callback, QString begin_time = QDateTime::currentDateTime().toString("yyyy-MM-dd"),
                                           QString end_time = QDateTime::currentDateTime().toString("yyyy-MM-dd"),QString goodsMsg = "", QString orderName = "", QString pageNum = "");
    //获取订单详情
    Q_INVOKABLE void getHandoverRecordDetail4Qml(QJSValue callback, QString staff_id, QString start_time, QString end_time);
    /*!
     * \brief getGoodsSaleStatistics4Qml 商品销售统计查询
     * \param callback
     * \param begin_time
     * \param end_time
     * \param goodsMsg
     * \param pageNum
     * \param pageSizes
     * \param groupUnique
     * \param kindUnique
     * \param supplierUnique
     */
    Q_INVOKABLE void  getGoodsSaleStatistics4Qml(QJSValue callback, QString begin_time, QString end_time,QString goodsMsg, QString pageNum,QString pageSizes, QString groupUnique, QString kindUnique, QString supplierUnique);
    /*!
     * \brief getGoodsSaleDetail4Qml 商品销售统计详情页查询
     * \param goodsBarcode
     * \param startTime
     * \param endTime
     * \param pageNum
     * \param pageSize
     */
    Q_INVOKABLE void  getGoodsSaleDetail4Qml(QJSValue callback, QString goodsBarcode, QString startTime, QString endTime, QString pageNum, QString pageSize);
    /*!
     * \brief reqRefundOrder 请求退款
     * \param callback QML回调
     * \param data_json 数据JSON
     */
    Q_INVOKABLE void reqRefundOrder(QJSValue callback, QVariant data_json);

    /*!
     * \brief addPendingOrderByShopCart 根据购物车添加挂单
     */
    Q_INVOKABLE void addPendingOrderByShopCart();

    /*!
     * \brief backupOrder 备份订单
     */
    void backupOrder();

    /*!
     * \brief getPendingOrders4Qml 获取所有挂单JSON
     * \return 挂单JSON
     */
    Q_INVOKABLE QString getPendingOrdersJson();

    /*!
     * \brief clearAllPendingOrders 清空所有挂单
     * \return
     */
    Q_INVOKABLE void clearAllPendingOrders();

    /*!
     * \brief takePendingOrder2ShopCart 取单到购物车
     * \param order_unique 订单ID
     */
    Q_INVOKABLE void takePendingOrder2ShopCart(QString order_unique);

    /*!
     * \brief getPendingOrderDetailJson 根据挂单ID获取挂单详情JSON
     * \param order_index 订单唯一编号
     * \return
     */
    Q_INVOKABLE QString getPendingOrderDetailJson(QString sale_list_unique);

    /*!
     * \brief eraserPendingOrderById 根据挂单ID删除挂单
     * \param sale_list_id 挂单ID
     * \return
     */
    Q_INVOKABLE bool eraserPendingOrderById(QString sale_list_id);

    /*!
     * \brief generateOrderId 生成订单ID
     * \return 订单ID
     */
    static QString generateOrderId();

    /*!
     * \brief getBackupOrderPrice 获取备份订单价格
     * \return 备份订单价格
     */
    Q_INVOKABLE double getBackupOrderPrice();
    Q_INVOKABLE QString doubleToString(double value);

    Q_INVOKABLE bool printPendingOrderByOrderUnique(QString order_unique);


    std::vector<std::shared_ptr<ShopCartList>> pending_orders_;    // 挂单
    std::shared_ptr<ShopCartList>              backup_last_order_; // 上一单

signals:
    void sigRefreshPendingOrders();
    void sigBackupOrderFinished();
};

#endif // ORDERCONTROL_H
