﻿#include "LogCtrl.h"
#include <QDateTime>
#include <QDir>
#include "ControlManager.h"
#include "LogModule/LogManager.h"
#include "NetModule/HttpCallback.h"
#include "NetModule/NetGlobal.h"
#include "Utils/Utils.h"
#include "json-qt.hpp"
#include "zip_file/zip_file.hpp"

using namespace AeaQt;

LogCtrl::LogCtrl(QObject *parent) : QObject{parent}
{
}

void LogCtrl::uploadLogZipWizard(QString start_date, QString end_date)
{
    if (compressionLogs(start_date, end_date))
    {
        reqUploadLogsZip();
    }
}

bool LogCtrl::reqUploadLogsZip()
{
    LOG_NET_WARN("reqUploadLogsZip");

    QString url       = req_host_prefix + "/shopmanager/pc/uploadLog.do";
    auto    shop_ctrl = ControlManager::getInstance()->getShopControl();

    HttpClient *http_client = new HttpClient();

    auto &&request = http_client->post(url);

    QHttpMultiPart *multiPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);

    {
        QHttpPart cur_part;
        cur_part.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant("form-data; name=\"shopUnique\""));
        cur_part.setBody(QString::number(shop_ctrl->getShopUnique()).toUtf8());
        multiPart->append(cur_part);
    }

    {
        QHttpPart filePart;

        QFile *fileLogZip = new QFile(getLogZipPath());
        fileLogZip->open(QIODevice::ReadOnly);

        if (!fileLogZip->isOpen())
        {
            LOG_OPT_ERROR("日志ZIP打开失败");
            return false;
        }

        fileLogZip->setParent(multiPart);

        QString requestFormat = QString::fromUtf8("form-data;name=%1;filename=%2").arg("file").arg(fileLogZip->fileName());

        filePart.setHeader(QNetworkRequest::ContentDispositionHeader, requestFormat);
        filePart.setBodyDevice(fileLogZip);

        multiPart->append(filePart);
    }

    {
        QHttpPart cur_part;
        cur_part.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant("form-data; name=\"macId\""));
        cur_part.setBody(Utils::getHostMacAddressWithoutColon().toUtf8());
        multiPart->append(cur_part);
    }

    request.bodyWithMultiPart(multiPart);

    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    auto json_doc = Json::parse(data);

                                    if (getDataFromJson(json_doc, "status").toInt() == 1)
                                    {
                                        LOG_OPT_INFO("日志上传成功");
                                    }
                                    else
                                    {
                                        LOG_OPT_ERROR("日志上传失败");
                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    LOG_OPT_ERROR("日志上传失败");
                                    break;
                                }
                            }
                        });
    return true;
}

bool LogCtrl::compressionLogs(QString start_date, QString end_date)
{
    auto log_mgr = LogMgr::getInst();

    zip_start_date_ = start_date;
    zip_end_date_   = end_date;

    //保存当前日志
    if (start_date.isEmpty() && end_date.isEmpty())
    {
        auto log_path = QString::fromStdString(log_mgr->getCurLogPath());

        miniz_cpp::zip_file zip_f;

        QFileInfo log_path_info((log_path));

        if (!log_path_info.exists())
        {
            LOG_DATA_ERROR("log 文件夹不存在");
            return false;
        }

        QDir log_dir((log_path));

        QFileInfoList file_info_list = log_dir.entryInfoList(QDir::Files | QDir::Hidden | QDir::NoSymLinks);

        for (auto &file_info_item : file_info_list)
        {
            zip_f.write(file_info_item.absoluteFilePath().toStdString(), file_info_item.fileName().toStdString());
        }
        auto zip_file_path = getLogZipPath().toStdString();

        {
            QFileInfo FileInfo(QString::fromStdString(zip_file_path));

            if (FileInfo.exists())
            {
                if (FileInfo.isFile()) //如果是文件
                {
                    QFile::remove(QString::fromStdString(zip_file_path));
                }
                else if (FileInfo.isDir()) //如果是文件夹
                {
                    QDir qDir(QString::fromStdString(zip_file_path));
                    qDir.removeRecursively();
                }
            }
        }

        zip_f.save(getLogZipPath().toStdString());
    }
    else
    {
        QDateTime date_begin = QDateTime::fromString(start_date, "yyyy-MM-dd");
        QDateTime date_end   = QDateTime::fromString(end_date, "yyyy-MM-dd");

        miniz_cpp::zip_file zip_f;

        while (date_begin <= date_end)
        {
            auto date_prefix = date_begin.toString("yyyy-MM-dd");
            auto log_path    = QString::fromStdString(log_mgr->getLogPath()) + date_prefix;

            addFolder2Zip(zip_f, log_path, date_prefix);

            date_begin = date_begin.addDays(1);
        }

        auto zip_file_path = getLogZipPath().toStdString();
        {
            QFileInfo FileInfo(QString::fromStdString(zip_file_path));

            if (FileInfo.exists())
            {
                if (FileInfo.isFile()) //如果是文件
                {
                    QFile::remove(QString::fromStdString(zip_file_path));
                }
                else if (FileInfo.isDir()) //如果是文件夹
                {
                    QDir qDir(QString::fromStdString(zip_file_path));
                    qDir.removeRecursively();
                }
            }
        }

        zip_f.save(getLogZipPath().toStdString());
    }

    return true;
}

QString LogCtrl::getLogZipPath()
{
    QString date_str;
    if (zip_start_date_ == zip_end_date_)
    {
        date_str = zip_start_date_;
    }
    else
    {
        date_str = zip_start_date_ + "-" + zip_end_date_;
    }

    auto log_zip_path = (date_str.isEmpty() ? Utils::DateTime::getCurDateStr() : date_str) + ("_" + Utils::getHostMacAddressWithoutColon()) + ".zip";
    return log_zip_path;
}

void LogCtrl::addFolder2Zip(miniz_cpp::zip_file &zip_f, QString path, QString prefix)
{
    QDir source_dir(path);
    if (!source_dir.exists())
        return;

    QFileInfoList file_info_list = source_dir.entryInfoList(QDir::Files | QDir::Hidden | QDir::NoSymLinks);
    for (auto &file_info_item : file_info_list)
    {
        zip_f.write(file_info_item.absoluteFilePath().toStdString(),
                    (prefix.isEmpty() ? "" : (prefix + "/")).toStdString() + file_info_item.fileName().toStdString());
    }

    QFileInfoList file_info_list_dir = source_dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);
    for (auto &file_info_list_dir_item : file_info_list_dir)
    {
        addFolder2Zip(zip_f, file_info_list_dir_item.absoluteFilePath(), (prefix.isEmpty() ? "" : (prefix + "/")) + file_info_list_dir_item.fileName());
    }
}
