﻿#include "BusinessAlarmControl.h"
#include <string>
#include "Utils/Utils.h"
#include "Utils/Utils4Qml.h"
#include "json-qt.hpp"
#include "qobjectdefs.h"
#include "qurl.h"
#include "NetModule/HttpCallback.h"
#include "NetModule/HttpClient.h"
#include "NetModule/HttpWorker.h"

#include <fstream>
#include "ControlModule/ControlManager.h"
#include "ControlModule/ShopControl.h"
#include "DataModule/DataManager.h"
#include "LogModule/LogManager.h"
#include "NetModule/NetGlobal.h"


using namespace std;
using namespace AeaQt;

std::unique_ptr<BusinessAlarmControl> BusinessAlarmControl::singleton_;
std::mutex                  BusinessAlarmControl::mutex_;

BusinessAlarmControl::BusinessAlarmControl(QObject *parent) : QObject{parent}
{

}
BusinessAlarmControl::~BusinessAlarmControl()
{

}
BusinessAlarmControl *BusinessAlarmControl::getInstance()
{
    std::lock_guard<std::mutex> lock(mutex_);

    if (singleton_ == nullptr)
    {
        singleton_.reset(new BusinessAlarmControl());
    };
    return singleton_.get();
}

void BusinessAlarmControl::sendDingTalkAlarmMessages(QString requestUrl, QString errorContent,QString cur_url)
{
    ShopControl *shop_control        = ControlManager::getInstance()->getShopControl();
    Utils4Qml *utils4qml             = Utils4Qml::getInstance();
    std::string dingdingUserID = "***********";
    QString storeInformation      = "店铺ID:"+QString::number(shop_control->getShopUnique()) + ";店铺名称:" +ControlManager::getInstance()->getShopControl()->getShopName()
            +";零售收银机版本:"+utils4qml->getVersion()+";收银机系统信息:Windows 7";
    QString url      = "https://oapi.dingtalk.com/robot/send?access_token=41159f8d1ab603b7e2fd8466bc82ff5314e543c30b4cdb199201faaae89f262d";

    Json body_map_message; //@ 请求接口 参数 店铺 设备信息
    body_map_message["content"]   = "**新版零售收银机接口报错**:\n\n【收银机信息】:"+storeInformation.toStdString()+"\n"+"【请求地址】:"+ cur_url.toStdString()+"\n"+"【请求参数】:"+ requestUrlParama.toStdString()+"\n"+"【报错原因】:"+ requestUrl.toStdString()+"\n"+"【报错内容】:"+errorContent.toStdString();
    Json json_body;
    json_body["msgtype"]          = "text";
    json_body["text"]             = body_map_message;
    // 添加at字段，指定@的人员和是否@所有人
    json_body["at"]               = {{"atMobiles", {dingdingUserID,"15376087225","15904964486"}}, {"isAtAll", false}};
    HttpClient *http_client = new HttpClient();

    auto &&request = http_client->post(url);
    request.bodyWithJson(json_body.dump());
    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    auto json_doc = Json::parse(data, nullptr, false);
                                    if (json_doc.is_discarded())
                                    {
//                                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "预警系统发送消息回调数据解析错误");
                                    }
                                    else
                                    {
//                                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "预警系统发送消息回调结果:{}", json_doc.dump());

                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    break;
                                }
                            }
                            http_client->deleteLater();
                        });
}
