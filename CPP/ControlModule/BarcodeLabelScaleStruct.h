﻿#ifndef BARCODELABELSCALESTRUCT_H
#define BARCODELABELSCALESTRUCT_H

#include <QObject>
#include <QQmlEngine>
#include <windows.h>
#include "Utils/Utils.h"
#include "json-qt.hpp"

///-------------------------------------------| 顶尖AI |-------------------------------------------
//
struct DviIn
{
    UINT32 ProtocolType;
    UINT32 Addr;
    UINT32 Port;
    UCHAR  name[16];
    UINT32 ID;
    UINT32 Version;
    BYTE   Country;
    BYTE   DepartmentID;
    BYTE   KeyType;
    UINT64 PrinterDot;
    LONG64 PrnStartDate;
    UINT32 LabelPage;
    UINT32 PrinterNo;
    USHORT PLUStorage;
    USHORT HotKeyCount;
    USHORT NutritionStorage;
    USHORT DiscountStorage;
    USHORT Note1Storage;
    USHORT Note2Storage;
    USHORT Note3Storage;
    USHORT Note4Storage;
    BYTE   stroge[177];
};

extern "C"
{
    typedef bool(CALLBACK *pAclasSDKInitialize)(char *s);
    typedef bool(CALLBACK *pGetDevicesInfo)(UINT32 Addr, UINT32 Port, UINT32 ProtocolType, DviIn *info);

    typedef void(WINAPI *FP)(UINT32 Eorrorcode, UINT32 index, UINT32 Total, char *userdata);

    typedef HANDLE(CALLBACK *pAclasSDKExecTask)(UINT32 Addr, UINT32 Port, UINT32 ProtocolType, UINT32 ProceType, UINT32 DataType, char *FileName, FP fp,
                                                char *uerdata);
    typedef HANDLE(CALLBACK *pAclasSDKWaitForTask)(HANDLE handle);

    typedef int(CALLBACK *pAclasSDKSyncExecTask)(char *Addr, UINT32 Port, UINT32 ProtocolType, UINT32 ProceType, UINT32 DataType, char *FileName);
}

void WINAPI ongress(UINT32 Eorrorcode, UINT32 index, UINT32 Total, char *userdata);

UINT MakehostToDword(char *host);
//
///-------------------------------------------| 顶尖AI |-------------------------------------------


class BarcodeLabelScaleEnum : public QObject
{
    Q_OBJECT
public:
    enum class WeighingScaleEnum : size_t
    {
        BARCODE_LABLE_SCALE_DAHUA = 10,   // 大华
        BARCODE_LABLE_SCALE_SHU_HENG,     // 数衡
        BARCODE_LABLE_SCALE_TOLEDO,       // 托利多
        BARCODE_LABLE_SCALE_DING_JIAN,    // 顶尖
        BARCODE_LABLE_SCALE_DING_JIAN_AI, // 顶尖AI
        BARCODE_LABLE_SCALE_TAI_HANG,     // 太航
        BARCODE_LABLE_SCALE_BALANCE       // 佰伦斯
    };
    Q_ENUM(WeighingScaleEnum)

    // 将当前类中枚举注册到QML
    static void declareQML()
    {
        qmlRegisterType<BarcodeLabelScaleEnum>("BarcodeScaleEnum", 1, 0, "BarcodeScaleEnum");
    }
};
using BarcodeScaleEnum = BarcodeLabelScaleEnum::WeighingScaleEnum;


struct NetInfoObj
{
    QString ip;
    int     port = -1;

    friend void from_json(const Json &json, NetInfoObj &obj)
    {
        obj.ip = getDataFromJson(json, "ip").toString();

        auto port_var = getDataFromJson(json, "port");
        obj.port      = (port_var.isNull() || !port_var.isValid()) ? -1 : port_var.toInt();
    }

    friend void to_json(Json &json, const NetInfoObj &obj)
    {
        json["ip"]   = obj.ip.toStdString();
        json["port"] = obj.port;
    }
};


struct BarcodeScaleItem
{
    BarcodeScaleEnum barcode_scale_type = BarcodeScaleEnum::BARCODE_LABLE_SCALE_DAHUA;

    NetInfoObj net_info;

    bool    is_enabled = true;
    QString timestamp  = Utils::DateTime::getTimestampMSecs();

    friend void from_json(const Json &json, BarcodeScaleItem &obj)
    {
        QVariant var_tmp;
        tryFromJson(json, "barcode_scale_type", var_tmp);
        obj.barcode_scale_type = static_cast<BarcodeScaleEnum>(var_tmp.toInt());
        tryFromJson(json, "barcode_scale_ip", var_tmp);

        if (json.contains("net_info"))
        {
            obj.net_info = json["net_info"].get<NetInfoObj>();
        }

        tryFromJson(json, "is_enabled", var_tmp);
        obj.is_enabled = var_tmp.toBool();
        tryFromJson(json, "timestamp", var_tmp);
        obj.timestamp = var_tmp.toString();
    }

    friend void to_json(Json &json, const BarcodeScaleItem &obj)
    {
        json["barcode_scale_type"] = obj.barcode_scale_type;
        json["net_info"]           = obj.net_info;
        json["is_enabled"]         = obj.is_enabled;
        json["timestamp"]          = obj.timestamp.toStdString();
    }
};

//ExecuteTaskInFile  以文件方式传入参数执行任务，Ansi版本，文件必须以UTF-8格式保存
//函数声明：
//	extern "C" bool __stdcall ExecuteTaskInFile(const char *szTaskID, const char *szInputFile, const char *szOutputFile, bool bSynch)
//传入参数：
//	szTaskID: String类型，传入的任务编号（唯一），Ansi编码
//	szInputFile: String类型，传入的任务信息文件名（全路径），Ansi编码
//	szOutputFile: String类型，需要生成的任务结果信息文件名（全路径），Ansi编码
//	bSynch：bool类型，执行方式，True为同步执行，即传输完毕后再返回，False为异步执行，即验证完输入参数正确性后就返回，后台自动执行任务。
//返回值：
//	bool类型，返回调用是否成功。
typedef bool (*ExecuteTaskInFileApi)(const char *, const char *, const char *, bool);

//QueryTask
//	查询指定的任务状态，以Xml字符串的方式传入参数，Ansi版本。
//函数声明：
//	extern "C" char *__stdcall QueryTask(const char *szInput)
//传入参数：
//	szInput: String类型，传入的Xml信息字符串，Xml结构参见《3 传输结构说明》，Ansi编码
//返回值：
//	String类型，返回指定的任务状态，以Xml形式体现，如果是批量查询多个任务，只返回已经存在的任务的状态，Ansi编码。
typedef char *(*QueryTaskApi)(const char *);


#endif // BARCODELABELSCALESTRUCT_H
