﻿#ifndef UPDATECTRL_H
#define UPDATECTRL_H

#include <QJSValue>
#include <QObject>
#include <map>
#include "Utils/Utils.h"
#include "json-qt.hpp"

constexpr const char *update_sign = "YXL-UPGRADE-XYL";

struct FileInfo
{
    std::string filePath;
    std::string fileMd5;

    bool base = false;

    unsigned long long fileSize = 0;

    friend void from_json(const nlohmann::json &json_j, FileInfo &json_obj)
    {
        json_obj.filePath = getDataFromJson(json_j, "filePath").toString().toStdString();
        json_obj.fileMd5  = getDataFromJson(json_j, "fileMd5").toString().toStdString();
        json_obj.base     = getDataFromJson(json_j, "base").toBool();
        json_obj.fileSize = getDataFromJson(json_j, "fileSize").toULongLong();
    }

    friend void to_json(nlohmann::json &json_j, const FileInfo &json_obj)
    {
        json_j["filePath"] = json_obj.filePath;
        json_j["fileMd5"]  = json_obj.fileMd5;
        json_j["base"]     = json_obj.base;
        json_j["fileSize"] = json_obj.fileSize;
    }
};

class UpdateCtrl : public QObject
{
    Q_OBJECT
    Q_PROPERTY(long file_download_total READ getDownloadTotal NOTIFY sigDownloadTotalChanged)
    Q_PROPERTY(long file_download_index READ getDownloadIndex NOTIFY sigDownloadIndexChanged)
    Q_PROPERTY(QString upgradeDetail READ getUpgradeDetail NOTIFY sigUpgradeDetailChanged)
    Q_PROPERTY(bool is_updating READ getIsUpdating NOTIFY sigIsUpdatingChanged)
    Q_PROPERTY(QString upgradeBrief READ upgradeBrief NOTIFY upgradeBriefChanged FINAL)
    Q_PROPERTY(bool isMaintenanceExist READ isMaintenanceExist WRITE setIsMaintenanceExist NOTIFY isMaintenanceExistChanged FINAL)

public:
    explicit UpdateCtrl(QObject *parent = nullptr);

    // 重置数据
    void resetData();

    // 获取sign
    QString getSign();

    // 获取更新信息
    Q_INVOKABLE void reqGetVersionInfo(QJSValue callback, bool is_auto_check = false);

    // 获取更新信息new
    Q_INVOKABLE void reqGetVersionInfoNew(QJSValue callback, bool is_auto_check = false);

    // 刷新本地文件列表
    void refreshLocalFileInfo(QString path = Utils::getAppDirPath(), QString prefix = "");

    // 开始更新
    Q_INVOKABLE void updateStart();

    // 检查更新程序
    void checkBuyhooMaintenance();

    // 通知服务器更新完毕
    void reqSendUpdateFinish();

    // 比较文件
    void compareFile();

    // 下载文件
    void downloadFile2(FileInfo file_info);

    // 保存更新信息到JSON
    bool saveUpdateInfo2Json();

    // 清空文件夹
    void clearDir(const QString path);

    // 启动更新程序
    void launchUpdateMaintenance();
    //检查更新程序是否存在
    bool isMaintenanceExist()
    {
        return is_maintenance_exist_;
    }
    void setIsMaintenanceExist(bool is_exist)
    {
        is_maintenance_exist_ = is_exist;
        emit isMaintenanceExistChanged();
    }
    unsigned long long file_download_total_      = 0;
    unsigned long long file_download_index_total = 0;
    unsigned long long file_download_index_cur_  = 0;

    const std::string update_folder_name = "update";

    long long logId_ = -1;

    std::string versionDownloadUrl_;
    std::string baseDownloadUrl_;


    QString upgradeDetail_;
    QString getUpgradeDetail()
    {
        return upgradeDetail_;
    }
    QString upgradeBrief_;
    QString upgradeBrief()
    {
        return upgradeBrief_;
    }

    bool    isForceUpdate_ = false;
    bool    isCanUpgrade_  = false;
    QString appVersion_;

    bool is_updating = false;

    void setIsUpdating(bool is_updating)
    {
        this->is_updating = is_updating;
        emit sigIsUpdatingChanged();
    }

    bool getIsUpdating()
    {
        return is_updating;
    }

    std::map<std::string, FileInfo> server_file_info_map_;
    std::map<std::string, FileInfo> local_file_info_map_;

    std::vector<FileInfo> update_files_;
    std::vector<FileInfo> update_files_downloading;

    std::vector<FileInfo> delete_files_;
    std::vector<FileInfo> equal_files_;

    std::vector<std::string> ignore_files_;

    // 检查下载进度
    void checkDownloadList();

    long getDownloadTotal()
    {
        return file_download_total_;
    }

    long getDownloadIndex()
    {
        return file_download_index_total + file_download_index_cur_;
    }
    bool is_maintenance_exist_ = true;
signals:
    void sigNeedUpgrade(bool is_force_update);
    void sigDownloadTotalChanged();
    void sigDownloadfailed();
    void sigDownloadIndexChanged();
    void sigUpgradeDetailChanged();
    void sigIsUpdatingChanged();
    void upgradeBriefChanged();
    void isMaintenanceExistChanged();
};

#endif // UPDATECTRL_H
