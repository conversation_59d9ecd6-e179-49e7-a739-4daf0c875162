﻿#include "MemberControl.h"
#include "ControlModule/ControlManager.h"
#include "LogManager.h"
#include "NetModule/HttpWorker.h"
#include "NetModule/NetGlobal.h"
#include "json-qt.hpp"
MemberControl::MemberControl(QObject *parent) : QObject{parent}
{
}
//模糊搜索会员信息
void MemberControl::reqFuzzyMemberInfo4Qml(QJSValue callback,QString cusMsg, QString searchType, QString valueTyp)
{
    auto cur_thread_p = req_fuzzy_member_info_thread;

    if (cur_thread_p)
        return;

    auto shop_control = ControlManager::getInstance()->getShopControl();


    QString url = req_host_prefix + "shopmanager/customer/queryShopCusList.do";
    bodyMap body_map;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    body_map["cusMsg"] = cusMsg;
    body_map["searchType"] = searchType;
    body_map["valueType"] = valueTyp;


    cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);
    url_encode_str         = url_encode_str;

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
                cur_thread_p = nullptr;
            });
    // 启动线程
    cur_thread_p->start();
}
void MemberControl::reqCusConRecordNingyuQml(QJSValue callback,QString member_unique,QString pageNum,QString pageSize,QString startDate,QString endDate,QString cusPhone)
{
    auto cur_thread_p = req_Cuscon_record_thread;

    if (cur_thread_p)
        return;
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/customer/queryNYCusConsumeList.do";
    bodyMap body_map;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    body_map["cusId"] = member_unique;
    body_map["cusPhone"] = cusPhone;
    body_map["pageNum"] = pageNum;
    body_map["pageSize"] = pageSize;
    body_map["startTime"] = startDate;
    body_map["endTime"] = endDate;
    cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);
    url_encode_str         = url_encode_str;

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
                cur_thread_p = nullptr;
            });
    // 启动线程
    cur_thread_p->start();
}
void MemberControl::reqCusConRecordQml(QJSValue callback,QString member_unique,QString pageNum,QString pageSize,QString cusType,QString startDate,QString endDate)
{
    auto cur_thread_p = req_Cuscon_record_thread;

    if (cur_thread_p)
        return;

    QString url = req_host_prefix + "harricane/customerCheckout/queryCusConRecord.do";
    bodyMap body_map;
    body_map["cusId"] = member_unique;
    body_map["pageNum"] = pageNum;
    body_map["pageSize"] = pageSize;
    body_map["cusType"] = cusType;
    body_map["startDate"] = startDate.mid(0,10);
    body_map["endDate"] = endDate.mid(0,10);
    cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);
    url_encode_str         = url_encode_str;

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
                cur_thread_p = nullptr;
            });
    // 启动线程
    cur_thread_p->start();
}
//搜索所有会员信息
void MemberControl::reqAllMemberInfo4Qml(QJSValue callback)
{
    auto cur_thread_p = req_member_info_thread;

    if (cur_thread_p)
        return;

    auto shop_control = ControlManager::getInstance()->getShopControl();


    QString url = req_host_prefix + "shopmanager/customer/queryShopCusList.do";
    bodyMap body_map;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    //    body_map["cusMsg"]     = "";


    cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);
    url_encode_str         = "cusMsg=&" + url_encode_str;

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

//#ifdef BUYHOO_PARSE_DEBUG
//                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
//#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
                cur_thread_p = nullptr;
            });
    // 启动线程
    cur_thread_p->start();
}
void MemberControl::reqMemberCusByIdForQml(QJSValue callback, QString serchedata, QString saleListTotal)
{
    auto cur_thread_p =  req_member_info_detail_thread;
    if (cur_thread_p)
        return;
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopUpdate/cuscheckout/findCusById.do";
    bodyMap body_map;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    body_map["cus_unique"]  = serchedata;
    cur_thread_p = new QThread(this); // 工作线程
    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);
    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();
                LOG_EVT_INFO("请求数据是否成功{}",is_succ);
                if (!Json::accept(reply_data_c))
                {
                    LOG_EVT_INFO("数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
#ifdef BUYHOO_PARSE_DEBUG
                     SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant tmp_var;
                    tryFromJson(json_doc, "status", tmp_var);
                    auto status = tmp_var.toInt();
                    LOG_EVT_INFO("回调传参{}",status);
                    if (status == 1)
                    {
                        LOG_EVT_INFO("回调传参");
                        if (callback.isCallable())
                        {
                            LOG_EVT_INFO("回调传参开始");
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                            LOG_EVT_INFO("回调传参结束");
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void MemberControl::reqMemberInfoDetail4Qml(QJSValue callback, QString member_unique)
{
    auto cur_thread_p = req_member_info_thread;

    if (cur_thread_p)
        return;

    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/customer/queryShopCusDetail.do";
    bodyMap body_map;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    body_map["cusUnique"]  = member_unique;

    cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant tmp_var;
                    tryFromJson(json_doc, "status", tmp_var);
                    auto status = tmp_var.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void MemberControl::reqCustomerState4Qml(QJSValue callback)
{
    auto cur_thread_p = req_member_info_thread;

    if (cur_thread_p)
        return;

    auto shop_control = ControlManager::getInstance()->getShopControl();
    //QString url = "http://test170.buyhoo.cc/shopmanager/customer/queryCustomerStat.do";
    QString url = req_host_prefix + "shopmanager/customer/queryCustomerStat.do";
    bodyMap body_map;
    //body_map["shopUnique"] = "1572591030884";
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());

    cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant tmp_var;
                    tryFromJson(json_doc, "status", tmp_var);
                    auto status = tmp_var.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void MemberControl::reqCustomerRealTimeStatQml(QJSValue callback)
{
    auto cur_thread_p = req_member_info_thread;

    if (cur_thread_p)
        return;

    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/customer/queryCustomerRealTimeStat.do";
    bodyMap body_map;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());

    cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant tmp_var;
                    tryFromJson(json_doc, "status", tmp_var);
                    auto status = tmp_var.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void MemberControl::reqAddMember4Qml(QJSValue callback, QString post_json)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/cuscheckout/addCus.do";

    Json post_json_doc = Json::parse(post_json.toUtf8().toStdString());

    post_json_doc.at("cusPassword") = Utils::Encrypt::str2Md5(QString::fromStdString(post_json_doc.at("cusPassword").get<std::string>())).toStdString();

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *cur_http_worker = new HttpWorker(url, HttpWorker::urlEncode(post_json_doc), "application/x-www-form-urlencoded");
    cur_http_worker->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http_worker, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http_worker, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());

                    QVariant tmp_var;
                    tryFromJson(json_doc, "status", tmp_var);
                    auto status = tmp_var.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        auto ret = callback.call(arglist);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void MemberControl::reqDeleteMember4Qml(QJSValue callback, QString member_unique)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/cuscheckout/deleteCus.do";
    bodyMap body_map;
    body_map["shop_unique"] = QString::number(shop_control->getShopUnique());
    body_map["cus_unique"]  = member_unique;

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(json_doc["status"] == 1);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                        auto ret = callback.call(arglist);
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        auto ret = callback.call(arglist);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void MemberControl::reqChangeMemberPointsByMemberId4Qml(QJSValue callback, QVariant member_id, QVariant changed_points)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/cuscheckout/updateCusPointsById.do";
    bodyMap body_map;
    body_map.insert("cusId", member_id.toString());

    auto changed_point = changed_points.toFloat();

    QString point_type;

    if (changed_point == 0)
        return;
    if (changed_point > 0)
    {
        point_type = "1";
        body_map.insert("cusPoints", changed_points.toString());
    }
    else if (changed_point < 0)
    {
        point_type = "2";
        body_map.insert("cusPoints", QString::number(fabs(changed_points.toFloat())));
    }
    body_map.insert("point_type", point_type);


    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *cur_http_worker = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    cur_http_worker->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http_worker, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http_worker, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    if (json_doc["status"] == 1)
                    {
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                    }
                    else
                    {
                        arglist.push_back(false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                    }
                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void MemberControl::reqChangeMemberInfoByMemberId4Qml(QJSValue callback, QVariant member_data_json)
{

    auto shop_ctrl = ControlManager::getInstance()->getShopControl();
    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    Json json_doc = Json::parse(member_data_json.toString().toStdString());


    QString url = req_host_prefix + "shopUpdate/cuscheckout/editCus.do";
    bodyMap body_map;

    QVariant var_tmp;


    body_map.insert("shopUnique", QString::number(shop_ctrl->getShopUnique()));

    tryFromJson(json_doc, "cusUnique", var_tmp);
    body_map.insert("cus_unique", var_tmp.toString());

    tryFromJson(json_doc, "cusName", var_tmp);
    body_map.insert("cusName", var_tmp.toString());

    tryFromJson(json_doc, "cusPhone", var_tmp);
    body_map.insert("cusPhone", var_tmp.toString());

    body_map.insert("cusRegeditDate", "");
    body_map.insert("cusBirthday", "");
    body_map.insert("cusType", "会,储");
    body_map.insert("cusAddress", "");
    body_map.insert("cusBalance", "0");

    tryFromJson(json_doc, "cusPassword", var_tmp);
    body_map.insert("cusPassword", Utils::Encrypt::str2Md5(var_tmp.toString()));

    body_map.insert("cusPoints", "0");

    tryFromJson(json_doc, "cus_remark", var_tmp);
    body_map.insert("cus_remark", var_tmp.toString());

    // TODO 会员状态1:正常 0:禁用
    //    body_map.insert("cus_status", "1");


    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *cur_http_worker = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    cur_http_worker->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http_worker, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http_worker, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    if (json_doc["status"] == 1)
                    {
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                    }
                    else
                    {
                        arglist.push_back(false);
                    }
                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void MemberControl::reqRegisterMember4Qml(QJSValue callback, QVariant member_data_json)
{
    auto shop_ctrl = ControlManager::getInstance()->getShopControl();
    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    Json json_doc = Json::parse(member_data_json.toString().toStdString());

    QString url = req_host_prefix + "harricane/cuscheckout/addCus.do";

    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_ctrl->getShopUnique()));
    body_map.insert("cusType", "会,储");
    body_map.insert("cusPoints", "0");

    QVariant tmp_variant;
    tryFromJson(json_doc, "cusName", tmp_variant);
    body_map.insert("cusName", tmp_variant.toString());

    tryFromJson(json_doc, "cusPassword", tmp_variant);
    body_map.insert("cusPassword", Utils::Encrypt::str2Md5(tmp_variant.toString()));

    tryFromJson(json_doc, "cusPhone", tmp_variant);
    body_map.insert("cusPhone", tmp_variant.toString());

    tryFromJson(json_doc, "cusBalance", tmp_variant);
    body_map.insert("cusBalance", tmp_variant.toString());

    tryFromJson(json_doc, "cusRegeditDate", tmp_variant);
    body_map.insert("cusRegeditDate", Utils::DateTime::getCurYearDateStr());

    tryFromJson(json_doc, "cusUnique", tmp_variant);
    body_map.insert("cusUnique", tmp_variant.toString());

    tryFromJson(json_doc, "cus_remark", tmp_variant);
    body_map.insert("cus_remark", tmp_variant.toString());

    tryFromJson(json_doc, "cusEmail", tmp_variant);
    body_map.insert("cusEmail", tmp_variant.toString());

    tryFromJson(json_doc, "vipEndTime", tmp_variant);
    body_map.insert("vipEndTime", tmp_variant.toString());

    tryFromJson(json_doc, "cusSex", tmp_variant);
    body_map.insert("cusSex", tmp_variant.toString());

    tryFromJson(json_doc, "password", tmp_variant);
    body_map.insert("password", tmp_variant.toString());


    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *cur_http_worker = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    cur_http_worker->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http_worker, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http_worker, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QJSValueList arglist;
                    if (json_doc["status"] == 0)
                    {
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                    }
                    else
                    {
                        arglist.push_back(false);
                    }
                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
