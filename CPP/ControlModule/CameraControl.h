﻿#ifndef CAMERACONTROL_H
#define CAMERACONTROL_H

#include <QCameraInfo>
#include <QObject>
#include <mutex>
#include "AdapterModule/Annotation.h"
#include "AdapterModule/ImageProvider.h"
#include "WorkerModule/CameraWorker.h"
#include "WorkerModule/CameraWorker2.h"
#include "qobjectdefs.h"

class CameraControl : public QObject
{
    Q_OBJECT

    Q_PROPERTY(QString camera4Face READ camera4Face WRITE setCamera4Face NOTIFY camera4FaceChanged FINAL)
    Q_PROPERTY(QString camera4Goods READ camera4Goods WRITE setCamera4Goods NOTIFY camera4GoodsChanged FINAL)

    Q_PROPERTY(bool isGoodsRecognition READ isGoodsRecognition WRITE setIsGoodsRecognition NOTIFY isGoodsRecognitionChanged FINAL)
    Q_PROPERTY(bool isOpeningGoodsCamera READ isOpeningGoodsCamera WRITE setIsOpeningGoodsCamera NOTIFY isOpeningGoodsCameraChanged FINAL)

    Q_PROPERTY(bool isGoodsCameraOpened READ isGoodsCameraOpened WRITE setIsGoodsCameraOpened NOTIFY isGoodsCameraOpenedChanged FINAL)

public:
    explicit CameraControl(QObject *parent = nullptr);
    ~CameraControl();

    void init();

    Q_INVOKABLE void openFaceCamera(int camera_index = 0);
    Q_INVOKABLE void closeFaceCamera();


    void openGoodsCamera();
    void closeGoodsCamera();

    CameraWorker2 *getCameraWorkerFace()
    {
        return camera_worker_face_;
    }

    QImage getLastOriginalImg();

    static void                refreshAvailableCameras();
    Q_INVOKABLE static QString getAvailableCameras();

    QString camera4Face();
    void    setCamera4Face(const QString &c_name);
    int     detected_num_ = 0;

    QString camera4Goods();
    void    setCamera4Goods(const QString &c_name);

    bool isGoodsRecognition();
    void setIsGoodsRecognition(bool is_goods_recognition);

    bool isOpeningGoodsCamera();
    void setIsOpeningGoodsCamera(bool is_opening);

    bool isGoodsCameraOpened();
    void setIsGoodsCameraOpened(bool is_open);

    static QImage  MatToQImage(const cv::Mat &mat);
    static cv::Mat QImageToMat(QImage image);

    ImageProvider *image_provider_face_    = nullptr;
    ImageProvider *image_provider_4_goods_ = nullptr;

    ImageProvider *image_provider_4_goods_selected_area_ = nullptr;

    Q_INVOKABLE void setRecognitionArea(int width, int height);
    QRect recognition_area_;

    Q_INVOKABLE void setAnnotation(Annotation* annotation);
    Annotation *annotation_ = nullptr;

    bool saveSelectedArea(cv::Mat src_img);
    bool getSelectedAreaMat(cv::Mat src_img,cv::Mat& out_img);

    Q_INVOKABLE void setSelectedArea();

    // QPointF getPointImgOffset();

#ifdef _RECOGNITION_
    // >=0稳定 <0不稳定
    static std::atomic_int goods_recognition_status_;
#endif

private:
    ///-------------------------------------------| 人脸识别 |-------------------------------------------
    //
    QThread       *camera_worker_face_thread_ = nullptr;
    CameraWorker2 *camera_worker_face_        = nullptr;

    std::mutex mutex_original_face_img_;
    QImage     last_original_face_img_;

    QString camera_4_face_;

    cv::CascadeClassifier face_cascade_;
    //
    ///-------------------------------------------| 人脸识别 |-------------------------------------------


    ///-------------------------------------------| 商品识别 |-------------------------------------------
    //
    QThread       *camera_worker_goods_thread_ = nullptr;
    CameraWorker2 *camera_worker_goods_        = nullptr;
    std::mutex     mutex_original_goods_img_;

    //QImage  last_original_goods_img_;
    QString camera_4_goods_;
    bool    is_goods_recognition_    = false;
    bool    is_opening_goods_camera_ = false;
    bool    is_goods_camera_opened_  = false;
    //
    ///-------------------------------------------| 商品识别 |-------------------------------------------

    static QList<QCameraInfo> available_cameras_;

signals:
    ///-------------------------------------------| 人脸识别 |-------------------------------------------
    //
    void sigOpenFaceCamera(QString camera_name, int interval = 42);
    void sigCloseFaceCamera();
    void sigSendFaceImg();
    void sigFaceDetected(int num);
    //
    ///-------------------------------------------| 人脸识别 |-------------------------------------------

    ///-------------------------------------------| 商品识别 |-------------------------------------------
    //
    void sigOpenGoodsCamera(QString camera_name, int interval = 0);
    void sigCloseGoodsCamera();
    void sigSendGoodsResult(std::vector<std::tuple<int, float>> result_list);
    // void sigRecognitionChange();
    void sigCaptureNow();
    //
    ///-------------------------------------------| 商品识别 |-------------------------------------------

    void camera4FaceChanged();
    void camera4GoodsChanged();

    void isGoodsRecognitionChanged();
    void isOpeningGoodsCameraChanged();


    void sigSendDebugList(QString debug_list);


    void goodsImgChanged();

    void isGoodsCameraOpenedChanged();
};

#endif // CAMERACONTROL_H
