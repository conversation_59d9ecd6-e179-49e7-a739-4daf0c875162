﻿#ifndef PAYMETHODCONTROL_H
#define PAYMETHODCONTROL_H

#include <QImage>
#include <QJSEngine>
#include <QJSValue>
#include <QObject>
#include <QTimer>
#include <cstdint>
#include <mutex>
#include "DataModule/OrderData.h"
#include "EnumTool.h"
#include "qimage.h"
#include "qjsvalue.h"
#include "NetModule/HttpCallback.h"

/*
    QML支付回调格式:
    arg1:支付状态 EnumTool::PayStatusEnum
    arg2:支付方式 EnumTool::PayMethodEnum
    arg3:其他数据 JSON String

应收: receivable_amount
实收: received_amount
找零: change_amount
订单号: order_unique

小程序优惠券:     mini_program_discount
小程序百货豆抵扣: mini_program_beans_use
小程序百货豆赠送: mini_program_beans_give
小程序余额:      mini_program_balance
小程序微信支付:   mini_program_wechat

*/

using PayEnum = EnumTool::PayMethodEnum;
class ShopCartList;

struct FacePayInfo
{
    bool is_need_pay = false;
    bool is_paying   = false;

    QString cusName;
    QString cusPhone;
    double  cus_balance = .0;
    QString cusUnique;

    QImage detect_img;
};

struct PayMethodStatus
{
    bool    is_enabled;
    PayEnum pay_enum;

    friend void from_json(const nlohmann::json &nlohmann_json_j, PayMethodStatus &nlohmann_json_t)
    {
        QVariant var_tmp;
        tryFromJson(nlohmann_json_j, "is_enabled", var_tmp);
        nlohmann_json_t.is_enabled = var_tmp.toBool();
        tryFromJson(nlohmann_json_j, "pay_enum", var_tmp);
        nlohmann_json_t.pay_enum = static_cast<PayEnum>(var_tmp.toInt());
    }

    friend void to_json(nlohmann::json &nlohmann_json_j, const PayMethodStatus &nlohmann_json_t)
    {
        nlohmann_json_j["is_enabled"] = nlohmann_json_t.is_enabled;
        nlohmann_json_j["pay_enum"]   = static_cast<int>(nlohmann_json_t.pay_enum);
    }
};

class PayMethodControl : public QObject
{
    Q_OBJECT

    Q_PROPERTY(bool is_need_face_pay READ getIsNeedFacePay WRITE setIsNeedFacePay NOTIFY sigIsNeedFacePayCHG)
    Q_PROPERTY(bool is_face_paying READ getIsFacePaying WRITE setIsFacePaying NOTIFY sigIsFacePayingCHG)

    Q_PROPERTY(QString payMethodList READ payMethodList WRITE setPayMethodList NOTIFY payMethodListChanged FINAL)

public:
    explicit PayMethodControl(QObject *parent = nullptr);

    //*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\

    void initCtrl();

    using CallbackPaymentStatus = std::function<void(int)>;

    void initPayMethodStatusList();
    /*!
     * \brief insertSaleListData
     * \param dataString
     * \param saleListUnique
     * \return
     */
    bool insertSaleListData(QString dataString,QString saleListUnique);
    /*!
     * \brief insertSaleListCombineData
     * \param body_map
     * \param saleListUnique
     * \return
     */
    bool insertSaleListCombineData(QMap<QString, QString> body_map, QString saleListUnique);
    /*!
     * \brief readTxtToQMap
     * \param filePath
     * \return
     */
    Q_INVOKABLE QMap<QString, QString> readTxtToQMap(const QString& filePath);
    const std::list<PayMethodStatus> &getPayMethodStatusList();

    Q_INVOKABLE QString getPayMethodStatusListJson();
    Q_INVOKABLE QString getEnabledPayMethodStatusListJson();

    Q_INVOKABLE void setPayMethodStatus(int pay_enum, bool is_enabled);
    void             setPayMethodStatus(PayEnum pay_enum, bool is_enabled);

    bool             getPayMethodStatus(PayEnum pay_enum);
    Q_INVOKABLE bool getPayMethodStatus(int pay_enum);

    QString getPayMethodStr(PayEnum pay_enum);

    Q_INVOKABLE bool savePayMethodStatusListByJson(QVariant json_data);
    void             FillPayMethodList();
    void             reFreshPayMethodList();

    ///-------------------------------------------| 通用 |-------------------------------------------
    //
    /*!
     * \brief payByPaymentCode 根据支付码自动选择支付方式
     * \param callback QML回调
     * \param payment_code 支付码
     */
    Q_INVOKABLE void payByPaymentCode(QJSValue callback, QString payment_code = "");

    /*!
     * \brief payByPayMethod 根据支付方式选择支付方式
     * \param callback QML回调
     * \param pay_method 支付方式枚举
     */
    Q_INVOKABLE void payByPayMethod(QJSValue callback, int pay_method);

    /*!
     * \brief cashPay4Qml QML现金支付
     * \param callback QML回调
     */
    Q_INVOKABLE void cashPay4Qml(QJSValue callback);

    /*!
     * \brief alipay4Qml 支付宝支付(线下)
     * \param callback QML回调
     */
    Q_INVOKABLE void alipay4Qml(QJSValue callback);

    /*!
     * \brief wechat4Qml 微信支付(线下)
     * \param callback QML回调
     */
    Q_INVOKABLE void wechat4Qml(QJSValue callback);

    /*!
     * \brief uploadOrder 上传未上传订单
     * \param callback QML回调
     */
    void uploadNotUploadOrder_v2(bool is_online_pay = false);
    /*!

    getDataFromUnuploadFiles()
    /*!
     * \brief uploadNotUploadOrder_offlineOrder 上传本地保存的离线订单
     */
    Q_INVOKABLE int uploadNotUploadOrder_offlineOrder();
    /*!
     * \brief sleepForSeconds
     * \param seconds
     */
    Q_INVOKABLE void sleepForSeconds(int seconds);
    /*!
     * \brief getUnuploadRecCount 获取未上传订单数量
     */
    Q_INVOKABLE int getUnuploadRecCount();
    /*!
     * \brief getDataFromUnuploadFiles 获取未上传订单详细信息
     * \return
     */
    Q_INVOKABLE QString getDataFromUnuploadFiles();
    /*!
     * \brief getNetStatus 获取网络状态
     * \return
     */
    Q_INVOKABLE QString getNetStatus();
    void uploadOrder_v2(ShopCartList *shop_cart_list, AeaQt::HttpClient *http_client, CppCallbackStd callback_std);

    void uploadNotUploadOrder_v2_CallBack(CallbackPaymentStatus callBackStatus);

    static std::mutex upload_order_mutex_v2_;


    Q_INVOKABLE void onlinePay4Qml(QJSValue callback, QString payment_code, PayMethodEnum pay_method_enum);
    void             checkOnlinePay();
    void             startCheckOnlinePay(QJSValue callback, QString sale_list_unique, PayMethodEnum pay_method_enum);
    Q_INVOKABLE void stopCheckOnlinePay();
    PayMethodEnum    pay_method_enum_online_ = PayMethodEnum::PAY_METHOD__WECHAT_ONLINE;
    QString          online_pay_order_;
    QTimer           timer_online_pay_;
    unsigned int     timer_online_pay_index_ = 0;
    QJSValue         online_pay_callback_;
    bool             is_stop_check_online_pay_ = true;


    Q_INVOKABLE void combinedOnlinePay(QJSValue callback, QVariant payment_code, QVariant online_pay, QVariant cash_pay);
    Q_INVOKABLE void combinedOnlineVipPay(QJSValue callback, QVariant payment_code, QVariant online_pay, QVariant cash_pay,QVariant total_pay);

    void             checkCombinedOnlinePay();
    void             checkCombinedOnlineVipPay();
    void             startCheckCombinedOnlinePay(QJSValue callback, QString sale_list_unique);
    void             startCheckCombinedOnlineVipPay(QJSValue callback, QString sale_list_unique);
    Q_INVOKABLE void stopCheckCombinedOnlinePay();
    Q_INVOKABLE void stopCheckCombinedOnlineVipPay();
    QString          combined_online_pay_order_;
    OrderInfo        combined_online_pay_order_orderInfo;
    QTimer           timer_combined_online_pay_;
    unsigned int     timer_combined_online_pay_index_ = 0;
    QJSValue         combined_online_pay_callback_;
    bool             is_stop_check_combined_online_pay_ = true;
    double           combined_online_pay_cash_          = .0;
    double           combined_online_pay_online_        = .0;
    double           combined_online_pay_total_        = .0;
    //组合付
    OrderInfo generateCombinedOnlinePayOrder(double online_pay, double cash_pay);
    //储值卡组合支付
    OrderInfo generateCombinedOnlineVipPayOrder(double online_pay, double cash_pay,double total_pay);
    //
    ///-------------------------------------------| 通用 |-------------------------------------------

    //*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\

    ///-------------------------------------------| 会员支付 |----------------------------------------
    //
    /*!
     * \brief payByMemberBalance 会员余额支付
     * \param callback QML回调
     * \param member_unique
     * \param pay_money
     * \return
     */
    Q_INVOKABLE void payByMemberBalance_v2(QJSValue callback, QVariant member_unique);
    /*!
     * \brief payByMemberBalance 宁宇会员余额支付
     * \param callback QML回调
     * \param member_unique
     * \param pay_money
     * \return
     */
    Q_INVOKABLE void payByMemberBalance_v2_ningyu(QJSValue callback, QVariant member_unique,QVariant sale_list_payment,QVariant storeAmount,QVariant cashAmount,QVariant jinQuan);

    /*!
     * \brief rechargeMemberByPaymentCode4Qml 会员充值 - 支付码支付
     * \param callback qml回调
     * \param payment_code 支付码
     * \param cusUnique 会员unique
     * \param money 金额
     * \return
     */
    Q_INVOKABLE void rechargeMemberByPaymentCode4Qml(QJSValue callback, QVariant payment_code, QVariant cusUnique, QVariant money);

    /*!
     * \brief rechargeMemberByPaymentCodeNingyuQml 宁宇会员充值 - 支付码支付
     * \param callback qml回调
     * \param payment_code 支付码
     * \param cusUnique 会员unique
     * \param money 金额
     * \return
     */
    Q_INVOKABLE void rechargeMemberByPaymentCodeNingyuQml(QJSValue callback, QVariant payment_code, QVariant cusUnique, QVariant money,QVariant give_money);
    void             checkRechargeMemberByPaymentCode();
    void             startCheckRechargeMember(QJSValue callback, QString sale_list_unique);
    void             stopCheckRechargeMember();
    QString          recharge_member_order_;
    QTimer           timer_recharge_member_pay_;
    unsigned int     timer_recharge_member_pay_index_ = 0;
    QJSValue         recharge_member_pay_callback_;
    bool             is_stop_check_recharge_member_pay_ = true;

    /*!
     * \brief rechargeMemberByCash4Qml 会员充值 - 现金支付
     * \param callback qml回调
     * \param cusUnique 会员unique
     * \param money 金额
     * \return
     */
    Q_INVOKABLE void rechargeMemberByCash4Qml(QJSValue callback, QVariant cusUnique, QVariant money);

    /*!
     * \brief rechargeMemberByCash4Qml 会员充值 - 退费
     * \param callback qml回调
     * \param cusUnique 会员unique
     * \param refundMoney 退费金额
     * \return
     */
    Q_INVOKABLE void reqRefundMemberByChange4Qml(QJSValue callback, QVariant cusUnique, QVariant money);
    /*!
     * \brief rechargeMemberByCash4Qml 宁宇会员充值 - 退费
     * \param callback qml回调
     * \param cusUnique 会员unique
     * \param refundMoney 退费金额
     * \return
     */
    Q_INVOKABLE void reqRefundMemberByChangeNingyuQml(QJSValue callback, QVariant cusUnique, QVariant money);
    /*!
     * \brief rechargeMemberByCash4Qml 会员充值 - 存零
     * \param callback qml回调
     * \param cusUnique 会员unique
     * \param money 金额
     * \return
     */
    Q_INVOKABLE void reqRechargeMemberByChange4Qml(QJSValue callback, QVariant cusUnique, QVariant money);
    std::mutex       mutex__recharge_member_by_change;

    /*!
     * \brief memberPointPay4Qml 会员积分支付(兑换)
     * \param callback
     * \return
     */
    Q_INVOKABLE void reqMemberPointExchange4Qml(QJSValue callback, QVariant member_unique, QVariant points_num, QVariant goods_barcode);

    /*!
     * \brief reqCombinedPay4Qml 组合支付
     * \param callback QML回调
     * \param money
     * \return
     */
    Q_INVOKABLE void reqCombinedPay4Qml(QJSValue callback, QVariant member_unique, QVariant pay_detail);

    //
    ///-------------------------------------------| 会员支付 |----------------------------------------

    //*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\

    ///-------------------------------------------| 人脸支付 |-------------------------------------------
    //

    // 根据摄像头最后一张人脸图片查找会员信息
    Q_INVOKABLE void getMemberInfoByLastFaceImg(QJSValue callback = 0, double saleListTotal = 0);

    /*!
     * \brief getMemberInfoByFaceImg 根据人脸图片查找会员信息
     * \param face_img 人脸图片
     */
    void getMemberInfoByFaceImg2(QJSValue callback, QImage face_img,double saleListTotal);

    /*!
     * \brief reqPayByOnlineMember 根据在线会员unique支付
     * \param callback QML回调
     * \param online_member_unique 在线会员unique
     */
    void reqPayByOnlineMember(QJSValue callback, QString online_member_unique);

    // 人脸支付信息
    FacePayInfo face_pay_info_;

    // 是否需要人脸支付
    bool getIsNeedFacePay();
    void setIsNeedFacePay(bool is_need = false);

    // 是否人脸支付中
    bool getIsFacePaying();
    void setIsFacePaying(bool is_paying = false);
    //
    ///-------------------------------------------| 人脸支付 |-------------------------------------------

    //*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/

    ///-------------------------------------------| 小程序支付 |-------------------------------------------
    //
    void payByMiniProgram_v2(QJSValue callback, QString payment_code = "");

    QJSValue callback_mini_program_pay_;  // QML回调
    QString  order_mini_program_pay_;     // 订单号
    QTimer   timer_mini_program_pay_;     // 订单检查定时器
    uint16_t index_mini_program_pay_ = 0; // 订单检查计数器

    void checkMiniProgramPay(); // 订单检查函数

    void querySaleListDetail(QJSValue callback, QString sale_list_unique);
    // 停止订单检查
    Q_INVOKABLE void stopCheckMiniProgramPay();
    //
    ///-------------------------------------------| 小程序支付 |-------------------------------------------

    //******************************************************************************************************************************

    ///-------------------------------------------| 工具 |-------------------------------------------
    //
    /*!
     * \brief getPayMethodByPaymentCode 根据支付码返回支付类型枚举
     * \param payment_code 支付码
     * \return 支付类型枚举
     */
    Q_INVOKABLE static PayEnum getPayMethodByPaymentCode(QString payment_code);

    /*!
     * \brief reqMiniProgramCode 请求小程序数据
     * \return
     */
    Q_INVOKABLE void     reqMiniProgramCode();
    QString              mini_program_img_url;
    Q_INVOKABLE QVariant getMiniProgramCodeUrl();


    void payFinishWizard(EnumTool::PayStatusEnum pay_status = EnumTool::PayStatusEnum::PAY_STATUS__SUCCESS,
                         EnumTool::PayMethodEnum pay_type   = EnumTool::PayMethodEnum::PAY_METHOD__UNKNOW);

    QJSValueList generateQmlCallbackArgList(EnumTool::PayStatusEnum pay_status, EnumTool::PayMethodEnum pay_method, QString json_detail = "");
    QJSValueList generateQmlCallbackArgList(EnumTool::PayStatusEnum pay_status, EnumTool::PayMethodEnum pay_method, std::string json_detail = "");

    Q_INVOKABLE QVariant getAllPayMethodJson();
    Q_INVOKABLE QVariant getValidPayMethodJson();
    Q_INVOKABLE QString  getValidPayMethodJson4Query();
    //
    ///-------------------------------------------| 工具 |-------------------------------------------

    //支付方式
    QString payMethodList();
    void    setPayMethodList(QString list_in);

    QVector<int> pay_method_list_;

    std::list<PayMethodStatus> pay_method_status_list_;

    std::map<PayEnum, QString> pay_method_kv_map_;

signals:
    void sigRefreshMiniProgramCode();
    void sigRefreshMemberInfo();
    void sigClearMemberInfo();
    //    void sigBackupOrderFinished();
    void sigCloseQRCode();

    void sigIsNeedFacePayCHG();
    void sigIsFacePayingCHG();

    void sigUnuploadRecCount();

    void payMethodListChanged();
};

#endif // PAYMETHODCONTROL_H
