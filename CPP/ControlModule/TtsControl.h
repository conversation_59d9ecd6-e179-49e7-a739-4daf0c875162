﻿#ifndef TTSCONTROL_H
#define TTSCONTROL_H

#include <QObject>
#include <QTextToSpeech>

class TtsControl : public QObject
{
    Q_OBJECT
public:
    explicit TtsControl(QObject *parent = nullptr);

    bool initTtsControl();

    void say(QString text);

    /*!
     * \brief getAvailableEngines 获取所有引擎
     * \return json
     */
    Q_INVOKABLE QVariant getAvailableEngines();
    Q_INVOKABLE bool     setCurEngineByName(QVariant engine_name);
    Q_INVOKABLE QVariant getCurEngineName();

    /*!
     * \brief getAvailableLanguage 获取所有语言
     * \return json
     */
    Q_INVOKABLE QVariant getAvailableLocales();
    Q_INVOKABLE bool     setCurLocaleByName(QVariant locale_name);
    Q_INVOKABLE QVariant getCurLocaleName();
    QString              getLocaleStr(const QLocale &locale);

    /*!
     * \brief getAvailableVoices 获取所有声音
     * \return json
     */
    Q_INVOKABLE QVariant getAvailableVoices();
    
    /*!
     * \brief setCurVoice 设置当前声音
     * \param voice
     */
    Q_INVOKABLE bool setCurVoiceByName(QVariant voice);

    /*!
     * \brief GetCurVoice 获取当前声音
     * \return 当前声音
     */
    Q_INVOKABLE QVariant getCurVoiceName();
    QString              getVoiceStr(const QVoice &voice);

    Q_INVOKABLE void testTts();

private:
    QString        cur_engine_name_ = "default";
    QTextToSpeech *speech_          = nullptr;
signals:
    void sigEngineChanged();
    void sigLocaleChanged();
    void sigVoiceChanged();
};

#endif // TTSCONTROL_H
