﻿#include "GoodsDataModel.h"
#include <algorithm>
#include <map>
#include <vector>
#include "ControlManager.h"
#include "DataModule/DataManager.h"
#include "DataModule/VirtualGoodsKindData.h"
#include "EnumTool.h"
#include "LogManager.h"

GoodsDataModel *GoodsDataModel::goods_data_model_             = nullptr;
GoodsDataModel *GoodsDataModel::goods_data_model_recognition_ = nullptr;


GoodsDataModel::GoodsDataModel(QObject *parent) : QAbstractListModel{parent}
{
}

int GoodsDataModel::rowCount(const QModelIndex &parent) const
{
    if (parent.isValid() || !goods_data_)
        return 0;

    if (reset_counter_ != 0)
        return 0;

    return goods_data_ptr_vec_.size();
}

QVariant GoodsDataModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || !goods_data_){
        return QVariant();
    }
    if (reset_counter_ < 0)
    {
        SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_evt_, "resetCounter为负 {}", reset_counter_);
    }

    if (reset_counter_ != 0){
        return QVariant();
    }
    if (goods_data_ptr_vec_.size() <= index.row()){
        return QVariant();
    }
    GoodsInfo *cur_goods_info = nullptr;
    int        index_loop     = 0;
    for (auto &goods_data : goods_data_ptr_vec_)
    {
        if (index_loop == index.row())
        {
            cur_goods_info = goods_data;
            break;
        }
        ++index_loop;
    }

    if (!cur_goods_info)
    {
        return QVariant();
    }
    auto shop_cart = ControlManager::getInstance()->getShopCartList();

    switch (role)
    {
    case GOODS_BARCODE_ROLE:
        return cur_goods_info->goods_barcode;
    case GOODS_IN_PRICE:
        return Utils::String::getClearNumStr(cur_goods_info->goods_in_price);
    case GOODS_SALE_PRICE_ROLE:
        return Utils::String::getClearNumStr(cur_goods_info->goods_sale_price);
    case GOODS_CUS_PRICE_ROLE:
        return Utils::String::getClearNumStr(cur_goods_info->goods_cus_price.toDouble());
    case GOODS_STANDARD_ROLE:
        return QVariant(cur_goods_info->goods_standard);
    case GOODS_NAME_ROLE:
    case GOODS_NAME_MEMBER_ROLE:
        return QVariant(cur_goods_info->goods_name);
    case GOODS_POINTS_ROLE:
        return QVariant(cur_goods_info->goods_points);
    case GOODS_CHENG_TYPE_ROLE:
        return QVariant(cur_goods_info->goodsChengType);
    case PC_SHELF_STATE_ROLE:
        return QVariant(cur_goods_info->pc_shelf_state);
    case GOODS_LIFE_ROLE:
        return QVariant(cur_goods_info->goods_life);
    case GOODS_ALIAS_ROLE:
        return QVariant(cur_goods_info->goods_alias);
    case GOODS_CONTAIN_ROLE:
        return QVariant(cur_goods_info->goods_contain);
    case GOODS_REMARKS_ROLE:
        return QVariant(cur_goods_info->goods_remarks);
    case GOODS_PROMOTION_ROLE:
        return QVariant(cur_goods_info->goods_promotion);
    case GOODS_BRAND_ROLE:
        return QVariant(cur_goods_info->goods_brand);
    case GOODS_COUNT_ROLE:
        return Utils::String::getClearNumStr(cur_goods_info->goods_count);
    case SHOP_CART_AMOUNT_ROLE:
        return shop_cart->getItemNumByBarcode(cur_goods_info->goods_barcode);
    }
    return QVariant();
}

bool GoodsDataModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    return false;
}

Qt::ItemFlags GoodsDataModel::flags(const QModelIndex &index) const
{
    if (!index.isValid())
        return Qt::NoItemFlags;

    return Qt::ItemIsEditable;
}

QHash<int, QByteArray> GoodsDataModel::roleNames() const
{
    QHash<int, QByteArray> names;
    names[GOODS_BARCODE_ROLE]    = "GOODS_BARCODE_ROLE";
    names[GOODS_IN_PRICE]        = "GOODS_IN_PRICE";
    names[GOODS_SALE_PRICE_ROLE] = "GOODS_SALE_PRICE_ROLE";
    names[GOODS_CUS_PRICE_ROLE]  = "GOODS_CUS_PRICE_ROLE";
    names[GOODS_STANDARD_ROLE]   = "GOODS_STANDARD_ROLE";
    names[GOODS_NAME_ROLE]       = "GOODS_NAME_ROLE";
    names[GOODS_POINTS_ROLE]     = "GOODS_POINTS_ROLE";
    names[GOODS_CHENG_TYPE_ROLE] = "GOODS_CHENG_TYPE_ROLE";
    names[PC_SHELF_STATE_ROLE]   = "PC_SHELF_STATE_ROLE";
    names[GOODS_LIFE_ROLE]       = "GOODS_LIFE_ROLE";
    names[GOODS_ALIAS_ROLE]      = "GOODS_ALIAS_ROLE";
    names[GOODS_CONTAIN_ROLE]    = "GOODS_CONTAIN_ROLE";
    names[GOODS_REMARKS_ROLE]    = "GOODS_REMARKS_ROLE";
    names[GOODS_PROMOTION_ROLE]  = "GOODS_PROMOTION_ROLE";
    names[GOODS_BRAND_ROLE]      = "GOODS_BRAND_ROLE";
    names[GOODS_COUNT_ROLE]      = "GOODS_COUNT_ROLE";
    names[SHOP_CART_AMOUNT_ROLE] = "SHOP_CART_AMOUNT_ROLE";
    return names;
}

int GoodsDataModel::getIndexByBarcode(QString goods_barcode)
{
    if (goods_barcode.isEmpty())
        return -1;

    bool is_exist = false;

    int index_goods_data_item = -1;
    for (auto &item_value : goods_data_ptr_vec_)
    {
        ++index_goods_data_item;
        if (item_value->goods_barcode == goods_barcode)
        {
            is_exist = true;
            break;
        }
    }
    if (!is_exist)
        return -1;

    return index_goods_data_item;
}

int GoodsDataModel::getRealCount() const
{
    int index_goods_data_item = 0;

    for (auto &goods_data_item : goods_data_ptr_vec_)
    {
        if (isDataValid(goods_data_item))
        {
            ++index_goods_data_item;
        }
    }

    return index_goods_data_item;
}

QVariant GoodsDataModel::getItemByIndex(QVariant index)
{
    Json result_json;

    if (goods_data_ptr_vec_.size() <= index.toUInt())
        return QVariant();

    unsigned int i = 0;
    for (auto &goods_data_item : goods_data_ptr_vec_)
    {
        if (i == index.toUInt())
        {
            result_json = *goods_data_item;
            break;
        }
        ++i;
    }

    return QString::fromStdString(result_json.dump());
}

GoodsData *GoodsDataModel::goodsData() const
{
    return goods_data_;
}

void GoodsDataModel::setGoodsData(GoodsData *goods_data)
{
    beginResetModel();

    if (goods_data_)
        goods_data_->disconnect(this);

    goods_data_ = goods_data;

    refreshGoodsPtrMap();
    if (goods_data_)
    {
        connect(goods_data_, &GoodsData::preItemInsert, this,
                [&](QString goods_barcode)
                {
                    if (reset_counter_ == 0)
                        beginResetModel();
                    ++reset_counter_;
                });

        connect(goods_data_, &GoodsData::postItemInserted, this,
                [&]()
                {
                    --reset_counter_;
                    if (reset_counter_ == 0)
                    {
                        refreshGoodsPtrMap();
                        endResetModel();
                    }
                });

        connect(goods_data_, &GoodsData::preItemRemoved, this,
                [&](QString goods_barcode)
                {
                    if (reset_counter_ == 0)
                        beginResetModel();
                    ++reset_counter_;
                });

        connect(goods_data_, &GoodsData::postItemRemoved, this,
                [&]()
                {
                    --reset_counter_;
                    if (reset_counter_ == 0)
                    {
                        refreshGoodsPtrMap();
                        endResetModel();
                    }
                });

        connect(goods_data_, &GoodsData::preItemReset, this,
                [&]()
                {
                    if (reset_counter_ == 0)
                        beginResetModel();
                    ++reset_counter_;
                });

        connect(goods_data_, &GoodsData::postItemReset, this,
                [&]()
                {
                    --reset_counter_;
                    if (reset_counter_ == 0)
                    {
                        refreshGoodsPtrMap();
                        endResetModel();
                    }
                });

        connect(goods_data_, &GoodsData::postRowChanged, this,
                [&](QString goods_barcode)
                {
            std::cout<<"sfsdfsdfsrfsfsfsdfs:"<<std::endl;
                    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

                    GoodsInfo goods_info;
                    if (goods_mgr->getGoodsByBarcode(goods_barcode, goods_info))
                    {
                        if (goods_kind_unique_ == goods_info.goods_kind_unique && !isGoodsExist(goods_barcode))
                        {
                            // 应当添加
                            beginResetModel();
                            refreshGoodsPtrMap();
                            endResetModel();
                        }
                        else if (goods_kind_unique_ != goods_info.goods_kind_unique && isGoodsExist(goods_barcode))
                        {
                            // 应当删除
                            beginResetModel();
                            refreshGoodsPtrMap();
                            endResetModel();
                        }
                        else
                        {
                            // 应当刷新
                            auto row = getIndexByBarcode(goods_barcode);
                            for (int i = 0; i < roleNames().size(); ++i)
                            {
                                // ERROR QT BUG 不能如期函数功能
                                dataChanged(index(row, i), index(row, i));
                            }
                        }
                    }
                });


        auto shop_cart = ControlManager::getInstance()->getShopCartList();
        connect(shop_cart, &ShopCartList::sigItemNumChanged, this,
                [&](QString barcode)
                {
                    auto goods_index = getIndexByBarcode(barcode);
                    if (goods_index == -1)
                        return;
                    for (int i = 0; i < roleNames().size(); ++i)
                    {
                        // ERROR QT BUG 不能如期函数功能
                        // 商品数量 index  16
                        dataChanged(index(goods_index, i), index(goods_index, i));
                    }
                });
        connect(shop_cart, &ShopCartList::preItemReset, this,
                [&]()
                {
                    beginResetModel();
                });
        connect(shop_cart, &ShopCartList::postItemReset, this,
                [&]()
                {
                    endResetModel();
                });

        auto goods_ctrl = ControlManager::getInstance()->getGoodsControl();
        connect(goods_ctrl, &GoodsControl::sigVGoodsKindGoodsChanged, this,
                [&]()
                {
                    refreshGoodsData();
                });
    }

    endResetModel();
}

QString GoodsDataModel::getSearchStr()
{
    return search_str_;
}

void GoodsDataModel::setSearchStr(QString search_str)
{
    if (search_str_ == search_str)
        return;

    if (Utils::Barcode::isEan13Valid(search_str) && search_str.left(2) == ControlManager::getInstance()->getBarcodeLabelScale()->barcode_weight_prefix_)
        search_str = search_str.left(7);

    beginResetModel();

    search_str_ = search_str;
    refreshGoodsPtrMap();
    endResetModel();

    emit sigSearchStrChanged();
}

QVariant GoodsDataModel::getGoodsKindUnique()
{
    return goods_kind_unique_;
}

void GoodsDataModel::setGoodsKindUnique(QVariant goods_kind_unique)
{
    if (goods_kind_unique_ == goods_kind_unique.toULongLong())
        return;

    beginResetModel();

    goods_kind_unique_ = goods_kind_unique.toULongLong();
    refreshGoodsPtrMap();
    endResetModel();
}

QVariant GoodsDataModel::getVGoodsKindUnique()
{
    return v_goods_kind_unique_;
}

void GoodsDataModel::setVGoodsKindUnique(QVariant v_goods_kind_unique)
{
    if (v_goods_kind_unique_ == v_goods_kind_unique.toULongLong())
        return;
    beginResetModel();

    search_str_          = "";
    LOG_EVT_INFO("刷新分类商品222333:{}",v_goods_kind_unique.toLongLong());
    v_goods_kind_unique_ = v_goods_kind_unique.toLongLong();
    refreshGoodsPtrMap();
    emit vGoodsKindUniqueChanged();

    endResetModel();
}

bool GoodsDataModel::isDataValid(GoodsInfo *goods_info) const
{
    if (!goods_info)
        return false;

    bool is_search_valid =
        (search_str_.isEmpty() ||
         (goods_info->goods_name.toUpper().contains(search_str_.toUpper()) || goods_info->goods_barcode.toUpper().contains(search_str_.toUpper()) ||
          goods_info->goods_alias.toUpper().contains(search_str_.toUpper())));

    bool is_goods_kind_valid = (goods_info->goods_kind_unique == goods_kind_unique_ || goods_kind_unique_ == 0);

    bool is_v_goods_kind_valid = (v_goods_kind_unique_ == 0);
    if (!is_v_goods_kind_valid)
    {
        auto &cur_v_goods_kind_map = DataManager::getInstance()->getVirtualGoodsKindMgr()->virtual_goods_kind_info_map_;
        for (const auto& pair : *cur_v_goods_kind_map)
        {
            //LOG_EVT_INFO("当前虚拟商品分类: {}", pair.first);
            const auto& barcode_vec = pair.second.goods_barode_vec_; // 注意这里修正了变量名
            for (const auto& barcode : barcode_vec)
            {
                //LOG_EVT_INFO("条形码: {}", barcode.toStdString()); // 假设barcode是一个可以转换为std::string的类型
            }
        }
        auto  iter_tmp             = cur_v_goods_kind_map->find(v_goods_kind_unique_);
        if (iter_tmp != cur_v_goods_kind_map->end())
        {
            const auto& barcode_vec = iter_tmp->second.goods_barode_vec_; // 注意修正了变量名
            for (const auto& barcode : barcode_vec)
            {
                 //LOG_EVT_INFO("迭代该分类下的条形码为:{}",barcode.toStdString());
            }
            auto ret_iter = std::find(iter_tmp->second.goods_barode_vec_.begin(), iter_tmp->second.goods_barode_vec_.end(), goods_info->goods_barcode);
            if (ret_iter != iter_tmp->second.goods_barode_vec_.end())
                is_v_goods_kind_valid = true;
        }
    }
    bool is_valid = (is_search_valid && is_goods_kind_valid && is_v_goods_kind_valid);

    return is_valid;
}

void GoodsDataModel::refreshGoodsData()
{
    beginResetModel();
    refreshGoodsPtrMap();
    endResetModel();
}

void GoodsDataModel::refreshGoodsPtrMap()
{
    if (goods_data_model_recognition_ == this)
        return;

    goods_data_ptr_vec_.clear();

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();
    // 是否使用无码商品
    if (v_goods_kind_unique_ == static_cast<unsigned long long>(EnumTool::UidEnum::ID_GOODS_KIND_COMMON) && goods_mgr->getIsShowNoCodeGoods())
    {
        //是否交换无码商品位置
        if (goods_mgr->isSwapNoCodeGoods())
        {
            for (auto iterator = goods_data_->goods_data_map_no_code_->rbegin(); iterator != goods_data_->goods_data_map_no_code_->rend(); ++iterator)
            {
                goods_data_ptr_vec_.emplace_back(&iterator->second);
            }
        }
        else
        {
            for (auto &[fst, snd] : *goods_data_->goods_data_map_no_code_)
            {
                goods_data_ptr_vec_.emplace_back(&snd);
            }
        }
    }
    for (auto &[item_barcode, item_data] : *goods_data_->getGoodsData())
    {
        //离线状态 获取本地的

        if (isDataValid(&item_data))
        {
            goods_data_ptr_vec_.emplace_back(&item_data);
        }
    };
    emit goodsCountChanged();

    setPageMaxNum(ceil(static_cast<float>(goods_data_ptr_vec_.size()) / (4 * 4)));
    setCurPageIndex(0);
}
#ifdef _RECOGNITION_

void GoodsDataModel::refreshGoodsPtrMapByRecognition(const std::vector<std::tuple<std::string, float>> &recognition_list)
{
    if (recognition_list.empty())
    {
        beginResetModel();

        goods_data_ptr_vec_.clear();

        setPageMaxNum(ceil(static_cast<float>(goods_data_ptr_vec_.size()) / (4 * 4)));
        setCurPageIndex(0);

        emit goodsCountChanged();
        endResetModel();
        return;
    }

    std::vector<GoodsInfo *> goods_data_ptr_vec;

    const auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    //tag 映射map
    auto &cur_tag_map = DataManager::getInstance()->getGoodsMgr()->tag_map_;

    //有效条码
    std::set<std::string> valid_barcode_list;

    //条码 + TAG&比例
    BarcodeTagRatioMap valid_barcode_tag_map;

    //根据匹配度的顺序插入
    for (std::tuple<std::string, float> recognition_item : recognition_list)
    {
        auto goods_tag_name = std::get<0>(recognition_item);

        if (auto tag_map_find_iter = cur_tag_map.find(goods_tag_name); tag_map_find_iter != cur_tag_map.end())
        {
            for (const auto &tag_item_barcode : tag_map_find_iter->second)
            {
                const auto ret_val = valid_barcode_list.insert(tag_item_barcode);

                //成功插入
                if (ret_val.second)
                {
                    valid_barcode_tag_map[tag_item_barcode].emplace_back(goods_tag_name, std::get<1>(recognition_item));
                }
            }
        }
    }

    goods_mgr->addRecognitionBarcodeTagRatioMap(valid_barcode_tag_map);

    std::vector<std::string> valid_barcode_list_final;

    for (auto cur_barcode_tag_ratio_map_iter = goods_mgr->barcode_tag_ratio_map_vec_.rbegin();
         cur_barcode_tag_ratio_map_iter != goods_mgr->barcode_tag_ratio_map_vec_.rend(); ++cur_barcode_tag_ratio_map_iter)
    {
        auto &cur_barcode_tag_ratio_map = *cur_barcode_tag_ratio_map_iter;

        for (auto &cur_barcode_tag_ratio_map_item : cur_barcode_tag_ratio_map)
        {
            auto ret_find = std::find(valid_barcode_list_final.begin(), valid_barcode_list_final.end(), cur_barcode_tag_ratio_map_item.first);

            if (ret_find != valid_barcode_list_final.end())
            {
                continue;
            }

            valid_barcode_list_final.push_back(cur_barcode_tag_ratio_map_item.first);
        }
    }

    for (const auto &valid_barcode : valid_barcode_list_final)
    {
        std::shared_ptr<goodsDataMap> goods_data = goods_data_->getGoodsData();

        auto find_iter = goods_data->find(QString::fromStdString(valid_barcode));

        if (find_iter != goods_data->end())
        {
            goods_data_ptr_vec.push_back(&find_iter->second);
        }
    }

    setPageMaxNum(ceil(static_cast<float>(goods_data_ptr_vec.size()) / (4 * 4)));
    setCurPageIndex(0);

    beginResetModel();
    std::swap(goods_data_ptr_vec_, goods_data_ptr_vec);
    emit goodsCountChanged();
    endResetModel();
}

void GoodsDataModel::refreshGoodsPtrMapByRecognition()
{
    std::vector<GoodsInfo *> goods_data_ptr_vec;

    const auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    std::set<std::string> valid_barcode_list_final;

    for (const auto &cur_barcode_tag_ratio_map : goods_mgr->barcode_tag_ratio_map_vec_)
    {
        for (auto &cur_barcode_tag_ratio_map_item : cur_barcode_tag_ratio_map)
        {
            valid_barcode_list_final.insert(cur_barcode_tag_ratio_map_item.first);
        }
    }

    for (const auto &valid_barcode : valid_barcode_list_final)
    {
        std::shared_ptr<goodsDataMap> goods_data = goods_data_->getGoodsData();

        auto find_iter = goods_data->find(QString::fromStdString(valid_barcode));

        if (find_iter != goods_data->end())
        {
            goods_data_ptr_vec.push_back(&find_iter->second);
        }
    }

    setPageMaxNum(ceil(static_cast<float>(goods_data_ptr_vec.size()) / (4 * 4)));
    setCurPageIndex(0);

    beginResetModel();
    std::swap(goods_data_ptr_vec_, goods_data_ptr_vec);
    emit goodsCountChanged();
    endResetModel();
}
#endif

bool GoodsDataModel::isGoodsExist(QString goods_barcode)
{
    auto goods_data_iter = std::find_if(goods_data_ptr_vec_.begin(), goods_data_ptr_vec_.end(),
                                        [=](GoodsInfo *goods_info)
                                        {
                                            return goods_info->goods_barcode == goods_barcode;
                                        });
    return goods_data_iter != goods_data_ptr_vec_.end();
}

long GoodsDataModel::curPageIndex() const
{
    return cur_page_index_;
}

void GoodsDataModel::setCurPageIndex(long index)
{
    cur_page_index_ = index;
    emit curPageIndexChanged();
}

long GoodsDataModel::pageMaxNum() const
{
    return page_max_num_;
}

void GoodsDataModel::setPageMaxNum(long max_num)
{
    page_max_num_ = max_num;
    emit pageMaxNumChanged();
}

void GoodsDataModel::set2Static()
{
    goods_data_model_ = this;
}

void GoodsDataModel::clearStatic()
{
    goods_data_model_ = nullptr;
}

void GoodsDataModel::set2Static4Recognition()
{
    beginResetModel();
    this->goods_data_ptr_vec_.clear();
    endResetModel();
    emit goodsCountChanged();
    goods_data_model_recognition_ = this;
}

void GoodsDataModel::clearStatic4Recognition()
{
    goods_data_model_recognition_ = nullptr;
}

void GoodsDataModel::clearGoods()
{
    beginResetModel();
    goods_data_ptr_vec_.clear();
    emit goodsCountChanged();
    endResetModel();
}

void GoodsDataModel::clearGoods4Recognition()
{
#ifdef _RECOGNITION_
    if (goods_data_ptr_vec_.empty())
        return;

    beginResetModel();

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    goods_mgr->barcode_tag_ratio_map_vec_.clear();
    goods_mgr->last_tag_ratio_vec_.clear();
    goods_data_ptr_vec_.clear();

    emit goodsCountChanged();
    endResetModel();
#endif
}

bool GoodsDataModel::isRecognition()
{
    return is_recognition_;
}

void GoodsDataModel::setIsRecognition(bool is_recognition)
{
    is_recognition_ = is_recognition;
    emit isRecognitionChanged();
}

long GoodsDataModel::goodsCount()
{
    return goods_data_ptr_vec_.size();
}
