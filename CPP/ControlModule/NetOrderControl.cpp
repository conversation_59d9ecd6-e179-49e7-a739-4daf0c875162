﻿#include "NetOrderControl.h"
#include "ControlModule/ControlManager.h"
#include "EnumTool.h"
#include "LogManager.h"
#include "NetModule/HttpWorker.h"
#include "NetModule/NetGlobal.h"
#include "qthread.h"

NetOrderControl::NetOrderControl(QObject *parent) : QObject{parent}
{
}

void NetOrderControl::getStoreOrdersRecord4Qml(QJSValue callback, int page_net_order_enum, QString begin_time, QString end_time)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    begin_time += " 00:00:00";
    end_time += " 23:59:59";

    QString url = req_host_prefix + "shopmanager/pc/pcQuerySaleList.do";
    bodyMap body_map;
    body_map["startTime"]             = begin_time;
    body_map["endTime"]               = end_time;
    body_map["shop_unique"]           = QString::number(shop_control->getShopUnique());
    body_map["flag"]                  = "true";
    body_map["sale_list_handlestate"] = QString ::number(page_net_order_enum);


    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void NetOrderControl::reqGetNetOrderCountNewByStatus(QJSValue callback, QVariant begin_time, QVariant end_time,QString orderType)
{

    auto shop_control = ControlManager::getInstance()->getShopControl();

    auto begin_time_str = begin_time.toString() + " 00:00:00";
    auto end_time_str   = end_time.toString() + " 23:59:59";

    QString url = req_host_prefix + "shopmanager/pc/selectOnlineOrderCountByStatus.do";  bodyMap body_map;
    body_map["startTime"]  = begin_time_str;
    body_map["endTime"]    = end_time_str;
    body_map["orderType"]   = orderType;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());

    auto cur_thread = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);
                    if (var_tmp.toInt() == 0)
                    {
                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                    else
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    cur_thread->start();
}
void NetOrderControl::reqGetNetOrderCountByStatus(QJSValue callback, QVariant begin_time, QVariant end_time)
{

    auto shop_control = ControlManager::getInstance()->getShopControl();

    auto begin_time_str = begin_time.toString() + " 00:00:00";
    auto end_time_str   = end_time.toString() + " 23:59:59";

    //QString url = req_host_prefix + "shopmanager/pc/selectOnlineOrderCountByStatus.do";
    QString url = req_host_prefix + "shopmanager/pc/getListCountByStatus.do";
    bodyMap body_map;
    body_map["startTime"]  = begin_time_str;
    body_map["endTime"]    = end_time_str;
    body_map["saleType"]   = "-1";
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());

    auto cur_thread = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);

                    if (var_tmp.toInt() == 1)
                    {
                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                    else
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    cur_thread->start();
}

void NetOrderControl::reqRefundRecord4Qml(QJSValue callback, QVariant record_status, QString begin_time, QString end_time)
{

    auto shop_control = ControlManager::getInstance()->getShopControl();

    begin_time += " 00:00:00";
    end_time += " 23:59:59";

    QString url = req_host_prefix + "shopUpdate/saleList/queryRetLists.do";
    bodyMap body_map;
    body_map["shopUnique"]         = QString::number(shop_control->getShopUnique());
    body_map["page"]               = "1";
    body_map["pageSize"]           = "999";
    body_map["retListHandlestate"] = record_status.toString(); // 订单处理状态：-1或不传：全部；1、待处理；3、处理完成；4、驳回
    body_map["startTime"]          = begin_time;
    body_map["endTime"]            = end_time;

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *cur_http_worker = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    cur_http_worker->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http_worker, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http_worker, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void NetOrderControl::reqRefundRecordDetail4Qml(QJSValue callback, QVariant record_unique)
{

    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/saleList/queryReturnDetail.do";
    bodyMap body_map;
    body_map["retListUnique"] = record_unique.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *cur_http_worker = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    cur_http_worker->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http_worker, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http_worker, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void NetOrderControl::reqRefundRecordStateModify(QJSValue callback, QVariant record_unique, QVariant handle_state, QVariant remark)
{

    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/saleList/modifyReturnMsg.do";
    bodyMap body_map;
    body_map["retListUnique"]      = record_unique.toString();
    body_map["retListHandlestate"] = handle_state.toString();
    body_map["staffId"]            = QString::number(shop_control->getUserId());
    body_map["macId"]              = Utils::getHostMacAddressWithoutColon();
    body_map["retListRemarks"]     = remark.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *cur_http_worker = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    cur_http_worker->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http_worker, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http_worker, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);

                    auto status = var_tmp.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

bool NetOrderControl::getStoreOrdersRecordDetail4Qml(QJSValue callback, QString sale_list_unique)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/app/shop/appQuerySaleListDetail.do";
    bodyMap body_map;
    body_map["shop_unique"]      = QString::number(shop_control->getShopUnique());
    body_map["sale_list_unique"] = sale_list_unique;

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_store_orders_record_detail = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_store_orders_record_detail->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_store_orders_record_detail, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_store_orders_record_detail, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_store_orders_record_detail, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_store_orders_record_detail, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();

    return true;
}

void NetOrderControl::getShopCourierList(QJSValue callback)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix_takeaway + "peisong/order/shopCourierList.do";
    bodyMap body_map;
    body_map["shop_unique"] = QString::number(shop_control->getShopUnique());

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *cur_http_worker = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    cur_http_worker->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, cur_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, cur_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http_worker, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http_worker, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void NetOrderControl::receivingTakeawayOrder(QJSValue callback, QVariant order_json)
{

    auto shop_control   = ControlManager::getInstance()->getShopControl();
    Json json_doc_order = Json::parse(order_json.toString().toStdString());

    QString url = req_host_prefix_takeaway + "peisong/createOrder.do";
    bodyMap body_map;
//    body_map["shop_unique"]       = QString::number(shop_control->getShopUnique()); //
    body_map["sale_list_cashier"] = QString::number(shop_control->getUserId());     // 收银员id
    body_map["goods_weight"]      = "0";                                            // 订单商品重量（kg）
    QVariant tmp_variant;
    tryFromJson(json_doc_order, "dispatching_type", tmp_variant);
    body_map["delivery_type"] = tmp_variant.toString();    // 配送方式 -1:到店自提 0：自配送 1：美团配送 2:一刻钟配送，不能为空
    tryFromJson(json_doc_order, "sale_list_unique", tmp_variant);
    body_map["sale_list_unique"] = tmp_variant.toString(); // 订单编号

    auto cur_thread_p = new QThread(this);                 // 工作线程

    HttpWorker *http_get_store_orders_record_detail = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_store_orders_record_detail->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_store_orders_record_detail, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_store_orders_record_detail, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_store_orders_record_detail, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_store_orders_record_detail, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant variant_tmp;
                    bool     is_succ = true;

                    if (tryFromJson(json_doc, "status", variant_tmp))
                    {

                        int status = variant_tmp.toInt();

                        if (status == 1)
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                            if (callback.isCallable())
                                auto ret = callback.call(arglist);
                        }
                        else
                        {
                            is_succ = false;
                        }
                    }
                    else
                    {
                        is_succ = false;
                    }

                    if (!is_succ)
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void NetOrderControl::receivingTakeawayOrder4SelfDispatch(QJSValue callback, QVariant order_json, QVariant courier_json)
{

    auto shop_control     = ControlManager::getInstance()->getShopControl();
    Json json_doc_order   = Json::parse(order_json.toString().toStdString());
    Json json_doc_courier = Json::parse(courier_json.toString().toStdString());

    QString url = req_host_prefix_takeaway + "peisong/createOrder.do";
    bodyMap body_map;
    body_map["shop_unique"]       = QString::number(shop_control->getShopUnique()); //
    body_map["sale_list_cashier"] = QString::number(shop_control->getUserId());     // 收银员id
    body_map["goods_weight"]      = "0";                                            // 订单商品重量（kg）
    body_map["delivery_type"]     = "0"; // 配送方式，0：自配送 1：美团配送 2:一刻钟配送，不能为空
    QVariant tmp_variant;
    tryFromJson(json_doc_courier, "shop_courier_id", tmp_variant);
    body_map["shop_courier_id"] = tmp_variant.toString();  // 自配送商家快递员id，可为空，自配送时不能为空
    tryFromJson(json_doc_courier, "courier_name", tmp_variant);
    body_map["courier_name"] = tmp_variant.toString();     // 配送员姓名，可为空，自配送时不能为空
    tryFromJson(json_doc_courier, "courier_phone", tmp_variant);
    body_map["courier_phone"] = tmp_variant.toString();    // 配送员电话，可为空，自配送时不能为空
    tryFromJson(json_doc_order, "sale_list_unique", tmp_variant);
    body_map["sale_list_unique"] = tmp_variant.toString(); // 订单编号

    auto cur_thread_p = new QThread(this);                 // 工作线程

    HttpWorker *http_get_store_orders_record_detail = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_store_orders_record_detail->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_store_orders_record_detail, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_store_orders_record_detail, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_store_orders_record_detail, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_store_orders_record_detail, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant variant_tmp;
                    bool     is_succ = true;

                    if (tryFromJson(json_doc, "status", variant_tmp))
                    {

                        int status = variant_tmp.toInt();

                        if (status == 1)
                        {
                            if (callback.isCallable())
                            {
                                QJSValueList arglist;
                                arglist.push_back(true);
                                arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                                auto ret = callback.call(arglist);
                            }
                        }
                        else
                        {
                            is_succ = false;
                        }
                    }
                    else
                    {
                        is_succ = false;
                    }

                    if (!is_succ)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void NetOrderControl::confirmReceiptOrder(QJSValue callback, QVariant order_unique)
{
    auto    shop_control = ControlManager::getInstance()->getShopControl();
    QString url          = req_host_prefix + "goBuy/my/confirmReceipt.do";
    bodyMap body_map;
    body_map["sale_list_unique"]  = order_unique.toString();
    body_map["sale_list_cashier"] = QString::number(shop_control->getUserId());


    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_store_orders_record_detail = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_store_orders_record_detail->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_store_orders_record_detail, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_store_orders_record_detail, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_store_orders_record_detail, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_store_orders_record_detail, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");

                    QJSValueList arglist;
                    arglist.push_back(false);

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant variant_tmp;
                    bool     is_succ = true;

                    if (tryFromJson(json_doc, "status", variant_tmp))
                    {

                        int status = variant_tmp.toInt();

                        if (status == 1)
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                            if (callback.isCallable())
                                auto ret = callback.call(arglist);
                        }
                        else
                        {
                            is_succ = false;
                        }
                    }
                    else
                    {
                        is_succ = false;
                    }

                    if (!is_succ)
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

QVariant NetOrderControl::getDispatchTypeByName(QVariant name)
{

    QString name_str = name.toString();
    if (name_str == "自配送")
    {
        return 0;
    }
    else if (name_str == "美团配送")
    {
        return 1;
    }
    else if (name_str == "一刻钟配送")
    {
        return 2;
    }
}

QVariant NetOrderControl::getDispatchNameByType(QVariant type)
{

    switch (type.toInt())
    {
    case -1:
        return "到店自提";
    case 0:
        return "自配送";
    case 1:
        return "美团配送";
    case 2:
        return "一刻钟配送";
    }
}
