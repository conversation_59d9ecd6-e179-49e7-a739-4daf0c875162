﻿#ifndef LOGCTRL_H
#define LOGCTRL_H

#include <QObject>
#include "qobjectdefs.h"

namespace miniz_cpp {
    class zip_file;
}


class LogCtrl : public QObject
{
    Q_OBJECT
public:
    explicit LogCtrl(QObject *parent = nullptr);

    //上传LOG ZIP 全流程
    Q_INVOKABLE void uploadLogZipWizard(QString start_date = "", QString end_date = "");

    //上传LOG ZIP
    bool reqUploadLogsZip();

    //压缩LOG
    bool compressionLogs(QString start_date = "", QString end_date = "");

    //获取LOG ZIP路径
    QString getLogZipPath();

    QString zip_start_date_;
    QString zip_end_date_;

    static void addFolder2Zip(miniz_cpp::zip_file &zip_f, QString path, QString prefix = "");

signals:
};

#endif // LOGCTRL_H
