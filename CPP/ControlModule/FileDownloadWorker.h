﻿#ifndef FILEDOWNLOADWORKER_H
#define FILEDOWNLOADWORKER_H

#include <QObject>

class FileDownloadWorker : public QObject
{
    Q_OBJECT
public:
    explicit FileDownloadWorker(std::vector<std::string> download_list, QObject *parent = nullptr);

    void process(); // 处理

    void stop()
    {
        is_stop_ = true;
    }

    bool is_stop_ = false;

    std::vector<std::string> download_list_original;
    std::vector<std::string> download_list;
    std::vector<std::string> downloading_list;
    std::string              update_folder_name = "update";

    std::string prefix;

    void downloadFile(std::string file_url);


signals:
    void sigDowloadFile(QString file_name);
    void sigDowloadFailedFile(QString file_name);
    void sigDowloadIndex(int index);
    void workStart(); // 工作开始
};

#endif // FILEDOWNLOADWORKER_H
