﻿#ifndef SHOPCARTCONTROL_H
#define SHOPCARTCONTROL_H

#include <DataModule/DataManager.h>
#include <QObject>
#include <QQmlEngine>
#include <QVector>
#include <Utils/Utils.h>
#include "EnumTool.h"
#include "qobjectdefs.h"
#include <QMutex>

constexpr int precision_shop_cart = 2;

enum class ShopCartPriceType : int
{
    CUS_PRICE = 0, // 自定义价格
    CUS_SUBTOTAL,  // 自定义小计
    CUS_DISCOUNT,  // 自定义折扣
};

/*!
 * \brief The ShopCartItem class 购物车存储信息
 */
class ShopCartItem
{
public:
    ShopCartItem();
    QString goods_barcode_ = ""; // 商品条码
    double  goods_num_     = 1;  // 数量
    QString goods_binding_unique = "" ;//捆绑Unique

    double cus_origin_price_    = .0;    // 自定义原价 用于无码商品
    bool   is_cus_origin_price_ = false; // 自定义原价?

    double cus_price_          = .0; // 自定义价格
    double cus_subtotal_       = .0; // 自定义小计
    double cus_discount_ratio_ = 1;  // 折扣比例

    ShopCartPriceType   price_type_ = ShopCartPriceType::CUS_DISCOUNT;

    bool is_barcode_scale_goods_ = false;

    QString add_date_time_; // 添加时间

    void operator=(const ShopCartItem &shop_cart_item)
    {
        goods_barcode_ = shop_cart_item.goods_barcode_;
        goods_num_     = shop_cart_item.goods_num_;
        cus_price_     = shop_cart_item.cus_price_;

        goods_binding_unique = shop_cart_item.goods_binding_unique;
        cus_origin_price_    = shop_cart_item.cus_origin_price_;
        is_cus_origin_price_ = shop_cart_item.is_cus_origin_price_;

        cus_subtotal_       = shop_cart_item.cus_subtotal_;
        cus_discount_ratio_ = shop_cart_item.cus_discount_ratio_;
        add_date_time_      = shop_cart_item.add_date_time_;

        price_type_             = shop_cart_item.price_type_;
        is_barcode_scale_goods_ = shop_cart_item.is_barcode_scale_goods_;
    }

    /*!
     * \brief resetPriceType 重置计价类型
     */
    void resetPriceType();

    /*!
     * \brief getIsWeight 是称重商品
     * \return 称重商品?
     */
    bool getIsWeight();

    /*!
     * \brief setCusDiscountRatio 设置折扣比例
     * \param ratio 折扣比例
     */
    void setCusDiscountRatio(double ratio);

    /*!
     * \brief setCusSubtotal 设置自定义小计
     * \param subtotal 自定义小计
     */
    void setCusSubtotal(double subtotal);
    /*!
     * \brief setCusSubAcctotal 累加
     * \param subtotal
     */
    void setCusSubAcctotal(double subtotal);
    /*!
     * \brief setCusPrice 设置自定义单价
     * \param price 自定义单价
     */
    void setCusPrice(double price);

    /*!
     * \brief setCusPrice 设置自定义原价
     * \param price 自定义原价
     */
    void setCusOriginPrice(double price);


    QString getGoodsName() const;

    /*!
     * \brief getPrice 获取商品折后价
     * \return 商品折后价
     */
    double getPrice() const;

    double getPrice(bool is_member) const;

    double getNum() const;
    double getNum(bool is_member) const;

    /*!
     * \brief getSubtotal 获取小计
     * \return 小计
     */
    double getSubtotal() const;
    double getSubtotal(bool is_member) const;

    /*!
     * \brief getSubtotal 获取无折扣小计
     * \return 无折扣小计
     */
    double getSubtotalNoDiscount() const;

    /*!
     * \brief getGoodsInfo 获取商品信息
     */
    GoodsInfo getGoodsInfo() const;


    friend void from_json(const nlohmann::json &json_item, ShopCartItem &obj_item)
    {
        obj_item.goods_barcode_          = getDataFromJson(json_item, "goods_barcode_").toString();
        obj_item.goods_binding_unique    = getDataFromJson(json_item, "goods_binding_unique").toString();
        obj_item.goods_num_              = getDataFromJson(json_item, "goods_num_").toDouble();
        obj_item.cus_origin_price_       = getDataFromJson(json_item, "cus_origin_price_").toDouble();
        obj_item.is_cus_origin_price_    = getDataFromJson(json_item, "is_cus_origin_price_").toBool();
        obj_item.cus_price_              = getDataFromJson(json_item, "cus_price_").toDouble();
        obj_item.cus_subtotal_           = getDataFromJson(json_item, "cus_subtotal_").toDouble();
        obj_item.cus_discount_ratio_     = getDataFromJson(json_item, "cus_discount_ratio_").toDouble();
        obj_item.price_type_             = static_cast<ShopCartPriceType>(getDataFromJson(json_item, "price_type_").toInt());
        obj_item.is_barcode_scale_goods_ = getDataFromJson(json_item, "price_type_").toBool();
        obj_item.add_date_time_          = getDataFromJson(json_item, "price_type_").toString();
    }

    friend void to_json(nlohmann::json &json_item, const ShopCartItem &obj_item)
    {
        json_item["goods_barcode_"]          = obj_item.goods_barcode_.toStdString();
        json_item["goods_binding_unique"]    = obj_item.goods_binding_unique.toStdString();
        json_item["goods_num_"]              = obj_item.goods_num_;
        json_item["cus_origin_price_"]       = obj_item.cus_origin_price_;
        json_item["is_cus_origin_price_"]    = obj_item.is_cus_origin_price_;
        json_item["cus_price_"]              = obj_item.cus_price_;
        json_item["cus_subtotal_"]           = obj_item.cus_subtotal_;
        json_item["cus_discount_ratio_"]     = obj_item.cus_discount_ratio_;
        json_item["price_type_"]             = static_cast<int>(obj_item.price_type_);
        json_item["is_barcode_scale_goods_"] = obj_item.is_barcode_scale_goods_;
        json_item["add_date_time_"]          = obj_item.add_date_time_.toStdString();
    }
};


struct shopCartListData
{
    QString            order_date_;
    QString            order_time_;
    QString            order_unique_;
    bool               is_cus_order_price_     = false;                            // 自定义价格?
    double             cus_order_price_        = .0;                               // 自定义价格
    WipeZeroTypeEnum   wipe_zero_type_         = WipeZeroTypeEnum::WIPE_ZERO__OFF; // 抹零类型
    bool               is_credit_              = false;                            // 赊账?
    double             cash_received_          = .0;                               // 收到现金 订单总金额
    unsigned long long payment                 = 0;                                // 支付方式
    QString            last_add_item_timestamp = "";                               // 最后添加元素时间戳 (用于高亮当前元素)
    QString            cash_method = "0";                               // 现金支付方式入口 0:现金菜单入口； 1:储值卡菜单
    double             recharge_change         = 0;                                // 存零金额

    QString member_unique  = ""; // 会员ID
    QString member_name    = ""; // 会员名
    QString member_balance = ""; // 会员余额
    QString member_point   = ""; // 会员积分

    bool is_paid = false; // 已支付?


    friend void from_json(const nlohmann::json &json_item, shopCartListData &obj_item)
    {
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "========================触发from_json============================：");
        obj_item.order_date_             = getDataFromJson(json_item, "order_date_").toString();
        obj_item.order_time_             = getDataFromJson(json_item, "order_time_").toString();
        obj_item.order_unique_           = getDataFromJson(json_item, "order_unique_").toString();
        obj_item.is_cus_order_price_     = getDataFromJson(json_item, "is_cus_order_price_").toBool();
        obj_item.cus_order_price_        = getDataFromJson(json_item, "cus_order_price_").toDouble();
        obj_item.wipe_zero_type_         = static_cast<WipeZeroTypeEnum>(getDataFromJson(json_item, "wipe_zero_type_").toInt());
        obj_item.is_credit_              = getDataFromJson(json_item, "is_credit_").toBool();
        obj_item.cash_received_          = getDataFromJson(json_item, "cash_received_").toDouble();
        obj_item.payment                 = getDataFromJson(json_item, "payment").toULongLong();
        obj_item.last_add_item_timestamp = getDataFromJson(json_item, "last_add_item_timestamp").toString();
        obj_item.recharge_change         = getDataFromJson(json_item, "recharge_change").toDouble();

        obj_item.member_unique  = getDataFromJson(json_item, "member_unique").toString();
        obj_item.member_name    = getDataFromJson(json_item, "member_name").toString();
        obj_item.member_balance = getDataFromJson(json_item, "member_balance").toString();
        obj_item.member_point   = getDataFromJson(json_item, "member_point").toString();
        obj_item.is_paid        = getDataFromJson(json_item, "is_paid").toBool();
    }

    friend void to_json(nlohmann::json &json_item, const shopCartListData &obj_item)
    {
        json_item["order_date_"]             = obj_item.order_date_.toStdString();
        json_item["order_time_"]             = obj_item.order_time_.toStdString();
        json_item["order_unique_"]           = obj_item.order_unique_.toStdString();
        json_item["is_cus_order_price_"]     = obj_item.is_cus_order_price_;
        json_item["cus_order_price_"]        = obj_item.cus_order_price_;
        json_item["wipe_zero_type_"]         = obj_item.wipe_zero_type_;
        json_item["is_credit_"]              = obj_item.is_credit_;
        json_item["cash_received_"]          = obj_item.cash_received_;
        json_item["payment"]                 = obj_item.payment;
        json_item["last_add_item_timestamp"] = obj_item.last_add_item_timestamp.toStdString();
        json_item["recharge_change"]         = obj_item.recharge_change;

        json_item["member_unique"]  = obj_item.member_unique.toStdString();
        json_item["member_name"]    = obj_item.member_name.toStdString();
        json_item["member_balance"] = obj_item.member_balance.toStdString();
        json_item["member_point"]   = obj_item.member_point.toStdString();
        json_item["is_paid"]        = obj_item.is_paid;
    }
};


class ShopCartList : public QObject
{
    Q_OBJECT

    Q_PROPERTY(QString memberUnique READ memberUnique WRITE setMemberUnique NOTIFY memberUniqueChanged FINAL)
    Q_PROPERTY(QString memberName READ memberName WRITE setMemberName NOTIFY memberNameChanged FINAL)
    Q_PROPERTY(QString memberBlance READ memberBlance WRITE setMemberBlance NOTIFY memberBlanceChanged FINAL)
    Q_PROPERTY(QString memberPoint READ memberPoint WRITE setMemberPoint NOTIFY memberPointChanged FINAL)
    Q_PROPERTY(QString cashMethod READ cashMethod WRITE setCashMethod NOTIFY cashMethodChanged FINAL)

    //高亮时间戳
    Q_PROPERTY(QString highlightTimeStamp READ highlightTimeStamp WRITE setHighlightTimeStamp NOTIFY highlightTimeStampChanged FINAL)

    //抹零方式
    Q_PROPERTY(int wipeZeroType READ wipeZeroType WRITE setWipeZeroType NOTIFY wipeZeroTypeChanged FINAL)

    //订单总计
    Q_PROPERTY(double orderTotal READ orderTotal NOTIFY orderTotalChanged FINAL)

    //订单无折扣总计
    Q_PROPERTY(double orderNoDiscountTotal READ orderNoDiscountTotal NOTIFY orderNoDiscountTotalChanged FINAL)

    //订单数量
    Q_PROPERTY(double orderQuantity READ orderQuantity NOTIFY orderQuantityChanged FINAL)
public:
    /*!
     * \brief ShopCartList 非主购物车请勿指定父类!!!
     * \param parent ControlManager
     */
    explicit ShopCartList(QObject *parent = nullptr);

    QVector<ShopCartItem> items() const;

    /*!
     * \brief clearAllInfo 重置所有信息
     */
    Q_INVOKABLE void resetAllInfo();

    /*!
     * \brief resetOrderInfo 初始化订单信息
     */
    void resetOrderInfo();

    /*!
     * \brief resetMemberInfo 重置会员信息
     */
    void resetMemberInfo();

    /*!
     * \brief getPendingOrderInfoJson 获取订单信息JSON
     * \return 订单信息JSON
     */
    QString getPendingOrderInfoJson();

    /*!
     * \brief getPendingOrderDetailInfoJson 获取订单详情JSON
     * \return 订单详情JSON
     */
    QString getPendingOrderDetailInfoJson();

    /*!
     * \brief sendRefreshSignal 刷新整个购物车
     */
    void sendRefreshSignal();

    /*!
     * \brief resetOrderUnique 重置订单ID
     */
    void resetOrderUnique();

    /*!
     * \brief getOrderUnique 获取订单ID
     * \return 订单ID
     */
    const QString getOrderUnique() const;

    //尝试重置已支付订单
    Q_INVOKABLE void tryResetPaidOrder();

    /*!
     * \brief setCurItemTimestamp 设置当前选中元素时间戳 (用于高亮当前元素)
     * \param timestamp 时间戳
     */
    Q_INVOKABLE void setCurItemTimestamp(QVariant timestamp);

    Q_INVOKABLE unsigned int getTimestampIndex(QVariant timestamp);

    /*!
     * \brief getMemberInfoJson 获取会员信息
     * \return 会员信息JSON
     */
    Q_INVOKABLE QString getMemberInfoJson();

    /*!
     * \brief setCurMemberUnique 设置当前会员
     * \param member_unique 会员unique
     * \param member_name 会员名
     * \param member_blance 会员余额
     * \param member_point 会员积分
     */
    Q_INVOKABLE void setCurMemberUnique(QVariant member_unique, QVariant member_name, QVariant member_blance, QVariant member_point);

    /*!
     * \brief clearMemberUnique 取消设置会员
     */
    Q_INVOKABLE void clearMemberUnique();

    /*!
     * \brief appendItemByBarcode 根据商品条码添加到购物车一个
     * \param goods_barcode 商品条码
     * \param is_force 未创建商品直接添加到购物车
     */
    Q_INVOKABLE void appendItemByBarcode(QString goods_barcode, bool is_force = false, bool is_add_2_tag = false,QString goods_binding_unique = "");
    /*!
     * \brief appendIndepItemByBarcode 将商品单行添加到购物车
     * \param goods_barcode
     * \param is_force
     * \param is_add_2_tag
     */
    Q_INVOKABLE void appendIndepItemByBarcode(QString goods_barcode,bool is_force = false, bool is_add_2_tag = false,QString goods_binding_unique = "");
    /*!
     * \brief appendGiftItemByBarcode 根据商品条码将满赠商品加入到购物车
     * \param goods_barcode 商品条码
     * \param is_force
     * \param is_add_2_tag
     * \param giftInfo 满赠商品信息
     */
    Q_INVOKABLE void appendGiftItemByBarcode(QString goods_barcode, bool is_force, bool is_add_2_tag, QString gift_num);
    /*!
     * \brief appendGiftItemByBarcode 根据商品条码将满减满赠商品加入到购物车
     * \param goods_barcode 商品条码
     * \param is_force
     * \param is_add_2_tag
     */
    Q_INVOKABLE void appendGiftDiscountItemByBarcode(QString goods_barcode, bool is_force, bool is_add_2_tag, QString giftInfo);

    Q_INVOKABLE void appendGiftItemOrderPromotionByBarcode(QString goods_barcode, bool is_force, bool is_add_2_tag, QString giftInfo);
    /*!
     * \brief appendNoCodeItemById 添加无码商品
     * \param goods_id 商品ID (999999998-无码称重  999999999-无码商品)
     * \param price
     * \param num
     */
    Q_INVOKABLE void appendNoCodeItemByBarcode(QVariant goods_barcode, QVariant price = 1, QVariant num = 1);

    /*!
     * \brief setItemPriceByBarcode 设置自定义商品价格
     * \param price 价格
     * \param date_time 时间戳
     */
    Q_INVOKABLE void setItemPriceByBarcode(QVariant goods_barcode, QVariant price, QVariant date_time);
    /*!
     * \brief setItemPriceNotimeByBarcode
     * \param goods_barcode
     * \param price
     */
    Q_INVOKABLE void setItemsubtotalNotimeByBarcode(QVariant goods_barcode, QVariant subtotal,QString goods_binding_unique);

    /*!
     * \brief setItemNumByBarcode 设置自定义商品数量
     * \param goods_barcode 商品条码
     * \param num 数量
     * \param date_time 时间戳
     */
    Q_INVOKABLE void setItemNumByBarcode(QVariant goods_barcode, QVariant num, QVariant date_time);
    /*!
     * \brief setItemNumNoTimeByBarcode
     * \param goods_barcode
     * \param num
     */
    Q_INVOKABLE void setItemNumNoTimeByBarcode(QVariant goods_barcode, QVariant num,QString goods_binding_unique);
    /*!
     * \brief setItemDiscountByBarcode 设置自定义折扣
     * \param goods_barcode 条码
     * \param discount 折扣
     * \param date_time 时间戳
     */
    Q_INVOKABLE void setItemDiscountByBarcode(QVariant goods_barcode, QVariant discount, QVariant date_time);

    /*!
     * \brief setItemSubtotalByBarcode 设置自定义小计
     * \param goods_barcode 条码
     * \param subtotal 小计
     * \param date_time 时间戳
     */
    Q_INVOKABLE void setItemSubtotalByBarcode(QVariant goods_barcode, QVariant subtotal, QVariant date_time);

    /*!
     * \brief setOrderDiscount 设置订单折扣
     * \param discount 折扣
     */
    Q_INVOKABLE void setOrderDiscount(QVariant discount);

    /*!
     * \brief removeItemById 根据商品ID从购物车删除一个
     * \param goods_id 商品ID
     */
    Q_INVOKABLE void removeItemById(QVariant goods_id, QVariant date_time);

    /*!
     * \brief getItemNumById 根据商品ID获取商品数量
     * \param goods_id 商品ID
     * \param is_weight 是否是称重商品
     * \return 商品数量
     */
    Q_INVOKABLE QVariant getItemNumByBarcode(QVariant goods_barcode);

    /*!
     * \brief clearAllGoods 清除所有商品
     */
    Q_INVOKABLE void clearAllGoods();

    Q_INVOKABLE QString getAllGoodsInfo();

    /*!
     * \brief clearAllGoods 根据商品ID清除所有商品
     */
    Q_INVOKABLE void clearGoodsByBarcode(QVariant goods_barcode, QVariant date_time);
    void clearGiftGoodsByBarcode(QVariant goods_barcode);


    /*!
     * \brief getTotalGoodsQuantity 获取商品数量和
     * \return 商品数量和
     */
    Q_INVOKABLE QVariant getTotalGoodsQuantity();

    /*!
     * \brief getTotalGoodsPrice 获取商品总价格
     * \return 商品总价格
     */
    Q_INVOKABLE double getTotalGoodsPrice();
    Q_INVOKABLE void handleDiscountsGift();

    /*!
     * \brief getFinalTotalGoodsPrice 获取最终价格
     * \return
     */
    Q_INVOKABLE double getFinalTotalGoodsPrice(int pay_method_enum = 0);
    // Q_INVOKABLE double getFinalTotalGoodsPrice(int pay_method_enum = (int)EnumTool::PayMethodEnum::PAY_METHOD__CASH);

    /*!
     * \brief getTotalGoodsPrice 获取商品无折扣总价格
     * \return 无折扣总价格
     */
    Q_INVOKABLE double getTotalGoodsPriceNoDiscount();

    /*!
     * \brief setCusOrderPrice 设置自定义订单价格
     * \param cus_order_price 自定义订单价格
     */
    Q_INVOKABLE void setCusOrderPrice(QVariant cus_order_price);

    /*!
     * \brief cancelCusOrderPrice 取消设置自定义订单价格
     */
    Q_INVOKABLE void cancelCusOrderPrice();

    /*!
     * \brief generateOrderInfoByShopCart 根据购物车生成订单信息
     * \param payment 支付方式 1-现金 2-支付宝 3-微信 4-银行卡  5-储值卡 6-美团外卖7-饿了么外卖 8-混合支付 9-免密支付 10-积分兑换 11-百货豆 12-拉卡拉
     * 13-易通付款码支付 14-合利宝刷卡
     * \param sync_type 是否已同步
     * \return 订单信息
     */
    OrderInfo generateOrderInfoByShopCart(int payment, int sync_type = 2);

    // 赊账
    Q_INVOKABLE void setIsCredit(bool is_credit);
    Q_INVOKABLE bool getIsCredit();

    // 存零
    Q_INVOKABLE void     setRechargeChangeAmount(QVariant amount);
    Q_INVOKABLE QVariant getRechargeChangeAmount();

    // 设置收到的现金
    Q_INVOKABLE void setReceivedCash(double cash_received);

    QVector<ShopCartItem> shop_cart_Items_;
    shopCartListData      shop_cart_list_data_;

    void setOrderPaid();

    ///-------------------------------------------| PROPERTY |-------------------------------------------
    //
    Q_INVOKABLE QString memberUnique();
    void    setMemberUnique(QString value_in);

    QString memberName();
    void    setMemberName(QString value_in);

    QString memberBlance();
    void    setMemberBlance(QString value_in);

    QString memberPoint();
    void    setMemberPoint(QString value_in);

    QString cashMethod();
    Q_INVOKABLE void    setCashMethod(QString value_in);

    QString highlightTimeStamp();
    void    setHighlightTimeStamp(QString timestamp);

    //抹零
    int  wipeZeroType();
    void setWipeZeroType(int wipe_zero_type);

    //订单总计
    double orderTotal();

    //订单无折扣总计
    double orderNoDiscountTotal();

    //订单数量
    double orderQuantity();
    //
    ///-------------------------------------------| PROPERTY |-------------------------------------------

signals:
    void memberUniqueChanged();
    void memberNameChanged();
    void memberBlanceChanged();
    void memberPointChanged();
    void cashMethodChanged();

signals:
    void preItemAppended();
    void postItemAppended();

    void preItemInsert(int index);
    void postItemInserted();

    void preItemRemoved(int index);
    void postItemRemoved();

    void preItemReset();
    void postItemReset();

    void postRowChanged(int row);

    void refreshOrderTotal();

    void sigGoodsTemp2Regular();

    void sigRemoveTempGoods();

    void sigInitState(); // 恢复副屏为默认状态

    void sigPayFinished(); // 副屏为结算完状态

    void sigClearMemberUnique(); // 清除UI会员

    void sigItemNumChanged(QString barcode);

    void highlightTimeStampChanged();

    void focusItem(unsigned int index);

    void wipeZeroTypeChanged();

    void orderTotalChanged();
    void orderNoDiscountTotalChanged();
    void orderQuantityChanged();

public:
    friend void from_json(const nlohmann::json &json_item, ShopCartList &obj_item)
    {
        QMutex mutex;
        if (json_item.contains("shop_cart_Items_"))
        {
            for (auto shop_cart_item : json_item["shop_cart_Items_"])
            {
                if(true){
                    QMutexLocker locker(&mutex);
                    obj_item.shop_cart_Items_.push_back(shop_cart_item.get<ShopCartItem>());
                }
            }
        }

        if (json_item.contains("shop_cart_list_data_"))
        {
            obj_item.shop_cart_list_data_ = json_item["shop_cart_list_data_"].get<shopCartListData>();
        }

        emit obj_item.memberUniqueChanged();
        emit obj_item.memberNameChanged();
        emit obj_item.memberBlanceChanged();
        emit obj_item.cashMethodChanged();
        emit obj_item.memberPointChanged();
    }

    friend void to_json(nlohmann::json &json_item, const ShopCartList &obj_item)
    {
        Json json_shop_cart_Items_;

        for (auto &shop_cart_item : obj_item.shop_cart_Items_)
        {
            json_shop_cart_Items_.push_back(shop_cart_item);
        }

        json_item["shop_cart_Items_"]     = json_shop_cart_Items_;
        json_item["shop_cart_list_data_"] = obj_item.shop_cart_list_data_;
    }
};

#endif // SHOPCARTCONTROL_H
