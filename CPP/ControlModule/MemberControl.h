﻿#ifndef MEMBERCONTROL_H
#define MEMBERCONTROL_H

#include <QJSValue>
#include <QObject>
#include <QVariant>

class MemberControl : public QObject
{
    Q_OBJECT
public:
    explicit MemberControl(QObject *parent = nullptr);

    Q_INVOKABLE void reqAllMemberInfo4Qml(QJSValue callback);
    QThread         *req_member_info_thread = nullptr;

    Q_INVOKABLE void reqFuzzyMemberInfo4Qml(QJSValue callback,QString cusMsg, QString searchType, QString valueTyp);
    QThread         *req_fuzzy_member_info_thread = nullptr;
    /*!
     * \brief reqCusConRecordQml
     * \param callback
     * \param member_unique
     * \param pageNum
     * \param pageSize
     * \param cusType
     * \param startDate
     * \param endDate
     */
    Q_INVOKABLE void reqCusConRecordQml(QJSValue callback,QString member_unique,QString pageNum,QString pageSize,QString cusType,QString startDate,QString endDate);
    /*!
     * \brief reqCusConRecordNingyuQml
     * \param callback
     * \param member_unique
     * \param pageNum
     * \param pageSize
     * \param cusType
     * \param startDate
     * \param endDate
     * \param cusPhone
     */
    Q_INVOKABLE void reqCusConRecordNingyuQml(QJSValue callback,QString member_unique,QString pageNum,QString pageSize,QString startDate,QString endDate,QString cusPhone);
    QThread         *req_Cuscon_record_thread = nullptr;
     /*!
     * \brief reqCustomerState4Qml 查询会员统计信息
     * \param callback
     */
    Q_INVOKABLE void reqCustomerState4Qml(QJSValue callback);
     /*!
     * \brief reqCustomerRealTimeStatQml 查询会员到店实时消费
     * \param callback
     */
    Q_INVOKABLE void reqCustomerRealTimeStatQml(QJSValue callback);

    Q_INVOKABLE void reqMemberInfoDetail4Qml(QJSValue callback, QString member_unique);
    QThread         *req_member_info_detail_thread = nullptr;


    Q_INVOKABLE void reqMemberCusByIdForQml(QJSValue callback, QString serchedata, QString saleListTotal);
    QThread         *req_member_info_byid_thread = nullptr;

    Q_INVOKABLE void reqAddMember4Qml(QJSValue callback, QString post_json);
    QThread         *req_add_member_thread = nullptr;

    Q_INVOKABLE void reqDeleteMember4Qml(QJSValue callback, QString member_unique);
    QThread         *req_delete_member_thread = nullptr;

    Q_INVOKABLE void reqChangeMemberPointsByMemberId4Qml(QJSValue callback, QVariant member_id, QVariant changed_points);

    Q_INVOKABLE void reqChangeMemberInfoByMemberId4Qml(QJSValue callback, QVariant member_data_json);
 
    Q_INVOKABLE void reqRegisterMember4Qml(QJSValue callback, QVariant member_data_json);

signals:
};

#endif // MEMBERCONTROL_H
