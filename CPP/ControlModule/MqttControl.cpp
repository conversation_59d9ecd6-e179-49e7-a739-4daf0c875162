﻿#include "MqttControl.h"
#include <QDateTime>
#include <QString>
#include <gist/ThreadRAII.h>
#include <string>
#include "ControlManager.h"
#include "LogManager.h"
#include "Utils/Utils.h"
#include "Version.h"
#include "json-qt.hpp"
#include <iostream>
#include <typeinfo>
#include "ControlModule/PrinterControl.h"

using namespace std;

MqttControl::MqttControl(const QString &host, const quint16 port, QObject *parent) : QMqttClient{parent}
{
    connect(this, &MqttControl::connected, this, &MqttControl::onConnected);
    connect(this, &MqttControl::disconnected, this, &MqttControl::onDisconnected);
    connect(this, &MqttControl::messageReceived, this, &MqttControl::onReceived);

    timer_reconn.setInterval(13000);

    connect(&timer_reconn, &QTimer::timeout,
            [&]()
            {
                LOG_EVT_WARN(" Re-connecting...... B");

                setCleanSession(false);
                connectToHost();

                LOG_EVT_WARN(" Re-connecting...... E");
            });

    setClientId("Market" + Utils::getHostMacAddressWithoutColon());

    setHostname(host);
    setPort(port);

    setKeepAlive(3);

    setWillTopic(TOPIC_MQTT);

    Json willMsg;
    willMsg = {
        {"ctrl", "msg_shutdown"},
        {"ID", Utils::getHostMacAddressWithoutColon().toUtf8().toStdString()},
        {"version", VERSION_NUM},
    };
    setWillMessage(willMsg.dump().c_str());
    setCleanSession(true);
    setWillQoS(2);
}

MqttControl::~MqttControl()
{
}

void MqttControl::onConnected()
{
    LOG_EVT_INFO("MQTT onConnected");

    timer_reconn.stop();

    auto subscription = subscribe(getTopic(), 2);

    if (!subscription)
    {
        LOG_EVT_ERROR("subscription {} error", getTopic().toStdString());
    }
    else
    {
        LOG_EVT_INFO("subscription {} succ", getTopic().toStdString());

        auto shop_control = ControlManager::getInstance()->getShopControl();

        Json message_pub = {
            {"ctrl", "msg_init"}, {"ID", Utils::getHostMacAddressWithoutColon().toStdString()},          {"status", 200}, {"errcode", 0}, {"msg", "success"},
            {"count", 1},         {"data", {{"shop_unique", to_string(shop_control->getShopUnique())}}},
        };

        publish(TOPIC_MQTT, message_pub.dump().c_str());
    }
}

void MqttControl::onDisconnected()
{
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT onDisconnected");
    brokerDisconnected();
}

void MqttControl::onTimeout()
{
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT onTimeout");
}

void MqttControl::onSubscribed(const QString &topic)
{
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT onSubscribed {}", topic.toStdString());
}

void MqttControl::onUnSubscribed(const QString &topic)
{
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT onUnSubscribed {}", topic.toStdString());
}

void MqttControl::onReceived(const QByteArray &message, const QMqttTopicName &topic)
{
    std::string received_str = message.toStdString();

    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT onReceived {}", received_str);

    // status 200-成功 400-失败
    Json json_doc = Json::parse(received_str, nullptr, false);
    if((json_doc["ctrl"] == "msg_net_refund") ||(json_doc["ctrl"] == "msg_net_new" ) ){
        if(json_doc["data"].size() == 0|| json_doc["data"].is_null()){
            LOG_EVT_ERROR("MQTT 数据格式有误!");
            return;
        }
        PrinterControl  *printer_control= new PrinterControl();
        Json json_doc_data = json_doc["data"];
        string ctrl_str = json_doc["ctrl"];

        auto goods_kind_mgr = DataManager::getInstance()->getGoodsKindMgr();
        auto goods_mgr      = DataManager::getInstance()->getGoodsMgr();
        LOG_EVT_ERROR("================mqtt消息到来:{}",ctrl_str);
        if (ctrl_str == "msg_net_new")
        {
            LOG_EVT_INFO("MQTT msg_net_new   新网单到来");
            if ( (json_doc_data.size() && json_doc_data.is_null()))
                return;
            emit sigMqttRefreshOrderCountByStatus4Qml();
            //语音
            if(ConfigTool::getInstance()->isPlayMiniProgramTakeOrders() == true){
                ControlManager::getInstance()->getTtsControl()->say("您有网单请及时处理");
            }
            if(ConfigTool::getInstance()->getSetting(ConfigEnum::IS_AUTO_TAKE_ORDERS_AND_PRINT) == true){
                printer_control->printNetOrder(QVariant(QString::fromStdString(json_doc.dump())));
            }
        }
        else if (ctrl_str == "msg_net_refund")
        {
            LOG_EVT_INFO("MQTT msg_net_refund   新的退款单到来");
            if ( (json_doc_data.size() && json_doc_data.is_null()))
                return;        
            emit sigMqttRefreshOrderCountByStatus4Qml();
            printer_control->printRefundNetOrder(QVariant(QString::fromStdString(json_doc.dump())));
        }
        //printer_control->deleteLater();
    }
    else{
        if (json_doc.is_discarded() || !json_doc["data"].is_array() || json_doc["data"][0].is_null() || json_doc["data"].size() == 0)
        {
                LOG_EVT_ERROR("MQTT 数据格式有误!");
                return;
        }

        Json json_doc_data = json_doc["data"];

        string ctrl_str = json_doc["ctrl"];

        auto goods_kind_mgr = DataManager::getInstance()->getGoodsKindMgr();
        auto goods_mgr      = DataManager::getInstance()->getGoodsMgr();
        LOG_EVT_ERROR("================mqtt消息到来:{}",ctrl_str);
        if (ctrl_str == "msg_init")
        {
            // 收银机开机
        }
        else if (ctrl_str == "msg_shutdown")
        {
            // 收银机关机
        }
        else if (ctrl_str == "msg_pay_ok")
        {
            // 支付成功
        }
        else if (ctrl_str == "msg_pay_error")
        {
            // 支付失败
        }
        else if (ctrl_str == "msg_goods_kind_update")
        {
            // 商品分类变更
        }
        else if (ctrl_str == "msg_goods_kind_add")
        {
            // 商品分类-新增
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_goods_kind_add 商品分类-新增");

            if (!json_doc_data.is_array() || (json_doc_data.size() && json_doc_data[0].is_null()))
                return;

            for (auto &cur_it : json_doc_data)
            {
                GoodsKindInfo goods_kind_info;
                goods_kind_info.shop_unique          = cur_it["shop_unique"].get<unsigned long long>();
                goods_kind_info.same_type            = cur_it["same_type"];
                goods_kind_info.goods_kind_name      = QString::fromStdString(cur_it["goods_kind_name"]);
                goods_kind_info.goods_kind_order     = cur_it["goods_kind_order"];
                goods_kind_info.valid_type           = cur_it["valid_type"];
                goods_kind_info.goods_kind_id        = cur_it["goods_kind_id"];
                goods_kind_info.goods_kind_parunique = cur_it["goods_kind_parunique"];
                goods_kind_info.edit_type            = cur_it["edit_type"];
                goods_kind_info.goods_kind_picture   = QString::fromStdString(cur_it["goods_kind_picture"]);
                goods_kind_info.goods_kind_unique    = cur_it["goods_kind_unique"];
                goods_kind_info.goods_kind_alias     = QString::fromStdString(cur_it["goods_kind_alias"]);
                goods_kind_info.kind_type            = cur_it["kind_type"];

                if (goods_kind_mgr->addGoodsKind(goods_kind_info))
                {
                    bool is_parent_goods_kind = goods_kind_info.goods_kind_parunique == 0;
                    emit sigMqttAddGoodsKind4Qml(is_parent_goods_kind, QString::number(goods_kind_info.goods_kind_unique));
                }
            }
        }
        else if (ctrl_str == "msg_goods_kind_delete")
        {
            // 商品分类-删除
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_goods_kind_delete 商品分类-删除");

            if (!json_doc_data.is_array() || (json_doc_data.size() && json_doc_data[0].is_null()))
                return;

            for (auto &cur_it : json_doc_data)
            {
                QVariant var_tmp;
                tryFromJson(cur_it, "goods_kind_unique", var_tmp);
                QString goods_kind_unique_str = var_tmp.toString();
                tryFromJson(cur_it, "goods_kind_parunique", var_tmp);
                bool is_parent_goods_kind = var_tmp.toInt() == 0;

                if (goods_kind_mgr->deleteGoodsKind(goods_kind_unique_str))
                    emit sigMqttDelGoodsKind4Qml(is_parent_goods_kind, goods_kind_unique_str);
            }
        }
        else if (ctrl_str == "msg_goods_update")
        {
            // 	商品更新
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_goods_update");

            if (!json_doc_data.is_array() || (json_doc_data.size() && json_doc_data[0].is_null()))
                return;

            auto shop_cart_list = ControlManager::getInstance()->getShopCartList();

            for (auto iter1 = json_doc_data.rbegin(); iter1 != json_doc_data.rend(); ++iter1)
            {
                auto &cur_item = *iter1;

                GoodsInfo goods_info;

                QVariant variant_tmp;

                tryFromJson(cur_item, "goods_name", variant_tmp);
                goods_info.goods_name = variant_tmp.toString();

                tryFromJson(cur_item, "shop_unique", variant_tmp);
                goods_info.shop_unique = variant_tmp.toULongLong();

                // 校验mqtt消息中的shop_unique是否与当前店铺相同。不同则忽略词条消息。
                auto               shop_control  = ControlManager::getInstance()->getShopControl();
                unsigned long long m_shop_unique = shop_control->getShopUnique();
                if (m_shop_unique != goods_info.shop_unique)
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_,
                                       "当前店铺的shopUnique为" + QString::number(m_shop_unique).toStdString() + "。此MQTT消息与当前店铺无关,忽略!!!");
                    return;
                }

                //tryFromJson(cur_item, "pc_shelf_state", variant_tmp);
                if(tryFromJson(cur_item, "pc_shelf_state", variant_tmp)){
                   goods_info.pc_shelf_state = variant_tmp.toULongLong();
                }
                if(tryFromJson(cur_item, "shelf_state", variant_tmp)){
                   goods_info.shelfState = variant_tmp.toULongLong();
                }
                if(tryFromJson(cur_item, "foreign_key", variant_tmp)){
                   goods_info.foreign_key = variant_tmp.toULongLong();
                }

                tryFromJson(cur_item, "goods_in_price", variant_tmp);
                goods_info.goods_in_price = variant_tmp.toFloat();
                if(tryFromJson(cur_item, "goodStockPrice", variant_tmp)){
                   goods_info.goodStockPrice = variant_tmp.toFloat();
                }

                tryFromJson(cur_item, "goods_id", variant_tmp);
                goods_info.goods_id = variant_tmp.toULongLong();

                tryFromJson(cur_item, "goods_count", variant_tmp);
                goods_info.goods_count = variant_tmp.toFloat();

                tryFromJson(cur_item, "goods_standard", variant_tmp);
                goods_info.goods_standard = variant_tmp.toString();

                tryFromJson(cur_item, "goods_sale_price", variant_tmp);
                goods_info.goods_sale_price = variant_tmp.toFloat();
                if(tryFromJson(cur_item, "goods_cus_price", variant_tmp)){
                   goods_info.goods_cus_price = variant_tmp.toString();
                }

                tryFromJson(cur_item, "update_time", variant_tmp);
                goods_info.update_time = variant_tmp.toString();

                tryFromJson(cur_item, "goods_barcode", variant_tmp);
                goods_info.goods_barcode = variant_tmp.toString();

                tryFromJson(cur_item, "goods_unit", variant_tmp);
                goods_info.goods_unit = variant_tmp.toString();

                tryFromJson(cur_item, "goods_contain", variant_tmp);
                goods_info.goods_contain = variant_tmp.toULongLong();

                tryFromJson(cur_item, "goods_alias", variant_tmp);
                goods_info.goods_alias = variant_tmp.toString();

                tryFromJson(cur_item, "goods_web_sale_price", variant_tmp);
                goods_info.goods_web_sale_price = variant_tmp.toFloat();

                tryFromJson(cur_item, "goodsChengType", variant_tmp);
                goods_info.goodsChengType = variant_tmp.toInt();

                tryFromJson(cur_item, "goods_kind_unique", variant_tmp);
                goods_info.goods_kind_unique = variant_tmp.toULongLong();

                tryFromJson(cur_item, "goods_brand", variant_tmp);
                goods_info.goods_brand = variant_tmp.toString();
                if(tryFromJson(cur_item, "supplierUnique", variant_tmp)){
                   goods_info.default_supplier_unique = variant_tmp.toString();
                }
                GoodsInfo goods_info_tmp;

                auto barcode_label_scale = ControlManager::getInstance()->getBarcodeLabelScale();

                if (goods_mgr->getGoodsByBarcode(goods_info.goods_barcode, goods_info_tmp))
                {
                    // 商品存在
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_goods_update 商品存在");

                    if (!goods_mgr->setGoodsInfo(goods_info))
                        return;

                    // 刷新购物车
                    if (shop_cart_list->items().size() > 0)
                        shop_cart_list->sendRefreshSignal();

                    // 刷新商品
                    emit sigMqttRefreshGoodsByBarcode4Qml(goods_info.goods_barcode);
                }
                else
                {
                    // 商品不存在 (新建商品)
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_goods_update 商品不存在");

                    if (!goods_mgr->addGoods(goods_info))
                        return;

                    emit sigMqttAddGoods4Qml(goods_info.goods_barcode);
                }

                barcode_label_scale->sendSingleGoods2BarcodeScaleWizard(goods_info.goods_barcode);
            }
        }
        else if (ctrl_str == "msg_goods_delete")
        {
            // 删除商品
            if (!json_doc_data.is_array() || (json_doc_data.size() && json_doc_data[0].is_null()))
                return;

            for (auto cur_it : json_doc_data)
            {
                QVariant variant_tmp;
                tryFromJson(cur_it, "goodsBarcode", variant_tmp);
                auto barcode = variant_tmp.toString();
                goods_mgr->deleteGoodsByBarcode(barcode);
            }
        }
        else if (ctrl_str == "msg_news_count")
        {
            // 	安卓收银机首页消息数量
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_news_count 安卓收银机首页消息数量");
        }
        else if (ctrl_str == "msg_log_upload")
        {
            // windows收银机上传日志
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_log_upload windows收银机上传日志");

            if (json_doc_data.size() == 0)
                return;

            auto start_time_s = getDataFromJson(json_doc_data[0], "startTime").toString();
            auto end_time_s   = getDataFromJson(json_doc_data[0], "endTime").toString();

            auto cur_time = QDateTime::currentDateTime().toString("yyyy-MM-dd");

            if (start_time_s == end_time_s && start_time_s == cur_time)
            {
                //获取当日日志
                ControlManager::getInstance()->getLogCtrl()->uploadLogZipWizard();
            }
            else
            {
                //获取区间
                ControlManager::getInstance()->getLogCtrl()->uploadLogZipWizard(start_time_s, end_time_s);
            }

            emit sigLogUpload();
        }
        else if (ctrl_str == "msg_pc_update")
        {
            // 	windows收银机升级指令,收到指令后，调用//shopmanager/update/provingUpdate.do
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_pc_update windows收银机升级指令,收到指令后，调用//shopmanager/update/provingUpdate.do");
        }
        else if (ctrl_str == "msg_yun_print")
        {
            // 云商订单接单后自动打印
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_yun_print 云商订单接单后自动打印");
        }
        else if (ctrl_str == "msg_order_change")
        {
            // 网单状态发生变化
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_order_change 网单状态发生变化");
        }
        else if (ctrl_str == "msg_shop_order")
        {
            // 收银机收款码接受收银机订单
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_shop_order 收银机收款码接受收银机订单");
        }
        else if (ctrl_str == "msg_goods_update_v2.0")
        {
            if (!json_doc_data.is_array() || (json_doc_data.size() && json_doc_data[0].is_null()))
                return;

            for (auto iter = json_doc_data.rbegin(); iter != json_doc_data.rend(); ++iter)
            {
                auto json_doc_data_it = *iter;

                if (!json_doc_data_it.contains("goodsBarcode"))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "msg_goods_update_v2.0 信息有误");
                    return;
                }

                auto shop_cart_list = ControlManager::getInstance()->getShopCartList();

                string goodsBarcode = json_doc_data_it["goodsBarcode"];

                GoodsInfo goods_info;

                QVariant variant_tmp;

                goods_info.goods_barcode = QString::fromStdString(goodsBarcode);

                tryFromJson(json_doc_data_it, "supplierUnique", variant_tmp);
                goods_info.default_supplier_unique = variant_tmp.toString();

                tryFromJson(json_doc_data_it, "goodsName", variant_tmp);
                goods_info.goods_name = variant_tmp.toString();

                tryFromJson(json_doc_data_it, "shopUnique", variant_tmp);
                goods_info.shop_unique = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_it, "pcShelfState", variant_tmp);
                goods_info.pc_shelf_state = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_it, "foreignKey", variant_tmp);
                goods_info.foreign_key = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_it, "goodsInPrice", variant_tmp);
                goods_info.goods_in_price = variant_tmp.toFloat();

                tryFromJson(json_doc_data_it, "goodsId", variant_tmp);
                goods_info.goods_id = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_it, "goodsCount", variant_tmp);
                goods_info.goods_count = variant_tmp.toFloat();

                tryFromJson(json_doc_data_it, "goodsStandard", variant_tmp);
                goods_info.goods_standard = variant_tmp.toString();

                tryFromJson(json_doc_data_it, "goodsSalePrice", variant_tmp);
                goods_info.goods_sale_price = variant_tmp.toFloat();

                tryFromJson(json_doc_data_it, "goodsCusPrice", variant_tmp);
                goods_info.goods_cus_price = variant_tmp.toString();

                tryFromJson(json_doc_data_it, "goodsUnit", variant_tmp);
                goods_info.goods_unit = variant_tmp.toString();

                tryFromJson(json_doc_data_it, "goodsContain", variant_tmp);
                goods_info.goods_contain = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_it, "goodsAlias", variant_tmp);
                goods_info.goods_alias = variant_tmp.toString();

                tryFromJson(json_doc_data_it, "goodsWebSalePrice", variant_tmp);
                goods_info.goods_web_sale_price = variant_tmp.toFloat();

                tryFromJson(json_doc_data_it, "goodsScaleType", variant_tmp);
                goods_info.goodsChengType = variant_tmp.toInt();

                tryFromJson(json_doc_data_it, "goodsKindUnique", variant_tmp);
                goods_info.goods_kind_unique = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_it, "goodsBrand", variant_tmp);
                goods_info.goods_brand = variant_tmp.toString();

                GoodsInfo goods_info_tmp;
                if (goods_mgr->getGoodsByBarcode(goods_info.goods_barcode, goods_info_tmp))
                {
                    // 商品存在
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_goods_update_v2.0 商品存在");

                    if (!goods_mgr->setGoodsInfo(goods_info))
                        return;

                    // 刷新购物车
                    if (shop_cart_list->items().size() > 0)
                        shop_cart_list->sendRefreshSignal();

                    // 刷新商品
                    emit sigMqttRefreshGoodsByBarcode4Qml(goods_info.goods_barcode);
                }
                else
                {
                    // 商品不存在 (新建商品)
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_goods_update_v2.0 商品不存在");

                    if (!goods_mgr->addGoods(goods_info))
                        return;

                    emit sigMqttAddGoods4Qml(goods_info.goods_barcode);
                }

                auto barcode_label_scale = ControlManager::getInstance()->getBarcodeLabelScale();
                barcode_label_scale->sendSingleGoods2BarcodeScaleWizard(goods_info.goods_barcode);
            }
        }
        else if (ctrl_str == "msg_goods_add_v2.0")
        {
            if (!json_doc_data.is_array() || (json_doc_data.size() && json_doc_data[0].is_null()))
                return;

            auto shop_cart_list = ControlManager::getInstance()->getShopCartList();

            for (auto iter = json_doc_data.rbegin(); iter != json_doc_data.rend(); ++iter)
            {
                auto json_doc_data_item = *iter;

                if (!json_doc_data_item.contains("goodsBarcode"))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "msg_goods_add_v2.0 信息有误");
                    return;
                }
                string goodsBarcode = json_doc_data_item["goodsBarcode"];

                GoodsInfo goods_info;

                QVariant variant_tmp;

                tryFromJson(json_doc_data_item, "supplierUnique", variant_tmp);
                goods_info.default_supplier_unique = variant_tmp.toString();

                goods_info.goods_barcode = QString::fromStdString(goodsBarcode);

                tryFromJson(json_doc_data_item, "goodsName", variant_tmp);
                goods_info.goods_name = variant_tmp.toString();

                tryFromJson(json_doc_data_item, "shopUnique", variant_tmp);
                goods_info.shop_unique = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_item, "pcShelfState", variant_tmp);
                goods_info.pc_shelf_state = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_item, "foreignKey", variant_tmp);
                goods_info.foreign_key = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_item, "goodsInPrice", variant_tmp);
                goods_info.goods_in_price = variant_tmp.toFloat();

                tryFromJson(json_doc_data_item, "goodsId", variant_tmp);
                goods_info.goods_id = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_item, "goodsCount", variant_tmp);
                goods_info.goods_count = variant_tmp.toFloat();

                tryFromJson(json_doc_data_item, "goodsStandard", variant_tmp);
                goods_info.goods_standard = variant_tmp.toString();

                tryFromJson(json_doc_data_item, "goodsSalePrice", variant_tmp);
                goods_info.goods_sale_price = variant_tmp.toFloat();

                tryFromJson(json_doc_data_item, "goodsCusPrice", variant_tmp);
                goods_info.goods_cus_price = variant_tmp.toString();

                tryFromJson(json_doc_data_item, "goodsUnit", variant_tmp);
                goods_info.goods_unit = variant_tmp.toString();

                tryFromJson(json_doc_data_item, "goodsContain", variant_tmp);
                goods_info.goods_contain = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_item, "goodsAlias", variant_tmp);
                goods_info.goods_alias = variant_tmp.toString();

                tryFromJson(json_doc_data_item, "goodsWebSalePrice", variant_tmp);
                goods_info.goods_web_sale_price = variant_tmp.toFloat();

                tryFromJson(json_doc_data_item, "goodsScaleType", variant_tmp);
                goods_info.goodsChengType = variant_tmp.toInt();

                tryFromJson(json_doc_data_item, "goodsKindUnique", variant_tmp);
                goods_info.goods_kind_unique = variant_tmp.toULongLong();

                tryFromJson(json_doc_data_item, "goodsBrand", variant_tmp);
                goods_info.goods_brand = variant_tmp.toString();

                GoodsInfo goods_info_tmp;
                if (goods_mgr->getGoodsByBarcode(goods_info.goods_barcode, goods_info_tmp))
                {
                    // 商品存在
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_goods_update_v2.0 商品存在");

                    if (!goods_mgr->setGoodsInfo(goods_info))
                        return;

                    // 刷新购物车
                    if (shop_cart_list->items().size() > 0)
                        shop_cart_list->sendRefreshSignal();

                    // 刷新商品
                    emit sigMqttRefreshGoodsByBarcode4Qml(goods_info.goods_barcode);
                }
                else
                {
                    // 商品不存在 (新建商品)
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "MQTT msg_goods_update_v2.0 商品不存在");

                    if (!goods_mgr->addGoods(goods_info))
                        return;

                    emit sigMqttAddGoods4Qml(goods_info.goods_barcode);
                }

                auto barcode_label_scale = ControlManager::getInstance()->getBarcodeLabelScale();
                barcode_label_scale->sendSingleGoods2BarcodeScaleWizard(goods_info.goods_barcode);
            }
        }
    }
}


void MqttControl::sendWillMsg()
{
    Json willMsg;
    willMsg = {
        {"ctrl", "msg_shutdown"},
        {"ID", Utils::getHostMacAddressWithoutColon().toUtf8().toStdString()},
        {"version", VERSION_NUM},
    };
    publish(TOPIC_MQTT, willMsg.dump().c_str());
}

void MqttControl::brokerDisconnected()
{
    if (this->state() == ClientState::Disconnected)
    {
        timer_reconn.start();
    }
}

QString MqttControl::getClientId()
{
    auto result = "Market" + Utils::getHostMacAddressWithoutColon();
    return result;
}

QString MqttControl::getTopic()
{
    auto result = QString("win_qt_cash_") + Utils::getHostMacAddressWithoutColon();
    LOG_EVT_INFO("TOPIC为:{}",result.toStdString());
    return result;
}
