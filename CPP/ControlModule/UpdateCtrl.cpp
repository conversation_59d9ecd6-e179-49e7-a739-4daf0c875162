﻿#include "UpdateCtrl.h"
#include <QApplication>
#include <QDir>
#include <QFileInfo>
#include <QNetworkReply>
#include <QProcess>
#include <cmath>
#include <cstdlib>
#include <fstream>
#include <iostream>
#include <math.h>
#include "ControlManager.h"
#include "LogManager.h"
#include "NetModule/HttpCallback.h"
#include "NetModule/HttpClient.h"
#include "NetModule/NetGlobal.h"
#include "Utils/Utils.h"
#include "Version.h"
#include "json-qt.hpp"
#include "qdir.h"
#include "qfileinfo.h"

using namespace std;
using namespace AeaQt;

UpdateCtrl::UpdateCtrl(QObject *parent) : QObject{parent}
{
    resetData();
}

void UpdateCtrl::resetData()
{
    server_file_info_map_.clear();
    local_file_info_map_.clear();

    update_files_.clear();
    delete_files_.clear();
    equal_files_.clear();

    logId_ = -1;

    versionDownloadUrl_.clear();
    baseDownloadUrl_.clear();

    appVersion_.clear();
    isForceUpdate_ = false;
    isCanUpgrade_  = false;

    {
        ignore_files_.clear();
        ignore_files_.emplace_back("config.ini");
        ignore_files_.emplace_back("buyhoo.db");
        ignore_files_.emplace_back("dhbz5obj.txt");
        ignore_files_.emplace_back("dhbz6obj.txt");
        ignore_files_.emplace_back("dhip.ini");
        ignore_files_.emplace_back("dhplupathname.ini");
        ignore_files_.emplace_back("dhtma07obj.txt");
        ignore_files_.emplace_back("pending_order.cfg");
        ignore_files_.emplace_back("BuyhooMaintenance.exe");
        ignore_files_.emplace_back("BuyhooMaintenance2.exe");
    }
}

QString UpdateCtrl::getSign()
{
    auto shop_ctrl = ControlManager::getInstance()->getShopControl();
    return Utils::Encrypt::str2Md5(update_sign + QString::number(shop_ctrl->getShopUnique()));
}
void UpdateCtrl::reqGetVersionInfoNew(QJSValue callback, bool is_auto_check)
{
    auto  ctrl_mag = ControlManager::getInstance();
    ctrl_mag->loadingIndexPlus();
//    auto router_ctrl = CtrlMgr::getInstance()->getRouterCtrl();
//    router_ctrl->loadingIndexPlus();
    QString url = "https://platform.allscm.net/gw/platformBaseWeb/noAuth/pc/checkVersion";

    HttpClient *http_client = new HttpClient();
    auto    shop_ctrl = ControlManager::getInstance()->getShopControl();

    Json json_doc;
    json_doc["sign"]           = getSign().toStdString();
    json_doc["shopUnique"]     = shop_ctrl->getShopUnique();
    json_doc["appCode"]        = "NEW_PC_CASHIER";
    json_doc["currentVersion"] = VERSION_NUM;

    auto &&request = http_client->post(url);
    request.bodyWithJson(json_doc.dump());
    LOG_NET_INFO("Request Body:{}",QString::fromStdString(json_doc.dump()).toStdString());
    execAndCallback4Cpp(
        request,
        [=](HttpHandleType http_handle_type, std::string data) mutable
        {
            switch (http_handle_type)
            {
            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                {
                    int data_status = (int)DataStatusEnum::DATA_STATUS__INVALID;

                    Json json_doc = Json::parse(data, nullptr, false);

                    auto is_data_valid = json_doc.contains("data") &&
                        //
                        json_doc["data"].contains("upgrade") &&
                        json_doc["data"]["upgrade"].is_object()
                        //
                        && json_doc["data"].contains("upgradeFileList") &&
                        json_doc["data"]["upgradeFileList"].is_array()
                        //
                        && json_doc["data"].contains("versionDownloadUrl") && json_doc["data"].contains("baseDownloadUrl");

                    if (!json_doc.is_discarded() && is_data_valid)
                    {
                        auto json_data = json_doc["data"];
                        int status_code = getDataFromJson(json_doc, "code").toInt();

                        if (status_code == (int)NetStatusEnum::NET_STATUS__SUCC)
                        {

                            versionDownloadUrl_ = getDataFromJson(json_data, "versionDownloadUrl").toString().toStdString();
                            baseDownloadUrl_    = getDataFromJson(json_data, "baseDownloadUrl").toString().toStdString();
                            isCanUpgrade_       = getDataFromJson(json_data, "canUpgrade").toBool();

                            if (isCanUpgrade_)
                            {
                                data_status = (int)DataStatusEnum::DATA_STATUS__VALID;
                            }

                            auto &json_upgrade = json_data["upgrade"];

                            upgradeDetail_ = getDataFromJson(json_upgrade, "remark").toString();
                            upgradeBrief_  = getDataFromJson(json_upgrade, "introduce").toString();
                            appVersion_    = getDataFromJson(json_upgrade, "version").toString();
                            isForceUpdate_ = getDataFromJson(json_upgrade, "forceUpgrade").toInt() == 1;

                            logId_ = getDataFromJson(json_data, "logId").toLongLong();

                            if (json_data.contains("upgradeFileList"))
                            {
                                auto upgradeFileList = json_data["upgradeFileList"];

                                if (upgradeFileList.is_array())
                                {
                                    for (auto &upgradeFileList_item : upgradeFileList)
                                    {
                                        auto &&file_info = upgradeFileList_item.get<FileInfo>();
                                        server_file_info_map_.insert(std::make_pair(file_info.filePath, file_info));
                                    }
                                }
                            }
                            emit sigUpgradeDetailChanged();
                            emit upgradeBriefChanged();
                            // 强制升级
                            if (isCanUpgrade_ && isForceUpdate_)
                            {
                                LOG_EVT_INFO("开始强制升级！！！！！！");
                                updateStart();
                            }
                        }
                    }
                    else
                    {
                        LOG_EVT_ERROR("更新信息格式有误");
                        //Utils4Qml::getInstance()->sigOpenToast("更新信息格式有误", static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__ERROR));
                    }
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(data_status);
                        arglist.push_back(QString::fromStdString(data));
                        callback.call(arglist);
                    }
                    break;
                }
            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                {
//                    callback4Qml(callback, (int)DataStatusEnum::DATA_STATUS__INVALID);
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back((int)DataStatusEnum::DATA_STATUS__INVALID);
                        arglist.push_back("");
                        callback.call(arglist);
                    }
                    break;
                }
            }
            ctrl_mag->loadingIndexMinus();
            //router_ctrl->loadingIndexMinus();
            http_client->deleteLater();
        });

}

void UpdateCtrl::reqGetVersionInfo(QJSValue callback, bool is_auto_check)
{
    QString url       = req_host_prefix__cash_upgrade + "upgrade/pc/checkVersion";
    auto    shop_ctrl = ControlManager::getInstance()->getShopControl();

    Json json_body;
    json_body["shopUnqiue"]     = shop_ctrl->getShopUnique();
    json_body["sign"]           = getSign().toStdString();
    json_body["softType"]       = 1;
    json_body["currentVersion"] = VERSION_NUM;

    HttpClient *http_client = new HttpClient();

    auto &&request = http_client->post(url);
    request.bodyWithJson(json_body.dump());

    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    Json json_doc = Json::parse(data);

                                    auto status = getDataFromJson(json_doc, "status").toInt();

                                    if (status == 1)
                                    {
                                        if (json_doc.contains("data"))
                                        {
                                            if (!getDataFromJson(json_doc["data"], "canUpgrade").toBool())
                                            {
                                                if (callback.isCallable())
                                                {
                                                    QJSValueList arglist;
                                                    arglist.push_back(0);
                                                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                                                    callback.call(arglist);
                                                }
                                                return;
                                            }
                                        }
                                        else
                                        {
                                            if (callback.isCallable())
                                            {
                                                QJSValueList arglist;
                                                arglist.push_back(0);
                                                arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                                                callback.call(arglist);
                                            }
                                            return;
                                        }
                                    }

                                    auto is_data_valid = json_doc.contains("data") &&
                                        //
                                        json_doc["data"].contains("upgrade") &&
                                        json_doc["data"]["upgrade"].is_object()
                                        //
                                        && json_doc["data"].contains("upgradeFileList") &&
                                        json_doc["data"]["upgradeFileList"].is_array()
                                        //
                                        && json_doc["data"].contains("upgrade") &&
                                        json_doc["data"]["upgrade"].is_object()
                                        //
                                        && json_doc["data"].contains("versionDownloadUrl") && json_doc["data"].contains("baseDownloadUrl");

                                    if (!is_data_valid)
                                    {
                                        LOG_NET_ERROR("更新数据有误");
                                        if (callback.isCallable())
                                        {
                                            QJSValueList arglist;
                                            arglist.push_back(0);
                                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                                            callback.call(arglist);
                                        }
                                        return;
                                    }

                                    if (status == 1)
                                    {
                                        auto &data_j            = json_doc["data"];
                                        auto &upgradeFileList_j = json_doc["data"]["upgradeFileList"];
                                        auto &upgrade_j         = json_doc["data"]["upgrade"];

                                        versionDownloadUrl_ = getDataFromJson(data_j, "versionDownloadUrl").toString().toStdString();
                                        baseDownloadUrl_    = getDataFromJson(data_j, "baseDownloadUrl").toString().toStdString();
                                        isCanUpgrade_       = getDataFromJson(data_j, "canUpgrade").toBool();
                                        emit sigUpgradeDetailChanged();

                                        upgradeDetail_ = getDataFromJson(upgrade_j, "upgradeDetail").toString();
                                        appVersion_    = getDataFromJson(upgrade_j, "appVersion").toString();
                                        isForceUpdate_ = getDataFromJson(upgrade_j, "forceUpdate").toInt() == 2;

                                        this->logId_ = getDataFromJson(data_j, "logId").toLongLong();

                                        for (auto &upgradeFileList_item : upgradeFileList_j)
                                        {
                                            if (upgradeFileList_item.is_null())
                                                return;
                                            auto &&file_info = upgradeFileList_item.get<FileInfo>();
                                            auto iter_find = std::find(ignore_files_.begin(), ignore_files_.end(), file_info.filePath);

                                            if (iter_find == ignore_files_.end())
                                            {
                                                server_file_info_map_.insert(std::make_pair(file_info.filePath, file_info));
                                            }
                                        }

                                        if (callback.isCallable())
                                        {
                                            QJSValueList arglist;
                                            arglist.push_back(1);
                                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                                            callback.call(arglist);
                                        }

                                        if (is_auto_check && isCanUpgrade_)
                                        {
                                            emit sigNeedUpgrade(isForceUpdate_);
                                        }

                                        // 强制升级
                                        if (isForceUpdate_)
                                        {
                                            updateStart();
                                        }
                                    }
                                    else
                                    {
                                        if (callback.isCallable())
                                        {
                                            QJSValueList arglist;
                                            arglist.push_back(0);
                                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                                            callback.call(arglist);
                                        }
                                    }

                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    if (callback.isCallable())
                                    {
                                        QJSValueList arglist;
                                        arglist.push_back(3);
                                        callback.call(arglist);
                                    }
                                    break;
                                }
                            }
                            http_client->deleteLater();
                        });
}

void UpdateCtrl::updateStart()
{
    LOG_EVT_INFO("开始更新");
    setIsUpdating(true);

    refreshLocalFileInfo();
    compareFile();

    //checkBuyhooMaintenance();

    clearDir(update_folder_name.c_str());
    LOG_EVT_INFO("update_folder_name.c_str():{}",update_folder_name.c_str());
    for (auto update_files : update_files_)
    {
        file_download_total_ += update_files.fileSize;
    }

    // file_download_total_ = update_files_.size();
    file_download_index_cur_ = 0;

    emit sigDownloadTotalChanged();
    emit sigDownloadIndexChanged();

    update_files_downloading = update_files_;

    checkDownloadList();

    saveUpdateInfo2Json();
}

void UpdateCtrl::checkBuyhooMaintenance()
{
    LOG_OPT_INFO("检查更新程序");

    auto exe_path  = Utils::getAppDirPath() + "/BuyhooMaintenance.exe";
    auto exe2_path = Utils::getAppDirPath() + "/update/BuyhooMaintenance2.exe";
    LOG_OPT_INFO("exe_path:{}",exe_path.toStdString());
    LOG_OPT_INFO("exe2_path:{}",exe2_path.toStdString());
    QFileInfo fileInfo(exe2_path);
    if (fileInfo.exists())
    {
        remove(exe_path.toStdString().c_str());

        auto ret = rename(exe2_path.toStdString().c_str(), exe_path.toStdString().c_str());

        if (ret == 0)
        {
            LOG_OPT_INFO("checkBuyhooMaintenance source_str: {}  target_str: {} 移动成功", exe2_path.toStdString(), exe_path.toStdString());
        }
        else
        {
            LOG_OPT_ERROR("checkBuyhooMaintenance source_str: {}  target_str: {} 移动失败", exe2_path.toStdString(), exe_path.toStdString());
        }
    }else{
       LOG_OPT_INFO("{}升级程序不存在!", exe2_path.toStdString(), exe_path.toStdString());
    }
}

void UpdateCtrl::reqSendUpdateFinish()
{
    //QString url       = req_host_prefix__cash_upgrade + "upgrade/pc/upgradeFinish"; 旧版
    QString url       = "https://platform.allscm.net/gw/platformBaseWeb/noAuth/pc/upgradeFinish";
    auto    shop_ctrl = ControlManager::getInstance()->getShopControl();

    Json json_body;
    json_body["shopUnique"] = shop_ctrl->getShopUnique();
    json_body["sign"]       = getSign().toStdString();
    json_body["logId"]      = logId_;

    HttpClient *http_client = new HttpClient();

    auto &&request = http_client->post(url);
    request.bodyWithJson(json_body.dump());

    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            checkBuyhooMaintenance();
                            launchUpdateMaintenance();
                            setIsUpdating(false);
                            http_client->deleteLater();
                        });
}

void UpdateCtrl::refreshLocalFileInfo(QString path, QString prefix)
{
    QDir source_dir(path);

    QFileInfoList file_info_list = source_dir.entryInfoList(QDir::Files | QDir::Hidden | QDir::NoSymLinks);

    for (auto &file_info_item : file_info_list)
    {
        auto iter_find = std::find(ignore_files_.begin(), ignore_files_.end(), file_info_item.fileName().toStdString());

        if (iter_find != ignore_files_.end())
            continue;

        FileInfo file_info;
        file_info.fileMd5  = Utils::Encrypt::fileMd5(file_info_item.filePath()).toStdString();
        file_info.filePath = (prefix.isEmpty() ? "" : (prefix.toStdString() + "/")) + file_info_item.fileName().toStdString();
        LOG_EVT_INFO("file_info.filePath:{}",file_info.filePath);
        local_file_info_map_.insert(std::make_pair(file_info.filePath, file_info));
    }

    QFileInfoList file_info_list_dir = source_dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);

    for (auto &file_info_list_dir_item : file_info_list_dir)
    {
        if (file_info_list_dir_item.fileName() == QString::fromStdString(update_folder_name))
            continue;
        refreshLocalFileInfo(file_info_list_dir_item.absoluteFilePath(), (prefix.isEmpty() ? "" : (prefix + "/")) + file_info_list_dir_item.fileName());
    }
}

void UpdateCtrl::compareFile()
{
    update_files_.clear();

    // 获取相等文件 & 需更新文件
    for (auto &server_file : server_file_info_map_)
    {
        auto local_file_info_map_iter = local_file_info_map_.find(server_file.first);

        if (local_file_info_map_iter != local_file_info_map_.end())
        {
            if (local_file_info_map_iter->second.fileMd5 == server_file.second.fileMd5)
            {
                equal_files_.push_back(server_file.second);
            }
            else
            {
                update_files_.push_back(server_file.second);
            }
        }
        else
        {
            update_files_.push_back(server_file.second);
        }
    }

    // 获取需删除文件
    for (auto &local_file_info_map_item : local_file_info_map_)
    {
        auto server_file_info_map_iter = server_file_info_map_.find(local_file_info_map_item.first);

        if (server_file_info_map_iter == server_file_info_map_.end())
        {
            delete_files_.push_back(local_file_info_map_item.second);
        }
    }
}

void UpdateCtrl::downloadFile2(FileInfo file_info)
{
    QString url;
    if (file_info.base)
    {
        url = QString::fromStdString(baseDownloadUrl_ + file_info.filePath);
    }
    else
    {
        url = QString::fromStdString(versionDownloadUrl_ + file_info.filePath);
    }

    auto path_list = QString::fromStdString(file_info.filePath).split('/');

    QString postfix;

    if (path_list.size() > 1)
    {
        postfix += "/";

        for (int i = 0; i < path_list.size() - 1; ++i)
        {
            auto cur_item = path_list[i];
            postfix += (cur_item + "/");
        }

        postfix.chop(1);
    }

    HttpClient *http_client = new HttpClient();
    auto      &&request     = http_client->get(url);

    request.download()
        .onSuccess(
            [&]()
            {
                if (update_files_downloading.begin()->fileSize == 0)
                {
                    file_download_index_total += update_files_downloading.begin()->fileSize;
                    update_files_downloading.erase(update_files_downloading.begin());

                    emit sigDownloadIndexChanged();

                    checkDownloadList();
                }
            })
        .downloadPath(Utils::getAppDirPath() + "/" + update_folder_name.c_str() + postfix)
        .onDownloadProgress(
            [&](qint64 bytesReceived, qint64 bytesTotal)
            {
                LOG_NET_INFO("bytes received: {}bytes total: {}", bytesReceived, bytesTotal);
                file_download_index_cur_ = bytesReceived;
                emit sigDownloadIndexChanged();
            })
        .onDownloadFileSuccess(
            [&](QString fileName)
            {
                LOG_NET_INFO("download success: {}", fileName.toStdString());

                // file_download_index_total += file_download_index_cur_;
                file_download_index_total += update_files_downloading.begin()->fileSize;

                update_files_downloading.erase(update_files_downloading.begin());


                emit sigDownloadIndexChanged();

                checkDownloadList();
            })
        .onDownloadFileFailed(
            [&](QString error)
            {
                LOG_NET_ERROR("download failed: {}", error.toStdString());

                setIsUpdating(false);

                emit sigDownloadfailed();
            })

        .exec();
}

bool UpdateCtrl::saveUpdateInfo2Json()
{
    Json json_doc;

    json_doc["update_files"] = Json::array();
    json_doc["delete_files"] = Json::array();
    json_doc["equal_files"]  = Json::array();

    json_doc["ignore_files"] = ignore_files_;

    for (auto cur_file : update_files_)
    {
        json_doc["update_files"].push_back(cur_file.filePath);
    }
    for (auto cur_file : delete_files_)
    {
        json_doc["delete_files"].push_back(cur_file.filePath);
    }
    for (auto cur_file : equal_files_)
    {
        json_doc["equal_files"].push_back(cur_file.filePath);
    }

    json_doc["update_info"]   = upgradeDetail_.toStdString();
    json_doc["appVersion"]    = appVersion_.toStdString();
    json_doc["isForceUpdate"] = isForceUpdate_;

    QFileInfo file_info;
    file_info.setFile(update_folder_name.c_str());

    if (!file_info.exists())
    {
        QDir dir1;
        if (!dir1.mkdir(update_folder_name.c_str()))
        {
            LOG_OPT_ERROR("创建更新文件夹失败");
        }
    }

    fstream fstream1;
    fstream1.open(update_folder_name + "/updateInfo.json", ios::out | ios::trunc);

    if (!fstream1.is_open())
    {
        LOG_OPT_ERROR("更新信息保存失败");
        return false;
    }

    fstream1 << json_doc.dump();
    fstream1.close();

    return true;
}

void UpdateCtrl::clearDir(const QString path)
{
    QDir dir(path);

    dir.setFilter(QDir::NoDotAndDotDot | QDir::Files);
    foreach (QString dirItem, dir.entryList())
        dir.remove(dirItem);

    dir.setFilter(QDir::NoDotAndDotDot | QDir::Dirs);
    foreach (QString dir_item, dir.entryList())
    {
        QDir sub_dir(dir.absoluteFilePath(dir_item));
        sub_dir.removeRecursively();
    }
}
//旧版
//void UpdateCtrl::launchUpdateMaintenance()
//{
//    QProcess    process;
//    auto        exe_path = Utils::getAppDirPath() + "/BuyhooMaintenance.exe";
//    QStringList args;
//    LOG_OPT_INFO("更新程序路径: {}", exe_path.toStdString());
//    LOG_OPT_INFO("开始启动更新程序!");
//    process.startDetached(exe_path, args);

//    QApplication::exit();
//}
void UpdateCtrl::launchUpdateMaintenance()
{
    QProcess process;

    QString   exe_path = Utils::getAppDirPath() + "/BuyhooMaintenance.exe";
    QFileInfo fileInfo(exe_path);
    if (fileInfo.exists())
    {
        QStringList args;
        LOG_OPT_INFO("更新程序路径: {}", exe_path.toStdString());
        process.startDetached(exe_path, args);

        QApplication::exit();
    }
    else
    {
        setIsMaintenanceExist(false);
        LOG_OPT_ERROR("更新维护程序不存在!");
        //Utils4Qml::getInstance()->sendToast("更新维护程序不存在!", static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__ERROR));
    }
}
void UpdateCtrl::checkDownloadList()
{
    LOG_OPT_INFO("待下载文件:{}", update_files_downloading.size());

    if (update_files_downloading.size() > 0)
    {
        downloadFile2(*update_files_downloading.begin());
    }
    else
    {
        reqSendUpdateFinish();
        // launchUpdateMaintenance();
    }
}
