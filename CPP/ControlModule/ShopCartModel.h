﻿#ifndef SHOPCARTMODEL_H
#define SHOPCARTMODEL_H

#include <QAbstractListModel>

// Q_MOC_INCLUDE("ShopCartModel.h")

class ShopCartList;

class ShopCartModel : public QAbstractListModel
{
    Q_OBJECT
    Q_PROPERTY(ShopCartList *list READ list WRITE setList)

    Q_PROPERTY(unsigned long long itemCount READ itemCount NOTIFY itemCountChanged FINAL)

public:
    explicit ShopCartModel(QObject *parent = nullptr);

    enum
    {
        GOODS_BARCODE = Qt::UserRole,
        GOODS_NAME_ROLE,
        GOODS_PRICE_ROLE,
        GOODS_PRICE_ROLE_JIN,
        GOODS_AMOUNT_ROLE,
        GOODS_TOTAL_PRICE_ROLE,
        GOODS_DATE_TIME_ROLE,
        GOODS_IS_WEIGHT,
    };

    // Basic functionality:
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;

    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    // Editable:
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;

    Qt::ItemFlags flags(const QModelIndex &index) const override;

    virtual QHash<int, QByteArray> roleNames() const override;

    ShopCartList *list() const;
    void          setList(ShopCartList *list);

    unsigned long long itemCount();

    Q_INVOKABLE void refreshView();

private:
    ShopCartList *shop_cart_list_;

signals:
    void itemCountChanged();
};

#endif // SHOPCARTMODEL_H
