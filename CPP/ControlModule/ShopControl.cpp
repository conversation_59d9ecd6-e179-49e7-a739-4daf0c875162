﻿#include "ShopControl.h"
#include <QJSValue>
#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
#include <map>
#include "ControlManager.h"
#include "DataModule/DataManager.h"
#include "LogManager.h"
#include "NetModule/HttpCallback.h"
#include "NetModule/HttpClient.h"
#include "NetModule/HttpWorker.h"
#include "NetModule/NetGlobal.h"
#include "GoodsPromotionCtrl.h"
#include "Utils/Utils.h"
#include "Version.h"
#include "../NetModule/NetGlobal.h"
#include "ControlModule/PayMethodControl.h"

using namespace AeaQt;

ShopControl::ShopControl(QObject *parent) : QObject{parent}
{
}
bool updateShopInfo(const int &ids, QSqlQuery &dbQuery, QString &id) {
    QString actionParamTemp = "";
    switch (ids)
    {
    case 111:
        actionParamTemp = "pc_member_recharge";
        break;
    case 112:
        actionParamTemp = "pc_member_exchange";
        break;
    case 113:
        actionParamTemp = "pc_member_update";
        break;
    case 114:
        actionParamTemp = "pc_member_register";
        break;
    case 115:
        actionParamTemp = "pc_update_selling_price";
        break;
    case 116:
        actionParamTemp = "pc_update_stock";
        break;
    case 117:
        actionParamTemp = "pc_stock_goods_calss";
        break;
    case 118:
        actionParamTemp = "pc_update_purchase_price";
        break;
    case 119:
        actionParamTemp = "pc_update_goods_name";
        break;
    case 120:
        actionParamTemp = "pc_goods_delete";
        break;
    case 121:
        actionParamTemp = "pc_goods_add";
        break;
    case 306:
        actionParamTemp = "pc_warehousing";
        break;
    case 307:
        actionParamTemp = "pc_out_stock";
        break;
    case 308:
        actionParamTemp = "pc_update_member_price";
        break;
    case 309:
        actionParamTemp = "pc_member_add_increase";
        break;
    case 310:
        actionParamTemp = "pc_member_delete";
        break;
    case 75: //收银
        actionParamTemp = "pc_cash";
        break;
    case 76: //查询
        actionParamTemp = "pc_query";
        break;
    case 77: //网单
        actionParamTemp = "pc_netlist";
        break;
    case 78: //入库
        actionParamTemp = "pc_instock";
        break;
    case 79: //会员
        actionParamTemp = "pc_member_manger";
        break;
    case 80: //统计
        actionParamTemp = "pc_statistics";
        break;
    case 81: //查询-出入库记录
        actionParamTemp = "pc_query_inout_stock";
        break;
    case 82: //查询-销售记录
        actionParamTemp = "pc_query_sale_record";
        break;
    case 83: //查询-交接班记录
        actionParamTemp = "pc_query_change_shifts";
        break;
    case 84: //查询-商品信息列表
        actionParamTemp = "pc_query_goods_list";
        break;
    case 1141: //查询-商品销售统计
        actionParamTemp = "pc_query_goods_sales";
        break;
    case 85: //网单-新订单
        actionParamTemp = "pc_netlist_new_order";
        break;
    case 86: //网单-待发货
        actionParamTemp = "pc_netlist_pending_shipment";
        break;
    case 87: //网单-配送中
        actionParamTemp = "pc_netlist_delivery_in";
        break;
    case 88: //网单-已完成
        actionParamTemp = "pc_netlist_completed";
        break;
    case 89: //网单-已取消
        actionParamTemp = "pc_netlist_cancel";
        break;
    case 90: //收银-商品管理
        actionParamTemp = "pc_cash_goods_manger";
        break;
    case 793: //设置
        actionParamTemp = "pc_setting";
        break;
    default:
        LOG_EVT_INFO("未找到对应权限!");
        return false;
    }
    QString idsTemp = QString::number(ids);
    QString sqlStr = "update shops set " + actionParamTemp +"= " + idsTemp + " where manager_account = " + id;
    bool success = dbQuery.exec(sqlStr);
    if (!success) {
        LOG_EVT_INFO("Error executing SQL:{}",dbQuery.lastError().text().toStdString());
    }
//    LOG_EVT_INFO("根据登录接口返回数据，数据库更新命令为:{}", sqlStr.toStdString());
    return success;
}
std::map<std::string, std::string> loadMapFromFile(const std::string& filename) {
    std::map<std::string, std::string> mapData;
    std::ifstream inFile(filename);
    if (inFile.is_open()) {
        std::string line;
        while (std::getline(inFile, line)) {
            std::istringstream iss(line);
            std::string key, value;
            if (iss >> key >> value) {
                mapData[key] = value;
            }
        }
        inFile.close();
    } else {
        std::cerr << "open permission.txt failed! " << filename << std::endl;
    }
    return mapData;
}
std::string findAndOutputValue( std::map<std::string, std::string>& mapData, std::string& inputKey) {
    auto it = mapData.find(inputKey);
    if (it != mapData.end()) {
        return it->second;
    } else {
        return "";
    }
}
void ShopControl::login_v2(QJSValue callback, QString id, QString passwd)
{

      LOG_EVT_INFO("==登录时检测到的网络状态为:{}==",net_status_global.toStdString());
      if(net_status_global != "101")
      {
         LOG_EVT_INFO("==网络连接失败 开启离线收银==");
          QSqlDatabase db;
          QString connectionName = "buyhoo203";
          if (QSqlDatabase::contains(connectionName))
          {
              db = QSqlDatabase::database(connectionName);
          }
          else
          {
              db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
              //db.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
          }
          QString dataPath = Utils::getDataBasePath();
          db.setDatabaseName(dataPath + "buyhoo2.db");
          if (db.open() == false)
          {
              LOG_EVT_INFO("dishdb数据库打开失败loginPage5");
          }
          QSqlQuery   dbQuery(db);

          QSqlDatabase db2;
          QString connectionName2 = "buyhoo2032";
          if (QSqlDatabase::contains(connectionName2))
          {
              db2 = QSqlDatabase::database(connectionName2);
          }
          else
          {
              db2 = QSqlDatabase::addDatabase("QSQLITE", connectionName2);
              db2.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
          }
          QString dataPath2 = Utils::getDataBasePath();
          db2.setDatabaseName(dataPath2 + "buyhoo2.db");
          if (db2.open() == false)
          {
              LOG_EVT_INFO("dishdb数据库打开失败loginPage5");
          }
          QSqlQuery   dbQuery2(db2);

          QStringList list = db.tables();

          if (!list.contains("shops"))
          {
              dbQuery.prepare("create table shops(shop_id integer primary key,"
                              "shop_unique int64, "
                              "login_id int64, "
                              "shop_name varchar,"
                              "manager_unique varchar,"
                              "manager_account varchar,"
                              "manager_pwd varchar,"
                              "shop_alias varchar,"
                              "trade_area_unique varchar,"
                              "shop_latitude decimal,"
                              "shop_longitude decimal,"
                              "shop_seller_email varchar,"
                              "shop_address_detail varchar,"
                              "shop_phone varchar,"
                              "shop_remark varchar,"
                              "shop_image_path varchar,"
                              "shop_partner varchar,"
                              "shop_APP_ID varchar,"
                              "shop_MCH_ID varchar,"
                              "shop_API_KEY varchar,"
                              "area_dict_num varchar,"
                              "examinestatus integer,"
                              "power_supplier integer,"
                              "examinestatus_reason varchar,"
                              "shop_hours varchar,"
                              "shop_counter varchar,"
                              "channel_id varchar,"
                              "shop_source integer,"
                              "cashier_id varchar,"
                              "sameType integer,"
                              "pc_update_selling_price varchar,"
                              "pc_update_stock varchar,"
                              "pc_stock_goods_calss varchar,"
                              "pc_update_purchase_price varchar,"
                              "pc_update_goods_name varchar,"
                              "pc_goods_delete varchar,"
                              "pc_goods_add varchar,"
                              "pc_member_recharge varchar,"
                              "pc_member_exchange varchar,"
                              "pc_member_update varchar,"
                              "pc_member_register varchar,"
                              "pc_warehousing varchar,"
                              "pc_out_stock varchar,"
                              "pc_update_member_price varchar,"
                              "pc_member_add_increase varchar,"
                              "pc_member_delete varchar,"
                              "in_store_record varchar,"
                              "sale_record varchar,"
                              "pc_cash varchar,"
                              "pc_query varchar,"
                              "pc_netlist varchar,"
                              "pc_instock varchar,"
                              "pc_member_manger varchar,"
                              "pc_statistics varchar,"
                              "pc_query_inout_stock varchar,"
                              "pc_query_sale_record varchar,"
                              "pc_query_change_shifts varchar,"
                              "pc_query_goods_list varchar,"
                              "pc_query_goods_sales varchar,"
                              "pc_netlist_new_order varchar,"
                              "pc_netlist_pending_shipment varchar,"
                              "pc_netlist_delivery_in varchar,"
                              "pc_netlist_completed varchar,"
                              "pc_netlist_cancel varchar,"
                              "pc_cash_goods_manger varchar,"
                              "pc_setting varchar,"
                              "changeshift_record varchar,"
                              "goods_info_record varchar,"
                              "goods_sale_record varchar,"
                              "power_check_page varchar,"
                              "power_wangdan_page varchar,"
                              "power_instore_page varchar,"
                              "power_member_page varchar,"
                              "power_pcstatis_page varchar,"
                              "power_settings_page varchar,"
                              "shop_type varchar)");
              if (!dbQuery.exec())
              {
                  LOG_EVT_INFO("建店铺信息表失败！");
              }
          }
          else
          {

              if (!id.isEmpty() && !passwd.isEmpty())
              {
                  QString selectSql = "select manager_pwd ,cashier_id,shop_unique,login_id,shop_type,pc_member_recharge,pc_member_exchange,pc_member_update,pc_member_register,"
                                      "pc_update_selling_price,pc_update_stock,pc_stock_goods_calss,pc_update_purchase_price,pc_update_goods_name,"
                                      "pc_goods_delete,pc_goods_add,pc_warehousing,pc_out_stock,pc_update_member_price,pc_member_add_increase,"
                                      "pc_member_delete,pc_cash,pc_query,pc_netlist,pc_instock,pc_member_manger,pc_statistics,pc_query_inout_stock,"
                                      "pc_query_sale_record,pc_query_change_shifts,pc_query_goods_list,pc_query_goods_sales,pc_netlist_new_order,"
                                      "pc_netlist_pending_shipment,pc_netlist_delivery_in,pc_netlist_completed,pc_netlist_cancel,pc_cash_goods_manger,"
                                      "pc_setting from shops where manager_account='";
                  selectSql += id;
                  selectSql += "'";
                  dbQuery.exec(selectSql);
                  LOG_EVT_INFO("没有网!!!!!查询语句为:{}", selectSql.toStdString());
                  int flag = -1;
                  while (dbQuery.next())
                  {
                      LOG_EVT_INFO("passwd:{}dbQuery.value(0).toString():{}",passwd.toStdString(),dbQuery.value(0).toString().toStdString());
                      auto config_tool = ConfigTool::getInstance();
                      LOG_EVT_INFO("passwd == dbQuery.value(0).toString()：{}",passwd == dbQuery.value(0).toString());
                      if (passwd == dbQuery.value(0).toString())
                      {
                          flag = 1;
                          LOG_EVT_INFO("离线密码验证通过!!!!!");
                          config_tool->setSettingShopInfo(ConfigToolEnum::ConfigEnum::USER_ACCOUNT_SHOPINFO, id);

                          shop_unique_ = dbQuery.value(2).toString().toULongLong();
                          LOG_EVT_INFO("更新店铺id为：{}",shop_unique_);
                          person_info_.cashier_id = dbQuery.value(1).toString().toULongLong();
                          LOG_EVT_INFO("更新收银员id为：{}",person_info_.cashier_id);
                          login_id_ = dbQuery.value(3).toString().toULongLong();
                          LOG_EVT_INFO("更新登录ID为：{}",login_id_);
                          shop_type_ = dbQuery.value(4).toString();
                          LOG_EVT_INFO("更新店铺类型为：{}",shop_type_.toStdString());
                          //向系统添加会员权限
                          auto permission_control = ControlManager::getInstance()->getPermissionControl();
                          for(int i =1;i<34;i++ ){
                             //LOG_EVT_INFO("权限id:{}",dbQuery.value(i).toInt());
                             permission_control->addPermission(static_cast<PermissionEnum>(dbQuery.value(i).toInt()));
                             //LOG_EVT_INFO("向系统添加会员权限:{}",i);
                          }
                          break;
                      }
                      else
                      {
                          flag = 0;
                      }

                  }

                  if (flag == 0 || flag == -1)
                  {
                      //std::string = data {"macId":"1831BF4E5263","machineTime":"2024-07-20 10:31:06","staffAccount":"***********","staffPwd":"a123456","versionNumber":"3.0.07.15"}
                      //emit loginResault("0", "0");
                      if (callback.isCallable())
                      {
                          std::string  data  =  R"({"status":1,"msg":"离线登录失败！"})";
                          QJSValueList arglist;
                          arglist.push_back(false);
                          arglist.push_back(QString::fromStdString(data));
                          callback.call(arglist);
                      }
                  }
                  else if (flag == 1)
                  {
                      if (callback.isCallable())
                      {
                          std::string  data  =  R"({"status":0,"msg":"离线登录成功！"})";
                          QJSValueList arglist;
                          arglist.push_back(true);
                          arglist.push_back(QString::fromStdString(data));
                          callback.call(arglist);
                      }
                  }
              }
              else
              {
//                  emit loginResault("2", "0");
              }
          }
          ///-------------------------------------------| 获取本地数据 |-------------------------------------------
          auto data_mgr        = DataManager::getInstance();
          data_mgr->getGoodsMgr()->getLocalGoodsData_Thread();
          data_mgr->getGoodsKindMgr()->getLocalGoodsKindData_Thread();
          data_mgr->getVirtualGoodsKindMgr()->getLocalVGoodsKindData_Thread();

          db.close();
      }
      else{
          LOG_EVT_INFO("==网络连接成功 开启在线收银==");
          if (!login_mutex.try_lock())
              return;
          QString url = req_host_prefix + "shopmanager/pc/v2/pcStaffLogin.do";

          QString mac_addr = Utils::getHostMacAddressWithoutColon();
          LOG_EVT_INFO("登录MAC地址{}", mac_addr.toStdString());

          Json body_json;
          body_json["staffAccount"]  = id.toStdString();
          body_json["staffPwd"]      = passwd.toStdString();
          body_json["versionNumber"] = VERSION_NUM;
          body_json["machineTime"]   = Utils::DateTime::getCurDateTimeStr().toStdString();
          body_json["macId"]         = mac_addr.toStdString();

          HttpClient *http_client = new HttpClient();
          auto      &&request     = http_client->post(url);
          request.bodyWithJson(body_json.dump());
          request.timeout(3);

          execAndCallback4Cpp(request,
                              [=](HttpHandleType http_handle_type, std::string data) mutable
                              {
                                  switch (http_handle_type)
                                  {
                                  case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                      {
                                          Json json_doc = Json::parse(data, nullptr, false);

                                          if (json_doc.is_discarded())
                                          {
                                              if (callback.isCallable())
                                              {
                                                  QJSValueList arglist;
                                                  arglist.push_back(false);
                                                  arglist.push_back("");
                                                  arglist.push_back(QString::fromStdString(data));
                                                  callback.call(arglist);
                                              }
                                          }
                                          else
                                          {
                                              QVariant var_tmp;
                                              tryFromJson(json_doc, "status", var_tmp);
                                              auto status = var_tmp.toInt();

                                              // 登录成功
                                              if (status == 0)
                                              {
                                                  if (json_doc.contains("data"))
                                                  {
                                                      auto &json_data = json_doc.at("data");

                                                      QVariant var_tmp;
                                                      tryFromJson(json_data, "shopName", var_tmp);
                                                      shop_name_ = var_tmp.toString();

                                                      tryFromJson(json_data, "shopUnique", var_tmp);
                                                      shop_unique_ = var_tmp.toULongLong();

                                                      tryFromJson(json_data, "goodsInPriceType", var_tmp);
                                                      goods_in_price_type_ = var_tmp.toULongLong();

                                                      tryFromJson(json_data, "login_id", var_tmp);
                                                      login_id_ = var_tmp.toULongLong();
                                                      tryFromJson(json_data, "shopImage", var_tmp);
                                                      shop_image_path_ = var_tmp.toString();

                                                      tryFromJson(json_data, "shopAddressDetail", var_tmp);
                                                      shop_address_detail_ = var_tmp.toString();

                                                      tryFromJson(json_data, "staffName", var_tmp);
                                                      person_info_.name = var_tmp.toString();

                                                      tryFromJson(json_data, "staffPhone", var_tmp);
                                                      person_info_.phone = var_tmp.toString();

                                                      tryFromJson(json_data, "shopType", var_tmp);
                                                      shop_type_ = var_tmp.toString();
                                                      //是否是海外商城系统
                                                      tryFromJson(json_data, "novawholesales", var_tmp);
                                                      person_info_.novawholesales = var_tmp.toString();

                                                      tryFromJson(json_data, "staffId", var_tmp);
                                                      person_info_.cashier_id = var_tmp.toULongLong();

                                                      login_account_ = id;

                                                      auto mqtt_ctrl = ControlManager::getInstance()->getMqttControl();
                                                      mqtt_ctrl->disconnectFromHost();
                                                      mqtt_ctrl->setCleanSession(true);
                                                      mqtt_ctrl->connectToHost();

                                                      auto config_tool = ConfigTool::getInstance();
                                                      if (id != config_tool->getSetting(ConfigToolEnum::ConfigEnum::USER_ACCOUNT).toString())
                                                      {
                                                          DataManager::getInstance()->getGoodsMgr()->deleteAllGoods();
                                                          DataManager::getInstance()->getGoodsKindMgr()->deleteAllGoodsKind();

                                                          config_tool->setSetting(ConfigEnum::REQ_GOODS_BEGIN_DATE_TIME, "");
                                                          config_tool->setSetting(ConfigToolEnum::ConfigEnum::USER_ACCOUNT, id);
                                                          config_tool->setSettingShopInfo(ConfigToolEnum::ConfigEnum::USER_ACCOUNT_SHOPINFO, id);
                                                      }
                                                      config_tool->setSetting(ConfigToolEnum::ConfigEnum::USER_PASSWORD, passwd);

                                                      LOG_EVT_INFO("开始更新本地数据库表信息");
                                                      GoodsPromotionCtrl::getInstance()->queryGoodsPromotionRequest();//更新本地数据库表信息
                                                      GoodsPromotionCtrl::getInstance()->deleteGoodsPromotion2(); // 程序启动删除已过期的商品促销
                                                      GoodsPromotionCtrl::getInstance()->queryOrderPromotionForQml();//请求云端数据存入配置文件
                                                      GoodsPromotionCtrl::getInstance()->queryPromotionGoodsSingle();//更新表goodsPromotion3数据
                                                      LOG_EVT_INFO("结束更新本地数据库表信息");
                                                      ///-------------------------------------------| 权限 |-------------------------------------------
                                                      //
                                                      LOG_EVT_INFO("在线登录后的数据库权限信息写入");
                                                      QSqlDatabase db;
                                                      QString connectionName = "ShopInfo";
                                                      if (QSqlDatabase::contains(connectionName))
                                                      {
                                                          db = QSqlDatabase::database(connectionName);
                                                      }
                                                      else
                                                      {
                                                          db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
                                                      }
                                                      QString dataPath = Utils::getDataBasePath();
                                                      db.setDatabaseName(dataPath + "buyhoo2.db");
                                                      if (db.open() == false)
                                                      {
                                                          LOG_EVT_INFO("dishdb数据库打开失败loginPage3");
                                                          exit(0);
                                                          return;
                                                      }
                                                      QSqlQuery   dbQuery(db);
                                                      QStringList list = db.tables();

                                                      if (!list.contains("shops"))
                                                      {
                                                          dbQuery.prepare("create table shops(shop_id integer primary key,"
                                                                          "shop_unique int64, "
                                                                          "login_id int64, "
                                                                          "shop_name varchar,"
                                                                          "manager_unique varchar,"
                                                                          "manager_account varchar,"
                                                                          "manager_pwd varchar,"
                                                                          "shop_alias varchar,"
                                                                          "trade_area_unique varchar,"
                                                                          "shop_latitude decimal,"
                                                                          "shop_longitude decimal,"
                                                                          "shop_seller_email varchar,"
                                                                          "shop_address_detail varchar,"
                                                                          "shop_phone varchar,"
                                                                          "shop_remark varchar,"
                                                                          "shop_image_path varchar,"
                                                                          "shop_partner varchar,"
                                                                          "shop_APP_ID varchar,"
                                                                          "shop_MCH_ID varchar,"
                                                                          "shop_API_KEY varchar,"
                                                                          "area_dict_num varchar,"
                                                                          "examinestatus integer,"
                                                                          "power_supplier integer,"
                                                                          "examinestatus_reason varchar,"
                                                                          "shop_hours varchar,"
                                                                          "shop_counter varchar,"
                                                                          "channel_id varchar,"
                                                                          "shop_source integer,"
                                                                          "cashier_id varchar,"
                                                                          "sameType integer,"
                                                                          "pc_update_selling_price varchar,"
                                                                          "pc_update_stock varchar,"
                                                                          "pc_stock_goods_calss varchar,"
                                                                          "pc_update_purchase_price varchar,"
                                                                          "pc_update_goods_name varchar,"
                                                                          "pc_goods_delete varchar,"
                                                                          "pc_goods_add varchar,"
                                                                          "pc_member_recharge varchar,"
                                                                          "pc_member_exchange varchar,"
                                                                          "pc_member_update varchar,"
                                                                          "pc_member_register varchar,"
                                                                          "pc_warehousing varchar,"
                                                                          "pc_out_stock varchar,"
                                                                          "pc_update_member_price varchar,"
                                                                          "pc_member_add_increase varchar,"
                                                                          "pc_member_delete varchar,"
                                                                          "in_store_record varchar,"
                                                                          "sale_record varchar,"
                                                                          "pc_cash varchar,"
                                                                          "pc_query varchar,"
                                                                          "pc_netlist varchar,"
                                                                          "pc_instock varchar,"
                                                                          "pc_member_manger varchar,"
                                                                          "pc_statistics varchar,"
                                                                          "pc_query_inout_stock varchar,"
                                                                          "pc_query_sale_record varchar,"
                                                                          "pc_query_change_shifts varchar,"
                                                                          "pc_query_goods_list varchar,"
                                                                          "pc_query_goods_sales varchar,"
                                                                          "pc_netlist_new_order varchar,"
                                                                          "pc_netlist_pending_shipment varchar,"
                                                                          "pc_netlist_delivery_in varchar,"
                                                                          "pc_netlist_completed varchar,"
                                                                          "pc_netlist_cancel varchar,"
                                                                          "pc_cash_goods_manger varchar,"
                                                                          "pc_setting varchar,"
                                                                          "changeshift_record varchar,"
                                                                          "goods_info_record varchar,"
                                                                          "goods_sale_record varchar,"
                                                                          "power_check_page varchar,"
                                                                          "power_wangdan_page varchar,"
                                                                          "power_instore_page varchar,"
                                                                          "power_member_page varchar,"
                                                                          "power_pcstatis_page varchar,"
                                                                          "power_settings_page varchar,"
                                                                          "shop_type varchar)");
                                                          if (!dbQuery.exec())
                                                          {
                                                              LOG_EVT_INFO("建店铺信息表失败");
                                                          }
                                                          dbQuery.prepare("insert into shops (manager_pwd ) values (?)");
                                                          dbQuery.addBindValue(passwd);
                                                          dbQuery.exec();
                                                      }
                                                      else
                                                      {
                                                          auto config_tool = ConfigTool::getInstance();
                                                          QString strSql = "select * from shops where manager_account=";
                                                          strSql +=config_tool->getSettingShopInfo(ConfigToolEnum::ConfigEnum::USER_ACCOUNT_SHOPINFO).toString();
                                                          dbQuery.exec(strSql);
                                                          LOG_EVT_INFO("1111@@@@@@@@@@@@@:{}",strSql.toStdString());
                                                          int flag = 0;
                                                          while(dbQuery.next())
                                                          {
                                                              flag = 1;
                                                          }
                                                          LOG_EVT_INFO("flag:{}",flag);
                                                          if (flag == 0)
                                                          {
                                                              QString strSql = "";
                                                              strSql += "insert into shops (shop_unique,manager_account,manager_pwd,cashier_id,login_id,shop_type) values (";
                                                              strSql += QString::number(getShopUnique());
                                                              strSql += ",'";
                                                              strSql += id;
                                                              strSql += "','";
                                                              strSql += passwd;
                                                              strSql += "','";
                                                              strSql += QString::number(person_info_.cashier_id);
                                                              strSql += "','";
                                                              strSql += QString::number(login_id_);
                                                              strSql += "','";
                                                              strSql += shop_type_;
                                                              strSql += "')";
                                                              bool success =  dbQuery.exec(strSql);
                                                              if (!success) {
                                                                  LOG_EVT_INFO("shops表存在，但是其中未查到本账号信息，插入数据命令:{}",strSql.toStdString());
                                                                  LOG_EVT_INFO("插入数据失败！原因为:{}",dbQuery.lastError().text().toStdString());
                                                              }else{
                                                                  LOG_EVT_INFO("插入数据成功！ shops表存在，但是其中未查到本账号信息，插入数据命令:{}",strSql.toStdString());
                                                              }
                                                          }
                                                          else if (flag == 1)
                                                          {
                                                              QString sqlStr = "";
                                                              sqlStr += "update shops set shop_unique=";
                                                              sqlStr += QString::number(getShopUnique());
                                                              sqlStr += ",manager_account='";
                                                              sqlStr += id;
                                                              sqlStr += "',manager_pwd='";
                                                              sqlStr += passwd;
                                                              sqlStr += "',cashier_id='";
                                                              sqlStr += QString::number(person_info_.cashier_id);
                                                              sqlStr += "',login_id='";
                                                              sqlStr += QString::number(login_id_);
                                                              sqlStr += "',shop_type='";
                                                              sqlStr += shop_type_;
                                                              sqlStr += "' where manager_account=";
                                                              sqlStr += id;
                                                              dbQuery.exec(sqlStr);
                                                              LOG_EVT_INFO("shops表存在，其中已查到本账号信息，更新数据命令:{}",sqlStr.toStdString());
                                                          }
                                                      }

                                                      auto permission_control = ControlManager::getInstance()->getPermissionControl();
                                                      permission_control->clearPermission();
                                                      if (json_data.contains("v2List"))
                                                      {
                                                          auto action_list_j = json_data["v2List"];
                                                          std::map<std::string, std::string> loadedMap = loadMapFromFile("permission.txt");
                                                          if (action_list_j.is_array())
                                                          {
                                                              for (auto action_item : action_list_j)
                                                              {
                                                                  std::string code;
                                                                  std::string ids;
                                                                  QVariant cods;
                                                                  tryFromJson(action_item, "powerName", cods);
                                                                  if( cods.toString().toStdString() == ""){
                                                                      std::cout<<"powerName is null"<<std::endl;
                                                                  }else{
                                                                      LOG_EVT_INFO("读取到权限:{}",cods.toString().toStdString());
                                                                      code = cods.toString().toStdString();
                                                                      ids = findAndOutputValue(loadedMap, code);
                                                                      if(ids != ""){
      //                                                                      std::cout<<cods.toString().toStdString()<<":"<<ids<<std::endl;
                                                                            permission_control->addPermission(static_cast<PermissionEnum>( std::stoi(ids)));
                                                                            //添加权限设置到数据库中 函数参数（id,dbQuery指针）
                                                                            bool result = updateShopInfo(std::stoi(ids), dbQuery,id);
                                                                            if (result) {
                                                                                LOG_EVT_INFO("权限字段：{}更新数据库成功！",ids );
                                                                            } else {
                                                                                LOG_EVT_INFO("权限字段：{}更新数据库失败！",ids );
                                                                            }
                                                                      }else{
                                                                        std::cout<<cods.toString().toStdString()<<"don't map"<<std::endl;
                                                                      }
                                                                  }
                                                          }
                                                      }
                                                      }
                                                      /*if (json_data.contains("actionList"))
                                                      {
                                                          auto action_list_j = json_data["actionList"];

                                                          if (action_list_j.is_array())
                                                          {
                                                              for (auto action_item : action_list_j)
                                                              {
                                                                  QVariant ids;
                                                                  tryFromJson(action_item, "id", ids);
                                                                  QVariant havePower;
                                                                  tryFromJson(action_item, "havePower", havePower);
                                                                  if (havePower.toInt() == 1)
                                                                  {
                                                                      permission_control->addPermission(static_cast<PermissionEnum>(ids.toInt()));
                                                                      //添加权限设置到数据库中 函数参数（id,dbQuery指针）
                                                                      bool result = updateShopInfo(ids.toInt(), dbQuery,id);
                                                                      if (result) {
                                                                      } else {
                                                                          LOG_EVT_INFO("权限字段：{}更新数据库失败！",ids.toString().toStdString() );
                                                                      }
                                                                  }
                                                              }
                                                          }
                                                      }*/

                                                      /*if (json_data.contains("listLevel"))
                                                      {
                                                          auto list_level_j = json_data["listLevel"];

                                                          if (list_level_j.is_array())
                                                          {
                                                              for (auto list_level_item : list_level_j)
                                                              {
                                                                  QVariant ids;
                                                                  tryFromJson(list_level_item, "id", ids);
                                                                  QVariant havePower;
                                                                  tryFromJson(list_level_item, "havePower", havePower);
                                                                  if (havePower.toInt() == 1){
                                                                      permission_control->addPermission(static_cast<PermissionEnum>(ids.toInt()));
                                                                      LOG_EVT_INFO("2权限id:{}",ids.toInt());
                                                                      //添加权限设置到数据库中
                                                                      bool result = updateShopInfo(ids.toInt(), dbQuery,id);
                                                                      if (result) {
                                                                          //LOG_EVT_INFO("权限字段：{}更新数据库成功！",ids.toString().toStdString() );
                                                                      } else {
                                                                          LOG_EVT_INFO("权限字段：{}更新数据库失败！",ids.toString().toStdString() );
                                                                      }
                                                                  }

                                                                  if (list_level_item.contains("listLeverlTwo"))
                                                                  {
                                                                      auto list_leverl_two = list_level_item["listLeverlTwo"];
                                                                      if (list_leverl_two.is_array())
                                                                      {
                                                                          for (auto list_leverl_two_item : list_leverl_two)
                                                                          {
                                                                              QVariant ids;
                                                                              tryFromJson(list_leverl_two_item, "id", ids);
                                                                              QVariant havePower;
                                                                              tryFromJson(list_leverl_two_item, "havePower", havePower);

                                                                              if (havePower.toInt() == 1)
                                                                              {
                                                                                  permission_control->addPermission(static_cast<PermissionEnum>(ids.toInt()));
                                                                                  LOG_EVT_INFO("权限id:{}",ids.toInt());
                                                                                  //添加权限设置到数据库中
                                                                                  if(ids.toInt() == 114){
                                                                                     ids = 1141;
                                                                                  }
                                                                                  bool result = updateShopInfo(ids.toInt(), dbQuery,id);
                                                                                  if (result) {
                                                                                      //LOG_EVT_INFO("权限字段：{}更新数据库成功！",ids.toString().toStdString() );
                                                                                  } else {
                                                                                      LOG_EVT_INFO("权限字段：{}更新数据库失败！",ids.toString().toStdString() );
                                                                                  }
                                                                              }
                                                                          }
                                                                      }
                                                                  }
                                                              }
                                                          }
                                                      }*/
                                                      //
                                                      ///-------------------------------------------| 权限 |-------------------------------------------


                                                      loginWizard();
                                                      db.close();

                                                      LOG_EVT_INFO("检查更新");
                                                      emit sigCheckUpdate();
                                                      LOG_EVT_INFO("发送检查更新信号成功");


                                                      //触发回调
                                                      if (callback.isCallable())
                                                      {
                                                          QJSValueList arglist;
                                                          arglist.push_back(true);
                                                          arglist.push_back(QString::fromStdString(data));
                                                          callback.call(arglist);
                                                      }
//                                                      //自动上传未提交的离线订单
//                                                      PayMethodControl *payMethodControl      = new PayMethodControl(this);
//                                                      payMethodControl->uploadNotUploadOrder_offlineOrder();
                                                  }
                                                  else
                                                  {
                                                      if (callback.isCallable())
                                                      {
                                                          QJSValueList arglist;
                                                          arglist.push_back(false);
                                                          arglist.push_back(QString::fromStdString(data));
                                                          callback.call(arglist);
                                                      }
                                                  }
                                              }
                                              else if (status == 1)
                                              {
                                                  if (callback.isCallable())
                                                  {
                                                      QJSValueList arglist;
                                                      arglist.push_back(false);
                                                      arglist.push_back(QString::fromStdString(data));
                                                      callback.call(arglist);
                                                  }
                                              }
                                          }
                                          break;
                                      }
                                  case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                                  case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                      {
                                          if (callback.isCallable())
                                          {
                                              QJSValueList arglist;
                                              arglist.push_back(false);
                                              arglist.push_back("");
                                              callback.call(arglist);
                                          }
                                          break;
                                      }
                                  }

                                  login_mutex.unlock();
                              });
      }
}

void ShopControl::loginWizard()
{
    auto control_manager = ControlManager::getInstance();
    auto data_mgr        = DataManager::getInstance();
    auto config_tool     = ConfigTool::getInstance();

    control_manager->getShopCartList()->clearAllGoods();
    control_manager->getGoodsControl()->resetGoodsFlag();

    QString store_id    = config_tool->getSetting(ConfigToolEnum::ConfigEnum::STORE_ID).toString();
    QString store_name  = config_tool->getSetting(ConfigToolEnum::ConfigEnum::STORE_NAME).toString();
    QString store_addr  = config_tool->getSetting(ConfigToolEnum::ConfigEnum::STORE_ADDRESS).toString();
    QString store_phone = config_tool->getSetting(ConfigToolEnum::ConfigEnum::STORE_PHONE_NUMBER).toString();

//    if (store_id.isEmpty() || store_id == QString::number(getShopUnique()))

    if (store_id == QString::number(getShopUnique()))
    {
        //登录相同账号
        if (store_name.isEmpty())
        {
            config_tool->setSetting(ConfigToolEnum::ConfigEnum::STORE_NAME, getShopName(false));
        }
        if (store_addr.isEmpty())
        {
            config_tool->setSetting(ConfigToolEnum::ConfigEnum::STORE_ADDRESS, getShopAddress(false));
        }
        if (store_phone.isEmpty())
        {
            config_tool->setSetting(ConfigToolEnum::ConfigEnum::STORE_PHONE_NUMBER, getUserPhone(false));
        }

        ///-------------------------------------------| 获取本地数据 |-------------------------------------------
        //
        data_mgr->getGoodsMgr()->getLocalGoodsData_Thread();
        data_mgr->getGoodsKindMgr()->getLocalGoodsKindData_Thread();
        data_mgr->getVirtualGoodsKindMgr()->getLocalVGoodsKindData_Thread();
        //
        ///-------------------------------------------| 获取本地数据 |-------------------------------------------
    }
    else
    {
        //登录不同账号
        LOG_EVT_INFO("当使用不同的账户登录时");
        config_tool->setSetting(ConfigToolEnum::ConfigEnum::STORE_ID, getShopUnique());
        config_tool->setSetting(ConfigToolEnum::ConfigEnum::STORE_NAME, getShopName(false));
        config_tool->setSetting(ConfigToolEnum::ConfigEnum::STORE_ADDRESS, getShopAddress(false));
        config_tool->setSetting(ConfigToolEnum::ConfigEnum::STORE_PHONE_NUMBER, getUserPhone(false));

        control_manager->getOnlineInfo(true);
    }
    setIsLogin(true);

    emit loggedIn();
}

void ShopControl::reqHandOverInfo4Qml(QJSValue callback)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/pc/pcStaffSignOut.do";
    bodyMap body_map;
    body_map["login_id"] = QString::number(shop_control->login_id_);

    auto scoped_thread = new QThread(this); // 工作线程

    HttpWorker *scoped_http = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    scoped_http->moveToThread(scoped_thread);

    connect(scoped_thread, &QThread::started, scoped_http, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(scoped_thread, &QThread::finished, scoped_http, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(scoped_http, &HttpWorker::destroyed, scoped_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(scoped_http, &HttpWorker::sendReply, this,                                  // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                        auto ret = callback.call(arglist);
                    }
                }
                scoped_thread->quit();
                scoped_thread->wait();
            });
    // 启动线程
    scoped_thread->start();
}

void ShopControl::reqHandOver4Qml(QJSValue callback)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/pc/staffSignOut.do";
    bodyMap body_map;
    body_map["login_id"] = QString::number(shop_control->login_id_);

    auto scoped_thread_p = new QThread(this); // 工作线程

    HttpWorker *scoped_http_worker = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    scoped_http_worker->moveToThread(scoped_thread_p);

    connect(scoped_thread_p, &QThread::started, scoped_http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(scoped_thread_p, &QThread::finished, scoped_http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(scoped_http_worker, &HttpWorker::destroyed, scoped_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(scoped_http_worker, &HttpWorker::sendReply, this,                                    // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);

                    auto status = var_tmp.toInt();

                    if (status == 0)
                    {
                        ControlManager::getInstance()->getMqttControl()->sendWillMsg();
                        //ControlManager::getInstance()->getMqttControl()->setAutoReconnect(false);
                        ControlManager::getInstance()->getMqttControl()->disconnectFromHost();

                        ControlManager::getInstance()->getGoodsControl()->markCurDateTime4Req();

                        auto shop_ctrl = ControlManager::getInstance()->getShopControl();
                        shop_ctrl->setIsLogin(false);

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                scoped_thread_p->quit();
                scoped_thread_p->wait();
            });
    // 启动线程
    scoped_thread_p->start();
}

PersonInfo ShopControl::getPersonInfo()
{
    return person_info_;
}

const QString ShopControl::getShopName(bool is_from_cfg)
{
    if (is_from_cfg)
    {
        auto config_tool = ConfigTool::getInstance();
        return config_tool->getSetting(ConfigToolEnum::ConfigEnum::STORE_NAME).toString();
    }
    return shop_name_;
}

unsigned long long ShopControl::getShopUnique()
{
    return shop_unique_;
}
unsigned long long ShopControl::getGoodsInPriceType()
{
    return goods_in_price_type_;
}
unsigned long long ShopControl::getLoginId()
{
    return login_id_;
}
const QString ShopControl::getShopImagePath()
{
    return shop_image_path_;
}
QString ShopControl::getShopType()
{
    return shop_type_;
}
const QString ShopControl::getShopAddress(bool is_from_cfg)
{
    if (is_from_cfg)
    {
        auto config_tool = ConfigTool::getInstance();
        return config_tool->getSetting(ConfigToolEnum::ConfigEnum::STORE_ADDRESS).toString();
    }

    return shop_address_detail_;
}

QString ShopControl::getUserName()
{
    return person_info_.name;
}
QString ShopControl::getUserPhone(bool is_from_cfg)
{
    if (is_from_cfg)
    {
        auto config_tool = ConfigTool::getInstance();
        return config_tool->getSetting(ConfigToolEnum::ConfigEnum::STORE_PHONE_NUMBER).toString();
    }
    return person_info_.phone;
}
QString ShopControl::getNovawholesales()
{
    return person_info_.novawholesales;
}
unsigned long long ShopControl::getUserId()
{
    return person_info_.cashier_id;
}

//void ShopControl::reqRechargeConfig(QJSValue callback)
//{
//    QString url = req_host_prefix + "harricane/cuscheckout/queryRechargeConfig.do";

//    Json body_json;
//    body_json["shop_unique"] = shop_unique_;

//    auto callback4Qml = [](QJSValue callback, bool is_succ = false, QString data = "")
//    {
//        if (callback.isCallable())
//        {
//            QJSValueList arglist;
//            arglist.push_back(is_succ);
//            arglist.push_back(data);
//            callback.call(arglist);
//        }
//    };

//    HttpClient *client = new HttpClient();
//    client->post(url)
//        .bodyWithJson(body_json.dump())
//        .onResponse(
//            [=](QNetworkReply *)
//            {
//                client->deleteLater();
//            })
//        .onSuccess(
//            [=](QString result)
//            {
//                auto result_str = result.toStdString();

//                Json json_doc = Json::parse(result_str, nullptr, false);

//                if (json_doc.is_discarded() && json_doc.contains("data"))
//                {
//                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "{}: parse err \n{}", url.toStdString(), result_str);
//                    return;
//                }
//                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "{}: parse succ \n{}", url.toStdString(), result_str);

//                QVariant var_tmp;
//                tryFromJson(json_doc, "status", var_tmp);

//                auto status = var_tmp.toInt();

//                callback4Qml(callback, status == 1, QString::fromStdString(json_doc.dump()));
//            })
//        .onFailed(
//            [=](QString error)
//            {
//                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "{} onFailed \n{}", url.toStdString(), error.toStdString());
//                callback4Qml(callback, false);
//            })
//        .onTimeout(
//            [=]()
//            {
//                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "{} onTimeout", url.toStdString());
//                callback4Qml(callback, false);
//            })
//        .exec();
//}
void ShopControl::reqRechargeConfig(QJSValue callback)
{
    QString url = req_host_prefix + "harricane/cuscheckout/queryRechargeConfig.do";

    QVariantMap post_map;
    post_map.insert("shop_unique", shop_unique_);

    HttpClient *http_client = new HttpClient();

    auto &&request = http_client->post(url);
    request.bodyWithFormUrlencoded(post_map);


    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    Json json_doc = Json::parse(data);

                                    auto status = getDataFromJson(json_doc, "status").toInt();

                                    if (status == 1)
                                    {
                                        if (callback.isCallable())
                                        {
                                            QJSValueList arglist;
                                            arglist.push_back(true);
                                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                                            callback.call(arglist);
                                        }
                                    }
                                    else{
                                        if (callback.isCallable())
                                        {
                                            QJSValueList arglist;
                                            arglist.push_back(false);
                                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                                            callback.call(arglist);
                                        }
                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    break;
                                }
                            }
                            http_client->deleteLater();
                        });
}
void ShopControl::reqShopBeans()
{
    QString url = req_host_prefix + "harricane/payOnline/queryShopBeans.do";

    QVariantMap post_map;
    post_map.insert("shop_unique", getShopUnique());

    HttpClient *http_client = new HttpClient();

    auto &&request = http_client->post(url);
    request.bodyWithFormUrlencoded(post_map);


    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    Json json_doc = Json::parse(data);

                                    auto status = getDataFromJson(json_doc, "status").toInt();

                                    if (status == 1)
                                    {
                                        if (json_doc.contains("data") && json_doc["data"].contains("shop_beans"))
                                        {
                                            auto beans_num = getDataFromJson(json_doc["data"], "shop_beans").toUInt();
                                            setShopBeans(beans_num);
                                        }
                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    break;
                                }
                            }
                            http_client->deleteLater();
                        });
}

QString ShopControl::getLoginAccount()
{
    return login_account_;
}

int ShopControl::shopBeans()
{
    return shop_beans_;
}

void ShopControl::setShopBeans(int beans)
{
    shop_beans_ = beans;
    emit shopBeansChanged();
}

bool ShopControl::isLogin()
{
    return is_login_;
}
int ShopControl::goodsInPriceType()
{
    return goods_in_price_type_;
}
void ShopControl::setIsLogin(bool is_login)
{
    is_login_ = is_login;
    emit isLoginChanged();
}
