﻿#include "SupplierControl.h"
#include "ControlModule/ControlManager.h"
#include "LogManager.h"
#include "NetModule/HttpWorker.h"
#include "NetModule/NetGlobal.h"

SupplierControl::SupplierControl(QObject *parent) : QObject{parent}
{
}

void SupplierControl::reqAllSupplierCategory4Qml(QJSValue callback)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/html/supplier/querySupplierKindByShopUnique.do";
    bodyMap body_map;
    body_map["shop_unique"] = QString::number(shop_control->getShopUnique());

    auto cur_thread_p = new QThread(this); // 工作线程

    auto url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str, "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
                cur_thread_p = nullptr;
            });
    // 启动线程
    cur_thread_p->start();
}

void SupplierControl::reqSupplierCategory4Qml(QJSValue callback)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/html/supplier/querySupplierKindList.do";
    bodyMap body_map;
    body_map["shop_unique"] = QString::number(shop_control->getShopUnique());
    body_map["page"]        = "1";
    body_map["limit"]       = "100";

    auto cur_thread_p = new QThread(this); // 工作线程

    auto url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str, "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void SupplierControl::reqCreateSupplierCategory4Qml(QJSValue callback, QVariant supplier_name)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/html/supplier/saveSupplierKind.do";
    bodyMap body_map;
    body_map["shop_unique"]        = QString::number(shop_control->getShopUnique());
    body_map["supplier_kind_name"] = supplier_name.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    auto url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str, "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);
                    auto status = var_tmp.toInt();

                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(status == 1 ? true : false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                        auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void SupplierControl::reqDelSupplierCategory4Qml(QJSValue callback, QVariant category_id)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/html/supplier/deleteSupplierKind.do";
    bodyMap body_map;
    body_map["id"] = category_id.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    auto url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str, "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);
                    auto status = var_tmp.toInt();

                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(status == 1 ? true : false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                        auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void SupplierControl::reqChangeSupplierCategory4Qml(QJSValue callback, QVariant category_id, QVariant categoty_name)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/html/supplier/editSupplierKind.do";
    bodyMap body_map;
    body_map["id"]                 = category_id.toString();
    body_map["supplier_kind_name"] = categoty_name.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    auto url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str, "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);
                    auto status = var_tmp.toInt();

                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(status == 1 ? true : false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                        auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void SupplierControl::addSupplier4Qml(QJSValue callback,QVariant supplierName)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/pc/addSupplier.do";
    bodyMap body_map;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    body_map["supplierName"] = supplierName.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    auto url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str, "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功:{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);
                    auto status = var_tmp.toInt();

                    if (status == 1)
                    {
                        if (json_doc.contains("data"))
                        {
                            supplier_info_vec_.clear();
                            for (auto &supplier_item : json_doc["data"])
                            {
                                supplier_info_vec_.push_back(supplier_item.get<SupplierInfo>());
                            }
                        }

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void SupplierControl::reqSupplier4Qml(QJSValue callback)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/html/supplier/querySupplierList.do";
    bodyMap body_map;
    body_map["shop_unique"] = QString::number(shop_control->getShopUnique());
    body_map["page"]        = "1";
    body_map["limit"]       = "99";

    auto cur_thread_p = new QThread(this); // 工作线程

    auto url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str, "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);
                    auto status = var_tmp.toInt();

                    if (status == 1)
                    {
                        if (json_doc.contains("data"))
                        {
                            supplier_info_vec_.clear();
                            for (auto &supplier_item : json_doc["data"])
                            {
                                supplier_info_vec_.push_back(supplier_item.get<SupplierInfo>());
                            }
                        }

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(getSupplierJson());
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

QString SupplierControl::getSupplierJson()
{
    Json result;

    for (auto &supplier_info : supplier_info_vec_)
    {
        result.push_back(supplier_info);
    }

    return QString::fromStdString(result.dump());
}

void SupplierControl::reqCreateSupplier4Qml(QJSValue callback, QVariant json_data)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/html/supplier/saveSupplier.do";
    bodyMap body_map;
    body_map["shop_unique"] = QString::number(shop_control->getShopUnique());

    Json json_doc = Json::parse(json_data.toString().toStdString());

    QVariant var_tmp;
    tryFromJson(json_doc, "shop_name", var_tmp);
    body_map["shop_name"] = var_tmp.toString();

    tryFromJson(json_doc, "shop_address_detail", var_tmp);
    body_map["shop_address_detail"] = var_tmp.toString();

    tryFromJson(json_doc, "shop_phone", var_tmp);
    body_map["shop_phone"] = var_tmp.toString();
    tryFromJson(json_doc, "supplier_kind_id", var_tmp);
    body_map["supplier_kind_id"] = var_tmp.toString();

    tryFromJson(json_doc, "company_leagl", var_tmp);
    body_map["company_leagl"] = var_tmp.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    auto url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str, "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void SupplierControl::reqDelSupplier4Qml(QJSValue callback, QVariant supplier_id)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/html/supplier/deleteP.do";
    bodyMap body_map;
    body_map["supplier_id"] = supplier_id.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    auto url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str, "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);

                    auto status = var_tmp.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void SupplierControl::reqChangeSupplier4Qml(QJSValue callback, QVariant json_data)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "harricane/html/supplier/editSupplier.do";
    bodyMap body_map;
    body_map["shop_unique"] = QString::number(shop_control->getShopUnique());

    Json json_doc = Json::parse(json_data.toString().toStdString());

    QVariant var_tmp;

    tryFromJson(json_doc, "supplier_unique", var_tmp);
    body_map["supplier_unique"] = var_tmp.toString();

    tryFromJson(json_doc, "shop_name", var_tmp);
    body_map["shop_name"] = var_tmp.toString();

    tryFromJson(json_doc, "shop_address_detail", var_tmp);
    body_map["shop_address_detail"] = var_tmp.toString();

    tryFromJson(json_doc, "shop_phone", var_tmp);
    body_map["shop_phone"] = var_tmp.toString();
    tryFromJson(json_doc, "supplier_kind_id", var_tmp);
    body_map["supplier_kind_id"] = var_tmp.toString();

    tryFromJson(json_doc, "company_leagl", var_tmp);
    body_map["company_leagl"] = var_tmp.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    auto url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str, "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
