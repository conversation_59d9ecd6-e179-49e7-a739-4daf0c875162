﻿#include "ControlManager.h"
#include <QDebug>
#include <QFontDatabase>
#include "ControlModule/LogCtrl.h"

std::unique_ptr<ControlManager> ControlManager::singleton_;
std::mutex                      ControlManager::mutex_;

ControlManager::~ControlManager()
{
    qDebug() << "~ControlManager() B";
    delete goods_control_;
    delete shop_cart_list_;
    delete order_control_;
    delete shop_control_;
    delete pay_method_control_;
    delete business_alarm_control_;
    delete camera_control_;
    delete printer_control_;
    delete net_order_control_;
    delete member_control_;
    delete weighting_scale_control_;
    delete supplier_control_;
    delete mqtt_control_;
    delete barcode_label_scale_;
    delete permission_ctrl_;
    delete update_ctrl_;
    delete log_ctrl_;
    delete sound_ctrl_;
    delete net_status_ctrl_;
    qDebug() << "~ControlManager() E";
}

ControlManager *ControlManager::getInstance()
{
    std::lock_guard<std::mutex> lock(mutex_);

    if (singleton_ == nullptr)
    {
        singleton_.reset(new ControlManager());
    }
    return singleton_.get();
}
void ControlManager::setLoadingIndex(int index)
{
    loading_index_ = index;
    emit loadingIndexChanged();
}
int ControlManager::loadingIndex()
{
    return loading_index_;
}
void ControlManager::initControls()
{
    tts_control_->initTtsControl();
    order_control_->initDataByConfig();
    camera_control_->init();
}

void ControlManager::getOnlineInfo(bool is_force_get_all)
{
    ///-------------------------------------------| 获取在线数据 |-------------------------------------------
    //
    getGoodsControl()->reRequestData(is_force_get_all);
    getSupplierControl()->reqSupplier4Qml();
    //getUpdateCtrl()->reqGetVersionInfo(0, true);//接口失效 暂时关闭
    getPayMethodControl()->reqMiniProgramCode();
    getShopControl()->reqShopBeans();
    //
    ///-------------------------------------------| 获取在线数据 |-------------------------------------------
}

ControlManager::ControlManager(QObject *parent) : QObject{parent}
{
    weighting_scale_control_ = new WeightingScaleControl(this);
    goods_control_           = new GoodsControl(this);
    shop_cart_list_          = new ShopCartList(this);
    order_control_           = new OrderControl(this);
    shop_control_            = new ShopControl(this);
    pay_method_control_      = new PayMethodControl(this);
    business_alarm_control_  = new BusinessAlarmControl(this);
    camera_control_          = new CameraControl(this);
    printer_control_         = new PrinterControl(this);
    net_order_control_       = new NetOrderControl(this);
    member_control_          = new MemberControl(this);
    supplier_control_        = new SupplierControl(this);
    mqtt_control_            = new MqttControl(HOST_MQTT, PORT_MQTT);
    tts_control_             = new TtsControl();
    barcode_label_scale_     = new BarcodeLabelScale();
    permission_ctrl_         = new PermissionCtrl();
    update_ctrl_             = new UpdateCtrl();
    log_ctrl_                = new LogCtrl();
    sound_ctrl_              = new SoundCtrl();
    net_status_ctrl_         = new NetStatusCtrl();
}
