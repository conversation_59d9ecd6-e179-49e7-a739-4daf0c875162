﻿#include "GoodsPromotionCtrl.h"
#include <algorithm>
#include <map>
#include <vector>
#include <QDateTime>
#include <QDir>
#include "../Utils/Utils.h"
#include "../LogModule/LogManager.h"
#include "../NetModule/NetGlobal.h"
#include "../ControlModule/ControlManager.h"
#include "../NetModule/HttpWorker.h"

std::unique_ptr<GoodsPromotionCtrl> GoodsPromotionCtrl::singleton_;
std::mutex                  GoodsPromotionCtrl::mutex_;

GoodsPromotionCtrl::GoodsPromotionCtrl(QObject *parent) : QObject{parent}
{

}
GoodsPromotionCtrl::~GoodsPromotionCtrl()
{
}
GoodsPromotionCtrl *GoodsPromotionCtrl::getInstance()
{
    std::lock_guard<std::mutex> lock(mutex_);

    if (singleton_ == nullptr)
    {
        singleton_.reset(new GoodsPromotionCtrl());
    };
    return singleton_.get();
}
QString GoodsPromotionCtrl::queryGoodsPromotionForQml2(QString goodMessage)
{

    LOG_EVT_INFO("queryGoodsPromotionForQml2");
    QString      goodsPromotion2;
    QSqlDatabase db;
    QString      connectionName = "goodsPromotion111";
    if (QSqlDatabase::contains(connectionName))
    {
        db = QSqlDatabase::database(connectionName);
    }
    else
    {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        // db.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
    }
    QString dataPath = Utils::getDataBasePath();
    //LOG_EVT_INFO("dataPath:{}",dataPath.toStdString());
    //QString dataPath = Common::getDataBasePath();
    db.setDatabaseName(dataPath + "buyhoo.db"); // 促销
    if (db.open() == false)
    {
        //std::cout<< "  vv " << std::endl;
       // //LOG_EVT_INFO("数据库打开失败:{}",db.lastError().text().toStdString());
    }
    QSqlQuery   dbQuery(db);
    QStringList list = db.tables();
    if (!list.contains("goodsPromotion2"))
    {
        QString sqlCreatTable = "create table goodsPromotion2 (goods_id integer,"
                                "meet_count1 varchar, "
                                "meet_count2 varchar,"
                                "meet_count3 varchar,"
                                "gift_count1 varchar,"
                                "gift_count2 varchar,"
                                "gift_count3 varchar,"
                                "goods_id_gift varchar,"
                                "start_time varchar,"
                                "end_time varchar)";
        if (!dbQuery.exec(sqlCreatTable))
        {
            LOG_EVT_INFO("表goodsPromotion2创建失败");
            //MYLOG << QStringLiteral("3建商品信息表失败:") << dbQuery.lastError().text();
        }else{
            LOG_EVT_INFO("表goodsPromotion2创建成功");
        }
    }
    else
    {
        QDate   date = QDate::currentDate();
        QDate   date1;
        QDate   date2;
        QString selectSql;
        selectSql = "select goods_id, meet_count1 ,meet_count2, meet_count3, gift_count1, gift_count2, gift_count3, "
                    "goods_id_gift, start_time, end_time from goodsPromotion2 where goods_id='";
        selectSql += goodMessage;
        selectSql += "'";
        dbQuery.exec(selectSql);
        LOG_EVT_INFO("表goodsPromotion2开始查询数据库 查询语句为:{}",selectSql.toStdString());
        while (dbQuery.next())
        {
            LOG_EVT_INFO("表goodsPromotion2开始查询数据库 存在数据");
            date1 = QDate::fromString(dbQuery.value(8).toString(), "yyyy-MM-dd");
            date2 = QDate::fromString(dbQuery.value(9).toString(), "yyyy-MM-dd");
            if (dbQuery.value(0).toString().isEmpty())
            {
                continue;
            }
            if (date.daysTo(date2) >= 0 && date.daysTo(date1) <= 0)
            {
                goodsPromotion2 += dbQuery.value(0).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(1).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(2).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(3).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(4).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(5).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(6).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(7).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(8).toString();
                goodsPromotion2 += ";";
            }
        }
        goodsPromotion2.chop(1);
    }
    LOG_EVT_INFO("queryGoodsPromotionForQml2 执行结束!");
    db.close();
    QString promotionList;
    promotionList = goodsPromotion2;
    LOG_EVT_INFO("queryGoodsPromotionForQml2 执行结束 goodsPromotion2:{}",goodsPromotion2.toStdString());

    return promotionList;
}
QString GoodsPromotionCtrl::queryGoodsPromotionForQml1(QString goodMessage)
{
    LOG_EVT_INFO("queryGoodsPromotionForQml1");
    QString      goodsPromotion1;
    QSqlDatabase db;
    QString      connectionName = "goodsPromotion111";
    if (QSqlDatabase::contains(connectionName))
    {
        db = QSqlDatabase::database(connectionName);
    }
    else
    {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
    }
    QString dataPath = Utils::getDataBasePath();
    db.setDatabaseName(dataPath + "buyhoo.db"); // 促销
    if (db.open() == false)
    {
       LOG_EVT_INFO("数据库打开失败:{}");
    }
    QSqlQuery   dbQuery(db);
    QStringList list = db.tables();
    if (!list.contains("goodsPromotion1"))
    {
        QString sqlCreatTable = "create table goodsPromotion1 (goods_id integer,"
                                "meet_count1 varchar, "
                                "meet_count2 varchar,"
                                "meet_count3 varchar,"
                                "discount_percent1 varchar,"
                                "discount_percent2 varchar,"
                                "discount_percent3 varchar,"
                                "order_activity varchar,"
                                "limit_count varchar,"
                                "start_time varchar,"
                                "end_time varchar)";
        if (!dbQuery.exec(sqlCreatTable))
        {
            LOG_EVT_INFO("表goodsPromotion1创建失败");
            //MYLOG << QStringLiteral("建表失败:") << dbQuery.lastError().text();
        }else{
           LOG_EVT_INFO("表goodsPromotion1创建成功");
        }
    }
    else
    {
        QDate date = QDate::currentDate();
        QDate date1;
        QDate date2;

        QString selectSql;
        selectSql = "select goods_id, meet_count1 ,meet_count2, meet_count3, discount_percent1, discount_percent2, discount_percent3, "
                    "order_activity, limit_count, start_time, end_time from goodsPromotion1 where goods_id='";
        selectSql += goodMessage;
        selectSql += "'";
        dbQuery.exec(selectSql);
        while (dbQuery.next())
        {
            date1 = QDate::fromString(dbQuery.value(9).toString(), "yyyy-MM-dd");
            date2 = QDate::fromString(dbQuery.value(10).toString(), "yyyy-MM-dd");
            if (dbQuery.value(0).toString().isEmpty())
            {
                continue;
            }
            if (date.daysTo(date2) >= 0 && date.daysTo(date1) <= 0)
            {
                goodsPromotion1 += dbQuery.value(0).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(1).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(2).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(3).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(4).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(5).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(6).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(7).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(8).toString();
                goodsPromotion1 += ";";
            }
        }
        goodsPromotion1.chop(1);
    }
    db.close();
    LOG_EVT_INFO("queryGoodsPromotionForQml1 执行结束 goodsPromotion1:{}",goodsPromotion1.toStdString());
    QString promotionList;
    promotionList = goodsPromotion1;
    return promotionList;
}
std::vector<QString> GoodsPromotionCtrl::queryGoodsPromotionForQml(QString goodMessage)
{
    LOG_EVT_INFO("queryGoodsPromotionForQml");
    QString      goodsPromotion1;
    QString      goodsPromotion2;
    QString      goodsPromotion3;
    QSqlDatabase db;
    QString      connectionName = "goodsPromotion111";
    if (QSqlDatabase::contains(connectionName))
    {
        db = QSqlDatabase::database(connectionName);
    }
    else
    {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        // db.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
    }
    QString dataPath = Utils::getDataBasePath();
    //LOG_EVT_INFO("dataPath:{}",dataPath.toStdString());
    //QString dataPath = Common::getDataBasePath();
    db.setDatabaseName(dataPath + "buyhoo.db"); // 促销
    if (db.open() == false)
    {
        //std::cout<< "  vv " << std::endl;
       // //LOG_EVT_INFO("数据库打开失败:{}",db.lastError().text().toStdString());
    }
    QSqlQuery   dbQuery(db);
    QStringList list = db.tables();
    if (!list.contains("goodsPromotion1"))
    {
        QString sqlCreatTable = "create table goodsPromotion1 (goods_id integer,"
                                "meet_count1 varchar, "
                                "meet_count2 varchar,"
                                "meet_count3 varchar,"
                                "discount_percent1 varchar,"
                                "discount_percent2 varchar,"
                                "discount_percent3 varchar,"
                                "order_activity varchar,"
                                "limit_count varchar,"
                                "start_time varchar,"
                                "end_time varchar)";
        if (!dbQuery.exec(sqlCreatTable))
        {
            LOG_EVT_INFO("表goodsPromotion1创建失败");
            //MYLOG << QStringLiteral("建表失败:") << dbQuery.lastError().text();
        }else{
           LOG_EVT_INFO("表goodsPromotion1创建成功");
        }
    }
    else
    {
        QDate date = QDate::currentDate();
        QDate date1;
        QDate date2;

        QString selectSql;
        selectSql = "select goods_id, meet_count1 ,meet_count2, meet_count3, discount_percent1, discount_percent2, discount_percent3, "
                    "order_activity, limit_count, start_time, end_time from goodsPromotion1 where goods_id='";
        selectSql += goodMessage;
        selectSql += "'";
        dbQuery.exec(selectSql);
        while (dbQuery.next())
        {
            date1 = QDate::fromString(dbQuery.value(9).toString(), "yyyy-MM-dd");
            date2 = QDate::fromString(dbQuery.value(10).toString(), "yyyy-MM-dd");
            if (dbQuery.value(0).toString().isEmpty())
            {
                continue;
            }
            if (date.daysTo(date2) >= 0 && date.daysTo(date1) <= 0)
            {
                goodsPromotion1 += dbQuery.value(0).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(1).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(2).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(3).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(4).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(5).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(6).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(7).toString();
                goodsPromotion1 += "^";
                goodsPromotion1 += dbQuery.value(8).toString();
                goodsPromotion1 += ";";
            }
        }
        goodsPromotion1.chop(1);
    }

    if (!list.contains("goodsPromotion2"))
    {
        QString sqlCreatTable = "create table goodsPromotion2 (goods_id integer,"
                                "meet_count1 varchar, "
                                "meet_count2 varchar,"
                                "meet_count3 varchar,"
                                "gift_count1 varchar,"
                                "gift_count2 varchar,"
                                "gift_count3 varchar,"
                                "goods_id_gift varchar,"
                                "start_time varchar,"
                                "end_time varchar)";
        if (!dbQuery.exec(sqlCreatTable))
        {
            LOG_EVT_INFO("表goodsPromotion2创建失败");
            //MYLOG << QStringLiteral("3建商品信息表失败:") << dbQuery.lastError().text();
        }else{
            LOG_EVT_INFO("表goodsPromotion2创建成功");
        }
    }
    else
    {
        QDate   date = QDate::currentDate();
        QDate   date1;
        QDate   date2;
        QString selectSql;
        selectSql = "select goods_id, meet_count1 ,meet_count2, meet_count3, gift_count1, gift_count2, gift_count3, "
                    "goods_id_gift, start_time, end_time from goodsPromotion2 where goods_id='";
        selectSql += goodMessage;
        selectSql += "'";
        dbQuery.exec(selectSql);
        LOG_EVT_INFO("queryGoodsPromotionForQml==表goodsPromotion2开始查询数据库 查询语句为:{}",selectSql.toStdString());
        while (dbQuery.next())
        {
            LOG_EVT_INFO("表goodsPromotion2开始查询数据库 存在数据");
            date1 = QDate::fromString(dbQuery.value(8).toString(), "yyyy-MM-dd");
            date2 = QDate::fromString(dbQuery.value(9).toString(), "yyyy-MM-dd");
            if (dbQuery.value(0).toString().isEmpty())
            {
                continue;
            }
            if (date.daysTo(date2) >= 0 && date.daysTo(date1) <= 0)
            {
                goodsPromotion2 += dbQuery.value(0).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(1).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(2).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(3).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(4).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(5).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(6).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(7).toString();
                goodsPromotion2 += "^";
                goodsPromotion2 += dbQuery.value(8).toString();
                goodsPromotion2 += ";";
            }
        }
        goodsPromotion2.chop(1);
    }

    if (!list.contains("goodsPromotion3"))
    {
        QString sqlCreatTable = "create table goodsPromotion3 (goods_id integer,"
                                "meet_count1 varchar, "
                                "meet_count2 varchar,"
                                "meet_count3 varchar,"
                                "discount_percent1 varchar,"
                                "discount_percent2 varchar,"
                                "discount_percent3 varchar,"
                                "start_time varchar,"
                                "end_time varchar)";
        if (!dbQuery.exec(sqlCreatTable))
        {
            LOG_EVT_INFO("表goodsPromotion3创建失败");
            //MYLOG << QStringLiteral("建表失败:") << dbQuery.lastError().text();
        }else{
            LOG_EVT_INFO("表goodsPromotion3创建成功");
        }
    }
    else
    {
        QDate date = QDate::currentDate();
        QDate date1;
        QDate date2;
        QString selectSql;
        selectSql = "select goods_id, meet_count1 ,meet_count2, meet_count3, discount_percent1, discount_percent2, discount_percent3, "
                    "start_time, end_time from goodsPromotion3 where goods_id='";
        selectSql += goodMessage;
        selectSql += "'";
        dbQuery.exec(selectSql);
        while (dbQuery.next())
        {
            date1 = QDate::fromString(dbQuery.value(7).toString(), "yyyy-MM-dd");
            date2 = QDate::fromString(dbQuery.value(8).toString(), "yyyy-MM-dd");
            if (dbQuery.value(0).toString().isEmpty())
            {
                continue;
            }
            if (date.daysTo(date2) >= 0 && date.daysTo(date1) <= 0)
            {
                goodsPromotion3 += dbQuery.value(0).toString();
                goodsPromotion3 += "^";
                goodsPromotion3 += dbQuery.value(1).toString();
                goodsPromotion3 += "^";
                goodsPromotion3 += dbQuery.value(2).toString();
                goodsPromotion3 += "^";
                goodsPromotion3 += dbQuery.value(3).toString();
                goodsPromotion3 += "^";
                goodsPromotion3 += dbQuery.value(4).toString();
                goodsPromotion3 += "^";
                goodsPromotion3 += dbQuery.value(5).toString();
                goodsPromotion3 += "^";
                goodsPromotion3 += dbQuery.value(6).toString();
                goodsPromotion3 += ";";
            }
        }
        goodsPromotion3.chop(1);
    }
    LOG_EVT_INFO("queryGoodsPromotionForQml 执行结束!");
    db.close();
    std::vector<QString> promotionList;
    promotionList.push_back(goodsPromotion1);
    LOG_EVT_INFO("queryGoodsPromotionForQml 执行结束 goodsPromotion2:{}",goodsPromotion2.toStdString());
    promotionList.push_back(goodsPromotion2);
    promotionList.push_back(goodsPromotion3);
    LOG_EVT_INFO("queryGoodsPromotionForQml 执行结束 goodsPromotion3:{}",goodsPromotion3.toStdString());
    return promotionList;
}
void GoodsPromotionCtrl::deleteGoodsPromotion()
{
    LOG_EVT_INFO("deleteGoodsPromotion");
    QString      selectsql;
    QSqlDatabase db;
    QString      connectionName = "goodsPromotionDelete3";
    if (QSqlDatabase::contains(connectionName))
    {
        db = QSqlDatabase::database(connectionName);
    }
    else
    {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        // db.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
    }
    QString dataPath = Utils::getDataBasePath();
    db.setDatabaseName(dataPath + "buyhoo.db"); // 促销
    if (db.open() == false)
    {
        //LOG_EVT_INFO("数据库打开失败");
    }

    QSqlQuery dbQuery(db);

    selectsql = "delete from goodsPromotion1";
    dbQuery.exec(selectsql);

    selectsql = "delete from goodsPromotion2";
    dbQuery.exec(selectsql);

    db.close();
}
void GoodsPromotionCtrl::deleteGoodsPromotion2()
{
    LOG_EVT_INFO("deleteGoodsPromotion2 开始执行！");
    QDate        date = QDate::currentDate();
    QDate        date2;
    QString      selectsql;
    QSqlDatabase db;
    QSqlDatabase db2;
    QStringList  list;
    QString      connectionName = "goodsPromotionDelete";
    if (QSqlDatabase::contains(connectionName))
    {
        db = QSqlDatabase::database(connectionName);
    }
    else
    {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        // db.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
    }
    QString dataPath = Utils::getDataBasePath();
    db.setDatabaseName(dataPath + "buyhoo.db"); // 促销
    if (db.open() == false)
    {
        LOG_EVT_INFO("无密码数据库打开失败");
    }

    connectionName = "goodsPromotionDelete2";
    if (QSqlDatabase::contains(connectionName))
    {
        db2 = QSqlDatabase::database(connectionName);
    }
    else
    {
        db2 = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        db2.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
    }
    dataPath = Utils::getDataBasePath();
    db2.setDatabaseName(dataPath + "buyhoo.db"); // 促销
    if (db2.open() == false)
    {
        LOG_EVT_INFO("有密码数据库打开失败");
    }

    QSqlQuery dbQuery(db);
    QSqlQuery dbQuery2(db2);

    selectsql = "select end_time from goodsPromotion1";
    dbQuery.exec(selectsql);
    while (dbQuery.next())
    {
        list.append(dbQuery.value(0).toString());
    }
    for (int i = 0; i < list.size(); i++)
    {
        if (list.at(i).split(" ").size() > 0)
        {
            date2 = QDate::fromString(list.at(i).split(" ").at(0), "yyyy-MM-dd");
            if (date.daysTo(date2) < 0)
            {
                selectsql = "delete from goodsPromotion1 where end_time ='";
                selectsql += list.at(i);
                selectsql += "'";
                dbQuery2.exec(selectsql);
                break;
            }
            else
            {
                continue;
            }
        }
    }

    selectsql = "select end_time from goodsPromotion2";
    dbQuery.exec(selectsql);
    list.clear();
    while (dbQuery.next())
    {
        list.append(dbQuery.value(0).toString());
    }
    for (int i = 0; i < list.size(); i++)
    {
        if (list.at(i).split(" ").size() > 0)
        {
            date2 = QDate::fromString(list.at(i).split(" ").at(0), "yyyy-MM-dd");
            if (date.daysTo(date2) < 0)
            {
                selectsql = "delete from goodsPromotion2 where end_time ='";
                selectsql += list.at(i);
                selectsql += "'";
                dbQuery2.exec(selectsql);
                break;
            }
            else
            {
                continue;
            }
        }
    }

    selectsql = "select end_time from goodsPromotion3";
    dbQuery.exec(selectsql);
    list.clear();
    while (dbQuery.next())
    {
        list.append(dbQuery.value(0).toString());
    }
    for (int i = 0; i < list.size(); i++)
    {
        if (list.at(i).split(" ").size() > 0)
        {
            date2 = QDate::fromString(list.at(i).split(" ").at(0), "yyyy-MM-dd");
            if (date.daysTo(date2) < 0)
            {
                selectsql = "delete from goodsPromotion3 where end_time ='";
                selectsql += list.at(i);
                selectsql += "'";
                dbQuery2.exec(selectsql);
                break;
            }
            else
            {
                continue;
            }
        }
    }
    LOG_EVT_INFO("deleteGoodsPromotion2 执行结束！");
    db.close();
    db2.close();
}
void GoodsPromotionCtrl::deleteGoodsPromotion3()
{
    LOG_EVT_INFO("deleteGoodsPromotion3");
    QString      selectsql;
    QSqlDatabase db;
    QString      connectionName = "goodsPromotionDelete3";
    if (QSqlDatabase::contains(connectionName))
    {
        db = QSqlDatabase::database(connectionName);
    }
    else
    {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        // db.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
    }
    QString dataPath = Utils::getDataBasePath();
    db.setDatabaseName(dataPath + "buyhoo.db"); // 促销
    if (db.open() == false)
    {
        //LOG_EVT_INFO("数据库打开失败");
    }

    QSqlQuery dbQuery(db);

    selectsql = "delete from goodsPromotion3";
    dbQuery.exec(selectsql);
    db.close();
}
void GoodsPromotionCtrl::queryGoodsPromotionRequest()
{
    auto shop_control = ControlManager::getInstance()->getShopControl();
    LOG_EVT_INFO("查询商品优惠满赠信息接口2");
    QString url = req_host_prefix + "harricane/payOnline/queryAllGoodsPromotion.do";
    bodyMap body_map;
    body_map["shopUnique"]  = QString::number(shop_control->getShopUnique());
    auto cur_thread_p = new QThread(this); // 工作线程
    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);
    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "查询商品优惠满赠信息解析出错");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
                    auto status = getDataFromJson(json_doc, "status").toInt();
                    if (status == 1)
                    {
                        QString         goodsPromotion1 = "";
                        QString         goodsPromotion2 = "";
                        int        nSize = json_doc["data"].size();
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的data长度为:{}", nSize);
                        if(json_doc.contains("data")){
                            deleteGoodsPromotion();
                        }
                        if (json_doc.contains("data") && nSize > 0)
                        {
                            for (int i = 0; i < nSize; ++i){
                                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的iiii==为:{}", i);
                                Json Datas = json_doc["data"][i];

                                if(Datas.contains("goods_id")){
                                    QString goods_id = QString::number(getDataFromJson(Datas, "goods_id").toDouble(), 'f', 0);
                                    goodsPromotion1 += goods_id;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("meet_count1")){
                                    QString meet_count1 = QString::number(getDataFromJson(Datas, "meet_count1").toDouble(), 'f', 0);
                                    goodsPromotion1 += meet_count1;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("meet_count2")){
                                    QString meet_count2 = QString::number(getDataFromJson(Datas, "meet_count2").toDouble(), 'f', 0);
                                    goodsPromotion1 += meet_count2;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("meet_count3")){
                                    QString meet_count3 = QString::number(getDataFromJson(Datas, "meet_count3").toDouble(), 'f', 0);
                                    goodsPromotion1 += meet_count3;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("discount_percent1")){
                                    QString discount_percent1 = QString::number(getDataFromJson(Datas, "discount_percent1").toDouble(), 'f', 2);
                                    goodsPromotion1 += discount_percent1;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("discount_percent2")){
                                    QString discount_percent2 = QString::number(getDataFromJson(Datas, "discount_percent2").toDouble(), 'f', 2);
                                    goodsPromotion1 += discount_percent2;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("discount_percent3")){
                                    QString discount_percent3 = QString::number(getDataFromJson(Datas, "discount_percent3").toDouble(), 'f', 2);
                                    goodsPromotion1 += discount_percent3;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("order_activity")){
                                    QString order_activity = QString::number(getDataFromJson(Datas, "order_activity").toDouble(), 'f', 0);
                                    goodsPromotion1 += order_activity;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("limit_count")){
                                    QString limit_count = QString::number(getDataFromJson(Datas, "limit_count").toDouble(), 'f', 0);
                                    goodsPromotion1 += limit_count;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("start_time")){
                                    QString start_time = getDataFromJson(Datas, "start_time").toString();
                                    goodsPromotion1 += start_time;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("end_time")){
                                    QString end_time =getDataFromJson(Datas, "end_time").toString();
                                    goodsPromotion1 += end_time;
                                    goodsPromotion1 += ";";
                                }
                                else{
                                    goodsPromotion1 += ";";
                                }
                            }
                        }
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "goodsPromotion1为:{}", goodsPromotion1.toStdString());
                        int        nSize1 = json_doc["goodsData"].size();
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的goodsData长度为:{}", nSize1);
                        if (json_doc.contains("goodsData") && nSize1 > 0)
                        {
                            for (int i = 0; i < nSize1; ++i){
                                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的iiii==为:{}", i);
                                Json GoodsDatas = json_doc["goodsData"][i];
                                if(GoodsDatas.contains("meet_count1"))
                                {
                                    QString meet_count1 = QString::number(getDataFromJson(GoodsDatas, "meet_count1").toDouble(), 'f', 0);
                                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的meet_count1为:{}", meet_count1.toStdString());
                                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "goodsPromotion2为:{}", goodsPromotion2.toStdString());
                                    goodsPromotion2 += meet_count1;
                                    goodsPromotion2 += "^";
                                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的meet_count1==为:{}", meet_count1.toStdString());
                                }
                                else{
                                    goodsPromotion2 += "^";
                                }
                                if(GoodsDatas.contains("meet_count2"))
                                {
                                    QString meet_count2 = QString::number(getDataFromJson(GoodsDatas, "meet_count2").toDouble(), 'f', 0);
                                    goodsPromotion2 += meet_count2;
                                    goodsPromotion2 += "^";
                                }
                                else{
                                    goodsPromotion2 += "^";
                                }
                                if(GoodsDatas.contains("meet_count3"))
                                {
                                    QString meet_count3 = QString::number(getDataFromJson(GoodsDatas, "meet_count3").toDouble(), 'f', 0);
                                    goodsPromotion2 += meet_count3;
                                    goodsPromotion2 += "^";
                                }
                                else{
                                    goodsPromotion2 += "^";
                                }
                                if(GoodsDatas.contains("gift_count1"))
                                {
                                    QString gift_count1 = QString::number(getDataFromJson(GoodsDatas, "gift_count1").toDouble(), 'f', 0);
                                    goodsPromotion2 += gift_count1;
                                    goodsPromotion2 += "^";
                                }
                                else{
                                    goodsPromotion2 += "^";
                                }
                                if(GoodsDatas.contains("gift_count2"))
                                {
                                    QString gift_count2 = QString::number(getDataFromJson(GoodsDatas, "gift_count2").toDouble(), 'f', 0);
                                    goodsPromotion2 += gift_count2;
                                    goodsPromotion2 += "^";
                                }
                                else{
                                    goodsPromotion2 += "^";
                                }
                                if(GoodsDatas.contains("gift_count3"))
                                {
                                    QString gift_count3 = QString::number(getDataFromJson(GoodsDatas, "gift_count3").toDouble(), 'f', 0);
                                    goodsPromotion2 += gift_count3;
                                    goodsPromotion2 += "^";
                                }
                                else{
                                    goodsPromotion2 += "^";
                                }
                                if(GoodsDatas.contains("goods_id"))
                                {
                                    QString goods_id = QString::number(getDataFromJson(GoodsDatas, "goods_id").toDouble(), 'f', 0);
                                    goodsPromotion2 += goods_id;
                                    goodsPromotion2 += "^";
                                }
                                else{
                                    goodsPromotion2 += "^";
                                }
                                if(GoodsDatas.contains("goods_id_gift"))
                                {
                                    QString goods_id_gift = QString::number(getDataFromJson(GoodsDatas, "goods_id_gift").toDouble(), 'f', 0);
                                    goodsPromotion2 += goods_id_gift;
                                    goodsPromotion2 += "^";
                                }
                                else{
                                    goodsPromotion2 += "^";
                                }
                                if(GoodsDatas.contains("start_time"))
                                {
                                    QString start_time = getDataFromJson(GoodsDatas, "start_time").toString();
                                    goodsPromotion2 += start_time;
                                    goodsPromotion2 += "^";
                                }
                                else{
                                    goodsPromotion2 += "^";
                                }
                                if(GoodsDatas.contains("end_time"))
                                {
                                    QString end_time = getDataFromJson(GoodsDatas, "end_time").toString();
                                    goodsPromotion2 += end_time;
                                    goodsPromotion2 += ";";
                                }
                                else{
                                    goodsPromotion2 += ";";
                                }
                            }
                        }
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的goodsPromotion2.size()为:{}", goodsPromotion2.size());
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的goodsPromotion1.size()为:{}", goodsPromotion1.size());
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的goodsPromotion1为:{}", goodsPromotion1.toStdString());
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的goodsPromotion2为:{}", goodsPromotion2.toStdString());
                        if (goodsPromotion1.size() > 0)
                        {
                            goodsPromotion1.chop(1);
                            queryGoodsPromotionSql(goodsPromotion1);
                        }
                        if (goodsPromotion2.size() > 0)
                        {
                            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "goodsPromotion2==为:{}", goodsPromotion2.toStdString());
                            goodsPromotion2.chop(1);
                            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "goodsPromotion2===为:{}", goodsPromotion2.toStdString());
                            queryGoodsPromotionSql2(goodsPromotion2);
                        }
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "goodsPromotion2为:{}", goodsPromotion2.toStdString());
                    }
                    else
                    {
                        deleteGoodsPromotion();
                    }
                }
                if (!is_succ)
                {
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void  GoodsPromotionCtrl::pcSaleGoodsSearchForQml(QString goodsBarcode)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();
    LOG_EVT_INFO("商品捆绑查询接口");
    QString url = req_host_prefix + "shopmanager/app/shop/pcSaleGoodsSearch.do";
    bodyMap body_map;
    body_map["shopUnique"]  = QString::number(shop_control->getShopUnique());
    body_map["goodsBarcode"]  = goodsBarcode;
    auto cur_thread_p = new QThread(this); // 工作线程
    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);
    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QString              goodsStr         = "";
                QString              bindingUniqueStr = "";
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "查询商品捆绑信息解析出错");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
                    auto status = getDataFromJson(json_doc, "status").toInt();
//                    {"address":null,"cus_data":null,
//                    "data":[{"bindingTotal":5.0,"bindingUnique":1720151943806,"listDetail":[{"goodsBarcode":"11556","goodsCount":1.0,"goodsDiscount":1.0,"goodsId":1543234000,"goodsInPrice":1.0,"goodsName":"馒头1234","goodsPicPath":"/image/1536215939565/1155687.png","goodsSalePrice":0.09803921568627451},{"goodsBarcode":"2100178","goodsCount":1.0,"goodsDiscount":1.0,"goodsId":1543302906,"goodsInPrice":1.0,"goodsName":"苹果","goodsPicPath":"","goodsSalePrice":4.901960784313726}]},
//                    {"bindingTotal":0.02,"bindingUnique":111,"listDetail":[{"goodsBarcode":"11556","goodsCount":1.0,"goodsDiscount":1.0,"goodsId":1543234000,"goodsInPrice":1.0,"goodsName":"馒头1234","goodsPicPath":"/image/1536215939565/1155687.png","goodsSalePrice":0.02}]}],
//                    "data1":null,"msg":"1","status":0}
                    if (status == 0)
                    {
                        int        nSize = json_doc["data"].size();
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的data长度为:{}", nSize);
                        if (json_doc.contains("data") && nSize > 0)
                        {

                            for (auto &cur_it : json_doc["data"])
                            {

                                if(getDataFromJson(cur_it, "bindingUnique") == "" || getDataFromJson(cur_it, "bindingUnique") == "0"){

                                    emit sendBindingStr(goodsStr, bindingUniqueStr);
                                    continue;
                                }
                                for (auto &ListDetail : cur_it["listDetail"]){

                                        if(getDataFromJson(ListDetail, "goodsId") == ""){
                                            continue;
                                        }

                                        if(cur_it.contains("bindingUnique")){
                                            goodsStr += getDataFromJson(cur_it, "bindingUnique").toString();
                                            goodsStr += "^";
                                        }
                                        if(ListDetail.contains("goodsId")){
                                            goodsStr += getDataFromJson(ListDetail, "goods_id").toString();
                                            goodsStr += "^";
                                        }
                                        if(ListDetail.contains("goodsName")){
                                            goodsStr += getDataFromJson(ListDetail, "goodsName").toString();
                                            goodsStr += "^";
                                        }
                                        if(ListDetail.contains("goodsBarcode")){
                                            goodsStr += getDataFromJson(ListDetail, "goodsBarcode").toString();
                                            goodsStr += "^";
                                        }
                                        if(ListDetail.contains("goodsSalePrice")){
                                            goodsStr += getDataFromJson(ListDetail, "goodsSalePrice").toString();
                                            goodsStr += "^";
                                        }
                                        if(ListDetail.contains("goodsCount")){
                                            goodsStr += getDataFromJson(ListDetail, "goodsCount").toString();
                                            goodsStr += "^";
                                        }
                                        if(ListDetail.contains("goodsDiscount")){
                                            goodsStr += getDataFromJson(ListDetail, "goodsDiscount").toString();
                                            goodsStr += "^";
                                        }
                                        if(ListDetail.contains("goodsInPrice")){
                                            goodsStr += getDataFromJson(ListDetail, "goodsInPrice").toString();
                                            goodsStr += ";";
                                        }
                                    if (goodsStr.size() > 0)
                                    {
                                        goodsStr.chop(1);
                                    }
                                    goodsStr += "#";
                                }
                                bindingUniqueStr += getDataFromJson(cur_it, "bindingUnique").toString();
                                bindingUniqueStr += "^";
                            }
                            if (goodsStr.size() > 0)
                            {
                                goodsStr.chop(1);
                            }
                            if (bindingUniqueStr.size() > 0)
                            {
                                bindingUniqueStr.chop(1);
                            }
                            LOG_EVT_INFO("goodsStr:{};bindingUniqueStr:{}",goodsStr.toStdString(),bindingUniqueStr.toStdString());
                            emit sendBindingStr(goodsStr, bindingUniqueStr);
                            LOG_EVT_INFO("emit");
                        }
                    }
                    else{
                        LOG_EVT_INFO("ERROR:服务器发来信息出错");
                        emit sendBindingStr(goodsStr, bindingUniqueStr);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void GoodsPromotionCtrl::queryPromotionGoodsSingle()
{
    auto shop_control = ControlManager::getInstance()->getShopControl();
    LOG_EVT_INFO("queryPromotionGoodsSingle开始执行");
    QString url = req_host_prefix + "harricane/payOnline/queryPromotionGoodsSingle.do";
    bodyMap body_map;
    body_map["shopUnique"]  = QString::number(shop_control->getShopUnique());
    auto cur_thread_p = new QThread(this); // 工作线程
    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);
    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "查询商品优惠满赠信息解析出错");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
                    auto status = getDataFromJson(json_doc, "status").toInt();
                    if (status == 1)
                    {
                        QString         goodsPromotion1 = "";
                        int        nSize = json_doc["data"].size();
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的data长度为:{}", nSize);
                        if(json_doc.contains("data")){
                            deleteGoodsPromotion3();
                        }
                        if (json_doc.contains("data") && nSize > 0)
                        {
                            for (int i = 0; i < nSize; ++i){
                                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的iiii==为:{}", i);
                                Json Datas = json_doc["data"][i];

                                if(Datas.contains("goods_id")){
                                    QString goods_id = QString::number(getDataFromJson(Datas, "goods_id").toDouble(), 'f', 0);
                                    goodsPromotion1 += goods_id;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("number1")){
                                    QString number1 = QString::number(getDataFromJson(Datas, "number1").toDouble(), 'f', 0);
                                    goodsPromotion1 += number1;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("number2")){
                                    QString number2 = QString::number(getDataFromJson(Datas, "number2").toDouble(), 'f', 0);
                                    goodsPromotion1 += number2;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("number3")){
                                    QString number3 = QString::number(getDataFromJson(Datas, "number3").toDouble(), 'f', 0);
                                    goodsPromotion1 += number3;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("discount_percent1")){
                                    QString discount_percent1 = QString::number(getDataFromJson(Datas, "discount_percent1").toDouble(), 'f', 0);
                                    goodsPromotion1 += discount_percent1;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("discount_percent2")){
                                    QString discount_percent2 = QString::number(getDataFromJson(Datas, "discount_percent2").toDouble(), 'f', 0);
                                    goodsPromotion1 += discount_percent2;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("discount_percent3")){
                                    QString discount_percent3 = QString::number(getDataFromJson(Datas, "discount_percent3").toDouble(), 'f', 0);
                                    goodsPromotion1 += discount_percent3;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("start_time")){
                                    QString start_time = QString::number(getDataFromJson(Datas, "start_time").toDouble(), 'f', 0);
                                    goodsPromotion1 += start_time;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("end_time")){
                                    QString end_time = QString::number(getDataFromJson(Datas, "end_time").toDouble(), 'f', 0);
                                    goodsPromotion1 += end_time;
                                    goodsPromotion1 += ";";
                                }
                                else{
                                    goodsPromotion1 += ";";
                                }
                            }
                        }
                        if (goodsPromotion1.size() > 0)
                        {
                            goodsPromotion1.chop(1);
                            queryGoodsPromotionSql3(goodsPromotion1);
                            LOG_EVT_INFO("queryPromotionGoodsSingle成功存入!");
                        }
                    }
                }
                if (!is_succ)
                {
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void GoodsPromotionCtrl::queryGoodsPromotionSql(QString str)
{
    LOG_EVT_INFO("向表goodsPromotion1插入查询数据");
    QSqlDatabase db;
    QString      connectionName = "goodsPromotion1";
    if (QSqlDatabase::contains(connectionName))
    {
        db = QSqlDatabase::database(connectionName);
    }
    else
    {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        // db.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
    }
    QString dataPath = Utils::getDataBasePath();
    db.setDatabaseName(dataPath + "buyhoo.db"); // 促销
    if (db.open() == false)
    {
        LOG_EVT_INFO("buyhoo.db 数据库打开失败!");
    }
    QSqlQuery   dbQuery(db);
    QStringList list = db.tables();

    if (!list.contains("goodsPromotion1"))
    {
        QString sqlCreatTable = "create table goodsPromotion1 (goods_id integer,"
                                "meet_count1 varchar, "
                                "meet_count2 varchar,"
                                "meet_count3 varchar,"
                                "discount_percent1 varchar,"
                                "discount_percent2 varchar,"
                                "discount_percent3 varchar,"
                                "order_activity varchar,"
                                "limit_count varchar,"
                                "start_time varchar,"
                                "end_time varchar)";
        if (!dbQuery.exec(sqlCreatTable))
        {
            LOG_EVT_INFO("表goodsPromotion1创建失败");
        }
        else
        {
            QString     selectSql;
            QStringList list     = str.split(";");
            int         listSize = list.size();
            db.transaction();
            for (int i = 0; i < listSize; i++)
            {
                selectSql = "insert into goodsPromotion1 (goods_id,";
                selectSql += "meet_count1,";
                selectSql += "meet_count2,";
                selectSql += "meet_count3,";
                selectSql += "discount_percent1,";
                selectSql += "discount_percent2,";
                selectSql += "discount_percent3,";
                selectSql += "order_activity,";
                selectSql += "limit_count,";
                selectSql += "start_time,";
                selectSql += "end_time) values ('";
                selectSql += list.at(i).split("^").at(0);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(1);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(2);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(3);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(4);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(5);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(6);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(7);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(8);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(9);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(10);
                selectSql += "')";
                if (!dbQuery.exec(selectSql))
                {
                    LOG_EVT_INFO("goodsPromotion1插入失败");
                    db.close();
                    return;
                }
            }
            db.commit();
        }
    }
    else
    {
        QString     selectSql;
        QStringList list     = str.split(";");
        int         listSize = list.size();
        db.transaction();
        for (int i = 0; i < listSize; i++)
        {
            selectSql = "insert into goodsPromotion1 (goods_id,";
            selectSql += "meet_count1,";
            selectSql += "meet_count2,";
            selectSql += "meet_count3,";
            selectSql += "discount_percent1,";
            selectSql += "discount_percent2,";
            selectSql += "discount_percent3,";
            selectSql += "order_activity,";
            selectSql += "limit_count,";
            selectSql += "start_time,";
            selectSql += "end_time) values ('";
            selectSql += list.at(i).split("^").at(0);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(1);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(2);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(3);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(4);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(5);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(6);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(7);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(8);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(9);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(10);
            selectSql += "')";
            LOG_EVT_INFO("goodsPromotion1插入命令为:{}",selectSql.toStdString());
            if (!dbQuery.exec(selectSql))
            {
                LOG_EVT_INFO("goodsPromotion1插入失败");
                db.close();
                return;
            }
        }
        db.commit();
    }
    db.close();
}
void GoodsPromotionCtrl::queryGoodsPromotionSql2(QString str)
{
    LOG_EVT_INFO("向表goodsPromotion2存入数据");
    QSqlDatabase db;
    QString      connectionName = "goodsPromotion2";
    if (QSqlDatabase::contains(connectionName))
    {
        db = QSqlDatabase::database(connectionName);
    }
    else
    {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        // db.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
    }
    QString dataPath = Utils::getDataBasePath();
    db.setDatabaseName(dataPath + "buyhoo.db"); // 促销
    if (db.open() == false)
    {
        LOG_EVT_INFO("数据库打开失败");
    }
    QSqlQuery   dbQuery(db);
    QStringList list = db.tables();

    if (!list.contains("goodsPromotion2"))
    {
        QString sqlCreatTable = "create table goodsPromotion2 (goods_id integer,"
                                "meet_count1 varchar, "
                                "meet_count2 varchar,"
                                "meet_count3 varchar,"
                                "gift_count1 varchar,"
                                "gift_count2 varchar,"
                                "gift_count3 varchar,"
                                "goods_id_gift varchar,"
                                "start_time varchar,"
                                "end_time varchar)";
        if (!dbQuery.exec(sqlCreatTable))
        {
            //LOG_EVT_INFO("goodsPromotion2表创建失败");
        }
        else
        {
            QString     selectSql;
            QStringList list     = str.split(";");
            int         listSize = list.size();
            db.transaction();
            for (int i = 0; i < listSize; i++)
            {
                selectSql = "insert into goodsPromotion2 (goods_id,";
                selectSql += "meet_count1,";
                selectSql += "meet_count2,";
                selectSql += "meet_count3,";
                selectSql += "gift_count1,";
                selectSql += "gift_count2,";
                selectSql += "gift_count3,";
                selectSql += "goods_id_gift,";
                selectSql += "start_time,";
                selectSql += "end_time) values ('";
                selectSql += list.at(i).split("^").at(6);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(0);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(1);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(2);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(3);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(4);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(5);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(7);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(8);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(9);
                selectSql += "')";
                if (!dbQuery.exec(selectSql))
                {
                    //LOG_EVT_INFO("goodsPromotion2插入失败");
                    return;
                }
            }
            db.commit();
        }
    }
    else
    {
        QString     selectSql;
        QStringList list     = str.split(";");
        int         listSize = list.size();
        db.transaction();
        for (int i = 0; i < listSize; i++)
        {
            selectSql = "insert into goodsPromotion2 (goods_id,";
            selectSql += "meet_count1,";
            selectSql += "meet_count2,";
            selectSql += "meet_count3,";
            selectSql += "gift_count1,";
            selectSql += "gift_count2,";
            selectSql += "gift_count3,";
            selectSql += "goods_id_gift,";
            selectSql += "start_time,";
            selectSql += "end_time) values ('";
            selectSql += list.at(i).split("^").at(6);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(0);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(1);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(2);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(3);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(4);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(5);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(7);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(8);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(9);
            selectSql += "')";
            LOG_EVT_INFO("表goodsPromotion2存入SQL命令为:{}",selectSql.toStdString());
            if (!dbQuery.exec(selectSql))
            {
                LOG_EVT_INFO("goodsPromotion2存入数据失败");
                return;
            }
            else{
                LOG_EVT_INFO("goodsPromotion2存入数据成功");
            }
        }
        db.commit();
    }
    db.close();
}
void GoodsPromotionCtrl::queryGoodsPromotionSql3(QString str)
{
    LOG_EVT_INFO("goodsPromotion3开始存入数据：{}",str.toStdString());
    QSqlDatabase db;
    QString      connectionName = "goodsPromotion3";
    if (QSqlDatabase::contains(connectionName))
    {
        db = QSqlDatabase::database(connectionName);
    }
    else
    {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        // db.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
    }
    QString dataPath = Utils::getDataBasePath();
    db.setDatabaseName(dataPath + "buyhoo.db"); // 促销
    if (db.open() == false)
    {
        //LOG_EVT_INFO("数据库打开失败");
    }
    QSqlQuery   dbQuery(db);
    QStringList list = db.tables();

    if (!list.contains("goodsPromotion3"))
    {
        QString sqlCreatTable = "create table goodsPromotion3 (goods_id integer,"
                                "meet_count1 varchar, "
                                "meet_count2 varchar,"
                                "meet_count3 varchar,"
                                "discount_percent1 varchar,"
                                "discount_percent2 varchar,"
                                "discount_percent3 varchar,"
                                "start_time varchar,"
                                "end_time varchar)";
        if (!dbQuery.exec(sqlCreatTable))
        {
            ////LOG_EVT_INFO("表goodsPromotion3创建失败，原因:{}",dbQuery.lastError().text());
        }
        else
        {
            QString     selectSql;
            QStringList list     = str.split(";");
            int         listSize = list.size();
            db.transaction();
            for (int i = 0; i < listSize; i++)
            {
                selectSql = "insert into goodsPromotion3 (goods_id,";
                selectSql += "meet_count1,";
                selectSql += "meet_count2,";
                selectSql += "meet_count3,";
                selectSql += "discount_percent1,";
                selectSql += "discount_percent2,";
                selectSql += "discount_percent3,";
                selectSql += "start_time,";
                selectSql += "end_time) values ('";
                selectSql += list.at(i).split("^").at(0);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(1);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(2);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(3);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(4);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(5);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(6);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(7);
                selectSql += "','";
                selectSql += list.at(i).split("^").at(8);
                selectSql += "')";
                if (!dbQuery.exec(selectSql))
                {
                    LOG_EVT_INFO("表goodsPromotion3存入失败!");
                    db.close();
                    return;
                }
            }
            db.commit();
        }
    }
    else
    {
        QString     selectSql;
        QStringList list     = str.split(";");
        int         listSize = list.size();
        db.transaction();
        for (int i = 0; i < listSize; i++)
        {
            selectSql = "insert into goodsPromotion3 (goods_id,";
            selectSql += "meet_count1,";
            selectSql += "meet_count2,";
            selectSql += "meet_count3,";
            selectSql += "discount_percent1,";
            selectSql += "discount_percent2,";
            selectSql += "discount_percent3,";
            selectSql += "start_time,";
            selectSql += "end_time) values ('";
            selectSql += list.at(i).split("^").at(0);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(1);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(2);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(3);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(4);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(5);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(6);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(7);
            selectSql += "','";
            selectSql += list.at(i).split("^").at(8);
            selectSql += "')";
            LOG_EVT_INFO("表goodsPromotion3存入SQL命令为:{}",selectSql.toStdString());
            if (!dbQuery.exec(selectSql))
            {
                LOG_EVT_INFO("表goodsPromotion3存入数据失败");
                db.close();
                return;
            }else{
                LOG_EVT_INFO("表goodsPromotion3存入数据成功");
            }
        }
        db.commit();
    }
    db.close();
}
void GoodsPromotionCtrl::queryOrderPromotionForQml()
{
    auto shop_control = ControlManager::getInstance()->getShopControl();
    LOG_EVT_INFO("queryOrderPromotionForQml开始执行");
    QString url = req_host_prefix + "harricane/payOnline/queryOrderPromotion.do";
    bodyMap body_map;
    body_map["shopUnique"]  = QString::number(shop_control->getShopUnique());
    auto cur_thread_p = new QThread(this); // 工作线程
    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);
    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "查询商品优惠满赠信息解析出错");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
                    auto status = getDataFromJson(json_doc, "status").toInt();
                    if (status == 1)
                    {
                        QString         goodsPromotion1 = "";
                        int        nSize = json_doc["data"].size();
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "商品优惠信息解析出的data长度为:{}", nSize);
                        if (nSize == 0)
                        {
                            ConfigTool::getInstance()->setSetting(ConfigEnum::ORDER_PROMOTION_INFO,"");

                        }
                        if (json_doc.contains("data") && nSize > 0)
                        {
                            for (int i = 0; i < nSize; ++i){
                                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "解析出的iiii==为:{}", i);
                                Json Datas = json_doc["data"][i];

                                if(Datas.contains("meet_price1")){
                                    QString meet_price1 = QString::number(getDataFromJson(Datas, "meet_price1").toDouble(), 'f', 0);
                                    goodsPromotion1 += meet_price1;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("meet_price2")){
                                    QString meet_price2 = QString::number(getDataFromJson(Datas, "meet_price2").toDouble(), 'f', 0);
                                    goodsPromotion1 += meet_price2;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("meet_price3")){
                                    QString meet_price3 = QString::number(getDataFromJson(Datas, "meet_price3").toDouble(), 'f', 0);
                                    goodsPromotion1 += meet_price3;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("discount_price1")){
                                    QString discount_price1 = QString::number(getDataFromJson(Datas, "discount_price1").toDouble(), 'f', 2);
                                    goodsPromotion1 += discount_price1;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("discount_price2")){
                                    QString discount_price2 = QString::number(getDataFromJson(Datas, "discount_price2").toDouble(), 'f', 2);
                                    goodsPromotion1 += discount_price2;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("discount_price3")){
                                    QString discount_price3 = QString::number(getDataFromJson(Datas, "discount_price3").toDouble(), 'f', 2);
                                    goodsPromotion1 += discount_price3;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("goods_id1")){
                                    QString goods_id1 = QString::number(getDataFromJson(Datas, "goods_id1").toDouble(), 'f', 0);
                                    goodsPromotion1 += goods_id1;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("goods_id2")){
                                    QString goods_id2 = QString::number(getDataFromJson(Datas, "goods_id2").toDouble(), 'f', 0);
                                    goodsPromotion1 += goods_id2;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("goods_id3")){
                                    QString goods_id3 = QString::number(getDataFromJson(Datas, "goods_id3").toDouble(), 'f', 0);
                                    goodsPromotion1 += goods_id3;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("gift_count1")){
                                    QString gift_count1 = QString::number(getDataFromJson(Datas, "gift_count1").toDouble(), 'f', 0);
                                    goodsPromotion1 += gift_count1;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("gift_count2")){
                                    QString gift_count2 = QString::number(getDataFromJson(Datas, "gift_count2").toDouble(), 'f', 0);
                                    goodsPromotion1 += gift_count2;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("gift_count3")){
                                    QString gift_count3 = QString::number(getDataFromJson(Datas, "gift_count3").toDouble(), 'f', 0);
                                    goodsPromotion1 += gift_count3;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("start_time")){
                                    QString start_time = getDataFromJson(Datas, "start_time").toString();
                                    goodsPromotion1 += start_time;
                                    goodsPromotion1 += "^";
                                }
                                else{
                                    goodsPromotion1 += "^";
                                }
                                if(Datas.contains("end_time")){
                                    QString end_time = getDataFromJson(Datas, "end_time").toString();
                                    goodsPromotion1 += end_time;
                                    goodsPromotion1 += "#";
                                }
                                else{
                                    goodsPromotion1 += "#";
                                }
                            }
                        }
                        if (!goodsPromotion1.isEmpty())
                        {
                            goodsPromotion1.chop(1);
                            ConfigTool::getInstance()->setSetting(ConfigEnum::ORDER_PROMOTION_INFO, goodsPromotion1);
                            LOG_EVT_INFO("配置文件促销配置参数更新成功!");
                        }
                    }
                    else
                    {
                        ConfigTool::getInstance()->setSetting(ConfigEnum::ORDER_PROMOTION_INFO, QString::fromStdString(""));

                    }
                }
                if (!is_succ)
                {
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
