﻿#include "NetStatusCtrl.h"
#include <QDnsLookup>
#include <QEventLoop>
#include <QHostInfo>
#include "WorkerModule/NetStatusWorker.h"
#include "LogModule/LogManager.h"
#include "NetModule/NetGlobal.h"

NetStatusCtrl::NetStatusCtrl(QObject *parent) : QObject{parent}
{
    network_thread = new NetStatusWorker();
    connect(network_thread, &NetStatusWorker::signalsNetwork_States, this,
            [&](bool state)
            {
                setNetStatus(state ? (int)NetworkStatus::NETWORK_STATUS__CONNECTED : (int)NetworkStatus::NETWORK_STATUS__DISCONNECTED);
                if(state){
                    net_status_global = "101";
                    //LOG_EVT_INFO("网络正常！ 更新网络状态为：{}",net_status_global.toStdString());
                }else{
                    net_status_global = "102";
                    //LOG_EVT_INFO("网络断开！ 更新网络状态为：{}",net_status_global.toStdString());
                }

            });
    network_thread->start(); //开启网络检测线程
    LOG_EVT_INFO("==完成网络检测线程开启==");
}

NetStatusCtrl::~NetStatusCtrl()
{
    network_thread->stop();
    // delete network_thread;
}

int NetStatusCtrl::netStatus()
{
    return net_status_;
}

void NetStatusCtrl::setNetStatus(int status)
{
    net_status_ = status;
    emit netStatusChanged();
}
