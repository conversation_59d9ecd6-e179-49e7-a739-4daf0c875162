﻿#include "PrinterStruct.h"
#include "ConfModule/ConfigTool.h"

bool PriceTag40x30::save2Cfg()
{
    Json tmp_json;
    tmp_json    = *this;
    auto result = QString::fromStdString(tmp_json.dump());
    ConfigTool::getInstance()->setSetting(ConfigEnum::PRINTER_PRICE_TAG_40x30, result);
    return true;
}

bool PriceTag95x38::save2Cfg()
{
    Json tmp_json;
    tmp_json    = *this;
    auto result = QString::fromStdString(tmp_json.dump());
    ConfigTool::getInstance()->setSetting(ConfigEnum::PRINTER_PRICE_TAG_95x38, result);
    return true;
}

bool PriceTag60x35::save2Cfg()
{
    Json tmp_json;
    tmp_json    = *this;
    auto result = QString::fromStdString(tmp_json.dump());
    ConfigTool::getInstance()->setSetting(ConfigEnum::PRINTER_PRICE_TAG_60x35, result);
    return true;
}
