﻿#ifndef MQTTCONTROL_H
#define MQTTCONTROL_H

#include <QHostAddress>
#include <QMqttClient>
#include <QObject>
#include <QTimer>
#include <QVariant>
#include "json.hpp"
#include "qmqttclient.h"
#include "qobject.h"
#include "qtimer.h"

using Json = nlohmann::json;

// const QHostAddress HOST_MQTT      = QHostAddress("**************");
const QString HOST_MQTT  = ("**************");
const quint16 PORT_MQTT  = 1883;
const QString TOPIC_MQTT = "win_qt_cash_1.0";

class MqttControl : public QMqttClient
{
    Q_OBJECT
public:
    explicit MqttControl(const QString &host, const quint16 port, QObject *parent = nullptr);
    virtual ~MqttControl();
    void onConnected();
    void onDisconnected();
    void onTimeout();
    void onSubscribed(const QString &topic);
    void onUnSubscribed(const QString &topic);
    void onReceived(const QByteArray &message, const QMqttTopicName &topic);

    void sendWillMsg();
    void brokerDisconnected();

    QString getClientId();
    QString getTopic();

    QTimer timer_reconn;

    QByteArray will_msg_;
signals:
    void sigMqttAddGoodsKind4Qml(bool is_parent_goods_kind, QVariant goods_kind_unique = "");
    void sigMqttDelGoodsKind4Qml(bool is_parent_goods_kind, QVariant goods_kind_unique = "");
    void sigMqttRefreshGoodsKind4Qml(bool is_parent_goods_kind, QVariant goods_kind_unique = "");

    void sigMqttAddGoods4Qml(QVariant goods_barcode = "");
    void sigMqttDelGoods4Qml(QVariant goods_barcode = "");
    void sigMqttRefreshGoodsByBarcode4Qml(QVariant goods_barcode = "");
    void sigMqttRefreshOrderCountByStatus4Qml();

    void sigLogUpload();
};


#endif // MQTTCONTROL_H
