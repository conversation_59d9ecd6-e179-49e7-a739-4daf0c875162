﻿#ifndef SUPPLIERCONTROL_H
#define SUPPLIERCONTROL_H

#include <QJSValue>
#include <QObject>
#include <QVariant>

#include "DataModule/DataStruct.h"

class SupplierControl : public QObject
{
    Q_OBJECT
public:
    explicit SupplierControl(QObject *parent = nullptr);

    ///-------------------------------------------| 供应商分类 |-------------------------------------------
    //

    /*!
     * \brief reqAllSupplierCategory4Qml 请求供应商分类
     * \param callback QML回调
     */
    Q_INVOKABLE void reqAllSupplierCategory4Qml(QJSValue callback);

    /*!
     * \brief reqSupplierCategory4Qml 请求供应商分类
     * \param callback QML回调
     */
    Q_INVOKABLE void reqSupplierCategory4Qml(QJSValue callback);

    /*!
     * \brief reqCreateSupplierCategory4Qml 请求创建供应商分类
     * \param callback QML回调
     */
    Q_INVOKABLE void reqCreateSupplierCategory4Qml(QJSValue callback, QVariant supplier_name);

    /*!
     * \brief reqDelSupplierCategory4Qml 请求删除供应商分类
     * \param callback QML回调
     */
    Q_INVOKABLE void reqDelSupplierCategory4Qml(QJSValue callback, QVariant category_id);

    /*!
     * \brief reqDelSupplierCategory4Qml 请求删除供应商分类
     * \param callback QML回调
     */
    Q_INVOKABLE void reqChangeSupplierCategory4Qml(QJSValue callback, QVariant categoty_id, QVariant categoty_name);

    //
    ///-------------------------------------------| 供应商分类 |-------------------------------------------

    //*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\*/*\

    ///-------------------------------------------| 供应商 |-------------------------------------------
    //
    /*!
     * \brief reqSupplier4Qml 请求供应商列表
     * \param callback QML回调
     */
    Q_INVOKABLE void reqSupplier4Qml(QJSValue callback = 0);
    /*!
     * \brief reqSupplier4Qml 添加供应商
     * \param callback QML回调
     */
    Q_INVOKABLE void addSupplier4Qml(QJSValue callback,QVariant supplierName);

    Q_INVOKABLE QString getSupplierJson();

    /*!
     * \brief reqCreateSupplier4Qml 请求创建供应商
     * \param callback QML回调
     */
    Q_INVOKABLE void reqCreateSupplier4Qml(QJSValue callback, QVariant json_data);

    /*!
     * \brief reqDelSupplier4Qml 请求删除供应商
     * \param callback QML回调
     */
    Q_INVOKABLE void reqDelSupplier4Qml(QJSValue callback, QVariant supplier_id);

    /*!
     * \brief reqChangeSupplier4Qml 请求更改供应商
     * \param callback QML回调
     */
    Q_INVOKABLE void reqChangeSupplier4Qml(QJSValue callback, QVariant json_data);
    //
    ///-------------------------------------------| 供应商 |-------------------------------------------

    std::vector<SupplierInfo> supplier_info_vec_;

signals:
};

#endif // SUPPLIERCONTROL_H
