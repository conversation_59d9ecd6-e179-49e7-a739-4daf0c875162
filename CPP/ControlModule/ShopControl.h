﻿#ifndef SHOPCONTROL_H
#define SHOPCONTROL_H

#include <QJSEngine>
#include <QJSValue>
#include <QThread>

#include <QObject>
struct PersonInfo
{
    QString            name;
    QString            phone;
    QString            novawholesales;
    unsigned long long cashier_id;
};

class ShopControl : public QObject
{
    Q_OBJECT

    Q_PROPERTY(int shopBeans READ shopBeans WRITE setShopBeans NOTIFY shopBeansChanged FINAL)

    Q_PROPERTY(bool isLogin READ isLogin WRITE setIsLogin NOTIFY isLoginChanged FINAL)
    Q_PROPERTY(int goodsInPriceType READ getGoodsInPriceType NOTIFY goodsInPriceTypeChanged)

public:
    explicit ShopControl(QObject *parent = nullptr);

    Q_INVOKABLE void login_v2(QJSValue callback, QString id, QString passwd);
    std::mutex       login_mutex;

    /*!
     * \brief loginWizard 登陆成功后请调用!
     */
    void loginWizard();

    /*!
     * \brief handOver 获取交班信息
     * \param callback qml回调
     */
    Q_INVOKABLE void reqHandOverInfo4Qml(QJSValue callback);

    Q_INVOKABLE void reqHandOver4Qml(QJSValue callback);

    PersonInfo getPersonInfo();

    const QString getShopName(bool is_from_cfg = true);

    Q_INVOKABLE unsigned long long getShopUnique();

    const QString getShopImagePath();
    Q_INVOKABLE  QString getShopType();

    const QString getShopAddress(bool is_from_cfg = true);

    Q_INVOKABLE QString getUserName();

    Q_INVOKABLE QString getUserPhone(bool is_from_cfg = true);

    Q_INVOKABLE unsigned long long getUserId();
    Q_INVOKABLE unsigned long long getLoginId();

    Q_INVOKABLE void reqRechargeConfig(QJSValue callback);

    Q_INVOKABLE unsigned long long getGoodsInPriceType();
    //获取店铺百货豆
    Q_INVOKABLE void reqShopBeans();
    //获取是否是海外商城系统
    Q_INVOKABLE QString getNovawholesales();
    QString getLoginAccount();

    int shopBeans();
    int goodsInPriceType();
    void setShopBeans(int beans);

    bool isLogin();

    void setIsLogin(bool is_login);

private:
    QString shop_name_;

    unsigned long long login_id_;      // 登录ID
    QString            login_account_; // 登录账号
    unsigned long long shop_unique_;
    unsigned long long goods_in_price_type_; //库存管理方式：0-最近入库价，1-移动加权平均 2-先进先出
    QString            shop_image_path_;
    QString            shop_address_detail_;
    PersonInfo         person_info_;
    QString            shop_type_;//店铺类型

    int shop_beans_ = .0;

    bool is_login_ = false;

signals:
    void loggedIn();

    void shopBeansChanged();

    void isLoginChanged();
    void goodsInPriceTypeChanged();
    void sigCheckUpdate(); //检查更新
};

#endif // SHOPCONTROL_H
