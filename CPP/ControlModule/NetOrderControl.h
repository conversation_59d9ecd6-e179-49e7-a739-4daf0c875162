﻿#ifndef NETORDERCONTROL_H
#define NETORDERCONTROL_H

#include <QDateTime>
#include <QJSEngine>
#include <QJSValue>
#include <QObject>
#include <QVariant>

class NetOrderControl : public QObject
{
    Q_OBJECT
public:
    explicit NetOrderControl(QObject *parent = nullptr);

    /*!
     * \brief getStoreOrdersRecord4Qml 获取订单列表
     * \param callback QML回调
     * \param page_net_order_enum 订单状态
     * \param begin_time 开始时间 yyyy-MM-dd
     * \param end_time 结束时间 yyyy-MM-dd
     */
    Q_INVOKABLE void getStoreOrdersRecord4Qml(QJSValue callback, int page_net_order_enum,
                                              QString begin_time = QDateTime::currentDateTime().toString("yyyy-MM-dd"),
                                              QString end_time   = QDateTime::currentDateTime().toString("yyyy-MM-dd"));

    /*!
     * \brief reqGetNetOrderCountByStatus 获取个订单数量
     * \param callback QML回调
     * \return
     */
    Q_INVOKABLE void reqGetNetOrderCountByStatus(QJSValue callback, QVariant begin_time, QVariant end_time);
    /*!
     * \brief reqGetNetOrderCountNewByStatus 根据类型获取订单类型数量
     * \param callback
     * \param begin_time
     * \param end_time
     * \param orderType
     */
    Q_INVOKABLE void reqGetNetOrderCountNewByStatus(QJSValue callback, QVariant begin_time, QVariant end_time,QString orderType);
    /*!
     * \brief reqRefundRecord4Qml 查询退款订单
     * \param callback QML回调
     * \param record_status 要查询的订单状态
     * \param begin_time 开始时间
     * \param end_time 结束时间
     */
    Q_INVOKABLE void reqRefundRecord4Qml(QJSValue callback, QVariant record_status, QString begin_time = QDateTime::currentDateTime().toString("yyyy-MM-dd"),
                                         QString end_time = QDateTime::currentDateTime().toString("yyyy-MM-dd"));

    /*!
     * \brief reqRefundRecordDetail4Qml 查询退款订单详情
     * \param callback QML回调
     * \param record_unique 订单unique
     */
    Q_INVOKABLE void reqRefundRecordDetail4Qml(QJSValue callback, QVariant record_unique);


    /*!
     * \brief reqRefundRecordStateModify 修改退款单状态
     * \param callback QML回调
     * \param record_unique 退款单unique
     * \param handle_state 处理状态 3、同意退款；4、拒绝退款
     */
    Q_INVOKABLE void reqRefundRecordStateModify(QJSValue callback, QVariant record_unique, QVariant handle_state,
                                                QVariant remark = QVariant::fromValue(QString("")));


    /*!
     * \brief getStoreOrdersRecordDetail4Qml 获取订单详情
     * \param callback QML回调
     * \param sale_list_unique 订单unique
     * \return
     */
    Q_INVOKABLE bool getStoreOrdersRecordDetail4Qml(QJSValue callback, QString sale_list_unique);

    /*!
     * \brief getShopCourierList 获取商家配送员列表
     * \param callback QML回调
     * \return 商家配送员列表
     */
    Q_INVOKABLE void getShopCourierList(QJSValue callback);


    /*!
     * \brief receivingTakeawayOrder 网单接单
     * \param callback QML回调
     * \param order_json 订单信息json
     */
    Q_INVOKABLE void receivingTakeawayOrder(QJSValue callback, QVariant order_json);

    /*!
     * \brief receivingTakeawayOrder4SelfDispatch 自配送
     * \param callback QML回调
     * \param order_json 订单信息json
     * \param courier_json  配送员信息json
     */
    Q_INVOKABLE void receivingTakeawayOrder4SelfDispatch(QJSValue callback, QVariant order_json, QVariant courier_json);

    /*!
     * \brief confirmReceiptOrder 确认收货
     * \param callback QML回调
     * \param order_unique 订单unique
     */
    Q_INVOKABLE void confirmReceiptOrder(QJSValue callback, QVariant order_unique);


    /*!
     * \brief getDispatchTypeByName 根据配送类型名获取类型
     * \param name 配送类型名
     * \return  配送类型
     */
    Q_INVOKABLE QVariant getDispatchTypeByName(QVariant name);

    /*!
     * \brief getDispatchNameByType 根据配送类型获取类型名称
     * \param type 配送类型
     * \return 配送类型名称
     */
    Q_INVOKABLE QVariant getDispatchNameByType(QVariant type);


signals:
};

#endif // NETORDERCONTROL_H
