﻿#ifndef GOODSDATAMODEL_H
#define GOODSDATAMODEL_H

#include <QAbstractListModel>
#include <QObject>
#include <QVariant>
#include <cstddef>
#include <tuple>
#include <vector>
#include "DataModule/DataManager.h"

class GoodsDataModel : public QAbstractListModel
{
    Q_OBJECT
    Q_PROPERTY(GoodsData *goods_data READ goodsData WRITE setGoodsData)
    Q_PROPERTY(QString search_str READ getSearchStr WRITE setSearchStr NOTIFY sigSearchStrChanged)
    Q_PROPERTY(QVariant goods_kind_unique READ getGoodsKindUnique WRITE setGoodsKindUnique)
    Q_PROPERTY(QVariant v_goods_kind_unique READ getVGoodsKindUnique WRITE setVGoodsKindUnique NOTIFY vGoodsKindUniqueChanged FINAL)

    Q_PROPERTY(long curPageIndex READ curPageIndex WRITE setCurPageIndex NOTIFY curPageIndexChanged FINAL)
    Q_PROPERTY(long pageMaxNum READ pageMaxNum WRITE setPageMaxNum NOTIFY pageMaxNumChanged FINAL)

    Q_PROPERTY(bool isRecognition READ isRecognition WRITE setIsRecognition NOTIFY isRecognitionChanged FINAL)

    Q_PROPERTY(long goodsCount READ goodsCount NOTIFY goodsCountChanged FINAL)
public:

    explicit GoodsDataModel(QObject *parent = nullptr);

    enum
    {
        GOODS_BARCODE_ROLE = Qt::UserRole, // 商品条码
        GOODS_IN_PRICE,                    // 进价
        GOODS_SALE_PRICE_ROLE,             // 售价
        GOODS_CUS_PRICE_ROLE,              // 会员价
        GOODS_STANDARD_ROLE,               // 规格
        GOODS_NAME_ROLE,                   // 名字
        GOODS_NAME_MEMBER_ROLE,            // 名字 (会员)
        GOODS_POINTS_ROLE,                 // 积分
        GOODS_CHENG_TYPE_ROLE,             // 称重类型 0:按件 1:按重量
        PC_SHELF_STATE_ROLE,               // 上架状态 1:已上架 2:已下架
        GOODS_LIFE_ROLE,                   // 保质期
        GOODS_ALIAS_ROLE,                  // 别名
        GOODS_CONTAIN_ROLE,                // 高商品与基础规格换算数量
        GOODS_REMARKS_ROLE,                // 备注
        GOODS_PROMOTION_ROLE,              // 促销状态
        GOODS_BRAND_ROLE,                  // 品牌
        GOODS_COUNT_ROLE,                  // 库存
        SHOP_CART_AMOUNT_ROLE,             // 购物车存在数量
    };

    int rowCount(const QModelIndex &parent = QModelIndex()) const override;

    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;

    Qt::ItemFlags flags(const QModelIndex &index) const override;

    virtual QHash<int, QByteArray> roleNames() const override;

    int getIndexByBarcode(QString goods_barcode);
    int getRealCount() const;

    Q_INVOKABLE QVariant getItemByIndex(QVariant index);

    // 商品数据来源
    GoodsData *goodsData() const;
    void       setGoodsData(GoodsData *goods_data);

    // 搜索
    QString getSearchStr();
    void    setSearchStr(QString search_str);

    // 分类
    QVariant getGoodsKindUnique();
    void     setGoodsKindUnique(QVariant goods_kind_unique);

    // 虚拟分类
    QVariant getVGoodsKindUnique();
    void     setVGoodsKindUnique(QVariant v_goods_kind_unique);

    // 数据有效
    bool isDataValid(GoodsInfo *goods_info) const;

    // 刷新数据 并通知界面
    Q_INVOKABLE void refreshGoodsData();

    // 刷新数据
    void refreshGoodsPtrMap();


#ifdef _RECOGNITION_
    void refreshGoodsPtrMapByRecognition(const std::vector<std::tuple<std::string, float>> &recognition_list);
    void refreshGoodsPtrMapByRecognition();
#endif

    // 商品是否存在
    bool isGoodsExist(QString goods_barcode);

    long curPageIndex() const;
    void setCurPageIndex(long index);

    long pageMaxNum() const;
    void setPageMaxNum(long max_num);

    long page_max_num_   = 0;
    long cur_page_index_ = 0;

    static GoodsDataModel *goods_data_model_;
    static GoodsDataModel *goods_data_model_recognition_;

    Q_INVOKABLE void set2Static();
    Q_INVOKABLE void clearStatic();

    Q_INVOKABLE void set2Static4Recognition();
    Q_INVOKABLE void clearStatic4Recognition();

    Q_INVOKABLE void clearGoods();
    Q_INVOKABLE void clearGoods4Recognition();

    bool isRecognition();
    void setIsRecognition(bool is_recognition);

    long goodsCount();

signals:
    void sigSearchStrChanged();

    void curPageIndexChanged();
    void pageMaxNumChanged();

    void vGoodsKindUniqueChanged();
    void isRecognitionChanged();

    void goodsCountChanged();

private:
    GoodsData               *goods_data_ = nullptr;
    QString                  search_str_;
    unsigned long long       goods_kind_unique_ = 0;
    std::vector<GoodsInfo *> goods_data_ptr_vec_;
    long long                v_goods_kind_unique_ = 0;

    bool is_recognition_ = false;

    int reset_counter_ = 0;
};

#endif // GOODSDATAMODEL_H
