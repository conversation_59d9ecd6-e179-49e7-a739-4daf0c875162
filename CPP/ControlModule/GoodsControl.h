﻿#ifndef GOODSCONTROL_H
#define GOODSCONTROL_H

#include <QJSValue>
#include <QObject>
#include <QVariant>
#include <mutex>
#include "Utils/Utils.h"

class GoodsControl : public QObject
{
    Q_OBJECT
public:
    explicit GoodsControl(QObject *parent = nullptr);
    ~GoodsControl();

    void resetGoodsFlag();

    void reRequestData(bool is_force_get_all = false);

    //bool is_goods_init_        = false;
    //bool is_goods_kind_init_   = false;
    //bool is_v_goods_kind_init_ = false;

    // ==1 本地数据同步完毕 ==2 在线数据同步完毕
    int goods_init_status_        = 0;
    int goods_kind_init_status_   = 0;
    int v_goods_kind_init_status_ = 0;

    // 请求服务器所有商品并保存到本地
    Q_INVOKABLE void reqGoodsAndSave();

    /*!
     * \brief reqAfterTimeGoodsAndSave 请求输入时间后更改的商品的数据
     * \param date_time_str 时间str yyyy-MM-dd
     */
    Q_INVOKABLE void reqAfterTimeGoodsAndSave(QString date_time_str = Utils::DateTime::getOffsetYearDateTimeStr(0, 0, -3));

    // reqGoodsKindAndSave 请求服务器所有商品分类并保存到本地
    Q_INVOKABLE void reqGoodsKindAndSave();

    // reqGoodsKindAndSave 请求服务器所有商品分类并保存到本地
    Q_INVOKABLE void reqGoodsKindAndSave4Qml(QJSValue callback);

    /*!
     * \brief reqGoodsCountByKindUnique 请求服务器分类下商品数量
     * \param kind_unique 分类唯一ID
     */
    Q_INVOKABLE void reqGoodsCountByKindUnique(QJSValue callback, QString kind_unique);

    /*!
     * \brief reqAddGoodsKind 请求服务器添加商品分类
     * \param callback qml回调
     * \param kind_parent_unique 父类唯一ID
     * \param kind_unique 分类名
     */
    Q_INVOKABLE void reqAddGoodsKind(QJSValue callback, QVariant kind_parent_unique, QVariant kind_name);

    /*!
     * \brief reqAddRootGoodsKind 请求服务器添加商品父级分类
     * \param callback qml回调
     * \param kind_name 分类名
     */
    Q_INVOKABLE void reqAddRootGoodsKind(QJSValue callback, QVariant kind_name);

    /*!
     * \brief reqGoodsCountByKindUnique 请求服务器删除商品分类
     * \param kind_unique 分类唯一ID
     * \return
     */
    Q_INVOKABLE void reqDelGoodsKindByKindUnique(QJSValue callback, QVariant kind_unique);

    /*!
     * \brief reqGoodsCountByKindUnique 请求服务器更改商品分类名称
     * \param kind_unique 分类唯一ID
     * \return
     */
    Q_INVOKABLE void reqChangeGoodsKindNameByKindUnique(QJSValue callback, QVariant kind_unique, QVariant kind_name);

    // 添加新商品到服务器
    Q_INVOKABLE void reqUploadGoods(QJSValue callback, QString data_json_str);
    Q_INVOKABLE void reqUploadGoods_v2(QJSValue callback, QString data_json_str);
    Q_INVOKABLE void reqUploadGoods_v2_4Old(QJSValue callback, QString data_json_str);

    // 从服务器删除商品
    Q_INVOKABLE void reqDelGoodsByBarcode(QJSValue callback, QVariant goods_barcode);

    // 根据json字符串更新商品到服务器
    Q_INVOKABLE void reqUpdateGoodsByDataJson(QJSValue callback, const QString &data_json_str);
    Q_INVOKABLE void reqUpdateGoodsByDataJson_v2(QJSValue callback, const QString &data_json_str);
    Q_INVOKABLE void reqUpdateGoodsByDataJson_v2_4Old(QJSValue callback, const QString &data_json_str);

    /*!
     * \brief reqCreateHighLevelGoodsByDataJson 新建高规格商品到服务器
     * \param callback qml回调
     * \param data_json_str JSON字符串数据
     */
    Q_INVOKABLE void reqCreateHighLevelGoodsByDataJson(QJSValue callback, const QString &data_json_str);

    /*!
     * \brief reqUpdateHighLevelGoodsByDataJson 更新高规格商品到服务器
     * \param callback qml回调
     * \param data_json_str JSON字符串数据
     */
    Q_INVOKABLE void reqUpdateHighLevelGoodsByDataJson(QJSValue callback, const QString &data_json_str);

    /*!
     * \brief reqStockChange 更改商品库存
     * \param callback qml回调
     * \param goods_barcode 商品条码
     * \param stock_change 库存变更
     * \return 成功?
     */
    Q_INVOKABLE void reqStockChange(QJSValue callback, QVariant goods_barcode, QVariant stock_change);
    Q_INVOKABLE void reqStockChange_v2(QJSValue callback, QVariant goods_barcode, QVariant stock_change);
    Q_INVOKABLE void reqStockChangeNew_v2(QJSValue callback, QVariant goods_barcode, QVariant stock_change,QVariant stockType,
                                          QVariant stockPrice,QVariant stockOutColorType,QVariant goodsProd,QVariant goodsExp,QVariant goodBatchMessage,QVariant stockTotalPrice);

    /*!
     * \brief reqOnlineGoodsInfo 查询线上商品信息
     * \param callback qml回调
     * \param goods_barcode 商品条码
     */
    Q_INVOKABLE void reqGetOnlineGoodsInfo(QJSValue callback, QVariant goods_barcode);
    /*!
     * \brief queryGoodsDetail
     * \param callback
     * \param goods_barcode
     */
    Q_INVOKABLE void queryGoodsDetail(QJSValue callback, QVariant goods_barcode);

    ///-------------------------------------------| 商品单位 |-------------------------------------------
    //
    Q_INVOKABLE void reqGoodsUnitAndSave(QJSValue callback = 0);

    Q_INVOKABLE void reqAddGoodsUnit(QJSValue callback, QVariant goods_unit);

    Q_INVOKABLE void reqDelGoodsUnit(QJSValue callback, QVariant goods_unit_id);

    Q_INVOKABLE void reqUpdateGoodsUnit(QJSValue callback, QVariant goods_unit_id, QVariant goods_unit);
    //
    ///-------------------------------------------| 商品单位 |-------------------------------------------


    ///-------------------------------------------| 虚拟分类 |-------------------------------------------
    //
    /*!
     * \brief reqGetVirualGoodsKindAndSave 请求虚拟分类并保存
     */
    Q_INVOKABLE void reqGetVirualGoodsKindAndSave();

    /*!
     * \brief reqGetVirualGoodsKindAndSave 请求虚拟分类并保存
     */
    Q_INVOKABLE void reqGetVirualGoodsKindAndSave4Qml(QJSValue callback = 0);

    /*!
     * \brief reqAddVirualGoodsKind4Qml 请求添加虚拟分类
     * \param callback QML回调
     * \param goods_kind_name 虚拟分类名
     */
    Q_INVOKABLE void reqAddVirualGoodsKind4Qml(QJSValue callback, QVariant goods_kind_name);

    /*!
     * \brief reqDelVirualGoodsKind4Qml 请求删除虚拟分类
     * \param callback QML回调
     * \param goods_kind_unique 虚拟分类unique
     */
    Q_INVOKABLE void reqDelVirualGoodsKind4Qml(QJSValue callback, QVariant goods_kind_unique);

    /*!
     * \brief reqChangeVirualGoodsKind4Qml 请求更改虚拟分类名称
     * \param callback QML回调
     * \param goods_kind_unique 虚拟分类unique
     * \param goods_kind_name 虚拟分类名称
     */
    Q_INVOKABLE void reqChangeVirualGoodsKind4Qml(QJSValue callback, QVariant goods_kind_unique, QVariant goods_kind_name);

    /*!
     * \brief reqAddGoodsByVirtualGoodsKind 请求从虚拟分类添加商品
     * \param callback QML回调
     * \param goods_kind_unique 虚拟分类unique
     * \param goods_barcode 商品条码
     */
    Q_INVOKABLE void reqAddGoodsByVirtualGoodsKind(QJSValue callback, QVariant goods_kind_id, QVariant goods_barcode);

    /*!
     * \brief reqDelGoodsByVirtualGoodsKind 请求从虚拟分类删除商品
     * \param callback QML回调
     * \param goods_kind_unique 虚拟分类unique
     * \param goods_barcode 商品条码
     */
    Q_INVOKABLE void reqDelGoodsByVirtualGoodsKind(QJSValue callback, QVariant goods_kind_id, QVariant goods_barcode);

    Q_INVOKABLE void sendRefreshSig();
    //
    ///-------------------------------------------| 虚拟分类 |-------------------------------------------

    void markCurDateTime4Req();

    Q_INVOKABLE static QString generateId();

    bool checkIsGoodsLoaded();

signals:
    void sigGoodsReload();
    void sigRefreshHomepageGoodsKind();

    void sigInitGoodsList();

    void sigVGoodsKindGoodsChanged();
};


#endif // GOODSCONTROL_H
