﻿#ifndef PERMISSIONCTRL_H
#define PERMISSIONCTRL_H

#include <QObject>
#include <set>
#include "EnumTool.h"

using PermissionEnum = EnumTool::PermissionEnum;

class PermissionCtrl : public QObject
{
    Q_OBJECT
public:
    explicit PermissionCtrl(QObject *parent = nullptr);

    /*!
     * \brief isHavePermission 是否拥有权限
     * \param permission 权限int
     * \return 拥有?
     */
    Q_INVOKABLE bool isHavePermission(int permission);

    /*!
     * \brief isHavePermission 是否拥有权限
     * \param permission 权限enum
     * \return 拥有?
     */
    bool isHavePermission(PermissionEnum permission);

    /*!
     * \brief addPermission 添加权限
     * \param permission 权限enum
     */
    void addPermission(PermissionEnum permission);

    /*!
     * \brief removePermission 删除权限
     * \param permission 权限enum
     */
    void removePermission(PermissionEnum permission);

    /*!
     * \brief clearPermission 清除所有权限
     */
    void clearPermission();

    std::set<PermissionEnum> permission_set_;

signals:
};

#endif // PERMISSIONCTRL_H
