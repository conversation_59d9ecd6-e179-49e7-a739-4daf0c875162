﻿#include "PermissionCtrl.h"
#include <iostream>

PermissionCtrl::PermissionCtrl(QObject *parent) : QObject{parent}
{
}

bool PermissionCtrl::isHavePermission(int permission)
{
    return isHavePermission(static_cast<PermissionEnum>(permission));
}

bool PermissionCtrl::isHavePermission(PermissionEnum permission)
{
    auto permission_set_iter = permission_set_.find(permission);
    if (permission_set_iter != permission_set_.end())
        return true;
    return false;
}

void PermissionCtrl::addPermission(PermissionEnum permission)
{
    permission_set_.insert(permission);
}

void PermissionCtrl::removePermission(PermissionEnum permission)
{
    auto permission_set_iter = permission_set_.find(permission);
    if (permission_set_iter != permission_set_.end())
        permission_set_.erase(permission_set_iter);
}

void PermissionCtrl::clearPermission()
{
    permission_set_.clear();
}
