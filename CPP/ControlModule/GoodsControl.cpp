﻿#include "GoodsControl.h"
#include <QApplication>
#include <functional>
#include <iostream>
#include <thread>
#include "ControlManager.h"
#include "DataModule/DataManager.h"
#include "LogModule/LogManager.h"
#include "NetModule/HttpWorker.h"
#include "NetModule/NetGlobal.h"
#include "Utils/Utils4Qml.h"
#include "gist/ThreadRAII.h"
#include "json-qt.hpp"
#include "NetModule/HttpCallback.h"

using namespace std;
using namespace AeaQt;

GoodsControl::GoodsControl(QObject *parent) : QObject{parent}
{
    qRegisterMetaType<GoodsInfo>("GoodsInfo");
    qRegisterMetaType<std::vector<GoodsInfo>>("std::vector<GoodsInfo>");
}

GoodsControl::~GoodsControl()
{
}

void GoodsControl::resetGoodsFlag()
{
    goods_init_status_        = 0;
    goods_kind_init_status_   = 0;
    v_goods_kind_init_status_ = 0;
}

void GoodsControl::reRequestData(bool is_force_get_all)
{
    auto config_tool = ConfigTool::getInstance();

    auto req_goods_begin_date_time = config_tool->getSetting(ConfigEnum::REQ_GOODS_BEGIN_DATE_TIME).toString();
    if (is_force_get_all || req_goods_begin_date_time.isEmpty())
    {        
        reqGoodsAndSave();
    }
    else
    {
        QDateTime config_date_time  = QDateTime::fromString(req_goods_begin_date_time, "yyyy-MM-dd hh:mm:ss");
        auto      tmp_date_time     = Utils::DateTime::getOffsetYearDateTimeStr(0, 0, -3);
        QDateTime default_date_time = QDateTime::fromString(tmp_date_time, "yyyy-MM-dd hh:mm:ss");

        QString req_date_time;

        if (default_date_time < config_date_time)
        {
            req_date_time = Utils::DateTime::getOffsetYearDateTimeStr(0, 0, -3);
        }
        else
        {
            req_date_time = req_goods_begin_date_time;
        }

        reqAfterTimeGoodsAndSave(req_date_time);
    }

    reqGoodsKindAndSave();
    reqGetVirualGoodsKindAndSave();
    reqGoodsUnitAndSave();

    markCurDateTime4Req();
}

void GoodsControl::reqGoodsAndSave()
{
    LOG_EVT_INFO("调用reqGoodsAndSave========");
    QString url = req_host_prefix + "shopmanager/pc/pcGoods.do";
    bodyMap body_map;
    body_map["shop_unique"] = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());
    body_map["today"]       = "0";
    body_map["isCache"]     = "2";

    auto cur_thread = new QThread(); // 工作线程

    HttpWorker *http_worker = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_worker->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程

    connect(http_worker, &HttpWorker::sendReply, this, // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply)
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc;

                    std::vector<GoodsInfo> goods_infos;

                    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

                    json_doc = Json::parse(reply_data_c);
//#ifdef BUYHOO_PARSE_DEBUG
//                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
//#endif
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "end--解析商品信息JSON");

                    for (auto data_i : json_doc["data"])
                    {
                        goods_infos.push_back(data_i.get<GoodsInfo>());
                    }
                    goods_mgr->deleteAllGoods(); // 清空所有商品数据
                    goods_mgr->addGoodsByList(goods_infos);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "end--商品信息到内存");

                    //同步商品后保存信息到本地
                    goods_mgr->saveGoodsData2Local_Thread();

                    emit sigGoodsReload();
                    Utils4Qml::getInstance()->sendToast(tr("同步商品成功"));
                    markCurDateTime4Req();
                }
                cur_thread->quit();
                cur_thread->wait();

                goods_init_status_ = 2;

                checkIsGoodsLoaded();
            });
    // 启动线程
    cur_thread->start();
}

void GoodsControl::reqAfterTimeGoodsAndSave(QString date_time_str)
{
    LOG_EVT_INFO("调用reqAfterTimeGoodsAndSave========");
    QString url = req_host_prefix + "shopmanager/pc/pcGoods.do";

    QVariantMap body_map;
    body_map["shop_unique"] = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());
    body_map["today"]       = "0";
    body_map["sameTime"]    = date_time_str;
    body_map["isCache"]     = "2";
    body_map["deleteType"]  = "3";

    HttpClient *http_client = new HttpClient();
    auto      &&request     = http_client->post(url);
    request.bodyWithFormUrlencoded(body_map);

    execAndCallback4Cpp(request,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                            switch (http_handle_type)
                            {
                            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                                {
                                    std::vector<GoodsInfo> goods_infos;
                                    std::vector<std::tuple<QString, QString>> delete_goods_ids;

                                    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

                                    Json json_doc = Json::parse(data, nullptr, false);

                                    if (!json_doc.is_discarded() && json_doc.contains("data") && json_doc["data"].is_array())
                                    {
                                        LOG_EVT_INFO("begin--解析商品信息");
                                        for (auto data_i : json_doc["data"])
                                        {
                                            bool is_data_valid = false;
                                            auto tmp           = getDataFromJson(data_i, "sameType").toInt(&is_data_valid);
                                            auto goods_names           = getDataFromJson(data_i, "goods_name");
                                            auto goods_counts           = getDataFromJson(data_i, "goods_count").toInt(&is_data_valid);
                                            //SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "goods_kind_name接口返回数据:{}",getDataFromJson(data_i, "goods_kind_name").toString());
                                            if (is_data_valid)
                                            {
                                                if (tmp == 3)
                                                {
                                                    auto tmp_goods_id = getDataFromJson(data_i, "id").toString();
                                                    auto tmp_goods_barcode = getDataFromJson(data_i, "goods_barcode").toString();
                                                    //暂不使用 update_time
                                                    delete_goods_ids.push_back(std::make_tuple(tmp_goods_id, tmp_goods_barcode));
                                                }
                                                else
                                                {
                                                    auto cur_item = data_i.get<GoodsInfo>();
                                                    //SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "接口返回数据:{}",json_doc.dump());

                                                    goods_infos.push_back(cur_item);
                                                }
                                            }
                                        }


                                        LOG_EVT_INFO("begin--商品信息到内存");
                                        goods_mgr->deleteGoodsByBarcodeList(delete_goods_ids);
                                        goods_mgr->addGoodsByList(goods_infos);
                                        LOG_EVT_INFO("end--商品信息到内存");

                                        markCurDateTime4Req();
                                    }
                                    else
                                    {
                                        Utils4Qml::getInstance()->sendToast("商品信息获取失败");
                                    }
                                    break;
                                }
                            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                                {
                                    // 网络错误
                                    Utils4Qml::getInstance()->sendToast("商品信息获取失败");
                                }
                            }
                            goods_init_status_ = 2;
                            checkIsGoodsLoaded();
                        });
}

void GoodsControl::reqGoodsKindAndSave()
{
    QString url = req_host_prefix + "shopmanager/pc/pcGoodsKind.do";
    bodyMap body_map;
    body_map["shop_unique"] = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());
    body_map["valid_type"]  = "1";

    auto cur_thread = new QThread(); // 工作线程

    HttpWorker *http_worker = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_worker->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_worker, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply)
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);

                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "分类信息查询:");

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    DataManager::getInstance()->getGoodsKindMgr()->deleteAllGoodsKind(); // 清空所有商品分类数据
                    for (auto data_i : json_doc["data"])
                    {
                        const auto goods_kind_info = data_i.get<GoodsKindInfo>();
                        DataManager::getInstance()->getGoodsKindMgr()->addGoodsKind(goods_kind_info);
                    }
                }
                const auto all_goods_kind = DataManager::getInstance()->getGoodsKindMgr()->getGoodsKindData();
                cur_thread->quit();
                cur_thread->wait();

                goods_kind_init_status_ = 2;
                checkIsGoodsLoaded();
            });
    // 启动线程
    cur_thread->start();
}

void GoodsControl::reqGoodsKindAndSave4Qml(QJSValue callback)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/pc/pcGoodsKind.do";
    bodyMap body_map;
    body_map["shop_unique"] = QString::number(ControlManager::getInstance()->getShopControl()->getShopUnique());
    body_map["valid_type"]  = "1";


    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);

                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "分类信息查询:");
#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    DataManager::getInstance()->getGoodsKindMgr()->deleteAllGoodsKind(); // 清空所有商品分类数据
                    for (auto data_i : json_doc["data"])
                    {
                        const auto goods_kind_info = data_i.get<GoodsKindInfo>();
                        DataManager::getInstance()->getGoodsKindMgr()->addGoodsKind(goods_kind_info);
                    }

                    if (callback.isCallable())
                        auto ret = callback.call();
                }
                const auto all_goods_kind = DataManager::getInstance()->getGoodsKindMgr()->getGoodsKindData();
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqGoodsCountByKindUnique(QJSValue callback, QString kind_unique)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/goodsKinds/getGoodsCountByKindUnique.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));
    body_map.insert("goodsKindUnique", kind_unique);

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                    else
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqAddGoodsKind(QJSValue callback, QVariant kind_parent_unique, QVariant kind_name)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/goodsKinds/modifyCustomKinds.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));
    body_map.insert("goodsKindParunique", kind_parent_unique.toString());
    body_map.insert("goodsKindName", kind_name.toString());

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{} {}", is_succ, reply->errorString().toStdString());
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    int status = json_doc["status"];
                    if (status == 1)
                    {
                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        // DataManager::getInstance()->getGoodsKindMgr()->addGoodsKind(kind_unique.toString());

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                    else if (status == 2)
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqAddRootGoodsKind(QJSValue callback, QVariant kind_name)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/goodsKinds/modifyCustomKinds.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));
    body_map.insert("goodsKindParunique", "0");
    body_map.insert("goodsKindName", kind_name.toString());

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        ControlManager::getInstance()->getGoodsControl()->reqGoodsKindAndSave4Qml(callback);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqDelGoodsKindByKindUnique(QJSValue callback, QVariant kind_unique)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/goodsKinds/modifyCustomKinds.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));
    body_map.insert("kindUnique", kind_unique.toString());
    body_map.insert("validType", "2");

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqChangeGoodsKindNameByKindUnique(QJSValue callback, QVariant kind_unique, QVariant kind_name)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/goodsKinds/modifyCustomKinds.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));
    body_map.insert("kindUnique", kind_unique.toString());
    body_map.insert("goodsKindName", kind_name.toString());

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        auto goods_kind_mgr = DataManager::getInstance()->getGoodsKindMgr();
                        goods_kind_mgr->changeGoodsKindNameByKindUnique(kind_unique, kind_name);

                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqUploadGoods(QJSValue callback, QString data_json_str)
{
    Json json_doc = Json::parse(data_json_str.toStdString());

    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/app/shop/appNewGoods.do";
    bodyMap body_map;
    body_map["shop_unique"]   = QString::number(shop_control->getShopUnique());
    body_map["staffId"]       = QString::number(shop_control->getUserId());
    body_map["id"]            = generateId();
    body_map["goods_contain"] = "1";
    body_map["goods_address"] = "";
    body_map["stockCount"]    = "0";
    body_map["goods_remarks"] = "";
    body_map["goods_life"]    = "";
    body_map["goods_picture"] = "";

    QVariant var_tmp;

    tryFromJson(json_doc, "default_supplier_unique", var_tmp);
    body_map["default_supplier_unique"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_barcode", var_tmp);
    body_map["goods_barcode"] = var_tmp.toString();
    body_map["foreign_key"]   = var_tmp.toString();

    tryFromJson(json_doc, "goods_kind_unique", var_tmp);
    body_map["goods_kind_unique"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_name", var_tmp);
    body_map["goods_name"] = var_tmp.toString();
    tryFromJson(json_doc, "goods_in_price", var_tmp);
    body_map["goods_in_price"] = var_tmp.toString();
    tryFromJson(json_doc, "goods_sale_price", var_tmp);
    body_map["goods_sale_price"] = var_tmp.toString();

    // 没有会员价 使用普通价格
    tryFromJson(json_doc, "goods_cus_price", var_tmp);
    if (var_tmp.toString().isEmpty())
        tryFromJson(json_doc, "goods_sale_price", var_tmp);
    body_map["goods_cus_price"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_brand", var_tmp);
    body_map["goods_brand"] = var_tmp.toString();
    tryFromJson(json_doc, "goods_standard", var_tmp);
    body_map["goods_standard"] = var_tmp.toString();
    tryFromJson(json_doc, "goodsChengType", var_tmp);
    body_map["goodsChengType"] = var_tmp.toString();

    tryFromJson(json_doc, "pc_shelf_state", var_tmp);
    body_map["pc_shelf_state"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_unit", var_tmp);
    body_map["goods_unit"] = var_tmp.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QVariant var_tmp;
                    tryFromJson(json_doc, "", var_tmp);

                    int status = var_tmp.toInt();

                    if (status == 0)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqUploadGoods_v2(QJSValue callback, QString data_json_str)
{
    QString req_url = req_host_prefix + "shopUpdate/goods/v2/addGoods.do";

    auto shop_control = ControlManager::getInstance()->getShopControl();

    auto goods_kind_mgr = DataManager::getInstance()->getGoodsKindMgr();

    auto in_json = Json::parse(data_json_str.toStdString());

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    if (!in_json.is_array() || in_json.size() < 1)
        return;

    auto basic_item = in_json[0];

    Json body_json;
    body_json["shopUnique"] = shop_control->getShopUnique();

    QVariant var_tmp;
    if (tryFromJson(basic_item, "goodsChengType", var_tmp))
        body_json["goodsChengType"] = var_tmp.toInt();

    auto default_goods_kind = goods_kind_mgr->defaultGoodsKind();

    if (default_goods_kind.isEmpty())
    {
        body_json["goodsKindUnique"] = var_tmp.toString().toStdString();
    }
    else
    {
        if (tryFromJson(basic_item, "goods_kind_unique", var_tmp))
        {
            body_json["goodsKindUnique"] = goods_kind_mgr->defaultGoodsKind().toStdString();
        }
    }

    if (tryFromJson(basic_item, "goods_barcode", var_tmp))
        body_json["foreignKey"] = var_tmp.toULongLong();

    if (tryFromJson(basic_item, "default_supplier_unique", var_tmp))
        body_json["supplierUnique"] = var_tmp.toString().toStdString();

    if (tryFromJson(basic_item, "goods_brand", var_tmp))
        body_json["goodsBrand"] = var_tmp.toString().toStdString();


    Json json_goods_message;
    for (int i = 0; i < in_json.size(); ++i)
    {
        Json single_goods_info;
        auto cur_goods = in_json[i];

        QVariant var_tmp;

        tryFromJson(cur_goods, "goods_barcode", var_tmp);
        single_goods_info["goodsBarcode"] = var_tmp.toString().toStdString();

        GoodsInfo goods_info_tmp;
        bool      is_goods_exist = goods_mgr->getGoodsByBarcode(var_tmp.toString(), goods_info_tmp);

        if (is_goods_exist)
            single_goods_info["goodsId"] = goods_info_tmp.goods_id;

        if (tryFromJson(cur_goods, "goods_name", var_tmp))
            single_goods_info["goodsName"] = var_tmp.toString().toStdString();

        if (tryFromJson(cur_goods, "goods_in_price", var_tmp))
            single_goods_info["goodsInPrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_sale_price", var_tmp))
            single_goods_info["goodsSalePrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_web_sale_price", var_tmp))
            single_goods_info["goodsWebSalePrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_cus_price", var_tmp))
            single_goods_info["goodsCusPrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_unit", var_tmp))
            single_goods_info["goodsUnit"] = var_tmp.toString().toStdString();

        if (tryFromJson(cur_goods, "goods_contain", var_tmp))
            single_goods_info["goodsContain"] = var_tmp.toString().toInt();

        if (tryFromJson(cur_goods, "goods_standard", var_tmp))
            single_goods_info["goodsStandard"] = var_tmp.toString().toStdString();

        if (tryFromJson(cur_goods, "web_shelf_state", var_tmp))
            single_goods_info["shelfState"] = var_tmp.toString().toUInt();

        if (tryFromJson(cur_goods, "pc_shelf_state", var_tmp))
            single_goods_info["pcShelfState"] = var_tmp.toString().toInt();

        json_goods_message.push_back(single_goods_info);
    }
    body_json["goodsMessage"] = json_goods_message;
    body_json["goodsCount"]   = 0;

    auto        cur_thread = new QThread(this); // 工作线程
    HttpWorker *cur_http   = new HttpWorker(req_url, QString::fromStdString(body_json.dump()).toUtf8());
    cur_http->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, cur_http, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, cur_http, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);

                    int status = var_tmp.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    cur_thread->start();
}

void GoodsControl::reqUploadGoods_v2_4Old(QJSValue callback, QString data_json_str)
{
    QString req_url = req_host_prefix + "shopUpdate/goods/v2/addGoods.do";

    auto shop_control = ControlManager::getInstance()->getShopControl();

    auto in_json = Json::parse(data_json_str.toStdString());

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    if (!in_json.is_array() || in_json.size() < 1)
        return;

    auto basic_item = in_json[0];

    Json body_json;
    body_json["shopUnique"] = shop_control->getShopUnique();

    QVariant var_tmp;
    if (tryFromJson(basic_item, "goodsChengType", var_tmp))
        body_json["goodsChengType"] = var_tmp.toInt();

    if (tryFromJson(basic_item, "goods_kind_unique", var_tmp))
        body_json["goodsKindUnique"] = var_tmp.toString().toStdString();

    if (tryFromJson(basic_item, "goods_barcode", var_tmp))
        body_json["foreignKey"] = var_tmp.toULongLong();
    if (tryFromJson(basic_item, "goodsProd", var_tmp))
        body_json["goodsProd"] = var_tmp.toString().toStdString();
    if (tryFromJson(basic_item, "goodsLife", var_tmp))
        body_json["goodsLife"] = var_tmp.toString().toStdString();

    GoodsInfo goods_info_tmp0;
    bool      is_goods_exist = goods_mgr->getGoodsByBarcode(var_tmp.toString(), goods_info_tmp0);


    if (tryFromJson(basic_item, "default_supplier_unique", var_tmp))
        body_json["supplierUnique"] = var_tmp.toString().toStdString();

    if (tryFromJson(basic_item, "goods_brand", var_tmp))
        body_json["goodsBrand"] = goods_info_tmp0.goods_brand.toStdString();


    Json json_goods_message;
    for (int i = 0; i < in_json.size(); ++i)
    {
        Json single_goods_info;
        auto cur_goods = in_json[i];

        QVariant var_tmp;

        tryFromJson(cur_goods, "goods_barcode", var_tmp);
        single_goods_info["goodsBarcode"] = var_tmp.toString().toStdString();

        GoodsInfo goods_info_tmp;
        bool      is_goods_exist = goods_mgr->getGoodsByBarcode(var_tmp.toString(), goods_info_tmp);

        if (tryFromJson(cur_goods, "goods_name", var_tmp))
            single_goods_info["goodsName"] = var_tmp.toString().toStdString();

        if (tryFromJson(cur_goods, "goods_in_price", var_tmp))
            single_goods_info["goodsInPrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_sale_price", var_tmp))
            single_goods_info["goodsSalePrice"] = var_tmp.toString().toDouble();

        // if (tryFromJson(cur_goods, "goods_web_sale_price", var_tmp))
        //     single_goods_info["goodsWebSalePrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_cus_price", var_tmp))
            single_goods_info["goodsCusPrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_unit", var_tmp))
            single_goods_info["goodsUnit"] = var_tmp.toString().toStdString();
        if (tryFromJson(cur_goods, "goods_contain", var_tmp))
            single_goods_info["goodsContain"] = var_tmp.toString().toInt();

        if (tryFromJson(cur_goods, "goods_standard", var_tmp))
            single_goods_info["goodsStandard"] = var_tmp.toString().toStdString();

        single_goods_info["shelfState"]   = goods_info_tmp.shelfState;
        single_goods_info["pcShelfState"] = goods_info_tmp.pc_shelf_state;

        json_goods_message.push_back(single_goods_info);
    }
    body_json["goodsMessage"] = json_goods_message;
    body_json["goodsCount"]   = getDataFromJson(in_json[0], "goodsCount").toUInt();

    auto        cur_thread = new QThread(this); // 工作线程
    HttpWorker *cur_http   = new HttpWorker(req_url, QString::fromStdString(body_json.dump()).toUtf8());
    cur_http->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, cur_http, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, cur_http, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "商品新增返回数据:{}",json_doc.dump());
#endif

                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);

                    int status = var_tmp.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    cur_thread->start();
}

void GoodsControl::reqDelGoodsByBarcode(QJSValue callback, QVariant goods_barcode)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/goods/deleteShopsGoods.do";
    bodyMap body_map;
    body_map["shopUnique"]   = QString::number(shop_control->getShopUnique());
    body_map["goodsBarcode"] = goods_barcode.toString();

    auto        cur_thread_p   = new QThread(this); // 工作线程
    QString     url_encode_str = HttpWorker::urlEncode(body_map);
    HttpWorker *http_worker    = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_worker->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_worker, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_worker, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自{}的返回", request_url.toString().toStdString());
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);

                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);

                    int status = var_tmp.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        auto ret = callback.call(arglist);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqUpdateGoodsByDataJson(QJSValue callback, const QString &data_json_str)
{
    Json json_doc     = Json::parse(data_json_str.toStdString());
    auto shop_control = ControlManager::getInstance()->getShopControl();
    auto goods_mgr    = DataManager::getInstance()->getGoodsMgr();

    QString cur_goods_barcode;
    if (!tryFromJson(json_doc, "goods_barcode", cur_goods_barcode))
    {
        // 如果没有此条码商品
        SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_net_, "错误调用 reqUpdateGoodsByDataJson");
        return;
    }

    GoodsInfo cur_goods_info;
    if (!goods_mgr->getGoodsByBarcode(cur_goods_barcode, cur_goods_info))
    {
        SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_net_, "获取商品失败");
        return;
    }

    bodyMap body_map;
    body_map["shop_unique"]   = QString::number(shop_control->getShopUnique());
    body_map["goods_barcode"] = cur_goods_barcode;
    body_map["staffId"]       = QString::number(shop_control->getUserId());
    body_map["goods_count"]   = QVariant::fromValue(cur_goods_info.goods_count).toString();
    body_map["goods_life"]    = QVariant::fromValue(cur_goods_info.goods_life).toString();
    body_map["goods_picture"] = cur_goods_info.goods_picturepath;
    body_map["goods_remarks"] = cur_goods_info.goods_remarks;

    QVariant var_tmp;

    tryFromJson(json_doc, "default_supplier_unique", var_tmp);
    body_map["default_supplier_unique"] = var_tmp.toString();

    if (tryFromJson(json_doc, "id", var_tmp))
        body_map["id"] = (var_tmp.toString() == "") ? generateId() : var_tmp.toString();
    else
        body_map["id"] = QString::number(cur_goods_info.goods_id);

    if (!tryFromJson(json_doc, "foreign_key", var_tmp))
        body_map["foreign_key"] = cur_goods_barcode;
    else
        body_map["foreign_key"] = var_tmp.toString();

    if (tryFromJson(json_doc, "goods_name", var_tmp))
        body_map["goods_name"] = var_tmp.toString();
    else
        body_map["goods_name"] = QVariant(cur_goods_info.goods_name).toString();

    if (tryFromJson(json_doc, "goods_in_price", var_tmp))
        body_map["goods_in_price"] = var_tmp.toString();
    else
        body_map["goods_in_price"] = QVariant(cur_goods_info.goods_in_price).toString();

    if (tryFromJson(json_doc, "goods_sale_price", var_tmp))
        body_map["goods_sale_price"] = var_tmp.toString();
    else
        body_map["goods_sale_price"] = QVariant(cur_goods_info.goods_sale_price).toString();

    tryFromJson(json_doc, "goods_cus_price", var_tmp);
    if (var_tmp.toString().isEmpty())
        body_map["goods_cus_price"] = body_map["goods_sale_price"];
    else
        body_map["goods_cus_price"] = var_tmp.toString();

    if (tryFromJson(json_doc, "goods_kind_unique", var_tmp))
        body_map["goods_kind_unique"] = var_tmp.toString();
    else
        body_map["goods_kind_unique"] = QVariant(cur_goods_info.goods_kind_unique).toString();

    if (tryFromJson(json_doc, "goods_brand", var_tmp))
        body_map["goods_brand"] = var_tmp.toString();
    else
        body_map["goods_brand"] = QVariant(cur_goods_info.goods_brand).toString();

    if (tryFromJson(json_doc, "goods_address", var_tmp))
        body_map["goods_address"] = var_tmp.toString();
    else
        body_map["goods_address"] = QVariant(cur_goods_info.goods_address).toString();

    if (tryFromJson(json_doc, "goods_standard", var_tmp))
        body_map["goods_standard"] = var_tmp.toString();
    else
        body_map["goods_standard"] = QVariant(cur_goods_info.goods_standard).toString();

    if (tryFromJson(json_doc, "goodsChengType", var_tmp))
        body_map["goodsChengType"] = var_tmp.toString();
    else
        body_map["goodsChengType"] = QVariant(cur_goods_info.goodsChengType).toString();

    if (tryFromJson(json_doc, "goods_contain", var_tmp))
        body_map["goods_contain"] = var_tmp.toString();
    else
        body_map["goods_contain"] = QVariant(cur_goods_info.goods_contain).toString();

    if (tryFromJson(json_doc, "pc_shelf_state", var_tmp))
        body_map["pc_shelf_state"] = var_tmp.toString();
    else
        body_map["pc_shelf_state"] = QVariant(cur_goods_info.pc_shelf_state).toString();

    if (tryFromJson(json_doc, "goods_unit", var_tmp))
        body_map["goods_unit"] = var_tmp.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    QString body_str = HttpWorker::urlEncode(body_map);
    QString url      = req_host_prefix + "shopmanager/app/shop/appNewGoods.do";

    HttpWorker *http_get_sales_record = new HttpWorker(url, body_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "ret data:{}", reply_data_c);

                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 0)
                    {
                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        auto ret = callback.call(arglist);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqUpdateGoodsByDataJson_v2(QJSValue callback, const QString &data_json_str)
{
    QString req_url = req_host_prefix + "shopUpdate/goods/v2/updateGoods.do";

    auto shop_control = ControlManager::getInstance()->getShopControl();

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto in_json = Json::parse(data_json_str.toStdString());

    if (!in_json.is_array() || in_json.size() < 1)
        return;

    auto basic_item = in_json[0];

    Json body_json;
    body_json["shopUnique"] = shop_control->getShopUnique();

    QVariant var_tmp;
    if (tryFromJson(basic_item, "goodsChengType", var_tmp))
        body_json["goodsChengType"] = var_tmp.toInt();

    if (tryFromJson(basic_item, "goods_kind_unique", var_tmp))
        body_json["goodsKindUnique"] = var_tmp.toString().toStdString();

    if (tryFromJson(basic_item, "goods_barcode", var_tmp))
        body_json["foreignKey"] = var_tmp.toULongLong();

    if (tryFromJson(basic_item, "default_supplier_unique", var_tmp))
        body_json["supplierUnique"] = var_tmp.toString().toStdString();

    if (tryFromJson(basic_item, "goods_brand", var_tmp))
        body_json["goodsBrand"] = var_tmp.toString().toStdString();

    Json json_goods_message;
    for (int i = 0; i < in_json.size(); ++i)
    {
        Json single_goods_info;
        auto cur_goods = in_json[i];

        QVariant var_tmp;

        tryFromJson(cur_goods, "goods_barcode", var_tmp);
        single_goods_info["goodsBarcode"] = var_tmp.toString().toStdString();

        GoodsInfo goods_info_tmp;
        bool      is_goods_exist = goods_mgr->getGoodsByBarcode(var_tmp.toString(), goods_info_tmp);

        if (is_goods_exist)
        {
            single_goods_info["goodsId"] = goods_info_tmp.goods_id;
        }

        if (tryFromJson(cur_goods, "goods_alias", var_tmp))
        {
            single_goods_info["goodsAlias"] = var_tmp.toString().toStdString();
        }
        else if (is_goods_exist)
        {
            single_goods_info["goodsAlias"] = goods_info_tmp.goods_alias.toStdString();
        }

        if (tryFromJson(cur_goods, "goods_name", var_tmp))
            single_goods_info["goodsName"] = var_tmp.toString().toStdString();

        if (tryFromJson(cur_goods, "goods_in_price", var_tmp))
            single_goods_info["goodsInPrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_sale_price", var_tmp))
            single_goods_info["goodsSalePrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_cus_price", var_tmp))
            single_goods_info["goodsCusPrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_web_sale_price", var_tmp))
            single_goods_info["goodsWebSalePrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_unit", var_tmp))
            single_goods_info["goodsUnit"] = var_tmp.toString().toStdString();

        if (tryFromJson(cur_goods, "goods_contain", var_tmp))
            single_goods_info["goodsContain"] = var_tmp.toString().toInt();

        if (tryFromJson(cur_goods, "goods_standard", var_tmp))
            single_goods_info["goodsStandard"] = var_tmp.toString().toStdString();

        if (tryFromJson(cur_goods, "pc_shelf_state", var_tmp))
            single_goods_info["pcShelfState"] = var_tmp.toString().toInt();

        if (tryFromJson(cur_goods, "web_shelf_state", var_tmp))
            single_goods_info["shelfState"] = var_tmp.toString().toInt();

        json_goods_message.push_back(single_goods_info);
    }
    body_json["goodsMessage"] = json_goods_message;


    auto        cur_thread = new QThread(this); // 工作线程
    HttpWorker *cur_http   = new HttpWorker(req_url, QString::fromStdString(body_json.dump()).toUtf8());
    cur_http->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, cur_http, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, cur_http, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);

                    int status = var_tmp.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    cur_thread->start();
}

void GoodsControl::reqUpdateGoodsByDataJson_v2_4Old(QJSValue callback, const QString &data_json_str)
{
    QString req_url = req_host_prefix + "shopUpdate/goods/v2/updateGoods.do";

    auto shop_control = ControlManager::getInstance()->getShopControl();

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto in_json = Json::parse(data_json_str.toStdString());

    if (!in_json.is_array() || in_json.size() < 1)
        return;

    auto basic_item = in_json[0];

    Json body_json;
    body_json["shopUnique"] = shop_control->getShopUnique();


    QVariant var_tmp;
    if (tryFromJson(basic_item, "goodsChengType", var_tmp))
        body_json["goodsChengType"] = var_tmp.toInt();

    if (tryFromJson(basic_item, "goods_kind_unique", var_tmp))
        body_json["goodsKindUnique"] = var_tmp.toString().toStdString();

    if (tryFromJson(basic_item, "goods_barcode", var_tmp))
        body_json["foreignKey"] = var_tmp.toULongLong();
    if (tryFromJson(basic_item, "goodsLife", var_tmp))
        body_json["goodsLife"] = var_tmp.toString().toStdString();
    GoodsInfo goods_info_tmp0;
    bool      is_goods_exist = goods_mgr->getGoodsByBarcode(var_tmp.toString(), goods_info_tmp0);

    if (tryFromJson(basic_item, "default_supplier_unique", var_tmp))
        body_json["supplierUnique"] = var_tmp.toString().toStdString();

    body_json["goodsBrand"] = goods_info_tmp0.goods_brand.toStdString();

    Json json_goods_message;
    for (int i = 0; i < in_json.size(); ++i)
    {
        Json single_goods_info;
        auto cur_goods = in_json[i];

        QVariant var_tmp;

        tryFromJson(cur_goods, "goods_barcode", var_tmp);
        single_goods_info["goodsBarcode"] = var_tmp.toString().toStdString();

        GoodsInfo goods_info_tmp;
        bool      is_goods_exist = goods_mgr->getGoodsByBarcode(var_tmp.toString(), goods_info_tmp);

        if (is_goods_exist)
            single_goods_info["goodsId"] = goods_info_tmp.goods_id;

        if (tryFromJson(cur_goods, "goods_name", var_tmp))
            single_goods_info["goodsName"] = var_tmp.toString().toStdString();

        if (tryFromJson(cur_goods, "goods_in_price", var_tmp))
            single_goods_info["goodsInPrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_sale_price", var_tmp))
            single_goods_info["goodsSalePrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_cus_price", var_tmp))
            single_goods_info["goodsCusPrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_web_sale_price", var_tmp))
            single_goods_info["goodsWebSalePrice"] = var_tmp.toString().toDouble();

        if (tryFromJson(cur_goods, "goods_unit", var_tmp))
            single_goods_info["goodsUnit"] = var_tmp.toString().toStdString();

        if (tryFromJson(cur_goods, "goods_contain", var_tmp))
            single_goods_info["goodsContain"] = var_tmp.toString().toInt();

        single_goods_info["goodsStandard"] = getDataFromJson(cur_goods, "goods_standard").toString().toStdString();
        LOG_EVT_INFO("goods_info_tmp.pc_shelf_state:{}",goods_info_tmp.pc_shelf_state);
        single_goods_info["pcShelfState"] = goods_info_tmp.pc_shelf_state;

        single_goods_info["shelfState"] = goods_info_tmp.shelfState;

        json_goods_message.push_back(single_goods_info);
    }
    body_json["goodsMessage"] = json_goods_message;


    auto        cur_thread = new QThread(this); // 工作线程
    HttpWorker *cur_http   = new HttpWorker(req_url, QString::fromStdString(body_json.dump()).toUtf8());
    cur_http->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, cur_http, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, cur_http, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误{}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);

                    int status = var_tmp.toInt();

                    if (status == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    cur_thread->start();
}

void GoodsControl::reqCreateHighLevelGoodsByDataJson(QJSValue callback, const QString &data_json_str)
{
    Json json_doc = Json::parse(data_json_str.toStdString());

    auto shop_control = ControlManager::getInstance()->getShopControl();

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    QString cur_goods_barcode;
    if (!tryFromJson(json_doc, "goods_barcode", cur_goods_barcode))
    {
        // 如果没有此条码商品
        SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_net_, "错误调用 reqCreateHighLevelGoodsByDataJson");
        return;
    }

    QString url = req_host_prefix + "shopmanager/app/shop/appNewGoods.do";

    bodyMap body_map;
    body_map["shop_unique"]   = QString::number(shop_control->getShopUnique());
    body_map["goods_barcode"] = cur_goods_barcode;
    body_map["staffId"]       = QString::number(shop_control->getUserId());
    body_map["goods_count"]   = "0";
    body_map["goods_life"]    = "";
    body_map["goods_picture"] = "";
    body_map["goods_remarks"] = "";
    body_map["id"]            = generateId();

    QVariant var_tmp;

    tryFromJson(json_doc, "default_supplier_unique", var_tmp);
    body_map["default_supplier_unique"] = var_tmp.toString();

    tryFromJson(json_doc, "foreign_key", var_tmp);
    body_map["foreign_key"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_name", var_tmp);
    body_map["goods_name"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_in_price", var_tmp);
    body_map["goods_in_price"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_sale_price", var_tmp);
    body_map["goods_sale_price"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_cus_price", var_tmp);
    if (var_tmp.toString().isEmpty())
    {
        body_map["goods_cus_price"] = body_map["goods_sale_price"];
    }
    else
    {
        body_map["goods_cus_price"] = var_tmp.toString();
    }

    tryFromJson(json_doc, "goods_kind_unique", var_tmp);
    body_map["goods_kind_unique"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_brand", var_tmp);
    body_map["goods_brand"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_standard", var_tmp);
    body_map["goods_standard"] = var_tmp.toString();

    tryFromJson(json_doc, "goodsChengType", var_tmp);
    body_map["goodsChengType"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_contain", var_tmp);
    body_map["goods_contain"] = var_tmp.toString();

    tryFromJson(json_doc, "pc_shelf_state", var_tmp);
    body_map["pc_shelf_state"] = var_tmp.toString();

    if (tryFromJson(json_doc, "goods_unit", var_tmp))
        body_map["goods_unit"] = var_tmp.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 0)
                    {
                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                    else
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqUpdateHighLevelGoodsByDataJson(QJSValue callback, const QString &data_json_str)
{
    Json json_doc = Json::parse(data_json_str.toStdString());

    auto shop_control = ControlManager::getInstance()->getShopControl();

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    QString cur_goods_barcode;
    if (!tryFromJson(json_doc, "goods_barcode", cur_goods_barcode))
    {
        // 如果没有此条码商品
        SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_net_, "错误调用 reqUpdateHighLevelGoodsByDataJson");
        return;
    }

    GoodsInfo cur_goods_info;
    if (!goods_mgr->getGoodsByBarcode(cur_goods_barcode, cur_goods_info))
    {
        SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_net_, "获取商品失败");
        return;
    }

    QString url = req_host_prefix + "shopmanager/app/shop/appNewGoods.do";

    bodyMap body_map;
    body_map["shop_unique"]             = QString::number(shop_control->getShopUnique());
    body_map["goods_barcode"]           = cur_goods_barcode;
    body_map["staffId"]                 = QString::number(shop_control->getUserId());
    body_map["goods_count"]             = QVariant::fromValue(cur_goods_info.goods_count).toString();
    body_map["goods_life"]              = QVariant::fromValue(cur_goods_info.goods_life).toString();
    body_map["goods_picture"]           = cur_goods_info.goods_picturepath;
    body_map["goods_remarks"]           = cur_goods_info.goods_remarks;
    body_map["default_supplier_unique"] = cur_goods_info.default_supplier_unique;
    body_map["id"]                      = QVariant::fromValue(cur_goods_info.goods_id).toString();
    body_map["foreign_key"]             = QVariant::fromValue(cur_goods_info.foreign_key).toString();
    body_map["goods_address"]           = QVariant::fromValue(cur_goods_info.goods_address).toString();

    QVariant var_tmp;

    tryFromJson(json_doc, "default_supplier_unique", var_tmp);
    body_map["default_supplier_unique"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_name", var_tmp);
    body_map["goods_name"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_in_price", var_tmp);
    body_map["goods_in_price"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_sale_price", var_tmp);
    body_map["goods_sale_price"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_cus_price", var_tmp);
    if (var_tmp.toString().isEmpty())
    {
        body_map["goods_cus_price"] = body_map["goods_sale_price"];
    }
    else
    {
        body_map["goods_cus_price"] = var_tmp.toString();
    }

    tryFromJson(json_doc, "goodsChengType", var_tmp);
    body_map["goodsChengType"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_standard", var_tmp);
    body_map["goods_standard"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_contain", var_tmp);
    body_map["goods_contain"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_kind_unique", var_tmp);
    body_map["goods_kind_unique"] = var_tmp.toString();

    tryFromJson(json_doc, "goods_brand", var_tmp);
    body_map["goods_brand"] = var_tmp.toString();

    tryFromJson(json_doc, "pc_shelf_state", var_tmp);
    body_map["pc_shelf_state"] = var_tmp.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 0)
                    {
                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                    else
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqStockChange(QJSValue callback, QVariant goods_barcode, QVariant stock_change)
{
    GoodsInfo cur_goods;
    DataManager::getInstance()->getGoodsMgr()->getGoodsByBarcode(goods_barcode.toString(), cur_goods);

    auto shop_control = ControlManager::getInstance()->getShopControl();


    QDateTime time           = QDateTime::currentDateTime(); // 获取当前时间
    int       timeT          = time.toTime_t();
    QString   strNew         = QString("%1").arg(QString::number(qrand() % 10000).toInt(), 4, 10, QLatin1Char('0'));
    QString   goodsFailIdTmp = QString::number(timeT) + strNew;

    QString url = req_host_prefix + "shopmanager/app/shop/appNewGoods.do";
    bodyMap body_map;
    body_map["shop_unique"]             = QString::number(shop_control->getShopUnique());
    body_map["goods_barcode"]           = cur_goods.goods_barcode;
    body_map["goods_name"]              = cur_goods.goods_name;
    body_map["goods_in_price"]          = QString::number(cur_goods.goods_in_price);
    body_map["goods_sale_price"]        = QString::number(cur_goods.goods_sale_price);
    body_map["goods_cus_price"]         = cur_goods.goods_cus_price;
    body_map["goods_count"]             = QString::number(cur_goods.goods_count);
    body_map["goods_kind_unique"]       = QString::number(cur_goods.goods_kind_unique);
    body_map["default_supplier_unique"] = cur_goods.default_supplier_unique;
    body_map["goods_picture"]           = cur_goods.goods_picturepath;
    body_map["goods_brand"]             = cur_goods.goods_brand;
    body_map["goods_remarks"]           = cur_goods.goods_remarks;
    body_map["goods_address"]           = cur_goods.goods_address;
    body_map["goods_standard"]          = cur_goods.goods_standard;
    body_map["goods_life"]              = cur_goods.goods_life;
    body_map["stockCount"]              = stock_change.toString();
    // body_map["stockCount"]              = QString::number(cur_goods.goods_count);
    body_map["staffId"]        = QString::number(shop_control->getUserId());
    body_map["goodsChengType"] = QString::number(cur_goods.goodsChengType);
    body_map["id"]             = goodsFailIdTmp;
    body_map["foreign_key"]    = QString::number(cur_goods.foreign_key);
    body_map["goods_contain"]  = QString::number(cur_goods.goods_contain);

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif


                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);
                    auto status = var_tmp.toInt();


                    if (status == 0)
                    { // 成功
                      //                        GoodsInfo *cur_goods_ptr = nullptr;
                        //                        bool       is_succ       = DataManager::getInstance()->getGoodsMgr()->getGoodsPtr(goods_id.toInt(),
                        //                        cur_goods_ptr);

                        //                        if (is_succ) {
                        //                            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_data_, "商品ID:{}库存更改{}", cur_goods_ptr->goods_id,
                        //                                               cur_goods.goods_count + stock.toFloat());

                        //                            cur_goods_ptr->goods_count = cur_goods.goods_count + stock.toFloat();
                        //                        } else {
                        //                            SPDLOG_LOGGER_ERROR(LogMgr::getInst()->logger_data_, "商品ID:{}库存更改失败", cur_goods_ptr->goods_id);
                        //                        }

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QString::fromStdString(json_doc.dump()));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void GoodsControl::reqStockChangeNew_v2(QJSValue callback, QVariant goods_barcode, QVariant stock_change,QVariant stockType,QVariant stockPrice,
                                        QVariant stockOutColorType,QVariant goodsProd,QVariant goodsExp,QVariant goodBatchMessage,QVariant stockTotalPrice)
{
    LOG_EVT_INFO("进入出入库接口");
    GoodsInfo cur_goods;
    DataManager::getInstance()->getGoodsMgr()->getGoodsByBarcode(goods_barcode.toString(), cur_goods);
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/stock/newStockRecord.do";
    bodyMap body_map;
    body_map["shopUnique"]   = QString::number(shop_control->getShopUnique());
    body_map["goodsBarcode"] = cur_goods.goods_barcode;
    body_map["goodsCount"]   = QString::number(fabs(stock_change.toDouble()));
    body_map["stockType"]    = stockType.toString();
    body_map["stockPrice"]   = stockPrice.toString();
    body_map["goodsProd"]    = goodsProd.toString();
    body_map["goodsExp"]    = goodsExp.toString();
    body_map["goodBatchMessage"]    = goodBatchMessage.toString();
    body_map["stockTotalPrice"]   = stockTotalPrice.toString();
    body_map["stockOrigin"]  = "2";
    body_map["reason"]  = stockOutColorType.toString();

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);
                    auto status = var_tmp.toInt();

                    if (status == 1)
                    { // 成功
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QString::fromStdString(json_doc.dump()));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QString::fromStdString(json_doc.dump()));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void GoodsControl::reqStockChange_v2(QJSValue callback, QVariant goods_barcode, QVariant stock_change)
{
    GoodsInfo cur_goods;
    DataManager::getInstance()->getGoodsMgr()->getGoodsByBarcode(goods_barcode.toString(), cur_goods);

    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/stock/newStockRecord.do";
    bodyMap body_map;
    body_map["shopUnique"]   = QString::number(shop_control->getShopUnique());
    body_map["goodsBarcode"] = cur_goods.goods_barcode;
    body_map["goodsCount"]   = QString::number(fabs(stock_change.toDouble()));
    body_map["stockType"]    = stock_change.toDouble() > 0 ? "1" : "2";
    body_map["stockPrice"]   = QString::number(cur_goods.goods_in_price);
    body_map["stockOrigin"]  = "2";

    auto cur_thread_p = new QThread(this); // 工作线程

    QString url_encode_str = HttpWorker::urlEncode(body_map);

    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QVariant var_tmp;
                    tryFromJson(json_doc, "status", var_tmp);
                    auto status = var_tmp.toInt();

                    if (status == 1)
                    { // 成功
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QString::fromStdString(json_doc.dump()));
                            auto ret = callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QString::fromStdString(json_doc.dump()));
                            auto ret = callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void GoodsControl::queryGoodsDetail(QJSValue callback, QVariant goods_barcode)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/app/shop/appQueryGoodsDetail.do";
    bodyMap body_map;
    body_map.insert("shop_unique", QString::number(shop_control->getShopUnique()));
    body_map.insert("goods_barcode", goods_barcode.toString());

    auto        cur_thread_p          = new QThread(); // 工作线程
    QString     url_encode_str        = HttpWorker::urlEncode(body_map);
    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                auto goods_mgr = DataManager::getInstance()->getGoodsMgr();
                std::vector<GoodsInfo> goods_infos;
                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);
#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    if (json_doc["status"] == 0)
                    {
                            LOG_EVT_INFO("单个商品--商品信息解析");
                                bool is_data_valid = false;
                                //auto tmp           = getDataFromJson(data_i, "sameType").toInt(&is_data_valid);
                                auto goods_names           = getDataFromJson(json_doc["data"], "goods_name");
                                auto goods_counts           = getDataFromJson(json_doc["data"], "goods_count").toInt(&is_data_valid);
                                if (is_data_valid)
                                {
                                    LOG_EVT_INFO("商品库存量读取成功!");
                                    auto cur_item = json_doc["data"].get<GoodsInfo>();
                                    LOG_EVT_INFO("单个商品--商品信息解析");
                                    goods_infos.push_back(cur_item);
                                }
                            LOG_EVT_INFO("单个商品信息开始存储到内存");
                            bool result = goods_mgr->addGoodsByList(goods_infos);
                            LOG_EVT_INFO("单个商品信息结束存储到内存,结果为:{}",result);
                            //markCurDateTime4Req();

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                    else
                    {
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "商品详情接口请求失败");

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        callback.call(arglist);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void GoodsControl::reqGetOnlineGoodsInfo(QJSValue callback, QVariant goods_barcode)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/goodsDict/queryGoodsDictMsg.do";
    bodyMap body_map;
    body_map.insert("shop_unique", QString::number(shop_control->getShopUnique()));
    body_map.insert("goods_barcode", goods_barcode.toString());

    auto        cur_thread_p          = new QThread(); // 工作线程
    QString     url_encode_str        = HttpWorker::urlEncode(body_map);
    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 0)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                    else
                    {
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "获取商品单位失败");

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        callback.call(arglist);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqGoodsUnitAndSave(QJSValue callback)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/goodsUnit/findGoodsUnitList.do";
    bodyMap body_map;
    body_map.insert("shop_unique", QString::number(shop_control->getShopUnique()));

    auto        cur_thread_p          = new QThread(); // 工作线程
    QString     url_encode_str        = HttpWorker::urlEncode(body_map);
    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        goods_mgr->clearGoodsUnit();

                        if (json_doc.contains("data"))
                        {
                            auto json_data = json_doc["data"];

                            if (json_data.is_array())
                            {
                                for (auto json_data_item : json_data)
                                {
                                    goods_mgr->addGoodsUnit(json_data_item.get<GoodsUnitInfo>());
                                }
                            }
                        }

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                    else
                    {
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "获取商品单位失败");

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        callback.call(arglist);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqAddGoodsUnit(QJSValue callback, QVariant goods_unit)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/goodsUnit/addGoodsUnit.do";
    bodyMap body_map;
    body_map.insert("shop_unique", QString::number(shop_control->getShopUnique()));
    body_map.insert("goods_unit", goods_unit.toString());

    auto        cur_thread_p          = new QThread(); // 工作线程
    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        reqGoodsUnitAndSave(callback);
                    }
                    else
                    {
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "获取商品单位失败");

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        callback.call(arglist);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqDelGoodsUnit(QJSValue callback, QVariant goods_unit_id)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/goodsUnit/deleteGoodsUnit.do";
    bodyMap body_map;
    body_map.insert("shop_unique", QString::number(shop_control->getShopUnique()));
    body_map.insert("goods_unit_id", goods_unit_id.toString());

    auto        cur_thread_p          = new QThread(); // 工作线程
    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        reqGoodsUnitAndSave(callback);
                    }
                    else
                    {
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "获取商品单位失败");

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqUpdateGoodsUnit(QJSValue callback, QVariant goods_unit_id, QVariant goods_unit)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopUpdate/goodsUnit/editGoodsUnit.do";
    bodyMap body_map;
    body_map.insert("shop_unique", QString::number(shop_control->getShopUnique()));
    body_map.insert("goods_unit_id", goods_unit_id.toString());
    body_map.insert("goods_unit", goods_unit.toString());

    auto        cur_thread_p = new QThread(); // 工作线程
    HttpWorker *cur_http     = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    cur_http->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, cur_http, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, cur_http, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_http, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_http, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        reqGoodsUnitAndSave(callback);
                    }
                    else
                    {
                        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "获取商品单位失败");

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqGetVirualGoodsKindAndSave()
{
    auto shop_control = ControlManager::getInstance()->getShopControl();
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "查询虚拟商品分类列表cpp:");
    QString url = req_host_prefix + "shopmanager/goodsKindInvented/queryGoodsInventedList.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));

    auto        cur_thread_p          = new QThread(); // 工作线程
    QString     url_encode_str        = HttpWorker::urlEncode(body_map);
    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();


                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        auto                         json_data = json_doc["data"];
                        vector<VirtualGoodsKindInfo> virtual_goods_kind_infos;
                        for (auto json_data_item : json_data)
                        {
                            virtual_goods_kind_infos.push_back(json_data_item.get<VirtualGoodsKindInfo>());
                        }

                        auto virtual_goods_kind_mgr = DataManager::getInstance()->getVirtualGoodsKindMgr();
                        virtual_goods_kind_mgr->clearAllVirtualGoodsKind();
                        virtual_goods_kind_mgr->addVirualGoodsKindList(virtual_goods_kind_infos);

                        v_goods_kind_init_status_ = 2;
                        checkIsGoodsLoaded();
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqGetVirualGoodsKindAndSave4Qml(QJSValue callback)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "查询虚拟商品分类列表QML:");
    QString url = req_host_prefix + "shopmanager/goodsKindInvented/queryGoodsInventedList.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));

    auto        cur_thread_p          = new QThread(); // 工作线程
    QString     url_encode_str        = HttpWorker::urlEncode(body_map);
    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();


                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    if (json_doc["status"] == 1)
                    {
                        auto                         json_data = json_doc["data"];
                        vector<VirtualGoodsKindInfo> virtual_goods_kind_infos;

                        for (auto json_data_item : json_data)
                        {
                            virtual_goods_kind_infos.push_back(json_data_item.get<VirtualGoodsKindInfo>());
                        }

                        auto virtual_goods_kind_mgr = DataManager::getInstance()->getVirtualGoodsKindMgr();
                        virtual_goods_kind_mgr->clearAllVirtualGoodsKind();
                        virtual_goods_kind_mgr->addVirualGoodsKindList(virtual_goods_kind_infos);

                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqAddVirualGoodsKind4Qml(QJSValue callback, QVariant goods_kind_name)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/goodsKindInvented/addNewGoodsKindInventedMsg.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));
    body_map.insert("goodsKindName", goods_kind_name.toString());

    auto        cur_thread_p          = new QThread(this); // 工作线程
    QString     url_encode_str        = HttpWorker::urlEncode(body_map);
    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                if (reply->error() == QNetworkReply::TimeoutError)
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "超时");
                }

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        callback.call(arglist);
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqDelVirualGoodsKind4Qml(QJSValue callback, QVariant goods_kind_unique)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/goodsKindInvented/addNewGoodsKindInventedMsg.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));
    body_map.insert("validType", "0");
    body_map.insert("goodsKindUnique", goods_kind_unique.toString());

    auto        cur_thread_p          = new QThread(this); // 工作线程
    QString     url_encode_str        = HttpWorker::urlEncode(body_map);
    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                if (reply->error() == QNetworkReply::TimeoutError)
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "超时");
                }

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        auto virtual_goods_kind_mgr = DataManager::getInstance()->getVirtualGoodsKindMgr();

                        auto json_data = json_doc["data"];

                        for (auto json_data_item : json_data)
                        {
                            virtual_goods_kind_mgr->addVirualGoodsKind(json_data_item.get<VirtualGoodsKindInfo>());
                        }
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));
                            callback.call(arglist);
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqChangeVirualGoodsKind4Qml(QJSValue callback, QVariant goods_kind_unique, QVariant goods_kind_name)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/goodsKindInvented/addNewGoodsKindInventedMsg.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));
    body_map.insert("goodsKindUnique", goods_kind_unique.toString());
    body_map.insert("goodsKindName", goods_kind_name.toString());

    auto        cur_thread_p          = new QThread(this); // 工作线程
    QString     url_encode_str        = HttpWorker::urlEncode(body_map);
    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                if (reply->error() == QNetworkReply::TimeoutError)
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "超时");
                }

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        auto virtual_goods_kind_mgr = DataManager::getInstance()->getVirtualGoodsKindMgr();

                        auto json_data = json_doc["data"];

                        for (auto json_data_item : json_data)
                        {
                            virtual_goods_kind_mgr->addVirualGoodsKind(json_data_item.get<VirtualGoodsKindInfo>());
                        }
                    }
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

// ERROR
void GoodsControl::reqAddGoodsByVirtualGoodsKind(QJSValue callback, QVariant goods_kind_id, QVariant goods_barcode)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    // QString url = QString("http://***************:8087/") + "shopmanager/goodsKindInvented/addGoodsKindInventedGoodsList.do";
    QString url = req_host_prefix + "shopmanager/goodsKindInvented/addGoodsKindInventedGoodsList.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));
    body_map.insert("operateType", "1");
    body_map.insert("goodskindInventedId", goods_kind_id.toString());
    body_map.insert("goodsList", "[\"" + goods_barcode.toString() + "\"]");

    auto        cur_thread_p          = new QThread(this); // 工作线程
    QString     url_encode_str        = HttpWorker::urlEncode(body_map);
    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                if (reply->error() == QNetworkReply::TimeoutError)
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "超时");
                }

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误: {}", reply_data_c);
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            callback.call(arglist);
                        }
                    }
                    else
                    {
                        LOG_EVT_INFO("添加商品到此分类失败，原因为:{}",json_doc["msg"]);
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        callback.call(arglist);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::reqDelGoodsByVirtualGoodsKind(QJSValue callback, QVariant goods_kind_id, QVariant goods_barcode)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/goodsKindInvented/addGoodsKindInventedGoodsList.do";
    bodyMap body_map;
    body_map.insert("shopUnique", QString::number(shop_control->getShopUnique()));
    body_map.insert("operateType", "0");
    body_map.insert("goodskindInventedId", goods_kind_id.toString());
    body_map.insert("goodsList", "[\"" + goods_barcode.toString() + "\"]");

    auto        cur_thread_p          = new QThread(this); // 工作线程
    QString     url_encode_str        = HttpWorker::urlEncode(body_map);
    HttpWorker *http_get_sales_record = new HttpWorker(url, url_encode_str.toUtf8(), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                if (reply->error() == QNetworkReply::TimeoutError)
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "超时");
                }

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    if (json_doc["status"] == 1)
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(true);
                            callback.call(arglist);
                        }
                    }
                    else
                    {
                        if (callback.isCallable())
                        {
                            QJSValueList arglist;
                            arglist.push_back(false);
                            callback.call(arglist);
                        }
                    }
                }

                if (!is_succ)
                {
                    if (callback.isCallable())
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        callback.call(arglist);
                    }
                }

                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void GoodsControl::sendRefreshSig()
{
    emit sigVGoodsKindGoodsChanged();
}

void GoodsControl::markCurDateTime4Req()
{
    auto config_tool = ConfigTool::getInstance();
    config_tool->setSetting(ConfigEnum::REQ_GOODS_BEGIN_DATE_TIME, Utils::DateTime::getCurDateTimeStr());
}

QString GoodsControl::generateId()
{
    QDateTime time              = QDateTime::currentDateTime(); // 获取当前时间
    int       time_t            = time.toTime_t();
    QString   str_new           = QString("%1").arg(QString::number(qrand() % 10000).toInt(), 4, 10, QLatin1Char('0'));
    QString   goods_fail_id_tmp = QString::number(time_t) + str_new;
    return goods_fail_id_tmp;
}

//TODO 在线数据获取
bool GoodsControl::checkIsGoodsLoaded()
{
    //加载本地商品信息失败
    if (goods_init_status_ == -1)
    {
        goods_init_status_ = 0;
        ControlManager::getInstance()->getOnlineInfo(true);
        return false;
    }

    // //加载本地分类信息失败
    // if (goods_kind_init_status_ == -1)
    // {
    //     ControlManager::getInstance()->getGoodsControl()->reqGoodsAndSave();
    //     return false;
    // }

    // //加载本地虚拟分类信息失败
    // if (v_goods_kind_init_status_ == -1)
    // {
    //     ControlManager::getInstance()->getGoodsControl()->reqGoodsAndSave();
    //     return false;
    // }
    LOG_EVT_INFO("this->goods_init_status_:{};this->goods_kind_init_status_:{};v_goods_kind_init_status_:{}",this->goods_init_status_,this->goods_kind_init_status_,v_goods_kind_init_status_);
    if (this->goods_init_status_ == 1 && this->goods_kind_init_status_ == 1 && v_goods_kind_init_status_ == 1)
    {
        emit sigInitGoodsList();
        ControlManager::getInstance()->getOnlineInfo();
    }
    else if (this->goods_init_status_ == 2 && this->goods_kind_init_status_ == 2 && v_goods_kind_init_status_ == 2)
    {
        emit sigInitGoodsList();
        DataManager::getInstance()->saveData2Local();
    }
    return true;
}
