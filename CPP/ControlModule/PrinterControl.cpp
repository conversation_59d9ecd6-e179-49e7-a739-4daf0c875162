﻿#include "PrinterControl.h"
#include <QDebug>
#include <QImage>
#include <QPainter>
#include <QPrintDialog>
#include <QPrintPreviewDialog>
#include <QPrinter>
#include <QPrinterInfo>
#include <QStringList>
#include <Windows.h>
#include <cmath>
#include <stdio.h>
#include "ConfModule/ConfigTool.h"
#include "ControlModule/ControlManager.h"
#include "ControlModule/PrinterStruct.h"
#include "ControlModule/ShopControl.h"
#include "Utils/Utils.h"
#include "gist/ThreadRAII.h"
#include "json-qt.hpp"
#include "qnamespace.h"
#include "qpoint.h"
#include "qvariant.h"
#include "WorkerModule/PrinterWorker.h"

QStringList  PrinterControl::invalid_printer_list_;

void PrintError(DWORD dwError, LPCTSTR lpString)
{
#define MAX_MSG_BUF_SIZE 512
    TCHAR *msgBuf;
    DWORD  cMsgLen;

    cMsgLen = FormatMessage(FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_ALLOCATE_BUFFER | 40, NULL, dwError, MAKELANGID(0, SUBLANG_ENGLISH_US), (LPTSTR)&msgBuf,
                            MAX_MSG_BUF_SIZE, NULL);
    printf(("%s Error [%d]:: %s\n"), lpString, dwError, msgBuf);
    LocalFree(msgBuf);
#undef MAX_MSG_BUF_SIZE
}

BOOL RawDataToPrinter(LPTSTR szPrinterName, LPBYTE lpData, DWORD dwCount)
{
    HANDLE     hPrinter;
    DOC_INFO_1 DocInfo;
    DWORD      dwJob;
    DWORD      dwBytesWritten;

    // Need a handle to the printer.
    if (!OpenPrinter(szPrinterName, &hPrinter, NULL))
    {
        PrintError(GetLastError(), TEXT("OpenPrinter"));
        return FALSE;
    }

    // Fill in the structure with info about this "document."
    DocInfo.pDocName    = TEXT("My Document");
    DocInfo.pOutputFile = NULL;
    DocInfo.pDatatype   = TEXT("RAW");
    // Inform the spooler the document is beginning.
    if ((dwJob = StartDocPrinter(hPrinter, 1, (LPBYTE)&DocInfo)) == 0)
    {
        PrintError(GetLastError(), TEXT("StartDocPrinter"));
        ClosePrinter(hPrinter);
        return FALSE;
    }
    // Start a page.
    if (!StartPagePrinter(hPrinter))
    {
        PrintError(GetLastError(), TEXT("StartPagePrinter"));
        EndDocPrinter(hPrinter);
        ClosePrinter(hPrinter);
        return FALSE;
    }
    // Send the data to the printer.
    if (!WritePrinter(hPrinter, lpData, dwCount, &dwBytesWritten))
    {
        PrintError(GetLastError(), TEXT("WritePrinter"));
        EndPagePrinter(hPrinter);
        EndDocPrinter(hPrinter);
        ClosePrinter(hPrinter);
        return FALSE;
    }
    // End the page.
    if (!EndPagePrinter(hPrinter))
    {
        PrintError(GetLastError(), TEXT("EndPagePrinter"));
        EndDocPrinter(hPrinter);
        ClosePrinter(hPrinter);
        return FALSE;
    }
    // Inform the spooler that the document is ending.
    if (!EndDocPrinter(hPrinter))
    {
        PrintError(GetLastError(), TEXT("EndDocPrinter"));
        ClosePrinter(hPrinter);
        return FALSE;
    }
    // Tidy up the printer handle.
    ClosePrinter(hPrinter);
    // Check to see if correct number of bytes were written.
    if (dwBytesWritten != dwCount)
    {
        printf(("Wrote %d bytes instead of requested %d bytes.\n"), dwBytesWritten, dwCount);
        return FALSE;
    }
    return TRUE;
}


bool openCashBox()
{
    auto cashbox_printer = ConfigTool::getInstance()->getSetting(ConfigEnum::CASHBOX_PORT).toString();

    auto ret_printer_index = PrinterControl::invalid_printer_list_.indexOf(cashbox_printer);

    if (ret_printer_index != -1)
        return false;

    auto cashbox_printer_w = cashbox_printer.toStdWString();
    auto local_str         = cashbox_printer_w.c_str();

    LPTSTR printer_name = (LPTSTR)(local_str);

    LPBYTE p_bytes = (LPBYTE) "\x1B\x70\x30\x32\xC8";
    DWORD  dw_size = sizeof(p_bytes);

    bool ret = false;

    if (RawDataToPrinter(printer_name, p_bytes, dw_size))
        ret = true;

    // GlobalFree((HGLOBAL)p_bytes);

    if (RawDataToPrinter(printer_name, (LPBYTE) " ", (DWORD)1))
        ret = true;

    return ret;
}

PrinterControl::PrinterControl(QObject *parent) : QObject{parent}
{
    auto config_tool = ConfigTool::getInstance();

    is_use_food_ticket_ = config_tool->getSetting(ConfigToolEnum::ConfigEnum::PRINTER_IS_USE_FOOD_TICKET).toBool();
    emit sigIsUseFoodTicketChanged();

    food_ticket_index_ = config_tool->getSetting(ConfigToolEnum::ConfigEnum::PRINTER_FOOD_TICKET_INDEX).toULongLong();
    emit sigFoodTicketIndexChanged();

    ticket_printer_name_    = config_tool->getSetting(ConfigToolEnum::ConfigEnum::PRINTER_NAME_TICKET).toString();
    online_printer_name_    = config_tool->getSetting(ConfigToolEnum::ConfigEnum::PRINTER_NAME_ONLINE).toString();
    price_tag_printer_name_ = config_tool->getSetting(ConfigToolEnum::ConfigEnum::PRINTER_NAME_PRICE_TAG).toString();
    tickets_remark_info_ = config_tool->getSetting(ConfigToolEnum::ConfigEnum::REMARK_INFO_TICKETS).toString();
    ticket_printer_num_    = config_tool->getSetting(ConfigToolEnum::ConfigEnum::PRINTER_NUM_TICKET).toUInt();
    online_printer_num_    = config_tool->getSetting(ConfigToolEnum::ConfigEnum::PRINTER_NUM_ONLINE).toUInt();
    price_tag_printer_num_ = config_tool->getSetting(ConfigToolEnum::ConfigEnum::PRINTER_NUM_PRICE_TAG).toUInt();

    ticket_printer_is_print_ = config_tool->getSetting(ConfigToolEnum::ConfigEnum::PRINTER_IS_PRINT__TICKET).toBool();

    cur_price_tag_type_ = static_cast<PriceTagTemplateEnum>(config_tool->getSetting(ConfigEnum::TAG_TEMUPLATE_TYPE).toInt());

    {
        auto json_str = config_tool->getSetting(ConfigEnum::PRINTER_PRICE_TAG_40x30).toString().toStdString();
        Json json_doc = Json::parse(json_str, nullptr, false);
        if (!json_doc.is_discarded())
        {
            price_tag_40x30_ = json_doc.get<PriceTag40x30>();
        }
    }

    {
        auto json_str = config_tool->getSetting(ConfigEnum::PRINTER_PRICE_TAG_95x38).toString().toStdString();
        Json json_doc = Json::parse(json_str, nullptr, false);

        if (!json_doc.is_discarded())
        {
            price_tag_95x38_ = json_doc.get<PriceTag95x38>();
        }
    }

    {
        auto json_str = config_tool->getSetting(ConfigEnum::PRINTER_PRICE_TAG_60x35).toString().toStdString();
        Json json_doc = Json::parse(json_str, nullptr, false);

        if (!json_doc.is_discarded())
        {
            price_tag_60x35_ = json_doc.get<PriceTag60x35>();
        }
    }

    invalid_printer_list_.push_back("");
    invalid_printer_list_.push_back("无");
    invalid_printer_list_.push_back("Fax");
    invalid_printer_list_.push_back("Microsoft XPS Document Writer");
    invalid_printer_list_.push_back("OneNote for Windows 10");
    invalid_printer_list_.push_back("Microsoft Print to PDF");

    printer_worker_        = new PrinterWorker();
    printer_worker_thread_ = new QThread;

    qRegisterMetaType<std::shared_ptr<ShopCartList>>("std::shared_ptr<ShopCartList>");
    qRegisterMetaType<OrderInfo>("OrderInfo");
    qRegisterMetaType<std::string>("std::string");

    connect(printer_worker_thread_, &QThread::finished, printer_worker_, &CameraWorker::deleteLater);   // 工作线程停止,销毁工作对象
    connect(printer_worker_, &CameraWorker2::destroyed, printer_worker_thread_, &QThread::deleteLater); // 工作对象销毁,销毁工作线程

    connect(this, &PrinterControl::sigPrintOrder, printer_worker_, &PrinterWorker::printOrder);
    connect(this, &PrinterControl::sigPrintOrderCombine, printer_worker_, &PrinterWorker::printOrderCombine);
    connect(this, &PrinterControl::sigPrintOrder4Food, printer_worker_, &PrinterWorker::printOrder4Food);
    connect(this, &PrinterControl::sigPrintOrderNingyu, printer_worker_, &PrinterWorker::printOrderNingyu);

    printer_worker_->moveToThread(printer_worker_thread_);
    printer_worker_thread_->start();
}

QStringList PrinterControl::getPrinterNames()
{
    QStringList result;
    result.push_back(tr("无"));

    for (auto name_item : QPrinterInfo::availablePrinterNames())
    {
        result.push_back(name_item);
    }

    return result;
}

void PrinterControl::setTicketPrinterByName(QString name)
{
    ticket_printer_name_ = name;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NAME_TICKET, name);
}

void PrinterControl::setOnlinePrinterByName(QString name)
{
    online_printer_name_ = name;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NAME_ONLINE, name);
}

void PrinterControl::setPriceTagPrinterByName(QString name)
{
    LOG_EVT_INFO("重复打印接口名称:{}",name.toStdString());
    price_tag_printer_name_ = name;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NAME_PRICE_TAG, name);
}

QString PrinterControl::getTicketPrinterName()
{
    return ticket_printer_name_;
}
QString PrinterControl::getTicketsRemarkInfo()
{
    return tickets_remark_info_;
}
int PrinterControl::getTicketPrinterNum()
{
    return ticket_printer_num_;
}

int PrinterControl::getOnlinePrinterNum()
{
    return online_printer_num_;
}

int PrinterControl::getPriceTagPrinterNum()
{
    return price_tag_printer_num_;
}

void PrinterControl::setTicketPrinterNum(int num)
{
    ticket_printer_num_ = num;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NUM_TICKET, num);
}

void PrinterControl::setOnlinePrinterNum(int num)
{
    online_printer_num_ = num;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NUM_ONLINE, num);
}

void PrinterControl::setPriceTagPrinterNum(int num)
{
    price_tag_printer_num_ = num;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NUM_PRICE_TAG, num);
}

bool PrinterControl::setPriceTagPrintFormatByJson(QVariant json_str)
{
    Json json_doc = Json::parse(json_str.toString().toStdString());

    switch (cur_price_tag_type_)
    {
    case PriceTagTemplateEnum::PriceTagTemplate_40x30:
        {
            price_tag_40x30_ = json_doc.get<PriceTag40x30>();
            return price_tag_40x30_.save2Cfg();
        }
    case PriceTagTemplateEnum::PriceTagTemplate_95x38:
        {
            price_tag_95x38_ = json_doc.get<PriceTag95x38>();
            return price_tag_95x38_.save2Cfg();
        }
    case PriceTagTemplateEnum::PriceTagTemplate_60x35:
        {
            price_tag_60x35_ = json_doc.get<PriceTag60x35>();
            return price_tag_60x35_.save2Cfg();
        }
    default:
        return false;
    }
}

bool PrinterControl::reprintLastOrder()
{
    auto order_control = ControlManager::getInstance()->getOrderControl();
    if (order_control->backup_last_order_.get() == nullptr)
        return false;
    LOG_EVT_INFO("重复打印接口");
    printOrderThread(order_control->backup_last_order_.get(), 1, true);
    return true;
}
void PrinterControl::printTest()
{
    ShopCartList shop_cart_list;
    shop_cart_list.appendItemByBarcode(QString::number((unsigned long long)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS));
    printOrderThread(&shop_cart_list, 1, true);
    LOG_EVT_INFO("===========================开启钱箱检测==============================");
    openCashBox();
}

QImage PrinterControl::Text2BarcodeImg(const QString &str)
{
    Zint::QZint qzint;
    qzint.setSymbol(BARCODE_CODE128);
    qzint.setOption1(-1);
    qzint.setOption2(0);
    qzint.setOption3(0);
    //    qzint.setReaderInit(true);
    qzint.setScale(1);
    qzint.setText(str);
    qzint.setShowText(false);
    QImage img(550, 200, QImage::Format_RGB888);
    img.fill(QColor(255, 255, 255));
    QPainter painter(&img);
    QRectF   paintRect(0, 0, 550, 200);
    qzint.render(painter, paintRect);
    return img;
}

void PrinterControl::printOrder(ShopCartList *shop_cart_list, int print_num, std::string addition_info)
{
    if (!isPrinterValid(ticket_printer_name_))
        return;

    if (!ticket_printer_is_print_ && print_num < 1)
        return;

    float dpi_mm = 8;

    float   paper_width_mm  = 54;
    float   paper_height_mm = 800;
    QString bar_code        = QString("DH") + shop_cart_list->shop_cart_list_data_.order_unique_;
    int     font_size       = 24;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name_);
    printer.setResolution(mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setPageMargins(QMarginsF());
    printer.setPageSize(QPageSize(QPageSize::Custom));

    if (print_num > 0)
    {
        printer.setCopyCount(print_num);
    }
    else
    {
        printer.setCopyCount(ticket_printer_num_);
    }

    QSize canvas_size = QSize(paper_width_mm * dpi_mm, paper_height_mm * dpi_mm);

    int cur_v_pos = 0;
    // int left_margin = 0;
    int left_margin  = 3 * dpi_mm;
    int right_margin = left_margin;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    int price_h_pos_r = 0;

    cur_v_pos += 10; // 上边距

    QPixmap pixmap(canvas_size);
    pixmap.fill(QColor(255, 255, 255));

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);
    {
        printItemInfo item_info;
        item_info.text = shop_name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("品名　　　单价　 数量　 金额");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 商品详情
    {
        for (ShopCartItem &shop_cart_item : shop_cart_list->items())
        {
            GoodsInfo goods_info;
            goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);

            int max_height = 0;
            int name_width = 0;
            {
                printItemInfo item_info;
                //item_info.text = goods_info.goods_name;
                item_info.text = shop_cart_item.getGoodsName();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - left_margin - right_margin, item_info.getLineHeight());

                QFontMetrics metrics(item_info.font);
                QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
                painter.drawText(qrect, item_info.alignment_flag, elidedText);

                name_width = item_info.getLineWidth();

                QFontMetrics fm = painter.fontMetrics();
                name_width      = fm.width(item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();

                if (name_width > 110)
                    cur_v_pos += item_info.getLineHeight();
            }

            float cur_h_pos = 0;
            cur_h_pos += 18 * dpi_mm;

            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getPrice()); // QString::number(shop_cart_item.getPrice(), 'f', 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.goods_num_, 3);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getSubtotal());

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_v_pos += max_height;
        }
    }

    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("应付金额：　　") + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPrice()) + tr("元");
        LOG_EVT_INFO("========================应付金额2============================：{}",(Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPrice())).toStdString());
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text =
            tr("折后金额：　　") + Utils::String::getClearNumStr(shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment)) + tr("元");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        QString       actual_payment_amount;
        printItemInfo item_info;
        if (shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__CASH)
        {

            actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_);
        }
        else
        {
            actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_);
            //actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment));
        }

        item_info.text = tr("实付金额：　　") + actual_payment_amount + tr("元");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;

        QString give_change;

        if (shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__CASH)
        {
            give_change = Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_ -
                                                        shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment) -
                                                        shop_cart_list->shop_cart_list_data_.recharge_change);
        }
        else
        {
            give_change = "0";
        }

        // TODO 保留两位小数
        if (give_change.toDouble() < 0)
        {
            item_info.text = tr("优惠：　　　　") + Utils::String::getClearNumStr(std::abs(give_change.toDouble())) + tr("元");
        }
        else
        {
            item_info.text = tr("找零：　　　　") + give_change + tr("元");
        }

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;

        auto discount = shop_cart_list->getTotalGoodsPrice() - shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment) -
            shop_cart_list->shop_cart_list_data_.recharge_change;

        if (discount < 0)
            discount = 0;

        item_info.text = tr("优惠：　　　　") + Utils::String::getClearNumStr(discount) + tr("元");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("交易件数：　　") + Utils::String::getClearNumStr(shop_cart_list->orderQuantity());
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("支付方式：　　") + OrderInfo::getPayMethodStr(shop_cart_list->shop_cart_list_data_.payment);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    //if (shop_cart_list->shop_cart_list_data_.payment == static_cast<int>(EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD))
    //只要有会员ID就打印会员信息
    if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
    {
        Json json_tmp = Json::parse(addition_info, nullptr, false);

        if (!json_tmp.is_discarded())
        {
            {
                printItemInfo item_info;

                QString member_name;

                if (json_tmp.contains("cusName"))
                {
                    member_name = getDataFromJson(json_tmp, "cusName").toString();
                }
                else
                {
                    member_name = getDataFromJson(json_tmp, "cus_name").toString();
                }

                QString member_name_processed;

                if (!member_name.isEmpty())
                {

                    QString name_begin;
                    QString name_middle = member_name;
                    QString name_end;
                    name_end = QString(*member_name.rbegin());

                    if (member_name.size() > 2)
                    {
                        name_begin = QString(*member_name.begin());
                        name_middle.remove(member_name.size() - 1, 1);
                    }

                    name_middle.remove(0, 1);
                    name_middle.fill('*');
                    member_name_processed = (name_begin + name_middle + name_end);
                }

                item_info.text = tr("会员名：　　　") + member_name_processed;

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;

                QString member_blance;

                if (json_tmp.contains("cusBalance"))
                {
                    member_blance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                else
                {
                    member_blance = getDataFromJson(json_tmp, "cus_balance").toString();
                }

                item_info.text = tr("会员余额：　　") + Utils::String::getClearNumStr(member_blance.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;

                QString member_point;

                if (json_tmp.contains("cusPoints"))
                {
                    member_point = getDataFromJson(json_tmp, "cusPoints").toString();
                }
                else
                {
                    member_point = getDataFromJson(json_tmp, "cus_points").toString();
                }

                item_info.text = tr("会员积分：　　") + Utils::String::getClearNumStr(member_point.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
        }
    }

    {
        printItemInfo item_info;
        item_info.text = tr("收银员：　　　") + ControlManager::getInstance()->getShopControl()->getPersonInfo().name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("收款机：　　　") + QString("1");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        // item_info.text = "订单时间:" + shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        // item_info.font.setWordSpacing(-50);
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 条码
    {
        QImage img = Text2BarcodeImg(bar_code);
        img        = img.scaled(contain_w, 8 * dpi_mm);

        auto qrect = QRectF(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.drawPixmap(qrect.toRect(), QPixmap::fromImage(img));

        cur_v_pos += qrect.height();

        printItemInfo item_info;

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft;

        qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.setFont(item_info.font);
        painter.drawText(qrect, item_info.alignment_flag, bar_code);
    }

    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("联系电话:") + ControlManager::getInstance()->getShopControl()->getPersonInfo().phone;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("温馨提示:如需发票请联系前台");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    painter.end();
}

void PrinterControl::printOrder4Food(ShopCartList *shop_cart_list, int print_num, bool is_reprint, std::string addition_info)
{
    if (!isPrinterValid(ticket_printer_name_))
        return;

    if (!is_reprint)
    {
        setFoodTicketIndex(food_ticket_index_ + 1);
    }

    if (!ticket_printer_is_print_ && print_num < 1)
        return;

    float dpi_mm = 8;

    QString bar_code  = QString("DH") + shop_cart_list->shop_cart_list_data_.order_unique_;
    int     font_size = 24;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name_);
    printer.setResolution(mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setPageMargins(QMarginsF());
    printer.setPageSize(QPageSize(QPageSize::Custom));

    if (print_num > 0)
    {
        printer.setCopyCount(print_num);
    }
    else
    {
        printer.setCopyCount(ticket_printer_num_);
    }

    QSize canvas_size = QSize(54 * dpi_mm, 800 * dpi_mm);

    int cur_v_pos    = 0;
    int left_margin  = 0 * dpi_mm;
    int right_margin = 10 * dpi_mm;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    cur_v_pos += 10; // 上边距

    QPixmap pixmap(canvas_size);
    pixmap.fill(QColor(255, 255, 255));

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);

    painter.drawLine(QLineF(QPointF(58 * dpi_mm, 0), QPointF(58 * dpi_mm, 100 * dpi_mm)));

    {
        printItemInfo item_info;
        item_info.text = tr("排队序号: ") + QString::number(food_ticket_index_);
        item_info.font.setPixelSize(40);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = shop_name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("品名　　　　 　数量　　小计");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 商品详情
    {
        for (ShopCartItem &shop_cart_item : shop_cart_list->items())
        {
            GoodsInfo goods_info;
            goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);

            {
                printItemInfo item_info;
                item_info.text = goods_info.goods_name + "*" + QString::number(shop_cart_item.goods_num_, 'f', 0);
                item_info.font.setPixelSize(40);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop | Qt::TextWrapAnywhere;
                painter.setFont(item_info.font);

                float name_width  = item_info.getLineWidth();
                float name_height = item_info.getLineHeight();

                int multiplier = 1;
                if (name_width > contain_w)
                {
                    multiplier = ceil(name_width / contain_w);
                }
                name_height *= multiplier;

                auto qrect = QRectF(left_margin, cur_v_pos, contain_w, name_height);

                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;
                //item_info.text = QString::number(shop_cart_item.getSubtotal(), 'f', 2);
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getSubtotal());
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, contain_w - 4 * dpi_mm, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
        }
    }

    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }


    {
        printItemInfo item_info;
        item_info.text =
            tr("总金额：　　　") + Utils::String::getClearNumStr(shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment)) + tr("元");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("交易件数：　　") + Utils::String::getClearNumStr(shop_cart_list->orderQuantity());
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("支付方式：　　") + OrderInfo::getPayMethodStr(shop_cart_list->shop_cart_list_data_.payment);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    //只要有会员ID就打印会员信息
    if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
    {
        Json json_tmp = Json::parse(addition_info, nullptr, false);

        if (!json_tmp.is_discarded())
        {
            {
                printItemInfo item_info;

                QString member_name;

                if (json_tmp.contains("cusName"))
                {
                    member_name = getDataFromJson(json_tmp, "cusName").toString();
                }
                else
                {
                    member_name = getDataFromJson(json_tmp, "cus_name").toString();
                }

                if (!member_name.isEmpty())
                {

                    QString name_begin;
                    QString name_middle = member_name;
                    QString name_end;
                    name_end = QString(*member_name.rbegin());

                    if (member_name.size() > 2)
                    {
                        name_begin = QString(*member_name.begin());
                        name_middle.remove(member_name.size() - 1, 1);
                    }

                    name_middle.remove(0, 1);
                    name_middle.fill('*');

                    item_info.text = tr("会员名：　　　") + (name_begin + name_middle + name_end);
                }

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;

                QString member_blance;

                if (json_tmp.contains("cusBalance"))
                {
                    member_blance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                else
                {
                    member_blance = getDataFromJson(json_tmp, "cus_balance").toString();
                }

                item_info.text = tr("会员余额：　　") + Utils::String::getClearNumStr(member_blance.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;

                QString member_point;

                if (json_tmp.contains("cusPoints"))
                {
                    member_point = getDataFromJson(json_tmp, "cusPoints").toString();
                }
                else
                {
                    member_point = getDataFromJson(json_tmp, "cus_points").toString();
                }

                item_info.text = tr("会员积分：　　") + Utils::String::getClearNumStr(member_point.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
        }
    }

    {
        printItemInfo item_info;
        item_info.text = shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        // item_info.text = "订单时间:" + shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        // item_info.font.setWordSpacing(-50);
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 条码
    {
        QImage img = Text2BarcodeImg(bar_code);
        img        = img.scaled(contain_w, 8 * dpi_mm);

        auto qrect = QRectF(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.drawPixmap(qrect.toRect(), QPixmap::fromImage(img));

        cur_v_pos += qrect.height();

        printItemInfo item_info;

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft;

        qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.setFont(item_info.font);
        painter.drawText(qrect, item_info.alignment_flag, bar_code);
    }

    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("联系电话:") + ControlManager::getInstance()->getShopControl()->getPersonInfo().phone;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("温馨提示:如需发票请联系前台");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    painter.end();
}

void PrinterControl::printOrderThread(ShopCartList *shop_cart_list, int print_num, bool is_reprint, std::string addition_info)
{
    std::shared_ptr<ShopCartList> shop_cart_list_p;
    shop_cart_list_p.reset(new ShopCartList());
    shop_cart_list_p->shop_cart_Items_     = shop_cart_list->shop_cart_Items_;
    shop_cart_list_p->shop_cart_list_data_ = shop_cart_list->shop_cart_list_data_;
    const auto shop_control = ControlManager::getInstance()->getShopControl();

    if(shop_control->getShopType() =="6" ||shop_control->getShopType() =="7"){
        emit sigPrintOrderNingyu(shop_cart_list_p, print_num, is_reprint, addition_info);
    }
    else if (is_use_food_ticket_)
    {
        emit sigPrintOrder4Food(shop_cart_list_p, print_num, is_reprint, addition_info);
    }
    else
    {
        emit sigPrintOrder(shop_cart_list_p, print_num, addition_info);
    }
}
void PrinterControl::printOrderThreadCombine(ShopCartList *shop_cart_list, int print_num, bool is_reprint, std::string addition_info,double combined_online_pay_cash_,double combined_online_pay_online_,double toalPay)
{
    std::shared_ptr<ShopCartList> shop_cart_list_p;
    shop_cart_list_p.reset(new ShopCartList());
    shop_cart_list_p->shop_cart_Items_     = shop_cart_list->shop_cart_Items_;
    shop_cart_list_p->shop_cart_list_data_ = shop_cart_list->shop_cart_list_data_;
    emit sigPrintOrderCombine(shop_cart_list_p,print_num, addition_info,combined_online_pay_cash_,combined_online_pay_online_,toalPay);
}
void PrinterControl::printPriceTagByBarcode(QVariant goods_barcode)
{
    GoodsInfo goods_info;

    if (!DataManager::getInstance()->getGoodsMgr()->getGoodsByBarcode(goods_barcode.toString(), goods_info))
        return;

    switch (cur_price_tag_type_)
    {
    case PriceTagTemplateEnum::PriceTagTemplate_40x30:
        {
            printPriceTag40x30(goods_info);
            break;
        }
    case PriceTagTemplateEnum::PriceTagTemplate_95x38:
        {
            printPriceTag95x38(goods_info);
            break;
        }
    case PriceTagTemplateEnum::PriceTagTemplate_60x35:
        {
            printPriceTag60x35(goods_info);
            break;
        }
    default:
        break;
    }
}


void PrinterControl::printPriceTag40x30(GoodsInfo goods_info)
{
    if (!isPrinterValid(price_tag_printer_name_))
        return;

    QPrinter printer;

    printer.setPrinterName(price_tag_printer_name_);
    printer.setPageSize(QPrinter::Custom);

    auto paper_size = QSizeF(price_tag_40x30_.paper_w, price_tag_40x30_.paper_h);
    auto dpi_mm     = price_tag_40x30_.dpi_mm;
    printer.setPaperSize(paper_size, QPrinter::Millimeter);
    printer.setResolution(price_tag_40x30_.dpi_mm * 25.4);
    printer.setFullPage(true);
    printer.setPageMargins(price_tag_40x30_.margin_left, price_tag_40x30_.margin_top, 0, 0, QPrinter::Millimeter);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setCopyCount(price_tag_printer_num_);

    QPixmap pixmap(paper_size.width() * dpi_mm, paper_size.height() * dpi_mm);
    pixmap.fill(QColor(255, 255, 255));
    QPainter painter(&pixmap);

    QFont font("微软雅黑", price_tag_40x30_.goods_name_size);
    painter.setFont(font);


    auto goods_name_pos = QPointF(price_tag_40x30_.goods_name_x * dpi_mm, price_tag_40x30_.goods_name_y * dpi_mm);
    painter.drawText(goods_name_pos, goods_info.goods_name);

    font.setPointSize(price_tag_40x30_.rmb_symbol_size);
    painter.setFont(font);
    auto price_symbol_pos = QPointF(price_tag_40x30_.rmb_symbol_x * dpi_mm, price_tag_40x30_.rmb_symbol_y * dpi_mm);
    painter.drawText(price_symbol_pos, tr("¥"));

    font.setPointSize(price_tag_40x30_.price_size);
    painter.setFont(font);
    auto price_pos = QPointF(price_tag_40x30_.price_x * dpi_mm, price_tag_40x30_.price_y * dpi_mm);
    painter.drawText(price_pos, QString::number(goods_info.goods_sale_price, 10, 2));

    auto bar_code_img = QPixmap::fromImage(Text2BarcodeImg(goods_info.goods_barcode));

    auto bar_code_img_size = QSize(price_tag_40x30_.barcode_w * dpi_mm, price_tag_40x30_.barcode_h * dpi_mm);

    bar_code_img = bar_code_img.scaled(QSize(bar_code_img_size.width(), bar_code_img_size.height()));

    auto bar_code_pos = QPointF(price_tag_40x30_.barcode_x * dpi_mm, price_tag_40x30_.barcode_y * dpi_mm);
    painter.drawPixmap(bar_code_pos, bar_code_img);
    auto bar_code_pos_tmp = bar_code_pos;
    bar_code_pos_tmp.setY(bar_code_pos.y() + ((bar_code_img_size.height())));

    QRectF tmp_rect2(bar_code_pos_tmp, bar_code_img_size);

    font.setPointSize(price_tag_40x30_.barcode_size);
    painter.setFont(font);
    painter.drawText(tmp_rect2, Qt::AlignHCenter, goods_info.goods_barcode);

    painter.end();

    QPainter painterPixmap;
    painterPixmap.begin(&printer);
    painterPixmap.drawPixmap(0, 0, pixmap);
    painterPixmap.end();
}

void PrinterControl::printPriceTag95x38(GoodsInfo goods_info)
{
    if (!isPrinterValid(price_tag_printer_name_))
        return;

    QPrinter printer;

    printer.setPrinterName(price_tag_printer_name_);
    printer.setPageSize(QPrinter::Custom);

    auto paper_size = QSizeF(price_tag_95x38_.paper_w, price_tag_95x38_.paper_h);
    auto dpi_mm     = price_tag_95x38_.dpi_mm;
    printer.setPaperSize(paper_size, QPrinter::Millimeter);
    printer.setResolution(price_tag_95x38_.dpi_mm * 25.4);
    printer.setFullPage(true);
    printer.setPageMargins(price_tag_95x38_.margin_left, price_tag_95x38_.margin_top, 0, 0, QPrinter::Millimeter);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setCopyCount(price_tag_printer_num_);

    QPixmap pixmap(paper_size.width() * dpi_mm, paper_size.height() * dpi_mm);
    pixmap.fill(QColor(255, 255, 255));
    QPainter painter(&pixmap);

    QFont font("微软雅黑", price_tag_95x38_.goods_name_size);
    painter.setFont(font);

    auto goods_name_pos = QPointF(price_tag_95x38_.goods_name_x * dpi_mm, price_tag_95x38_.goods_name_y * dpi_mm);
    painter.drawText(goods_name_pos, goods_info.goods_name);

    font.setPointSize(price_tag_95x38_.rmb_symbol_size);
    painter.setFont(font);
    auto price_symbol_pos = QPointF(price_tag_95x38_.rmb_symbol_x * dpi_mm, price_tag_95x38_.rmb_symbol_y * dpi_mm);
    painter.drawText(price_symbol_pos, tr("¥"));

    font.setPointSize(price_tag_95x38_.price_size);
    painter.setFont(font);
    auto price_pos = QPointF(price_tag_95x38_.price_x * dpi_mm, price_tag_95x38_.price_y * dpi_mm);
    //    painter.drawText(price_pos, QString::number(goods_info.goods_sale_price));
    painter.drawText(price_pos, QString::number(goods_info.goods_sale_price, 10, 2));

    auto bar_code_img = QPixmap::fromImage(Text2BarcodeImg(goods_info.goods_barcode));

    auto bar_code_img_size = QSize(price_tag_95x38_.barcode_w * dpi_mm, price_tag_95x38_.barcode_h * dpi_mm);

    bar_code_img = bar_code_img.scaled(QSize(bar_code_img_size.width(), bar_code_img_size.height()));

    auto bar_code_pos = QPointF(price_tag_95x38_.barcode_x * dpi_mm, price_tag_95x38_.barcode_y * dpi_mm);
    painter.drawPixmap(bar_code_pos, bar_code_img);
    auto bar_code_pos_tmp = bar_code_pos;
    bar_code_pos_tmp.setY(bar_code_pos.y() + ((bar_code_img_size.height())));

    QRectF tmp_rect2(bar_code_pos_tmp, bar_code_img_size);

    font.setPointSize(price_tag_95x38_.barcode_size);
    painter.setFont(font);
    painter.drawText(tmp_rect2, Qt::AlignHCenter, goods_info.goods_barcode);

    painter.end();

    QPainter painterPixmap;
    painterPixmap.begin(&printer);
    painterPixmap.drawPixmap(0, 0, pixmap);
    painterPixmap.end();
}

void PrinterControl::printPriceTag60x35(GoodsInfo goods_info)
{
    if (!isPrinterValid(price_tag_printer_name_))
        return;

    auto cur_price_tag_conf = price_tag_60x35_;

    QPrinter printer;

    printer.setPrinterName(price_tag_printer_name_);
    printer.setPageSize(QPrinter::Custom);

    auto paper_size = QSizeF(cur_price_tag_conf.paper_w, cur_price_tag_conf.paper_h);
    auto dpi_mm     = cur_price_tag_conf.dpi_mm;
    printer.setPaperSize(paper_size, QPrinter::Millimeter);
    printer.setResolution(cur_price_tag_conf.dpi_mm * 25.4);
    printer.setFullPage(true);
    printer.setPageMargins(cur_price_tag_conf.margin_left, cur_price_tag_conf.margin_top, 0, 0, QPrinter::Millimeter);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setCopyCount(price_tag_printer_num_);

    QPixmap pixmap(paper_size.width() * dpi_mm, paper_size.height() * dpi_mm);
    pixmap.fill(QColor(255, 255, 255));
    QPainter painter(&pixmap);

    QFont font("微软雅黑", cur_price_tag_conf.goods_name_size);
    painter.setFont(font);

    //店铺名
    {
        auto shop_ctrl = ControlManager::getInstance()->getShopControl();
        auto tmp_pos   = QPointF(cur_price_tag_conf.shop_name_x * dpi_mm, cur_price_tag_conf.shop_name_y * dpi_mm);
        painter.drawText(tmp_pos, shop_ctrl->getShopName());
    }

    //商品名
    {
        auto goods_name_pos = QPointF(cur_price_tag_conf.goods_name_x * dpi_mm, cur_price_tag_conf.goods_name_y * dpi_mm);
        painter.drawText(goods_name_pos, goods_info.goods_name);
    }

    //价格
    {
        font.setPointSize(cur_price_tag_conf.price_size);
        painter.setFont(font);
        auto price_pos = QPointF(cur_price_tag_conf.price_x * dpi_mm, cur_price_tag_conf.price_y * dpi_mm);
        painter.drawText(price_pos, QString::number(goods_info.goods_sale_price, 10, 2));
    }

    //规格
    {
        font.setPointSize(cur_price_tag_conf.spec_size);
        painter.setFont(font);
        auto price_pos = QPointF(cur_price_tag_conf.spec_x * dpi_mm, cur_price_tag_conf.spec_y * dpi_mm);
        painter.drawText(price_pos, goods_info.goods_standard);
    }

    //单位
    {
        font.setPointSize(cur_price_tag_conf.unit_size);
        painter.setFont(font);
        auto price_pos = QPointF(cur_price_tag_conf.unit_x * dpi_mm, cur_price_tag_conf.unit_y * dpi_mm);
        painter.drawText(price_pos, goods_info.goods_unit);
    }

    //条码
    {
        auto bar_code_pos = QPointF(cur_price_tag_conf.barcode_x * dpi_mm, cur_price_tag_conf.barcode_y * dpi_mm);
        font.setPointSize(cur_price_tag_conf.barcode_size);
        painter.setFont(font);
        painter.drawText(bar_code_pos, goods_info.goods_barcode);
    }

    painter.end();

    QPainter painterPixmap;
    painterPixmap.begin(&printer);
    painterPixmap.drawPixmap(0, 0, pixmap);
    painterPixmap.end();
}

QString PrinterControl::getPriceTagInfo(PriceTagTemplateEnum price_tag_template_enum)
{
    QString result;
    Json    json_tmp;

    switch (price_tag_template_enum)
    {
    case PriceTagTemplateEnum::PriceTagTemplate_40x30:
        {
            json_tmp = price_tag_40x30_;
            result   = QString::fromStdString(json_tmp.dump());
            break;
        }
    case PriceTagTemplateEnum::PriceTagTemplate_95x38:
        {
            json_tmp = price_tag_95x38_;
            result   = QString::fromStdString(json_tmp.dump());
            break;
        }
    case PriceTagTemplateEnum::PriceTagTemplate_60x35:
        {
            json_tmp = price_tag_60x35_;
            result   = QString::fromStdString(json_tmp.dump());
            break;
        }
    default:
        break;
    }

    return result;
}

QString PrinterControl::getPriceTagInfo(int price_tag_template_enum)
{
    return getPriceTagInfo((PriceTagTemplateEnum)price_tag_template_enum);
}

void PrinterControl::printPriceTagTest()
{
    GoodsInfo goods_info;
    goods_info.goods_name       = tr("测试");
    goods_info.goods_barcode    = "9999999";
    goods_info.goods_sale_price = 99.99;
    goods_info.goods_unit       = tr("测试单位");
    goods_info.goods_standard   = tr("测试规格");

    switch (cur_price_tag_type_)
    {
    case PriceTagTemplateEnum::PriceTagTemplate_40x30:
        {
            printPriceTag40x30(goods_info);
            break;
        }
    case PriceTagTemplateEnum::PriceTagTemplate_95x38:
        {
            printPriceTag95x38(goods_info);
            break;
        }
    case PriceTagTemplateEnum::PriceTagTemplate_60x35:
        {
            printPriceTag60x35(goods_info);
            break;
        }
    default:
        break;
    }
}

void PrinterControl::printQueryOrder(QVariant json_data)
{
    if (!isPrinterValid(ticket_printer_name_))
        return;

    Json json_doc = Json::parse(json_data.toString().toStdString());
    if (!json_doc.contains("status") || !json_doc.contains("data") || !json_doc["data"].contains("sale_list_unique") ||
        !json_doc["data"].contains("sale_list_state") || !json_doc["data"].contains("sale_list_name") || !json_doc["data"].contains("sale_list_phone") ||
        !json_doc["data"].contains("sale_list_datetime") || !json_doc["data"].contains("payDetail"))
    {
        return;
    }

    float dpi_mm = 8;

    float paper_width_mm  = 54;
    float paper_height_mm = 800;

    QVariant bar_code; // 条码
    tryFromJson(json_doc["data"], "sale_list_unique", bar_code);

    QVariant sale_list_state; // 支付状态
    tryFromJson(json_doc["data"], "sale_list_state", sale_list_state);

    QVariant sale_list_name; // 客户姓名
    tryFromJson(json_doc["data"], "sale_list_name", sale_list_name);

    QVariant sale_list_phone; // 客户电话
    tryFromJson(json_doc["data"], "sale_list_phone", sale_list_phone);

    QVariant sale_list_datetime; // 下单时间
    tryFromJson(json_doc["data"], "sale_list_datetime", sale_list_datetime);

    QVariant sale_list_total; // 实付金额
    tryFromJson(json_doc["data"], "sale_list_total", sale_list_total);

    QString ret_list_total_money = QString::number(getDataFromJson(json_doc["data"], "ret_list_total_money").toDouble(), 'f', 2);

    QString sale_list_total_actually = QString::number(sale_list_total.toDouble() - ret_list_total_money.toDouble(), 'f', 2);

    int font_size = 24;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name_);
    printer.setResolution(mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setCopyCount(1);
    printer.setPageSize(QPageSize(QPageSize::Custom));
    printer.setPageMargins(QMarginsF());


    QSize canvas_size = QSize(paper_width_mm * dpi_mm, paper_height_mm * dpi_mm);

    int cur_v_pos    = 0;
    int left_margin  = 3 * dpi_mm;
    int right_margin = left_margin;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    int price_h_pos_r = 0;

    cur_v_pos += 10; // 上边距

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);

    {
        printItemInfo item_info;
        item_info.text = shop_name; // + "　查询单  ";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    auto ret_count = .0;

    if (json_doc["data"].contains("listDetail") && json_doc["data"]["listDetail"].is_array())
    {
        for (auto detail_item : json_doc["data"]["listDetail"])
        {
            QVariant var_tmp;
            tryFromJson(detail_item, "retCount", var_tmp);

            ret_count += var_tmp.toDouble();
        }
    }

    if (ret_count > 0)
    {
        printItemInfo item_info;

        item_info.text = QString(tr("品名　　　单价　 数量")) + tr("　 金额");

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
        //        cur_v_pos += qrect.height();

        auto  h_advance = painter.fontMetrics().horizontalAdvance(tr("品名　　　单价　 数量"));
        QRect qrect2    = QRect(left_margin + h_advance, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());

        auto tmp_font = QFont("黑体");
        tmp_font.setPixelSize(16);
        painter.setFont(tmp_font);
        painter.drawText(qrect2, item_info.alignment_flag, tr("(退)"));

        cur_v_pos += qrect.height();
    }
    else
    {
        printItemInfo item_info;
        item_info.text = tr("品名　　　单价　 数量　 金额");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 商品详情
    {
        for (const auto &list_item : json_doc["data"]["listDetail"])
        {
            QVariant goods_name; // 商品名
            tryFromJson(list_item, "goods_name", goods_name);

            QVariant sale_list_detail_price; // 售价 (单价)
            tryFromJson(list_item, "sale_list_detail_price", sale_list_detail_price);

            QVariant sale_list_detail_count; // 数量
            tryFromJson(list_item, "sale_list_detail_count", sale_list_detail_count);

            QVariant sale_list_return_count; // 退款数量
            tryFromJson(list_item, "retCount", sale_list_return_count);

            QVariant sub_total; // 小计
            tryFromJson(list_item, "sub_total", sub_total);

            int max_height = 0;
            int name_width = 0;
            {
                printItemInfo item_info;
                item_info.text = goods_name.toString();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - left_margin - right_margin, item_info.getLineHeight());

                QFontMetrics metrics(item_info.font);
                QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
                painter.drawText(qrect, item_info.alignment_flag, elidedText);

                name_width = item_info.getLineWidth();

                QFontMetrics fm = painter.fontMetrics();
                name_width      = fm.width(item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();

                if (name_width > 110)
                    cur_v_pos += item_info.getLineHeight();
            }

            float cur_h_pos = 0;
            cur_h_pos += 18 * dpi_mm;

            float cur_goods_amount_pos = 0;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(sale_list_detail_price.toDouble());
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10.5 * dpi_mm;
            cur_goods_amount_pos = cur_h_pos;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(sale_list_detail_count.toDouble());
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(sub_total.toDouble());
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_v_pos += max_height;

            printItemInfo item_info;
            item_info.text = QString::number(sale_list_detail_count.toDouble(), 'f', 3);
            item_info.font.setPixelSize(font_size);
            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
            painter.setFont(item_info.font);
            if (sale_list_return_count.toDouble() > 0)
            {
                auto qrect2 = QRectF(cur_goods_amount_pos, cur_v_pos - 2 * dpi_mm, canvas_size.width() - right_margin, item_info.getLineHeight());

                auto tmp_font = QFont("黑体");
                tmp_font.setPixelSize(16);
                painter.setFont(tmp_font);
                painter.drawText(qrect2, item_info.alignment_flag, QString(tr("(退")) + QString::number(sale_list_return_count.toDouble(), 'f', 3) + ")");
                cur_v_pos += qrect2.height();
            }
        }
    }

    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("付款金额：　　") + Utils::String::getClearNumStr(sale_list_total.toDouble()) + tr("元");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    if (ret_list_total_money.toDouble() > 0)
    {
        {
            printItemInfo item_info;
            item_info.text = tr("退款金额：　　") + Utils::String::getClearNumStr(ret_list_total_money.toDouble()) + tr("元");
            item_info.font.setPixelSize(font_size);
            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
            painter.setFont(item_info.font);

            QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
            painter.drawText(qrect, item_info.alignment_flag, item_info.text);

            cur_v_pos += qrect.height();
        }

        {
            printItemInfo item_info;
            item_info.text = tr("实付金额：　　") + sale_list_total_actually + tr("元");
            item_info.font.setPixelSize(font_size);
            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
            painter.setFont(item_info.font);

            QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
            painter.drawText(qrect, item_info.alignment_flag, item_info.text);

            cur_v_pos += qrect.height();
        }
    }

    {
        printItemInfo item_info;
        // item_info.text = "订单时间：　　" + sale_list_datetime.toString();
        item_info.text = sale_list_datetime.toString();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
        // item_info.font.setWordSpacing(-50);
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 条码
    {
        QImage img = Text2BarcodeImg(QString("DH") + bar_code.toString());
        img        = img.scaled(contain_w, 8 * dpi_mm);

        //        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, 8 * dpi_mm);
        QRect qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.drawPixmap(qrect, QPixmap::fromImage(img));

        cur_v_pos += qrect.height();

        printItemInfo item_info;

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft;

        qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.setFont(item_info.font);
        painter.drawText(qrect, item_info.alignment_flag, QString("DH") + bar_code.toString());
    }

    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignBottom;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    painter.end();
}
void PrinterControl::printPendingOrder(ShopCartList *shop_cart_list)
{
    if (shop_cart_list == nullptr)
        return;

    if (!isPrinterValid(ticket_printer_name_))
        return;

    float dpi_mm = 8;

    float   paper_width_mm  = 54;
    float   paper_height_mm = 800;
    QString bar_code        = QString("DH") + shop_cart_list->shop_cart_list_data_.order_unique_;
    int     font_size       = 24;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name_);
    printer.setResolution(mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setPageMargins(QMarginsF());
    printer.setPageSize(QPageSize(QPageSize::Custom));

    QSize canvas_size = QSize(paper_width_mm * dpi_mm, paper_height_mm * dpi_mm);

    int cur_v_pos = 0;
    //    int left_margin = 0;
    int left_margin  = 3 * dpi_mm;
    int right_margin = left_margin;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    int price_h_pos_r = 0;

    cur_v_pos += 10; // 上边距
    int right_padding = 40;

    QPixmap pixmap(canvas_size);
    pixmap.fill(QColor(255, 255, 255));

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);
    {
        printItemInfo item_info;
        item_info.text = "** "+shop_name + " **";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("挂单");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("收银机：") + QString("1");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("收银员：") + ControlManager::getInstance()->getShopControl()->getPersonInfo().name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("品名　　　数量　 单价　 合计");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    // 商品详情
    {
        for (ShopCartItem &shop_cart_item : shop_cart_list->items())
        {
            GoodsInfo goods_info;
            goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);

            int max_height = 0;
            int name_width = 0;
            {
                printItemInfo item_info;
                //item_info.text = goods_info.goods_name;
                item_info.text = shop_cart_item.getGoodsName();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - left_margin - right_margin, item_info.getLineHeight());

                QFontMetrics metrics(item_info.font);
                QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
                painter.drawText(qrect, item_info.alignment_flag, elidedText);

                name_width = item_info.getLineWidth();

                QFontMetrics fm = painter.fontMetrics();
                name_width      = fm.width(item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();

                if (name_width > 110)
                    cur_v_pos += item_info.getLineHeight();
            }

            float cur_h_pos = 0;
            cur_h_pos += 18 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = "X"+Utils::String::getClearNumStr(shop_cart_item.goods_num_, 3);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getPrice()); // QString::number(shop_cart_item.getPrice(), 'f', 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getSubtotal());

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }
            cur_v_pos += max_height;
        }
    }
//    {
//        for (ShopCartItem &shop_cart_item : shop_cart_list->items())
//        {
//            GoodsInfo goods_info;
//            goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);

//            int max_height = 0;
//            int name_width = 0;
//            {
//                printItemInfo item_info;
//                item_info.text = shop_cart_item.getGoodsName();
//                //item_info.text = goods_info.goods_name;
//                item_info.font.setPixelSize(font_size);
//                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//                painter.setFont(item_info.font);

//                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - left_margin - right_margin, item_info.getLineHeight());

//                QFontMetrics metrics(item_info.font);
//                QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
//                painter.drawText(qrect, item_info.alignment_flag, elidedText);

//                name_width = item_info.getLineWidth();

//                QFontMetrics fm = painter.fontMetrics();
//                name_width      = fm.width(item_info.text);

//                if (max_height < qrect.height())
//                    max_height = qrect.height();

//                if (name_width > 110)
//                    cur_v_pos += item_info.getLineHeight();
//            }

//            float cur_h_pos = 0;
//            cur_h_pos += 18 * dpi_mm;

//            {
//                printItemInfo item_info;
//                //item_info.text = QString::number(shop_cart_item.getPrice(), 'f', 2);
//                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getPrice());
//                item_info.font.setPixelSize(font_size);
//                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//                painter.setFont(item_info.font);

//                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
//                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//                if (max_height < qrect.height())
//                    max_height = qrect.height();
//            }

//            cur_h_pos += 10.5 * dpi_mm;
//            {
//                printItemInfo item_info;
//                item_info.text = Utils::String::getClearNumStr(shop_cart_item.goods_num_, 3);
//                item_info.font.setPixelSize(font_size);
//                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//                painter.setFont(item_info.font);

//                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
//                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//                if (max_height < qrect.height())
//                    max_height = qrect.height();
//            }

//            cur_h_pos += 10.5 * dpi_mm;
//            {
//                printItemInfo item_info;
//                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getSubtotal());
//                item_info.font.setPixelSize(font_size);
//                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//                painter.setFont(item_info.font);

//                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
//                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//                if (max_height < qrect.height())
//                    max_height = qrect.height();
//            }

//            cur_v_pos += max_height;
//        }
//    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    //合计
    {
        printItemInfo item_info;
//        item_info.text = tr("合计：　　") + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsQuantity().toDouble()) +"                       "
//                +Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount());

        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("合计：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

//        QString point_text1 = "X" + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsQuantity().toDouble());
//        item_info.text = point_text1;
//        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
//        int point_width1 = fontMetrics.horizontalAdvance(point_text1);
//        int right_padding1 = 220;
//        double value_x_pos1 = canvas_size.width() - right_margin - right_padding1 - point_width1;
//        auto qrect_value1 = QRectF(value_x_pos1, cur_v_pos, point_width1, item_info.getLineHeight());
//        painter.drawText(qrect_value1, item_info.alignment_flag, item_info.text);

        int start_x_for_quantity = qrect_label.right();
        int right_padding_for_quantity = 50;
        QString point_text1 = "X" + Utils::String::getClearNumStr(shop_cart_list->orderQuantity());
        item_info.text = point_text1;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int point_width1 = fontMetrics.horizontalAdvance(point_text1);
        auto qrect_value1 = QRectF(start_x_for_quantity + right_padding_for_quantity, cur_v_pos, point_width1, item_info.getLineHeight());
        painter.drawText(qrect_value1, item_info.alignment_flag, item_info.text);

        QString point_text = Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPrice());
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
//    {
//        printItemInfo item_info;
//        item_info.text = "********************************************************";
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }
    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("应付：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        QString point_text = Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPrice());
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();
    }
    QString totalDiscount = Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount() - shop_cart_list->shop_cart_list_data_.cash_received_);
    {
        printItemInfo item_info;

        auto discount = shop_cart_list->getTotalGoodsPrice() - shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment) -
            shop_cart_list->shop_cart_list_data_.recharge_change;

        if (discount < 0)
            discount = 0;
        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("合计优惠：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        QString point_text = Utils::String::getClearNumStr(discount);
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();

    }
//    {
//        printItemInfo item_info;
//        item_info.text = tr("应付金额：　　") + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPrice()) + tr("元");
//        LOG_EVT_INFO("========================应付金额1============================：{}",(Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPrice())).toStdString());
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }

//    {
//        printItemInfo item_info;

//        auto discount = shop_cart_list->getTotalGoodsPrice() - shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment) -
//            shop_cart_list->shop_cart_list_data_.recharge_change;

//        if (discount < 0)
//            discount = 0;

//        item_info.text = tr("优惠：　　　　") + Utils::String::getClearNumStr(discount) + tr("元");
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
//        cur_v_pos += qrect.height();
//    }

//    {
//        printItemInfo item_info;
//        item_info.text = tr("交易件数：　　") + Utils::String::getClearNumStr(shop_cart_list->orderQuantity());
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }

//    {
//        printItemInfo item_info;
//        item_info.text = tr("收银员：　　　") + ControlManager::getInstance()->getShopControl()->getPersonInfo().name;
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }

//    {
//        printItemInfo item_info;
//        item_info.text = tr("收款机：　　　") + QString("1");
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
    {
        printItemInfo item_info;
//        item_info.text = tr("会员姓名：　　") + shop_cart_list->shop_cart_list_data_.member_name;
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();

        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("会员：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        QString point_text = shop_cart_list->shop_cart_list_data_.member_name;
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();
    }
    if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    // 订单时间
    {
        printItemInfo item_info;
        item_info.text = shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("联系电话:") + ControlManager::getInstance()->getShopControl()->getPersonInfo().phone;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    //{
    //    printItemInfo item_info;
    //    item_info.text = "温馨提示:如需发票请联系前台";
    //    item_info.font.setPixelSize(font_size);
    //    item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
    //    painter.setFont(item_info.font);

    //    QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
    //    painter.drawText(qrect, item_info.alignment_flag, item_info.text);

    //    cur_v_pos += qrect.height();
    //}

    painter.end();
}
void PrinterControl::printRefundNetOrder(QVariant json_data)
{
    if(ConfigTool::getInstance()->getSetting(ConfigEnum::IS_REFUND_ORDER_AND_PRINT) == false){
        return;
    }
    if (!isPrinterValid(online_printer_name_))
        return;
    //测试 退款语音
    if(ConfigTool::getInstance()->isPlayMiniProgramRefund()){
        ControlManager::getInstance()->getTtsControl()->say("您有退款单请及时处理");
    }
    Json json_doc = Json::parse(json_data.toString().toStdString());
    float dpi_mm = 8;
    float    paper_width_mm  = 54;
    float    paper_height_mm = 800;
    QVariant bar_code; // 网单退单条码
    tryFromJson(json_doc["data"], "saleListUnique", bar_code);
    QVariant sale_list_address; // 送货地址
    tryFromJson(json_doc["data"], "saleListAddress", sale_list_address);
    QVariant sale_list_phone; // 电话
    tryFromJson(json_doc["data"], "saleListPhone", sale_list_phone);
    QVariant sale_list_name; // 姓名 暂时没有
    tryFromJson(json_doc["data"], "saleListName", sale_list_name);

    QVariant ret_List_Datetime; // 退单时间
    tryFromJson(json_doc["data"], "retListDatetime", ret_List_Datetime);

    QVariant sale_list_total; // 实付金额
    tryFromJson(json_doc["data"], "retListTotal", sale_list_total);


    int font_size = 24;

    QPrinter printer;
    printer.setPrinterName(online_printer_name_);
    printer.setResolution(mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setCopyCount(online_printer_num_);

    QSize canvas_size = QSize(paper_width_mm * dpi_mm, paper_height_mm * dpi_mm);

    int cur_v_pos = 0;
    //    int left_margin = 0;
    int left_margin  = 3 * dpi_mm;
    int right_margin = left_margin;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    int price_h_pos_r = 0;

    cur_v_pos += 10; // 上边距

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);
    {
        printItemInfo item_info;
        item_info.text = shop_name + " 退款单";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
        cur_v_pos += qrect.height();
    }


    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "品名　　　单价　 数量　 金额";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    // 商品详情
    {
        for (const auto &list_item : json_doc["data"]["detailList"])
        {

            QVariant goods_name; // 商品名
            tryFromJson(list_item, "goodsName", goods_name);

            QVariant sale_list_detail_price; // 售价 (单价)
            tryFromJson(list_item, "retListDetailPrice", sale_list_detail_price);

            QVariant sale_list_detail_count; // 数量
            tryFromJson(list_item, "retListDetailCount", sale_list_detail_count);

            QVariant sub_total; // 小计 暂时没有
            sub_total = sale_list_detail_count.toDouble(0) * sale_list_detail_price.toDouble(0);
            //tryFromJson(list_item, "sub_total", sub_total);


            int max_height = 0;
            int name_width = 0;
            {
                printItemInfo item_info;
                item_info.text = goods_name.toString();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - left_margin - right_margin, item_info.getLineHeight());

                QFontMetrics metrics(item_info.font);
                QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
                painter.drawText(qrect, item_info.alignment_flag, elidedText);

                name_width = item_info.getLineWidth();

                QFontMetrics fm = painter.fontMetrics();
                name_width      = fm.width(item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();

                if (name_width > 110)
                    cur_v_pos += item_info.getLineHeight();
            }

            float cur_h_pos = 0;
            cur_h_pos += 20 * dpi_mm;

            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(sale_list_detail_price.toDouble());
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10 * dpi_mm;
            {
                printItemInfo item_info;
                //item_info.text = sale_list_detail_count.toString();
                item_info.text = Utils::String::getClearNumStr(sale_list_detail_count.toDouble());
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = sub_total.toString();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_v_pos += max_height;
        }
    }

    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "实付金额: " + Utils::String::getClearNumStr(sale_list_total.toDouble()) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = ret_List_Datetime.toString();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        // item_info.font.setWordSpacing(-50);
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "姓名: " + sale_list_name.toString();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "联系电话: " + sale_list_phone.toString();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
        cur_v_pos += qrect.height();
    }

    // 条码
    {
        QImage img = Text2BarcodeImg(QString("WD") + bar_code.toString());
        img        = img.scaled(contain_w, 8 * dpi_mm);

        //        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, 8 * dpi_mm);
        QRect qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.drawPixmap(qrect, QPixmap::fromImage(img));
        cur_v_pos += qrect.height();
        printItemInfo item_info;

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft;

        qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 3 * dpi_mm);
        cur_v_pos += qrect.height();
        painter.setFont(item_info.font);
        painter.drawText(qrect, item_info.alignment_flag, QString("WD") + bar_code.toString());
    }
    painter.end();
}
void PrinterControl::printNetOrder(QVariant json_data)
{
    LOG_EVT_INFO("online_printer_name_:{}",online_printer_name_.toStdString());

    if (!isPrinterValid(online_printer_name_))
    {
        LOG_EVT_INFO("网单打印机不可用");
        return;
    }
    Json json_doc = Json::parse(json_data.toString().toStdString());
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "打印数据{}",json_doc.dump());
    if (!json_doc.contains("status") || !json_doc.contains("data") || !json_doc["data"].contains("sale_list_unique") ||
        !json_doc["data"].contains("sale_list_state") || !json_doc["data"].contains("sale_list_name") || !json_doc["data"].contains("sale_list_phone") ||
        !json_doc["data"].contains("sale_list_datetime") || !json_doc["data"].contains("payDetail"))
    {
        return;
    }

    float dpi_mm = 8;

    float    paper_width_mm  = 54;
    float    paper_height_mm = 800;
    QVariant bar_code; // 条码
    tryFromJson(json_doc["data"], "sale_list_unique", bar_code);

    QVariant sale_list_address; // 送货地址
    tryFromJson(json_doc["data"], "sale_list_address", sale_list_address);

    QVariant sale_list_phone; // 电话
    tryFromJson(json_doc["data"], "sale_list_phone", sale_list_phone);

    QVariant sale_list_state; // 支付状态
    tryFromJson(json_doc["data"], "sale_list_state", sale_list_state);

    QVariant sale_list_name; // 姓名
    tryFromJson(json_doc["data"], "sale_list_name", sale_list_name);

    QVariant sale_list_datetime; // 下单时间
    tryFromJson(json_doc["data"], "sale_list_datetime", sale_list_datetime);

    QVariant sale_list_total; // 实付金额
    tryFromJson(json_doc["data"], "sale_list_total", sale_list_total);

    QVariant delivery_type; // 配送类型
    tryFromJson(json_doc["data"], "delivery_type", delivery_type);

    QVariant subDelfee; // 配送费用
    tryFromJson(json_doc["data"], "subDelfee", subDelfee);

    int font_size = 24;

    QPrinter printer;
    printer.setPrinterName(online_printer_name_);
    printer.setResolution(mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setCopyCount(online_printer_num_);

    QSize canvas_size = QSize(paper_width_mm * dpi_mm, paper_height_mm * dpi_mm);

    int cur_v_pos = 0;
    //    int left_margin = 0;
    int left_margin  = 3 * dpi_mm;
    int right_margin = left_margin;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    int price_h_pos_r = 0;

    cur_v_pos += 10; // 上边距

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();
    QPainter painter(&printer);
    {
        printItemInfo item_info;
        item_info.text = "** "+shop_name + " **";
        item_info.font.setPixelSize(font_size + 4);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);
        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = "网单";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
//    QPainter painter(&printer);
//    {
//        printItemInfo item_info;
//        item_info.text = shop_name + " 网单";
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }
//    {
//        printItemInfo item_info;
//        item_info.text = "********************************************************";
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
//    {
//        printItemInfo item_info;
//        item_info.text = "品名　　　单价　 数量　 金额";
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }
    {
        printItemInfo item_info;
        item_info.text = tr("品名　　　数量　 单价　 合计");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    // 商品详情
    {
        for (const auto &list_item : json_doc["data"]["listDetail"])
        {

            QVariant goods_name; // 商品名
            tryFromJson(list_item, "goods_name", goods_name);

            QVariant sale_list_detail_price; // 售价 (单价)
            tryFromJson(list_item, "sale_list_detail_price", sale_list_detail_price);

            QVariant sale_list_detail_count; // 数量
            tryFromJson(list_item, "sale_list_detail_count", sale_list_detail_count);

            QVariant sub_total; // 小计
            tryFromJson(list_item, "sub_total", sub_total);


            int max_height = 0;
            int name_width = 0;
            {
                printItemInfo item_info;
                item_info.text = goods_name.toString();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - left_margin - right_margin, item_info.getLineHeight());

                QFontMetrics metrics(item_info.font);
                QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
                painter.drawText(qrect, item_info.alignment_flag, elidedText);

                name_width = item_info.getLineWidth();

                QFontMetrics fm = painter.fontMetrics();
                name_width      = fm.width(item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();

                if (name_width > 110)
                    cur_v_pos += item_info.getLineHeight();
            }

            float cur_h_pos = 0;
            cur_h_pos += 20 * dpi_mm;

            {
                printItemInfo item_info;
                //item_info.text = Utils::String::getClearNumStr(sale_list_detail_price.toDouble());
                item_info.text = Utils::String::getClearNumStr(sale_list_detail_count.toDouble());
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10 * dpi_mm;
            {
                printItemInfo item_info;
                //item_info.text = sale_list_detail_count.toString();
                item_info.text = Utils::String::getClearNumStr(sale_list_detail_price.toDouble());
                //item_info.text = Utils::String::getClearNumStr(sale_list_detail_count.toDouble());
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = sub_total.toString();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_v_pos += max_height;
        }
    }

    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "实付金额: " + Utils::String::getClearNumStr(sale_list_total.toDouble()) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    if(delivery_type == 2){

        {
            printItemInfo item_info;
            item_info.text = "配送费: " + Utils::String::getClearNumStr(subDelfee.toDouble()) + "元";
            item_info.font.setPixelSize(font_size);
            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
            painter.setFont(item_info.font);

            QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
            painter.drawText(qrect, item_info.alignment_flag, item_info.text);

            cur_v_pos += qrect.height();
        }
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = "姓名: " + sale_list_name.toString();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "联系电话: " + sale_list_phone.toString();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        // item_info.text = "订单时间: " + sale_list_datetime.toString();
        item_info.text = sale_list_datetime.toString();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        // item_info.font.setWordSpacing(-50);
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    // 条码
    {
        QImage img = Text2BarcodeImg(QString("WD") + bar_code.toString());
        img        = img.scaled(contain_w, 8 * dpi_mm);

        //        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, 8 * dpi_mm);
        QRect qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);
        painter.drawPixmap(qrect, QPixmap::fromImage(img));
        cur_v_pos += qrect.height();
        printItemInfo item_info;

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft;

        qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 3 * dpi_mm);
        cur_v_pos += qrect.height();
        painter.setFont(item_info.font);
        painter.drawText(qrect, item_info.alignment_flag, QString("WD") + bar_code.toString());
        LOG_EVT_INFO("DFDSDFASFDSF33333");
    }
    painter.end();
    LOG_EVT_INFO("DFDSDFASFDSF");
}
bool PrinterControl::printHandoverInfo(QVariant json_data_str, int print_num)
{
    Json json_doc = Json::parse(json_data_str.toString().toStdString());
    if (!json_doc.contains("address") || !json_doc.contains("data") || !json_doc.contains("data1"))
        return false;

    if (!json_doc["address"].is_array() || !json_doc["data"].is_object() || !json_doc["data1"].is_array())
        return false;

    QString account = ControlManager::getInstance()->getShopControl()->getLoginAccount(); // 账号
    QString name    = ControlManager::getInstance()->getShopControl()->getShopName();     // 姓名

    int    order_quantity   = 0;                                                          // 订单量 -
    double total_receivable = .0;                                                         // 应收 -
    double total_received   = .0;                                                         // 实收 -

    double cash_income        = .0;                                                       // 现金
    double wechat_income      = .0;                                                       // 微信
    double alipay_income      = .0;                                                       // 支付宝
    double stored_card_income = .0;                                                       // 储值卡
    double online_income      = .0;                                                       // 线上收入
    double beans_deduct       = .0;                                                       // 百货豆折扣
    double jinquan_income     = .0;                                                       // 金圈平台收入
    double recharge_income    = .0;                                                       // 充值收入

    QString shift_start_time;                                                             // 接班时间 -
    QString shift_end_time;                                                               // 交班时间 -


    auto json_data = json_doc["data"];

    QVariant var_tmp;
    tryFromJson(json_data, "sum", var_tmp);
    order_quantity = var_tmp.toInt();

    tryFromJson(json_data, "total", var_tmp);
    total_receivable = var_tmp.toDouble();

    tryFromJson(json_data, "receivable", var_tmp);
    total_received = var_tmp.toDouble();

    tryFromJson(json_data, "login_datetime", var_tmp);
    shift_start_time = var_tmp.toString();

    tryFromJson(json_data, "endTime", var_tmp);
    shift_end_time = var_tmp.toString();


    for (auto &cur_item : json_doc["address"])
    {
        QVariant var_tmp;
        tryFromJson(cur_item, "sale_list_actually_received", var_tmp);
        recharge_income += var_tmp.toDouble();
    }

    for (auto &cur_item : json_doc["data1"])
    {
        QVariant var_tmp;
        tryFromJson(cur_item, "pay_ment", var_tmp);
        QVariant var_tmp2;
        tryFromJson(cur_item, "payment_total", var_tmp2);
        auto payment_str = var_tmp.toString();
        if (payment_str == "现金")
        {
            cash_income = var_tmp2.toDouble();
        }
        else if (payment_str == "微信")
        {
            wechat_income = var_tmp2.toDouble();
        }
        else if (payment_str == "支付宝")
        {
            alipay_income = var_tmp2.toDouble();
        }
        else if (payment_str == "储值卡")
        {
            stored_card_income = var_tmp2.toDouble();
        }
        else if (payment_str == "百货豆抵扣数量")
        {
            beans_deduct = var_tmp2.toDouble();
        }
        else if (payment_str == "金圈平台")
        {
            jinquan_income = var_tmp2.toDouble();
        }
    }

    auto shop_cart_list = ControlManager::getInstance()->getShopCartList();

    float dpi_mm = 8;

    float   paper_width_mm  = 54;
    float   paper_height_mm = 800;
    QString bar_code        = QString("DH") + shop_cart_list->shop_cart_list_data_.order_unique_;
    int     font_size       = 23;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name_);
    printer.setResolution(mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setPageMargins(QMarginsF());
    printer.setPageSize(QPageSize(QPageSize::Custom));

    if (print_num > 0)
        printer.setCopyCount(print_num);
    else
        printer.setCopyCount(ticket_printer_num_);

    QSize canvas_size = QSize(paper_width_mm * dpi_mm, paper_height_mm * dpi_mm);

    int cur_v_pos = 0;
    //    int left_margin = 0;
    int left_margin  = 3 * dpi_mm;
    int right_margin = left_margin;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    cur_v_pos += 10; // 上边距

    QPixmap pixmap(canvas_size);
    pixmap.fill(QColor(255, 255, 255));

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);
    {
        printItemInfo item_info;
        item_info.text = shop_name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "登录账号: " + account;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "姓名: " + name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "订单量: " + Utils::String::getClearNumStr(order_quantity);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "应收: " + Utils::String::getClearNumStr(total_receivable);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "实收: " + Utils::String::getClearNumStr(total_received);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "现金: " + Utils::String::getClearNumStr(cash_income);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "微信: " + Utils::String::getClearNumStr(wechat_income);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "支付宝: " + Utils::String::getClearNumStr(alipay_income);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "储值卡: " + Utils::String::getClearNumStr(stored_card_income);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "线上收入: " + Utils::String::getClearNumStr(online_income);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "百货豆抵扣: " + Utils::String::getClearNumStr(beans_deduct);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "金圈平台收入: " + Utils::String::getClearNumStr(jinquan_income);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "充值收入: " + Utils::String::getClearNumStr(recharge_income);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "接班时间: " + shift_start_time;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "交班时间: " + shift_end_time;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    painter.end();

    return true;
}
bool PrinterControl::printNingyuHandoverInfo(QVariant json_data_str, int print_num)
{
    if (!isPrinterValid(ticket_printer_name_))
        return false;

    Json json_doc = Json::parse(json_data_str.toString().toStdString());
    if (!json_doc.contains("address") || !json_doc.contains("data") || !json_doc.contains("data1"))
        return false;

    if (!json_doc["address"].is_array() || !json_doc["data"].is_object() || !json_doc["data1"].is_array())
        return false;

    QString account = ControlManager::getInstance()->getShopControl()->getLoginAccount(); // 账号
    QString name    = ControlManager::getInstance()->getShopControl()->getShopName();     // 姓名

    int    order_quantity   = 0;  // 订单量 -
    double total_receivable = .0; // 应收 -
    double total_received   = .0; // 实收 -

    double cash_income         = .0; // 现金
    double wechat_income       = .0; // 微信
    double alipay_income       = .0; // 支付宝
    double stored_card_income  = .0; // 储值卡
    double online_income       = .0; // 线上收入
    double beans_deduct        = .0; // 百货豆折扣
    double jinquan_income      = .0; // 金圈平台收入
    double recharge_income     = .0; // 充值收入
    double mini_program_income = .0; // 小程序
    double bank_card_income    = .0; //银行卡
    double balance_income      = .0; //本金金额
    double rebute_income       = .0; //赠送金额

    QString shift_start_time; // 接班时间 -
    QString shift_end_time;   // 交班时间 -


    auto json_data = json_doc["data"];

    QVariant var_tmp;
    tryFromJson(json_data, "sum", var_tmp);
    order_quantity = var_tmp.toInt();

    tryFromJson(json_data, "total", var_tmp);
    total_receivable = var_tmp.toDouble();

    tryFromJson(json_data, "receivable", var_tmp);
    total_received = var_tmp.toDouble();

    tryFromJson(json_data, "login_datetime", var_tmp);
    shift_start_time = var_tmp.toString();

    tryFromJson(json_data, "endTime", var_tmp);
    shift_end_time = var_tmp.toString();


    for (auto &cur_item : json_doc["address"])
    {
        QVariant var_tmp;
        tryFromJson(cur_item, "sale_list_actually_received", var_tmp);
        recharge_income += var_tmp.toDouble();
    }

    for (auto &cur_item : json_doc["data1"])
    {
        QVariant var_tmp;
        tryFromJson(cur_item, "pay_ment", var_tmp);
        QVariant var_tmp2;
        tryFromJson(cur_item, "payment_total", var_tmp2);
        auto payment_str = var_tmp.toString();
        if (payment_str == "现金")
        {
            cash_income = var_tmp2.toDouble();
        }
        else if (payment_str == "微信")
        {
            wechat_income = var_tmp2.toDouble();
        }
        else if (payment_str == "支付宝")
        {
            alipay_income = var_tmp2.toDouble();
        }
        else if (payment_str == "储值卡")
        {
            stored_card_income = var_tmp2.toDouble();
        }
        else if (payment_str == "百货豆抵扣数量")
        {
            beans_deduct = var_tmp2.toDouble();
        }
        else if (payment_str == "金圈平台")
        {
            jinquan_income = var_tmp2.toDouble();
        }
        else if (payment_str == "网上收入")
        {
            mini_program_income = var_tmp2.toDouble();
        }
        else if (payment_str == "银行卡")
        {
            bank_card_income = var_tmp2.toDouble();
        }
        else if (payment_str == "本金金额")
        {
            balance_income = var_tmp2.toDouble();
        }
        else if (payment_str == "赠送金额")
        {
            rebute_income = var_tmp2.toDouble();
        }
    }

    auto shop_cart_list = ControlManager::getInstance()->getShopCartList();

    float dpi_mm = 8;

    float   paper_width_mm  = 54;
    float   paper_height_mm = 800;
    QString bar_code        = QString("DH") + shop_cart_list->shop_cart_list_data_.order_unique_;
    int     font_size       = 23;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name_);
    printer.setResolution(mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setPageMargins(QMarginsF());
    printer.setPageSize(QPageSize(QPageSize::Custom));

    if (print_num > 0)
        printer.setCopyCount(print_num);
    else
        printer.setCopyCount(ticket_printer_num_);

    QSize canvas_size = QSize(paper_width_mm * dpi_mm, paper_height_mm * dpi_mm);

    int cur_v_pos = 0;
    //    int left_margin = 0;
    int left_margin  = 3 * dpi_mm;
    int left_margin1  = 10 * dpi_mm;
    int right_margin = left_margin;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    cur_v_pos += 10; // 上边距

    QPixmap pixmap(canvas_size);
    pixmap.fill(QColor(255, 255, 255));

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);

    {
        printItemInfo item_info;
        item_info.text = tr("登录账号: ") + account;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("姓名: ") + name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("订单量: ") + Utils::String::getClearNumStr(order_quantity);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("应收: ") + Utils::String::getClearNumStr(total_receivable) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("实收: ") + Utils::String::getClearNumStr(total_received) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("现金: ") + Utils::String::getClearNumStr(cash_income) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("微信: ") + Utils::String::getClearNumStr(wechat_income) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("支付宝: ") + Utils::String::getClearNumStr(alipay_income) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("银行卡: ") + Utils::String::getClearNumStr(bank_card_income) + "元" ;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("储值卡: ") + Utils::String::getClearNumStr(stored_card_income) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("本金金额: ") + Utils::String::getClearNumStr(balance_income) + "元" ;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("赠送金额: ") + Utils::String::getClearNumStr(rebute_income) + "元" ;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("免密付: ") + Utils::String::getClearNumStr(jinquan_income) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("线上收入: ") + Utils::String::getClearNumStr(mini_program_income) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("百货豆抵扣: ") + Utils::String::getClearNumStr(beans_deduct) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("金圈平台收入: ") + Utils::String::getClearNumStr(jinquan_income) + "元" ;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("充值详情: ") + Utils::String::getClearNumStr(recharge_income) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("类别　品名 　 数量　 小计");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    for (auto &cur_item : json_doc["cus_data"])
    {
        QVariant var_kind_tmp;
        tryFromJson(cur_item, "goods_kind_name", var_kind_tmp);
        QVariant var_totalcount_tmp;
        tryFromJson(cur_item, "totalCount", var_totalcount_tmp);
        QVariant var_totalmoney_tmp;
        tryFromJson(cur_item, "totalMoney", var_totalmoney_tmp);
        {
            int max_height = 0;
            int name_width = 0;
            {
                printItemInfo item_info;
                item_info.text = var_kind_tmp.toString();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - left_margin - right_margin, item_info.getLineHeight());

                QFontMetrics metrics(item_info.font);
                QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
                painter.drawText(qrect, item_info.alignment_flag, elidedText);

                name_width = item_info.getLineWidth();

                QFontMetrics fm = painter.fontMetrics();
                name_width      = fm.width(item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();

                if (name_width > 110)
                    cur_v_pos += item_info.getLineHeight();
            }
            float cur_h_pos = 0;
            cur_h_pos += 25.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(var_totalcount_tmp.toDouble(), 3);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(var_totalmoney_tmp.toDouble());

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_v_pos += max_height;
        }


        if (cur_item.contains("list") ) {
            for ( auto& cur_item : cur_item["list"]) {
                QVariant var_name_tmp;
                tryFromJson(cur_item, "goods_name", var_name_tmp);
                QVariant var_count_tmp;
                tryFromJson(cur_item, "count", var_count_tmp);
                QVariant var_total_tmp;
                tryFromJson(cur_item, "total", var_total_tmp);
                {
                    int max_height = 0;
                    int name_width = 0;
                    {
                        printItemInfo item_info;
                        item_info.text = var_name_tmp.toString();
                        item_info.font.setPixelSize(font_size);
                        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                        painter.setFont(item_info.font);

                        auto qrect = QRectF(left_margin1, cur_v_pos, canvas_size.width() - left_margin1 - right_margin, item_info.getLineHeight());

                        QFontMetrics metrics(item_info.font);
                        QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
                        painter.drawText(qrect, item_info.alignment_flag, elidedText);

                        name_width = item_info.getLineWidth();

                        QFontMetrics fm = painter.fontMetrics();
                        name_width      = fm.width(item_info.text);

                        if (max_height < qrect.height())
                            max_height = qrect.height();

                        if (name_width > 110)
                            cur_v_pos += item_info.getLineHeight();
                    }
                    float cur_h_pos = 0;
                    cur_h_pos += 25.5 * dpi_mm;
                    {
                        printItemInfo item_info;
                        item_info.text = Utils::String::getClearNumStr(var_count_tmp.toDouble(), 3);
                        item_info.font.setPixelSize(font_size);
                        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                        painter.setFont(item_info.font);

                        auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                        if (max_height < qrect.height())
                            max_height = qrect.height();
                    }

                    cur_h_pos += 10.5 * dpi_mm;
                    {
                        printItemInfo item_info;
                        item_info.text = Utils::String::getClearNumStr(var_total_tmp.toDouble());

                        item_info.font.setPixelSize(font_size);
                        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                        painter.setFont(item_info.font);

                        auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                        if (max_height < qrect.height())
                            max_height = qrect.height();
                    }

                    cur_v_pos += max_height;
                }
            }
        }

    }






    {
        printItemInfo item_info;
        item_info.text = tr("接班时间: ") + shift_start_time;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("交班时间: ") + shift_end_time;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    painter.end();

    return true;
}

bool PrinterControl::getIsPrintTicket()
{
    return ticket_printer_is_print_;
}

void PrinterControl::setIsPrintTicket(QVariant is_print)
{
    ticket_printer_is_print_ = is_print.toBool();
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::PRINTER_IS_PRINT__TICKET, is_print.toBool());
}

void PrinterControl::openCashBox()
{
    LOG_EVT_INFO("开启钱箱函数======================================");
    ::openCashBox();
}

float PrinterControl::dpi2Mm(float dpi)
{
    return dpi / 25.4;
}

float PrinterControl::mm2Dpi(float mm)
{
    return mm * 25.4;
}

bool PrinterControl::getIsUseFoodTicket()
{
    return is_use_food_ticket_;
}

void PrinterControl::setIsUseFoodTicket(bool is_use)
{
    is_use_food_ticket_ = is_use;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::PRINTER_IS_USE_FOOD_TICKET, is_use_food_ticket_);
    emit sigIsUseFoodTicketChanged();
}

unsigned long PrinterControl::getFoodTicketIndex()
{
    return food_ticket_index_;
}

void PrinterControl::setFoodTicketIndex(unsigned long index)
{
    food_ticket_index_ = index;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::PRINTER_FOOD_TICKET_INDEX, QVariant::fromValue(food_ticket_index_));
    emit sigFoodTicketIndexChanged();
}

void PrinterControl::resetFoodTicketIndex()
{
    setFoodTicketIndex(0);
}

int PrinterControl::curPriceTagType()
{
    return static_cast<int>(cur_price_tag_type_);
}

void PrinterControl::setCurPriceTagType(int tag_type)
{
    cur_price_tag_type_ = static_cast<PriceTagTemplateEnum>(tag_type);
    auto config_tool    = ConfigTool::getInstance();
    config_tool->setSetting(ConfigEnum::TAG_TEMUPLATE_TYPE, tag_type);
    emit curPriceTagTypeChanged();
}

bool PrinterControl::isPrinterValid(QString printer_name)
{
    int ret_printer_index = invalid_printer_list_.indexOf(printer_name);
    if (ret_printer_index != -1)
        return false;
    QPrinter printer;
    printer.setPrinterName(printer_name);
    if (!printer.isValid())
    {
        return false;
    }
    return true;
}

int printItemInfo::getLineHeight()
{
    QFontMetrics metrics(font);
    return metrics.height() + metrics.height() * .2;
}

int printItemInfo::getLineWidth()
{
    QFontMetrics metrics(this->font);
    return metrics.horizontalAdvance(this->text);
}
