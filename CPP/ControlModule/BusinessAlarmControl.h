﻿#ifndef BUSINESSALARMCONTROL_H
#define BUSINESSALARMCONTROL_H
#include <QImage>
#include <QJSEngine>
#include <QJSValue>
#include <QObject>
#include <QTimer>
#include <cstdint>
#include <mutex>

class BusinessAlarmControl : public QObject
{
    Q_OBJECT
private:
    static std::unique_ptr<BusinessAlarmControl> singleton_;
    static std::mutex                  mutex_;
public:
    explicit BusinessAlarmControl(QObject *parent = nullptr);
    ~BusinessAlarmControl() override;
    static BusinessAlarmControl *getInstance();
    //void PayMethodControl::checkCombinedOnlinePay()
    Q_INVOKABLE void    sendDingTalkAlarmMessages( QString requestUrl, QString errorContent, QString cur_url);
};
#endif // BUSINESSALARMCONTROL_H
