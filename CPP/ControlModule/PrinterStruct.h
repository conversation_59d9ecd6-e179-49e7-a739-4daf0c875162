﻿#ifndef PRINTERSTRUCT_H
#define PRINTERSTRUCT_H

#include <QFont>
#include <QRect>
#include "json-qt.hpp"

struct printItemInfo
{
    QString text;
    QRect   rect;
    int     alignment_flag = Qt::AlignLeft;
    QFont   font           = QFont("黑体", 25);
    int     getLineHeight();
    int     getLineWidth();
};

struct PriceTag40x30
{
    float paper_w = 40;
    float paper_h = 30;
    float dpi_mm  = 8;

    float margin_left = 1;
    float margin_top  = 0;

    float goods_name_x    = 2;
    float goods_name_y    = 7;
    float goods_name_size = 22;

    float rmb_symbol_x    = 6;
    float rmb_symbol_y    = 15;
    float rmb_symbol_size = 28;

    float price_x    = 10;
    float price_y    = 15;
    float price_size = 28;

    float barcode_x    = 0;
    float barcode_y    = 17;
    float barcode_w    = 38;
    float barcode_h    = 8;
    float barcode_size = 16;

    bool save2Cfg();

    friend void from_json(const J<PERSON> &j<PERSON>, PriceTag40x30 &obj)
    {
        obj.paper_w         = getDataFromJson(json, "paper_w").toFloat();
        obj.paper_h         = getDataFromJson(json, "paper_h").toFloat();
        obj.dpi_mm          = getDataFromJson(json, "dpi_mm").toFloat();
        obj.margin_left     = getDataFromJson(json, "margin_left").toFloat();
        obj.margin_top      = getDataFromJson(json, "margin_top").toFloat();
        obj.goods_name_x    = getDataFromJson(json, "goods_name_x").toFloat();
        obj.goods_name_y    = getDataFromJson(json, "goods_name_y").toFloat();
        obj.goods_name_size = getDataFromJson(json, "goods_name_size").toFloat();
        obj.rmb_symbol_x    = getDataFromJson(json, "rmb_symbol_x").toFloat();
        obj.rmb_symbol_y    = getDataFromJson(json, "rmb_symbol_y").toFloat();
        obj.rmb_symbol_size = getDataFromJson(json, "rmb_symbol_size").toFloat();
        obj.price_x         = getDataFromJson(json, "price_x").toFloat();
        obj.price_y         = getDataFromJson(json, "price_y").toFloat();
        obj.price_size      = getDataFromJson(json, "price_size").toFloat();
        obj.barcode_x       = getDataFromJson(json, "barcode_x").toFloat();
        obj.barcode_y       = getDataFromJson(json, "barcode_y").toFloat();
        obj.barcode_w       = getDataFromJson(json, "barcode_w").toFloat();
        obj.barcode_h       = getDataFromJson(json, "barcode_h").toFloat();
        obj.barcode_size    = getDataFromJson(json, "barcode_size").toFloat();
    }

    friend void to_json(Json &json, const PriceTag40x30 &obj)
    {
        json["paper_w"]         = obj.paper_w;
        json["paper_h"]         = obj.paper_h;
        json["dpi_mm"]          = obj.dpi_mm;
        json["margin_left"]     = obj.margin_left;
        json["margin_top"]      = obj.margin_top;
        json["goods_name_x"]    = obj.goods_name_x;
        json["goods_name_y"]    = obj.goods_name_y;
        json["goods_name_size"] = obj.goods_name_size;
        json["rmb_symbol_x"]    = obj.rmb_symbol_x;
        json["rmb_symbol_y"]    = obj.rmb_symbol_y;
        json["rmb_symbol_size"] = obj.rmb_symbol_size;
        json["price_x"]         = obj.price_x;
        json["price_y"]         = obj.price_y;
        json["price_size"]      = obj.price_size;
        json["barcode_x"]       = obj.barcode_x;
        json["barcode_y"]       = obj.barcode_y;
        json["barcode_w"]       = obj.barcode_w;
        json["barcode_h"]       = obj.barcode_h;
        json["barcode_size"]    = obj.barcode_size;
    }
};


struct PriceTag95x38
{
    float paper_w = 95;
    float paper_h = 38;
    float dpi_mm  = 8;

    float margin_left = 1;
    float margin_top  = 0;

    float goods_name_x    = 15;
    float goods_name_y    = 7;
    float goods_name_size = 22;

    float rmb_symbol_x    = 5;
    float rmb_symbol_y    = 19;
    float rmb_symbol_size = 50;

    float price_x    = 10;
    float price_y    = 19;
    float price_size = 50;

    float barcode_x    = 1;
    float barcode_y    = 24;
    float barcode_w    = 50;
    float barcode_h    = 7;
    float barcode_size = 16;

    bool save2Cfg();

    friend void from_json(const Json &json, PriceTag95x38 &obj)
    {
        obj.paper_w         = getDataFromJson(json, "paper_w").toFloat();
        obj.paper_h         = getDataFromJson(json, "paper_h").toFloat();
        obj.dpi_mm          = getDataFromJson(json, "dpi_mm").toFloat();
        obj.margin_left     = getDataFromJson(json, "margin_left").toFloat();
        obj.margin_top      = getDataFromJson(json, "margin_top").toFloat();
        obj.goods_name_x    = getDataFromJson(json, "goods_name_x").toFloat();
        obj.goods_name_y    = getDataFromJson(json, "goods_name_y").toFloat();
        obj.goods_name_size = getDataFromJson(json, "goods_name_size").toFloat();
        obj.rmb_symbol_x    = getDataFromJson(json, "rmb_symbol_x").toFloat();
        obj.rmb_symbol_y    = getDataFromJson(json, "rmb_symbol_y").toFloat();
        obj.rmb_symbol_size = getDataFromJson(json, "rmb_symbol_size").toFloat();
        obj.price_x         = getDataFromJson(json, "price_x").toFloat();
        obj.price_y         = getDataFromJson(json, "price_y").toFloat();
        obj.price_size      = getDataFromJson(json, "price_size").toFloat();
        obj.barcode_x       = getDataFromJson(json, "barcode_x").toFloat();
        obj.barcode_y       = getDataFromJson(json, "barcode_y").toFloat();
        obj.barcode_w       = getDataFromJson(json, "barcode_w").toFloat();
        obj.barcode_h       = getDataFromJson(json, "barcode_h").toFloat();
        obj.barcode_size    = getDataFromJson(json, "barcode_size").toFloat();
    }

    friend void to_json(Json &json, const PriceTag95x38 &obj)
    {
        json["paper_w"]         = obj.paper_w;
        json["paper_h"]         = obj.paper_h;
        json["dpi_mm"]          = obj.dpi_mm;
        json["margin_left"]     = obj.margin_left;
        json["margin_top"]      = obj.margin_top;
        json["goods_name_x"]    = obj.goods_name_x;
        json["goods_name_y"]    = obj.goods_name_y;
        json["goods_name_size"] = obj.goods_name_size;
        json["rmb_symbol_x"]    = obj.rmb_symbol_x;
        json["rmb_symbol_y"]    = obj.rmb_symbol_y;
        json["rmb_symbol_size"] = obj.rmb_symbol_size;
        json["price_x"]         = obj.price_x;
        json["price_y"]         = obj.price_y;
        json["price_size"]      = obj.price_size;
        json["barcode_x"]       = obj.barcode_x;
        json["barcode_y"]       = obj.barcode_y;
        json["barcode_w"]       = obj.barcode_w;
        json["barcode_h"]       = obj.barcode_h;
        json["barcode_size"]    = obj.barcode_size;
    }
};


struct PriceTag60x35
{
    float paper_w = 60;
    float paper_h = 35;
    float dpi_mm  = 8;

    float margin_left = 1;
    float margin_top  = 0;

    float shop_name_x    = 6;
    float shop_name_y    = 5;
    float shop_name_size = 15;

    float goods_name_x    = 12;
    float goods_name_y    = 12;
    float goods_name_size = 22;

    float price_x    = 3;
    float price_y    = 29;
    float price_size = 50;

    float spec_x    = 38;
    float spec_y    = 19;
    float spec_size = 22;

    float unit_x    = 38;
    float unit_y    = 23;
    float unit_size = 22;

    float barcode_x    = 33;
    float barcode_y    = 33;
    float barcode_size = 18;

    bool save2Cfg();

    friend void from_json(const Json &json, PriceTag60x35 &obj)
    {
        obj.paper_w     = getDataFromJson(json, "paper_w").toFloat();
        obj.paper_h     = getDataFromJson(json, "paper_h").toFloat();
        obj.dpi_mm      = getDataFromJson(json, "dpi_mm").toFloat();
        obj.margin_left = getDataFromJson(json, "margin_left").toFloat();
        obj.margin_top  = getDataFromJson(json, "margin_top").toFloat();

        obj.shop_name_x    = getDataFromJson(json, "shop_name_x").toFloat();
        obj.shop_name_y    = getDataFromJson(json, "shop_name_y").toFloat();
        obj.shop_name_size = getDataFromJson(json, "shop_name_size").toFloat();

        obj.goods_name_x    = getDataFromJson(json, "goods_name_x").toFloat();
        obj.goods_name_y    = getDataFromJson(json, "goods_name_y").toFloat();
        obj.goods_name_size = getDataFromJson(json, "goods_name_size").toFloat();

        obj.price_x    = getDataFromJson(json, "price_x").toFloat();
        obj.price_y    = getDataFromJson(json, "price_y").toFloat();
        obj.price_size = getDataFromJson(json, "price_size").toFloat();

        obj.spec_x    = getDataFromJson(json, "spec_x").toFloat();
        obj.spec_y    = getDataFromJson(json, "spec_y").toFloat();
        obj.spec_size = getDataFromJson(json, "spec_size").toFloat();

        obj.unit_x    = getDataFromJson(json, "unit_x").toFloat();
        obj.unit_y    = getDataFromJson(json, "unit_y").toFloat();
        obj.unit_size = getDataFromJson(json, "unit_size").toFloat();

        obj.barcode_x    = getDataFromJson(json, "barcode_x").toFloat();
        obj.barcode_y    = getDataFromJson(json, "barcode_y").toFloat();
        obj.barcode_size = getDataFromJson(json, "barcode_size").toFloat();
    }

    friend void to_json(Json &json, const PriceTag60x35 &obj)
    {
        json["paper_w"]     = obj.paper_w;
        json["paper_h"]     = obj.paper_h;
        json["dpi_mm"]      = obj.dpi_mm;
        json["margin_left"] = obj.margin_left;
        json["margin_top"]  = obj.margin_top;

        json["shop_name_x"]    = obj.shop_name_x;
        json["shop_name_y"]    = obj.shop_name_y;
        json["shop_name_size"] = obj.shop_name_size;

        json["goods_name_x"]    = obj.goods_name_x;
        json["goods_name_y"]    = obj.goods_name_y;
        json["goods_name_size"] = obj.goods_name_size;

        json["price_x"]    = obj.price_x;
        json["price_y"]    = obj.price_y;
        json["price_size"] = obj.price_size;

        json["spec_x"]    = obj.spec_x;
        json["spec_y"]    = obj.spec_y;
        json["spec_size"] = obj.spec_size;

        json["unit_x"]    = obj.unit_x;
        json["unit_y"]    = obj.unit_y;
        json["unit_size"] = obj.unit_size;

        json["barcode_x"]    = obj.barcode_x;
        json["barcode_y"]    = obj.barcode_y;
        json["barcode_size"] = obj.barcode_size;
    }
};


#endif // PRINTERSTRUCT_H
