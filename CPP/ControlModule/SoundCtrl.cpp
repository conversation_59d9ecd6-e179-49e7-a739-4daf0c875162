﻿#include "SoundCtrl.h"
#include <QSound>
#include "ConfModule/ConfigTool.h"

SoundCtrl::SoundCtrl(QObject *parent) : QObject{parent}
{
    sound_di_.reset(new QSound(":/Sound/di.wav"));
    sound_click_.reset(new QSound(":/Sound/click.wav"));
}

void SoundCtrl::playDi()
{
    if (!ConfigTool::getInstance()->isPlayPromptSound())
        return;

    sound_di_->play();
}

void SoundCtrl::playClick()
{
    if (!ConfigTool::getInstance()->isPlayKeyboardSound())
        return;

    sound_click_->play();
}
