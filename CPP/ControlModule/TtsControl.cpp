﻿#include "TtsControl.h"
#include "ConfModule/ConfigTool.h"
#include "LogManager.h"
#include "gist/ThreadRAII.h"
#include "json-qt.hpp"

using Json = nlohmann::json;

#ifdef DISABLE_TTS
constexpr bool is_disable_tts = true;
#else
constexpr bool is_disable_tts = false;
#endif

TtsControl::TtsControl(QObject *parent) : QObject{parent}
{
}

bool TtsControl::initTtsControl()
{
    if (speech_ != nullptr)
        return false;

    auto config_tool = ConfigTool::getInstance();
    cur_engine_name_ = config_tool->getSetting(ConfigToolEnum::ConfigEnum::TTS_ENGINE).toString();

    QStringList engines  = QTextToSpeech::availableEngines();
    bool        is_exist = false;
    for (const auto &engine : engines)
    {
        if (engine == cur_engine_name_)
        {
            is_exist = true;
        }
    }

    if (is_exist)
    {
        speech_ = new QTextToSpeech(cur_engine_name_, this);
    }
    else
    {
        speech_ = new QTextToSpeech(this);
    }

    if (!is_disable_tts)
    {
        setCurLocaleByName(config_tool->getSetting(ConfigToolEnum::ConfigEnum::TTS_LOCALE));
        setCurVoiceByName(config_tool->getSetting(ConfigToolEnum::ConfigEnum::TTS_VOICE));
    }

    return true;
}

void TtsControl::say(QString text)
{
#ifdef DISABLE_TTS
    return;
#endif
    // std::thread thread1(
    //    [&]()
    //    {
    speech_->say(text);
    LOG_EVT_INFO("=============================：{}",text.toStdString());
    //    });
    // ThreadRAII thread_raii(thread1);
}

QVariant TtsControl::getAvailableEngines()
{
    if (is_disable_tts)
        return false;

    QStringList engines = QTextToSpeech::availableEngines();
    Json        json_tmp;
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "引擎名列表开始");

    for (auto engine : engines)
    {
        Json tmp = {{"role_name", engine.toStdString()}};
        json_tmp.push_back(tmp);

        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "引擎名 {}", engine.toStdString());
    }

    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "引擎名列表结束");
    return QString::fromStdString(json_tmp.dump());
}

bool TtsControl::setCurEngineByName(QVariant engine_name)
{
    if (is_disable_tts)
        return false;

    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "availableEngines begin");

    QStringList engines  = QTextToSpeech::availableEngines();
    bool        is_exist = false;
    for (const auto &engine : engines)
    {
        if (engine == engine_name)
        {
            is_exist = true;
        }
    }
    if (is_exist)
    {
        delete speech_;
        speech_ = new QTextToSpeech(engine_name.toString(), this);
        ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::TTS_ENGINE, engine_name);
        cur_engine_name_ = engine_name.toString();
        emit sigEngineChanged();

        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "availableEngines end");

        return true;
    }
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "availableEngines end err");
    return false;
}

QVariant TtsControl::getCurEngineName()
{
    if (is_disable_tts)
        return false;

    return cur_engine_name_;
}

QVariant TtsControl::getAvailableLocales()
{
    if (is_disable_tts)
        return false;

    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "getAvailableLocales begin");
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "speech_{}", (int)speech_);
    Json             json_tmp;
    QVector<QLocale> locales = speech_->availableLocales();
    for (const QLocale &locale : locales)
    {
        Json tmp = {{"role_name", getLocaleStr(locale).toStdString()}};
        json_tmp.push_back(tmp);
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "AvailableLocales {}", getLocaleStr(locale).toStdString());
    }
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "getAvailableLocales end");

    return QString::fromStdString(json_tmp.dump());
}

bool TtsControl::setCurLocaleByName(QVariant locale_name)
{
    if (is_disable_tts)
        return false;

    const QVector<QLocale> locales = speech_->availableLocales();
    QLocale                current = speech_->locale();
    for (const QLocale &locale : locales)
    {
        if (getLocaleStr(locale) == locale_name.toString())
        {
            speech_->setLocale(locale);
            ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::TTS_LOCALE, locale_name);
            emit sigLocaleChanged();
            return true;
        }
    }
    return false;
}

QVariant TtsControl::getCurLocaleName()
{
    if (is_disable_tts)
        return false;

    QLocale locale = speech_->locale();
    return getLocaleStr(locale);
}

QString TtsControl::getLocaleStr(const QLocale &locale)
{
    return QString("%1 (%2)").arg(QLocale::languageToString(locale.language())).arg(QLocale::countryToString(locale.country()));
}

QVariant TtsControl::getAvailableVoices()
{
    if (is_disable_tts)
        return false;

    Json   json_tmp;
    auto   voices       = speech_->availableVoices();
    QVoice currentVoice = speech_->voice();
    //SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "getAvailableVoices begin");

    for (const QVoice &voice : voices)
    {
        Json tmp = {{"role_name", getVoiceStr(voice).toStdString()}};
        json_tmp.push_back(tmp);
        //SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "AvailableVoices {}", getVoiceStr(voice).toStdString());
    }
    //SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "getAvailableVoices end");

    return QString::fromStdString(json_tmp.dump());
}

bool TtsControl::setCurVoiceByName(QVariant voice_name)
{
    if (is_disable_tts)
        return false;

    auto voices = speech_->availableVoices();
    for (const QVoice &voice : voices)
    {
        if (getVoiceStr(voice) == voice_name)
        {
            speech_->setVoice(voice);
            ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::TTS_VOICE, voice_name);
            emit sigVoiceChanged();
            return true;
        }
    }
    return false;
}

QVariant TtsControl::getCurVoiceName()
{
    if (is_disable_tts)
        return false;

    return getVoiceStr(speech_->voice());
}

QString TtsControl::getVoiceStr(const QVoice &voice)
{
    return QString("%1 - %2 - %3").arg(voice.name()).arg(QVoice::genderName(voice.gender())).arg(QVoice::ageName(voice.age()));
}

void TtsControl::testTts()
{
    speech_->say("欢迎使用百货收银");
}
