﻿#include "FileDownloadWorker.h"
#include <synchapi.h>
#include "LogManager.h"
#include "NetModule/HttpClient.h"
#include "Utils/Utils.h"

using namespace AeaQt;

FileDownloadWorker::FileDownloadWorker(std::vector<std::string> download_list, QObject *parent) : QObject{parent}
{
    this->download_list_original = download_list;
    this->download_list          = download_list;
}

void FileDownloadWorker::process()
{
    while ((download_list.size() || downloading_list.size()) && !is_stop_)
    {
        // LOG_OPT_INFO("downloading_list.size(): {}", downloading_list.size());

        if (downloading_list.size())
        {
            Sleep(1000);
            continue;
        }

        downloading_list.push_back(*download_list.begin());

        auto file_url = prefix + *downloading_list.begin();

        LOG_OPT_INFO("downloadFile :{}", file_url);

        QString url = QString::fromStdString(file_url);

        HttpClient *http_client = new HttpClient();
        auto        request     = http_client->get(url);

        auto down_path = Utils::getAppDirPath() + "/" + update_folder_name.c_str();

        request.download()
            .downloadPath(down_path)

            .onDownloadProgress(
                [=](qint64 bytesReceived, qint64 bytesTotal)
                {
                    LOG_OPT_INFO("bytes received: {}bytes total: {}", bytesReceived, bytesTotal);
                })
            .onDownloadFileSuccess(
                [&, file_url](QString fileName) mutable
                {
                    LOG_OPT_WARN("download success: {}", fileName.toStdString());

                    std::vector<std::string>::iterator find_iter1 = std::find(downloading_list.begin(), downloading_list.end(), file_url);

                    if (find_iter1 != downloading_list.end())
                    {
                        downloading_list.erase(find_iter1);
                    }

                    emit sigDowloadIndex(download_list.size());
                })
            .onSuccess(
                [&]()
                {
                    std::vector<std::string>::iterator find_iter1 = std::find(downloading_list.begin(), downloading_list.end(), file_url);

                    if (find_iter1 != downloading_list.end())
                    {
                        downloading_list.erase(find_iter1);
                    }
                })
            .onDownloadFileFailed(
                [&](QString error)
                {
                    LOG_OPT_ERROR("download failed: {}", error.toStdString());
                    emit sigDowloadFailedFile(file_url.c_str());

                    std::vector<std::string>::iterator find_iter1 = std::find(downloading_list.begin(), downloading_list.end(), file_url);

                    if (find_iter1 != downloading_list.end())
                    {
                        downloading_list.erase(find_iter1);
                    }

                    emit sigDowloadIndex(download_list.size());
                })
            .exec();


        // downloadFile(prefix + *downloading_list.begin());
    }
}

void FileDownloadWorker::downloadFile(std::string file_url)
{
    LOG_OPT_INFO("downloadFile :{}", file_url);

    QString url = QString::fromStdString(file_url);

    HttpClient *http_client = new HttpClient();
    auto        request     = http_client->get(url);

    auto down_path = Utils::getAppDirPath() + "/" + update_folder_name.c_str();

    request.download()
        .downloadPath(down_path)
        .onDownloadProgress(
            [=](qint64 bytesReceived, qint64 bytesTotal)
            {
                LOG_OPT_INFO("bytes received: {}bytes total: {}", bytesReceived, bytesTotal);
            })
        .onDownloadFileSuccess(
            [&, file_url](QString fileName) mutable
            {
                LOG_OPT_WARN("download success: {}", fileName.toStdString());

                std::vector<std::string>::iterator find_iter1 = std::find(downloading_list.begin(), downloading_list.end(), file_url);

                if (find_iter1 != downloading_list.end())
                {
                    downloading_list.erase(find_iter1);
                }

                emit sigDowloadIndex(download_list.size());
            })
        .onDownloadFileFailed(
            [&](QString error)
            {
                LOG_OPT_ERROR("download failed: {}", error.toStdString());
                emit sigDowloadFailedFile(file_url.c_str());

                std::vector<std::string>::iterator find_iter1 = std::find(downloading_list.begin(), downloading_list.end(), file_url);

                if (find_iter1 != downloading_list.end())
                {
                    downloading_list.erase(find_iter1);
                }

                emit sigDowloadIndex(download_list.size());
            })
        .exec();
}
