﻿#ifndef NETSTATUSCTRL_H
#define NETSTATUSCTRL_H

#include <QObject>
#include <QTimer>
#include "EnumTool.h"
#include "WorkerModule/NetStatusWorker.h"
#include "WorkerModule/Ping.h"

//NetworkStatus
class NetStatusCtrl : public QObject
{
    Q_OBJECT

    Q_PROPERTY(int netStatus READ netStatus WRITE setNetStatus NOTIFY netStatusChanged FINAL)

public:
    explicit NetStatusCtrl(QObject *parent = nullptr);
    ~NetStatusCtrl();

    QTimer timer_net_check_;

    int  netStatus();
    void setNetStatus(int status);

    NetStatusWorker *network_thread;
    int              net_status_ = (int)NetworkStatus::NETWORK_STATUS__CONNECTED;

signals:
    void netStatusChanged();
};

#endif // NETSTATUSCTRL_H
