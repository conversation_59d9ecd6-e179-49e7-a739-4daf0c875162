﻿#ifndef CONTROLMANAGER_H
#define CONTROLMANAGER_H

#include <QObject>
#include <memory>
#include <mutex>
#include "BarcodeLabelScale.h"
#include "ControlModule/CameraControl.h"
#include "ControlModule/GoodsControl.h"
#include "ControlModule/MqttControl.h"
#include "ControlModule/OrderControl.h"
#include "ControlModule/PayMethodControl.h"
#include "ControlModule/BusinessAlarmControl.h"
#include "ControlModule/ShopCartControl.h"
#include "ControlModule/ShopControl.h"
#include "ControlModule/SoundCtrl.h"
#include "ControlModule/UpdateCtrl.h"
#include "LogCtrl.h"
#include "MemberControl.h"
#include "MqttControl.h"
#include "NetOrderControl.h"
#include "NetStatusCtrl.h"
#include "PermissionCtrl.h"
#include "PrinterControl.h"
#include "SupplierControl.h"
#include "TtsControl.h"
#include "WeightingScaleControl.h"
#include "GoodsPromotionCtrl.h"

class ControlManager : public QObject
{
    Q_OBJECT
    Q_PROPERTY(int loadingIndex READ loadingIndex WRITE setLoadingIndex NOTIFY loadingIndexChanged FINAL)

public:
    ~ControlManager();
    static ControlManager *getInstance();
    int        loading_index_ = 0;

    void initControls();
    int        loadingIndex();
    void       setLoadingIndex(int index);
    inline int loadingIndexPlus()
    {
        setLoadingIndex(loadingIndex() + 1);
        return loadingIndex();
    }
    inline int loadingIndexMinus()
    {
        setLoadingIndex(loadingIndex() - 1);
        return loadingIndex();
    }
    GoodsControl *getGoodsControl()
    {
        return goods_control_;
    }
    GoodsPromotionCtrl *getGoodsPromotionCtrl()
    {
        return goods_promotion_ctrl_;
    }
    ShopCartList *getShopCartList()
    {
        return shop_cart_list_;
    }

    OrderControl *getOrderControl()
    {
        return order_control_;
    }

    ShopControl *getShopControl()
    {
        return shop_control_;
    }

    PayMethodControl *getPayMethodControl()
    {
        return pay_method_control_;
    }
    BusinessAlarmControl *getBusinessAlarmControl()
    {
        return business_alarm_control_;
    }
    CameraControl *getCameraControl()
    {
        return camera_control_;
    }

    PrinterControl *getPrinterControl()
    {
        return printer_control_;
    }

    NetOrderControl *getNetOrderControl()
    {
        return net_order_control_;
    }

    MemberControl *getMemberControl()
    {
        return member_control_;
    }

    WeightingScaleControl *getWeightingScaleControl()
    {
        return weighting_scale_control_;
    }

    SupplierControl *getSupplierControl()
    {
        return supplier_control_;
    }

    MqttControl *getMqttControl()
    {
        return mqtt_control_;
    }

    TtsControl *getTtsControl()
    {
        return tts_control_;
    }

    BarcodeLabelScale *getBarcodeLabelScale()
    {
        return barcode_label_scale_;
    }

    PermissionCtrl *getPermissionControl()
    {
        return permission_ctrl_;
    }

    UpdateCtrl *getUpdateCtrl()
    {
        return update_ctrl_;
    }

    LogCtrl *getLogCtrl()
    {
        return log_ctrl_;
    }

    SoundCtrl *getSoundCtrl()
    {
        return sound_ctrl_;
    }

    NetStatusCtrl *getNetStatusCtrl()
    {
        return net_status_ctrl_;
    }

    void getOnlineInfo(bool is_force_get_all = false);

private:
    MqttControl           *mqtt_control_            = nullptr;
    GoodsControl          *goods_control_           = nullptr;
    GoodsPromotionCtrl    *goods_promotion_ctrl_     = nullptr;
    ShopCartList          *shop_cart_list_          = nullptr;
    OrderControl          *order_control_           = nullptr;
    ShopControl           *shop_control_            = nullptr;
    PayMethodControl      *pay_method_control_      = nullptr;
    BusinessAlarmControl  *business_alarm_control_  = nullptr;
    CameraControl         *camera_control_          = nullptr;
    PrinterControl        *printer_control_         = nullptr;
    NetOrderControl       *net_order_control_       = nullptr;
    MemberControl         *member_control_          = nullptr;
    WeightingScaleControl *weighting_scale_control_ = nullptr;
    SupplierControl       *supplier_control_        = nullptr;
    TtsControl            *tts_control_             = nullptr;
    BarcodeLabelScale     *barcode_label_scale_     = nullptr;
    PermissionCtrl        *permission_ctrl_         = nullptr;
    UpdateCtrl            *update_ctrl_             = nullptr;
    LogCtrl               *log_ctrl_                = nullptr;
    SoundCtrl             *sound_ctrl_              = nullptr;
    NetStatusCtrl         *net_status_ctrl_         = nullptr;
signals:
    void loadingIndexChanged();
private:
    static std::unique_ptr<ControlManager> singleton_;

    static std::mutex mutex_;
    explicit ControlManager(QObject *parent = nullptr);
};

#endif // CONTROLMANAGER_H
