﻿#include "OrderControl.h"
#include <fstream>
#include "ControlModule/ControlManager.h"
#include "ControlModule/ShopControl.h"
#include "DataModule/DataManager.h"
#include "LogModule/LogManager.h"
#include "NetModule/HttpWorker.h"
#include "NetModule/NetGlobal.h"
#include "NetModule/HttpCallback.h"
#include "NetModule/HttpClient.h"
#include "NetModule/HttpWorker.h"
#include "NetModule/NetGlobal.h"
#include "qurl.h"

using namespace std;
using namespace AeaQt;
extern double saleList_actually_received_Temp;

OrderControl::OrderControl(QObject *parent) : QObject{parent}
{
}

OrderControl::~OrderControl()
{
}

QString OrderControl::getPendingOrderPath()
{
    return Utils::getAppDirPath() + "/" + "pending_order.cfg";
}

void OrderControl::initDataByConfig()
{
    fstream stream_pending_order;
    stream_pending_order.open(getPendingOrderPath().toStdString(), ios_base::in); // | ios_base::out | ios_base::trunc);
    std::stringstream buffer;
    buffer << stream_pending_order.rdbuf();
    stream_pending_order.close();
    std::string pending_order_j_str(buffer.str());

    auto pending_orders_json = Json::parse(pending_order_j_str, nullptr, false);

    if (!pending_orders_json.is_discarded() && pending_orders_json.is_array())
    {
        for (auto &cur_order : pending_orders_json)
        {
            std::shared_ptr<ShopCartList> shop_cart_list_p;
            shop_cart_list_p.reset(new ShopCartList());

            if (cur_order.contains("shop_cart_Items_"))
            {
                for (auto shop_cart_item : cur_order["shop_cart_Items_"])
                {
                    QMutex mutex;
                    if(true){
                        QMutexLocker locker(&mutex);
                        shop_cart_list_p->shop_cart_Items_.push_back(shop_cart_item.get<ShopCartItem>());
                    }

                }
            }

            if (cur_order.contains("shop_cart_list_data_"))
            {
                shop_cart_list_p->shop_cart_list_data_ = cur_order["shop_cart_list_data_"].get<shopCartListData>();

            }

            pending_orders_.push_back(shop_cart_list_p);
        }
    }
}

void OrderControl::savePendingOrder2Config()
{
    Json result;

    for (std::shared_ptr<ShopCartList> &pending_order : pending_orders_)
    {
        result.push_back(*pending_order);
    }

    fstream stream_pending_order;
    stream_pending_order.open(getPendingOrderPath().toStdString(), ios_base::out | ios_base::trunc);
    stream_pending_order << result.dump();
    stream_pending_order.close();
}

bool OrderControl::addOrder(const OrderInfo &order_info)
{
    return DataManager::getInstance()->getOrderDataMgr()->addOrder(order_info);
}
void OrderControl::getGoodBatchListQml(QJSValue callback, QString stockType,int pageSize,int pageIndex,QString goodsBarcodes)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    Json post_json;
    post_json["goodsBarcode"]   = goodsBarcodes.toStdString();
    post_json["shopUnique"]     = QString::number(shop_control->getShopUnique()).toStdString();
    post_json["stockType"]      = stockType.toStdString();
    post_json["pageSize"]       = 10;
    post_json["pageIndex"]      = 1;


    QString url = req_host_prefix + "shopUpdate/goodBatch/queryGoodBatchList.do";

    HttpClient *http_client = new HttpClient();
    auto      &&request     = http_client->post(url);
    request.bodyWithJson(post_json.dump());

    LOG_NET_INFO("请求批次列表接口");
    execAndCallback4Cpp(
        request,
        [=](HttpHandleType http_handle_type, std::string data) mutable
        {
            switch (http_handle_type)
            {
            case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                {
                    auto json_doc = Json::parse(data, nullptr, false);
                    if (json_doc.is_discarded())
                    {
                        LOG_NET_ERROR("数据解析错误");
                    }
                    else
                    {
                        QVariant var_tmp;
                        tryFromJson(json_doc, "status", var_tmp);
                        auto status = var_tmp.toInt();

                        if (status == 1)
                        {
                            if (callback.isCallable())
                            {
                                QJSValueList arglist;
                                arglist.push_back(QString::fromStdString(data));                          
                                auto ret      = callback.call(arglist);
                            }
                        }
                        else if (status == 0)
                        {
                    }
                    break;
                }
            }
            case HttpHandleType::HTTP_HANDLE__ON_ERROR:
            case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                {
                    // 网络错误
                    if (callback.isCallable())
                    {
                    }
                    break;
                }
            }
            http_client->deleteLater();
        });

    if (callback.isCallable())
    {
//        Json json_tmp = {
//            {"receivable_amount", saleList_total_price}, {"received_amount", saleList_actually_received}, {"change_amount", change_amount}, {"sn", __LINE__}};
//        auto arg_list = generateQmlCallbackArgList(EnumTool::PayStatusEnum::PAY_STATUS__PAYING, pay_method_enum, json_tmp.dump());

//        LOG_EVT_INFO("主线程PAY_STATUS__PAYING {}", json_tmp.dump());

//        callback.call(arg_list);
    }

}
//void OrderControl::getGoodBatchListQml(QJSValue callback, QString stockType,int pageSize,int pageIndex,QString goodsBarcodes)
//{
//    auto shop_control = ControlManager::getInstance()->getShopControl();

//    //QString url = req_host_prefix + "shopUpdate/goodBatch/queryGoodBatchLista.do";
//    QString url = "http://test170.buyhoo.cc/shopUpdate/goodBatch/queryGoodBatchList.do";
//    bodyMap body_map;
//    body_map["goodsBarcode"]    = goodsBarcodes;
//    body_map["shopUnique"]    = QString::number(shop_control->getShopUnique());
//    body_map["stockType"]     = stockType;
////    body_map.insert("pageSize", "100");
////    body_map.insert("pageIndex", "1");
//    body_map["pageSize"]     = "10";
//    body_map["pageIndex"]     = "1";

//    auto cur_thread_p = new QThread(this); // 工作线程

//    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
//    http_get_sales_record->moveToThread(cur_thread_p);
//    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "65463454634");
//    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
//    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
//    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
//    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
//            [=](QNetworkReply *reply) mutable
//            {
//                QByteArray reply_data   = reply->readAll();
//                QUrl       request_url  = reply->url();
//                bool       is_succ      = reply->error() == QNetworkReply::NoError;
//                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

//                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
//                if (!Json::accept(reply_data_c))
//                {
//                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
//                }
//                else
//                {

//#ifdef BUYHOO_PARSE_DEBUG
//                    Json json_doc = Json::parse(reply_data_c);
//                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
//#endif

//                    QJSValueList arglist;
//                    arglist.push_back(QJSValue(QString::fromStdString(reply_data_c)));

//                    if (callback.isCallable())
//                        auto ret = callback.call(arglist);
//                }
//                cur_thread_p->quit();
//                cur_thread_p->wait();
//            });
//    // 启动线程
//    cur_thread_p->start();
//}
void OrderControl::getStockRecord4Qml(QJSValue callback, QString begin_time, QString end_time)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    begin_time += " 00:00:00";
    end_time += " 23:59:59";

    QString url = req_host_prefix + "shopUpdate/stock/queryShopStockRecord.do";
    bodyMap body_map;
    body_map["startTime"]     = begin_time;
    body_map["endTime"]       = end_time;
    body_map["shopUnique"]    = QString::number(shop_control->getShopUnique());
    body_map["stockResource"] = "-1";
    body_map.insert("stockType", "-1");
    body_map.insert("pageSize", "10000");
    body_map.insert("pageNum", "1");
    body_map.insert("order", "4");
    body_map.insert("orderType", "2");

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {

#ifdef BUYHOO_PARSE_DEBUG
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(reply_data_c)));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}


void OrderControl::getSalesRecord4Qml(QJSValue callback, QString begin_time, QString end_time)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    begin_time += ":00";
    end_time += ":59";

    QString url = req_host_prefix + "shopmanager/pc/queryTodayLists.do";
    bodyMap body_map;
    body_map["startTime"]  = begin_time;
    body_map["endTime"]    = end_time;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());

    auto req_sales_record_thread = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(req_sales_record_thread);

    connect(req_sales_record_thread, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(req_sales_record_thread, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, req_sales_record_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                            // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {

#ifdef BUYHOO_PARSE_DEBUG
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(reply_data_c)));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                req_sales_record_thread->quit();
                req_sales_record_thread->wait();
            });
    // 启动线程
    req_sales_record_thread->start();
}

void OrderControl::getSalesRecordDetail4Qml(QJSValue callback, QVariant saleUnique)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/app/shop/appQuerySaleListDetail.do";
    bodyMap body_map;
    body_map["shop_unique"]      = QString::number(shop_control->getShopUnique());
    body_map["sale_list_unique"] = saleUnique.toString();

    auto cur_thread = new QThread(this); // 工作线程

    HttpWorker *http_worker = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_worker->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_worker, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {

#ifdef BUYHOO_PARSE_DEBUG
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(reply_data_c)));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    cur_thread->start();
}
void OrderControl::getGoodsSaleDetail4Qml(QJSValue callback,QString goodsBarcode, QString startTime, QString endTime, QString pageNum, QString pageSize)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/pc/queryGoodsSaleDetail.do";
    bodyMap body_map;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    body_map["startTime"]  = startTime;
    body_map["endTime"]    = endTime;
    body_map["goodsBarcode"] = goodsBarcode;
    body_map["pageNum"]  = pageNum;
    body_map["pageSize"]  = pageSize;

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {

#ifdef BUYHOO_PARSE_DEBUG
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(reply_data_c)));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void OrderControl::getGoodsSaleStatistics4Qml(QJSValue callback, QString begin_time, QString end_time,QString goodsMsg, QString pageNum,QString pageSizes, QString groupUnique, QString kindUnique, QString supplierUnique)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    begin_time += ":00";
    end_time += ":59";
    QString url = req_host_prefix + "shopmanager/pc/queryGoodsSaleStatistics.do";
    bodyMap body_map;
    body_map["goodsMsg"]    = goodsMsg;
    body_map["pageNum"]  = pageNum;
    body_map["startTime"]  = begin_time;
    body_map["endTime"]    = end_time;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    body_map["pageSize"]  = pageSizes;

    body_map["groupUnique"]    = groupUnique;
    body_map["kindUnique"] = kindUnique;
    body_map["supplierUnique"]  = supplierUnique;

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {

#ifdef BUYHOO_PARSE_DEBUG
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(reply_data_c)));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void OrderControl::getGoodsInformation4Qml(QJSValue callback, QString begin_time, QString end_time,QString goodsMsg, QString orderName, QString pageNum)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    begin_time += ":00";
    end_time += ":59";
    QString url = req_host_prefix + "shopmanager/pc/queryTodayGoodsSaleMsg.do";
    bodyMap body_map;
    body_map["goodsMsg"]    = goodsMsg;
    body_map["orderType"] = "DESC";
    body_map["orderName"] = orderName;
    body_map["pageNum"]  = pageNum;
    body_map["startTime"]  = begin_time;
    body_map["endTime"]    = end_time;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    body_map["pageSize"]  = "10";

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {

#ifdef BUYHOO_PARSE_DEBUG
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(reply_data_c)));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void OrderControl::getHandoverRecord4Qml(QJSValue callback, QString begin_time, QString end_time)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    begin_time += ":00";
    end_time += ":59";

    QString url = req_host_prefix + "shopmanager/pc/pcStaffSignOutForBoss.do";

    bodyMap body_map;
    body_map["startTime"]  = begin_time;
    body_map["endTime"]    = end_time;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    body_map["todayOnly"]  = "2";

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {

#ifdef BUYHOO_PARSE_DEBUG
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(reply_data_c)));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}

void OrderControl::getHandoverRecordDetail4Qml(QJSValue callback, QString staff_id, QString start_time, QString end_time)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();

    QString url = req_host_prefix + "shopmanager/pc/goodsSaleMsgForStaff.do";
    bodyMap body_map;
    body_map["shopUnique"] = QString::number(shop_control->getShopUnique());
    body_map["staffId"]    = staff_id;
    body_map["startTime"]  = start_time;
    if (end_time != "")
        body_map["endTime"] = end_time;

    auto cur_thread = new QThread(this); // 工作线程

    HttpWorker *http_worker = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_worker->moveToThread(cur_thread);

    connect(cur_thread, &QThread::started, http_worker, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread, &QThread::finished, http_worker, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_worker, &HttpWorker::destroyed, cur_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_worker, &HttpWorker::sendReply, this,                               // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {

#ifdef BUYHOO_PARSE_DEBUG
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(reply_data_c)));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread->quit();
                cur_thread->wait();
            });
    // 启动线程
    cur_thread->start();
}

void OrderControl::reqRefundOrder(QJSValue callback, QVariant data_json)
{
    auto shop_control = ControlManager::getInstance()->getShopControl();
    Json json_doc     = Json::parse(data_json.toString().toStdString());

    QString url = req_host_prefix + "shopmanager/ret/addNewRetRecord.do";
    bodyMap body_map;
    body_map["shopUnique"]         = QString::number(shop_control->getShopUnique());
    body_map["staffId"]            = QString::number(shop_control->getUserId());
    body_map["macId"]              = Utils::getHostMacAddressWithoutColon();
    body_map["retListHandlestate"] = "3";
    body_map["retListState"]       = "2";
    body_map["retOrigin"]          = "1";

    QVariant variant_tmp;
    tryFromJson(json_doc, "goodsMessage", variant_tmp);
    body_map["goodsMessage"] = variant_tmp.toString();
    tryFromJson(json_doc, "retListRemarks", variant_tmp);
    body_map["retListRemarks"] = variant_tmp.toString();
    tryFromJson(json_doc, "retListTotalMoney", variant_tmp);
    body_map["retListTotalMoney"] = variant_tmp.toString();

    // 1：现金；
    // 2：支付宝；
    // 3：微信；
    // 4：银行卡；
    // 5：储值卡；
    // 13：免密支付（易通）

    auto ret_money_type = static_cast<PayMethodEnum>(getDataFromJson(json_doc, "retMoneyType").toInt());

    switch (ret_money_type)
    {
    case PayMethodEnum::PAY_METHOD__CASH:
        body_map["retMoneyType"] = "1";
        break;
    case PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE:
        body_map["retMoneyType"] = "2";
        break;
    case PayMethodEnum::PAY_METHOD__WECHAT_OFFLINE:
        body_map["retMoneyType"] = "3";
        break;
    case PayMethodEnum::PAY_METHOD__BANK_CARD:
        body_map["retMoneyType"] = "4";
        break;
    case PayMethodEnum::PAY_METHOD__VIPCARD:
        body_map["retMoneyType"] = "5";
        break;
    case PayMethodEnum::PAY_METHOD__JINQUAN:
        body_map["retMoneyType"] = "13";
        break;
    default:
        break;
    }

    tryFromJson(json_doc, "saleListUnique", variant_tmp);
    body_map["saleListUnique"] = variant_tmp.toString();

    auto cur_req_thread = new QThread(this); // 工作线程

    HttpWorker *cur_work_thread = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    cur_work_thread->moveToThread(cur_req_thread);

    connect(cur_req_thread, &QThread::started, cur_work_thread, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_req_thread, &QThread::finished, cur_work_thread, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(cur_work_thread, &HttpWorker::destroyed, cur_req_thread, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(cur_work_thread, &HttpWorker::sendReply, this,                                   // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                QUrl       request_url  = reply->url();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误: {}", reply_data_c);
                }
                else
                {

#ifdef BUYHOO_PARSE_DEBUG
                    Json json_doc = Json::parse(reply_data_c);
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif

                    QVariant variant_tmp;
                    tryFromJson(json_doc, "status", variant_tmp);

                    int status = variant_tmp.toInt();

                    if (status == 1)
                    {
                        QJSValueList arglist;
                        arglist.push_back(true);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                    else
                    {
                        QJSValueList arglist;
                        arglist.push_back(false);
                        arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                        if (callback.isCallable())
                            auto ret = callback.call(arglist);
                    }
                }
                cur_req_thread->quit();
                cur_req_thread->wait();
            });
    // 启动线程
    cur_req_thread->start();
}


void OrderControl::addPendingOrderByShopCart()
{
    ShopCartList *shop_cart_list = ControlManager::getInstance()->getShopCartList();
    auto          is_need_print  = ConfigTool::getInstance()->getSetting(ConfigEnum::IS_PENDING_ORDER_PRINT_TICKETS).toBool();

    if (shop_cart_list->orderQuantity() == 0)
        return;

    std::shared_ptr<ShopCartList> shop_cart_list_p;
    shop_cart_list_p.reset(new ShopCartList());

    shop_cart_list_p->shop_cart_Items_     = shop_cart_list->shop_cart_Items_;
    shop_cart_list_p->shop_cart_list_data_ = shop_cart_list->shop_cart_list_data_;

    pending_orders_.push_back(shop_cart_list_p);

    if (is_need_print)
        ControlManager::getInstance()->getPrinterControl()->printPendingOrder(shop_cart_list_p.get());

    shop_cart_list->resetOrderInfo();
    shop_cart_list->resetMemberInfo();
    shop_cart_list->clearAllGoods();

    savePendingOrder2Config();
}

void OrderControl::backupOrder()
{
    auto                         *shop_cart_list = ControlManager::getInstance()->getShopCartList();
    std::shared_ptr<ShopCartList> shop_cart_list_p;
    shop_cart_list_p.reset(new ShopCartList());
    shop_cart_list_p->shop_cart_Items_     = shop_cart_list->shop_cart_Items_;
    shop_cart_list_p->shop_cart_list_data_ = shop_cart_list->shop_cart_list_data_;
    backup_last_order_                     = shop_cart_list_p;
    emit sigBackupOrderFinished();
}

QString OrderControl::getPendingOrdersJson()
{
    Json pending_orders_j;

    for (auto &pending_order : pending_orders_)
    {
        pending_orders_j.push_back(Json::parse(pending_order->getPendingOrderInfoJson().toStdString()));
    }

    return QString::fromStdString(pending_orders_j.dump());
}

void OrderControl::clearAllPendingOrders()
{
    pending_orders_.clear();
    savePendingOrder2Config();
    emit sigRefreshPendingOrders();
}

void OrderControl::takePendingOrder2ShopCart(QString order_unique)
{
    ShopCartList *shop_cart_list = ControlManager::getInstance()->getShopCartList();

    for (auto pending_order_iter = pending_orders_.begin(); pending_order_iter != pending_orders_.end(); ++pending_order_iter)
    {
        if ((*pending_order_iter)->shop_cart_list_data_.order_unique_ == order_unique)
        {
            shop_cart_list->shop_cart_Items_     = (*pending_order_iter)->shop_cart_Items_;
            shop_cart_list->shop_cart_list_data_ = (*pending_order_iter)->shop_cart_list_data_;
            pending_orders_.erase(pending_order_iter);
            shop_cart_list->sendRefreshSignal();
            savePendingOrder2Config();
            return;
        }
    }
}

QString OrderControl::getPendingOrderDetailJson(QString sale_list_unique)
{
    Json json;
    for (auto &pending_order : pending_orders_)
    {
        if (pending_order->shop_cart_list_data_.order_unique_ == sale_list_unique)
            json = Json::parse(pending_order->getPendingOrderDetailInfoJson().toStdString());
    }

    return QString::fromStdString(json.dump());
}

bool OrderControl::eraserPendingOrderById(QString sale_list_id)
{
    for (auto pending_order_iter = pending_orders_.begin(); pending_order_iter != pending_orders_.end(); ++pending_order_iter)
    {
        if ((*pending_order_iter)->shop_cart_list_data_.order_unique_ == sale_list_id)
        {
            pending_orders_.erase(pending_order_iter);
            return true;
        }
    }
    return false;
}

QString OrderControl::generateOrderId()
{
    QString   accountNumTmp      = QString::number(ControlManager::getInstance()->getShopControl()->getUserId());
    QDateTime timeListCurrentTmp = QDateTime::currentDateTime(); // 获取当前时间
    qint64    time_List          = timeListCurrentTmp.toMSecsSinceEpoch();
    QString   srand_Num          = QString::number(qrand() % 10);
    LOG_EVT_INFO("accountNumTmp:{}srand_Num:{}time_List:{}",accountNumTmp.toStdString(),srand_Num.toStdString(),time_List);
    QString   order_id           = QString::number(time_List) + srand_Num + accountNumTmp.right(2);
    LOG_EVT_INFO("生成的订单号为:{}",order_id.toStdString());
    return order_id;
}

double OrderControl::getBackupOrderPrice()
{
    return saleList_actually_received_Temp;
    //return backup_last_order_->getFinalTotalGoodsPrice(backup_last_order_->shop_cart_list_data_.payment);
}
QString OrderControl::doubleToString(double value) {
    return QString::number(value, 'f', 2); // 'f' 表示固定点表示法，0 表示不保留小数
}
bool OrderControl::printPendingOrderByOrderUnique(QString order_unique)
{
    auto print_ctrl = ControlManager::getInstance()->getPrinterControl();
    for (auto pending_order_iter = pending_orders_.begin(); pending_order_iter != pending_orders_.end(); ++pending_order_iter)
    {
        if ((*pending_order_iter)->shop_cart_list_data_.order_unique_ == order_unique)
        {
            print_ctrl->printPendingOrder(pending_order_iter->get());
            return true;
        }
    }
    return false;
}
