﻿#define SPDLOG_ACTIVE_LEVEL SPDLOG_LEVEL_TRACE
#include <AdapterModule/Annotation.h>
#include <ModelModule/TagEditHelper.h>
#include <QApplication>
#include <QMessageBox>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QSharedMemory>
#include <QSplashScreen>
#include <QSystemSemaphore>
#include <QTextCodec>
#include <Windows.h>
#include <memory>
#include <qnetwork.h>
#include <qwindow.h>
#include <shellapi.h>
#include <windows.h>
#include "ConfModule/ConfigTool.h"
#include "ControlModule/CameraControl.h"
#include "ControlModule/ControlManager.h"
#include "CheckDataModule/CheckData.h"
#include "ControlModule/GoodsControl.h"
#include "ControlModule/GoodsDataModel.h"
#include "ControlModule/ShopCartControl.h"
#include "ControlModule/ShopCartModel.h"
#include "ControlModule/GoodsPromotionCtrl.h"
#include "EnumTool.h"
#include "KeyEvent/KeyEvent.h"
#include "LogModule/LogManager.h"
#include "LogModule/QtLog.h"
#include "NetModule/NetGlobal.h"
#include "Splashscreen/Splashscreen.h"
#include "Utils/Utils4Qml.h"
#include <QFontDatabase>
#include <QTranslator>
#include <iostream>
#include <windows.h>
#include <windows.h>
#include <shellapi.h>
#include <iostream>
#include "NetModule/HttpClient.h"
#include "NetModule/HttpWorker.h"

using namespace  AeaQt;
//检查是否需要更新
void updateTime(){

    QString url = "http://buyhoo.cc/shopmanager/app/shop/getNowTime.do";

    HttpClient *http_time = new HttpClient();
    auto      &&requestTime     = http_time->post(url);

    requestTime.timeout(3);
    LOG_EVT_INFO("开始进行数据库时间查询");
    execAndCallback4Cpp(requestTime,
                        [=](HttpHandleType http_handle_type, std::string data) mutable
                        {
                        switch (http_handle_type)
                        {
                        case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
                            {
                                Json json_doc = Json::parse(data, nullptr, false);

                                if (json_doc.is_discarded())
                                {
                                    LOG_EVT_INFO("数据解析失败");
                                }
                                else
                                {
                                    QVariant var_tmp;
                                    tryFromJson(json_doc, "status", var_tmp);
                                    auto status = var_tmp.toInt();

                                    // 查询成功
                                    if (status == 1)
                                    {
                                        LOG_EVT_INFO("数据库时间查询成功");
                                        std::string systemTime = json_doc["data"];
                                        LOG_EVT_INFO("读到的时间为:{}",systemTime);
                                        size_t spacePos = systemTime.find(' ');
                                        if (spacePos != std::string::npos) {
                                            std::string dateStr = systemTime.substr(0, spacePos);
                                            std::string timeStr = systemTime.substr(spacePos + 1);
                                            QString datas = QString::fromStdString(dateStr);
                                            QString times = QString::fromStdString(timeStr);
                                            LOG_EVT_INFO("日期为: {}", datas.toStdString());
                                            LOG_EVT_INFO("时间为: {}", times.toStdString());
                                            QString datetime = "date " + datas.replace('-', '/') + "\n" + "time " + times; // 可能需要调整日期格式
                                            QByteArray data1 = datetime.toLocal8Bit();

                                            QDir dir("C:/tab0");
                                            if (!dir.exists()) {
                                                dir.mkdir(dir.absolutePath());
                                            }

                                            QFile file(dir.path() + "/date.bat");
                                            if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                                                LOG_EVT_INFO("date文件创建失败" ); // 使用标准错误输出进行调试
                                                std::cout<<"date establish failed"<<std::endl;
                                            }
                                            file.write(data1);
                                            file.close();
                                            //更新系统时间
                                            QProcess process;
                                            process.start(dir.path() + "/date.bat");
                                            bool finished = process.waitForFinished(); // 等待进程完成
                                            if (!finished) {
                                                LOG_EVT_INFO("批处理文件执行失败");
                                                std::cout<<"batch failed"<<std::endl;
                                            }else{
                                                LOG_EVT_INFO("批处理文件执行成功");
                                                std::cout<<"batch successful"<<std::endl;
                                            }
                                        } else {
                                            LOG_EVT_ERROR("时间字符串格式错误，无法找到空格分隔符");
                                        }
                                    }
                                    }
                              }
                        case HttpHandleType::HTTP_HANDLE__ON_ERROR:
                        {
                        }
                        case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
                        {
                        }
                      }
                         });
}
// Toggle the press and hold gesture for the given window
bool TogglePressAndHold(HWND hWnd, bool enable);
int main(int argc, char *argv[])
{

#ifdef HIGH_PRIORITY
    SetPriorityClass(GetCurrentProcess(), REALTIME_PRIORITY_CLASS);
#endif

    HDC hdc = GetDC(NULL);
    int cx  = GetDeviceCaps(hdc, DESKTOPHORZRES);

    QApplication app(argc, argv);

    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));

    LogMgr::getInst();

#ifdef BUYHOO_LOG
    auto log_path    = Utils::getAppDirPath().toStdString();
    auto date__y_m_d = Utils::DateTime::getCurDateStr().toStdString();

    log_path += "/log/" + date__y_m_d + "/";

    logSysInit(QString::fromStdString(log_path) + "qt.log");
#endif
    refreshReqHostPrefixByConfig();

    // 加载字体
    int fontId = QFontDatabase::addApplicationFont(":/Libraries/font/SourceHanSansSC/SourceHanSansSC-Regular.otf");
    if (fontId != -1) {
        QStringList fontFamilies = QFontDatabase::applicationFontFamilies(fontId);
        if (!fontFamilies.isEmpty()) {
            QString fontFamily = fontFamilies.at(0); // 获取字体家族名
            LOG_EVT_INFO("{}字体加载成功！",fontFamily.toStdString());
        }
    }
    //更新时间
    updateTime();

    ConfigTool *configtools= ConfigTool::getInstance();
    /**创建翻译器对象**/
    QTranslator translator;
    switch (configtools->languageSelected().toInt())
    {
        case 0:
            LOG_EVT_INFO("开始加载中文");
            if (translator.load(":/languages/translations/lang_Chinese.qm"))
            {
                app.installTranslator(&translator);
            }
            break;
        case 1:
            LOG_EVT_INFO("开始加载英文");
            if (translator.load(":/languages/translations/lang_English.qm"))
                {
                LOG_EVT_INFO("load 英文成功！");
                app.installTranslator(&translator);
                }
            break;
        case 2:
            LOG_EVT_INFO("开始加载越南语");
            if (translator.load(":/languages/translations/lang_Vietnam.qm"))
                {
                app.installTranslator(&translator);
                }
            break;
        case 3:
            LOG_EVT_INFO("开始加载泰文");
            if (translator.load(":/languages/translations/lang_Thailand.qm"))
                {
                app.installTranslator(&translator);
                }
            break;
        case 4:
            LOG_EVT_INFO("开始加载俄文");
            if (translator.load(":/languages/translations/lang_Russia.qm"))
                {
                app.installTranslator(&translator);
                }
            break;
        default:
            LOG_EVT_INFO("未选择默认中文");
            if (translator.load(":/languages/translations/lang_Chinese.qm"))
            {
                app.installTranslator(&translator);
            }
            break;
    }

    QQmlApplicationEngine engine;

    q_qml_application_engine = &engine;
    ///-------------------------------------------| 确保只运行一次 |-------------------------------------------
    //
    QSystemSemaphore sema("ServerKey", 1, QSystemSemaphore::Open);

    sema.acquire(); // 在临界区操作共享内存
    QSharedMemory mem("BUYHOO-Market-MSVC");

    if (!mem.create(1)) // 如果全局对象以存在则退出
    {
        QMessageBox::warning(NULL, "提示", "程序已经在运行！");
        sema.release();
        exit(0);
    }
    sema.release();
    //
    ///-------------------------------------------| 确保只运行一次 |-------------------------------------------
    auto startTime = Utils::DateTime::getCurDateTimeStr().toStdString();
    LOG_EVT_INFO("开始加载屏幕:{}",startTime);
    // 加载屏幕 ------ Begin ------
    auto          screen_tool    = Utils::Screen::GetInstance();
    QThread      *splashscreen_t = new QThread();
    QSize         pic_size(screen_tool->GetScreenWidth(), screen_tool->GetScreenHeight()); // （启动动画）获取size
    SplashScreen *splashscreen = new SplashScreen(QPixmap(":/Images/jinquan.png").scaled(pic_size, Qt::IgnoreAspectRatio));
    splashscreen->moveToThread(splashscreen_t);
    QObject::connect(splashscreen_t, &QThread::finished, splashscreen_t, &QThread::deleteLater);
    QObject::connect(splashscreen_t, &QThread::started, splashscreen, &SplashScreen::setProgress);
    splashscreen_t->start();
    // 加载屏幕 ------  End  ------

    ///-------------------------------------------| 初始化control |-------------------------------------------
    //
    LOG_EVT_INFO("------init data_mgr");
    auto data_mgr = DataManager::getInstance();
    data_mgr->init();
    LOG_EVT_INFO("------init data_mgr->init");
    ControlManager::getInstance()->initControls();
    //
    ///-------------------------------------------| 初始化control |-------------------------------------------


    // 初始化qml ------ Begin ------
    const QUrl url(QStringLiteral("qrc:/QML/main.qml"));
    LOG_EVT_INFO("------init qml");
    qmlRegisterType<EnumTool>("EnumTool", 1, 0, "EnumTool");
    qmlRegisterSingletonType(QUrl("qrc:/QML/QmlEvent.qml"), "QmlEvents", 1, 0, "QmlEvents"); // qml文件单列注册，然后其他qml文件使用注册过的qml单列


    qmlRegisterType<ShopCartModel>("ShopCartModel", 1, 0, "ShopCartModel");
    qmlRegisterUncreatableType<ShopCartList>("ShopCartModel", 1, 0, "ShopCartList", QStringLiteral("ShopCartList should not be created in QML"));

    qmlRegisterType<GoodsDataModel>("GoodsDataModel", 1, 0, "GoodsDataModel");
    qmlRegisterUncreatableType<GoodsData>("GoodsDataModel", 1, 0, "GoodsData", QStringLiteral("GoodsData should not be created in QML"));

    qmlRegisterType<Annotation>("Annotation", 1, 0, "Annotation");
    LOG_EVT_INFO("------init Annotation");


    ConfigToolEnum::declareQML();
    LOG_EVT_INFO("------init ConfigToolEnum");
    BarcodeLabelScaleEnum::declareQML();
    LOG_EVT_INFO("------init BarcodeLabelScaleEnum");

    TagEditHelper::declareQML();
    LOG_EVT_INFO("------init TagEditHelper");
    QObject::connect(
        &engine, &QQmlApplicationEngine::objectCreated, &app,
        [url](const QObject *obj, const QUrl &objUrl)
        {
            if (!obj && url == objUrl)
                QCoreApplication::exit(-1);
        },
        Qt::QueuedConnection);
    LOG_EVT_INFO("------init connect");
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    Utils4Qml *utils4qml = Utils4Qml::getInstance();
    CameraControl *camera_control = ControlManager::getInstance()->getCameraControl();
    engine.addImageProvider(QLatin1String("FaceImg"), camera_control->image_provider_face_);
    engine.rootContext()->setContextProperty("CameraWorker2",camera_control->getCameraWorkerFace());
    engine.addImageProvider(QLatin1String("goodsImg"), camera_control->image_provider_4_goods_);
    engine.addImageProvider(QLatin1String("goodsImgSelected"), camera_control->image_provider_4_goods_selected_area_);
    // engine.addImageProvider(QLatin1String("ScanPayImg"), ControlManager::getInstance()->getPayMethodControl()->img_provider_weight_qr_code);
    engine.rootContext()->setContextProperty("cameraControl", camera_control);
    engine.rootContext()->setContextProperty("translatorControl", &translator);
    engine.rootContext()->setContextProperty("settingTool", ConfigTool::getInstance());
    engine.rootContext()->setContextProperty("configTool", ConfigTool::getInstance());
    engine.rootContext()->setContextProperty("utils4Qml", utils4qml);
    engine.rootContext()->setContextProperty("logMgr", LogMgr::getInst());
    LOG_EVT_INFO("------init logMgr");
    engine.rootContext()->setContextProperty("goodsControl", ControlManager::getInstance()->getGoodsControl());
    engine.rootContext()->setContextProperty("shopCartList", ControlManager::getInstance()->getShopCartList());
    engine.rootContext()->setContextProperty("orderControl", ControlManager::getInstance()->getOrderControl());
    engine.rootContext()->setContextProperty("payMethodControl", ControlManager::getInstance()->getPayMethodControl());
    engine.rootContext()->setContextProperty("businessAlarmControl", ControlManager::getInstance()->getBusinessAlarmControl());
    engine.rootContext()->setContextProperty("printerControl", ControlManager::getInstance()->getPrinterControl());
    engine.rootContext()->setContextProperty("netOrderControl", ControlManager::getInstance()->getNetOrderControl());
    LOG_EVT_INFO("------init netOrderControl");
    engine.rootContext()->setContextProperty("checkData", CheckData::getInstance());
    //engine.rootContext()->setContextProperty("goodsPromotionCtrl", ControlManager::getInstance()->getGoodsPromotionCtrl());
    engine.rootContext()->setContextProperty("goodsPromotionCtrl", GoodsPromotionCtrl::getInstance());
    engine.rootContext()->setContextProperty("memberControl", ControlManager::getInstance()->getMemberControl());
    engine.rootContext()->setContextProperty("weightingScaleControl", ControlManager::getInstance()->getWeightingScaleControl());
    engine.rootContext()->setContextProperty("supplierControl", ControlManager::getInstance()->getSupplierControl());
    engine.rootContext()->setContextProperty("mqttControl", ControlManager::getInstance()->getMqttControl());
    engine.rootContext()->setContextProperty("ttsControl", ControlManager::getInstance()->getTtsControl());
    engine.rootContext()->setContextProperty("updateCtrl", ControlManager::getInstance()->getUpdateCtrl());
    engine.rootContext()->setContextProperty("logCtrl", ControlManager::getInstance()->getLogCtrl());
    engine.rootContext()->setContextProperty("soundCtrl", ControlManager::getInstance()->getSoundCtrl());
    engine.rootContext()->setContextProperty("netStatusCtrl", ControlManager::getInstance()->getNetStatusCtrl());
    LOG_EVT_INFO("------init netStatusCtrl");
    engine.rootContext()->setContextProperty("barcodeLabelScaleCtrl", ControlManager::getInstance()->getBarcodeLabelScale());
    engine.rootContext()->setContextProperty("permissionCtrl", ControlManager::getInstance()->getPermissionControl());
    LOG_EVT_INFO("------init permissionCtrl");
    engine.rootContext()->setContextProperty("shopControl", ControlManager::getInstance()->getShopControl());
    engine.rootContext()->setContextProperty("dataManager", data_mgr);
    LOG_EVT_INFO("------init dataManager");
    engine.rootContext()->setContextProperty("goodsManager", DataManager::getInstance()->getGoodsMgrPtr());
    engine.rootContext()->setContextProperty("goodsKindManager", DataManager::getInstance()->getGoodsKindMgrPtr());
    engine.rootContext()->setContextProperty("virtualGoodsKindMgr", DataManager::getInstance()->getVirtualGoodsKindMgr());
    engine.rootContext()->setContextProperty("keyEvent", KeyEvent::getInstance());
    LOG_EVT_INFO("------init keyEvent");
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    engine.load(url);
    // 初始化qml ------  End  ------

    LOG_EVT_INFO("------init finished");
    // 关闭加载屏幕
    splashscreen->finish(nullptr);
    auto endTime = Utils::DateTime::getCurDateTimeStr().toStdString();
    LOG_EVT_INFO("关闭加载屏幕:{}",endTime);
    //    PainterTag::getInstance()->printerTest();
    if (QWindow *window = qobject_cast<QWindow *>(engine.rootObjects().at(0)))
    {
        TogglePressAndHold((HWND)window->winId(), false);
    }

    return app.exec();
}

bool TogglePressAndHold(HWND hWnd, bool enable)
{
    // The atom identifier and Tablet PC atom
    ATOM    atomID     = 0;
    LPCTSTR tabletAtom = (LPCTSTR) "MicrosoftTabletPenServiceProperty";

    // Get the Tablet PC atom ID
    atomID = GlobalAddAtom(tabletAtom);

    // If getting the ID failed, return false
    if (atomID == 0)
    {
        return false;
    }

    // Enable or disable the press and hold gesture
    if (enable)
    {
        // Try to enable press and hold gesture by
        // clearing the window property, return the result
        return RemoveProp(hWnd, tabletAtom);
    }
    else
    {
        // Try to disable press and hold gesture by
        // setting the window property, return the result
        return SetProp(hWnd, tabletAtom, (HANDLE)1);
    }
}
