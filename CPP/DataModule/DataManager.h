﻿#ifndef DATAMANAGER_H
#define DATAMANAGER_H

#include <QDebug>
#include <QObject>
#include <QString>
#include <QTimer>
#include <QVariant>
#include <map>
#include <memory>
#include <mutex>
#include <queue>
#include <shared_mutex>
#include <unordered_map>
#include <vector>
#include "DataModule/GoodsData.h"
#include "DataModule/GoodsKindData.h"
#include "DataModule/OrderData.h"
#include "DataModule/VirtualGoodsKindData.h"


class DataManager : public QObject
{
    Q_OBJECT
public:
    static DataManager *getInstance();

    void init();

    std::shared_ptr<GoodsData> getGoodsMgr()
    {
        return goods_manager;
    }

    GoodsData *getGoodsMgrPtr()
    {
        return goods_manager.get();
    }

    std::shared_ptr<GoodsKindData> getGoodsKindMgr()
    {
        return goods_kind_manager;
    }

    GoodsKindData *getGoodsKindMgrPtr()
    {
        return goods_kind_manager.get();
    }

    OrderDataMgr *getOrderDataMgr()
    {
        return order_data_.get();
    }

    VirtualGoodsKindData *getVirtualGoodsKindMgr()
    {
        return virtual_goods_kind_data_.get();
    }

    ~DataManager();

    LocalDataWorker *local_data_worker_        = nullptr;
    QThread         *local_data_worker_thread_ = nullptr;

    QTimer save_data_2_local_timer_;

    void saveData2Local();

private:
    explicit DataManager(QObject *parent = nullptr);

    std::shared_ptr<GoodsData>            goods_manager;
    std::shared_ptr<GoodsKindData>        goods_kind_manager;
    std::shared_ptr<OrderDataMgr>         order_data_;
    std::shared_ptr<VirtualGoodsKindData> virtual_goods_kind_data_;

    static std::unique_ptr<DataManager> singleton_;
    static std::mutex                   mutex_;
};


#endif // DATAMANAGER_H
