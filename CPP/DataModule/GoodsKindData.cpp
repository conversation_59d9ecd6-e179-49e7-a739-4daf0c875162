﻿#include "GoodsKindData.h"
#include "ControlModule/ControlManager.h"
#include "WorkerModule/LocalDataWorker.h"


//TODO 更新数据槽
GoodsKindData::GoodsKindData(QObject *parent) : QObject{parent}
{
    goods_kind_data_map_.reset(new goodsKindDataMap);

    auto default_goods_kind = ConfigTool::getInstance()->getSetting(ConfigEnum::kDefaultGoodsKind).toString();
    default_goods_kind_     = default_goods_kind;
}

void GoodsKindData::init()
{
    auto data_mgr = DataManager::getInstance();
    connect(this, &GoodsKindData::sigSaveGoodsKindData2Local, data_mgr->local_data_worker_, &LocalDataWorker::saveGoodsKindData);
    connect(this, &GoodsKindData::sigGetGoodsKindData2Local, data_mgr->local_data_worker_, &LocalDataWorker::getGoodsKindData);

    connect(data_mgr->local_data_worker_, &LocalDataWorker::sigSendGoodsKindData, this,
            [&](std::shared_ptr<goodsKindDataMap> data)
            {
                goods_kind_data_map_ = data;

                ControlManager::getInstance()->getGoodsControl()->goods_kind_init_status_ = 1;
                ControlManager::getInstance()->getGoodsControl()->checkIsGoodsLoaded();
            });

    connect(data_mgr->local_data_worker_, &LocalDataWorker::sigSendGoodsKindDataError, this,
            [&]()
            {
                ControlManager::getInstance()->getGoodsControl()->goods_kind_init_status_ = -1;
                ControlManager::getInstance()->getGoodsControl()->checkIsGoodsLoaded();
            });
}

bool GoodsKindData::addGoodsKind(GoodsKindInfo goods_kind_info)
{
    for (auto &goods_kind_data : *getGoodsKindData())
    {
        if (goods_kind_data.second.goods_kind_unique == goods_kind_info.goods_kind_unique)
            return false;
    }
    getGoodsKindData()->insert_or_assign(goods_kind_info.goods_kind_unique, goods_kind_info);
    return true;
}

bool GoodsKindData::deleteGoodsKind(QString goods_kind_unique)
{
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_data_, "尝试删除商品分类");
    auto tmp = getGoodsKindData()->find(goods_kind_unique.toLongLong());
    if (tmp != getGoodsKindData()->end())
    {
        for (auto goods_kind_data_map_iter = getGoodsKindData()->begin(); goods_kind_data_map_iter != getGoodsKindData()->end(); ++goods_kind_data_map_iter)
        {
            if (goods_kind_data_map_iter->second.goods_kind_parunique == tmp->second.goods_kind_id)
            {
                goods_kind_data_map_iter = getGoodsKindData()->erase(goods_kind_data_map_iter);
            }
        }
        getGoodsKindData()->erase(tmp);
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_data_, "删除商品分类 成功");
        return true;
    }
    else
    {
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_data_, "删除商品分类 失败");
        return false;
    }
}

bool GoodsKindData::deleteAllGoodsKind()
{
    getGoodsKindData()->clear();
    return true;
}

QString GoodsKindData::defaultGoodsKind()
{
    return default_goods_kind_;
}

void GoodsKindData::setDefaultGoodsKind(QString default_goods_kind)
{
    default_goods_kind_ = default_goods_kind;

    ConfigTool::getInstance()->setSetting(ConfigEnum::kDefaultGoodsKind, default_goods_kind);

    emit defaultGoodsKindChanged();
}

void GoodsKindData::changeGoodsKindNameByKindUnique(QVariant goods_kind_unique, QVariant goods_name)
{
    for (auto &goods_kind_data : *getGoodsKindData())
    {
        if (goods_kind_data.second.goods_kind_unique == goods_kind_unique.toULongLong())
        {
            goods_kind_data.second.goods_kind_name = goods_name.toString();
            break;
        }
    }
}

bool GoodsKindData::getGoodsKindInfo(unsigned long long goods_kind_id, GoodsKindInfo &goods_kind_info)
{
    if (getGoodsKindData()->find(goods_kind_id) != getGoodsKindData()->end())
    {
        goods_kind_info = getGoodsKindData()->at(goods_kind_id);
        return true;
    }
    return false;
}

std::shared_ptr<goodsKindDataMap> GoodsKindData::getGoodsKindData()
{
    return goods_kind_data_map_;
}

void GoodsKindData::setGoodsKindData(goodsKindDataMap *goods_kind_data_map)
{
    goods_kind_data_map_.reset(goods_kind_data_map);
}

QVariant GoodsKindData::getAllGoodsKind4Qml()
{
    Json json;
    for (const auto &data : *getGoodsKindData())
        json.push_back(data.second);

    return QVariant::fromValue(QString::fromStdString(std::string(json.dump(0))));
}

QVariant GoodsKindData::getRootGoodsKind4Qml()
{
    Json json;
    for (const auto &data : *getGoodsKindData())
    {
        if (data.second.goods_kind_parunique == 0)
            json.push_back(data.second);
    }
    return QVariant::fromValue(QString::fromStdString(std::string(json.dump(0))));
}

QVariant GoodsKindData::getSubGoodsKind4Qml(QVariant parent_goods_kind_unique)
{
    Json json;
    for (const auto &data : *getGoodsKindData())
    {
        if (data.second.goods_kind_parunique == parent_goods_kind_unique.toULongLong())
            json.push_back(data.second);
    }
    return QVariant::fromValue(QString::fromStdString(std::string(json.dump(0))));
}

QVariant GoodsKindData::getGoodsKindByKindId4Qml(QVariant goods_kind_id)
{
    Json json;
    auto goods_kind_id_num = goods_kind_id.toULongLong();

    for (auto &goods_kind_data : *getGoodsKindData())
    {
        if (goods_kind_data.second.goods_kind_parunique == goods_kind_id_num)
        {
            json.push_back(goods_kind_data.second);
        }
    }
    return QVariant::fromValue(QString::fromStdString(std::string(json.dump(0))));
}

QVariant GoodsKindData::getGoodsKindByKindUnique4Qml(QVariant goods_kind_unique)
{
    Json json;

    auto tmp_goods_kind_unique = goods_kind_unique.toLongLong();
    if (getGoodsKindData()->find(tmp_goods_kind_unique) != getGoodsKindData()->end())
    {
        json = getGoodsKindData()->at(tmp_goods_kind_unique);
    }

    return QVariant::fromValue(QString::fromStdString(std::string(json.dump(0))));
}

QVariant GoodsKindData::getHomepageGoodsKind4Qml()
{
    Json json;

    std::multimap<int, GoodsKindInfo> tmp_map;
    for (const auto &data : *getGoodsKindData())
    {
        if (data.second.goods_kind_parunique == 99990)
            tmp_map.insert(std::make_pair(data.second.goods_kind_order, data.second));
    }

    for (auto &cur_it : tmp_map)
        json.push_back(cur_it.second);

    return QVariant::fromValue(QString::fromStdString(std::string(json.dump(0))));
}

QVariant GoodsKindData::getHomepageDefaultGoodsKind4Qml()
{
    Json json;

    std::multimap<int, GoodsKindInfo> tmp_map;
    for (const auto &data : *getGoodsKindData())
    {
        if (data.second.goods_kind_parunique == 99990)
            tmp_map.insert(std::make_pair(data.second.goods_kind_order, data.second));
    }

    for (auto &cur_it : tmp_map)
    {
        json.push_back(cur_it.second);
        break;
    }
    return QVariant::fromValue(QString::fromStdString(std::string(json.dump())));
}

void GoodsKindData::saveGoodsKindData2Local_Thread()
{
    std::shared_ptr<std::vector<GoodsKindInfo>> goods_kind_data;

    goods_kind_data.reset(new std::vector<GoodsKindInfo>);

    for (auto [item_first, item_second] : *goods_kind_data_map_)
    {
        goods_kind_data->push_back(item_second);
    }
    emit sigSaveGoodsKindData2Local(goods_kind_data);
}

void GoodsKindData::getLocalGoodsKindData_Thread()
{
    emit sigGetGoodsKindData2Local();
}
