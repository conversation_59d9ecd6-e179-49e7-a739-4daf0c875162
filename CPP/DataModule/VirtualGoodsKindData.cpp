﻿#include "VirtualGoodsKindData.h"
#include "ControlModule/ControlManager.h"
#include "DataModule/DataManager.h"
#include "WorkerModule/LocalDataWorker.h"

using namespace std;

VirtualGoodsKindData::VirtualGoodsKindData(QObject *parent) : QObject{parent}
{
    virtual_goods_kind_info_map_.reset(new VGoodsKindDataMap);
    LOG_EVT_INFO("setVGoodsKind清空virtual_goods_kind_info_map_");
    vector<VirtualGoodsKindInfo> virtual_goods_kind_info_vec;

    for (auto virtual_goods_kind_info_vec_item : virtual_goods_kind_info_vec)
    {
        virtual_goods_kind_info_map_->insert_or_assign(virtual_goods_kind_info_vec_item.goods_kind_unique, virtual_goods_kind_info_vec_item);
    }
}

void VirtualGoodsKindData::init()
{
    const auto data_mgr = DataManager::getInstance();
    connect(this, &VirtualGoodsKindData::sigSaveVGoodsKindData2Local, data_mgr->local_data_worker_, &LocalDataWorker::saveVGoodsKindData);
    connect(this, &VirtualGoodsKindData::sigGetVGoodsKindData2Local, data_mgr->local_data_worker_, &LocalDataWorker::getVGoodsKindData);

    connect(data_mgr->local_data_worker_, &LocalDataWorker::sigSendVGoodsKindData, this,
            [&](std::shared_ptr<VGoodsKindDataMap> data)
            {
                virtual_goods_kind_info_map_ = data;

                for (const auto& pair : *virtual_goods_kind_info_map_)
                {
                    const auto& barcode_vec = pair.second.goods_barode_vec_; // 注意这里修正了变量名
                    for (const auto& barcode : barcode_vec)
                    {
                        //LOG_EVT_INFO("1条形码: {}", barcode.toStdString()); // 假设barcode是一个可以转换为std::string的类型
                    }
                }


                ControlManager::getInstance()->getGoodsControl()->v_goods_kind_init_status_ = 1;
                ControlManager::getInstance()->getGoodsControl()->checkIsGoodsLoaded();
            });

    connect(data_mgr->local_data_worker_, &LocalDataWorker::sigSendVGoodsKindDataError, this,
            [&]()
            {
                ControlManager::getInstance()->getGoodsControl()->v_goods_kind_init_status_ = -1;
                ControlManager::getInstance()->getGoodsControl()->checkIsGoodsLoaded();
            });
}

void VirtualGoodsKindData::addVirualGoodsKind(VirtualGoodsKindInfo virtual_goods_kind_info)
{

    virtual_goods_kind_info_map_->insert_or_assign(virtual_goods_kind_info.goods_kind_unique, virtual_goods_kind_info);
}

void VirtualGoodsKindData::addVirualGoodsKindList(std::vector<VirtualGoodsKindInfo> virtual_goods_kind_info_vec)
{
    for (const auto &virtual_goods_kind_info_vec_item : virtual_goods_kind_info_vec)
    {
        addVirualGoodsKind(virtual_goods_kind_info_vec_item);
    }
}

void VirtualGoodsKindData::delVirualGoodsKind(unsigned long long v_goods_kind_unique)
{
    auto v_goods_kind_info_iter = virtual_goods_kind_info_map_->find(v_goods_kind_unique);
    if (v_goods_kind_info_iter != virtual_goods_kind_info_map_->end())
        virtual_goods_kind_info_map_->erase(v_goods_kind_info_iter);
}

void VirtualGoodsKindData::changeVirualGoodsKind(VirtualGoodsKindInfo virtual_goods_kind_info)
{
    auto v_goods_kind_info_iter = virtual_goods_kind_info_map_->find(virtual_goods_kind_info.goods_kind_unique);
    if (v_goods_kind_info_iter != virtual_goods_kind_info_map_->end())
    {
        v_goods_kind_info_iter->second.goods_barode_vec_ = virtual_goods_kind_info.goods_barode_vec_;
    }
}

void VirtualGoodsKindData::saveVGoodsKindData2Local_Thread()
{
    std::shared_ptr<std::vector<VirtualGoodsKindInfo>> v_goods_kind_data;
    v_goods_kind_data.reset(new std::vector<VirtualGoodsKindInfo>);

    for (const auto &[item_first, item_second] : *virtual_goods_kind_info_map_)
    {
        v_goods_kind_data->push_back(item_second);
    }

    emit sigSaveVGoodsKindData2Local(v_goods_kind_data);
}

void VirtualGoodsKindData::getLocalVGoodsKindData_Thread()
{
    emit sigGetVGoodsKindData2Local();
}

void VirtualGoodsKindData::getVirualGoodsKind(QJSValue callback)
{
    Json result;
    for (auto &virtual_goods_kind_info : *getVGoodsKind())
    {
        result.push_back(virtual_goods_kind_info.second);
    }

    if (callback.isCallable())
    {
        QJSValueList arglist;
        arglist.push_back(QJSValue(QString::fromStdString(result.dump())));
        callback.call(arglist);
    }
}

void VirtualGoodsKindData::getGoodsByVirtualGoodsKindUnique(QJSValue callback, QVariant virtual_goods_kind_unique, QVariant is_need_nocode_goods)
{
    auto ret_goods_kind_iter = virtual_goods_kind_info_map_->find(virtual_goods_kind_unique.toULongLong());

    if (ret_goods_kind_iter == virtual_goods_kind_info_map_->end())
        return;

    auto goods_json = DataManager::getInstance()->getGoodsMgr()->getGoodsByVirtualKindJson(ret_goods_kind_iter->second, is_need_nocode_goods);
    if (callback.isCallable())
    {
        QJSValueList arglist;
        arglist.push_back(QJSValue(goods_json.toString()));
        callback.call(arglist);
    }
}

void VirtualGoodsKindData::clearAllVirtualGoodsKind()
{
    virtual_goods_kind_info_map_->clear();
}

std::shared_ptr<VGoodsKindDataMap> VirtualGoodsKindData::getVGoodsKind()
{
    return virtual_goods_kind_info_map_;
}

void VirtualGoodsKindData::setVGoodsKind(VGoodsKindDataMap *v_goods_kind_data_map)
{
    virtual_goods_kind_info_map_.reset(v_goods_kind_data_map);
    LOG_EVT_INFO("setVGoodsKind清空virtual_goods_kind_info_map_");
}
