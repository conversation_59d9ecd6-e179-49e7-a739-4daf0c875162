﻿#ifndef ORDERDATA_H
#define ORDERDATA_H

#include <QObject>
#include <json.hpp>
#include "DataModule/DataStruct.h"

using GoodsOrderMap = std::unordered_map<unsigned long long, OrderInfo>;

class OrderDataMgr : public QObject
{
    Q_OBJECT
public:
    explicit OrderDataMgr(QObject *parent = nullptr);

    void init();

    void saveOrderData2Local_Thread();

    bool addOrder(OrderInfo order_info);

    void clearUploadedOrder();

    OrderInfo &getOrderById(unsigned long long id);

    std::vector<OrderInfo> getNotUploadOrders();

    GoodsOrderMap goods_order_map_;
signals:
    void sigSaveOrderData2Local(std::shared_ptr<std::vector<OrderInfo>> order_data);

};

#endif // ORDERDATA_H
