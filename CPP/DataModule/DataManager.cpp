﻿#include "DataManager.h"

#include <QThread>
#include <random>
#include <utility>
#include <vector>
#include "ControlModule/ControlManager.h"
#include "DataModule/GoodsData.h"
#include "DataModule/GoodsKindData.h"
#include "DataModule/VirtualGoodsKindData.h"
#include "WorkerModule/LocalDataWorker.h"

std::unique_ptr<DataManager> DataManager::singleton_;
std::mutex                   DataManager::mutex_;

using namespace std;

DataManager *DataManager::getInstance()
{
    std::lock_guard<std::mutex> lock(mutex_);

    if (singleton_ == nullptr)
    {
        singleton_.reset(new DataManager());
    }
    return singleton_.get();
}

void DataManager::init()
{
    goods_manager->init();
    goods_kind_manager->init();
    virtual_goods_kind_data_->init();
}

DataManager::DataManager(QObject *parent) : QObject{parent}
{
    LOG_DATA_INFO("DataManager() B");

    goods_manager.reset(new GoodsData());
    goods_kind_manager.reset(new GoodsKindData());
    order_data_.reset(new OrderDataMgr());
    virtual_goods_kind_data_.reset(new VirtualGoodsKindData());

    local_data_worker_        = new LocalDataWorker;
    local_data_worker_thread_ = new QThread;

    connect(local_data_worker_thread_, &QThread::finished, local_data_worker_, &LocalDataWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(local_data_worker_, &LocalDataWorker::destroyed, local_data_worker_thread_, &QThread::deleteLater); // 工作对象销毁,销毁工作线程

    local_data_worker_->moveToThread(local_data_worker_thread_);
    local_data_worker_thread_->start();

    //10分钟保存一次
    save_data_2_local_timer_.setInterval(1000 * 60 * 10);

    connect(&save_data_2_local_timer_, &QTimer::timeout,
            [=]
            {
                auto shop_ctrl = ControlManager::getInstance()->getShopControl();
                if (shop_ctrl->isLogin())
                {
                    saveData2Local();
                }
            });

    save_data_2_local_timer_.start();

    LOG_DATA_INFO("DataManager() E");
}

DataManager::~DataManager()
{
}

void DataManager::saveData2Local()
{
    virtual_goods_kind_data_->saveVGoodsKindData2Local_Thread();
    goods_kind_manager->saveGoodsKindData2Local_Thread();
    goods_manager->saveGoodsData2Local_Thread();
}

int GoodsInfo::getShopBeansNum()
{
    auto &goods_kind_info = getGoodsKindInfo();
    return 0;
}

int GoodsInfo::getcommissionNum()
{
    auto &goods_kind_info = getGoodsKindInfo();
    return 0;
}

const GoodsKindInfo GoodsInfo::getGoodsKindInfo()
{
    GoodsKindInfo goods_kind_info;
    DataManager::getInstance()->getGoodsKindMgr()->getGoodsKindInfo(goods_kind_unique, goods_kind_info);
    return goods_kind_info;
}

bool GoodsInfo::isWeight()
{
    if (goodsChengType == 1)
        return true;
    return false;
}
