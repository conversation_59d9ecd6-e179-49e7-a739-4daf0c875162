﻿#include "OrderData.h"
#include <map>

#include "DataManager.h"
#include "WorkerModule/LocalDataWorker.h"

using namespace std;

OrderDataMgr::OrderDataMgr(QObject *parent) : QObject{parent}
{
}

void OrderDataMgr::init()
{
    // const auto data_mgr = DataManager::getInstance();
    // connect(this, &OrderDataMgr::sigSaveOrderData2Local, data_mgr->local_data_worker_, &LocalDataWorker::saveGoodsKindData);
}

void OrderDataMgr::saveOrderData2Local_Thread()
{
    std::shared_ptr<std::vector<OrderInfo>> order_data;
    order_data.reset(new std::vector<OrderInfo>);

    for (auto &[item_first, item_second] : goods_order_map_)
    {
        order_data->push_back(item_second);
    }

    emit sigSaveOrderData2Local(order_data);
}

bool OrderDataMgr::addOrder(OrderInfo order_info)
{
    goods_order_map_.insert(std::make_pair(order_info.sale_list_unique, order_info));
    clearUploadedOrder();
    return true;
}

void OrderDataMgr::clearUploadedOrder()
{
    for (auto goods_order_map_iter = goods_order_map_.begin(); goods_order_map_iter != goods_order_map_.end();)
    {
        if (goods_order_map_iter->second.sale_list_same_type == 1)
        {
            goods_order_map_iter = goods_order_map_.erase(goods_order_map_iter);
            continue;
        }
        ++goods_order_map_iter;
    }
}

OrderInfo &OrderDataMgr::getOrderById(unsigned long long id)
{
    OrderInfo order_info;

    if (goods_order_map_.find(id) == goods_order_map_.end())
        return order_info;

    return goods_order_map_.at(id);
}

std::vector<OrderInfo> OrderDataMgr::getNotUploadOrders()
{
    std::vector<OrderInfo> order_infos;

    for (pair<unsigned long long, OrderInfo> goods_order : goods_order_map_)
    {
        if (goods_order.second.sale_list_same_type == 2)
        {
            order_infos.push_back(goods_order.second);
        }
    }

    return order_infos;
}

long OrderInfo::getGoodsCount()
{
    long detail_count = 0;
    for (auto &order_detail_info : order_detail_infos)
    {
        detail_count += order_detail_info.sale_list_detail_count;
    }
    return detail_count;
}

QString OrderInfo::getPayMethodStr(int sale_list_payment)
{
    switch (sale_list_payment)
    {
    case (int)PayMethodEnum::PAY_METHOD__CASH:
        return QObject::tr("现金");
    case (int)PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE:
        return QObject::tr("支付宝");
    case (int)PayMethodEnum::PAY_METHOD__WECHAT_OFFLINE:
        return QObject::tr("微信");
    case (int)PayMethodEnum::PAY_METHOD__BANK_CARD:
        return QObject::tr("银行卡");
    case (int)PayMethodEnum::PAY_METHOD__VIPCARD:
        return QObject::tr("储值卡");
    case 6:
        return QObject::tr("美团外卖");
    case 7:
        return QObject::tr("饿了么外卖");
    case (int)PayMethodEnum::PAY_METHOD__COMBINED:
        return QObject::tr("组合支付");
    case 9:
        return QObject::tr("免密支付");
    case 10:
        return QObject::tr("积分兑换");
    case 11:
        return QObject::tr("百货豆");
    case 12:
        return QObject::tr("拉卡拉");
    case (int)PayMethodEnum::PAY_METHOD__YI_TONG:
        return QObject::tr("免密支付");
        return "易通付款码支付";
    case 14:
        return QObject::tr("合利宝刷卡");
    case 101:
        return QObject::tr("小程序");
    case 102:
        return QObject::tr("刷脸");

    default:
        break;
    }
    return "error";
}

QString OrderInfo::getOrderPayMethodStr()
{
    return getPayMethodStr(this->sale_list_payment);
}
