﻿#ifndef VIRTUALGOODSKINDDATA_H
#define VIRTUALGOODSKINDDATA_H

#include <QJSValue>
#include <QObject>
#include "DataModule/DataStruct.h"


class VirtualGoodsKindData : public QObject
{
    Q_OBJECT
public:
    VirtualGoodsKindData(QObject *parent = nullptr);
    void init();

    void addVirualGoodsKind(VirtualGoodsKindInfo virtual_goods_kind_info);
    void addVirualGoodsKindList(std::vector<VirtualGoodsKindInfo> virtual_goods_kind_info_vec);
    void delVirualGoodsKind(unsigned long long v_goods_kind_unique);
    void changeVirualGoodsKind(VirtualGoodsKindInfo virtual_goods_kind_info);

    void saveVGoodsKindData2Local_Thread();
    void getLocalVGoodsKindData_Thread();

    //获取虚拟分类
    Q_INVOKABLE void getVirualGoodsKind(QJSValue callback);

    //根据虚拟分类获取商品
    Q_INVOKABLE void getGoodsByVirtualGoodsKindUnique(QJSValue callback, QVariant virtual_goods_kind_unique, QVariant is_need_nocode_goods = true);

    void clearAllVirtualGoodsKind();

    std::shared_ptr<VGoodsKindDataMap> getVGoodsKind();
    void                               setVGoodsKind(VGoodsKindDataMap *v_goods_kind_data_map);

    std::shared_ptr<VGoodsKindDataMap> virtual_goods_kind_info_map_;


signals:
    void defaultGoodsKindChanged();

    void sigSaveVGoodsKindData2Local(std::shared_ptr<std::vector<VirtualGoodsKindInfo>> v_goods_kind_data);
    void sigGetVGoodsKindData2Local();
};

#endif // VIRTUALGOODSKINDDATA_H
