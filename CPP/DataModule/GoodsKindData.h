﻿#ifndef GOODSKINDDATA_H
#define GOODSKINDDATA_H

#include <QObject>
#include <memory>
#include "DataModule/DataStruct.h"


class GoodsKindData : public QObject
{
    Q_OBJECT

    Q_PROPERTY(QString defaultGoodsKind READ defaultGoodsKind WRITE setDefaultGoodsKind NOTIFY defaultGoodsKindChanged FINAL)

public:
    GoodsKindData(QObject *parent = nullptr);

    void init();
    void saveGoodsKindData2Local_Thread();
    void getLocalGoodsKindData_Thread();

    bool addGoodsKind(GoodsKindInfo goods_kind_info);
    bool deleteGoodsKind(QString goods_kind_unique);
    bool changeGoodsKind(GoodsKindInfo goods_kind_info);
    bool deleteAllGoodsKind();

    QString defaultGoodsKind();
    void    setDefaultGoodsKind(QString default_goods_kind);

    Q_INVOKABLE void changeGoodsKindNameByKindUnique(QVariant goods_kind_unique, QVariant goods_name);

    Q_INVOKABLE bool getGoodsKindInfo(unsigned long long goods_kind_id, GoodsKindInfo &goods_kind_info);

    std::shared_ptr<goodsKindDataMap> getGoodsKindData();
    void                              setGoodsKindData(goodsKindDataMap *goods_kind_data_map);

    Q_INVOKABLE QVariant getAllGoodsKind4Qml();

    Q_INVOKABLE QVariant getRootGoodsKind4Qml();
    Q_INVOKABLE QVariant getSubGoodsKind4Qml(QVariant parent_goods_kind_unique);

    Q_INVOKABLE QVariant getGoodsKindByKindId4Qml(QVariant goods_kind_id);
    Q_INVOKABLE QVariant getGoodsKindByKindUnique4Qml(QVariant goods_kind_unique);

    Q_INVOKABLE QVariant getHomepageGoodsKind4Qml();

    Q_INVOKABLE QVariant getHomepageDefaultGoodsKind4Qml();

    std::shared_ptr<goodsKindDataMap> goods_kind_data_map_;
private:
    QString default_goods_kind_;

signals:
    void defaultGoodsKindChanged();

    void sigSaveGoodsKindData2Local(std::shared_ptr<std::vector<GoodsKindInfo>> goods_kind_data);
    void sigGetGoodsKindData2Local();

};

#endif // GOODSKINDDATA_H
