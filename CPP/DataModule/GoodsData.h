﻿#ifndef GOODSDATA_H
#define GOODSDATA_H

#include <QObject>
#include <memory>
#include <set>
#include "DataModule/DataStruct.h"

class LocalDataWorker;
class GoodsData : public QObject
{
    Q_OBJECT

    Q_PROPERTY(bool isShowNoCodeGoods READ getIsShowNoCodeGoods WRITE setIsShowNoCodeGoods NOTIFY sigIsShowNoCodeGoodsChanged FINAL)
    Q_PROPERTY(bool isSwapNoCodeGoods READ isSwapNoCodeGoods WRITE setIsSwapNoCodeGoods NOTIFY isSwapNoCodeGoodsChanged FINAL)

    friend class GoodsDataModel;
    friend class ShopCartList;
    friend class TagEditHelper;

public:
    GoodsData(QObject *parent = nullptr);

    void init();

    bool addGoods(GoodsInfo goods_info);
    bool addGoodsByList(const std::vector<GoodsInfo> goods_info_queue);
    
    bool updateGoodsByList(const std::vector<GoodsInfo> &goods_info_queue);

    bool setGoodsInfo(const GoodsInfo &goods_info);
    bool deleteGoodsByBarcode(QString goods_barcode);
    bool deleteGoodsByBarcodeList(const std::vector<std::tuple<QString, QString>> &goods_id_and_barcode_vec);
    bool deleteAllGoods();

    bool getGoodsByBarcode(QString barcode, GoodsInfo &goods_info);

    GoodsInfo getNoCodeGoods(bool is_weight = false);


    /*!
     * \brief getGoodsPtrByBarcode 根据条码获取商品指针
     * \param goods_barcode 条码
     * \param goods_info 商品指针
     * \return
     */
    bool getGoodsPtrByBarcode(QString goods_barcode, GoodsInfo *&goods_info);

    //保存商品信息到本地
    void saveGoodsData2Local_Thread();
    void getLocalGoodsData_Thread();

        
    std::shared_ptr<goodsDataMap> getGoodsData();
    std::shared_ptr<std::vector<GoodsInfo>> getGoodsDataCopy() const;

    void setGoodsData(goodsDataMap *goods_data_map);

    //根据商品分类获取商品JSON列表
    Q_INVOKABLE QVariant getGoodsByKind4Qml(QVariant kind_unique, QVariant is_need_nocode_goods = true);

    //获取虚拟分类下的商品
    Q_INVOKABLE QVariant getGoodsByVirtualKindJson(const VirtualGoodsKindInfo &virtual_kind_info, QVariant is_need_nocode_goods = true);

    Q_INVOKABLE QVariant getGoodsByBarcode4Qml(QVariant barcode);

    Q_INVOKABLE QVariant getGoodsByGoodId4Qml(QVariant goodId);

    Q_INVOKABLE QVariant getForeignGoodsJsonListByBarcode(QVariant barcode);


    Q_INVOKABLE bool getIsShowNoCodeGoods();
    Q_INVOKABLE void setIsShowNoCodeGoods(QVariant is_show_no_code_goods);

    bool isSwapNoCodeGoods();
    void setIsSwapNoCodeGoods(bool is_swap);

    Q_INVOKABLE QString generateCustomBarcode(bool is_weight = false);

    Q_INVOKABLE QString contrastGenerateCustomBarcode(bool is_weight = false,QVariant barcode1="",QVariant barcode2="");

    ///-------------------------------------------| 单位 |-------------------------------------------
    //
    bool                 addGoodsUnit(GoodsUnitInfo goods_unit_info);
    bool                 deleteGoodsUnit(unsigned long goods_unit_id);
    bool                 updateGoodsUnit(GoodsUnitInfo goods_unit_info);
    void                 clearGoodsUnit();
    Q_INVOKABLE QVariant getAllGoodsUnitJson();
    //
    ///-------------------------------------------| 单位 |-------------------------------------------

    bool                          is_show_no_code_goods_ = true;
    std::shared_ptr<goodsDataMap> goods_data_map_no_code_;


    bool is_show_no_code_weight_in_recognition_ = true;

private:
    std::unordered_map<unsigned long, GoodsUnitInfo> goods_unit_info_map_;

    std::shared_ptr<goodsDataMap> goods_data_map_;

    bool is_swap_no_code_goods_ = false;


#ifdef _RECOGNITION_
    ///-------------------------------------------| TAG |-------------------------------------------
    //
public:
    void initTagList();
    void saveTagList();

    void addTag(std::string barcode, std::vector<std::string> goods_tag_list);
    void delTag(std::string barcode, std::vector<std::string> goods_tag_list);
    void clearTag(std::string barcode);

    void refreshTagMap();

    //显示用
    void addRecognitionBarcodeTagRatioMap(const BarcodeTagRatioMap &barcode_tag_ratio_map);

    //保存最后识别状态
    void setTagRatioVec(TagRatioVec tag_ratio_vec);

    //尝试根据识别列表添加TAG
    Q_INVOKABLE void tryAddTagByRecognition(QString goods_barcode);

    //根据识别列表删除绑定
    Q_INVOKABLE void tryDelTagByRecognition(QString goods_barcode);

    //根据识别列表添加TAG
    void addTagByRecognition(QString goods_barcode);

    //根据识别列表删除TAG
    void delTagByRecognition(QString goods_barcode);

    //显示用(最后三次识别)
    std::vector<BarcodeTagRatioMap> barcode_tag_ratio_map_vec_;

    //最后识别状态
    TagRatioVec last_tag_ratio_vec_;

private:

    // TAG 条码
    std::map<std::string, std::set<std::string>> tag_map_;

    //存储状态TAG (条码 TAG LIST)
    std::vector<GoodsTag> goods_tag_list_;
    //
    ///-------------------------------------------| TAG |-------------------------------------------
#endif

signals:
    void sigIsShowNoCodeGoodsChanged();
    void isSwapNoCodeGoodsChanged();

    void preItemInsert(QString goods_barcode);
    void postItemInserted();

    void preItemRemoved(QString goods_barcode);
    void postItemRemoved();

    void preItemReset();
    void postItemReset();

    void postRowChanged(QString goods_barcode);

    void sigSaveGoodsData2Local(std::shared_ptr<std::vector<GoodsInfo>> goods_data);
    void sigGetGoodsData2Local();

};

#endif // GOODSDATA_H
