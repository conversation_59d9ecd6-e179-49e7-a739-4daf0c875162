﻿#ifndef DATASTRUCT_H
#define DATASTRUCT_H
#include <QString>
#include <vector>
#include "EnumTool.h"
#include "GlobeData.h"
#include "json-qt.hpp"



class GoodsKindInfo;

/*!
 * \brief The GoodsInfo class 商品信息
 */
struct GoodsInfo
{
    unsigned long long       goods_id             = 0;  // --商品编号
    unsigned long long       beanTop              = 0;  //
    unsigned long long       foreign_key          = 0;  // --商品包装分类外键
    unsigned long long       goods_kind_parunique = 0;  //
    float                    goods_count          = .0; // --商品库存数量
    float                    goods_sale_price     = .0; // --商品售价
    QString                  goods_standard;            // --商品规格
    QString                  goods_cus_price;           // --会员价 ??
    QString                  update_time;               // --最后一次更新时间
    QString                  goods_barcode;             // --商品条码
    unsigned long long       goods_points         = 0;  // --商品积分
    float                    goods_web_sale_price = .0; // --商品线上售价
    float                    goods_discount       = .0; // --商品折扣率
    unsigned long long       countBeans           = 0;  //
    unsigned long long       goodsChengType       = 0;  // --称重商品类型：0、按件；1、按重量 ?????????
    unsigned long long       sameType             = 0;  // --1:已同步；2：未同步
    QString                  goods_name;                // --商品名称
    unsigned long long       shop_unique    = 0;        // --店铺唯一标识
    unsigned long long       pc_shelf_state = 1;        // --pc收银上架状态：1、已上架；2、已下架
    unsigned long long       shelfState     = 1;        // --web上架状态：1、已上架；2、已下架
    float                    goods_sold     = .0;       // --已卖出的数量
    float                    goods_in_price = .0;       // --商品进价
    float                    goodStockPrice = .0;       // --最近入库价
    QString                  goods_picturepath;         // --商品图片保存路径
    //QString                  goods_life = "10";          // --商品保质天数
    float                    goods_life = 0;          // --商品保质天数
    QString                  goods_unit;                // --商品计价单位
    QString                  goods_address;             // --商品生产地
    QString                  goods_alias;               // --商品别名
    unsigned long long       goods_contain = 0;         // --商品包含子商品数量
    QString                  goods_remarks;             // --商品特殊说明
    QString                  goods_promotion   = 0;     // --商品促销状态：1，不促销，2促销
    unsigned long long       goods_hits        = 0;     // --点击率
    unsigned long long       goods_kind_unique = 0;     // --商品分类唯一标识符
    QString                  default_supplier_unique;   // --默认供应商编号
    unsigned long long       beanTimes = 0;             //
    QString                  goods_brand;               // --商品品牌
    unsigned long long       giveCount = 0;             //

    //std::vector<std::string> tagList;

    int getShopBeansNum();
    int getcommissionNum();

    const GoodsKindInfo getGoodsKindInfo();

    bool isWeight();

    friend void to_json(nlohmann::json &nlohmann_json_j, const GoodsInfo &nlohmann_json_t)
    {
        nlohmann_json_j["goods_id"]                = nlohmann_json_t.goods_id;
        nlohmann_json_j["beanTop"]                 = nlohmann_json_t.beanTop;
        nlohmann_json_j["foreign_key"]             = nlohmann_json_t.foreign_key;
        nlohmann_json_j["goods_kind_parunique"]    = nlohmann_json_t.goods_kind_parunique;
        nlohmann_json_j["goods_count"]             = nlohmann_json_t.goods_count;
        nlohmann_json_j["goods_sale_price"]        = nlohmann_json_t.goods_sale_price;
        nlohmann_json_j["goods_standard"]          = nlohmann_json_t.goods_standard.toStdString();
        nlohmann_json_j["goods_cus_price"]         = nlohmann_json_t.goods_cus_price.toStdString();
        nlohmann_json_j["update_time"]             = nlohmann_json_t.update_time.toStdString();
        nlohmann_json_j["goods_barcode"]           = nlohmann_json_t.goods_barcode.toStdString();
        nlohmann_json_j["goods_points"]            = nlohmann_json_t.goods_points;
        nlohmann_json_j["goods_web_sale_price"]    = nlohmann_json_t.goods_web_sale_price;
        nlohmann_json_j["goods_discount"]          = nlohmann_json_t.goods_discount;
        nlohmann_json_j["countBeans"]              = nlohmann_json_t.countBeans;
        nlohmann_json_j["goodsChengType"]          = nlohmann_json_t.goodsChengType;
        nlohmann_json_j["sameType"]                = nlohmann_json_t.sameType;
        nlohmann_json_j["goods_name"]              = nlohmann_json_t.goods_name.toStdString();
        nlohmann_json_j["shop_unique"]             = nlohmann_json_t.shop_unique;
        nlohmann_json_j["pc_shelf_state"]          = nlohmann_json_t.pc_shelf_state;
        nlohmann_json_j["shelfState"]              = nlohmann_json_t.shelfState;
        nlohmann_json_j["goods_sold"]              = nlohmann_json_t.goods_sold;
        nlohmann_json_j["goods_in_price"]          = nlohmann_json_t.goods_in_price;
        nlohmann_json_j["goodStockPrice"]          = nlohmann_json_t.goodStockPrice;
        nlohmann_json_j["goods_picturepath"]       = nlohmann_json_t.goods_picturepath.toStdString();
        nlohmann_json_j["goods_life"]              = nlohmann_json_t.goods_life;
        nlohmann_json_j["goods_unit"]              = nlohmann_json_t.goods_unit.toStdString();
        nlohmann_json_j["goods_address"]           = nlohmann_json_t.goods_address.toStdString();
        nlohmann_json_j["goods_alias"]             = nlohmann_json_t.goods_alias.toStdString();
        nlohmann_json_j["goods_contain"]           = nlohmann_json_t.goods_contain;
        nlohmann_json_j["goods_remarks"]           = nlohmann_json_t.goods_remarks.toStdString();
        nlohmann_json_j["goods_promotion"]         = nlohmann_json_t.goods_promotion.toStdString();
        nlohmann_json_j["goods_hits"]              = nlohmann_json_t.goods_hits;
        nlohmann_json_j["goods_kind_unique"]       = nlohmann_json_t.goods_kind_unique;
        nlohmann_json_j["default_supplier_unique"] = nlohmann_json_t.default_supplier_unique.toStdString();
        nlohmann_json_j["beanTimes"]               = nlohmann_json_t.beanTimes;
        nlohmann_json_j["goods_brand"]             = nlohmann_json_t.goods_brand.toStdString();
        nlohmann_json_j["giveCount"]               = nlohmann_json_t.giveCount;
        //nlohmann_json_j["tagList"]                 = nlohmann_json_t.tagList;
    }

    friend void from_json(const nlohmann::json &nlohmann_json_j, GoodsInfo &nlohmann_json_t)
    {
        QVariant var_tmp;

        if (nlohmann_json_j.find("goods_id") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_id").is_number_integer())
                nlohmann_json_t.goods_id = nlohmann_json_j.at("goods_id");

        if (nlohmann_json_j.find("beanTop") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("beanTop").is_number_integer())
                nlohmann_json_t.beanTop = nlohmann_json_j.at("beanTop");

        if (nlohmann_json_j.find("foreign_key") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("foreign_key").is_number_integer())
                nlohmann_json_t.foreign_key = nlohmann_json_j.at("foreign_key");

        if (nlohmann_json_j.find("goods_kind_parunique") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_kind_parunique").is_number_integer())
                nlohmann_json_t.goods_kind_parunique = nlohmann_json_j.at("goods_kind_parunique");

        if (nlohmann_json_j.find("goods_count") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_count").is_number_float())
                nlohmann_json_t.goods_count = nlohmann_json_j.at("goods_count");

        if (nlohmann_json_j.find("goods_sale_price") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_sale_price").is_number_float())
                nlohmann_json_t.goods_sale_price = nlohmann_json_j.at("goods_sale_price");

        if (nlohmann_json_j.find("goods_standard") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_standard").is_string())
                nlohmann_json_t.goods_standard = QString::fromStdString(nlohmann_json_j.at("goods_standard"));

        if (nlohmann_json_j.find("goods_cus_price") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_cus_price").is_string())
                nlohmann_json_t.goods_cus_price = QString::fromStdString(nlohmann_json_j.at("goods_cus_price"));

        if (nlohmann_json_j.find("update_time") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("update_time").is_string())
                nlohmann_json_t.update_time = QString::fromStdString(nlohmann_json_j.at("update_time"));

        if (nlohmann_json_j.find("goods_barcode") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_barcode").is_string())
                nlohmann_json_t.goods_barcode = QString::fromStdString(nlohmann_json_j.at("goods_barcode"));

        if (nlohmann_json_j.find("goods_points") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_points").is_number_integer())
                nlohmann_json_t.goods_points = nlohmann_json_j.at("goods_points");

        if (nlohmann_json_j.find("goods_web_sale_price") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_web_sale_price").is_number_float())
                nlohmann_json_t.goods_web_sale_price = nlohmann_json_j.at("goods_web_sale_price");

        if (nlohmann_json_j.find("goods_discount") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_discount").is_number_float())
                nlohmann_json_t.goods_discount = nlohmann_json_j.at("goods_discount");

        if (nlohmann_json_j.find("countBeans") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("countBeans").is_number_integer())
                nlohmann_json_t.countBeans = nlohmann_json_j.at("countBeans");

        if (nlohmann_json_j.find("goodsChengType") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goodsChengType").is_number_integer())
                nlohmann_json_t.goodsChengType = nlohmann_json_j.at("goodsChengType");

        if (nlohmann_json_j.find("sameType") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("sameType").is_number_integer())
                nlohmann_json_t.sameType = nlohmann_json_j.at("sameType");

        if (nlohmann_json_j.find("goods_name") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_name").is_string())
                nlohmann_json_t.goods_name = QString::fromStdString(nlohmann_json_j.at("goods_name"));

        if (nlohmann_json_j.find("shop_unique") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("shop_unique").is_number_integer())
                nlohmann_json_t.shop_unique = nlohmann_json_j.at("shop_unique");

        if (nlohmann_json_j.find("pc_shelf_state") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("pc_shelf_state").is_number_integer())
                nlohmann_json_t.pc_shelf_state = nlohmann_json_j.at("pc_shelf_state");

        tryFromJson(nlohmann_json_j, "shelfState", var_tmp);
        nlohmann_json_t.shelfState = var_tmp.toInt();

        if (nlohmann_json_j.find("goods_sold") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_sold").is_number())
                nlohmann_json_t.goods_sold = nlohmann_json_j.at("goods_sold");

        if (nlohmann_json_j.find("goods_in_price") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_in_price").is_number())
                nlohmann_json_t.goods_in_price = nlohmann_json_j.at("goods_in_price");
        if (nlohmann_json_j.find("goodStockPrice") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goodStockPrice").is_number())
                nlohmann_json_t.goodStockPrice = nlohmann_json_j.at("goodStockPrice");
        if (nlohmann_json_j.find("goods_picturepath") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_picturepath").is_string())
                nlohmann_json_t.goods_picturepath = QString::fromStdString(nlohmann_json_j.at("goods_picturepath"));

//        if (nlohmann_json_j.find("goods_life") != nlohmann_json_j.end())
//            if (nlohmann_json_j.at("goods_life").is_number())
//                from_json(nlohmann_json_j.at("goods_life"), nlohmann_json_t.goods_life);
//                nlohmann_json_t.goods_in_price = nlohmann_json_j.at("goods_in_price");
        if (nlohmann_json_j.find("goods_life") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_life").is_number())
                nlohmann_json_t.goods_life = nlohmann_json_j.at("goods_life");

        if (nlohmann_json_j.find("goods_unit") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_unit").is_string())
                nlohmann_json_t.goods_unit = QString::fromStdString(nlohmann_json_j.at("goods_unit"));

        if (nlohmann_json_j.find("goods_address") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_address").is_string())
                nlohmann_json_t.goods_address = QString::fromStdString(nlohmann_json_j.at("goods_address"));

        if (nlohmann_json_j.find("goods_alias") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_alias").is_string())
                nlohmann_json_t.goods_alias = QString::fromStdString(nlohmann_json_j.at("goods_alias"));

        if (nlohmann_json_j.find("goods_contain") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_contain").is_number())
                nlohmann_json_t.goods_contain = nlohmann_json_j.at("goods_contain");

        if (nlohmann_json_j.find("goods_remarks") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_remarks").is_string())
                nlohmann_json_t.goods_remarks = QString::fromStdString(nlohmann_json_j.at("goods_remarks"));

        if (nlohmann_json_j.find("goods_promotion") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_promotion").is_string())
                from_json(nlohmann_json_j.at("goods_promotion"), nlohmann_json_t.goods_promotion);

        if (nlohmann_json_j.find("goods_hits") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_hits").is_number_integer())
                nlohmann_json_t.goods_hits = nlohmann_json_j.at("goods_hits");

        nlohmann_json_t.goods_kind_unique = getDataFromJson(nlohmann_json_j, "goods_kind_unique").toULongLong();

        if (nlohmann_json_j.find("default_supplier_unique") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("default_supplier_unique").is_string())
                nlohmann_json_t.default_supplier_unique = QString::fromStdString(nlohmann_json_j.at("default_supplier_unique"));

        if (nlohmann_json_j.find("beanTimes") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("beanTimes").is_number_integer())
                nlohmann_json_t.beanTimes = nlohmann_json_j.at("beanTimes");

        if (nlohmann_json_j.find("goods_brand") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_brand").is_string())
                nlohmann_json_t.goods_brand = QString::fromStdString(nlohmann_json_j.at("goods_brand"));

        if (nlohmann_json_j.find("giveCount") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("giveCount").is_number_integer())
                nlohmann_json_t.giveCount = nlohmann_json_j.at("giveCount");

        //if (nlohmann_json_j.contains("tagList"))
        //{
        //    nlohmann_json_t.tagList = nlohmann_json_j["tagList"];
        //}
    }
};

/*!
 * \brief The GoodsKindInfo class 商品分类信息
 */
class GoodsKindInfo
{
public:
    unsigned long long goods_kind_id;        //--类型编号
    unsigned long long goods_kind_unique;    //--类型唯一标识符
    unsigned long long shop_unique;          //--商铺唯一标识符
    unsigned long long kind_type;            //--是否自定义：1、系统默认分类；2、自定义分类
    unsigned long long same_type;            //--1：已同步；2：未同步
    unsigned long long goods_kind_order;     //--排序优先级数值越大越靠前
    unsigned long long valid_type;           //--是否有效：1、有效；2、无效
    unsigned long long goods_kind_parunique; //--类型父类
    unsigned long long edit_type;            //--是否可编辑：1、不可编辑；2、可编辑
    QString            goods_kind_name;      //--类型名称
    QString            column_name;          //
    QString            goods_kind_picture;   //--分类图标保存路径
    QString            goods_kind_alias;     //--店铺自定义分类名称

    friend void from_json(const nlohmann::json &nlohmann_json_j, GoodsKindInfo &nlohmann_json_t)
    {

        if (nlohmann_json_j.find("goods_kind_id") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_kind_id").is_number_integer())
                nlohmann_json_t.goods_kind_id = nlohmann_json_j.at("goods_kind_id");

        if (nlohmann_json_j.find("goods_kind_unique") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_kind_unique").is_number_integer())
                nlohmann_json_t.goods_kind_unique = nlohmann_json_j.at("goods_kind_unique");

        if (nlohmann_json_j.find("shop_unique") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("shop_unique").is_number_integer())
                nlohmann_json_t.shop_unique = nlohmann_json_j.at("shop_unique");

        if (nlohmann_json_j.find("kind_type") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("kind_type").is_number_integer())
                nlohmann_json_t.kind_type = nlohmann_json_j.at("kind_type");

        if (nlohmann_json_j.find("same_type") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("same_type").is_number_integer())
                nlohmann_json_t.same_type = nlohmann_json_j.at("same_type");

        if (nlohmann_json_j.find("goods_kind_order") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_kind_order").is_number_integer())
                nlohmann_json_t.goods_kind_order = nlohmann_json_j.at("goods_kind_order");

        if (nlohmann_json_j.find("valid_type") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("valid_type").is_number_integer())
                nlohmann_json_t.valid_type = nlohmann_json_j.at("valid_type");

        if (nlohmann_json_j.find("goods_kind_parunique") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_kind_parunique").is_number_integer())
                nlohmann_json_t.goods_kind_parunique = nlohmann_json_j.at("goods_kind_parunique");

        if (nlohmann_json_j.find("edit_type") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("edit_type").is_number_integer())
                nlohmann_json_t.edit_type = nlohmann_json_j.at("edit_type");

        if (nlohmann_json_j.find("goods_kind_name") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_kind_name").is_string())
                nlohmann_json_t.goods_kind_name = QString::fromStdString(std::string(nlohmann_json_j.at("goods_kind_name")));

        if (nlohmann_json_j.find("column_name") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("column_name").is_string())
                nlohmann_json_t.column_name = QString::fromStdString(std::string(nlohmann_json_j.at("column_name")));

        if (nlohmann_json_j.find("goods_kind_picture") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_kind_picture").is_string())
                nlohmann_json_t.goods_kind_picture = QString::fromStdString(std::string(nlohmann_json_j.at("goods_kind_picture")));

        if (nlohmann_json_j.find("goods_kind_alias") != nlohmann_json_j.end())
            if (nlohmann_json_j.at("goods_kind_alias").is_string())
                nlohmann_json_t.goods_kind_alias = QString::fromStdString(std::string(nlohmann_json_j.at("goods_kind_alias")));
    }

    friend void to_json(nlohmann::json &nlohmann_json_j, const GoodsKindInfo &nlohmann_json_t)
    {
        nlohmann_json_j["goods_kind_id"]        = nlohmann_json_t.goods_kind_id;
        nlohmann_json_j["goods_kind_unique"]    = nlohmann_json_t.goods_kind_unique;
        nlohmann_json_j["shop_unique"]          = nlohmann_json_t.shop_unique;
        nlohmann_json_j["kind_type"]            = nlohmann_json_t.kind_type;
        nlohmann_json_j["same_type"]            = nlohmann_json_t.same_type;
        nlohmann_json_j["goods_kind_order"]     = nlohmann_json_t.goods_kind_order;
        nlohmann_json_j["valid_type"]           = nlohmann_json_t.valid_type;
        nlohmann_json_j["goods_kind_parunique"] = nlohmann_json_t.goods_kind_parunique;
        nlohmann_json_j["edit_type"]            = nlohmann_json_t.edit_type;
        nlohmann_json_j["goods_kind_name"]      = nlohmann_json_t.goods_kind_name.toStdString();
        nlohmann_json_j["column_name"]          = nlohmann_json_t.column_name.toStdString();
        nlohmann_json_j["goods_kind_picture"]   = nlohmann_json_t.goods_kind_picture.toStdString();
        nlohmann_json_j["goods_kind_alias"]     = nlohmann_json_t.goods_kind_alias.toStdString();
    }
};


class VirtualGoodsKindInfo
{
public:
    unsigned long long   goodsKindInventedId;
    unsigned long long   goods_kind_unique;
    QString              goods_kind_name;
    std::vector<QString> goods_barode_vec_;

    friend void from_json(const nlohmann::json &nlohmann_json_j, VirtualGoodsKindInfo &nlohmann_json_t)
    {
        QVariant var_tmp;
        tryFromJson(nlohmann_json_j, "goods_kind_unique", var_tmp);
        nlohmann_json_t.goods_kind_unique = var_tmp.toULongLong();
        tryFromJson(nlohmann_json_j, "goods_kind_name", var_tmp);
        nlohmann_json_t.goods_kind_name = var_tmp.toString();
        tryFromJson(nlohmann_json_j, "goodsKindInventedId", var_tmp);
        nlohmann_json_t.goodsKindInventedId = var_tmp.toULongLong();

        if (nlohmann_json_j.contains("goodsList"))
        {
//            for (const auto &barcode : nlohmann_json_j["goodsList"])
//            {
//                std::string str_barcode = barcode.get<std::string>();
//                QVariant var_tmp = QVariant(QString::fromStdString(str_barcode));
//                LOG_EVT_INFO("获取到的商品条形码为：{}", var_tmp.toString().toStdString());
//                nlohmann_json_t.goods_barode_vec_.push_back(var_tmp.toString());
//            }
            for (const auto &json_item : nlohmann_json_j["goodsList"])
            {
                tryFromJson(json_item, "goods_barcode", var_tmp);
                LOG_EVT_INFO("获取到的商品列表为：{}",var_tmp.toString().toStdString());
                nlohmann_json_t.goods_barode_vec_.push_back(var_tmp.toString());
            }
        }
    }

    friend void to_json(nlohmann::json &nlohmann_json_j, const VirtualGoodsKindInfo &nlohmann_json_t)
    {
//        Json goods_barode_vec_json = Json::array();
//        for (auto cur_goods_barode : nlohmann_json_t.goods_barode_vec_)
//        {
//            goods_barode_vec_json.push_back(cur_goods_barode.toStdString());
//        }
        nlohmann::json goods_barode_vec_json = nlohmann::json::array();
        for (const auto &cur_goods_barode : nlohmann_json_t.goods_barode_vec_)
        {
            nlohmann::json barcode_obj = {{"goods_barcode", cur_goods_barode.toStdString()}};
            goods_barode_vec_json.push_back(barcode_obj);
        }
        nlohmann_json_j["goodsList"]    = goods_barode_vec_json;
        nlohmann_json_j["goods_kind_unique"]   = nlohmann_json_t.goods_kind_unique;
        nlohmann_json_j["goods_kind_name"]     = nlohmann_json_t.goods_kind_name.toStdString();
        nlohmann_json_j["goodsKindInventedId"] = nlohmann_json_t.goodsKindInventedId;
    }
};

class GoodsUnitInfo
{
public:
    unsigned long goods_unit_id; // 单位ID
    QString       goods_unit;    // 单位名

    friend void from_json(const nlohmann::json &nlohmann_json_j, GoodsUnitInfo &nlohmann_json_t)
    {
        QVariant var_tmp;
        if (tryFromJson(nlohmann_json_j, "goods_unit_id", var_tmp))
            nlohmann_json_t.goods_unit_id = var_tmp.toULongLong();
        if (tryFromJson(nlohmann_json_j, "goods_unit", var_tmp))
            nlohmann_json_t.goods_unit = var_tmp.toString();
    }

    friend void to_json(nlohmann::json &nlohmann_json_j, const GoodsUnitInfo &nlohmann_json_t)
    {
        nlohmann_json_j["goods_unit_id"] = nlohmann_json_t.goods_unit_id;
        nlohmann_json_j["goods_unit"]    = nlohmann_json_t.goods_unit.toStdString();
    }
};


struct SupplierInfo
{
    std::string company_leagl;
    std::string shop_unique;
    std::string supplier_address;
    std::string supplier_id;
    std::string supplier_kind_id;
    std::string supplier_kind_name;
    std::string supplier_name;
    std::string supplier_phone;
    std::string supplier_unique;

    friend void from_json(const nlohmann::json &nlohmann_json_j, SupplierInfo &nlohmann_json_t)
    {
        LOG_EVT_INFO("开启supplier_kind_id353453     999999999999 的数据解析");
        nlohmann_json_t.company_leagl      = getDataFromJson(nlohmann_json_j, "company_leagl").toString().toStdString();
        nlohmann_json_t.shop_unique        = getDataFromJson(nlohmann_json_j, "shop_unique").toString().toStdString();
        nlohmann_json_t.supplier_address   = getDataFromJson(nlohmann_json_j, "supplier_address").toString().toStdString();
        nlohmann_json_t.supplier_id        = getDataFromJson(nlohmann_json_j, "supplier_id").toString().toStdString();
        nlohmann_json_t.supplier_kind_id   = getDataFromJson(nlohmann_json_j, "supplier_kind_id").toString().toStdString();
        nlohmann_json_t.supplier_kind_name = getDataFromJson(nlohmann_json_j, "supplier_kind_name").toString().toStdString();
        nlohmann_json_t.supplier_name      = getDataFromJson(nlohmann_json_j, "supplier_name").toString().toStdString();
        nlohmann_json_t.supplier_phone     = getDataFromJson(nlohmann_json_j, "supplier_phone").toString().toStdString();
        nlohmann_json_t.supplier_unique    = getDataFromJson(nlohmann_json_j, "supplier_unique").toString().toStdString();
    }

    friend void to_json(nlohmann::json &nlohmann_json_j, const SupplierInfo &nlohmann_json_t)
    {
        nlohmann_json_j["company_leagl"]      = nlohmann_json_t.company_leagl;
        nlohmann_json_j["shop_unique"]        = nlohmann_json_t.shop_unique;
        nlohmann_json_j["supplier_address"]   = nlohmann_json_t.supplier_address;
        nlohmann_json_j["supplier_id"]        = nlohmann_json_t.supplier_id;
        nlohmann_json_j["supplier_kind_id"]   = nlohmann_json_t.supplier_kind_id;
        nlohmann_json_j["supplier_kind_name"] = nlohmann_json_t.supplier_kind_name;
        nlohmann_json_j["supplier_name"]      = nlohmann_json_t.supplier_name;
        nlohmann_json_j["supplier_phone"]     = nlohmann_json_t.supplier_phone;
        nlohmann_json_j["supplier_unique"]    = nlohmann_json_t.supplier_unique;
    }
};



/*!
 * \brief The OrderDetailInfo class 订单详情
 */
struct OrderDetailInfo
{
    unsigned long long sale_list_detail_id = 0.0;       // 销售清单Id
    unsigned long long sale_list_unique    = 0.0;       // 销售单唯一标识
    QString            goods_barcode;                   // 商品条形码
    QString            goods_name;                      // 商品名称
    QString            goods_picturepath;               // 商品图片路径
    double             sale_list_detail_count    = 0.0; // 商品数量
    double             sale_list_detail_price    = 0.0; // 商品购买的价格
    double             sale_list_detail_subtotal = 0.0; // 金额小计
    unsigned long long goods_id                  = 0.0; // 商品id
    double             goods_purprice            = 0.0; // 商品进价
    double             commission_total          = 0.0; // 提成小计
    double             goods_old_price           = 0.0; // 原价
    unsigned long long goods_beans_count         = 0.0; // 供货商赠送百货豆
    QString            goods_label;                     // 价格标签
    unsigned long long shop_beans_count     = 0.0;      // 商家赠送百货豆
    unsigned long long sale_list_express_id = 0.0;      // 快递关联ID
    QString            saveTime;                        //

    friend void from_json(const nlohmann::json &json_j, OrderDetailInfo &json_t)
    {
        QVariant var_tmp;
        tryFromJson(var_tmp, "sale_list_detail_id", var_tmp);
        json_t.sale_list_detail_id = var_tmp.toULongLong();
        tryFromJson(var_tmp, "sale_list_unique", var_tmp);
        json_t.sale_list_unique = var_tmp.toULongLong();
        tryFromJson(var_tmp, "goods_barcode", var_tmp);
        json_t.goods_barcode = var_tmp.toString();
        tryFromJson(var_tmp, "goods_name", var_tmp);
        json_t.goods_name = var_tmp.toString();
        tryFromJson(var_tmp, "goods_picturepath", var_tmp);
        json_t.goods_picturepath = var_tmp.toString();
        tryFromJson(var_tmp, "sale_list_detail_count", var_tmp);
        json_t.sale_list_detail_count = var_tmp.toDouble();
        tryFromJson(var_tmp, "sale_list_detail_price", var_tmp);
        json_t.sale_list_detail_price = var_tmp.toDouble();
        tryFromJson(var_tmp, "sale_list_detail_subtotal", var_tmp);
        json_t.sale_list_detail_subtotal = var_tmp.toDouble();
        tryFromJson(var_tmp, "goods_id", var_tmp);
        json_t.goods_id = var_tmp.toULongLong();
        tryFromJson(var_tmp, "goods_purprice", var_tmp);
        json_t.goods_purprice = var_tmp.toDouble();
        tryFromJson(var_tmp, "goods_purprice", var_tmp);
        json_t.commission_total = var_tmp.toDouble();
        tryFromJson(var_tmp, "goods_old_price", var_tmp);
        json_t.goods_old_price = var_tmp.toDouble();
        tryFromJson(var_tmp, "goods_label", var_tmp);
        json_t.goods_label = var_tmp.toString();
        tryFromJson(var_tmp, "goods_beans_count", var_tmp);
        json_t.goods_beans_count = var_tmp.toULongLong();
        tryFromJson(var_tmp, "shop_beans_count", var_tmp);
        json_t.shop_beans_count = var_tmp.toULongLong();
        tryFromJson(var_tmp, "sale_list_express_id", var_tmp);
        json_t.sale_list_express_id = var_tmp.toULongLong();
        tryFromJson(var_tmp, "saveTime", var_tmp);
        json_t.saveTime = var_tmp.toString();
    }

    friend void to_json(nlohmann::json &json_j, const OrderDetailInfo &json_t)
    {
        json_j["sale_list_detail_id"]       = json_t.sale_list_detail_id;
        json_j["sale_list_unique"]          = json_t.sale_list_unique;
        json_j["goods_barcode"]             = json_t.goods_barcode.toStdString();
        json_j["goods_name"]                = json_t.goods_name.toStdString();
        json_j["goods_picturepath"]         = json_t.goods_picturepath.toStdString();
        json_j["sale_list_detail_count"]    = json_t.sale_list_detail_count;
        json_j["sale_list_detail_price"]    = json_t.sale_list_detail_price;
        json_j["sale_list_detail_subtotal"] = json_t.sale_list_detail_subtotal;
        json_j["goods_id"]                  = json_t.goods_id;
        json_j["goods_purprice"]            = json_t.goods_purprice;
        json_j["commission_total"]          = json_t.commission_total;
        json_j["goods_old_price"]           = json_t.goods_old_price;
        json_j["goods_label"]               = json_t.goods_label.toStdString();
        json_j["goods_beans_count"]         = json_t.goods_beans_count;
        json_j["shop_beans_count"]          = json_t.shop_beans_count;
        json_j["sale_list_express_id"]      = json_t.sale_list_express_id;
        json_j["saveTime"]                  = json_t.saveTime.toStdString();
    }
};


struct OrderPayMethod
{
    int    pay_method = 0;
    double pay_price  = .0;
};


/*!
 * \brief The OrderInfo class 订单信息
 */
struct OrderInfo
{
    unsigned long long sale_list_id         = 0;                 // id
    unsigned long long sale_list_unique     = 0;                 // 订单编号
    unsigned long long shop_unique          = 0;                 // 商店唯一标识符
    QString            sale_list_datetime   = default_date_time; // 销售单日期
    double             sale_list_total      = 0.0;               // 应收金额
    double             sale_list_pur        = 0.0;               // 订单进货价
    unsigned long long sale_list_totalCount = 0;                 // 商品总数量
    QString            cus_unique;                               // 消费者唯一编号

    unsigned long long sale_type = 0; // 订单类型 0-实体店销售 1-APP订单 2-微信商城小程序 3-网页订单 4-美团外卖订单 5-饿了么外卖订单
                                      // 6-移动端收银 7-收银端平台会员结算 8-积分兑换

    QString sale_list_name;            // 收货人姓名
    QString sale_list_phone;           // 收货人联系电话
    QString sale_list_address;         // 订单送货地址 实体店销售为空 网店按实际填写
    double  sale_list_delfee    = 0.0; // 外送费
    double  shop_subsidy_delfee = 0.0; // 商家补贴配送费

    double sale_list_discount = 0.0; // 订单折扣率

    // 付款状态 1-货到付款未付款 2-网上订单未付款 3-已付款 4-赊账  5-申请退款  6-同意退款 7-拒绝退款 8-自助收银未付款
    unsigned long long sale_list_state = default_state;

    // 发货状态：0-已删除 1-无效订单 2-待发货-3待收货 4-已完成 5-已取消 6-待评论 7-配送单待确认 8-待付款 9-待自提 10-配送异常 11-已核单未发货
    unsigned long long sale_list_handlestate = default_state;

    // 支付方式：1-现金 2-支付宝 3-微信 4-银行卡  5-储值卡 6-美团外卖7-饿了么外卖 8-混合支付 9-免密支付 10-积分兑换 11-百货豆 12-拉卡拉 13-易通付款码支付
    // 14-合利宝刷卡
    unsigned long long sale_list_payment = default_state;

    QString            sale_list_remarks;                               // 订单备注
    unsigned long long sale_list_flag              = 0;                 // 0-注册用户订单 1-游客订单
    QString            receipt_datetime            = default_date_time; // 订单签收时间
    QString            send_datetime               = default_date_time; // 订单发货时间
    unsigned long long sale_list_number            = default_state;     // 订单序号-用于统计记录每天的订单序号
    unsigned long long sale_list_cashier           = 0;                 // 收银员id-实体销售查询
    unsigned long long sale_list_same_type         = 2;                 // 1-PC已同步 2-PC未同步
    double             sale_list_actually_received = 0.0;               // 订单实际收到金额
    unsigned long long machine_num                 = 1;                 // 机器编号
    double             member_card                 = 0.0;               // 会员卡支付金额
    unsigned long long evaluate_point              = 0;                 // 订单评分
    double             commission_sum              = 0.0;               // 提成总和
    QString            pay_time                    = default_date_time; // 支付时间
    QString            trade_no;                                        // 交易流水号
    QString            cancle_reason;                                   // 取消订单原因描述
    double             refund_money = 0.0;                              // 退款金额
    QString            refund_reason;                                   // 退款原因
    QString            refunt_operate_reason;                           // 退款操作原因
    unsigned long long cus_id           = 0;                            // 会员id (自增键)
    double             addr_latitude    = 0.0;                          // 纬度
    double             addr_longitude   = 0.0;                          // 经度
    unsigned long long goods_kind_count = 0;                            // 几种商品
    unsigned long long shipping_method  = 1;                            // 1:送货上门 2:自提
    double             point_deduction  = 0.0;                          // 积分抵扣金额
    double             card_deduction   = 0.0;                          // 储值卡抵扣金额
    QString            label_val;                                       // 收货地址标签
    double             coupon_amount  = 0.0;                            // 优惠券优惠金额
    double             beans_get      = 0.0;                            // 百货豆赠送数量
    double             beans_use      = 0.0;                            // 百货豆使用数量
    unsigned long long shop_coupon_id = 0.0;                            // 优惠券ID
    double             point_val      = 0.0;                            // 积分抵扣积分
    double             beans_money    = 0.0;                            // 百货豆抵扣金额
    double             points_get     = 0.0;                            // 积分获取
    unsigned long long delivery_type  = 0;                              // 配送方式 0-自配送 1-美团配送 2-一刻钟配送
    QString            cancel_time    = default_date_time;              // 订单取消时间
    double             goods_weight   = 0.0;                            // 商品总重量
    QString            formId;                                          // 小程序提交formId
    unsigned long long add_shop_balance_status = 0;                     // 0-未增加店铺余额  1-已增加
    QString            delivery_error;                                  // 配送异常描述
    double             platform_shop_beans = 0.0;                       // 平台赠送商家的百货豆
    QString            head_image;                                      // 人脸信息
    QString            cus_face_token;                                  //
    unsigned long long sup_give_beans     = 0;                          // 供货商赠送的百货豆
    unsigned long long platform_cus_beans = 0;                          // 平台赠送会员的百货豆
    double             return_price       = 0.0;                        // 退还差价
    unsigned long long verify_staff_id    = 0;                          // 核单员工id
    unsigned long long shop_give_beans    = 0;                          // 商家线上商品补贴百货豆数量

    int     local_points_ratio = 0; // 积分比例
    QString local_member_unique;    // 会员unique


    std::vector<OrderDetailInfo> order_detail_infos;
    std::vector<OrderPayMethod>  order_pay_method_vec;

    long getGoodsCount();

    static QString getPayMethodStr(int sale_list_payment);
    QString        getOrderPayMethodStr();

    friend void from_json(const nlohmann::json &json_j, OrderInfo &nlohmann_json_t)
    {
        if (json_j.find("sale_list_id") != json_j.end())
            if (json_j.at("sale_list_id").is_number_integer())
                nlohmann_json_t.sale_list_id = json_j.at("sale_list_id");

        if (json_j.find("sale_list_unique") != json_j.end())
            if (json_j.at("sale_list_unique").is_number_integer())
                nlohmann_json_t.sale_list_unique = json_j.at("sale_list_unique");

        if (json_j.find("shop_unique") != json_j.end())
            if (json_j.at("shop_unique").is_number_integer())
                nlohmann_json_t.shop_unique = json_j.at("shop_unique");

        if (json_j.find("sale_list_datetime") != json_j.end())
            if (json_j.at("sale_list_datetime").is_string())
                nlohmann_json_t.sale_list_datetime = QString::fromStdString(json_j.at("sale_list_datetime"));

        if (json_j.find("sale_list_total") != json_j.end())
            if (json_j.at("sale_list_total").is_number_float())
                nlohmann_json_t.sale_list_total = json_j.at("sale_list_total");

        if (json_j.find("sale_list_pur") != json_j.end())
            if (json_j.at("sale_list_pur").is_number_float())
                nlohmann_json_t.sale_list_pur = json_j.at("sale_list_pur");

        if (json_j.find("sale_list_totalCount") != json_j.end())
            if (json_j.at("sale_list_totalCount").is_number_integer())
                nlohmann_json_t.sale_list_totalCount = json_j.at("sale_list_totalCount");

        if (json_j.find("cus_unique") != json_j.end())
            if (json_j.at("cus_unique").is_string())
                nlohmann_json_t.cus_unique = QString::fromStdString(json_j.at("cus_unique"));

        if (json_j.find("sale_type") != json_j.end())
            if (json_j.at("sale_type").is_number_integer())
                nlohmann_json_t.sale_type = json_j.at("sale_type");

        if (json_j.find("sale_list_name") != json_j.end())
            if (json_j.at("sale_list_name").is_string())
                nlohmann_json_t.sale_list_name = QString::fromStdString(json_j.at("sale_list_name"));

        if (json_j.find("sale_list_phone") != json_j.end())
            if (json_j.at("sale_list_phone").is_string())
                nlohmann_json_t.sale_list_phone = QString::fromStdString(json_j.at("sale_list_phone"));

        if (json_j.find("sale_list_address") != json_j.end())
            if (json_j.at("sale_list_address").is_string())
                nlohmann_json_t.sale_list_address = QString::fromStdString(json_j.at("sale_list_address"));

        if (json_j.find("sale_list_delfee") != json_j.end())
            if (json_j.at("sale_list_delfee").is_number_float())
                nlohmann_json_t.sale_list_delfee = json_j.at("sale_list_delfee");

        if (json_j.find("shop_subsidy_delfee") != json_j.end())
            if (json_j.at("shop_subsidy_delfee").is_number_float())
                nlohmann_json_t.shop_subsidy_delfee = json_j.at("shop_subsidy_delfee");

        if (json_j.find("sale_list_discount") != json_j.end())
            if (json_j.at("sale_list_discount").is_number_float())
                nlohmann_json_t.sale_list_discount = json_j.at("sale_list_discount");


        if (json_j.find("sale_list_state") != json_j.end())
            if (json_j.at("sale_list_state").is_number_integer())
                nlohmann_json_t.sale_list_state = json_j.at("sale_list_state");


        if (json_j.find("sale_list_handlestate") != json_j.end())
            if (json_j.at("sale_list_handlestate").is_number_integer())
                nlohmann_json_t.sale_list_handlestate = json_j.at("sale_list_handlestate");

        if (json_j.find("sale_list_payment") != json_j.end())
            if (json_j.at("sale_list_payment").is_number_integer())
                nlohmann_json_t.sale_list_payment = json_j.at("sale_list_payment");


        if (json_j.find("sale_list_remarks") != json_j.end())
            if (json_j.at("sale_list_remarks").is_string())
                nlohmann_json_t.sale_list_remarks = QString::fromStdString(json_j.at("sale_list_remarks"));

        if (json_j.find("sale_list_flag") != json_j.end())
            if (json_j.at("sale_list_flag").is_number_integer())
                nlohmann_json_t.sale_list_flag = json_j.at("sale_list_flag");

        if (json_j.find("receipt_datetime") != json_j.end())
            if (json_j.at("receipt_datetime").is_string())
                nlohmann_json_t.receipt_datetime = QString::fromStdString(json_j.at("receipt_datetime"));

        if (json_j.find("send_datetime") != json_j.end())
            if (json_j.at("send_datetime").is_string())
                nlohmann_json_t.send_datetime = QString::fromStdString(json_j.at("send_datetime"));

        if (json_j.find("sale_list_number") != json_j.end())
            if (json_j.at("sale_list_number").is_number_integer())
                nlohmann_json_t.sale_list_number = json_j.at("sale_list_number");

        if (json_j.find("sale_list_cashier") != json_j.end())
            if (json_j.at("sale_list_cashier").is_number_integer())
                nlohmann_json_t.sale_list_cashier = json_j.at("sale_list_cashier");

        if (json_j.find("sale_list_same_type") != json_j.end())
            if (json_j.at("sale_list_same_type").is_number_integer())
                nlohmann_json_t.sale_list_same_type = json_j.at("sale_list_same_type");

        if (json_j.find("sale_list_actually_received") != json_j.end())
            if (json_j.at("sale_list_actually_received").is_number_float())
                nlohmann_json_t.sale_list_actually_received = json_j.at("sale_list_actually_received");

        if (json_j.find("machine_num") != json_j.end())
            if (json_j.at("machine_num").is_number_integer())
                nlohmann_json_t.machine_num = json_j.at("machine_num");

        if (json_j.find("member_card") != json_j.end())
            if (json_j.at("member_card").is_number_float())
                nlohmann_json_t.member_card = json_j.at("member_card");

        if (json_j.find("evaluate_point") != json_j.end())
            if (json_j.at("evaluate_point").is_number_integer())
                nlohmann_json_t.evaluate_point = json_j.at("evaluate_point");

        if (json_j.find("commission_sum") != json_j.end())
            if (json_j.at("commission_sum").is_number_float())
                nlohmann_json_t.commission_sum = json_j.at("commission_sum");

        if (json_j.find("pay_time") != json_j.end())
            if (json_j.at("pay_time").is_string())
                nlohmann_json_t.pay_time = QString::fromStdString(json_j.at("pay_time"));

        if (json_j.find("trade_no") != json_j.end())
            if (json_j.at("trade_no").is_string())
                nlohmann_json_t.trade_no = QString::fromStdString(json_j.at("trade_no"));

        if (json_j.find("cancle_reason") != json_j.end())
            if (json_j.at("cancle_reason").is_string())
                nlohmann_json_t.cancle_reason = QString::fromStdString(json_j.at("cancle_reason"));

        if (json_j.find("refund_money") != json_j.end())
            if (json_j.at("refund_money").is_number_float())
                nlohmann_json_t.refund_money = json_j.at("refund_money");

        if (json_j.find("refund_reason") != json_j.end())
            if (json_j.at("refund_reason").is_string())
                nlohmann_json_t.refund_reason = QString::fromStdString(json_j.at("refund_reason"));

        if (json_j.find("refunt_operate_reason") != json_j.end())
            if (json_j.at("refunt_operate_reason").is_string())
                nlohmann_json_t.refunt_operate_reason = QString::fromStdString(json_j.at("refunt_operate_reason"));

        if (json_j.find("cus_id") != json_j.end())
            if (json_j.at("cus_id").is_number_integer())
                nlohmann_json_t.cus_id = json_j.at("cus_id");

        if (json_j.find("addr_latitude") != json_j.end())
            if (json_j.at("addr_latitude").is_number_float())
                nlohmann_json_t.addr_latitude = json_j.at("addr_latitude");

        if (json_j.find("addr_longitude") != json_j.end())
            if (json_j.at("addr_longitude").is_number_float())
                nlohmann_json_t.addr_longitude = json_j.at("addr_longitude");

        if (json_j.find("goods_kind_count") != json_j.end())
            if (json_j.at("goods_kind_count").is_number_integer())
                nlohmann_json_t.goods_kind_count = json_j.at("goods_kind_count");

        if (json_j.find("shipping_method") != json_j.end())
            if (json_j.at("shipping_method").is_number_integer())
                nlohmann_json_t.shipping_method = json_j.at("shipping_method");

        if (json_j.find("point_deduction") != json_j.end())
            if (json_j.at("point_deduction").is_number_integer())
                nlohmann_json_t.point_deduction = json_j.at("point_deduction");

        if (json_j.find("card_deduction") != json_j.end())
            if (json_j.at("card_deduction").is_number_integer())
                nlohmann_json_t.card_deduction = json_j.at("card_deduction");

        if (json_j.find("label_val") != json_j.end())
            if (json_j.at("label_val").is_string())
                nlohmann_json_t.label_val = QString::fromStdString(json_j.at("label_val"));

        if (json_j.find("coupon_amount") != json_j.end())
            if (json_j.at("coupon_amount").is_number_float())
                nlohmann_json_t.coupon_amount = json_j.at("coupon_amount");

        if (json_j.find("beans_get") != json_j.end())
            if (json_j.at("beans_get").is_number_float())
                nlohmann_json_t.beans_get = json_j.at("beans_get");

        if (json_j.find("beans_use") != json_j.end())
            if (json_j.at("beans_use").is_number_float())
                nlohmann_json_t.beans_use = json_j.at("beans_use");

        if (json_j.find("shop_coupon_id") != json_j.end())
            if (json_j.at("shop_coupon_id").is_number_integer())
                nlohmann_json_t.shop_coupon_id = json_j.at("shop_coupon_id");

        if (json_j.find("point_val") != json_j.end())
            if (json_j.at("point_val").is_number_float())
                nlohmann_json_t.point_val = json_j.at("point_val");

        if (json_j.find("beans_money") != json_j.end())
            if (json_j.at("beans_money").is_number_float())
                nlohmann_json_t.beans_money = json_j.at("beans_money");

        if (json_j.find("points_get") != json_j.end())
            if (json_j.at("points_get").is_number_float())
                nlohmann_json_t.points_get = json_j.at("points_get");

        if (json_j.find("delivery_type") != json_j.end())
            if (json_j.at("delivery_type").is_number_integer())
                nlohmann_json_t.delivery_type = json_j.at("delivery_type");

        if (json_j.find("cancel_time") != json_j.end())
            if (json_j.at("cancel_time").is_string())
                nlohmann_json_t.cancel_time = QString::fromStdString(json_j.at("cancel_time"));

        if (json_j.find("goods_weight") != json_j.end())
            if (json_j.at("goods_weight").is_number_float())
                nlohmann_json_t.goods_weight = json_j.at("goods_weight");

        if (json_j.find("formId") != json_j.end())
            if (json_j.at("formId").is_string())
                nlohmann_json_t.formId = QString::fromStdString(json_j.at("formId"));

        if (json_j.find("add_shop_balance_status") != json_j.end())
            if (json_j.at("add_shop_balance_status").is_number_integer())
                nlohmann_json_t.add_shop_balance_status = json_j.at("add_shop_balance_status");

        if (json_j.find("delivery_error") != json_j.end())
            if (json_j.at("delivery_error").is_string())
                nlohmann_json_t.delivery_error = QString::fromStdString(json_j.at("delivery_error"));

        if (json_j.find("platform_shop_beans") != json_j.end())
            if (json_j.at("platform_shop_beans").is_number_float())
                nlohmann_json_t.platform_shop_beans = json_j.at("platform_shop_beans");

        if (json_j.find("head_image") != json_j.end())
            if (json_j.at("head_image").is_string())
                nlohmann_json_t.head_image = QString::fromStdString(json_j.at("head_image"));

        if (json_j.find("cus_face_token") != json_j.end())
            if (json_j.at("cus_face_token").is_string())
                nlohmann_json_t.cus_face_token = QString::fromStdString(json_j.at("cus_face_token"));

        if (json_j.find("sup_give_beans") != json_j.end())
            if (json_j.at("sup_give_beans").is_number_integer())
                nlohmann_json_t.sup_give_beans = json_j.at("sup_give_beans");

        if (json_j.find("platform_cus_beans") != json_j.end())
            if (json_j.at("platform_cus_beans").is_number_integer())
                nlohmann_json_t.platform_cus_beans = json_j.at("platform_cus_beans");

        if (json_j.find("return_price") != json_j.end())
            if (json_j.at("return_price").is_number_float())
                nlohmann_json_t.return_price = json_j.at("return_price");

        if (json_j.find("verify_staff_id") != json_j.end())
            if (json_j.at("verify_staff_id").is_number_integer())
                nlohmann_json_t.verify_staff_id = json_j.at("verify_staff_id");

        QVariant var_tmp;
        tryFromJson(json_j, "shop_give_beans", var_tmp);
        nlohmann_json_t.shop_give_beans = var_tmp.toULongLong();
    }

    friend void to_json(nlohmann::json &nlohmann_json_j, const OrderInfo &nlohmann_json_t)
    {
        nlohmann_json_j["sale_list_id"]                = nlohmann_json_t.sale_list_id;
        nlohmann_json_j["sale_list_unique"]            = nlohmann_json_t.sale_list_unique;
        nlohmann_json_j["shop_unique"]                 = nlohmann_json_t.shop_unique;
        nlohmann_json_j["sale_list_datetime"]          = nlohmann_json_t.sale_list_datetime.toStdString();
        nlohmann_json_j["sale_list_total"]             = nlohmann_json_t.sale_list_total;
        nlohmann_json_j["sale_list_pur"]               = nlohmann_json_t.sale_list_pur;
        nlohmann_json_j["sale_list_totalCount"]        = nlohmann_json_t.sale_list_totalCount;
        nlohmann_json_j["cus_unique"]                  = nlohmann_json_t.cus_unique.toStdString();
        nlohmann_json_j["sale_type"]                   = nlohmann_json_t.sale_type;
        nlohmann_json_j["sale_list_name"]              = nlohmann_json_t.sale_list_name.toStdString();
        nlohmann_json_j["sale_list_phone"]             = nlohmann_json_t.sale_list_phone.toStdString();
        nlohmann_json_j["sale_list_address"]           = nlohmann_json_t.sale_list_address.toStdString();
        nlohmann_json_j["sale_list_delfee"]            = nlohmann_json_t.sale_list_delfee;
        nlohmann_json_j["shop_subsidy_delfee"]         = nlohmann_json_t.shop_subsidy_delfee;
        nlohmann_json_j["sale_list_discount"]          = nlohmann_json_t.sale_list_discount;
        nlohmann_json_j["sale_list_state"]             = nlohmann_json_t.sale_list_state;
        nlohmann_json_j["sale_list_handlestate"]       = nlohmann_json_t.sale_list_handlestate;
        nlohmann_json_j["sale_list_payment"]           = nlohmann_json_t.sale_list_payment;
        nlohmann_json_j["sale_list_remarks"]           = nlohmann_json_t.sale_list_remarks.toStdString();
        nlohmann_json_j["sale_list_flag"]              = nlohmann_json_t.sale_list_flag;
        nlohmann_json_j["receipt_datetime"]            = nlohmann_json_t.receipt_datetime.toStdString();
        nlohmann_json_j["send_datetime"]               = nlohmann_json_t.send_datetime.toStdString();
        nlohmann_json_j["sale_list_number"]            = nlohmann_json_t.sale_list_number;
        nlohmann_json_j["sale_list_cashier"]           = nlohmann_json_t.sale_list_cashier;
        nlohmann_json_j["sale_list_same_type"]         = nlohmann_json_t.sale_list_same_type;
        nlohmann_json_j["sale_list_actually_received"] = nlohmann_json_t.sale_list_actually_received;
        nlohmann_json_j["machine_num"]                 = nlohmann_json_t.machine_num;
        nlohmann_json_j["member_card"]                 = nlohmann_json_t.member_card;
        nlohmann_json_j["evaluate_point"]              = nlohmann_json_t.evaluate_point;
        nlohmann_json_j["commission_sum"]              = nlohmann_json_t.commission_sum;
        nlohmann_json_j["pay_time"]                    = nlohmann_json_t.pay_time.toStdString();
        nlohmann_json_j["trade_no"]                    = nlohmann_json_t.trade_no.toStdString();
        nlohmann_json_j["cancle_reason"]               = nlohmann_json_t.cancle_reason.toStdString();
        nlohmann_json_j["refund_money"]                = nlohmann_json_t.refund_money;
        nlohmann_json_j["refund_reason"]               = nlohmann_json_t.refund_reason.toStdString();
        nlohmann_json_j["refunt_operate_reason"]       = nlohmann_json_t.refunt_operate_reason.toStdString();
        nlohmann_json_j["cus_id"]                      = nlohmann_json_t.cus_id;
        nlohmann_json_j["addr_latitude"]               = nlohmann_json_t.addr_latitude;
        nlohmann_json_j["addr_longitude"]              = nlohmann_json_t.addr_longitude;
        nlohmann_json_j["goods_kind_count"]            = nlohmann_json_t.goods_kind_count;
        nlohmann_json_j["shipping_method"]             = nlohmann_json_t.shipping_method;
        nlohmann_json_j["point_deduction"]             = nlohmann_json_t.point_deduction;
        nlohmann_json_j["card_deduction"]              = nlohmann_json_t.card_deduction;
        nlohmann_json_j["label_val"]                   = nlohmann_json_t.label_val.toStdString();
        nlohmann_json_j["coupon_amount"]               = nlohmann_json_t.coupon_amount;
        nlohmann_json_j["beans_get"]                   = nlohmann_json_t.beans_get;
        nlohmann_json_j["beans_use"]                   = nlohmann_json_t.beans_use;
        nlohmann_json_j["shop_coupon_id"]              = nlohmann_json_t.shop_coupon_id;
        nlohmann_json_j["point_val"]                   = nlohmann_json_t.point_val;
        nlohmann_json_j["beans_money"]                 = nlohmann_json_t.beans_money;
        nlohmann_json_j["points_get"]                  = nlohmann_json_t.points_get;
        nlohmann_json_j["delivery_type"]               = nlohmann_json_t.delivery_type;
        nlohmann_json_j["cancel_time"]                 = nlohmann_json_t.cancel_time.toStdString();
        nlohmann_json_j["goods_weight"]                = nlohmann_json_t.goods_weight;
        nlohmann_json_j["formId"]                      = nlohmann_json_t.formId.toStdString();
        nlohmann_json_j["add_shop_balance_status"]     = nlohmann_json_t.add_shop_balance_status;
        nlohmann_json_j["delivery_error"]              = nlohmann_json_t.delivery_error.toStdString();
        nlohmann_json_j["platform_shop_beans"]         = nlohmann_json_t.platform_shop_beans;
        nlohmann_json_j["head_image"]                  = nlohmann_json_t.head_image.toStdString();
        nlohmann_json_j["cus_face_token"]              = nlohmann_json_t.cus_face_token.toStdString();
        nlohmann_json_j["sup_give_beans"]              = nlohmann_json_t.sup_give_beans;
        nlohmann_json_j["platform_cus_beans"]          = nlohmann_json_t.platform_cus_beans;
        nlohmann_json_j["return_price"]                = nlohmann_json_t.return_price;
        nlohmann_json_j["verify_staff_id"]             = nlohmann_json_t.verify_staff_id;
        nlohmann_json_j["shop_give_beans"]             = nlohmann_json_t.shop_give_beans;
    }
};


struct GoodsTag
{
    std::string              goods_barcode;
    std::vector<std::string> goods_tag_list;

    friend void to_json(nlohmann::json &json_out, const GoodsTag &obj)
    {
        json_out["goods_barcode"]  = obj.goods_barcode;
        json_out["goods_tag_list"] = obj.goods_tag_list;
    }

    friend void from_json(const nlohmann::json &json_in, GoodsTag &obj)
    {
        obj.goods_barcode = getDataFromJson(json_in, "goods_barcode").toString().toStdString();

        if (json_in.contains("goods_tag_list"))
        {
            obj.goods_tag_list = json_in["goods_tag_list"];
        }
    }
};


//条码 + TAG&比例
using BarcodeTagRatioMap = std::map<std::string, std::vector<std::tuple<std::string, float>>>;

using TagRatioVec = std::vector<std::tuple<std::string, float>>;

using goodsDataMap = std::map<QString, GoodsInfo>;
using VGoodsKindDataMap = std::map<unsigned long long, VirtualGoodsKindInfo>;
using goodsKindDataMap = std::map<unsigned long long, GoodsKindInfo>;

#endif // DATASTRUCT_H
