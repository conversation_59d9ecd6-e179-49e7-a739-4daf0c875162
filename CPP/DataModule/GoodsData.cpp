﻿#include "GoodsData.h"
#include <fstream>
#include "ControlModule/ControlManager.h"
#include "ControlModule/GoodsDataModel.h"
#include "EnumTool.h"
#include "LogManager.h"
#include "WorkerModule/LocalDataWorker.h"

using namespace std;

GoodsData::GoodsData(QObject *parent) : QObject{parent}
{
    is_show_no_code_goods_ = ConfigTool::getInstance()->getSetting(ConfigToolEnum::ConfigEnum::IS_SHOW_NO_CODE_GOODS).toBool();
    is_swap_no_code_goods_ = ConfigTool::getInstance()->getSetting(ConfigToolEnum::ConfigEnum::IS_SWAP_NO_CODE_GOODS).toBool();

    goods_data_map_.reset(new goodsDataMap());
    goods_data_map_no_code_.reset(new goodsDataMap());

    {
        auto no_code_goods        = getNoCodeGoods();
        auto no_code_goods_weight = getNoCodeGoods(true);

        goods_data_map_no_code_->insert(std::make_pair(no_code_goods.goods_barcode, no_code_goods));
        goods_data_map_no_code_->insert(std::make_pair(no_code_goods_weight.goods_barcode, no_code_goods_weight));
    }

#ifdef _RECOGNITION_
    initTagList();
#endif
}
void GoodsData::init()
{
    LOG_EVT_INFO("GoodsData 初始化！");
    auto data_mgr = DataManager::getInstance();
    connect(this, &GoodsData::sigSaveGoodsData2Local, data_mgr->local_data_worker_, &LocalDataWorker::saveGoodsData);
    connect(this, &GoodsData::sigGetGoodsData2Local, data_mgr->local_data_worker_, &LocalDataWorker::getGoodsData);

    connect(data_mgr->local_data_worker_, &LocalDataWorker::sigSendGoodsData, this,
            [&](std::shared_ptr<goodsDataMap> data)
            {
                goods_data_map_ = data;
                LOG_EVT_INFO("加载本地商品成功！");
                ControlManager::getInstance()->getGoodsControl()->goods_init_status_ = 1;
                ControlManager::getInstance()->getGoodsControl()->checkIsGoodsLoaded();
            });

    connect(data_mgr->local_data_worker_, &LocalDataWorker::sigSendGoodsDataError, this,
            [&]()
            {
                LOG_EVT_INFO("加载本地商品失败！");
                ControlManager::getInstance()->getGoodsControl()->goods_init_status_ = -1;
                ControlManager::getInstance()->getGoodsControl()->checkIsGoodsLoaded();
            });
}

bool GoodsData::addGoods(GoodsInfo goods_info)
{
    if (getGoodsData()->find(goods_info.goods_barcode) != getGoodsData()->end())
        return false;

    emit preItemReset();
    getGoodsData()->insert_or_assign(goods_info.goods_barcode, goods_info);
    emit postItemReset();

    return true;
}

bool GoodsData::addGoodsByList(const std::vector<GoodsInfo> goods_info_queue)
{
    if (!goods_info_queue.size())
        return false;

    // emit preItemReset();
    for (auto goods_info : goods_info_queue)
    {
        goods_data_map_->insert_or_assign(goods_info.goods_barcode, goods_info);
    }
    //同步商品后保存信息到本地
    saveGoodsData2Local_Thread();

    return true;
}

bool GoodsData::updateGoodsByList(const std::vector<GoodsInfo> &goods_info_queue)
{
    if (!goods_info_queue.size())
        return false;

    emit preItemReset();
    for (const auto &goods_info : goods_info_queue)
    {
        getGoodsData()->insert_or_assign(goods_info.goods_barcode, goods_info);
    }
    emit postItemReset();

    return true;
}

bool GoodsData::deleteGoodsByBarcode(QString goods_barcode)
{
    auto iter = getGoodsData()->find(goods_barcode);
    if (iter == getGoodsData()->end())
        return false;

    emit preItemRemoved(goods_barcode);
    getGoodsData()->erase(iter);
    emit postItemRemoved();
    return true;
}

bool GoodsData::deleteGoodsByBarcodeList(const std::vector<std::tuple<QString, QString>> &goods_id_and_barcode_vec)
{
    for (auto goods_id_and_barcode_vec_iter = goods_id_and_barcode_vec.begin(); goods_id_and_barcode_vec_iter != goods_id_and_barcode_vec.end();
         ++goods_id_and_barcode_vec_iter)
    {
        auto goods_id      = std::get<0>(*goods_id_and_barcode_vec_iter);
        auto goods_barcode = std::get<1>(*goods_id_and_barcode_vec_iter);

        auto ret_iter =
            std::find_if(goods_data_map_->begin(), goods_data_map_->end(),
                         [&](std::pair<QString, GoodsInfo> goods_info_pair)
                         {
                             return (goods_info_pair.second.goods_barcode == goods_barcode); // && (goods_info_pair.second.goods_id == goods_id.toULongLong()));
                         });

        if (ret_iter != goods_data_map_->end())
        {
            goods_data_map_->erase(ret_iter);
        }
    }

    return true;
}


bool GoodsData::deleteAllGoods()
{
    emit preItemReset();
    getGoodsData()->clear();
    emit postItemReset();
    return true;
}

bool GoodsData::getGoodsByBarcode(QString barcode, GoodsInfo &goods_info)
{
    if (barcode.toInt() == (int)EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS)
    {
        goods_info = getNoCodeGoods(true);
        return true;
    }
    else if (barcode.toInt() == (int)EnumTool::UidEnum::ID_NO_CODE_GOODS)
    {
        goods_info = getNoCodeGoods();
        return true;
    }

    for (const auto &goods_data_map_item : *getGoodsData())
    {
        //LOG_EVT_INFO("==:{}",goods_data_map_item.second.goods_barcode.toStdString());
        if (goods_data_map_item.second.goods_barcode == barcode)
        {
            goods_info = goods_data_map_item.second;
            return true;
        }
    }
    return false;
}

GoodsInfo GoodsData::getNoCodeGoods(bool is_weight)
{
    GoodsInfo goods_info;
    // 如果是无码商品
    if (is_weight)
    {
        goods_info.goods_id         = static_cast<int>(EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS);
        goods_info.goods_cus_price  = 1;
        goods_info.goods_sale_price = 1;
        goods_info.goods_barcode    = QString::number(static_cast<int>(EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS));
        goods_info.goodsChengType   = 1;
        goods_info.goods_name       = tr("无码称重");
        goods_info.goods_alias      = "wmcz";
    }
    else
    {
        goods_info.goods_id         = static_cast<int>(EnumTool::UidEnum::ID_NO_CODE_GOODS);
        goods_info.goods_cus_price  = 1;
        goods_info.goods_barcode    = QString::number(static_cast<int>(EnumTool::UidEnum::ID_NO_CODE_GOODS));
        goods_info.goods_sale_price = 1;
        goods_info.goods_name       = tr("无码商品");
        goods_info.goods_alias      = "wmsp";
    }
    return goods_info;
}
#ifdef _RECOGNITION_

void GoodsData::initTagList()
{
    if (goods_tag_list_.empty())
    {
        std::ifstream fs("tagList.json");
        if (!fs.is_open())
            return;

        std::string json_str((std::istreambuf_iterator<char>(fs)), std::istreambuf_iterator<char>());
        fs.close();

        Json json_doc = Json::parse(json_str);

        if (!json_doc.is_array())
            return;

        for (auto &json_item : json_doc)
        {
            auto tag_item = json_item.get<GoodsTag>();
            goods_tag_list_.push_back(tag_item);
        }
    }

    refreshTagMap();
}

void GoodsData::saveTagList()
{
    Json    json_doc;
    fstream fs;
    fs.open("tagList.json", ios_base::in | ios_base::out | ios_base::trunc);

    if (fs.is_open())
    {
        for (const auto &goods_tag : goods_tag_list_)
        {
            json_doc.push_back(goods_tag);
        }

        fs << json_doc.dump() << endl;
        fs.close();
    }
    else
    {
        LOG_DATA_ERROR("保存TAG失败");
    }
}

void GoodsData::addTag(std::string barcode, std::vector<std::string> goods_tag_list)
{
    auto find_tag_list_iter = find_if(goods_tag_list_.begin(), goods_tag_list_.end(),
                                      [=](const GoodsTag &goods_tag)
                                      {
                                          return goods_tag.goods_barcode == barcode;
                                      });

    if (find_tag_list_iter == goods_tag_list_.end())
    {
        GoodsTag goods_tag;
        goods_tag.goods_barcode  = barcode;
        goods_tag.goods_tag_list = goods_tag_list;
        goods_tag_list_.push_back(goods_tag);
    }
    else
    {
        for (auto goods_tag_list_item : goods_tag_list)
        {
            auto find_iter = std::find(find_tag_list_iter->goods_tag_list.begin(), find_tag_list_iter->goods_tag_list.end(), goods_tag_list_item);

            if (find_iter == find_tag_list_iter->goods_tag_list.end())
            {
                find_tag_list_iter->goods_tag_list.push_back(goods_tag_list_item);
            }
        }
    }
}

void GoodsData::delTag(std::string barcode, std::vector<std::string> goods_tag_list)
{
    auto find_tag_list_iter = find_if(goods_tag_list_.begin(), goods_tag_list_.end(),
                                      [=](GoodsTag goods_tag)
                                      {
                                          return goods_tag.goods_barcode == barcode;
                                      });

    if (find_tag_list_iter == goods_tag_list_.end())
        return;

    for (auto goods_tag_list_iter = find_tag_list_iter->goods_tag_list.begin(); goods_tag_list_iter != find_tag_list_iter->goods_tag_list.end();)
    {
        auto goods_tag = find(goods_tag_list.begin(), goods_tag_list.end(), *goods_tag_list_iter);

        if (goods_tag != goods_tag_list.end())
        {
            goods_tag_list_iter = find_tag_list_iter->goods_tag_list.erase(goods_tag_list_iter);

            //列表为空
            if (find_tag_list_iter->goods_tag_list.empty())
            {
                goods_tag_list_.erase(find_tag_list_iter);
                return;
            }
            continue;
        }
        ++goods_tag_list_iter;
    }
}

void GoodsData::clearTag(std::string barcode)
{
    auto find_tag_list_iter = find_if(goods_tag_list_.begin(), goods_tag_list_.end(),
                                      [=](GoodsTag goods_tag)
                                      {
                                          return goods_tag.goods_barcode == barcode;
                                      });

    if (find_tag_list_iter == goods_tag_list_.end())
        return;

    goods_tag_list_.erase(find_tag_list_iter);
}

void GoodsData::refreshTagMap()
{
    tag_map_.clear();

    for (const auto &goods_tag : goods_tag_list_)
    {
        for (const auto &goods_tag_item : goods_tag.goods_tag_list)
        {
            tag_map_[goods_tag_item].insert(goods_tag.goods_barcode);
        }
    }
}
#endif

bool GoodsData::getGoodsPtrByBarcode(QString goods_barcode, GoodsInfo *&goods_info)
{
    for (auto &goods_data : *getGoodsData())
    {
        if (goods_data.second.goods_barcode == goods_barcode)
        {
            goods_info = &goods_data.second;
            return true;
        }
    }
    return false;
}

void GoodsData::saveGoodsData2Local_Thread()
{
    emit sigSaveGoodsData2Local(getGoodsDataCopy());
}

void GoodsData::getLocalGoodsData_Thread()
{
    emit sigGetGoodsData2Local();
}

bool GoodsData::setGoodsInfo(const GoodsInfo &goods_info)
{
    for (auto &goods_data : *getGoodsData())
    {
        if (goods_data.second.goods_barcode == goods_info.goods_barcode)
        {
            goods_data.second.goods_name              = goods_info.goods_name;
            goods_data.second.shop_unique             = goods_info.shop_unique;
            goods_data.second.foreign_key             = goods_info.foreign_key;
            goods_data.second.goods_in_price          = goods_info.goods_in_price;
            goods_data.second.goods_count             = goods_info.goods_count;
            goods_data.second.goodStockPrice          = goods_info.goodStockPrice;
            goods_data.second.goods_standard          = goods_info.goods_standard;
            goods_data.second.goods_sale_price        = goods_info.goods_sale_price;
            goods_data.second.goods_cus_price         = goods_info.goods_cus_price;
            goods_data.second.update_time             = goods_info.update_time;
            goods_data.second.goods_barcode           = goods_info.goods_barcode;
            goods_data.second.goods_contain           = goods_info.goods_contain;
            goods_data.second.goods_alias             = goods_info.goods_alias;
            goods_data.second.goods_web_sale_price    = goods_info.goods_web_sale_price;
            goods_data.second.goods_kind_unique       = goods_info.goods_kind_unique;
            goods_data.second.goods_brand             = goods_info.goods_brand;
            goods_data.second.goodsChengType          = goods_info.goodsChengType;
            goods_data.second.goods_unit              = goods_info.goods_unit;
            goods_data.second.pc_shelf_state          = goods_info.pc_shelf_state;
            goods_data.second.default_supplier_unique = goods_info.default_supplier_unique;

            emit postRowChanged(goods_data.second.goods_barcode);
            return true;
        }
    }
    return false;
}

std::shared_ptr<goodsDataMap> GoodsData::getGoodsData()
{
    return goods_data_map_;
}

std::shared_ptr<std::vector<GoodsInfo>> GoodsData::getGoodsDataCopy() const
{
    std::shared_ptr<std::vector<GoodsInfo>> result;
    result.reset(new std::vector<GoodsInfo>);

    for (auto goods_data_map_item : *goods_data_map_)
    {
        result->push_back(goods_data_map_item.second);
    }

    return result;
}

void GoodsData::setGoodsData(goodsDataMap *goods_data_map)
{
    goods_data_map_.reset(goods_data_map);
}
#ifdef _RECOGNITION_

void GoodsData::addRecognitionBarcodeTagRatioMap(const BarcodeTagRatioMap &barcode_tag_ratio_map)
{
    if (barcode_tag_ratio_map.empty())
        return;

    while (barcode_tag_ratio_map_vec_.size() > 2)
    {
        barcode_tag_ratio_map_vec_.erase(barcode_tag_ratio_map_vec_.begin());
    }

    barcode_tag_ratio_map_vec_.push_back(barcode_tag_ratio_map);
}

void GoodsData::setTagRatioVec(TagRatioVec tag_ratio_vec)
{
    last_tag_ratio_vec_ = tag_ratio_vec;
}

void GoodsData::tryAddTagByRecognition(QString goods_barcode)
{
    addTagByRecognition(goods_barcode);
}

void GoodsData::tryDelTagByRecognition(QString goods_barcode)
{
    delTagByRecognition(goods_barcode);
}

void GoodsData::addTagByRecognition(QString goods_barcode)
{
    if (last_tag_ratio_vec_.empty())
        return;

    auto                     goods_mgr = DataManager::getInstance()->getGoodsMgr();
    std::vector<std::string> goods_tag_list;
    auto                     tmp_val = std::get<0>(last_tag_ratio_vec_[0]);
    goods_tag_list.push_back(tmp_val);

    addTag(goods_barcode.toStdString(), goods_tag_list);
    goods_mgr->refreshTagMap();

    //仅替换最后一次识别
    if (!goods_mgr->barcode_tag_ratio_map_vec_.empty())
    {
        goods_mgr->barcode_tag_ratio_map_vec_.pop_back();
    }

    GoodsDataModel::goods_data_model_recognition_->refreshGoodsPtrMapByRecognition(last_tag_ratio_vec_);

    goods_mgr->saveTagList();
}

void GoodsData::delTagByRecognition(QString goods_barcode)
{
    if (barcode_tag_ratio_map_vec_.empty())
        return;

    //NOTE 删除所有与此条码识别到的绑定信息
    std::set<std::string> goods_tag_list;

    for (auto &barcode_tag_ratio_map : barcode_tag_ratio_map_vec_)
    {
        auto find_iter = barcode_tag_ratio_map.find(goods_barcode.toStdString());

        if (find_iter == barcode_tag_ratio_map.end())
            continue;

        //标识 让识别列表刷新
        CameraControl::goods_recognition_status_ = -1;

        for (auto item : find_iter->second)
        {
            goods_tag_list.insert(std::get<0>(item));
        }

        barcode_tag_ratio_map.erase(find_iter);
    }

    std::vector<std::string> goods_tag_list_vec;

    for (auto goods_tag_list_item : goods_tag_list)
    {
        goods_tag_list_vec.push_back(goods_tag_list_item);
    }

    delTag(goods_barcode.toStdString(), goods_tag_list_vec);
    refreshTagMap();

    GoodsDataModel::goods_data_model_recognition_->refreshGoodsPtrMapByRecognition();

    saveTagList();
}

#endif

QVariant GoodsData::getGoodsByKind4Qml(QVariant kind_unique, QVariant is_need_nocode_goods)
{
    Json json;
    for (const auto &goods_data : *getGoodsData())
    {
        if (goods_data.second.goods_kind_unique == kind_unique.toULongLong())
        {
            json.push_back(goods_data.second);
        }
    }

    if (is_need_nocode_goods.toBool())
    {
        json.push_back(getNoCodeGoods());
        json.push_back(getNoCodeGoods(true));
    }

    return QVariant::fromValue(QString::fromStdString(json.dump()));
}

QVariant GoodsData::getGoodsByVirtualKindJson(const VirtualGoodsKindInfo &virtual_kind_info, QVariant is_need_nocode_goods)
{
    Json json;
    for (const auto &goods_data : *getGoodsData())
    {
        auto ret_find = std::find(virtual_kind_info.goods_barode_vec_.begin(), virtual_kind_info.goods_barode_vec_.end(), goods_data.second.goods_barcode);

        if (ret_find != virtual_kind_info.goods_barode_vec_.end())
        {
            json.push_back(goods_data.second);
        }
    }

    if (is_need_nocode_goods.toBool())
    {
        json.push_back(getNoCodeGoods());
        json.push_back(getNoCodeGoods(true));
    }

    return QString::fromStdString(json.dump());
}
QVariant GoodsData::getGoodsByBarcode4Qml(QVariant barcode)
{
    Json json;
    bool is_goods_exist = false;
    if (barcode.toULongLong() == static_cast<unsigned long long>(EnumTool::UidEnum::ID_NO_CODE_WEIGHT_GOODS))
    {
        json = getNoCodeGoods(true);

        return QVariant::fromValue(QString::fromStdString(json.dump()));
    }
    for (const auto &goods_data_map_item : *getGoodsData())
    {
        if (goods_data_map_item.second.goods_barcode == barcode)
        {
            json           = goods_data_map_item.second;
            is_goods_exist = true;
            break;
        }
    }
    if (!is_goods_exist && barcode.toString().length() == 13)
    {
        auto cur_goods_barcode = barcode.toString().left(7);
        if (getGoodsData()->find(cur_goods_barcode) != getGoodsData()->end())
        {
            json = getGoodsData()->at(cur_goods_barcode);
        }
    }

    return QVariant::fromValue(QString::fromStdString(json.dump()));
}
QVariant GoodsData::getGoodsByGoodId4Qml(QVariant goodId)
{
    Json json;
    for (const auto &goods_data_map_item : *getGoodsData())
    {
        if (goods_data_map_item.second.goods_id == goodId)
        {
            json           = goods_data_map_item.second;
            break;
        }
    }
    return QVariant::fromValue(QString::fromStdString(json.dump()));
}

QVariant GoodsData::getForeignGoodsJsonListByBarcode(QVariant barcode)
{
    Json json;
    for (const auto &data : *getGoodsData())
    {
        if (data.second.foreign_key == barcode.toULongLong() && (data.second.foreign_key != data.second.goods_barcode.toULongLong()))
        {
            json.push_back(data.second);
        }
    }
    return QVariant::fromValue(QString::fromStdString(std::string(json.dump(0))));
}

QVariant GoodsData::getAllGoodsUnitJson()
{
    Json result_json;

    for (const auto &goods_unit_item : goods_unit_info_map_)
    {
        result_json.push_back(goods_unit_item.second);
    }

    return QString::fromStdString(result_json.dump());
}


bool GoodsData::getIsShowNoCodeGoods()
{
    return is_show_no_code_goods_;
}

void GoodsData::setIsShowNoCodeGoods(QVariant is_show_no_code_goods)
{
    is_show_no_code_goods_ = is_show_no_code_goods.toBool();
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::IS_SHOW_NO_CODE_GOODS, is_show_no_code_goods_);
    emit sigIsShowNoCodeGoodsChanged();
}

bool GoodsData::isSwapNoCodeGoods()
{
    return is_swap_no_code_goods_;
}

void GoodsData::setIsSwapNoCodeGoods(bool is_swap)
{
    is_swap_no_code_goods_ = is_swap;
    ConfigTool::getInstance()->setSetting(ConfigToolEnum::ConfigEnum::IS_SWAP_NO_CODE_GOODS, is_swap_no_code_goods_);
    ControlManager::getInstance();

    emit isSwapNoCodeGoodsChanged();
}

QString GoodsData::generateCustomBarcode(bool is_weight)
{
    QString prefix;
    if (is_weight)
    {
        prefix = ConfigTool::getInstance()->getSetting(ConfigEnum::BAR_CODE_WEIGHING_PRE_TWO_CODE).toString();
    }
    else
    {
        prefix = ConfigTool::getInstance()->getSetting(ConfigEnum::BAR_CODE_NORMAL_PRE_TWO_CODE).toString();
    }
    if(is_weight){

        prefix          = QString("%1").arg(prefix.toInt(), 2, 10, QLatin1Char('0'));
        int num = 1;
        QString goods_barcode;
        auto find_iter = getGoodsData()->end();
        do {
            goods_barcode = prefix + QString("%1").arg(num, 5, 10, QLatin1Char('0'));
            find_iter = getGoodsData()->find(goods_barcode);
            if (find_iter != getGoodsData()->end()) {
                ++num;
            }
        } while (find_iter != getGoodsData()->end());
        LOG_EVT_INFO("称重商品生成条形码为:{}",goods_barcode.toStdString());
        return goods_barcode;
    }else{
        prefix          = QString("%1").arg(prefix.toInt(), 2, 10, QLatin1Char('0'));
        auto random_num = QString("%1").arg(Utils::generateRandomNum(5).toInt(), 5, 10, QLatin1Char('0'));

        auto goods_barcode = prefix + random_num;


        auto find_iter = getGoodsData()->find(goods_barcode);

        if (find_iter == getGoodsData()->end())
        {
            return goods_barcode;
        }
        else
        {
            return generateCustomBarcode(is_weight);
        }

    }
}
QString GoodsData::contrastGenerateCustomBarcode(bool is_weight,QVariant barcode1,QVariant barcode2)
{
    QString prefix;
    if (is_weight)
    {
        prefix = ConfigTool::getInstance()->getSetting(ConfigEnum::BAR_CODE_WEIGHING_PRE_TWO_CODE).toString();
    }
    else
    {
        prefix = ConfigTool::getInstance()->getSetting(ConfigEnum::BAR_CODE_NORMAL_PRE_TWO_CODE).toString();
    }
    if(is_weight){

        prefix          = QString("%1").arg(prefix.toInt(), 2, 10, QLatin1Char('0'));
        int num = 1;
        QString goods_barcode;
        auto find_iter = getGoodsData()->end();
        do {
            goods_barcode = prefix + QString("%1").arg(num, 5, 10, QLatin1Char('0'));
            find_iter = getGoodsData()->find(goods_barcode);
            if (find_iter != getGoodsData()->end()) {
                ++num;
            }else{
                if(goods_barcode == barcode1 || goods_barcode == barcode2){
                    ++num;
                }
            }
        } while (find_iter != getGoodsData()->end() || goods_barcode == barcode1 || goods_barcode == barcode2);
        LOG_EVT_INFO("称重商品生成条形码为:{}",goods_barcode.toStdString());
        return goods_barcode;
    }else{
        prefix          = QString("%1").arg(prefix.toInt(), 2, 10, QLatin1Char('0'));
        auto random_num = QString("%1").arg(Utils::generateRandomNum(5).toInt(), 5, 10, QLatin1Char('0'));

        auto goods_barcode = prefix + random_num;


        auto find_iter = getGoodsData()->find(goods_barcode);

        if (find_iter == getGoodsData()->end() && goods_barcode != barcode1 && goods_barcode != barcode2)
        {
            return goods_barcode;
        }
        else
        {
            return generateCustomBarcode(is_weight);
        }

    }
}
bool GoodsData::addGoodsUnit(GoodsUnitInfo goods_unit_info)
{
    const auto [it_ret, success] = goods_unit_info_map_.insert({goods_unit_info.goods_unit_id, goods_unit_info});
    return success;
}

bool GoodsData::deleteGoodsUnit(unsigned long goods_unit_id)
{
    auto find_iter = goods_unit_info_map_.find(goods_unit_id);

    if (find_iter != goods_unit_info_map_.end())
    {
        goods_unit_info_map_.erase(find_iter);
        return true;
    }
    else
    {
        return false;
    }
}

bool GoodsData::updateGoodsUnit(GoodsUnitInfo goods_unit_info)
{
    auto find_iter = goods_unit_info_map_.find(goods_unit_info.goods_unit_id);

    if (find_iter != goods_unit_info_map_.end())
    {
        find_iter->second = goods_unit_info;
        return true;
    }
    else
    {
        return false;
    }
}

void GoodsData::clearGoodsUnit()
{
    goods_unit_info_map_.clear();
}
