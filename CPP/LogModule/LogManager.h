﻿#ifndef LOGMANAGER_H
#define LOGMANAGER_H

#include <QObject>
#include <QString>
#include <QVariant>
#include <memory>
#include <mutex>
#include <spdlog/spdlog.h>

class LogMgr : public QObject
{
    Q_OBJECT

public:
    static LogMgr *getInst();
    ~LogMgr()
    {
    }

    ///-------------------------------------------| logEvt |-------------------------------------------
    //
    Q_INVOKABLE void logEvtDebug4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logEvtInfo4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logEvtWarn4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logEvtErr4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logEvtCritical4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    //
    ///-------------------------------------------| logEvt |-------------------------------------------

    ///-------------------------------------------| logOpt |-------------------------------------------
    //
    Q_INVOKABLE void logOptDebug4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logOptInfo4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logOptWarn4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logOptErr4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logOptCritical4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    //
    ///-------------------------------------------| logOpt |-------------------------------------------

    ///-------------------------------------------| logNet |-------------------------------------------
    //
    Q_INVOKABLE void logNetDebug4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logNetInfo4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logNetWarn4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logNetErr4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logNetCritical4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    //
    ///-------------------------------------------| logNet |-------------------------------------------

    ///-------------------------------------------| logData |-------------------------------------------
    //
    Q_INVOKABLE void logDataDebug4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logDataInfo4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logDataWarn4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logDataErr4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    Q_INVOKABLE void logDataCritical4Qml(const QString &fmt, QVariant v1 = QVariant(), QVariant v2 = QVariant(), QVariant v3 = QVariant());
    //
    ///-------------------------------------------| logData |-------------------------------------------

    std::string getCurLogPath()
    {
        return cur_log_path;
    }

    std::string getLogPath()
    {
        return log_path;
    }

    std::string getLogYMD()
    {
        return date__y_m_d;
    }

public:
    std::unique_ptr<spdlog::logger> logger_evt_;
    std::unique_ptr<spdlog::logger> logger_opt_;
    std::unique_ptr<spdlog::logger> logger_net_;
    std::unique_ptr<spdlog::logger> logger_data_;

private:
    explicit LogMgr(QObject *parent = nullptr);
    static std::unique_ptr<LogMgr> singleton_;
    static std::mutex              mutex_;

    std::string log_path;
    std::string cur_log_path;
    std::string date__y_m_d;

    static bool truncate_;
    static bool is_trace_level_;
    static bool is_need_console_;
};

#ifdef BUYHOO_LOG

// #define LOG_EVT_TRACE(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_evt_, spdlog::level::trace, __VA_ARGS__)
// #define LOG_EVT_DEBUG(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_evt_, spdlog::level::debug, __VA_ARGS__)
#define LOG_EVT_INFO(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_evt_, spdlog::level::info, __VA_ARGS__)
#define LOG_EVT_WARN(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_evt_, spdlog::level::warn, __VA_ARGS__)
#define LOG_EVT_ERROR(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_evt_, spdlog::level::err, __VA_ARGS__)

// #define LOG_OPT_TRACE(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_opt_, spdlog::level::trace, __VA_ARGS__)
// #define LOG_OPT_DEBUG(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_opt_, spdlog::level::debug, __VA_ARGS__)
#define LOG_OPT_INFO(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_opt_, spdlog::level::info, __VA_ARGS__)
#define LOG_OPT_WARN(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_opt_, spdlog::level::warn, __VA_ARGS__)
#define LOG_OPT_ERROR(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_opt_, spdlog::level::err, __VA_ARGS__)

// #define LOG_NET_TRACE(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_net_, spdlog::level::trace, __VA_ARGS__)
// #define LOG_NET_DEBUG(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_net_, spdlog::level::debug, __VA_ARGS__)
#define LOG_NET_INFO(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_net_, spdlog::level::info, __VA_ARGS__)
#define LOG_NET_WARN(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_net_, spdlog::level::warn, __VA_ARGS__)
#define LOG_NET_ERROR(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_net_, spdlog::level::err, __VA_ARGS__)

// #define LOG_DATA_TRACE(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_data_, spdlog::level::trace, __VA_ARGS__)
// #define LOG_DATA_DEBUG(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_data_, spdlog::level::debug, __VA_ARGS__)
#define LOG_DATA_INFO(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_data_, spdlog::level::info, __VA_ARGS__)
#define LOG_DATA_WARN(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_data_, spdlog::level::warn, __VA_ARGS__)
#define LOG_DATA_ERROR(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_data_, spdlog::level::err, __VA_ARGS__)

#else

// #define LOG_EVT_TRACE(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_evt_, spdlog::level::trace, __VA_ARGS__)
// #define LOG_EVT_DEBUG(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_evt_, spdlog::level::debug, __VA_ARGS__)
#define LOG_EVT_INFO(...) void()
#define LOG_EVT_WARN(...) void()
#define LOG_EVT_ERROR(...) void()

// #define LOG_OPT_TRACE(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_opt_, spdlog::level::trace, __VA_ARGS__)
// #define LOG_OPT_DEBUG(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_opt_, spdlog::level::debug, __VA_ARGS__)
#define LOG_OPT_INFO(...) void()
#define LOG_OPT_WARN(...) void()
#define LOG_OPT_ERROR(...) void()

// #define LOG_NET_TRACE(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_net_, spdlog::level::trace, __VA_ARGS__)
// #define LOG_NET_DEBUG(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_net_, spdlog::level::debug, __VA_ARGS__)
#define LOG_NET_INFO(...) void()
#define LOG_NET_WARN(...) void()
#define LOG_NET_ERROR(...) void()

// #define LOG_DATA_TRACE(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_data_, spdlog::level::trace, __VA_ARGS__)
// #define LOG_DATA_DEBUG(...) SPDLOG_LOGGER_CALL(LogMgr::getInst()->logger_data_, spdlog::level::debug, __VA_ARGS__)
#define LOG_DATA_INFO(...) void()
#define LOG_DATA_WARN(...) void()
#define LOG_DATA_ERROR(...) void()

#endif

#endif // LOGMANAGER_H
