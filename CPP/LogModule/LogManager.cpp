﻿#include "LogManager.h"
#include <spdlog/sinks/rotating_file_sink.h>
#include "Utils/Utils.h"
#include "spdlog/sinks/basic_file_sink.h"
#include "spdlog/sinks/stdout_color_sinks.h"

using namespace std;

std::unique_ptr<LogMgr> LogMgr::singleton_;
std::mutex              LogMgr::mutex_;
bool                    LogMgr::truncate_ = true;

#ifdef BUYHOO_DEBUG
bool LogMgr::is_trace_level_ = true;
#else
bool LogMgr::is_trace_level_ = false;
#endif

bool LogMgr::is_need_console_ = false;

LogMgr *LogMgr::getInst()
{
    std::lock_guard<std::mutex> lock(mutex_);

    if (singleton_ == nullptr)
    {
        singleton_.reset(new LogMgr());
    }
    return singleton_.get();
}

void LogMgr::logEvtDebug4Qml(const QString &fmt, QVariant v1, QVariant v2, QVariant v3)
{
    logger_evt_->debug(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logEvtInfo4Qml(const QString &fmt, QVariant v1, QVariant v2, QVariant v3)
{
    logger_evt_->info(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logEvtWarn4Qml(const QString &fmt, QVariant v1, QVariant v2, QVariant v3)
{
    logger_evt_->warn(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logEvtErr4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->error(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logEvtCritical4Qml(const QString &fmt, QVariant v1, QVariant v2, QVariant v3)
{
    logger_evt_->critical(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logOptDebug4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->debug(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logOptInfo4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->info(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logOptWarn4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->warn(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logOptErr4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->error(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logOptCritical4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->critical(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logNetDebug4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->debug(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logNetInfo4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->info(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logNetWarn4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->warn(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logNetErr4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->error(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logNetCritical4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->critical(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logDataDebug4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->debug(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logDataInfo4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->info(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logDataWarn4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->warn(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logDataErr4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->error(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

void LogMgr::logDataCritical4Qml(const QString& fmt, QVariant v1, QVariant v2, QVariant v3)
{
	logger_evt_->critical(fmt.toStdString(), v1.toString().toStdString(), v2.toString().toStdString(), v3.toString().toStdString());
}

LogMgr::LogMgr(QObject *parent) : QObject{parent}
{
    cur_log_path = Utils::getAppDirPath().toStdString();
    date__y_m_d  = Utils::DateTime::getCurDateStr().toStdString();
    
    auto max_size  = 1024 * 1024 * 10; // 10MB
    auto max_files = 10;

    log_path = cur_log_path + "/log/";
    cur_log_path += "/log/" + date__y_m_d + "/";
    //    sink_event_console->set_pattern("[event] [%^%l%$] %v");
    //// 输出格式https://github.com/gabime/spdlog/wiki/3.-Custom-formatting
    // logger->debug("test1");                     // 不会输出文件名和行号
    // SPDLOG_LOGGER_DEBUG(rotating_logger, "test3 {}", 3); // 会输出文件名和行号

    ///-------------------------------------------| event |-------------------------------------------
    //
    const auto log_path_event = cur_log_path + "event.log";

    auto sink_event_console = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    if (is_need_console_)
        sink_event_console->set_level(spdlog::level::trace);
    else
        sink_event_console->set_level(spdlog::level::off);
    auto sink_event_file = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(log_path_event, max_size, max_files);
    sink_event_file->set_level(spdlog::level::trace);
    logger_evt_.reset(new spdlog::logger("event_log", {sink_event_console, sink_event_file}));
    //
    ///-------------------------------------------| event |-------------------------------------------


    ///-------------------------------------------| operate |-------------------------------------------
    //
    const auto log_path_operate = cur_log_path + "operate.log";

    auto sink_operate_console = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    if (is_need_console_)
        sink_operate_console->set_level(spdlog::level::trace);
    else
        sink_operate_console->set_level(spdlog::level::off);
    auto sink_operate_file = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(log_path_operate, max_size, max_files);
    sink_operate_file->set_level(spdlog::level::debug);
    logger_opt_.reset(new spdlog::logger("operate_log", {sink_operate_console, sink_operate_file}));
    //
    ///-------------------------------------------| operate |-------------------------------------------


    ///-------------------------------------------| network |-------------------------------------------
    //
    const auto log_path_network     = cur_log_path + "network.log";
    auto       sink_network_console = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    if (is_need_console_)
        sink_network_console->set_level(spdlog::level::trace);
    else
        sink_network_console->set_level(spdlog::level::off);
    auto sink_network_file = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(log_path_network, max_size, max_files);
    sink_network_file->set_level(spdlog::level::info);
    logger_net_.reset(new spdlog::logger("network_log", {sink_network_console, sink_network_file}));
    //
    ///-------------------------------------------| network |-------------------------------------------


    ///-------------------------------------------| data |-------------------------------------------
    //
    const auto log_path_data     = cur_log_path + "data.log";
    auto       sink_data_console = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    if (is_need_console_)
        sink_data_console->set_level(spdlog::level::trace);
    else
        sink_data_console->set_level(spdlog::level::off);
    auto sink_data_file = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(log_path_data, max_size, max_files);
    sink_data_file->set_level(spdlog::level::info);
    logger_data_.reset(new spdlog::logger("data_log", {sink_data_console, sink_data_file}));
    //
    ///-------------------------------------------| data |-------------------------------------------

    if (is_trace_level_)
    {
        logger_evt_->flush_on(spdlog::level::trace);
        logger_opt_->flush_on(spdlog::level::trace);
        logger_net_->flush_on(spdlog::level::trace);
        logger_data_->flush_on(spdlog::level::trace);
    }
    else
    {
        logger_evt_->flush_on(spdlog::level::err);
        logger_opt_->flush_on(spdlog::level::err);
        logger_net_->flush_on(spdlog::level::err);
        logger_data_->flush_on(spdlog::level::err);
    }

    logger_evt_->set_pattern(">>>>>>>>>[%n] [%C-%m-%d %H:%M:%S.%o] [%l] [%@] [process %P] [thread %t]<<<<<<<<<\n[%!] %v");
    logger_opt_->set_pattern(">>>>>>>>>[%n] [%C-%m-%d %H:%M:%S.%o] [%l] [%@] [process %P] [thread %t]<<<<<<<<<\n[%!] %v");
    logger_net_->set_pattern(">>>>>>>>>[%n] [%C-%m-%d %H:%M:%S.%o] [%l] [%@] [process %P] [thread %t]<<<<<<<<<\n[%!] %v");
    logger_data_->set_pattern(">>>>>>>>>[%n] [%C-%m-%d %H:%M:%S.%o] [%l] [%@] [process %P] [thread %t]<<<<<<<<<\n[%!] %v");

#ifndef BUYHOO_LOG
    logger_evt_->set_level(spdlog::level::off);
    logger_opt_->set_level(spdlog::level::off);
    logger_net_->set_level(spdlog::level::off);
    logger_data_->set_level(spdlog::level::off);
#endif

    // 输出示例
    SPDLOG_LOGGER_WARN(logger_evt_, "event_logger {}", "初始化");
    SPDLOG_LOGGER_WARN(logger_opt_, "operate_logger {}", "初始化");
    SPDLOG_LOGGER_WARN(logger_net_, "network_logger {}", "初始化");
    SPDLOG_LOGGER_WARN(logger_data_, "data_logger {}", "初始化");
}
