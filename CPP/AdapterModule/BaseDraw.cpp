﻿#include "BaseDraw.h"

BaseDraw::BaseDraw(QObject *parent) : QObject(parent)
{
    _lastPt = falsePt = _startPt = QPoint(-1, -1);
}

void BaseDraw::mousePress(qreal x, qreal y)
{
    _pressed = true;
}

void BaseDraw::mouseMove(qreal x, qreal y)
{
}

void BaseDraw::mouseRelease(qreal x, qreal y)
{
    _pressed      = false;
    _startPtLight = false;
}

void BaseDraw::setPoints(const std::vector<QPoint> &pts)
{
    _pts = pts;
    pointsChanged();
}

void BaseDraw::setPoint(int idx, int x, int y)
{
    assert(idx < _pts.size() && idx >= 0);
    _pts[idx].setX(x);
    _pts[idx].setY(y);
    pointsChanged();
}

bool BaseDraw::closed()
{
    return _finished;
}

#include <QDebug>
void BaseDraw::reset()
{
    _currentPath = QPainterPath();
    _polys       = QPolygonF();
    _finished    = true;
    _lastPt = falsePt = _startPt = QPoint(-1, -1);
    _pts.clear();
}

std::vector<QPoint> BaseDraw::getPoints()
{
    return _pts;
}
