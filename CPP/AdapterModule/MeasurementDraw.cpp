﻿#include "MeasurementDraw.h"
#include "Annotation.h"

measurementDraw::measurementDraw(QObject *parent) : BaseDraw(parent)
{
}

void measurementDraw::mousePress(qreal x, qreal y)
{
    BaseDraw::mousePress(x, y);
    _pts.push_back(QPoint(x, y));
    _finished = false;
}

void measurementDraw::mouseMove(qreal x, qreal y)
{
    if (_pressed && !_finished)
    {
        if (_pts.size() != 2)
            _pts.push_back(QPoint(x, y));
        else
            _pts[1] = QPoint(x, y);
        static_cast<Annotation *>(parent())->update();
    }
}

void measurementDraw::mouseRelease(qreal x, qreal y)
{
    BaseDraw::mouseRelease(x, y);
    emit sigCompare();
}

void measurementDraw::paint(QPainter *painter)
{
    if (_pts.size() == 2)
    {
        //画线
        painter->setPen(QPen(QBrush(QColor("green")), 2, Qt::PenStyle::SolidLine));
        painter->drawLine(_pts[0], _pts[1]);
        //画两端的垂直线
        writeVerticalLine(painter, _pts[0], _pts[1]);
        //文字
        QPoint  firstPoint  = _pts[0].x() < _pts[1].x() ? _pts[0] : _pts[1];
        QPoint  lastPoint   = _pts[0].x() < _pts[1].x() ? _pts[1] : _pts[0];
        QPointF centerPoint = QPoint((firstPoint.x() + lastPoint.x()) / 2, (firstPoint.y() + lastPoint.y()) / 2);
        int     y           = firstPoint.y() < lastPoint.y() ? centerPoint.y() - 10 : centerPoint.y() + 10;
        QPointF textPos     = QPointF(centerPoint.x(), y);

        QPainterPath textPath;
        QFont        ft           = QFont("Arial");
        float        sizeInPixels = sqrt(pow(firstPoint.x() - lastPoint.x(), 2) + pow(firstPoint.y() - lastPoint.y(), 2));
        textPath.addText(textPos, ft, QString::number(sizeInPixels, 'f', 2) + QString("pix"));
        painter->save();
        painter->setPen(Qt::NoPen);
        painter->setBrush(QBrush(QColor(0, 0, 0, 75)));
        painter->drawRect(textPath.boundingRect().adjusted(-5, -5, 5, 5));
        painter->setBrush(QBrush(Qt::white));
        painter->drawPath(textPath);
        painter->restore();
    }
}

void measurementDraw::writeVerticalLine(QPainter *painter, QPoint pt1, QPoint pt2, int line_len)
{
    int    r   = (line_len - 1) / 2 + 1;
    int    dx  = abs(pt1.x() - pt2.x());
    int    dy  = abs(pt1.y() - pt2.y());
    double ly2 = pow(r, 2) * pow(dx, 2) / double(pow(dx, 2) + pow(dy, 2));
    double ly  = sqrt(ly2);
    double lx  = ly == 0 || dx == 0 ? 0 : dy / (double)dx * ly;

    QPointF lpt1, lpt2, lpt3, lpt4;
    if ((pt1.x() - pt2.x()) * (pt1.y() - pt2.y()) > 0)
    {
        lpt1 = QPointF(pt1.x() - lx, pt1.y() + ly);
        lpt2 = QPointF(pt1.x() + lx, pt1.y() - ly);
        lpt3 = QPointF(pt2.x() - lx, pt2.y() + ly);
        lpt4 = QPointF(pt2.x() + lx, pt2.y() - ly);
    }
    else
    {
        lpt1 = QPointF(pt1.x() - lx, pt1.y() - ly);
        lpt2 = QPointF(pt1.x() + lx, pt1.y() + ly);
        lpt3 = QPointF(pt2.x() - lx, pt2.y() - ly);
        lpt4 = QPointF(pt2.x() + lx, pt2.y() + ly);
    }
    painter->save();
    painter->setPen(QPen(QBrush(QColor("green")), 2, Qt::PenStyle::SolidLine));
    painter->drawLine(lpt1, lpt2);
    painter->drawLine(lpt3, lpt4);
    painter->restore();
}
