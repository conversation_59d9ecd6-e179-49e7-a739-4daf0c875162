﻿#ifndef IMAGEPROVIDER_H
#define IMAGEPROVIDER_H

#include <QObject>
#include <QQuickImageProvider>
#include <mutex>


class ImageProvider : public QQuickImageProvider
{
public:
    explicit ImageProvider(QObject *parent = nullptr);

    QImage  requestImage(const QString &id, QSize *size, const QSize &requestedSize);
    QPixmap requestPixmap(const QString &id, QSize *size, const QSize &requestedSize);

    QImage img_;
};

#endif // IMAGEPROVIDER_H
