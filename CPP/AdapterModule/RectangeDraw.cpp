﻿#include "RectangeDraw.h"
#include "Annotation.h"

rectangeDraw::rectangeDraw(QObject *parent) : BaseDraw(parent)
{
}

void rectangeDraw::mousePress(qreal x, qreal y)
{
    BaseDraw::mousePress(x, y);
    _pts.push_back(QPoint(x, y));
    _finished = false;
}

void rectangeDraw::mouseMove(qreal x, qreal y)
{
    if (_pressed && !_finished)
    {
        if (_pts.size() != 2)
            _pts.push_back(QPoint(x, y));
        else
            _pts[1] = QPoint(x, y);
        static_cast<Annotation *>(parent())->update();
    }
}

void rectangeDraw::mouseRelease(qreal x, qreal y)
{
    BaseDraw::mouseRelease(x, y);
    _finished = true;
    emit sigCompare();
}

void rectangeDraw::paint(QPainter *painter)
{
    if (_pts.size() == 2)
    {
        //画圆
        painter->setPen(QPen(QBrush(QColor("green")), 2, Qt::PenStyle::SolidLine));
        painter->drawRect(QRect(_pts[0], _pts[1]));
        //画点
        painter->setPen(QPen(QBrush("red"), 7, Qt::PenStyle::SolidLine, Qt::PenCapStyle::SquareCap));
        painter->drawPoint(_pts[0].x(), _pts[0].y());
        painter->drawPoint(_pts[1].x(), _pts[1].y());
    }
}
