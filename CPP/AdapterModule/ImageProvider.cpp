﻿#include "ImageProvider.h"

ImageProvider::ImageProvider(QObject *parent) : QQuickImageProvider(QQuickImageProvider::Image)
{
    img_.fill(QColor(255, 255, 255));
}

QImage ImageProvider::requestImage(const QString &id, QSize *size, const QSize &requestedSize)
{
    return this->img_;
}

QPixmap ImageProvider::requestPixmap(const QString &id, QSize *size, const QSize &requestedSize)
{
    return QPixmap::fromImage(this->img_);
}
