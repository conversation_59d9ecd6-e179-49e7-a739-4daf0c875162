﻿#include "Annotation.h"
#include <QDebug>
#include <QPainter>
#include <QPainterPath>
#include <QPainterPathStroker>
#include <cmath>
#include "EllipseDraw.h"
#include "MeasurementDraw.h"
#include "PolygonDraw.h"
#include "RectangeDraw.h"
#include "SplineDraw.h"
#include "opencv2/opencv.hpp"
#include "ConfModule/ConfigTool.h"

using namespace cv;

Annotation::Annotation(QQuickPaintedItem *parent) : QQuickPaintedItem(parent)
{
}

Annotation::EM_DRAW_TYPE Annotation::drawType()
{
    return _drawType;
}

void Annotation::setDrawType(const EM_DRAW_TYPE &type)
{
    _drawType = type;
    _pCurDraw.reset();
    switch (_drawType)
    {
    case POLYGON:
        {
            std::shared_ptr<BaseDraw> p(new polygonDraw(this));
            _pCurDraw = std::move(p);
            break;
        }
    case SPLINE:
        {
            std::shared_ptr<BaseDraw> p(new splineDraw(this));
            _pCurDraw = std::move(p);
            break;
        }
    case ELLIPSE:
        {
            std::shared_ptr<BaseDraw> p(new ellipseDraw(this));
            _pCurDraw = std::move(p);
            break;
        }
    case RECTANGLE:
        {
            std::shared_ptr<BaseDraw> p(new rectangeDraw(this));
            _pCurDraw = std::move(p);
            break;
        }
    case MEASUREMENT:
        {
            std::shared_ptr<BaseDraw> p(new measurementDraw(this));
            _pCurDraw = std::move(p);
            break;
        }
    }
    if (_pCurDraw)
        connect(_pCurDraw.get(), &BaseDraw::sigCompare, this, &Annotation::slotCompare);
}

void Annotation::initByConfig(int width,int height)
{
    auto config_tool = ConfigTool::getInstance();

    std::shared_ptr<BaseDraw> polygon_draw;
    polygon_draw.reset(new polygonDraw(this));

    std::vector<QPoint> points_tmp;
    points_tmp.push_back(QPoint(config_tool->recognitionRatioP1().x() * width, config_tool->recognitionRatioP1().y() * height));
    points_tmp.push_back(QPoint(config_tool->recognitionRatioP2().x() * width, config_tool->recognitionRatioP2().y() * height));
    points_tmp.push_back(QPoint(config_tool->recognitionRatioP3().x() * width, config_tool->recognitionRatioP3().y() * height));
    points_tmp.push_back(QPoint(config_tool->recognitionRatioP4().x() * width, config_tool->recognitionRatioP4().y() * height));

    polygon_draw->setPoints(points_tmp);
    _draws.push_back(polygon_draw);

    update();
}

void Annotation::paint(QPainter *painter)
{
    painter->setRenderHint(QPainter::Antialiasing);
    painter->save();
    for (auto pDraw : _draws)
    {
        pDraw->paint(painter);
    }
    if (_pCurDraw)
        _pCurDraw->paint(painter);

    painter->restore();
}

void Annotation::mousePress(qreal x, qreal y)
{
    if (_pCurDraw)
    {
        //如果在非勾画状态下点击到控制点,获取该点的索引
        if (_pCurDraw->closed())
        {
            for (unsigned int n = 0; n < _draws.size(); n++)
            {
                for (int i = 0; i < _draws[n]->getPoints().size(); i++)
                {
                    if (QLineF(QPointF(_draws[n]->getPoints()[i].x(), _draws[n]->getPoints()[i].y()), QPointF(x, y)).length() < 12)
                    {
                        _moveDraw.pDraw   = _draws[n];
                        _moveDraw.ptIndex = i;
                        break;
                    }
                }

                if (!_moveDraw.isEmpty())
                {
                    break;
                }
            }
        }

        if (_moveDraw.isEmpty())
        {
            _pCurDraw->mousePress(x, y);
        }
    }
}

void Annotation::mouseMove(qreal x, qreal y)
{
    if (!_moveDraw.isEmpty())
    {
        _moveDraw.pDraw->setPoint(_moveDraw.ptIndex, x, y);
        update();
    }
    else if (_pCurDraw)
        _pCurDraw->mouseMove(x, y);
}

void Annotation::mouseRelease(qreal x, qreal y)
{
    if (!_moveDraw.isEmpty())
        _moveDraw.reset();
    else if (_pCurDraw)
        _pCurDraw->mouseRelease(x, y);
}

void Annotation::slotCompare()
{
    _draws.push_back(_pCurDraw);
    setDrawType(_drawType);

    auto points = _pCurDraw->getPoints();

    for (auto cur_point : points)
    {
        std::vector<cv::Point> tmp_points;
        tmp_points.push_back(cv::Point(cur_point.x(), cur_point.y()));
        auto rect_tmp = cv::minAreaRect(tmp_points);
        cv::Point2f ps[4];
        rect_tmp.points(ps);
        std::vector<std::vector<cv::Point>> tmpContours;    // 创建一个InputArrayOfArrays 类型的点集

        std::vector<cv::Point> contour;
        for (int i = 0; i < 4; ++i)
        {
            contour.emplace_back(cv::Point2i(ps[i]));
        }

        // 插入到轮廓容器中
        tmpContours.insert(tmpContours.end(), contour);
        // 绘制轮廓，也就是绘制旋转矩形
        // drawContours(result, tmpContours, -1, Scalar(0), 1, 16);  // 填充mask
    }

    update();
}

void Annotation::undo()
{
    if (_pCurDraw)
    {
        if (_pCurDraw->getPoints().size() > 0)
        {
            _pCurDraw->reset();
            update();
        }
        else if (_draws.size() > 0)
        {
            _draws.pop_back();
            update();
        }
    }
}

void Annotation::clear()
{
    _draws.clear();
    update();
}
