﻿#include "splinedraw.h"

splineDraw::splineDraw(QObject *parent)
 : polygonDraw(parent)
{

}

std::vector<QPointF> splineDraw::catmullRomToBezier(const QPointF& p0, const QPointF& p1, const QPointF& p2, const QPointF& p3) const
{
    std::vector<QPointF> bezierPoints;
    bezierPoints.push_back(p1);

    float d1 = sqrt(pow(p1.x() - p0.x(), 2) + pow(p1.y() - p0.y(), 2));
    float d2 = sqrt(pow(p2.x() - p1.x(), 2) + pow(p2.y() - p1.y(), 2));
    float d3 = sqrt(pow(p3.x() - p2.x(), 2) + pow(p3.y() - p2.y(), 2));
    float rd1 = sqrt(d1);
    float rd2 = sqrt(d2);
    float rd3 = sqrt(d3);
    float B1X = (d1*p2.x() - d2*p0.x() + (2 * d1 + 3 * rd1*rd2 + d2)*p1.x()) / (3*rd1*(rd1+rd2));
    float B1Y = (d1*p2.y() - d2*p0.y() + (2 * d1 + 3 * rd1*rd2 + d2)*p1.y()) / (3 * rd1*(rd1 + rd2));
    float B2X = (d3*p1.x() - d2*p3.x() + (2 * d3 + 3 * rd3*rd2 + d2)*p2.x()) / (3 * rd3*(rd3 + rd2));
    float B2Y = (d3*p1.y() - d2*p3.y() + (2 * d3 + 3 * rd3*rd2 + d2)*p2.y()) / (3 * rd3*(rd3 + rd2));

    bezierPoints.push_back(QPointF(B1X, B1Y));
    bezierPoints.push_back(QPointF(B2X, B2Y));
    bezierPoints.push_back(p2);
    return bezierPoints;
}

QPainterPath splineDraw::getCurrentPath(const std::vector<QPoint>& coords)
{
    QPainterPath pth;
    if (coords.size() > 1)
    {
        pth.moveTo(coords[0].x(), coords[0].y());
        for (unsigned int i = 0; i < coords.size() - 1; ++i)
        {
            QPointF p1(coords[i].x(), coords[i].y());
            QPointF p2(coords[i + 1].x(), coords[i + 1].y());
            QPointF p0 = p1 - (p2 - p1);
            if (i > 0) {
                p0 = QPointF(coords[i - 1].x(), coords[i - 1].y());
            }
            else if (i == 0 && _finished && coords.size() > 2) {
                p0 = QPointF(coords[coords.size() - 1].x(), coords[coords.size() - 1].y());
            }
            QPointF p3 = p2 + (p2 - p1);
            if (i < coords.size() - 2) {
                p3 = QPointF(coords[i + 2].x(), coords[i + 2].y());
            }
            std::vector<QPointF> bezierPoints = catmullRomToBezier(p0, p1, p2, p3);
            pth.cubicTo(bezierPoints[1], bezierPoints[2], bezierPoints[3]);
        }
        if(_finished)
        {
            if (coords.size() > 1)
            {
                QPointF p1(coords[coords.size() - 1].x(), coords[coords.size() - 1].y());
                QPointF p2(coords[0].x(), coords[0].y());
                QPointF p0 = p1 - (p2 - p1);
                if (coords.size() > 2) {
                    QPointF p0 = QPointF(coords[coords.size() - 2].x(), coords[coords.size() - 2].y());
                }
                QPointF p3(coords[1].x(), coords[1].y());
                std::vector<QPointF> bezierPoints = catmullRomToBezier(p0, p1, p2, p3);
                pth.cubicTo(bezierPoints[1], bezierPoints[2], bezierPoints[3]);
            }
        }
    }
    return pth;
}

void splineDraw::pointsChanged()
{
    _currentPath = getCurrentPath(_pts);
    _polys = _currentPath.toFillPolygon();
    if (!_finished && _polys.isClosed()) {
        _polys.pop_back();
    }
}

void splineDraw::paint(QPainter *painter)
{
    //画线
    painter->setPen(QPen(QBrush(QColor("green")), 2,Qt::PenStyle::DashLine));
    painter->drawPolyline(_polys);

    //画点
    painter->setPen(QPen(QBrush("red"), 7, Qt::PenStyle::SolidLine, Qt::PenCapStyle::SquareCap));
    for (unsigned int i = 0; i < _pts.size(); ++i)
    {
        if(i == 0 && _startPtLight)
            painter->setPen(QPen(QBrush("#db7b7b"), 9, Qt::PenStyle::SolidLine, Qt::PenCapStyle::SquareCap));
        else
            painter->setPen(QPen(QBrush("red"), 7, Qt::PenStyle::SolidLine, Qt::PenCapStyle::SquareCap));

        painter->drawPoint(_pts[i].x(), _pts[i].y());
    }
    //画移动线
    if(_lastPt != QPoint(-1, -1) && falsePt != QPoint(-1, -1))
    {
        painter->setPen(QPen(QBrush(QColor("green")), 2,Qt::PenStyle::DotLine));
        painter->drawLine(_lastPt,falsePt);
    }
}
