﻿#pragma once
#include "BaseDraw.h"

class measurementDraw : public BaseDraw
{
public:
    measurementDraw(QObject *parent = nullptr);

public:
    virtual void mousePress(qreal x, qreal y) override;
    virtual void mouseMove(qreal x, qreal y) override;
    virtual void mouseRelease(qreal x, qreal y) override;

public:
    virtual void paint(Q<PERSON><PERSON><PERSON> *painter);

private:
    void writeVerticalLine(QPainter *painter, QPoint pt1, QPoint pt2, int line_len = 9);
};
