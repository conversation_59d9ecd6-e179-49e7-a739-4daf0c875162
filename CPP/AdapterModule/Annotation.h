﻿#pragma once

#include <QPainterPath>
#include <QPoint>
#include <QQuickPaintedItem>
#include <memory>
#include <vector>
#include "basedraw.h"

struct moveDraw
{
    std::shared_ptr<BaseDraw> pDraw;
    int                       ptIndex;

    void reset()
    {
        pDraw.reset();
        ptIndex = -1;
    }
    bool isEmpty()
    {
        return pDraw.get() == nullptr;
    }
};

class Annotation : public QQuickPaintedItem
{
    Q_OBJECT
    Q_ENUMS(EM_DRAW_TYPE)
    Q_PROPERTY(EM_DRAW_TYPE drawType READ drawType WRITE setDrawType)
public:

    enum EM_DRAW_TYPE
    {
        ELLIPSE,     //椭圆
        RECTANGLE,   //矩形
        POLYGON,     //多边形（直线型）
        SPLINE,      //多边形（曲线型）
        MEASUREMENT, //直尺
        MAX
    };

    Annotation(QQuickPaintedItem *parent = nullptr);

protected:
    void paint(<PERSON><PERSON>ain<PERSON> *painter);

public slots:
    void slotCompare();

public:
    Q_INVOKABLE void mousePress(qreal x, qreal y);
    Q_INVOKABLE void mouseMove(qreal x, qreal y);
    Q_INVOKABLE void mouseRelease(qreal x, qreal y);
    Q_INVOKABLE void undo();
    Q_INVOKABLE void clear();

    EM_DRAW_TYPE drawType();
    void         setDrawType(const EM_DRAW_TYPE &type);

    int getDrawsNum()
    {
        return _draws.size();
    }

    std::vector<std::shared_ptr<BaseDraw>> getDraws()
    {
        return _draws;
    }

    Q_INVOKABLE void initByConfig(int width, int height);

private:
    EM_DRAW_TYPE                           _drawType = MAX; //当前绘制类型
    std::vector<std::shared_ptr<BaseDraw>> _draws;          //保存的图形
    std::shared_ptr<BaseDraw>              _pCurDraw = nullptr;
    moveDraw                               _moveDraw;
};
