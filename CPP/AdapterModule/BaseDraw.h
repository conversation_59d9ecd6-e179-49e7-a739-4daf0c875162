﻿#ifndef BASEDRAW_H
#define BASEDRAW_H

#include <QObject>
#include <QPainter>
#include <QPainterPath>
#include <QPoint>
#include <vector>

class BaseDraw : public QObject
{
    Q_OBJECT
public:
    explicit BaseDraw(QObject *parent = 0);

signals:
    void sigCompare();

public slots:

public:
    virtual void mousePress(qreal x, qreal y);
    virtual void mouseMove(qreal x, qreal y);
    virtual void mouseRelease(qreal x, qreal y);

protected:

    virtual void pointsChanged()
    {
    }

    virtual QPainterPath getCurrentPath(const std::vector<QPoint> &coords)
    {
        return QPainterPath();
    }

public:
    void         reset();
    virtual void paint(QPainter *painter) = 0;

    std::vector<QPoint> getPoints();
    void                setPoints(const std::vector<QPoint> &);
    void                setPoint(int idx, int x, int y);

    bool closed();

    std::vector<QPoint> _pts; //当前点
protected:

    QPainterPath _currentPath;
    QPolygonF    _polys;
    QPoint       _startPt, _lastPt, falsePt;

    bool    _pressed      = false;    //是否按下
    bool    _finished     = true;     //是否闭合
    bool    _startPtLight = false;    //起始点是否高亮
    QString _type         = "spline"; //多边形类型
};

#endif // BASEDRAW_H
