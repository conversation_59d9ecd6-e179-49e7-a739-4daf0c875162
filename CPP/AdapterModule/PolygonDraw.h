﻿#ifndef POLYGONDRAW_H
#define POLYGONDRAW_H
#include "BaseDraw.h"

class polygonDraw : public BaseDraw
{
public:
    polygonDraw(QObject *parent = nullptr);

public:
    virtual void mousePress(qreal x, qreal y) override;
    virtual void mouseMove(qreal x, qreal y) override;
    virtual void mouseRelease(qreal x, qreal y) override;

protected:
    virtual void         pointsChanged();
    virtual QPainterPath getCurrentPath(const std::vector<QPoint> &coords);

public:
    virtual void paint(QPainter *painter);
};

#endif // POLYGONDRAW_H
