﻿#pragma once
#include "polygondraw.h"

class splineDraw : public polygonDraw
{
public:
    splineDraw(QObject *parent = nullptr);

protected:
    virtual void pointsChanged();
    virtual QPainterPath getCurrentPath(const std::vector<QPoint>& coords);

public:
    virtual void paint(QPainter *painter);

private:
    std::vector<QPointF> catmullRomToBezier(const QPointF& p0, const QPointF& p1, const QPointF& p2, const QPointF& p3) const;

};
