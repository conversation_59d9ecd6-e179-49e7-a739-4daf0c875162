﻿#include "EllipseDraw.h"
#include "Annotation.h"

ellipseDraw::ellipseDraw(QObject *parent) : BaseDraw(parent)
{
}

void ellipseDraw::mousePress(qreal x, qreal y)
{
    BaseDraw::mousePress(x, y);
    _pts.push_back(QPoint(x, y));
    _finished = false;
}

void ellipseDraw::mouseMove(qreal x, qreal y)
{
    if (_pressed && !_finished)
    {
        if (_pts.size() != 2)
            _pts.push_back(QPoint(x, y));
        else
            _pts[1] = QPoint(x, y);
        static_cast<Annotation *>(parent())->update();
    }
}

void ellipseDraw::mouseRelease(qreal x, qreal y)
{
    BaseDraw::mouseRelease(x, y);
    emit sigCompare();
}

void ellipseDraw::paint(QPainter *painter)
{
    if (_pts.size() == 2)
    {
        //画圆
        painter->setPen(QPen(QBrush(QColor("green")), 2, Qt::PenStyle::SolidLine));
        painter->drawEllipse(QRect(_pts[0], _pts[1]));
        //画点
        painter->setPen(QPen(QBrush("red"), 7, Qt::PenStyle::SolidLine, Qt::PenCapStyle::SquareCap));
        painter->drawPoint(_pts[0].x(), _pts[0].y());
        painter->drawPoint(_pts[1].x(), _pts[1].y());
    }
}
