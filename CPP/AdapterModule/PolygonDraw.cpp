﻿#include "PolygonDraw.h"
#include "Annotation.h"

polygonDraw::polygonDraw(QObject *parent) : BaseDraw(parent)
{
}

QPainterPath polygonDraw::getCurrentPath(const std::vector<QPoint> &coords)
{
    QPainterPath pth;
    if (coords.size() > 1)
    {
        pth.moveTo(coords[0].x(), coords[0].y());
        for (unsigned int i = 0; i < coords.size() - 1; ++i)
        {
            pth.lineTo(coords[i + 1].x(), coords[i + 1].y());
        }
        if (_finished)
        {
            pth.lineTo(coords[0].x(), coords[0].y());
        }
    }
    return pth;
}

void polygonDraw::pointsChanged()
{
    _currentPath = getCurrentPath(_pts);
}

void polygonDraw::mousePress(qreal x, qreal y)
{
    BaseDraw::mousePress(x, y);

    //如果勾画过程中该点挨近了起始点，则合并
    // if (_pts.size() > 2 && QLineF(QPointF(_startPt), QPointF(x, y)).length() < 12)
    if (_pts.size() > 3)
    {
        //emit sigCompare();
    }
    else
    {
        if (((Annotation *)parent())->getDrawsNum() > 0)
        {
            return;
        }

        QPoint pt(x, y);

        _lastPt = pt;
        _pts.push_back(pt);

        if (_finished)
        {
            _finished = false;
            _startPt  = pt;
        }

        pointsChanged();
        static_cast<Annotation *>(parent())->update();
    }
}

void polygonDraw::mouseMove(qreal x, qreal y)
{
    if (!_finished)
    {
        //如果勾画过程中挨近了起始点，则起始点高亮
        falsePt = QPoint(x, y);

        if (_pts.size() > 2 && QLineF(QPointF(_startPt), QPointF(falsePt)).length() < 12)
        {
            _startPtLight = true;
        }
        else
        {
            _startPtLight = false;
        }

        // //如果勾画过程中按住鼠标键进行拖动,且拖动距离大于40，则自动添加一个控制点
        // if (_pressed && QLineF(_lastPt, falsePt).length() > 40)
        // {
        //     QPoint pt(x, y);
        //     _lastPt = pt;
        //     _pts.push_back(pt);
        //     pointsChanged();
        // }
        static_cast<Annotation *>(parent())->update();
    }
}

void polygonDraw::mouseRelease(qreal x, qreal y)
{
    if (_pts.size() > 3)
    {
        _finished = true;
        _lastPt = falsePt = _startPt = QPoint(-1, -1);
        pointsChanged();
        emit sigCompare();
    }
    else
    {
        static_cast<Annotation *>(parent())->update();
    }

    // if (_startPtLight)
    // {
    //     _finished = true;
    //     _lastPt = falsePt = _startPt = QPoint(-1, -1);
    //     pointsChanged();
    //     emit sigCompare();
    // }
    // else
    // {
    //     static_cast<Annotation *>(parent())->update();
    // }

    BaseDraw::mouseRelease(x, y);
}

void polygonDraw::paint(QPainter *painter)
{
    int _r = 4;

    //画线
    painter->setPen(QPen(QBrush(QColor("green")), 2, Qt::PenStyle::SolidLine));
    painter->drawPath(_currentPath);

    //画点
    painter->save();
    painter->setBrush(QBrush(Qt::red));
    painter->setPen(QPen(QBrush("red"), 5, Qt::PenStyle::SolidLine, Qt::PenCapStyle::SquareCap));

    for (unsigned int i = 0; i < _pts.size(); ++i)
    {
        painter->save();
        if (i == 0 && _startPtLight)
        {
            painter->setPen(QPen(QBrush(QColor("red").lighter(150)), 2, Qt::PenStyle::SolidLine, Qt::PenCapStyle::SquareCap));
        }

        painter->drawRect(_pts[i].x() - _r, _pts[i].y() - _r, _r * 2, _r * 2);
        painter->restore();
    }
    painter->restore();

    //画移动线
    if (_lastPt != QPoint(-1, -1) && falsePt != QPoint(-1, -1))
    {
        painter->setPen(QPen(QBrush(QColor("green")), 2, Qt::PenStyle::DotLine));
        painter->drawLine(_lastPt, falsePt);
    }
}
