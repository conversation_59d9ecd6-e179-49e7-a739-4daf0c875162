﻿#include "HttpWorker2.h"
#include "LogModule/LogManager.h"
#include <qjsvalue.h>

HttpWorker2::HttpWorker2(QString url, QByteArray data, QString body_type, QNetOperation req_type) :
    QObject{nullptr}, net_operation_(req_type), url_(url), data_origin_(data), body_type_(body_type) {
}

HttpWorker2::~HttpWorker2() {
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_evt_, "::~HttpWorker2");
}

void HttpWorker2::process()
{
    QNetworkRequest request;
    request.setUrl(QUrl(url_));

    if (!body_type_.isEmpty())
        request.setHeader(QNetworkRequest::ContentTypeHeader, body_type_);
    network_manager_ = new QNetworkAccessManager();

    connect(network_manager_, &QNetworkAccessManager::finished, this, [&](QNetworkReply *reply) {
        QByteArray reply_data   = reply->readAll();
        QUrl       request_url  = reply->url();
        bool       is_succ      = reply->error() == QNetworkReply::NoError;
        auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "来自接口:{}的返回 请求数据是否成功:{}", request_url.toString().toStdString(), is_succ);

        if (!Json::accept(reply_data_c)) {
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误 错误信息:{} 返回内容:{}", reply->errorString().toStdString(), reply_data_c);
        } else {
            Json json_doc = Json::parse(reply_data_c);
            SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析成功 返回内容:{}", json_doc.dump());
            
        }
    });
    
    switch (net_operation_) {
    case QNetOperation::PostOperation:
        network_manager_->post(request, data_origin_);
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "post请求 url:{} 数据:{}", request.url().toString().toStdString(), data_origin_.toStdString());

        break;
    case QNetOperation::GetOperation:
        network_manager_->get(request);
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "get请求 url:{}", request.url().toString().toStdString());
        break;
    case QNetworkAccessManager::HeadOperation:
    case QNetworkAccessManager::PutOperation:
    case QNetworkAccessManager::DeleteOperation:
    case QNetworkAccessManager::CustomOperation:
    case QNetworkAccessManager::UnknownOperation:
        break;
    }
}
