﻿#ifndef HTTPCALLBACK_H
#define HTTPCALLBACK_H

#include <QJSValue>
#include <QObject>
#include <functional>
#include "EnumTool.h"
#include "HttpClient.h"

void httpCallback4Qml(QJSValue callback, HttpHandleType handle_type, QString data = "");
void httpCallback4Qml(QJSValue callback, int handle_type, QString data = "");

using CppCallbackStd = std::function<void(HttpHandleType, std::string)>;
// switch (http_handle_type)
// {
//     case HttpHandleType::HTTP_HANDLE__ON_FINISHED:
//     {
//         break;
//     }
//     case HttpHandleType::HTTP_HANDLE__ON_ERROR:
//     {
//         break;
//     }
//     case HttpHandleType::HTTP_HANDLE__ON_TIMEOUT:
//     {
//         break;
//     }
// }


void execAndCallback4Qml(AeaQt::HttpRequest &http_request, QJSValue callback);

void execAndCallback4Cpp(AeaQt::HttpRequest &http_request, CppCallbackStd callback);


#endif // HTTPCALLBACK_H
