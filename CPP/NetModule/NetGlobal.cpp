﻿#include "NetGlobal.h"

QString req_host_prefix;
QString req_host_prefix_mini_program;
QString req_host_prefix_face;
QString req_host_prefix_takeaway;
QString req_host_prefix__cash_upgrade = "http://update.allscm.top/cashUpgrade/";
QString net_status_global = "100";
QString requestUrlParama = "";
void refreshReqHostPrefixByConfig()
{
    auto config_tool             = ConfigTool::getInstance();
    req_host_prefix              = config_tool->getSetting(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX).toString();
    req_host_prefix_mini_program = config_tool->getSetting(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX_MINI_PROGRAM).toString();
    req_host_prefix_face         = config_tool->getSetting(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX_FACE).toString();
    req_host_prefix_takeaway     = config_tool->getSetting(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX_TAKEAWAY).toString();
}
