﻿#include "HttpCallback.h"

void httpCallback4Qml(QJSValue callback, int handle_type, QString data)
{
    if (!callback.isCallable())
        return;

    QJSValueList arglist;
    arglist.push_back(handle_type);
    arglist.push_back(data);
    callback.call(arglist);
}

void httpCallback4Qml(QJSValue callback, HttpHandleType handle_type, QString data)
{
    httpCallback4Qml(callback, static_cast<int>(handle_type), data);
}

void execAndCallback4Qml(AeaQt::HttpRequest &http_request, QJSValue callback)
{
    http_request
        .onFinished(
            [=](QString data)
            {
                httpCallback4Qml(callback, HttpHandleType::HTTP_HANDLE__ON_FINISHED, data);
            })
        .onFailed(
            [=](QString data)
            {
                httpCallback4Qml(callback, HttpHandleType::HTTP_HANDLE__ON_ERROR, data);
            })
        .onTimeout(
            [=]()
            {
                httpCallback4Qml(callback, HttpHandleType::HTTP_HANDLE__ON_TIMEOUT);
            })
        .exec();
}

void execAndCallback4Cpp(AeaQt::HttpRequest &http_request, CppCallbackStd callback_4_cpp)
{
    http_request
        .onFinished(
            [=](QString data)
            {
                callback_4_cpp(HttpHandleType::HTTP_HANDLE__ON_FINISHED, data.toStdString());
            })
        .onFailed(
            [=](QString data)
            {
                callback_4_cpp(HttpHandleType::HTTP_HANDLE__ON_ERROR, data.toStdString());
            })
        .onTimeout(
            [=]()
            {
                callback_4_cpp(HttpHandleType::HTTP_HANDLE__ON_TIMEOUT, "");
            })
        .exec();
}
