﻿#ifndef NETGLOBAL_H
#define NETGLOBAL_H

#include <QString>
#include "ConfModule/ConfigTool.h"

extern QString req_host_prefix;               // = "http://buyhoo.cc/";
extern QString req_host_prefix_mini_program;  //= "http://buyhoo.cc/";
extern QString req_host_prefix_face;          // = "http://face.buyhoo.cc/";
extern QString req_host_prefix_takeaway;      // = "http://buyhoo.cc/shop/";
extern QString req_host_prefix__cash_upgrade; // = "http://update.allscm.top/cashUpgrade/";
extern QString net_status_global;
extern QString requestUrlParama;

void refreshReqHostPrefixByConfig();

#endif // NETGLOBAL_H
