﻿#include "HttpWorker.h"

#include <LogModule/LogManager.h>
#include <QString>
#include <spdlog/spdlog.h>
#include "../ControlModule/ControlManager.h"
//#include "NetModule/NetGlobal.h"

HttpWorker::HttpWorker(QString url, QByteArray data, QString body_type, QNetOperation req_type) :
    QObject(nullptr), net_operation_(req_type), url_(url), data_origin_(data), body_type_(body_type)
{
}

HttpWorker::~HttpWorker()
{
    SPDLOG_LOGGER_TRACE(LogMgr::getInst()->logger_evt_, "::~HttpWorker");
    delete network_manager_;
}

void HttpWorker::process()
{
    QNetworkRequest request;
    request.setUrl(QUrl(url_));
    if (!body_type_.isEmpty())
        request.setHeader(QNetworkRequest::ContentTypeHeader, body_type_);
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString userIdValue = QString::number(shop_control->getPersonInfo().cashier_id);
    QString userNameValue = QString(shop_control->getPersonInfo().name);
    QString languageTemp = "";
    switch (ConfigTool::getInstance()->languageSelected().toInt()) {
    case 0:
        languageTemp = "zh_CN";
        break;
    case 1:
        languageTemp = "en_US";
        break;
    case 2:
        languageTemp = "vie_VN";
        break;
    case 3:
        languageTemp = "th_TH";
        break;
    case 4:
        languageTemp = "ru_RU";
        break;
    default:
        languageTemp = "zh_CN";
        break;
    }
    request.setRawHeader("userId", userIdValue.toUtf8());
    request.setRawHeader("userName", userNameValue.toUtf8());
    request.setRawHeader("language", languageTemp.toUtf8());

    network_manager_ = new QNetworkAccessManager();
    connect(network_manager_, &QNetworkAccessManager::finished, this, &HttpWorker::sendReply);

    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "语言:{}数据请求接口:{}", languageTemp.toStdString(),url_.toStdString());
    switch (net_operation_)
    {
    case QNetOperation::PostOperation:
        network_manager_->post(request, data_origin_);
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "post数据{}", data_origin_.toStdString());
        requestUrlParama = QString::fromStdString(data_origin_.toStdString());

        break;
    case QNetOperation::GetOperation:
        network_manager_->get(request);
        break;
    case QNetworkAccessManager::HeadOperation:
    case QNetworkAccessManager::PutOperation:
    case QNetworkAccessManager::DeleteOperation:
    case QNetworkAccessManager::CustomOperation:
    case QNetworkAccessManager::UnknownOperation:
        break;
    }
}

QString HttpWorker::img2Base64(QImage img)
{
    QByteArray byte_array;
    QBuffer    buffer(&byte_array);
    buffer.open(QIODevice::WriteOnly);
    img.save(&buffer, "jpg");

    //    QByteArray byte_array_compress = qCompress(byte_array, 9);

    return buffer.data().toBase64();
}
