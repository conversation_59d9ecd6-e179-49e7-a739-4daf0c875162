﻿#ifndef HTTPWORKER2_H
#define HTTPWORKER2_H

#include <QBuffer>
#include <QImage>
#include <QJsonObject>
#include <QJsonParseError>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QObject>
#include <QThread>
#include <string.h>
#include "json.hpp"

using Json          = nlohmann::json;
using QNetOperation = QNetworkAccessManager::Operation;

class HttpWorker2 : public QObject {
    Q_OBJECT
public:
    explicit HttpWorker2(QString url, QByteArray data, QString body_type = "application/json", QNetOperation req_type = QNetOperation::PostOperation);
    ~HttpWorker2();

    static QByteArray urlEncode(QMap<QString, QString> postMap) {
        QByteArray ret_str;

        for (auto iter = postMap.begin(); iter != postMap.end(); ++iter)
            ret_str += iter.key() + "=" + (iter->isEmpty() ? "\"\"" : iter.value()) + "&";

        ret_str.chop(1);
        return ret_str;
    }

    static QByteArray urlEncodeWithoutQuotes(QMap<QString, QString> postMap) {
        QByteArray ret_str;

        for (auto iter = postMap.begin(); iter != postMap.end(); ++iter)
            ret_str += iter.key() + "=" + (iter->isEmpty() ? "" : iter.value()) + "&";

        ret_str.chop(1);

        return ret_str;
    }

    static QByteArray urlEncode(Json postMap) {

        std::string ret_str;

        for (auto iter = postMap.begin(); iter != postMap.end(); ++iter) {
            std::string key_str   = iter.key();
            std::string value_str = iter.value().empty() ? R"("")" : iter.value();
            ret_str += key_str + "=" + value_str + "&";
        }

        ret_str.substr(0, ret_str.length() - 2);

        return QString::fromStdString(ret_str).toUtf8();
    }

    void process(); // 开始工作

    /*!
     * \brief img2Base64 图片转base64
     * \param img 图片
     * \return
     */
    static QString img2Base64(QImage img);

signals:
    void workStart(); // 工作开始
    void finished();  // 工作完成
    void sendReplyData(QNetworkReply *reply);

protected:
    QNetworkAccessManager *network_manager_ = nullptr;
    QNetOperation          net_operation_;
    QString                url_;
    QByteArray             data_origin_;
    QString                body_type_;

private:
    explicit HttpWorker2(QObject *parent = nullptr);
};

#endif // HTTPWORKER2_H
