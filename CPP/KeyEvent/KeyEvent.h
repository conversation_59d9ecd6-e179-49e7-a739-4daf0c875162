﻿#ifndef KEYEVENT_H
#define KEYEVENT_H

#include <QObject>
#include <QTimer>
#include <QVariant>
#include <mutex>

class KeyEvent : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isCapsLock READ isCapsLock NOTIFY isCapsLockChanged FINAL)

public:
    static KeyEvent *getInstance();
    Q_INVOKABLE void sendEvent(QVariant key_text);

    short            getCapsLockStatus();
    Q_INVOKABLE bool getIsShiftDown();
    Q_INVOKABLE void releaseShift();

    bool isCapsLock();

signals:
    void sendSwitchToMiniWindow();
    void isCapsLockChanged();

private:
    explicit KeyEvent(QObject *parent = nullptr);
    static KeyEvent  *pinstance_;
    static std::mutex mutex_;

    bool is_caps_lock_;

    QTimer caps_lock_timer_;
};

#endif // KEYEVENT_H
