﻿#include "KeyEvent.h"
#include <QDebug>
#include <QGuiApplication>
#include <QKeyEvent>
#include <windows.h>
#include "LogManager.h"

KeyEvent  *KeyEvent::pinstance_ = nullptr;
std::mutex KeyEvent::mutex_;

KeyEvent::KeyEvent(QObject *parent) : QObject{parent}
{
    //caps_lock_timer_.setInterval(1000 * 10);
    //    caps_lock_timer_.setInterval(1000);

    //    connect(&caps_lock_timer_, &QTimer::timeout,
    //            [=]
    //            {
    //                emit isCapsLockChanged();
    //            });

    //    caps_lock_timer_.start();
}

KeyEvent *KeyEvent::getInstance()
{
    std::lock_guard<std::mutex> lock(mutex_);
    if (pinstance_ == nullptr)
        pinstance_ = new KeyEvent();
    return pinstance_;
}

void KeyEvent::sendEvent(QVariant key_text)
{
    auto data = key_text.toString().toUpper();

    //qDebug() << "sendEvent: " << data;

    if (data == "Q")
    {
        keybd_event(81, 0, 0, 0);
        keybd_event(81, 0, 2, 0);
    }
    else if (data == "W")
    {
        keybd_event(87, 0, 0, 0);
        keybd_event(87, 0, 2, 0);
    }
    else if (data == "E")
    {
        keybd_event(69, 0, 0, 0);
        keybd_event(69, 0, 2, 0);
    }
    else if (data == "R")
    {
        keybd_event(82, 0, 0, 0);
        keybd_event(82, 0, 2, 0);
    }
    else if (data == "T")
    {
        keybd_event('T', 0, 0, 0);
        keybd_event('T', 0, 2, 0);
    }
    else if (data == "Y")
    {
        keybd_event(89, 0, 0, 0);
        keybd_event(89, 0, 2, 0);
    }
    else if (data == "U")
    {
        keybd_event(85, 0, 0, 0);
        keybd_event(85, 0, 2, 0);
    }
    else if (data == "I")
    {
        keybd_event(73, 0, 0, 0);
        keybd_event(73, 0, 2, 0);
    }
    else if (data == "O")
    {
        keybd_event(79, 0, 0, 0);
        keybd_event(79, 0, 2, 0);
    }
    else if (data == "P")
    {
        keybd_event(80, 0, 0, 0);
        keybd_event(80, 0, 2, 0);
    }
    else if (data == "A")
    {
        keybd_event(65, 0, 0, 0);
        keybd_event(65, 0, 2, 0);
    }
    else if (data == "S")
    {
        keybd_event(83, 0, 0, 0);
        keybd_event(83, 0, 2, 0);
    }
    else if (data == "D")
    {
        keybd_event(68, 0, 0, 0);
        keybd_event(68, 0, 2, 0);
    }
    else if (data == "F")
    {
        keybd_event(70, 0, 0, 0);
        keybd_event(70, 0, 2, 0);
    }
    else if (data == "G")
    {
        keybd_event(71, 0, 0, 0);
        keybd_event(71, 0, 2, 0);
    }
    else if (data == "H")
    {
        keybd_event(72, 0, 0, 0);
        keybd_event(72, 0, 2, 0);
    }
    else if (data == "J")
    {
        keybd_event(74, 0, 0, 0);
        keybd_event(74, 0, 2, 0);
    }
    else if (data == "K")
    {
        keybd_event(75, 0, 0, 0);
        keybd_event(75, 0, 2, 0);
    }
    else if (data == "L")
    {
        keybd_event(76, 0, 0, 0);
        keybd_event(76, 0, 2, 0);
    }
    else if (data == "Z")
    {
        keybd_event(90, 0, 0, 0);
        keybd_event(90, 0, 2, 0);
    }
    else if (data == "X")
    {
        keybd_event(88, 0, 0, 0);
        keybd_event(88, 0, 2, 0);
    }
    else if (data == "C")
    {
        keybd_event(67, 0, 0, 0);
        keybd_event(67, 0, 2, 0);
    }
    else if (data == "V")
    {
        keybd_event(86, 0, 0, 0);
        keybd_event(86, 0, 2, 0);
    }
    else if (data == "B")
    {
        keybd_event(66, 0, 0, 0);
        keybd_event(66, 0, 2, 0);
    }
    else if (data == "N")
    {
        keybd_event(78, 0, 0, 0);
        keybd_event(78, 0, 2, 0);
    }
    else if (data == "M")
    {
        keybd_event(77, 0, 0, 0);
        keybd_event(77, 0, 2, 0);
    }
    else if (data == "SHIFT")
    {
        if (!getIsShiftDown())
        {
            keybd_event(VK_SHIFT, 0, 0, 0); // 模拟按下shift键
            qDebug() << "模拟按下shift键";
        }
        else
        {
            keybd_event(VK_SHIFT, 0, 2, 0); // 模拟弹起shift键
            qDebug() << "模拟弹起shift键";
        }
    }
    else if (data == "SWITCH")
    {
        // 模拟按下ctrl键
        keybd_event(17, 0, 0, 0);
        // 模拟按下shift键
        keybd_event(32, 0, 0, 0);
        // 松开按键ctrl
        keybd_event(17, 0, 2, 0);
        // 松开按键shift
        keybd_event(32, 0, 2, 0);

        if (getCapsLockStatus() == 1)
        {
            keybd_event(20, 0, 0, 0); // Caps Lock
            keybd_event(20, 0, 2, 0); // Caps Lock
        }
    }
    else if (data == "CAPS_LOCK")
    {
        keybd_event(20, 0, 0, 0); // Caps Lock
        keybd_event(20, 0, 2, 0); // Caps Lock

        QTimer::singleShot(50,
                           [=]
                           {
                               emit isCapsLockChanged();
                           });
    }
    else if (data == "SPACE")
    {
        keybd_event(32, 0, 0, 0);
        keybd_event(32, 0, 2, 0);
    }
    else if (data == "BACKSPACE")
    {
        keybd_event(8, 0, 0, 0);
        keybd_event(8, 0, 2, 0);
        //        KEYEVENTF_KEYUP
    }
    else if (data == "COMMA")
    {
        keybd_event(VK_OEM_COMMA, 0, 0, 0);
        keybd_event(VK_OEM_COMMA, 0,KEYEVENTF_KEYUP, 0);
    }
    else if (data == "AT")
    {
        keybd_event(VK_SHIFT, 0, 0, 0);
        keybd_event('2', 0, 0, 0);
        keybd_event('2', 0, KEYEVENTF_KEYUP, 0);
        keybd_event(VK_SHIFT, 0, KEYEVENTF_KEYUP, 0);
    }
    else if (data == "ENTER")
    {
        keybd_event(13, 0, 0, 0);
        keybd_event(13, 0, 2, 0);
    }
    else if (data == "UP")
    {
        keybd_event(VK_UP, 0, 0, 0);
        keybd_event(VK_UP, 0, 2, 0);
    }
    else if (data == "DOWN")
    {
        keybd_event(VK_DOWN, 0, 0, 0);
        keybd_event(VK_DOWN, 0, 2, 0);
    }
    else if (data == "LEFT")
    {
        keybd_event(VK_LEFT, 0, 0, 0);
        keybd_event(VK_LEFT, 0, 2, 0);
    }
    else if (data == "RIGHT")
    {
        keybd_event(VK_RIGHT, 0, 0, 0);
        keybd_event(VK_RIGHT, 0, 2, 0);
    }
    else if (data == "0")
    {
        keybd_event('0', 0, 0, 0);
        keybd_event('0', 0, 2, 0);
    }
    else if (data == "1")
    {
        keybd_event(49, 0, 0, 0);
        keybd_event(49, 0, 2, 0);
    }
    else if (data == "2")
    {
        keybd_event(50, 0, 0, 0);
        keybd_event(50, 0, 2, 0);
    }
    else if (data == "3")
    {
        keybd_event(51, 0, 0, 0);
        keybd_event(51, 0, 2, 0);
    }
    else if (data == "4")
    {
        keybd_event(52, 0, 0, 0);
        keybd_event(52, 0, 2, 0);
    }
    else if (data == "5")
    {
        keybd_event(53, 0, 0, 0);
        keybd_event(53, 0, 2, 0);
    }
    else if (data == "6")
    {
        keybd_event(54, 0, 0, 0);
        keybd_event(54, 0, 2, 0);
    }
    else if (data == "7")
    {
        keybd_event(55, 0, 0, 0);
        keybd_event(55, 0, 2, 0);
    }
    else if (data == "8")
    {
        keybd_event(56, 0, 0, 0);
        keybd_event(56, 0, 2, 0);
    }
    else if (data == "9")
    {
        keybd_event(57, 0, 0, 0);
        keybd_event(57, 0, 2, 0);
    }
    else if (data == "+")
    {
        keybd_event(VK_ADD, 0, 0, 0);
        keybd_event(VK_ADD, 0, 2, 0);
    }
    else if (data == "-")
    {
        keybd_event(VK_SUBTRACT, 0, 0, 0);
        keybd_event(VK_SUBTRACT, 0, 2, 0);
    }
    else if (data == "_")
    {
        // TODO 键盘"_"
        //        keybd_event(VK_SUBTRACT, 0, 0, 0);
        //        keybd_event(VK_SUBTRACT, 0, 2, 0);
    }
    else if (data == "=")
    {
        keybd_event(109, 0, 0, 0);
        keybd_event(109, 0, 2, 0);
    }
    else if (data == "*")
    {
        keybd_event(VK_MULTIPLY, 0, 0, 0);
        keybd_event(VK_MULTIPLY, 0, 2, 0);
    }
    else if (data == "/")
    {
        keybd_event(VK_DIVIDE, 0, 0, 0);
        keybd_event(VK_DIVIDE, 0, 2, 0);
    }
    else if (data == ".")
    {
        keybd_event(VK_DECIMAL, 0, 0, 0);
        keybd_event(VK_DECIMAL, 0, 2, 0);
    }
    else if (data == "#")
    {
        keybd_event(16, 0, 0, 0);
        keybd_event(51, 0, 0, 0);
        keybd_event(51, 0, 2, 0);
        keybd_event(16, 0, 2, 0);
    }
    else if (data == "<")
    {
        keybd_event(189, 0, 0, 0);
        keybd_event(189, 0, 2, 0);
    }
    else if (data == ">")
    {
        keybd_event(187, 0, 0, 0);
        keybd_event(187, 0, 2, 0);
    }
    else if (data == ",")
    {
        keybd_event(188, 0, 0, 0);
        keybd_event(188, 0, 2, 0);
    }
    else if (data == " 。")
    {
        keybd_event(190, 0, 0, 0);
        keybd_event(190, 0, 2, 0);
    }
    else if (data == "DESKTOP")
    {
        emit sendSwitchToMiniWindow();
    }
}

short KeyEvent::getCapsLockStatus()
{
    return GetKeyState(VK_CAPITAL);
}

bool KeyEvent::getIsShiftDown()
{
    auto key_state = GetKeyState(16);
    if (key_state < 0)
        return true;
    return false;
}

void KeyEvent::releaseShift()
{
    keybd_event(VK_SHIFT, 0, 2, 0); // 模拟弹起shift键
}

bool KeyEvent::isCapsLock()
{
    auto result = GetKeyState(VK_CAPITAL) == 1;
    return result;
}
