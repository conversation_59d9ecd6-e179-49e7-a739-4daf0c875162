﻿#ifndef TAGEDITHELPER_H
#define TAGEDITHELPER_H

#include <QAbstractListModel>
#include <QObject>
#include "qobjectdefs.h"

class TagEditHelper : public QAbstractListModel
{
    Q_OBJECT

    Q_PROPERTY(QString goodsBarcode READ goodsBarcode WRITE setGoodsBarcode NOTIFY goodsBarcodeChanged FINAL)

public:
    explicit TagEditHelper(QObject *parent = nullptr);

    enum
    {
        NOT_USE = Qt::UserRole,
        TAG_ID,
        TAG_NAME
    };

    int      rowCount(const QModelIndex &parent) const override;
    QVariant data(const QModelIndex &index, int role) const override;

    virtual QHash<int, QByteArray> roleNames() const override;

    QString                  goodsBarcode();
    void                     setGoodsBarcode(QString barcode);
    QString                  goods_barcode_;
    std::vector<std::string> goods_tag_list_;

    Q_INVOKABLE void addTag(QString tag_name);
    Q_INVOKABLE void delTag(QString tag_name);
    Q_INVOKABLE void clearTag();

    Q_INVOKABLE void reloadTag();

    Q_INVOKABLE void saveTag();

    static void declareQML();

signals:
    void goodsBarcodeChanged();
};

#endif // TAGEDITHELPER_H
