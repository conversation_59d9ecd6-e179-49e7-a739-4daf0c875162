﻿#include "TagEditHelper.h"
#include <algorithm>
#include "DataModule/DataManager.h"
#include "WorkerModule/RecognitionWorker.h"

TagEditHelper::TagEditHelper(QObject *parent) : QAbstractListModel{parent}
{
}

int TagEditHelper::rowCount(const QModelIndex &parent) const
{
    //if (!parent.isValid())
    //    return 0;

    return goods_tag_list_.size();
}

QVariant TagEditHelper::data(const QModelIndex &index, int role) const
{
    auto goods_data = DataManager::getInstance()->getGoodsMgr();
    auto row_index  = index.row();

    if (goods_tag_list_.size() <= row_index)
        return QVariant();

    switch (role)
    {
    case NOT_USE:
        break;
    case TAG_ID:
        return row_index;
    case TAG_NAME:
        return QString::fromStdString(goods_tag_list_[row_index]);
    }

    return QVariant();
}

QHash<int, QByteArray> TagEditHelper::roleNames() const
{
    QHash<int, QByteArray> names;
    names[NOT_USE]  = "NOT_USE";
    names[TAG_ID]   = "TAG_ID";
    names[TAG_NAME] = "TAG_NAME";
    return names;
}

QString TagEditHelper::goodsBarcode()
{
    return goods_barcode_;
}

void TagEditHelper::setGoodsBarcode(QString barcode)
{
    goods_barcode_ = barcode;
    reloadTag();
    emit goodsBarcodeChanged();
}

void TagEditHelper::addTag(QString tag_name)
{
    beginResetModel();
    goods_tag_list_.push_back(tag_name.toStdString());
    endResetModel();
}

void TagEditHelper::delTag(QString tag_name)
{
    beginResetModel();
    auto find_iter = std::find(goods_tag_list_.begin(), goods_tag_list_.end(), tag_name.toStdString());

    if (find_iter != goods_tag_list_.end())
    {
        goods_tag_list_.erase(find_iter);
    }

    endResetModel();
}

void TagEditHelper::clearTag()
{
}

void TagEditHelper::reloadTag()
{
#ifdef _RECOGNITION_
    beginResetModel();

    goods_tag_list_.clear();

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    if (goods_barcode_ == "0")
    {
        goods_tag_list_ = RecognitionWorker::class_names_;
    }
    else
    {
        for (auto goods_tag : goods_mgr->goods_tag_list_)
        {
            if (goods_tag.goods_barcode == goods_barcode_.toStdString())
            {
                goods_tag_list_ = goods_tag.goods_tag_list;
                break;
            }
        }
    }
    endResetModel();
#endif
}

void TagEditHelper::saveTag()
{
#ifdef _RECOGNITION_

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto tag_iter = std::find_if(goods_mgr->goods_tag_list_.begin(), goods_mgr->goods_tag_list_.end(),
                                 [=](const GoodsTag &goods_tag)
                                 {
                                     return goods_tag.goods_barcode == goods_barcode_.toStdString();
                                 });

    if (tag_iter != goods_mgr->goods_tag_list_.end())
    {
        tag_iter->goods_tag_list = goods_tag_list_;
    }
    else
    {
        GoodsTag goods_tag;
        goods_tag.goods_barcode  = goods_barcode_.toStdString();
        goods_tag.goods_tag_list = goods_tag_list_;
        goods_mgr->goods_tag_list_.push_back(goods_tag);
    }

    goods_mgr->refreshTagMap();
    goods_mgr->saveTagList();
#endif
}


void TagEditHelper::declareQML()
{
    qmlRegisterType<TagEditHelper>("TagEditHelper", 1, 0, "TagEditHelper");
    //qmlRegisterUncreatableType<TagEditHelper>("TagEditHelper", 1, 0, "GoodsData", QStringLiteral("GoodsData should not be created in QML"));
}
