﻿#include "Utils.h"
#include <QColor>
#include <QDateTime>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <QGuiApplication>
#include <QList>
#include <QNetworkInterface>
#include <QRegularExpression>
#include <QScreen>
#include <QSettings>
#include <QSharedMemory>
#include <QSystemSemaphore>
#include <QTextCodec>
#include <cmath>
#include <iomanip>
#include <iostream>
#include <mutex>
#include <random>
#include <sstream>
#include "qrandom.h"
#include "LogModule/LogManager.h"

Utils::Utils(QObject *parent) : QObject{parent}
{
}

QString Utils::getAppDirPath()
{
    return qApp->applicationDirPath();
}

QString Utils::getDataBasePath()
{
    return qApp->applicationDirPath() + "/dataBase/";
}

QString Utils::getVersion()
{
    return "v1.1.2";
}

bool Utils::IsFileExist(QString fullFilePath)
{
    QFileInfo fileInfo(fullFilePath);
    if (fileInfo.exists())
        return true;
    return false;
}

QString Utils::getHostMacAddress()
{
    QList<QNetworkInterface> nets       = QNetworkInterface::allInterfaces(); // 获取所有网络接口列表
    int                      nCnt       = nets.count();
    QString                  strMacAddr = "";
    for (int i = 0; i < nCnt; i++)
    {
        // 如果此网络接口被激活并且正在运行并且不是回环地址，则就是我们需要找的Mac地址
        if (nets[i].flags().testFlag(QNetworkInterface::IsUp) && nets[i].flags().testFlag(QNetworkInterface::IsRunning) &&
            !nets[i].flags().testFlag(QNetworkInterface::IsLoopBack))
        {
            strMacAddr = nets[i].hardwareAddress();
            break;
        }
    }
    return strMacAddr;
}

QString Utils::getHostMacAddressWithoutColon()
{
    QList<QNetworkInterface> nets         = QNetworkInterface::allInterfaces(); // 获取所有网络接口列表
    int                      nCnt         = nets.count();
    QString                  str_mac_addr = "";
    for (int i = 0; i < nCnt; i++)
    {
        // 如果此网络接口被激活并且正在运行并且不是回环地址，则就是我们需要找的Mac地址
        if (nets[i].flags().testFlag(QNetworkInterface::IsUp) && nets[i].flags().testFlag(QNetworkInterface::IsRunning) &&
            !nets[i].flags().testFlag(QNetworkInterface::IsLoopBack))
        {
            str_mac_addr = nets[i].hardwareAddress();
            break;
        }
    }
    str_mac_addr = str_mac_addr.replace(QRegExp(":"), "");
    return str_mac_addr;
}

void Utils::setIsAutoRun(bool is_auto_run)
{
    QSettings reg("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", QSettings::NativeFormat);

    if (is_auto_run)
    {
        QString strAppPath = QDir::toNativeSeparators(QCoreApplication::applicationFilePath());
        reg.setValue("SortingSys", strAppPath);
    }
    else
    {
        reg.setValue("SortingSys", "");
    }
}

bool Utils::isAutoRun()
{
    QSettings reg("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", QSettings::NativeFormat);
    return reg.value("SortingSys").toString() != "";
}

QString Utils::floorDecimalStr(const double &value, int decimal_places)
{
    std::stringstream sstream1;
    sstream1 << std::fixed << std::setprecision(decimal_places) << value;
    auto final_str = sstream1.str();
    return QString::fromStdString(final_str);
}

double Utils::floorDecimal(const double &value, int decimal_places)
{
    std::stringstream sstream1;
    sstream1 << std::fixed << std::setprecision(decimal_places) << value;
    auto final_str = sstream1.str();
    return std::stod(final_str);
}


double Utils::floorDecimal2(const double &value, int decimal_places)
{
    int  multiplier = std::pow(10, decimal_places);
    auto tmp1       = (value + 0.000001) * multiplier;
    auto val        = std::floor(tmp1);
    auto result     = (double)val / multiplier;
    return result;
}

QString Utils::generateRandomNum(int length)
{
    return QString::number(QRandomGenerator::global()->generate() % (int)pow(10, length));
}

int Utils::getNumLength(unsigned long long num_in)
{
    int len = 0;              // 初始长度为0
    for (; num_in > 0; ++len) // 判断num是否大于0，否则长度+1
        num_in /= 10;         // 使用除法进行运算，直到num小于1
    return len;               // 返回长度的值
}

bool Utils::isValidPriceNum(QString num_str)
{
    QRegularExpression regex(QRegularExpression::anchoredPattern(QLatin1String(R"(^[0-9]{0,4}(?:\.[0-9]{1,2})?$)")));

    auto match_type = regex.match(num_str);
    return match_type.hasMatch();
}

QString Utils::mkMutiDir(const QString path)
{
    QDir dir(path);

    if (dir.exists(path))
    {
        return path;
    }

    QString parentDir = mkMutiDir(path.mid(0, path.lastIndexOf('/')));

    QString dirname = path.mid(path.lastIndexOf('/') + 1);

    QDir parentPath(parentDir);

    if (!dirname.isEmpty())

        parentPath.mkpath(dirname);

    return parentDir + "/" + dirname;
}

QString Utils::getColorWithOpacity(QString color, float opacity)
{
    QColor tmp_color(color);
    tmp_color.setAlphaF(opacity);

    QString hexColor = QString("#%1%2%3%4")
                           .arg(tmp_color.alpha(), 2, 16, QLatin1Char('0'))
                           .arg(tmp_color.red(), 2, 16, QLatin1Char('0'))
                           .arg(tmp_color.green(), 2, 16, QLatin1Char('0'))
                           .arg(tmp_color.blue(), 2, 16, QLatin1Char('0'));

    return hexColor;
}

//double Utils::roundDecimal(const double &value, int decimal_places)
//{
//    const int multiplier = std::pow(10.0, decimal_places);
//    auto      round_num  = (double)(int)std::round(value * multiplier) / multiplier;
//    return round_num;
//}存在溢出问题
double Utils::roundDecimal(const double &value, int decimal_places)
{
    const long long multiplier = std::llround(std::pow(10.0, decimal_places));
    long long round_num_ll = std::llround(value * multiplier);
    double round_num = static_cast<double>(round_num_ll) / multiplier;
    return round_num;
}
Utils::Screen *Utils::Screen::singleton_{nullptr};
std::mutex     Utils::Screen::mutex_;

Utils::Screen *Utils::Screen::GetInstance()
{
    std::lock_guard<std::mutex> lock(mutex_);
    if (singleton_ == nullptr)
        singleton_ = new Utils::Screen();
    return singleton_;
}

int Utils::Screen::GetScreenNum()
{
    return list_screen_.size();
}

double Utils::Screen::GetScreenWidth(int index)
{
    auto width = index < list_screen_.size() ? list_screen_.at(index)->geometry().width() : -1;
    return width;
}

int Utils::Screen::GetScreenHeight(int index)
{
    return index < list_screen_.size() ? list_screen_.at(index)->geometry().height() : -1;
}

Utils::Screen::Screen()
{
    list_screen_ = QGuiApplication::screens();
}

// Utils::DateTime *Utils::DateTime::singleton_{nullptr};
// std::mutex     Utils::DateTime::mutex_;

// Utils::DateTime *Utils::DateTime::GetInstance()
//{
//     std::lock_guard<std::mutex> lock(mutex_);
//     if (singleton_ == nullptr)
//         singleton_ = new Utils::DateTime();
//     return singleton_;
// }

QString Utils::DateTime::getCurYearDateStr()
{
    QDateTime dateTime(QDateTime::currentDateTime());
    return dateTime.toString("yyyy-MM-dd");
}

QString Utils::DateTime::getOffsetYearDateTimeStr(int year, int month, int day, int hour, int minute)
{
    QDateTime dateTime(QDateTime::currentDateTime());
    dateTime = dateTime.addYears(year);
    dateTime = dateTime.addMonths(month);
    dateTime = dateTime.addDays(day);
    dateTime = dateTime.addSecs(hour * 60 * 60 + minute * 60);
    return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}

QString Utils::DateTime::getCurDateStr()
{
    QDateTime dateTime(QDateTime::currentDateTime());
    return dateTime.toString("yyyy-MM-dd");
}

QString Utils::DateTime::getCurTimeStr()
{
    QDateTime dateTime(QDateTime::currentDateTime());
    return dateTime.toString("hh:mm:ss");
}

QString Utils::DateTime::getCurDateTimeStr()
{
    QDateTime dateTime(QDateTime::currentDateTime());
    return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}

QString Utils::DateTime::getTimestampMSecs()
{
    return QString::number(QDateTime::currentMSecsSinceEpoch());
}

int Utils::DateTime::getDaysByYearMonth(int year, int month)
{
    if (year < 0 || month > 12 || month < 0)
    {
        return -1;
    }
    int days = 0;
    if (month != 2)
    {
        switch (month)
        {
        case 1:
        case 3:
        case 5:
        case 7:
        case 8:
        case 10:
        case 12:
            days = 31;
            break;
        case 4:
        case 6:
        case 9:
        case 11:
            days = 30;
        }
    }
    else
    {
        // 闰年
        if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0)
        {
            days = 29;
        }
        else
        {
            days = 28;
        }
    }
    return days;
}

QString Utils::String::fillZeroFront(QString in_str, unsigned int num)
{
    std::stringstream str_stream;
    str_stream << std::setw(num) << std::setfill('0') << in_str.toStdString(); // 输出：0009
    return QString::fromStdString(str_stream.str());
}

bool Utils::String::isHavePrefixs(QString in_str, std::vector<QString> prefix_vec)
{
    if (in_str.length() != 13)
    {
        return false;
    }

    for (auto prefix_item : prefix_vec)
    {
        if (in_str.left(prefix_item.length()) == prefix_item)
        {
            return true;
        }
    }
    return false;
}

QString Utils::String::getClearNumStr(double num_in, int digital)
{
    auto tmp    = QString::number(num_in, 'f', digital).toDouble();
    auto result = QString::number(tmp, 'f', QLocale::FloatingPointShortest);
    return result;
}

std::string Utils::String::getClearNumStr2(std::string str_in)
{
    //删除尾部多余的0，如果尾部以点结束，也删除小数点
    std::string tmps = str_in;
    if (tmps.find(".") > 0)
    {
        size_t fp = tmps.rfind(".");
        size_t f  = tmps.rfind("0");
        while (f > fp)
        {

            if (f != -1)
            {
                tmps = tmps.erase(f);
            }
            f = tmps.rfind("0");
        }
        fp = tmps.rfind(".");
        if (fp == tmps.size() - 1)
        {
            tmps = tmps.erase(fp);
        }
        return (tmps);
    }

    return str_in;
}

QByteArray Utils::String::convert2Gbk(QString str_in)
{
    //INFO 要用ANSI编码!
    QTextCodec *utf8       = QTextCodec::codecForName("UTF-8");
    QTextCodec *gbk        = QTextCodec::codecForName("gbk");
    QString     strUnicode = utf8->toUnicode(str_in.toLocal8Bit().data());
    //2. unicode -> gbk, 得到QByteArray
    QByteArray gb_bytes = gbk->fromUnicode(strUnicode);
    // char      *p        = gb_bytes.data(); //获取其char *
    return gb_bytes;
}
