﻿#ifndef UTILS4QML_H
#define UTILS4QML_H

#include <QApplication>
#include <QDateTime>
#include <QObject>
#include <QVariant>
#include <QWindow>
#include <iomanip>
#include <memory.h>
#include <mutex>
#include <sstream>
#include "ConfModule/ConfigTool.h"
#include "EnumTool.h"
#include "GlobeData.h"
#include "LogModule/LogManager.h"
#include "Utils.h"
#include "Version.h"
#include "qobjectdefs.h"

class Utils4Qml : public QObject
{
    Q_OBJECT
public:
    static Utils4Qml *getInstance();

    Q_INVOKABLE int getScreenNum()
    {
        return Utils::Screen::GetInstance()->GetScreenNum();
    }
    Q_INVOKABLE QString getColorWithOpacity(QString color, float opacity = 1)
    {
        return Utils::getColorWithOpacity(color, opacity);
    };
    Q_INVOKABLE int getScreenWidth(int index = 0)
    {
        return Utils::Screen::GetInstance()->GetScreenWidth(index);
    }
    Q_INVOKABLE int getScreenHeight(int index = 0)
    {
        return Utils::Screen::GetInstance()->GetScreenHeight(index);
    }
    Q_INVOKABLE double getWidthRatio(int index = 0)
    {
        auto width_ratio = Utils::Screen::GetInstance()->GetScreenWidth(index) / 1920;
        return width_ratio;
    }
    Q_INVOKABLE int getHeightRatio(int index = 0)
    {
        return Utils::Screen::GetInstance()->GetScreenHeight(index) / 1080;
    }
    Q_INVOKABLE QString getAppDirPath()
    {
        return Utils::getAppDirPath();
    }
    Q_INVOKABLE QString getVersion()
    {
        return VERSION_NUM;
    }

    Q_INVOKABLE QString getCurDate()
    {
        return Utils::DateTime::getCurDateStr();
    }
    Q_INVOKABLE QString getCurTime()
    {
        return Utils::DateTime::getCurTimeStr();
    }
    Q_INVOKABLE QString getYesterdayTime()
    {
        return QDateTime::currentDateTime().addDays(-1).toString("yyyy-MM-dd");
    }

    Q_INVOKABLE QString getTimestampMSecs()
    {
        return Utils::DateTime::getTimestampMSecs();
    }

    Q_INVOKABLE QVariant roundDecimalStr(QVariant value, QVariant decimal_places = QVariant::fromValue(2))
    {
        return QVariant::fromValue(Utils::floorDecimalStr(Utils::roundDecimal(value.toDouble(), decimal_places.toInt()), decimal_places.toInt()));
    }

    Q_INVOKABLE QVariant getDaysByYearMonth(QVariant year, QVariant month)
    {
        return QVariant::fromValue(Utils::DateTime::getDaysByYearMonth(year.toUInt(), month.toUInt()));
    }

    void sendToast(QString msg, int level = static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__INFO))
    {
        emit sigOpenToast(msg, level);
    };

    void changeSecondScreenVisibleState(bool is_show)
    {
        emit sigChangeSecondScreenVisibleState(is_show);
    };

    Q_INVOKABLE bool isGoodsBarcode(QVariant in_str);

    Q_INVOKABLE bool isDaHuaNocodeGoodsBarcode(QString str_in);


    Q_INVOKABLE QVariant floorDecimal(QVariant value, QVariant decimal_places = 2)
    {
        return Utils::floorDecimalStr(value.toDouble(), decimal_places.toInt());
    }

    Q_INVOKABLE void shutdown()
    {
        Utils::System::shutdown();
    }

    Q_INVOKABLE QString getHostMacAddress()
    {
        return Utils::getHostMacAddress();
    }

    Q_INVOKABLE bool isValidPriceNum(QString num_str)
    {
        return Utils::isValidPriceNum(num_str);
    }

signals:
    void sigOpenToast(QString msg, int level = static_cast<int>(EnumTool::ToastLevelEnum::TOAST_LEVEL__INFO));

    void sigChangeSecondScreenVisibleState(QVariant is_show);

    void returnToPayPage();

    void sigPopupPayResultsClose();
    void sigPopupFacePayClose();

private:
    explicit Utils4Qml(QObject *parent = nullptr);
    static std::mutex                 mutex_;
    static std::unique_ptr<Utils4Qml> instance_;
};

#endif // UTILS4QML_H
