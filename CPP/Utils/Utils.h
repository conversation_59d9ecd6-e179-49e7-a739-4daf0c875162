﻿#ifndef UTILS_H
#define UTILS_H

#include <QCryptographicHash>
#include <QDir>
#include <QFile>
#include <QGuiApplication>
#include <QObject>
#include <QSettings>
#include <QVariant>
#include <Windows.h>
#include <iostream>
#include <mutex>
#include <regex>
#include <string>
#include "LogManager.h"

class QScreen;
class QSystemSemaphore;
class Utils : public QObject
{
    Q_OBJECT
public:
    explicit Utils(QObject *parent = nullptr);

    static QString getAppDirPath();
    static QString getVersion();
    static bool    IsFileExist(QString fullFilePath);

    //数据库
    static QString getDataBasePath();//获取数据库路径

    static QString getHostMacAddress();
    static QString getHostMacAddressWithoutColon();
    static void    setIsAutoRun(bool is_auto_run);
    static bool    isAutoRun();

    static QString floorDecimalStr(const double &value, int decimal_places = 2);
    static double  floorDecimal(const double &value, int decimal_places = 2);
    static double  roundDecimal(const double &value, int decimal_places = 2);

    static double floorDecimal2(const double &value, int decimal_places);

    static QString generateRandomNum(int length = 1);

    static int getNumLength(unsigned long long num_in);

    static bool isValidPriceNum(QString num_str);


    static QString mkMutiDir(const QString path);

    static QString getColorWithOpacity(QString color, float opacity = 1);

    class String;
    class Screen;
    class DateTime;
    class Encrypt;
    class Barcode;
    class Network;
    class System;
signals:
};


class Utils::String
{
public:
    static QString fillZeroFront(QString in_str, unsigned int num);

    static bool isHavePrefixs(QString in_str, std::vector<QString> prefix_vec);

    static QString     getClearNumStr(double num_in, int digital = 3);
    static std::string getClearNumStr2(std::string str_in);

    static QByteArray convert2Gbk(QString str_in);
};

class Utils::Screen
{
public:
    static Utils::Screen *GetInstance();

    int    GetScreenNum();
    double GetScreenWidth(int index = 0);
    int    GetScreenHeight(int index = 0);
    //    Singleton(Singleton &other)       = delete;
    //    void operator=(const Singleton &) = delete;

private:
    Screen();
    static std::mutex mutex_;

private:
    static Utils::Screen *singleton_;

    QList<QScreen *> list_screen_;
};

class Utils::DateTime
{
public:
    //    static Utils::DateTime *GetInstance();
    static QString getCurYearDateStr();
    static QString getOffsetYearDateTimeStr(int year = 0, int month = 0, int day = 0, int hour = 0, int minute = 0);
    static QString getCurDateStr();
    static QString getCurTimeStr();
    static QString getCurDateTimeStr();
    static QString getTimestampMSecs();
    static int     getDaysByYearMonth(int year, int month);
};

class Utils::Encrypt
{
public:
    static QString str2Md5(QString str)
    {
        return QCryptographicHash::hash(str.toUtf8(), QCryptographicHash::Md5).toHex();
    }

    static QString fileMd5(const QString &source_file_path)
    {
        QFile        source_file(source_file_path);
        qint64       file_size   = source_file.size();
        const qint64 buffer_size = 10240;

        if (source_file.open(QIODevice::ReadOnly))
        {
            char buffer[buffer_size];
            int  bytes_read;
            int  read_size = qMin(file_size, buffer_size);

            QCryptographicHash hash(QCryptographicHash::Md5);

            while (read_size > 0 && (bytes_read = source_file.read(buffer, read_size)) > 0)
            {
                file_size -= bytes_read;
                hash.addData(buffer, bytes_read);
                read_size = qMin(file_size, buffer_size);
            }

            source_file.close();
            return QString(hash.result().toHex()).toUpper();
        }
        return QString();
    }
};

class Utils::Barcode
{
public:
    static bool isEan13Valid(const QString barcode)
    {
        int sum = 0;

        for (int i = 1; i < 12; i += 2)
        {
            sum += barcode.at(i).digitValue();
        }
        int sum2 = 0;
        for (int i = 0; i < 12; i += 2)
        {
            sum2 += barcode.at(i).digitValue();
        }
        int count = sum * 3 + sum2;
        if ((10 - count % 10) % 10 == barcode.at(12).digitValue())
        {
            return true;
        }
        else
        {
            return false;
        }
    }
};

class Utils::Network
{

public:
    static bool isValidIp(const QString ip)
    {
        QRegExp pattern(R"(^(\d{1,3}\.){3}\d{1,3}$)");
        return pattern.exactMatch(ip);
    }
};

class Utils::System
{
public:
    static void setAutoRun(QString tag_str, bool is_auto_run = true)
    {

        QSettings reg("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run", QSettings::NativeFormat);
        if (is_auto_run)
        {
            QString strAppPath = QDir::toNativeSeparators(QCoreApplication::applicationFilePath());
            reg.setValue(tag_str, strAppPath);
            LOG_EVT_INFO("尝试设置开机自启动");
        }
        else
        {
            LOG_EVT_INFO("尝试取消开机自启动");
            reg.setValue(tag_str, "");
        }
    }
    static bool isAutoRun(QString tag_str)
    {
        QSettings reg("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run", QSettings::NativeFormat);
        return reg.value(tag_str).toString() != "";
    }

    static void shutdown()
    {
        system("shutdown -s -t 00");
    }
};

#endif // UTILS_H
