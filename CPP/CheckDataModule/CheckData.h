﻿#ifndef CHECKDATA_H
#define CHECKDATA_H

#include <QMap>
#include <QObject>
#include <QQmlEngine>
#include <QSharedPointer>
#include <QString>
#include <iostream>
#include <memory>
#include <mutex>

class CheckData : public QObject
{
    Q_OBJECT

    //Q_PROPERTY(float fontRatio READ fontRatio WRITE setFontRatio NOTIFY fontRatioChanged FINAL)


public:
    explicit CheckData(QObject *parent = nullptr);
    ~CheckData() override;
    static CheckData *getInstance();
    Q_INVOKABLE void mainMessageForPC(QJSValue callback);
    Q_INVOKABLE void saleStatisticsCycleRatio(QJSValue callback,QString type);
    Q_INVOKABLE void querySaleTotalByHour(QJSValue callback);
    Q_INVOKABLE void salesCategoryRatio(QJSValue callback,QString type);
    Q_INVOKABLE void paymentTypeRatio(QJSValue callback,QString type);
    Q_INVOKABLE void salesTrend(QJSValue callback,QString startTime, QString endTime);
    Q_INVOKABLE QString getPreSomeDay(QString day, int days);
    Q_INVOKABLE void overviewOfStock(QJSValue callback);
    Q_INVOKABLE void topGoodsSaleCount(QJSValue callback,QString dateType, QString standard, QString pageSize, QString order);
    Q_INVOKABLE void topGoodsPorfit(QJSValue callback,QString dateType, QString standard, QString pageSize, QString order);
    Q_INVOKABLE void queryUnmarketableTop5(QJSValue callback,QString dateType, QString standard, QString pageSize);

//    CheckData *getCheckDataControl()
//    {
//        return check_data_control_;
//    }

private:
    static std::unique_ptr<CheckData> singleton_;
    static std::mutex mutex_;
//    CheckData       *check_data_control_       = nullptr;


signals:

};

#endif // CHECKDATA_H
