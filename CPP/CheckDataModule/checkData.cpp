﻿#include "CheckData.h"
#include <QDateTime>
#include <QDir>
#include "json-qt.hpp"
#include "../NetModule/HttpWorker.h"
#include "../NetModule/NetGlobal.h"
#include "../ControlModule/ControlManager.h"

std::unique_ptr<CheckData> CheckData::singleton_;
std::mutex                      CheckData::mutex_;

CheckData::CheckData(QObject *parent) : QObject{parent}
{
//    check_data_control_       = new CheckData(this);
}
CheckData::~CheckData()
{
//    delete check_data_control_;
}
CheckData *CheckData::getInstance()
{
    std::lock_guard<std::mutex> lock(mutex_);

    if (singleton_ == nullptr)
    {
        singleton_.reset(new CheckData());
    }
    return singleton_.get();
}
void CheckData::mainMessageForPC(QJSValue callback)
{
    //LOG_EVT_INFO("收银界面新版-横轴接口");
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/pc/mainMessageForPC.do";
    bodyMap body_map;
    body_map["shopUnique"]           = QString::number(shop_control->getShopUnique());

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void CheckData::saleStatisticsCycleRatio(QJSValue callback,QString type)
{
    //LOG_EVT_INFO("销售数据周期占比");
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/pc/saleStatisticsCycleRatio.do";
    bodyMap body_map;
    body_map["shopUnique"]           = QString::number(shop_control->getShopUnique());
    body_map["dateType"]           = type;


    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void CheckData::querySaleTotalByHour(QJSValue callback)
{
    //LOG_EVT_INFO("营业额24H分布图");
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/pc/querySaleTotalByHour.do";
    bodyMap body_map;
    body_map["shopUnique"]           = QString::number(shop_control->getShopUnique());


    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void CheckData::overviewOfStock(QJSValue callback)
{
    LOG_EVT_INFO("库存指标概览");
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/pc/overviewOfStock.do";
    bodyMap body_map;
    body_map["shopUnique"]           = QString::number(shop_control->getShopUnique());


    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void CheckData::salesCategoryRatio(QJSValue callback,QString type)
{
    //LOG_EVT_INFO("销售品类占比");
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/pc/salesCategoryRatio.do";
    bodyMap body_map;
    body_map["shopUnique"]           = QString::number(shop_control->getShopUnique());
    body_map["dateType"]           = type;


    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void CheckData::paymentTypeRatio(QJSValue callback,QString type)
{
    LOG_EVT_INFO("支付分类占比");
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/pc/paymentTypeRatio.do";
    bodyMap body_map;
    body_map["shopUnique"]           = QString::number(shop_control->getShopUnique());
    body_map["dateType"]           = type;


    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void CheckData::salesTrend(QJSValue callback,QString startTime, QString endTime)
{
    LOG_EVT_INFO("销售额走势图");
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/pc/salesTrend.do";
    bodyMap body_map;
    body_map["shopUnique"]           = QString::number(shop_control->getShopUnique());
    body_map["startTime"]           = startTime;
    body_map["endTime"]           = endTime;

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
QString CheckData::getPreSomeDay(QString day, int days)
{
    QDateTime BeforeDay;
    QString BeforeDaystr;
    QStringList dayTmp = day.split("-");
    if (dayTmp.size() == 3)
    {
        if (dayTmp.at(1).length() == 1 && dayTmp.at(2).length() == 1)
        {
            BeforeDay = QDateTime::fromString(day,"yyyy-M-d");
        }
        else if (dayTmp.at(1).length() == 1 && dayTmp.at(2).length() == 2)
        {
            BeforeDay = QDateTime::fromString(day,"yyyy-M-dd");
        }
        else if (dayTmp.at(1).length() == 2 && dayTmp.at(2).length() == 1)
        {
            BeforeDay = QDateTime::fromString(day,"yyyy-MM-d");
        }
        else if (dayTmp.at(1).length() == 2 && dayTmp.at(2).length() == 2)
        {
            BeforeDay = QDateTime::fromString(day,"yyyy-MM-dd");
        }
        BeforeDaystr = BeforeDay.addDays(-days).toString("yyyy-MM-dd");
    }
    return BeforeDaystr;
}
void CheckData::topGoodsSaleCount(QJSValue callback,QString dateType, QString standard, QString pageSize, QString order)
{
    LOG_EVT_INFO("热销商品TOP5");
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/pc/topGoods.do";
    bodyMap body_map;
    body_map["shopUnique"]           = QString::number(shop_control->getShopUnique());
    body_map["dateType"]           = dateType;
    body_map["standard"]           = standard;
    body_map["pageSize"]           = pageSize;
    body_map["order"]           = order;

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void CheckData::topGoodsPorfit(QJSValue callback,QString dateType, QString standard, QString pageSize, QString order)
{
    LOG_EVT_INFO("累计利润TOP5");
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/pc/topGoods.do";
    bodyMap body_map;
    body_map["shopUnique"]           = QString::number(shop_control->getShopUnique());
    body_map["dateType"]           = dateType;
    body_map["standard"]           = standard;
    body_map["pageSize"]           = pageSize;
    body_map["order"]           = order;

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
void CheckData::queryUnmarketableTop5(QJSValue callback,QString dateType, QString standard, QString pageSize)
{
    LOG_EVT_INFO("滞销商品TOP5");
    auto shop_control = ControlManager::getInstance()->getShopControl();
    QString url = req_host_prefix + "shopmanager/pc/queryUnmarketableTop5.do";
    bodyMap body_map;
    body_map["shopUnique"]           = QString::number(shop_control->getShopUnique());
    body_map["dateType"]           = dateType;
    body_map["standard"]           = standard;
    body_map["pageSize"]           = pageSize;

    auto cur_thread_p = new QThread(this); // 工作线程

    HttpWorker *http_get_sales_record = new HttpWorker(url, HttpWorker::urlEncode(body_map), "application/x-www-form-urlencoded");
    http_get_sales_record->moveToThread(cur_thread_p);

    connect(cur_thread_p, &QThread::started, http_get_sales_record, &HttpWorker::process);       // 工作线程开始,开始处理
    connect(cur_thread_p, &QThread::finished, http_get_sales_record, &HttpWorker::deleteLater);  // 工作线程停止,销毁工作对象
    connect(http_get_sales_record, &HttpWorker::destroyed, cur_thread_p, &QThread::deleteLater); // 工作对象销毁,销毁工作线程
    connect(http_get_sales_record, &HttpWorker::sendReply, this,                                 // 接收到线程处理的数据,让线程退出
            [=](QNetworkReply *reply) mutable
            {
                QByteArray reply_data   = reply->readAll();
                bool       is_succ      = reply->error() == QNetworkReply::NoError;
                auto       reply_data_c = QString(reply_data).toUtf8().toStdString();

                SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "请求数据是否成功{}", is_succ);
                if (!Json::accept(reply_data_c))
                {
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "数据解析错误");
                }
                else
                {
                    Json json_doc = Json::parse(reply_data_c);

#ifdef BUYHOO_PARSE_DEBUG
                    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, json_doc.dump());
#endif
                    QJSValueList arglist;
                    arglist.push_back(QJSValue(QString::fromStdString(json_doc.dump())));

                    if (callback.isCallable())
                        auto ret = callback.call(arglist);
                }
                cur_thread_p->quit();
                cur_thread_p->wait();
            });
    // 启动线程
    cur_thread_p->start();
}
