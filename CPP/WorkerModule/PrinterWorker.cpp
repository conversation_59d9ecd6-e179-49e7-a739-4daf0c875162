﻿#include "PrinterWorker.h"
#include "ControlModule/PrinterControl.h"
#include "ControlModule/ControlManager.h"

#include <QPrinter>

PrinterWorker::PrinterWorker(QObject *parent) : QObject{parent}
{
}
/*
void PrinterWorker::printOrder(std::shared_ptr<ShopCartList> shop_cart_list, int print_num, std::string addition_info)
{
    auto printer_ctrl = ControlManager::getInstance()->getPrinterControl();

    auto ticket_printer_name = printer_ctrl->getTicketPrinterName();
    auto ticket_printer_num  = printer_ctrl->getTicketPrinterNum();

    if (!PrinterControl::isPrinterValid(ticket_printer_name))
        return;

    if (!printer_ctrl->getIsPrintTicket() && print_num < 1)
        return;

    float dpi_mm = 8;

    float   paper_width_mm  = 70;
    float   paper_height_mm = 800;
    QString bar_code        = QString("DH") + shop_cart_list->shop_cart_list_data_.order_unique_;
    int     font_size       = 36;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name);
    printer.setResolution(PrinterControl::mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setPageMargins(QMarginsF());
    printer.setPageSize(QPageSize(QPageSize::Custom));

    if (print_num > 0)
    {
        printer.setCopyCount(print_num);
    }
    else
    {
        printer.setCopyCount(ticket_printer_num);
    }

    QSize canvas_size = QSize(paper_width_mm * dpi_mm, paper_height_mm * dpi_mm);

    int cur_v_pos = 0;
    // int left_margin = 0;
    int left_margin  = 3 * dpi_mm;
    int right_margin = left_margin;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    int price_h_pos_r = 0;

    cur_v_pos += 10; // 上边距

    QPixmap pixmap(canvas_size);
    pixmap.fill(QColor(255, 255, 255));

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);
    {
        printItemInfo item_info;
        item_info.text = shop_name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "**************************************************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("品名　　　单价　 数量　 金额");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 商品详情
    {
        for (ShopCartItem &shop_cart_item : shop_cart_list->items())
        {
            GoodsInfo goods_info;
            goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);

            int max_height = 0;
            int name_width = 0;
            {
                printItemInfo item_info;
                //item_info.text = goods_info.goods_name;
                item_info.text = shop_cart_item.getGoodsName();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - left_margin - right_margin, item_info.getLineHeight());

                QFontMetrics metrics(item_info.font);
                QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
                painter.drawText(qrect, item_info.alignment_flag, elidedText);

                name_width = item_info.getLineWidth();

                QFontMetrics fm = painter.fontMetrics();
                name_width      = fm.width(item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();

                if (name_width > 110)
                    cur_v_pos += item_info.getLineHeight();
            }

            float cur_h_pos = 0;
            cur_h_pos += 18 * dpi_mm;

            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getPrice()); // QString::number(shop_cart_item.getPrice(), 'f', 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.goods_num_, 3);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getSubtotal());

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }
            cur_v_pos += max_height;
        }
    }
    QString discounted_amount;
    discounted_amount = Utils::String::getClearNumStr(shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment));
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "========================折后金额============================：{}",(discounted_amount).toStdString());
    QString       actual_payment_amount;
    if (shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__CASH)
    {
        actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_);
    }
    else if ((shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE) ||(shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__WECHAT_OFFLINE))
    {
        actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment));
         SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "========================实付金额============================：{}",(actual_payment_amount).toStdString());
    }
    else
    {
        actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_);

    }
        //ControlManager::getInstance()->getShopCartList()->resetAllInfo(); //重置数据

    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
        QString accounts_payable;
        accounts_payable =Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount());
    {
        printItemInfo item_info;
        item_info.text = tr("应付金额：　　") + accounts_payable + tr("元");
        LOG_EVT_INFO("========================应付金额============================：{}",shop_cart_list->getTotalGoodsPriceNoDiscount());
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;

        item_info.text =
            tr("折后金额：　　") + discounted_amount + tr("元");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();

    }
    {
        printItemInfo item_info;
        item_info.text = tr("实付金额：　　") + actual_payment_amount + tr("元");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);
        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();

    }
    {
        printItemInfo item_info;

        QString give_change;

        if (shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__CASH)
        {
            give_change = Utils::String::getClearNumStr(actual_payment_amount.toDouble() - discounted_amount.toDouble());
        }
        else
        {
            give_change = "0";
        }
        if (give_change.toDouble() < 0)
        {
            item_info.text = tr("抹零：　　　　") + Utils::String::getClearNumStr(std::abs(give_change.toDouble())) + tr("元");
        }
        else
        {
            item_info.text = tr("找零：　　　　") + give_change + tr("元");
        }

        if(give_change.toDouble() != 0){
            item_info.font.setPixelSize(font_size);
            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
            painter.setFont(item_info.font);

            auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
            painter.drawText(qrect, item_info.alignment_flag, item_info.text);

            cur_v_pos += qrect.height();
        }
    }

    {
        printItemInfo item_info;

        auto discount =   accounts_payable.toDouble() - discounted_amount.toDouble();
        if (discount < 0)
            discount = 0;
        if(discount != 0){
            item_info.text = tr("优惠：　　　　") + Utils::String::getClearNumStr(discount) + tr("元");
            item_info.font.setPixelSize(font_size);
            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
            painter.setFont(item_info.font);

            auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
            painter.drawText(qrect, item_info.alignment_flag, item_info.text);
            cur_v_pos += qrect.height();
        }
    }

    {
        printItemInfo item_info;
        item_info.text = tr("交易件数：　　") + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsQuantity().toDouble());
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("支付方式：　　") + OrderInfo::getPayMethodStr(shop_cart_list->shop_cart_list_data_.payment);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    //if (shop_cart_list->shop_cart_list_data_.payment == static_cast<int>(EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD))
    //只要有会员ID就打印会员信息
    if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
    {
        Json json_tmp = Json::parse(addition_info, nullptr, false);

        if (!json_tmp.is_discarded())
        {
            {
                printItemInfo item_info;

                QString member_name;

                if (json_tmp.contains("cusName"))
                {
                    member_name = getDataFromJson(json_tmp, "cusName").toString();
                }
                else
                {
                    member_name = getDataFromJson(json_tmp, "cus_name").toString();
                }

                QString member_name_processed;

                if (!member_name.isEmpty())
                {

                    QString name_begin;
                    QString name_middle = member_name;
                    QString name_end;
                    name_end = QString(*member_name.rbegin());

                    if (member_name.size() > 2)
                    {
                        name_begin = QString(*member_name.begin());
                        name_middle.remove(member_name.size() - 1, 1);
                    }

                    name_middle.remove(0, 1);
                    name_middle.fill('*');
                    member_name_processed = (name_begin + name_middle + name_end);
                }

                item_info.text = tr("会员名：　　　") + member_name_processed;

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;

                QString member_blance;

                if (json_tmp.contains("cusBalance"))
                {
                    member_blance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                else
                {
                    member_blance = getDataFromJson(json_tmp, "cus_balance").toString();
                }

                item_info.text = tr("会员余额：　　") + Utils::String::getClearNumStr(member_blance.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;

                QString member_point;

                if (json_tmp.contains("salePoints"))
                {
                    member_point = getDataFromJson(json_tmp, "salePoints").toString();
                }
                else
                {
                    member_point = getDataFromJson(json_tmp, "sale_points").toString();
                }

                item_info.text = tr("本单积分：　　") + Utils::String::getClearNumStr(member_point.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;

                QString member_point;

                if (json_tmp.contains("cusPoints"))
                {
                    member_point = getDataFromJson(json_tmp, "cusPoints").toString();
                }
                else
                {
                    member_point = getDataFromJson(json_tmp, "cus_points").toString();
                }

                item_info.text = tr("会员积分：　　") + Utils::String::getClearNumStr(member_point.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
        }
    }

    {
        printItemInfo item_info;
        item_info.text = tr("收银员：　　　") + ControlManager::getInstance()->getShopControl()->getPersonInfo().name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("收款机：　　　") + QString("1");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("备注：　　　") + ControlManager::getInstance()->getPrinterControl()->getTicketsRemarkInfo();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        // item_info.text = "订单时间:" + shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        // item_info.font.setWordSpacing(-50);
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 条码
    {
        QImage img = PrinterControl::Text2BarcodeImg(bar_code);
        img        = img.scaled(contain_w, 8 * dpi_mm);

        auto qrect = QRectF(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.drawPixmap(qrect.toRect(), QPixmap::fromImage(img));

        cur_v_pos += qrect.height();

        printItemInfo item_info;

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft;

        qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.setFont(item_info.font);
        painter.drawText(qrect, item_info.alignment_flag, bar_code);
    }

    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("联系电话:") + ControlManager::getInstance()->getShopControl()->getPersonInfo().phone;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("温馨提示:如需发票请联系前台");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }


    painter.end();
}
*/
void PrinterWorker::printOrder(std::shared_ptr<ShopCartList> shop_cart_list, int print_num, std::string addition_info)
{
    LOG_EVT_INFO("========================进入打印接口=====================");
    auto printer_ctrl = ControlManager::getInstance()->getPrinterControl();

    auto ticket_printer_name = printer_ctrl->getTicketPrinterName();
    auto ticket_printer_num  = printer_ctrl->getTicketPrinterNum();

    if (!PrinterControl::isPrinterValid(ticket_printer_name))
        return;

    if (!printer_ctrl->getIsPrintTicket() && print_num < 1)
        return;

    float dpi_mm = 8;

    float   paper_width_mm  = 50;
    float   paper_height_mm = 800;
    QString bar_code        = QString("DH") + shop_cart_list->shop_cart_list_data_.order_unique_;
    int     font_size       = 24;
    double amountTo         = .0;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name);
    printer.setResolution(PrinterControl::mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setPageMargins(QMarginsF());
    printer.setPageSize(QPageSize(QPageSize::Custom));

    if (print_num > 0)
    {
        printer.setCopyCount(print_num);
    }
    else
    {
        printer.setCopyCount(ticket_printer_num);
    }

    QSize canvas_size = QSize(paper_width_mm * dpi_mm, paper_height_mm * dpi_mm);

    int cur_v_pos = 0;
    // int left_margin = 0;
    int left_margin  = 3 * dpi_mm;
    int right_margin = left_margin;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    int price_h_pos_r = 0;

    cur_v_pos += 10; // 上边距
    int right_padding = 40;

    QPixmap pixmap(canvas_size);
    pixmap.fill(QColor(255, 255, 255));

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);
    {
        printItemInfo item_info;
        item_info.text = "** "+shop_name + " **";
        item_info.font.setPixelSize(font_size + 4);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);
        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("结账单");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("收银机：") + QString("1");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("收银员：") + ControlManager::getInstance()->getShopControl()->getPersonInfo().name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("品名　　　数量　 单价　 合计");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 商品详情
    {
        for (ShopCartItem &shop_cart_item : shop_cart_list->items())
        {
            GoodsInfo goods_info;
            goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);

            int max_height = 0;
            int name_width = 0;
            {
                printItemInfo item_info;
                //item_info.text = goods_info.goods_name;
                item_info.text = shop_cart_item.getGoodsName();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - left_margin - right_margin, item_info.getLineHeight());

                QFontMetrics metrics(item_info.font);
                QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
                painter.drawText(qrect, item_info.alignment_flag, elidedText);

                name_width = item_info.getLineWidth();

                QFontMetrics fm = painter.fontMetrics();
                name_width      = fm.width(item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();

                if (name_width > 110)
                    cur_v_pos += item_info.getLineHeight();
            }

            float cur_h_pos = 0;
            cur_h_pos += 18 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = "X"+Utils::String::getClearNumStr(shop_cart_item.goods_num_, 3);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }
//            {
//                printItemInfo item_info;
//                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getPrice()); // QString::number(shop_cart_item.getPrice(), 'f', 2);
//                item_info.font.setPixelSize(font_size);
//                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//                painter.setFont(item_info.font);

//                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
//                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//                if (max_height < qrect.height())
//                    max_height = qrect.height();
//            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getPrice()); // QString::number(shop_cart_item.getPrice(), 'f', 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }
//            {
//                printItemInfo item_info;
//                item_info.text = Utils::String::getClearNumStr(shop_cart_item.goods_num_, 3);
//                item_info.font.setPixelSize(font_size);
//                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//                painter.setFont(item_info.font);

//                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
//                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//                if (max_height < qrect.height())
//                    max_height = qrect.height();
//            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getSubtotal());
                amountTo += shop_cart_item.getSubtotal();

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }
            cur_v_pos += max_height;
        }
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    //合计
    {
        printItemInfo item_info;
//        item_info.text = tr("合计：　　") + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsQuantity().toDouble()) +"                       "
//                +Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount());

        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("合计：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

//        QString point_text1 = "X" + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsQuantity().toDouble());
//        item_info.text = point_text1;
//        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
//        int point_width1 = fontMetrics.horizontalAdvance(point_text1);
//        int right_padding1 = 220;
//        double value_x_pos1 = canvas_size.width() - right_margin - right_padding1 - point_width1;
//        auto qrect_value1 = QRectF(value_x_pos1, cur_v_pos, point_width1, item_info.getLineHeight());
//        painter.drawText(qrect_value1, item_info.alignment_flag, item_info.text);

        int start_x_for_quantity = qrect_label.right();
        int right_padding_for_quantity = 50;
        QString point_text1 = "X" + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsQuantity().toDouble());
        item_info.text = point_text1;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int point_width1 = fontMetrics.horizontalAdvance(point_text1);
        auto qrect_value1 = QRectF(start_x_for_quantity + right_padding_for_quantity, cur_v_pos, point_width1, item_info.getLineHeight());
        painter.drawText(qrect_value1, item_info.alignment_flag, item_info.text);

        //QString point_text = Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount());
        QString point_text = Utils::String::getClearNumStr(amountTo);
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    QString discounted_amount;
    discounted_amount = Utils::String::getClearNumStr(shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment));
     QString       actual_payment_amount;
    if (shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__CASH)
    {
        actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_);
    }
    else if ((shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE) ||(shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__WECHAT_OFFLINE))
    {
        actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_);
    }
    else
    {
         actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_);
    }
        //ControlManager::getInstance()->getShopCartList()->resetAllInfo(); //重置数据

//    {
//        printItemInfo item_info;
//        item_info.text = "********************************************************";
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }
        QString accounts_payable;
        accounts_payable =Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount());
    {
        printItemInfo item_info;
        LOG_EVT_INFO("========================应付金额============================：{}",shop_cart_list->getTotalGoodsPriceNoDiscount());
        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("应付：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        QString point_text = accounts_payable;
        //QString point_text = Utils::String::getClearNumStr(amountTo);
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();
    }
    QString totalDiscount = Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount() - shop_cart_list->shop_cart_list_data_.cash_received_);
    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("合计优惠：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
        {
            QString point_text = totalDiscount;
            //QString point_text = Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount() - amountTo);
            item_info.text = point_text;
            item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
            int point_width = fontMetrics.horizontalAdvance(point_text);
            double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
            auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
            painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
            cur_v_pos += item_info.getLineHeight();
        }
        else{
            QString point_text = Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount() - amountTo);
            item_info.text = point_text;
            item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
            int point_width = fontMetrics.horizontalAdvance(point_text);
            double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
            auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
            painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
            cur_v_pos += item_info.getLineHeight();
        }

    }
//    {
//        printItemInfo item_info;

//        item_info.text =
//            tr("折后金额：　　") + discounted_amount + tr("元");
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();

//    }
    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("实付：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
        {
            QString point_text = actual_payment_amount;
            item_info.text = point_text;
            item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
            int point_width = fontMetrics.horizontalAdvance(point_text);
            double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
            auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
            painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
            cur_v_pos += item_info.getLineHeight();
        }
        else{
            QString point_text = Utils::String::getClearNumStr(amountTo);
            item_info.text = point_text;
            item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
            int point_width = fontMetrics.horizontalAdvance(point_text);
            double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
            auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
            painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
            cur_v_pos += item_info.getLineHeight();
        }

    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
//    {
//        printItemInfo item_info;

//        QString give_change;

//        if (shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__CASH)
//        {
//            give_change = Utils::String::getClearNumStr(actual_payment_amount.toDouble() - discounted_amount.toDouble());
//        }
//        else
//        {
//            give_change = "0";
//        }
//        if (give_change.toDouble() < 0)
//        {
//            item_info.text = tr("抹零：　　　　") + Utils::String::getClearNumStr(std::abs(give_change.toDouble())) + tr("元");
//        }
//        else
//        {
//            item_info.text = tr("找零：　　　　") + give_change + tr("元");
//        }

//        if(give_change.toDouble() != 0){
//            item_info.font.setPixelSize(font_size);
//            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//            painter.setFont(item_info.font);

//            auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//            painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//            cur_v_pos += qrect.height();
//        }
//    }

//    {
//        printItemInfo item_info;

//        auto discount =   accounts_payable.toDouble() - discounted_amount.toDouble();
//        if (discount < 0)
//            discount = 0;
//        if(discount != 0){
//            item_info.text = tr("优惠：　　　　") + Utils::String::getClearNumStr(discount) + tr("元");
//            item_info.font.setPixelSize(font_size);
//            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//            painter.setFont(item_info.font);

//            auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//            painter.drawText(qrect, item_info.alignment_flag, item_info.text);
//            cur_v_pos += qrect.height();
//        }
//    }

//    {
//        printItemInfo item_info;
//        item_info.text = tr("交易件数：　　") + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsQuantity().toDouble());
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }

    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("支付方式");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        QString point_text = OrderInfo::getPayMethodStr(shop_cart_list->shop_cart_list_data_.payment);
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();

    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    //if (shop_cart_list->shop_cart_list_data_.payment == static_cast<int>(EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD))
    //只要有会员ID就打印会员信息
    if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
    {
        Json json_tmp = Json::parse(addition_info, nullptr, false);

        if (!json_tmp.is_discarded())
        {
            {
                printItemInfo item_info;

                QString member_name;

                if (json_tmp.contains("cusName"))
                {
                    member_name = getDataFromJson(json_tmp, "cusName").toString();
                }
                else
                {
                    member_name = getDataFromJson(json_tmp, "cus_name").toString();
                }

                QString member_name_processed;

                if (!member_name.isEmpty())
                {

                    QString name_begin;
                    QString name_middle = member_name;
                    QString name_end;
                    name_end = QString(*member_name.rbegin());

                    if (member_name.size() > 2)
                    {
                        name_begin = QString(*member_name.begin());
                        name_middle.remove(member_name.size() - 1, 1);
                    }

                    name_middle.remove(0, 1);
                    name_middle.fill('*');
                    member_name_processed = (name_begin + name_middle + name_end);
                }

                item_info.font.setPixelSize(font_size);
                painter.setFont(item_info.font);
                QFontMetrics fontMetrics(painter.font());
                QString label_text = tr("会员：");
                item_info.text = label_text;
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                int label_width = fontMetrics.horizontalAdvance(label_text);
                auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
                painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

                QString point_text = member_name_processed;
                item_info.text = point_text;
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                int point_width = fontMetrics.horizontalAdvance(point_text);
                double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
                auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
                painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
                cur_v_pos += item_info.getLineHeight();

            }
            {
                printItemInfo item_info;
                QString cusBalance;
                if (json_tmp.contains("cusBalance"))
                {
                    cusBalance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                else
                {
                    cusBalance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                item_info.font.setPixelSize(font_size);
                painter.setFont(item_info.font);
                QFontMetrics fontMetrics(painter.font());

                QString label_text = tr("会员余额：");
                item_info.text = label_text;
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                int label_width = fontMetrics.horizontalAdvance(label_text);

                auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
                painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

                QString point_text = Utils::String::getClearNumStr(cusBalance.toDouble(), 2);
                item_info.text = point_text;
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                int point_width = fontMetrics.horizontalAdvance(point_text);
                double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
                auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
                painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);

                cur_v_pos += item_info.getLineHeight();
            }
            {
                printItemInfo item_info;
                QString member_point;
                if (json_tmp.contains("salePoints"))
                {
                    member_point = getDataFromJson(json_tmp, "salePoints").toString();
                }
                else
                {
                    member_point = getDataFromJson(json_tmp, "sale_points").toString();
                }
                item_info.font.setPixelSize(font_size);
                painter.setFont(item_info.font);
                QFontMetrics fontMetrics(painter.font());

                QString label_text = tr("本单积分：");
                item_info.text = label_text;
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                int label_width = fontMetrics.horizontalAdvance(label_text);

                auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
                painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

                QString point_text = Utils::String::getClearNumStr(member_point.toDouble(), 2);
                item_info.text = point_text;
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                int point_width = fontMetrics.horizontalAdvance(point_text);
                double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
                auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
                painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);

                cur_v_pos += item_info.getLineHeight();
            }

            {
                printItemInfo item_info;

                QString member_point;

                if (json_tmp.contains("cusPoints"))
                {
                    member_point = getDataFromJson(json_tmp, "cusPoints").toString();
                }
                else
                {
                    member_point = getDataFromJson(json_tmp, "cus_points").toString();
                }
                item_info.font.setPixelSize(font_size);
                painter.setFont(item_info.font);
                QFontMetrics fontMetrics(painter.font());
                QString label_text = tr("会员积分：");
                item_info.text = label_text;
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                int label_width = fontMetrics.horizontalAdvance(label_text);
                auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
                painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

                QString point_text = Utils::String::getClearNumStr(member_point.toDouble(), 2);
                item_info.text = point_text;
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                int point_width = fontMetrics.horizontalAdvance(point_text);
                double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
                auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
                painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
                cur_v_pos += item_info.getLineHeight();
            }
            {
                printItemInfo item_info;
                item_info.text = "--------------------------------------------------------";
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
        }
    }

//    {
//        printItemInfo item_info;
//        item_info.text = tr("收银员：　　　") + ControlManager::getInstance()->getShopControl()->getPersonInfo().name;
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }

//    {
//        printItemInfo item_info;
//        item_info.text = tr("收款机：　　　") + QString("1");
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }
    {
        printItemInfo item_info;
        item_info.text ="--------------------------------------------------------";
        item_info.text =tr("***********备注***********");
        item_info.font.setPixelSize(font_size+2);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = ControlManager::getInstance()->getPrinterControl()->getTicketsRemarkInfo();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        // item_info.text = "订单时间:" + shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        // item_info.font.setWordSpacing(-50);
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 条码
    {
        QImage img = PrinterControl::Text2BarcodeImg(bar_code);
        img        = img.scaled(contain_w, 8 * dpi_mm);

        auto qrect = QRectF(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.drawPixmap(qrect.toRect(), QPixmap::fromImage(img));

        cur_v_pos += qrect.height();

        printItemInfo item_info;

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft;

        qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.setFont(item_info.font);
        painter.drawText(qrect, item_info.alignment_flag, bar_code);
    }

    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("联系电话:") + ControlManager::getInstance()->getShopControl()->getPersonInfo().phone;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("温馨提示:如需发票请联系前台");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }


    painter.end();
}

void PrinterWorker::printOrderCombine(std::shared_ptr<ShopCartList> shop_cart_list, int print_num, std::string addition_info, double cash, double online,double totalPay)
{
    auto printer_ctrl = ControlManager::getInstance()->getPrinterControl();

    auto ticket_printer_name = printer_ctrl->getTicketPrinterName();
    auto ticket_printer_num  = printer_ctrl->getTicketPrinterNum();

    if (!PrinterControl::isPrinterValid(ticket_printer_name))
        return;

    if (!printer_ctrl->getIsPrintTicket() && print_num < 1)
        return;

    float dpi_mm = 8;

    float   paper_width_mm  = 50;
    float   paper_height_mm = 800;
    QString bar_code        = QString("DH") + shop_cart_list->shop_cart_list_data_.order_unique_;
    int     font_size       = 24;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name);
    printer.setResolution(PrinterControl::mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setPageMargins(QMarginsF());
    printer.setPageSize(QPageSize(QPageSize::Custom));

    if (print_num > 0)
    {
        printer.setCopyCount(print_num);
    }
    else
    {
        printer.setCopyCount(ticket_printer_num);
    }

    QSize canvas_size = QSize(paper_width_mm * dpi_mm, paper_height_mm * dpi_mm);

    int cur_v_pos = 0;
    // int left_margin = 0;
    int left_margin  = 3 * dpi_mm;
    int right_margin = left_margin;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    int price_h_pos_r = 0;

    cur_v_pos += 10; // 上边距
    int right_padding = 40;

    QPixmap pixmap(canvas_size);
    pixmap.fill(QColor(255, 255, 255));

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);
    {
        printItemInfo item_info;
        item_info.text = "** "+shop_name + " **";
        item_info.font.setPixelSize(font_size + 4);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);
        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);
        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("结账单1");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("收银机：") + QString("1");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("收银员：") + ControlManager::getInstance()->getShopControl()->getPersonInfo().name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("品名　　　数量　 单价　 合计");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 商品详情
    {
        for (ShopCartItem &shop_cart_item : shop_cart_list->items())
        {
            GoodsInfo goods_info;
            goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);

            int max_height = 0;
            int name_width = 0;
            {
                printItemInfo item_info;
                //item_info.text = goods_info.goods_name;
                item_info.text = shop_cart_item.getGoodsName();
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - left_margin - right_margin, item_info.getLineHeight());

                QFontMetrics metrics(item_info.font);
                QString      elidedText = metrics.elidedText(item_info.text, Qt::ElideRight, qrect.width());
                painter.drawText(qrect, item_info.alignment_flag, elidedText);

                name_width = item_info.getLineWidth();

                QFontMetrics fm = painter.fontMetrics();
                name_width      = fm.width(item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();

                if (name_width > 110)
                    cur_v_pos += item_info.getLineHeight();
            }

            float cur_h_pos = 0;
            cur_h_pos += 18 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = "X"+Utils::String::getClearNumStr(shop_cart_item.goods_num_, 3);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }

            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getPrice()); // QString::number(shop_cart_item.getPrice(), 'f', 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }
            cur_h_pos += 10.5 * dpi_mm;
            {
                printItemInfo item_info;
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getSubtotal());

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(cur_h_pos, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                if (max_height < qrect.height())
                    max_height = qrect.height();
            }
            cur_v_pos += max_height;
        }
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    //合计
    {
        printItemInfo item_info;

        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("合计：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        int start_x_for_quantity = qrect_label.right();
        int right_padding_for_quantity = 50;
        QString point_text1 = "X" + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsQuantity().toDouble());
        item_info.text = point_text1;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int point_width1 = fontMetrics.horizontalAdvance(point_text1);
        auto qrect_value1 = QRectF(start_x_for_quantity + right_padding_for_quantity, cur_v_pos, point_width1, item_info.getLineHeight());
        painter.drawText(qrect_value1, item_info.alignment_flag, item_info.text);

        QString point_text = Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount());
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    QString discounted_amount;
    discounted_amount = Utils::String::getClearNumStr(shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment));
    QString       actual_payment_amount;
    LOG_EVT_INFO("shop_cart_list->shop_cart_list_data_.payment============================：{}",shop_cart_list->shop_cart_list_data_.payment);
    if (shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__CASH)
    {
        actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_);
    }
    else if ((shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__ALIPAY_OFFLINE) ||(shop_cart_list->shop_cart_list_data_.payment == (int)EnumTool::PayMethodEnum::PAY_METHOD__WECHAT_OFFLINE))
    {
        actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment));

    }
    else
    {
        if(OrderInfo::getPayMethodStr(shop_cart_list->shop_cart_list_data_.payment) == "组合支付"){
            actual_payment_amount = Utils::String::getClearNumStr(totalPay);
            LOG_EVT_INFO("储值卡实付金额============================：{}",(actual_payment_amount).toStdString());
        }else{
            actual_payment_amount = Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_);
        }

    }
        QString accounts_payable;
        accounts_payable =Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount());
    {
        printItemInfo item_info;
        LOG_EVT_INFO("========================应付金额============================：{}",shop_cart_list->getTotalGoodsPriceNoDiscount());
        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("应付：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        QString point_text = accounts_payable;
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();
    }
    QString totalDiscount = Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsPriceNoDiscount() - totalPay);
    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("合计优惠：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        QString point_text = totalDiscount;
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();

    }
    {
        printItemInfo item_info;        
        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("实付：");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        QString point_text = actual_payment_amount;
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();

    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        painter.setFont(item_info.font);
        QFontMetrics fontMetrics(painter.font());
        QString label_text = tr("支付方式");
        item_info.text = label_text;
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        int label_width = fontMetrics.horizontalAdvance(label_text);
        auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
        painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

        QString point_text = OrderInfo::getPayMethodStr(shop_cart_list->shop_cart_list_data_.payment);
        item_info.text = point_text;
        item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
        int point_width = fontMetrics.horizontalAdvance(point_text);
        double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
        auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
        painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
        cur_v_pos += item_info.getLineHeight();

    }
    if(cash != 0){
        {
            printItemInfo item_info;
            item_info.font.setPixelSize(font_size);
            painter.setFont(item_info.font);
            QFontMetrics fontMetrics(painter.font());
            QString label_text = tr("现金");
            item_info.text = label_text;
            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
            int label_width = fontMetrics.horizontalAdvance(label_text);
            auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
            painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

            QString point_text = Utils::String::getClearNumStr(cash);
            item_info.text = point_text;
            item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
            int point_width = fontMetrics.horizontalAdvance(point_text);
            double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
            auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
            painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
            cur_v_pos += item_info.getLineHeight();

        }
    }
    if(online != 0){
        {
            printItemInfo item_info;
            item_info.font.setPixelSize(font_size);
            painter.setFont(item_info.font);
            QFontMetrics fontMetrics(painter.font());
            QString label_text = tr("金圈收款");
            item_info.text = label_text;
            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
            int label_width = fontMetrics.horizontalAdvance(label_text);
            auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
            painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

            QString point_text = Utils::String::getClearNumStr(online);
            item_info.text = point_text;
            item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
            int point_width = fontMetrics.horizontalAdvance(point_text);
            double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
            auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
            painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
            cur_v_pos += item_info.getLineHeight();

        }
    }
    if((totalPay - cash -online) > 0.01){
        {
            printItemInfo item_info;
            item_info.font.setPixelSize(font_size);
            painter.setFont(item_info.font);
            QFontMetrics fontMetrics(painter.font());
            QString label_text = tr("储值卡");
            item_info.text = label_text;
            item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
            int label_width = fontMetrics.horizontalAdvance(label_text);
            auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
            painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

            QString point_text = Utils::String::getClearNumStr(totalPay - cash -online);
            item_info.text = point_text;
            item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
            int point_width = fontMetrics.horizontalAdvance(point_text);
            double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
            auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
            painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
            cur_v_pos += item_info.getLineHeight();

        }
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    //if (shop_cart_list->shop_cart_list_data_.payment == static_cast<int>(EnumTool::PayMethodEnum::PAY_METHOD__VIPCARD))
    //只要有会员ID就打印会员信息
    if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
    {
        Json json_tmp = Json::parse(addition_info, nullptr, false);

        if (!json_tmp.is_discarded())
        {
            {
                printItemInfo item_info;

                QString member_name;

                if (json_tmp.contains("cusName"))
                {
                    member_name = getDataFromJson(json_tmp, "cusName").toString();
                }
                else
                {
                    member_name = getDataFromJson(json_tmp, "cus_name").toString();
                }

                QString member_name_processed;

                if (!member_name.isEmpty())
                {

                    QString name_begin;
                    QString name_middle = member_name;
                    QString name_end;
                    name_end = QString(*member_name.rbegin());

                    if (member_name.size() > 2)
                    {
                        name_begin = QString(*member_name.begin());
                        name_middle.remove(member_name.size() - 1, 1);
                    }

                    name_middle.remove(0, 1);
                    name_middle.fill('*');
                    member_name_processed = (name_begin + name_middle + name_end);
                }

                item_info.font.setPixelSize(font_size);
                painter.setFont(item_info.font);
                QFontMetrics fontMetrics(painter.font());
                QString label_text = tr("会员：");
                item_info.text = label_text;
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                int label_width = fontMetrics.horizontalAdvance(label_text);
                auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
                painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

                QString point_text = member_name_processed;
                item_info.text = point_text;
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                int point_width = fontMetrics.horizontalAdvance(point_text);
                double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
                auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
                painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
                cur_v_pos += item_info.getLineHeight();

            }
            {
                printItemInfo item_info;
                QString cusBalance;
                if (json_tmp.contains("cusBalance"))
                {
                    cusBalance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                else
                {
                    cusBalance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                item_info.font.setPixelSize(font_size);
                painter.setFont(item_info.font);
                QFontMetrics fontMetrics(painter.font());

                QString label_text = tr("会员余额：");
                item_info.text = label_text;
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                int label_width = fontMetrics.horizontalAdvance(label_text);

                auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
                painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

                QString point_text = Utils::String::getClearNumStr(cusBalance.toDouble(), 2);
                item_info.text = point_text;
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                int point_width = fontMetrics.horizontalAdvance(point_text);
                double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
                auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
                painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);

                cur_v_pos += item_info.getLineHeight();
            }
            {
                printItemInfo item_info;
                QString member_point;
                if (json_tmp.contains("salePoints"))
                {
                    member_point = getDataFromJson(json_tmp, "salePoints").toString();
                }
                else
                {
                    member_point = getDataFromJson(json_tmp, "sale_points").toString();
                }
                item_info.font.setPixelSize(font_size);
                painter.setFont(item_info.font);
                QFontMetrics fontMetrics(painter.font());

                QString label_text = tr("本单积分：");
                item_info.text = label_text;
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                int label_width = fontMetrics.horizontalAdvance(label_text);

                auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
                painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

                QString point_text = Utils::String::getClearNumStr(member_point.toDouble(), 2);
                item_info.text = point_text;
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                int point_width = fontMetrics.horizontalAdvance(point_text);
                double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
                auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
                painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);

                cur_v_pos += item_info.getLineHeight();
            }

            {
                printItemInfo item_info;

                QString member_point;

                if (json_tmp.contains("cusPoints"))
                {
                    member_point = getDataFromJson(json_tmp, "cusPoints").toString();
                }
                else
                {
                    member_point = getDataFromJson(json_tmp, "cus_points").toString();
                }
                item_info.font.setPixelSize(font_size);
                painter.setFont(item_info.font);
                QFontMetrics fontMetrics(painter.font());
                QString label_text = tr("会员积分：");
                item_info.text = label_text;
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                int label_width = fontMetrics.horizontalAdvance(label_text);
                auto qrect_label = QRectF(left_margin, cur_v_pos, label_width, item_info.getLineHeight());
                painter.drawText(qrect_label, item_info.alignment_flag, item_info.text);

                QString point_text = Utils::String::getClearNumStr(member_point.toDouble(), 2);
                item_info.text = point_text;
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                int point_width = fontMetrics.horizontalAdvance(point_text);
                double value_x_pos = canvas_size.width() - right_margin - right_padding - point_width;
                auto qrect_value = QRectF(value_x_pos, cur_v_pos, point_width, item_info.getLineHeight());
                painter.drawText(qrect_value, item_info.alignment_flag, item_info.text);
                cur_v_pos += item_info.getLineHeight();
            }
            {
                printItemInfo item_info;
                item_info.text = "--------------------------------------------------------";
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
        }
    }

//    {
//        printItemInfo item_info;
//        item_info.text = tr("收银员：　　　") + ControlManager::getInstance()->getShopControl()->getPersonInfo().name;
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }

//    {
//        printItemInfo item_info;
//        item_info.text = tr("收款机：　　　") + QString("1");
//        item_info.font.setPixelSize(font_size);
//        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
//        painter.setFont(item_info.font);

//        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
//        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

//        cur_v_pos += qrect.height();
//    }
    {
        printItemInfo item_info;
        item_info.text ="--------------------------------------------------------";
        item_info.text ="***********备注***********";
        item_info.font.setPixelSize(font_size+2);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = ControlManager::getInstance()->getPrinterControl()->getTicketsRemarkInfo();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        // item_info.text = "订单时间:" + shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        // item_info.font.setWordSpacing(-50);
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 条码
    {
        QImage img = PrinterControl::Text2BarcodeImg(bar_code);
        img        = img.scaled(contain_w, 8 * dpi_mm);

        auto qrect = QRectF(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.drawPixmap(qrect.toRect(), QPixmap::fromImage(img));

        cur_v_pos += qrect.height();

        printItemInfo item_info;

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft;

        qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.setFont(item_info.font);
        painter.drawText(qrect, item_info.alignment_flag, bar_code);
    }

    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("联系电话:") + ControlManager::getInstance()->getShopControl()->getPersonInfo().phone;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("温馨提示:如需发票请联系前台");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, canvas_size.width(), item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }


    painter.end();
}
void PrinterWorker::printOrderNingyu(std::shared_ptr<ShopCartList> shop_cart_list, int print_num, bool is_reprint, std::string addition_info)
{
    auto printer_ctrl = ControlManager::getInstance()->getPrinterControl();

    auto ticket_printer_name = printer_ctrl->getTicketPrinterName();
    auto ticket_printer_num  = printer_ctrl->getTicketPrinterNum();

    if (!PrinterControl::isPrinterValid(printer_ctrl->getTicketPrinterName()))
        return;

    if (!is_reprint)
    {
        printer_ctrl->setFoodTicketIndex(printer_ctrl->getFoodTicketIndex() + 1);
    }

    if (!printer_ctrl->getIsPrintTicket() && print_num < 1)
        return;

    float dpi_mm = 8;

    QString bar_code  = QString("DH") + shop_cart_list->shop_cart_list_data_.order_unique_;
    int     font_size = 24;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name);
    printer.setResolution(PrinterControl::mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setPageMargins(QMarginsF());
    printer.setPageSize(QPageSize(QPageSize::Custom));

    if (print_num > 0)
    {
        printer.setCopyCount(print_num);
    }
    else
    {
        printer.setCopyCount(ticket_printer_num);
    }

    QSize canvas_size = QSize(54 * dpi_mm, 800 * dpi_mm);

    int cur_v_pos    = 0;
    int left_margin  = 0 * dpi_mm;
    int right_margin = 10 * dpi_mm;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    cur_v_pos += 10; // 上边距

    QPixmap pixmap(canvas_size);
    pixmap.fill(QColor(255, 255, 255));

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);

    painter.drawLine(QLineF(QPointF(58 * dpi_mm, 0), QPointF(58 * dpi_mm, 100 * dpi_mm)));


    auto config_tool     = ConfigTool::getInstance();
    QDateTime currentDateTime = QDateTime::currentDateTime();
    QString dateString = currentDateTime.toString("yyyy-MM-dd");

    QString printTime = config_tool->getSetting(ConfigToolEnum::ConfigEnum::NINGYU_TIME_PRINT).toString();
    QDate inputDate = QDate::fromString(printTime, "yyyy-MM-dd");
    if (!inputDate.isValid()) {
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_, "========================打印时间解析错误===========================");
    }
    QDate currentDate = QDate::currentDate();

    if (inputDate < currentDate) {
         printer_ctrl->resetFoodTicketIndex();
         printer_ctrl->setFoodTicketIndex(printer_ctrl->getFoodTicketIndex() + 1);
    }
    config_tool->setSetting(ConfigToolEnum::ConfigEnum::NINGYU_TIME_PRINT, dateString);
    {
        printItemInfo item_info;
        item_info.text = tr("排队序号: ") + QString::number(printer_ctrl->getFoodTicketIndex());
        item_info.font.setPixelSize(40);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "店铺: " + shop_name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }


    {
        printItemInfo item_info;
        item_info.text = tr("品名　　　　 　数量　　小计");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    // 商品详情
    {
        for (ShopCartItem &shop_cart_item : shop_cart_list->items())
        {
            GoodsInfo goods_info;
            goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);

            {
                printItemInfo item_info;
                item_info.text = goods_info.goods_name + "*" + QString::number(shop_cart_item.goods_num_, 'f', 0);
                item_info.font.setPixelSize(40);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop | Qt::TextWrapAnywhere;
                painter.setFont(item_info.font);

                float name_width  = item_info.getLineWidth();
                float name_height = item_info.getLineHeight();

                int multiplier = 1;
                if (name_width > contain_w)
                {
                    multiplier = ceil(name_width / contain_w);
                }
                name_height *= multiplier;

                auto qrect = QRectF(left_margin, cur_v_pos, contain_w, name_height);

                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;
                //item_info.text = QString::number(shop_cart_item.getSubtotal(), 'f', 2);
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getSubtotal()) + "元";
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, contain_w - 4 * dpi_mm, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
        }
    }

    {
        printItemInfo item_info;
        item_info.text = "--------------------------------------------------------";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }


    {
        printItemInfo item_info;

        if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
        {
            item_info.text =
                tr("总金额：　　　") + Utils::String::getClearNumStr(shop_cart_list->shop_cart_list_data_.cash_received_) + "元";
        }else{
            item_info.text =
                tr("总金额：　　　") + Utils::String::getClearNumStr(shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment)) + "元";
        }
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("交易件数：　　") + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsQuantity().toDouble());
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("支付方式：　　") + OrderInfo::getPayMethodStr(shop_cart_list->shop_cart_list_data_.payment);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    //只要有会员ID就打印会员信息
    if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
    {
        Json json_tmp = Json::parse(addition_info, nullptr, false);

        if (!json_tmp.is_discarded())
        {
            {
                printItemInfo item_info;

                QString member_name;

                if (json_tmp.contains("cusName"))
                {
                    member_name = getDataFromJson(json_tmp, "cusName").toString();
                }
                else
                {
                    member_name = getDataFromJson(json_tmp, "cus_name").toString();
                }
                {
                    printItemInfo item_info;

                    QString member_point;

                    if (json_tmp.contains("cusPoints"))
                    {
                        member_point = getDataFromJson(json_tmp, "cusPoints").toString();
                    }
                    else
                    {
                        member_point = getDataFromJson(json_tmp, "cus_points").toString();
                    }

                    item_info.text = tr("总积分：　　") + Utils::String::getClearNumStr(member_point.toDouble(), 2);
                    item_info.font.setPixelSize(font_size);
                    item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                    painter.setFont(item_info.font);

                    auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                    painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                    cur_v_pos += qrect.height();
                }
                if (!member_name.isEmpty())
                {

                    QString name_begin;
                    QString name_middle = member_name;
                    QString name_end;
                    name_end = QString(*member_name.rbegin());

                    if (member_name.size() > 2)
                    {
                        name_begin = QString(*member_name.begin());
                        name_middle.remove(member_name.size() - 1, 1);
                    }

                    name_middle.remove(0, 1);
                    name_middle.fill('*');

                    item_info.text = tr("会员姓名：　　　") + (name_begin + name_middle + name_end);
                }

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
            {
                printItemInfo item_info;

                QString cus_unique;

                if (json_tmp.contains("cusUnique"))
                {
                    cus_unique = getDataFromJson(json_tmp, "cusUnique").toString();
                }
                else
                {
                    cus_unique = getDataFromJson(json_tmp, "cus_unique").toString();
                }

                item_info.text = tr("储值卡号: ") + cus_unique;
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
            {
                printItemInfo item_info;

                QString use_balance;

                if (json_tmp.contains("useBalance"))
                {
                    use_balance = getDataFromJson(json_tmp, "useBalance").toString();
                }
                else
                {
                    use_balance = getDataFromJson(json_tmp, "use_balance").toString();
                }
                QString use_rebute;

                if (json_tmp.contains("useRebute"))
                {
                    use_rebute = getDataFromJson(json_tmp, "useRebute").toString();
                }
                else
                {
                    use_rebute = getDataFromJson(json_tmp, "use_rebute").toString();
                }
                item_info.text = tr("本金消费:") + Utils::String::getClearNumStr(use_balance.toDouble(), 2) + tr("赠额消费:") + Utils::String::getClearNumStr(use_rebute.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
            {
                printItemInfo item_info;

                QString cus_balance;

                if (json_tmp.contains("cusBalance"))
                {
                    cus_balance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                else
                {
                    cus_balance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                QString cus_rebate;

                if (json_tmp.contains("cus_rebate"))
                {
                    cus_rebate = getDataFromJson(json_tmp, "cus_rebate").toString();
                }
                else
                {
                    cus_rebate = getDataFromJson(json_tmp, "cus_rebate").toString();
                }
                item_info.text = tr("本金余额:") + Utils::String::getClearNumStr(cus_balance.toDouble(), 2)+tr("赠送余额:") + Utils::String::getClearNumStr(cus_rebate.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
            {
                printItemInfo item_info;

                QString member_blance;

                if (json_tmp.contains("cusBalance"))
                {
                    member_blance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                else
                {
                    member_blance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                QString cus_rebate;

                if (json_tmp.contains("cus_rebate"))
                {
                    cus_rebate = getDataFromJson(json_tmp, "cus_rebate").toString();
                }
                else
                {
                    cus_rebate = getDataFromJson(json_tmp, "cus_rebate").toString();
                }
                double total = member_blance.toDouble() + cus_rebate.toDouble();
                member_blance = QString::number(total, 'f', 2);
                item_info.text = tr("剩余总余额:") + member_blance;
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
        }
    }

    {
        printItemInfo item_info;
        item_info.text = "时间: " + shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("单号:") + shop_cart_list->shop_cart_list_data_.order_unique_;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("地址:") + ControlManager::getInstance()->getShopControl()->getShopAddress();
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        // 先计算文本需要的矩形大小
        QRectF textRect = painter.boundingRect(
            QRectF(left_margin, cur_v_pos, contain_w, 10000), // 高度设为足够大的值
            item_info.alignment_flag | Qt::TextWordWrap,
            item_info.text
        );

        // 使用计算出的实际高度创建矩形
        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, textRect.height());

        // 绘制文本
        painter.drawText(qrect, item_info.alignment_flag | Qt::TextWordWrap, item_info.text);

        // 更新垂直位置
        cur_v_pos += textRect.height();
    }
    {
        printItemInfo item_info;
        item_info.text = tr("电话:") + ControlManager::getInstance()->getShopControl()->getPersonInfo().phone;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    painter.end();
}
void PrinterWorker::printOrder4Food(std::shared_ptr<ShopCartList> shop_cart_list, int print_num, bool is_reprint, std::string addition_info)
{
    auto printer_ctrl = ControlManager::getInstance()->getPrinterControl();

    auto ticket_printer_name = printer_ctrl->getTicketPrinterName();
    auto ticket_printer_num  = printer_ctrl->getTicketPrinterNum();

    if (!PrinterControl::isPrinterValid(printer_ctrl->getTicketPrinterName()))
        return;

    if (!is_reprint)
    {
        printer_ctrl->setFoodTicketIndex(printer_ctrl->getFoodTicketIndex() + 1);
    }

    if (!printer_ctrl->getIsPrintTicket() && print_num < 1)
        return;

    float dpi_mm = 8;

    QString bar_code  = QString("DH") + shop_cart_list->shop_cart_list_data_.order_unique_;
    int     font_size = 24;

    QPrinter printer;
    printer.setPrinterName(ticket_printer_name);
    printer.setResolution(PrinterControl::mm2Dpi(dpi_mm));
    printer.setFullPage(true);
    printer.setColorMode(QPrinter::GrayScale);
    printer.setPageMargins(QMarginsF());
    printer.setPageSize(QPageSize(QPageSize::Custom));

    if (print_num > 0)
    {
        printer.setCopyCount(print_num);
    }
    else
    {
        printer.setCopyCount(ticket_printer_num);
    }

    QSize canvas_size = QSize(54 * dpi_mm, 800 * dpi_mm);

    int cur_v_pos    = 0;
    int left_margin  = 0 * dpi_mm;
    int right_margin = 10 * dpi_mm;
    int contain_w    = canvas_size.width() - left_margin - right_margin;

    cur_v_pos += 10; // 上边距

    QPixmap pixmap(canvas_size);
    pixmap.fill(QColor(255, 255, 255));

    auto goods_mgr = DataManager::getInstance()->getGoodsMgr();

    auto shop_name = ControlManager::getInstance()->getShopControl()->getShopName();

    QPainter painter(&printer);

    painter.drawLine(QLineF(QPointF(58 * dpi_mm, 0), QPointF(58 * dpi_mm, 100 * dpi_mm)));

    {
        printItemInfo item_info;
        item_info.text = tr("排队序号: ") + QString::number(printer_ctrl->getFoodTicketIndex());
        item_info.font.setPixelSize(40);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = shop_name;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignHCenter | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("品名　　　　 　数量　　小计");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 商品详情
    {
        for (ShopCartItem &shop_cart_item : shop_cart_list->items())
        {
            GoodsInfo goods_info;
            goods_mgr->getGoodsByBarcode(shop_cart_item.goods_barcode_, goods_info);

            {
                printItemInfo item_info;
                item_info.text = goods_info.goods_name + "*" + QString::number(shop_cart_item.goods_num_, 'f', 0);
                item_info.font.setPixelSize(40);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop | Qt::TextWrapAnywhere;
                painter.setFont(item_info.font);

                float name_width  = item_info.getLineWidth();
                float name_height = item_info.getLineHeight();

                int multiplier = 1;
                if (name_width > contain_w)
                {
                    multiplier = ceil(name_width / contain_w);
                }
                name_height *= multiplier;

                auto qrect = QRectF(left_margin, cur_v_pos, contain_w, name_height);

                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;
                //item_info.text = QString::number(shop_cart_item.getSubtotal(), 'f', 2);
                item_info.text = Utils::String::getClearNumStr(shop_cart_item.getSubtotal());
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignRight | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, contain_w - 4 * dpi_mm, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
        }
    }

    {
        printItemInfo item_info;
        item_info.text = "********************************************************";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }


    {
        printItemInfo item_info;
        item_info.text =
            tr("总金额：　　　") + Utils::String::getClearNumStr(shop_cart_list->getFinalTotalGoodsPrice(shop_cart_list->shop_cart_list_data_.payment)) + "元";
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("交易件数：　　") + Utils::String::getClearNumStr(shop_cart_list->getTotalGoodsQuantity().toDouble());
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("支付方式：　　") + OrderInfo::getPayMethodStr(shop_cart_list->shop_cart_list_data_.payment);
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    //只要有会员ID就打印会员信息
    if (!shop_cart_list->shop_cart_list_data_.member_unique.isEmpty())
    {
        Json json_tmp = Json::parse(addition_info, nullptr, false);

        if (!json_tmp.is_discarded())
        {
            {
                printItemInfo item_info;

                QString member_name;

                if (json_tmp.contains("cusName"))
                {
                    member_name = getDataFromJson(json_tmp, "cusName").toString();
                }
                else
                {
                    member_name = getDataFromJson(json_tmp, "cus_name").toString();
                }

                if (!member_name.isEmpty())
                {

                    QString name_begin;
                    QString name_middle = member_name;
                    QString name_end;
                    name_end = QString(*member_name.rbegin());

                    if (member_name.size() > 2)
                    {
                        name_begin = QString(*member_name.begin());
                        name_middle.remove(member_name.size() - 1, 1);
                    }

                    name_middle.remove(0, 1);
                    name_middle.fill('*');

                    item_info.text = tr("会员名：　　　") + (name_begin + name_middle + name_end);
                }

                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;

                QString member_blance;

                if (json_tmp.contains("cusBalance"))
                {
                    member_blance = getDataFromJson(json_tmp, "cusBalance").toString();
                }
                else
                {
                    member_blance = getDataFromJson(json_tmp, "cus_balance").toString();
                }

                item_info.text = tr("会员余额：　　") + Utils::String::getClearNumStr(member_blance.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;

                QString member_point;

                if (json_tmp.contains("salePoints"))
                {
                    member_point = getDataFromJson(json_tmp, "salePoints").toString();
                }
                else
                {
                    member_point = getDataFromJson(json_tmp, "sale_points").toString();
                }

                item_info.text = tr("本单积分：　　") + Utils::String::getClearNumStr(member_point.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }

            {
                printItemInfo item_info;

                QString member_point;

                if (json_tmp.contains("cusPoints"))
                {
                    member_point = getDataFromJson(json_tmp, "cusPoints").toString();
                }
                else
                {
                    member_point = getDataFromJson(json_tmp, "cus_points").toString();
                }

                item_info.text = tr("会员积分：　　") + Utils::String::getClearNumStr(member_point.toDouble(), 2);
                item_info.font.setPixelSize(font_size);
                item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
                painter.setFont(item_info.font);

                auto qrect = QRectF(left_margin, cur_v_pos, canvas_size.width() - right_margin, item_info.getLineHeight());
                painter.drawText(qrect, item_info.alignment_flag, item_info.text);

                cur_v_pos += qrect.height();
            }
        }
    }

    {
        printItemInfo item_info;
        item_info.text = shop_cart_list->shop_cart_list_data_.order_date_ + " " + shop_cart_list->shop_cart_list_data_.order_time_;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        auto qrect = QRectF(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    // 条码
    {
        QImage img = PrinterControl::Text2BarcodeImg(bar_code);
        img        = img.scaled(contain_w, 8 * dpi_mm);

        auto qrect = QRectF(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.drawPixmap(qrect.toRect(), QPixmap::fromImage(img));

        cur_v_pos += qrect.height();

        printItemInfo item_info;

        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft;

        qrect = QRect(left_margin + 1 * dpi_mm, cur_v_pos, contain_w - 8 * dpi_mm, 8 * dpi_mm);

        painter.setFont(item_info.font);
        painter.drawText(qrect, item_info.alignment_flag, bar_code);
    }

    {
        printItemInfo item_info;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("联系电话:") + ControlManager::getInstance()->getShopControl()->getPersonInfo().phone;
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    {
        printItemInfo item_info;
        item_info.text = tr("温馨提示:如需发票请联系前台");
        item_info.font.setPixelSize(font_size);
        item_info.alignment_flag = Qt::AlignLeft | Qt::AlignTop;
        painter.setFont(item_info.font);

        QRect qrect = QRect(left_margin, cur_v_pos, contain_w, item_info.getLineHeight());
        painter.drawText(qrect, item_info.alignment_flag, item_info.text);

        cur_v_pos += qrect.height();
    }

    painter.end();
}
