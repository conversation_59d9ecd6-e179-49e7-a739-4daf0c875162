﻿#include "NetStatusWorker.h"
#include <QDebug>

NetStatusWorker::NetStatusWorker(QObject *parent) : QThread{parent}
{
    flagRunning = true;
}

NetStatusWorker::~NetStatusWorker()
{

}

void NetStatusWorker::run()
{
    QString network_cmd = "ping www.baidu.com -n 2 -w 500";
    QString result;
    network_process = new QProcess(); //不要加this
    while (flagRunning)
    {
        int bResult = ping_.Ping("www.baidu.com"); //result即为延迟毫秒数ms，负数表示ping不通

        if (bResult >= 0 && bResult <= 2000)
        {
            qDebug() << bResult << "emit signalsNetwork_States(true); //在线";
            emit signalsNetwork_States(true); //在线
        }
        else
        {
            qDebug() << bResult << "emit signalsNetwork_States(false); //离线";
            emit signalsNetwork_States(false); //离线
        }

        // network_process->start(network_cmd);  //调用ping 指令
        // network_process->waitForFinished();   //等待指令执行完毕
        // result = network_process->readAll();  //获取指令执行结果
        // auto num = result.count('\n');
        // if (num > 1)
        // {
        //     emit signalsNetwork_States(true); //在线
        // }
        // else
        // {
        //     emit signalsNetwork_States(false); //离线
        // }
        sleep(1); //加sleep降低CPU占用率
    }
}

void NetStatusWorker::stop()
{
    flagRunning = false;
}
