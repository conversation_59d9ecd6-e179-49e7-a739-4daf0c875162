﻿#include "FaceRecognizer.h"

#include <utility>


#include "LogManager.h"
#include "Utils/Utils.h"

using namespace std;
using namespace cv;

FaceRecognizer::FaceRecognizer(QObject *parent) : QObject{parent}
{
}

int FaceRecognizer::imageProcess(cv::Mat camera, cv::CascadeClassifier &cascade_classifier)
{
    vector<Rect> faces;
    flip(camera, camera, 1);
    cv::Mat detected_img;

    auto face_num = detection2(camera, detected_img, cascade_classifier);

    return face_num;
}

int FaceRecognizer::detection2(cv::Mat img_in, cv::Mat &img_out, cv::CascadeClassifier &cascade_classifier)
{
    img_out = std::move(img_in);

    vector<Rect> faces;

    Mat    grayt = extractFace(img_out, &faces, cascade_classifier);
    Scalar he    = sum(grayt);

    if (he[0] == 0)
    {
        resize(img_out, img_out, <PERSON><PERSON>(), 0.5, 0.5, INTER_AREA);
        return faces.size();
    }

    resize(img_out, img_out, Size(), 0.5, 0.5, INTER_AREA);

    if (!faces.empty())
        rectangle(img_out, faces[0], Scalar(255, 255, 255), 2);

    return faces.size();
}

Mat FaceRecognizer::extractFace(cv::Mat input, std::vector<cv::Rect> *faces, cv::CascadeClassifier &cascade_classifier)
{
    Mat result = preProcessImage(input);

    Mat zero = Mat::zeros(result.rows, result.cols, CV_8UC1);

    cascade_classifier.detectMultiScale(result, *faces, 1.1, 2, 0 | CV_HAL_CMP_GE, Size(100, 100), Size(200, 200));

    if (faces->empty())
        return zero;

    Mat grayc = result.clone();
    grayc     = grayc((*faces)[0]); //

    return grayc;
}

Mat FaceRecognizer::preProcessImage(cv::Mat input)
{
    Mat result;

    resize(input, input, Size(), 0.5, 0.5, INTER_AREA);
    cvtColor(input, result, COLOR_BGR2GRAY);
    // Equalize the histogram
    equalizeHist(result, result);
    return result;
}
