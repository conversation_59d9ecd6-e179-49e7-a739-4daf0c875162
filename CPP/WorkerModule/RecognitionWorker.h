﻿#ifndef RECOGNITIONWORKER_H
#define RECOGNITIONWORKER_H

#include <QObject>
#include <opencv2/opencv.hpp>
#include <vector>
#ifdef _RECOGNITION_
#undef slots
#include <torch/script.h>
#include <torch/torch.h>
#define slots Q_SLOTS

class RecognitionWorker : public QObject
{
    Q_OBJECT
public:
    explicit RecognitionWorker(QObject *parent = nullptr);

    static void initGoodsList();;

    static cv::Mat pilResize(cv::Mat &img, int size);

    static cv::Mat pilCropCenter(const cv::Mat &img, int output_size);

    static cv::Mat setNorm(cv::Mat &img);

    static cv::Mat setMean(cv::Mat &image_resized_float);

    static int processImgMat(cv::Mat image, torch::Device &device, torch::jit::script::Module &model, std::vector<std::tuple<int, float>> &result_list);

    static std::vector<std::string> class_names_;

signals:
};
#endif // _RECOGNITION_

#endif // RECOGNITIONWORKER_H
