﻿#ifndef FACERECOGNIZER_H
#define FACERECOGNIZER_H

#include <QObject>
#include "opencv2/core.hpp"
#include "opencv2/face.hpp"
#include "opencv2/highgui.hpp"
#include "opencv2/imgproc.hpp"
#include "opencv2/ml.hpp"
#include "opencv2/objdetect/objdetect.hpp"
#include "opencv2/opencv.hpp"

class FaceRecognizer : public QObject
{
    Q_OBJECT
public:
    explicit FaceRecognizer(QObject *parent = nullptr);

    static int     imageProcess(cv::Mat camera, cv::CascadeClassifier &cascade_classifier);
    static int     detection2(cv::Mat img_in, cv::Mat &img_out, cv::CascadeClassifier &cascade_classifier);
    static cv::Mat extractFace(cv::Mat input, std::vector<cv::Rect> *faces, cv::CascadeClassifier &cascade_classifier);
    static cv::Mat preProcessImage(cv::Mat input);

signals:
};

#endif // FACERECOGNIZER_H
