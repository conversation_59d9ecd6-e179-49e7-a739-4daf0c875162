﻿#include "RecognitionWorker.h"
#include <ATen/Parallel.h>
#include <fstream>
#include <vector>
#include "json-qt.hpp"

#undef slots
#include <torch/script.h>
#include <torch/torch.h>
#define slots Q_SLOTS

using namespace cv;
using namespace std;
//using namespace torch;

constexpr int top_key_num = 5;

//vector<string> RecognitionWorker::class_names_ = {"apple", "banana", "grapes", "orange",     "pineapple", "tomato", "strawberry", "mangosteen", "cherry",
//                                                  "peach", "Kiwi",   "pitaya", "watermelon", "pears",     "lemon",  "cherries",   "carambola"};

vector<string> RecognitionWorker::class_names_;
//= {"苹果", "香蕉",   "葡萄",   "橘子", "菠萝", "西红柿", "草莓",   "山竹", "樱桃",
//                                                  "桃",   "猕猴桃", "火龙果", "西瓜", "梨",   "柠檬",   "车厘子", "杨桃"};


RecognitionWorker::RecognitionWorker(QObject *parent) : QObject{parent}
{
}

void RecognitionWorker::initGoodsList()
{
    std::fstream fstream;
    fstream.open("goods_list.json", std::fstream::in);

    if (!fstream.is_open())
    {
        return;
    }

    std::string file_str((std::istreambuf_iterator<char>(fstream)), std::istreambuf_iterator<char>());
    Json        json_doc = Json::parse(file_str, nullptr, false);

    if (json_doc.is_discarded())
    {
        return;
    }

    if (!json_doc.is_array())
        return;

    class_names_ = json_doc;
    return;
}

Mat RecognitionWorker::pilResize(Mat &img, int size)
{
    int img_width  = img.cols;
    int img_height = img.rows;
    if ((img_width <= img_height && img_width == size) || (img_height <= img_width && img_height == size))
    {
        return img;
    }

    Mat output;
    if (img_width < img_height)
    {
        int outWidth  = size;
        int outHeight = int(size * img_height / static_cast<float>(img_width));
        resize(img, output, Size(outWidth, outHeight));
    }
    else
    {
        int outHeight = size;
        int outWidth  = int(size * img_width / (float)img_height);
        resize(img, output, Size(outWidth, outHeight));
    }

    return output;
}

Mat RecognitionWorker::pilCropCenter(const Mat &img, int output_size)
{
    Rect imgRect;
    imgRect.x      = static_cast<int>(round((img.cols - output_size) / 2.));
    imgRect.y      = static_cast<int>(round((img.rows - output_size) / 2.));
    imgRect.width  = output_size;
    imgRect.height = output_size;

    return img(imgRect).clone();
}

Mat RecognitionWorker::setNorm(Mat &img)
{
    Mat img_rgb;
    cvtColor(img, img_rgb, COLOR_RGB2BGR);

    Mat img_resize = pilResize(img_rgb, 256);
    Mat img_crop   = pilCropCenter(img_resize, 224);

    Mat image_resized_float;
    img_crop.convertTo(image_resized_float, CV_32F, 1.0 / 255.0);

    return image_resized_float;
}

Mat RecognitionWorker::setMean(Mat &image_resized_float)
{
    vector<float> mean = {0.485, 0.456, 0.406};
    vector<float> std  = {0.229, 0.224, 0.225};

    vector<Mat> image_resized_split;
    split(image_resized_float, image_resized_split);
    for (int ch = 0; ch < image_resized_split.size(); ch++)
    {
        image_resized_split[ch] -= mean[ch];
        image_resized_split[ch] /= std[ch];
    }
    Mat image_resized_merge;
    merge(image_resized_split, image_resized_merge);

    return image_resized_merge;
}

int RecognitionWorker::processImgMat(cv::Mat image, torch::Device &device, torch::jit::script::Module &model, std::vector<std::tuple<int, float>> &result_list)
{
    if (image.empty()) //判断当前帧是否捕捉成功
        return -1;

    Mat image_resized_float = setNorm(image);
    Mat image_resized_merge = setMean(image_resized_float);

    auto img_tensor  = torch::from_blob(image_resized_merge.data, {224, 224, 3}, torch::kFloat32);
    auto img_tensor_ = torch::unsqueeze(img_tensor, 0);
    img_tensor_      = img_tensor_.permute({0, 3, 1, 2});

    vector<torch::jit::IValue> inputs;
    inputs.emplace_back(img_tensor_.to(device));

    torch::Tensor prob     = model.forward(inputs).toTensor();
    torch::Tensor soft_max = torch::nn::functional::softmax(prob, 1);

    auto top_k  = torch::topk(soft_max, top_key_num);
    auto values = std::get<0>(top_k).flatten();
    auto ids    = std::get<1>(top_k).flatten(); //flatten将任意维度张量转为一维张量

    for (int i = 0; i < top_key_num; ++i)
    {
        int  id  = ids[i].item<int>();
        auto val = values[i].item<float>();
        result_list.emplace_back(id, val);
    }

    return 0;
}
