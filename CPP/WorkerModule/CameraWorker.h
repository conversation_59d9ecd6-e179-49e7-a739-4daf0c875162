﻿#ifndef CAMERAWORKER_H
#define CAMERAWORKER_H

#include <QObject>
#include <memory>
#include <mutex>
#include <string>
#include "AdapterModule/ImageProvider.h"
#include "opencv2/core.hpp"
#include "opencv2/face.hpp"
#include "opencv2/highgui.hpp"
#include "opencv2/imgproc.hpp"
#include "opencv2/ml.hpp"
#include "opencv2/objdetect/objdetect.hpp"
#include "opencv2/opencv.hpp"

class CameraWorker : public QObject
{
    Q_OBJECT
public:
    explicit CameraWorker(QObject *parent = nullptr);
    ~CameraWorker(){};

    void process(int camera_index); // 处理

    void stop();

    int     camera_index = 0;
    bool    is_stop_     = true;
    QImage  MatToQImage(const cv::Mat &mat);
    cv::Mat QImageToMat(QImage image);

    bool init();

    int videoProcess();

    void imageProcess(cv::Mat camera);

    // 仅支持单脸
    // 未检测到脸时返回纯黑
    // 返回灰度脸图，缩小了1.1倍
    cv::Mat extractFace(cv::Mat input, std::vector<cv::Rect> *faces);

    // 输出灰度图像，直方图均衡，长宽缩小0.5倍
    cv::Mat preProcessImage(cv::Mat input);

    // 摄像头测试
    cv::Mat testvideo(cv::Mat img);

    // 检测人脸
    cv::Mat detection(cv::Mat img);

    int detection2(cv::Mat img_in, cv::Mat &img_out);

    int trainOnce(cv::Mat img);

    int updateLabels();

    cv::VideoCapture capture_;

    cv::Ptr<cv::face::FaceRecognizer> recognizer;
    cv::CascadeClassifier             faceCascade;
    char                              g_moduleFlag = 0;
    std::vector<std::string>          g_ids;

    int detected_num_ = 0;

signals:
    void sendImg(QImage img);         // 发送图片
    void  sigFaceDetected(int num);
    void sendOriginalImg(QImage img); // 发送图片
    void workStart();                 // 工作开始

    void sigCameraError();            // 相机错误
signals:
};

#endif // CAMERAWORKER_H
