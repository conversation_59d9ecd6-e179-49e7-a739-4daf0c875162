﻿#ifndef PRINTERWORKER_H
#define PRINTERWORKER_H

#include <QObject>
#include <memory>

class ShopCartList;

class PrinterWorker : public QObject
{
    Q_OBJECT
public:
    explicit PrinterWorker(QObject *parent = nullptr);

    void printOrder(std::shared_ptr<ShopCartList> shop_cart_list, int print_num = -1, std::string addition_info = "");
    void printOrderCombine(std::shared_ptr<ShopCartList> shop_cart_list, int print_num, std::string addition_info = "",double cash=0, double online=0,double totalPay=0);
    void printOrder4Food(std::shared_ptr<ShopCartList> shop_cart_list, int print_num = -1, bool is_reprint = false, std::string addition_info = "");
    void printOrderNingyu(std::shared_ptr<ShopCartList> shop_cart_list, int print_num = -1, bool is_reprint = false, std::string addition_info = "");

signals:
};

#endif // PRINTERWORKER_H
