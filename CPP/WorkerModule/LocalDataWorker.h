﻿#ifndef LOCALDATAWORKER_H
#define LOCALDATAWORKER_H

#include <QObject>
#include <QString>
#include <string>
#include "DataModule/DataStruct.h"

extern QString LOCAL_DATA;
extern QString GOODS_KIND_DATA;
extern QString GOODS_DATA;
extern QString GOODS_V_GOODS_KIND;

extern QString GAP;

extern QString DATE_PATTERN;


class LocalDataWorker : public QObject
{
    Q_OBJECT
public:
    explicit LocalDataWorker(QObject *parent = nullptr);

    static QString getTimeStr();

    void getVGoodsKindData();
    void saveVGoodsKindData(std::shared_ptr<std::vector<VirtualGoodsKindInfo>> v_goods_kind_data);

    void getGoodsKindData();
    void saveGoodsKindData(std::shared_ptr<std::vector<GoodsKindInfo>> goods_kind_data);

    void getGoodsData();
    void saveGoodsData(std::shared_ptr<std::vector<GoodsInfo>> goods_data);

    void clearUselessFile();

    void clearUselessVGoodsKindData();
    void clearUselessGoodsKindData();
    void clearUselessGoodsData();

    void clearUselessFileByPath(QString path);

signals:
    void sigSendVGoodsKindData(std::shared_ptr<VGoodsKindDataMap> data);
    void sigSendVGoodsKindDataError();

    void sigSendGoodsKindData(std::shared_ptr<goodsKindDataMap> data);
    void sigSendGoodsKindDataError();

    void sigSendGoodsData(std::shared_ptr<goodsDataMap> data);
    void sigSendGoodsDataError();
};

#endif // LOCALDATAWORKER_H
