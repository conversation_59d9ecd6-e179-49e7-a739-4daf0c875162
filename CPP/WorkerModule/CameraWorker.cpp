﻿#include "CameraWorker.h"
#include "LogManager.h"
#include "Utils/Utils.h"

using namespace cv;
using namespace std;

CameraWorker::CameraWorker(QObject *parent) : QObject{parent}
{
}

void CameraWorker::process(int camera_index)
{
    emit workStart(); // 工作开始
    capture_.release();
    capture_.open(camera_index);

    if (!capture_.isOpened() || !init())
    {
        LOG_OPT_ERROR("相机打开失败");
        is_stop_ = true;
        emit sigCameraError();
        return;
    }

    is_stop_ = false;

    while (!is_stop_)
    {
        Mat mat;
        capture_ >> mat;
        waitKey(30);
        sendOriginalImg(MatToQImage(mat));
        imageProcess(mat);
    }
    capture_.release();
}

void CameraWorker::stop()
{
    is_stop_ = true;
}

QImage CameraWorker::MatToQImage(const cv::Mat &mat)
{
    if (mat.type() == CV_8UC1)
    {
        QImage image(mat.cols, mat.rows, QImage::Format_Indexed8);
        // Set the color table (used to translate colour indexes to qRgb values)
        image.setColorCount(256);
        for (int i = 0; i < 256; i++)
        {
            image.setColor(i, qRgb(i, i, i));
        }
        // Copy input Mat
        uchar *pSrc = mat.data;
        for (int row = 0; row < mat.rows; row++)
        {
            uchar *pDest = image.scanLine(row);
            memcpy(pDest, pSrc, mat.cols);
            pSrc += mat.step;
        }
        return image;
    }
    // 8-bits unsigned, NO. OF CHANNELS = 3
    else if (mat.type() == CV_8UC3)
    {
        // Copy input Mat
        const uchar *pSrc = (const uchar *)mat.data;
        // Create QImage with same dimensions as input Mat
        QImage image(pSrc, mat.cols, mat.rows, mat.step, QImage::Format_RGB888);
        return image.rgbSwapped();
    }
    else if (mat.type() == CV_8UC4)
    {
        // Copy input Mat
        const uchar *pSrc = (const uchar *)mat.data;
        // Create QImage with same dimensions as input Mat
        QImage image(pSrc, mat.cols, mat.rows, mat.step, QImage::Format_ARGB32);
        return image.copy();
    }
    else
    {
        // MessageInfo("ERROR: Mat could not be converted to QImage.", 1);
        // emit sig_RunInfo("ERROR: Mat could not be converted to QImage.", 1);
        // if (!globalPara.IsInlineRun) Runstateinfo("ERROR: Mat could not be converted to QImage.", 1);
        return QImage();
    }
}

Mat CameraWorker::QImageToMat(QImage image)
{
    cv::Mat mat;
    switch (image.format())
    {
    case QImage::Format_ARGB32:
    case QImage::Format_RGB32:
    case QImage::Format_ARGB32_Premultiplied:
        mat = cv::Mat(image.height(), image.width(), CV_8UC4, (void *)image.constBits(), image.bytesPerLine());
        break;
    case QImage::Format_RGB888:
        mat = cv::Mat(image.height(), image.width(), CV_8UC3, (void *)image.constBits(), image.bytesPerLine());
        cv::cvtColor(mat, mat, COLOR_BGR2RGB);
        break;
    case QImage::Format_Indexed8:
        mat = cv::Mat(image.height(), image.width(), CV_8UC1, (void *)image.constBits(), image.bytesPerLine());
        break;
    }
    return mat;
}

bool CameraWorker::init()
{
    string faceCascadeName = Utils::getAppDirPath().toStdString() + "/haarcascade_frontalface_alt.xml";

    if (!faceCascade.load(faceCascadeName))
    {
        LOG_OPT_ERROR("Error loading cascade file. Exiting!");
        return false;
    }

    g_ids.push_back("Unknown");
    recognizer = cv::face::LBPHFaceRecognizer::create(1, 8, 8, 8, 200.);

    return true;
}

int CameraWorker::videoProcess()
{
    cout << "-->>function videoProcess" << endl;

    Mat          camera;
    vector<Rect> faces;
    char         ch    = 48;
    char         sh    = ch;
    int          count = 0;

    VideoCapture cap(0);

    if (!cap.isOpened())
    {
        cout << "No video!" << endl;
        return -1;
    }

    cout << "请在图片中选择摄像头模式：" << endl;
    cout << "	0 显示视频" << endl;
    cout << "	1 训练人脸" << endl;
    cout << "	2 持续测试人脸" << endl;

    while (true)
    {
        cap >> camera;
        flip(camera, camera, 1);

        if (ch == 48)
        { // 0	普通加载摄像头
            camera = detection(camera);
            // resize(camera, camera, Size(), 0.5, 0.5, INTER_AREA);
            imshow("Camera", camera);
            sh = ch;
            ch = waitKey(1);
        }
        else if (ch == 48 + 1)
        { // 1	训练人脸
            trainOnce(camera);
            resize(camera, camera, Size(), 0.5, 0.5, INTER_AREA);
            imshow("Camera", camera);
            ch = 48 + 2;
        }
        else if (ch == 48 + 2)
        { // 2		持续测试人脸
            camera = testvideo(camera);

            Scalar he = sum(camera);
            if (he[0] == 0) // 人脸未训练
                ch = 48;
            else
            {
                imshow("Camera", camera);
                sh = ch;
                ch = waitKey(1);
            }
        }
        else if (ch == 27) // esc
            break;
        else
            ch = sh;
    }
    cap.release();

    return 0;
}

void CameraWorker::imageProcess(cv::Mat camera)
{
    vector<Rect> faces;
    flip(camera, camera, 1);
    // camera = detection(camera);

    cv::Mat detected_img;

    auto face_num = detection2(camera, detected_img);


    if (face_num > 0)
    {
        ++detected_num_;
        if (detected_num_ > 10)
        {
            emit sigFaceDetected(face_num);
            detected_num_ = 0;
        }
    }
    else
    {
        detected_num_ = 0;
    }

    emit sendImg(MatToQImage(detected_img));
}

Mat CameraWorker::extractFace(cv::Mat input, std::vector<cv::Rect> *faces)
{
    // cout<<"-->>function extractFace"<<endl;
    Mat result = preProcessImage(input);

    Mat zero = Mat::zeros(result.rows, result.cols, CV_8UC1);

    faceCascade.detectMultiScale(result, *faces, 1.1, 2, 0 | CV_HAL_CMP_GE, Size(100, 100), Size(200, 200));

    if ((*faces).size() == 0)
        return zero;

    Mat grayc = result.clone();
    grayc     = grayc((*faces)[0]); //

    return grayc;
}

Mat CameraWorker::preProcessImage(cv::Mat input)
{
    // cout<<"-->>function preprocessImage"<<endl;
    Mat result;

    resize(input, input, Size(), 0.5, 0.5, INTER_AREA);
    cvtColor(input, result, COLOR_BGR2GRAY);
    // Equalize the histogram
    equalizeHist(result, result);
    return result;
}

Mat CameraWorker::testvideo(cv::Mat img)
{
    // cout<<"-->>function testvideo"<<endl;
    int    predictedLabel = -1;
    double confidence     = 0.0;

    vector<Rect> faces;
    Mat          zero = Mat::zeros(img.rows, img.cols, CV_8UC1);

    if (g_moduleFlag == 0)
    {
        cout << "未训练，无法测试" << endl;
        return zero;
    }

    Mat grayt = extractFace(img, &faces);

    Scalar he = sum(grayt);
    if (he[0] == 0)
    {
        // cout<<"图片中无人脸"<<endl;
        resize(img, img, Size(), 0.5, 0.5, INTER_AREA);
        return img;
    }

    resize(img, img, Size(), 0.5, 0.5, INTER_AREA);
    rectangle(img, faces[0], Scalar(255, 255, 255), 2);

    // predict the label of this image
    recognizer->predict(grayt,          // face image
                        predictedLabel, // predicted label of this image
                        confidence);    // confidence of the prediction

    string name;

    if (confidence < 80)
    {
        name = to_string(predictedLabel);
    }
    else
    {
        name       = g_ids[0];
        confidence = 0;
    }

    stringstream ss;
    ss << (int)confidence;
    putText(img, ss.str().c_str(), Point2d(faces[0].x, faces[0].y + 30), FONT_HERSHEY_SIMPLEX, 1, Scalar(0, 0, 255), 2, LINE_AA);

    putText(img, name, Point2d(faces[0].x, faces[0].y + faces[0].height), FONT_HERSHEY_SIMPLEX, 1, Scalar(0, 0, 0), 2, LINE_AA);

    return img;
}

Mat CameraWorker::detection(cv::Mat img)
{
    vector<Rect> faces;

    Mat grayt = extractFace(img, &faces);

    Scalar he = sum(grayt);
    if (he[0] == 0)
    {
        resize(img, img, Size(), 0.5, 0.5, INTER_AREA);
        return img;
    }

    resize(img, img, Size(), 0.5, 0.5, INTER_AREA);

    if (faces.size())
        rectangle(img, faces[0], Scalar(255, 255, 255), 2);

    return img;
}

int CameraWorker::detection2(cv::Mat img_in, cv::Mat &img_out)
{
    img_out = img_in;

    vector<Rect> faces;

    Mat    grayt = extractFace(img_out, &faces);
    Scalar he    = sum(grayt);

    if (he[0] == 0)
    {
        resize(img_out, img_out, Size(), 0.5, 0.5, INTER_AREA);
        return faces.size();
    }

    resize(img_out, img_out, Size(), 0.5, 0.5, INTER_AREA);

    if (faces.size())
        rectangle(img_out, faces[0], Scalar(255, 255, 255), 2);

    return faces.size();
}

int CameraWorker::trainOnce(cv::Mat img)
{
    cout << "-->>function trainOnce" << endl;

    std::vector<cv::Mat> referenceImages;
    std::vector<int>     labels;
    // vectors of reference image and their labels

    vector<Rect> faces;
    Mat          frame;
    int          inputlabel;

    frame     = extractFace(img, &faces);
    Scalar he = sum(frame);
    if (he[0] == 0)
    {
        cout << " No face" << endl;
        return -1;
    }


    inputlabel = updateLabels();
    referenceImages.push_back(frame);
    labels.push_back(inputlabel);

    if (g_moduleFlag == 0) // 是否有模型
    {
        recognizer->train(referenceImages, labels);
        cout << "g_moduleFlag == 0!" << endl;
        g_moduleFlag = 1;
    }
    else
        recognizer->update(referenceImages, labels);

    cout << "trained!" << endl;

    recognizer->write("../trainer.yml");

    return 0;
}

int CameraWorker::updateLabels()
{
    cout << "-->>function updateLabels" << endl;

    int inputlabel;

    cout << "请输入标签号：" << endl;
    cin >> inputlabel;
    cin.ignore();
    // g_ids.push_back(to_string(inputlabel));

    cout << "-->>function updateLabels end" << endl;
    return inputlabel;
}
