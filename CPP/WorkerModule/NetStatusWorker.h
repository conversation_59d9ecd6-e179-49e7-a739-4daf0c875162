﻿#ifndef NETSTATUSWORKER_H
#define NETSTATUSWORKER_H

#include "Ping.h"

#include <QObject>
#include <QThread>
#include <QProcess>

class NetStatusWorker : public QThread
{
    Q_OBJECT
public:
    explicit NetStatusWorker(QObject *parent = nullptr);
    ~NetStatusWorker();

    virtual void run();
    void         stop();
signals:
    void signalsNetwork_States(bool state);

private:
    bool      flagRunning = true; //线程运行标志
    QProcess *network_process;
    CPing     ping_;
};

#endif // NETSTATUSWORKER_H
