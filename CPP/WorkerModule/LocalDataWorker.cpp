﻿#include "LocalDataWorker.h"
#include <QDir>
#include <QFileInfoList>
#include <fstream>
#include <iostream>
#include <sstream>
#include "DataModule/DataManager.h"
#include "LogManager.h"
#include "Utils/Utils.h"

using namespace std;

QString LOCAL_DATA = "LocalData";

QString GOODS_V_GOODS_KIND = "goods_v_goods_kind";
QString GOODS_KIND_DATA    = "goods_kind_data";
QString GOODS_DATA         = "goods_data";
QString ORDER_DATA         = "order_data";

QString GAP = "__";

QString DATE_PATTERN = "yyyy.MM.dd-hh.mm.ss";

LocalDataWorker::LocalDataWorker(QObject *parent) : QObject{parent}
{
    qRegisterMetaType<std::shared_ptr<std::vector<GoodsInfo>>>("std::shared_ptr<std::vector<GoodsInfo>>");
    qRegisterMetaType<std::shared_ptr<std::vector<VirtualGoodsKindInfo>>>("std::shared_ptr<std::vector<VirtualGoodsKindInfo>>");
    qRegisterMetaType<std::shared_ptr<std::vector<GoodsKindInfo>>>("std::shared_ptr<std::vector<GoodsKindInfo>>");

    qRegisterMetaType<std::shared_ptr<VGoodsKindDataMap>>("std::shared_ptr<VGoodsKindDataMap>");
    qRegisterMetaType<std::shared_ptr<goodsKindDataMap>>("std::shared_ptr<goodsKindDataMap>");
    qRegisterMetaType<std::shared_ptr<goodsDataMap>>("std::shared_ptr<goodsDataMap>");
}

QString LocalDataWorker::getTimeStr()
{
    auto result = QDateTime::currentDateTime().toString(DATE_PATTERN);
    return result;
}

void LocalDataWorker::getVGoodsKindData()
{
    clearUselessVGoodsKindData();

    const auto dir_path = Utils::getAppDirPath() + "/" + LOCAL_DATA + "/" + GOODS_V_GOODS_KIND;

    const QDir source_dir(dir_path);
    if (!source_dir.exists())
    {
        emit sigSendVGoodsKindDataError();
        return;
    }

    QFileInfoList file_info_list = source_dir.entryInfoList(QDir::Files | QDir::Hidden | QDir::NoSymLinks);

    std::vector<QDateTime> timer_list;

    for (const auto &file_info_list_item : file_info_list)
    {
        auto      file_name = file_info_list_item.fileName();
        QDateTime file_time = QDateTime ::fromString(file_name, DATE_PATTERN);
        timer_list.push_back(file_time);
    }

    //将近的时间排序到前面
    std::sort(timer_list.begin(), timer_list.end(),
              [](QDateTime time_a, QDateTime time_b)
              {
                  return time_a > time_b;
              });

    std::vector<std::string> valid_file_name_vec;

    for (const auto &timer_list_item : timer_list)
    {
        if (!timer_list_item.isValid())
            continue;

        auto time_str = timer_list_item.toString(DATE_PATTERN).toStdString();

        if (valid_file_name_vec.size() == 3)
            break;

        valid_file_name_vec.push_back(time_str);
    }

    bool is_data_valid = false;

    for (const auto &valid_file_name_vec_item : valid_file_name_vec)
    {
        auto full_file_path = dir_path.toStdString() + "/" + valid_file_name_vec_item;

        fstream fstream;
        fstream.open(full_file_path, ios::in);

        if (!fstream.is_open())
        {
            LOG_DATA_WARN(full_file_path + "打开失败");
            continue;
        }

        std::string full_str((std::istreambuf_iterator<char>(fstream)), istreambuf_iterator<char>());

        Json json_doc = Json::parse(full_str, nullptr, false);

        if (json_doc.is_discarded() || !json_doc.is_array())
        {
            LOG_DATA_WARN(valid_file_name_vec_item + "格式有误");
            continue;
        }
        SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_net_,"读取本地虚拟分类：{}", json_doc.dump());
        std::shared_ptr<VGoodsKindDataMap> virtual_goods_kind_info_map;
        virtual_goods_kind_info_map.reset(new VGoodsKindDataMap);

        for (const auto &json_doc_item : json_doc)
        {
            auto v_goods_info = json_doc_item.get<VirtualGoodsKindInfo>();
            LOG_EVT_INFO("111111v_goods_info.goods_barode_vec.:{}",v_goods_info.goods_barode_vec_.size());
            virtual_goods_kind_info_map->insert_or_assign(v_goods_info.goods_kind_unique, v_goods_info);
        }
        is_data_valid = true;
        emit sigSendVGoodsKindData(virtual_goods_kind_info_map);
        LOG_EVT_INFO("发送本地虚拟分类数据信号");
        break;
    }

    if (!is_data_valid)
    {
        emit sigSendVGoodsKindDataError();
    }
}

void LocalDataWorker::saveVGoodsKindData(std::shared_ptr<std::vector<VirtualGoodsKindInfo>> v_goods_kind_data)
{
    const auto dir_path = Utils::getAppDirPath() + "/" + LOCAL_DATA + "/" + GOODS_V_GOODS_KIND;
    Utils::mkMutiDir(dir_path);

    const auto file_name = dir_path + "/" + getTimeStr();

    fstream fstream1;
    fstream1.open(file_name.toStdString(), fstream::out | fstream::trunc);

    if (!fstream1.is_open())
    {
        LOG_DATA_ERROR("虚拟分类文件打开失败");
        return;
    }

    Json json_result;
    json_result = *v_goods_kind_data;

    fstream1 << json_result.dump();

    fstream1.close();

    clearUselessVGoodsKindData();
}

void LocalDataWorker::getGoodsKindData()
{
    clearUselessGoodsKindData();

    const auto dir_path = Utils::getAppDirPath() + "/" + LOCAL_DATA + "/" + GOODS_KIND_DATA;

    const QDir source_dir(dir_path);
    if (!source_dir.exists())
    {
        emit sigSendGoodsKindDataError();
        return;
    }

    QFileInfoList file_info_list = source_dir.entryInfoList(QDir::Files | QDir::Hidden | QDir::NoSymLinks);

    std::vector<QDateTime> timer_list;

    for (const auto &file_info_list_item : file_info_list)
    {
        auto      file_name = file_info_list_item.fileName();
        QDateTime file_time = QDateTime ::fromString(file_name, DATE_PATTERN);
        timer_list.push_back(file_time);
    }

    //将近的时间排序到前面
    std::sort(timer_list.begin(), timer_list.end(),
              [](QDateTime time_a, QDateTime time_b)
              {
                  return time_a > time_b;
              });

    std::vector<std::string> valid_file_name_vec;

    for (const auto &timer_list_item : timer_list)
    {
        if (!timer_list_item.isValid())
            continue;

        auto time_str = timer_list_item.toString(DATE_PATTERN).toStdString();

        if (valid_file_name_vec.size() == 3)
            break;

        valid_file_name_vec.push_back(time_str);
    }

    bool is_data_valid = false;

    for (const auto &valid_file_name_vec_item : valid_file_name_vec)
    {
        auto full_file_path = dir_path.toStdString() + "/" + valid_file_name_vec_item;

        fstream fstream;
        fstream.open(full_file_path, ios::in);

        if (!fstream.is_open())
        {
            LOG_DATA_WARN(full_file_path + "打开失败");
            continue;
        }

        std::string full_str((std::istreambuf_iterator<char>(fstream)), istreambuf_iterator<char>());

        Json json_doc = Json::parse(full_str, nullptr, false);

        if (json_doc.is_discarded() || !json_doc.is_array())
        {
            LOG_DATA_WARN(valid_file_name_vec_item + "格式有误");
            continue;
        }

        std::shared_ptr<goodsKindDataMap> goods_kind_info_map;
        goods_kind_info_map.reset(new goodsKindDataMap);

        for (const auto &json_doc_item : json_doc)
        {
            auto v_goods_info = json_doc_item.get<GoodsKindInfo>();
            goods_kind_info_map->insert_or_assign(v_goods_info.goods_kind_unique, v_goods_info);
        }
        is_data_valid = true;
        emit sigSendGoodsKindData(goods_kind_info_map);
        break;
    }

    if (!is_data_valid)
    {
        emit sigSendGoodsKindDataError();
    }
}

void LocalDataWorker::saveGoodsKindData(std::shared_ptr<std::vector<GoodsKindInfo>> goods_kind_data)
{
    const auto dir_path = Utils::getAppDirPath() + "/" + LOCAL_DATA + "/" + GOODS_KIND_DATA;
    Utils::mkMutiDir(dir_path);

    const auto file_name = dir_path + "/" + getTimeStr();

    fstream fstream1;
    fstream1.open(file_name.toStdString(), fstream::out | fstream::trunc);

    if (!fstream1.is_open())
    {
        LOG_DATA_ERROR("商品文件打开失败");
        return;
    }

    Json json_result;
    json_result = *goods_kind_data;

    fstream1 << json_result.dump();

    fstream1.close();

    clearUselessGoodsKindData();
}
    void LocalDataWorker::getGoodsData()
    {
        LOG_EVT_INFO("getGoodsData==========");
        clearUselessGoodsData();

        const auto dir_path = Utils::getAppDirPath() + "/" + LOCAL_DATA + "/" + GOODS_DATA;

        const QDir source_dir(dir_path);
        if (!source_dir.exists())
        {
            emit sigSendGoodsDataError();
            return;
        }

        QFileInfoList file_info_list = source_dir.entryInfoList(QDir::Files | QDir::Hidden | QDir::NoSymLinks);

        std::vector<QDateTime> timer_list;

        for (const auto &file_info_list_item : file_info_list)
        {
            auto      file_name = file_info_list_item.fileName();
            QDateTime file_time = QDateTime ::fromString(file_name, DATE_PATTERN);
            timer_list.push_back(file_time);
        }

        //将近的时间排序到前面
        std::sort(timer_list.begin(), timer_list.end(),
                  [](QDateTime time_a, QDateTime time_b)
                  {
                      return time_a > time_b;
                  });

        std::vector<std::string> valid_file_name_vec;

        for (const auto &timer_list_item : timer_list)
        {
            if (!timer_list_item.isValid())
                continue;

            auto time_str = timer_list_item.toString(DATE_PATTERN).toStdString();

            if (valid_file_name_vec.size() == 3)
                break;

            valid_file_name_vec.push_back(time_str);
        }

        bool is_data_valid = false;

        for (const auto &valid_file_name_vec_item : valid_file_name_vec)
        {
            auto full_file_path = dir_path.toStdString() + "/" + valid_file_name_vec_item;

            fstream fstream;
            fstream.open(full_file_path, ios::in);

            if (!fstream.is_open())
            {
                LOG_DATA_WARN(full_file_path + "打开失败");
                continue;
            }

            std::string full_str((std::istreambuf_iterator<char>(fstream)), istreambuf_iterator<char>());

            Json json_doc = Json::parse(full_str, nullptr, false);

            if (json_doc.is_discarded() || !json_doc.is_array())
            {
                LOG_DATA_WARN(valid_file_name_vec_item + "格式有误");
                continue;
            }

            std::shared_ptr<goodsDataMap> goods_data_map;
            goods_data_map.reset(new goodsDataMap);

            for (const auto &json_doc_item : json_doc)
            {
                auto v_goods_info = json_doc_item.get<GoodsInfo>();
                goods_data_map->insert_or_assign(v_goods_info.goods_barcode, v_goods_info);
            }
            is_data_valid = true;
            emit sigSendGoodsData(goods_data_map);
            break;
        }

        if (!is_data_valid)
        {
            emit sigSendGoodsDataError();
        }
    }

void LocalDataWorker::saveGoodsData(std::shared_ptr<std::vector<GoodsInfo>> goods_data)
{
    const auto dir_path = Utils::getAppDirPath() + "/" + LOCAL_DATA + "/" + GOODS_DATA;
    Utils::mkMutiDir(dir_path);

    const auto file_name = dir_path + "/" + getTimeStr();

    fstream fstream1;
    fstream1.open(file_name.toStdString(), fstream::out | fstream::trunc);

    if (!fstream1.is_open())
    {
        LOG_DATA_ERROR("商品文件打开失败");
        return;
    }

    Json json_result;
    json_result = *goods_data;

    fstream1 << json_result.dump();

    fstream1.close();

    clearUselessGoodsData();
}

void LocalDataWorker::clearUselessFile()
{
    clearUselessVGoodsKindData();
    clearUselessGoodsKindData();
    clearUselessGoodsData();
}

void LocalDataWorker::clearUselessVGoodsKindData()
{
    const auto dir_path = Utils::getAppDirPath() + "/" + LOCAL_DATA + "/" + GOODS_V_GOODS_KIND;
    clearUselessFileByPath(dir_path);
}

void LocalDataWorker::clearUselessGoodsKindData()
{
    const auto dir_path = Utils::getAppDirPath() + "/" + LOCAL_DATA + "/" + GOODS_KIND_DATA;
    clearUselessFileByPath(dir_path);
}

void LocalDataWorker::clearUselessGoodsData()
{
    const auto dir_path = Utils::getAppDirPath() + "/" + LOCAL_DATA + "/" + GOODS_DATA;
    clearUselessFileByPath(dir_path);
}

void LocalDataWorker::clearUselessFileByPath(QString path)
{
    const QDir source_dir(path);
    if (!source_dir.exists())
    {
        emit sigSendVGoodsKindDataError();
        return;
    }
    QFileInfoList file_info_list = source_dir.entryInfoList(QDir::Files | QDir::Hidden | QDir::NoSymLinks);

    std::vector<QDateTime> timer_list;

    for (const auto &file_info_list_item : file_info_list)
    {
        auto      file_name = file_info_list_item.fileName();
        QDateTime file_time = QDateTime ::fromString(file_name, DATE_PATTERN);
        timer_list.push_back(file_time);
    }
    //将近的时间排序到前面
    std::sort(timer_list.begin(), timer_list.end(),
              [](QDateTime time_a, QDateTime time_b)
              {
                  return time_a > time_b;
              });

    bool is_delete_all = false;
if (!timer_list.empty() && *(timer_list.begin()) < QDateTime::currentDateTime().addDays(-3)) {
    is_delete_all = true;
}
    if (is_delete_all)
    {
        for (const auto &file_info_list_item : file_info_list)
        {
            auto file_path = file_info_list_item.absoluteFilePath();
            QFile::setPermissions(file_path, QFile::ReadOther | QFile::WriteOther);
            auto ret = QFile::remove(file_path);

            if (ret)
            {
                LOG_DATA_INFO("deleteFiles delete_files_ remove: {} 删除成功", file_path.toStdString());
            }
            else
            {
                LOG_DATA_ERROR("deleteFiles delete_files_ remove: {} 删除失败", file_path.toStdString());
            }
        }
    }
    else
    {
        if (timer_list.size() < 4)
        {
            LOG_DATA_INFO("文件数量少于4");
            return;
        }

        while (timer_list.size() > 3)
        {
            auto time_str = timer_list.rbegin()->toString(DATE_PATTERN);

            auto file_path = path + "/" + time_str;

            QFile::setPermissions(file_path, QFile::ReadOther | QFile::WriteOther);
            auto ret = QFile::remove(file_path);

            timer_list.pop_back();

            if (ret)
            {
                LOG_DATA_INFO("deleteFiles delete_files_ remove: {} 删除成功", file_path.toStdString());
            }
            else
            {
                LOG_DATA_ERROR("deleteFiles delete_files_ remove: {} 删除失败", file_path.toStdString());
            }
        }
    }
}
