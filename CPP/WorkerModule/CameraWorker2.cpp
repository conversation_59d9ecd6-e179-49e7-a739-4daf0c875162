﻿#include "CameraWorker2.h"
#include <QCameraImageCapture>
#include <QCameraInfo>
#include <QImage>
#include <QThread>
#include "LogModule/LogManager.h"
#include "json-qt.hpp"
#include "qobjectdefs.h"
#include "qtimer.h"
#include "LogManager.h"
#include <iostream>
#include <chrono>

CameraWorker2::CameraWorker2(QObject *parent) : QObject{parent}
{
}

const std::string getCurrentSystemTime1()
{

  time_t time_seconds = time(0);
  struct tm now_time;
  localtime_s(&now_time, &time_seconds);
  std::string timestamp = std::to_string(now_time.tm_year + 1900) + "-" +
          std::to_string(now_time.tm_mon + 1) + "-" +
          std::to_string(now_time.tm_mday) + "-" +
          std::to_string(now_time.tm_hour) + ":" +
          std::to_string(now_time.tm_min) + ":" +
          std::to_string(now_time.tm_sec);

  return timestamp;
}

//void CameraWorker2::setIsNeedFaceDetected(bool isNeedFaceDetected)
//{
//    isNeed_face_detected_ = isNeedFaceDetected;
//}
bool CameraWorker2::getIsNeedFaceDetected()
{
    return isNeed_face_detected_;
}
void CameraWorker2::setIsNeedFaceDetected(bool is_need)
{
    isNeed_face_detected_ = is_need;
    //emit sigIsNeedFaceDetected();
}
void CameraWorker2::process(QString camera_name, int interval)
{
    LOG_EVT_INFO("=== 打开相机时间:{}",getCurrentSystemTime1().c_str());
    LOG_EVT_INFO(">>>>开始打开相机<<<<<camera_name：{}",camera_name.toStdString());
    if (camera_name == "" || camera_name == "无")
    {
        LOG_EVT_INFO("camera_name为空！没有配置人脸摄像头");
        stop();
    }

    stopInternal();

    if (timer_capture_ == nullptr && interval > 0)
    {
        timer_capture_.reset(new QTimer);
        timer_capture_->setInterval(interval);
        connect(timer_capture_.get(), &QTimer::timeout, this, &CameraWorker2::capture, Qt::DirectConnection);
    }

    auto list_cameras = QCameraInfo::availableCameras();

    QCameraInfo *cur_camera_info_p = nullptr;
    for (auto &camera : list_cameras)
    {
        LOG_EVT_INFO(">> camera.description()：{}",camera.description().toStdString());
        if (camera.description() == camera_name)
        {
            cur_camera_info_p = &camera;
        }
    }
    if (cur_camera_info_p == nullptr)
    {
        LOG_EVT_INFO("camera err");
        emit sigCameraError();
        return;
    }

    camera_.reset(new QCamera(*cur_camera_info_p));

    connect(camera_.get(), &QCamera::errorOccurred, this,
            [=](QCamera::Error error)
            {
                LOG_OPT_ERROR("QCamera::Error {}", (int)error);
                emit sigCameraError();
            });

    camera_->setCaptureMode(QCamera::CaptureStillImage);
    // camera_->setCaptureMode(QCamera::CaptureVideo);

    image_capture_.reset(new QCameraImageCapture(camera_.get()));
    image_capture_->setCaptureDestination(QCameraImageCapture::CaptureToBuffer);
    LOG_EVT_INFO("准备开启获取照片:{}",getCurrentSystemTime1().c_str());
    connect(image_capture_.get(), &QCameraImageCapture::imageCaptured, this,
            [=](int id, const QImage &preview)
            {
                if(isNeed_face_detected_){
                    LOG_EVT_INFO("发送QImage照片:{}",getCurrentSystemTime1().c_str());
                    LOG_EVT_INFO("发送QImage 数据");
                    emit sendImg(preview);
                }
            });

    connect(camera_.get(), &QCamera::statusChanged, this,
            [&](QCamera::Status status)
            {
                if (status == QCamera::Status::ActiveStatus)
                {
                    if (timer_capture_ != nullptr)
                        timer_capture_->start();

                    emit sigWorkStart(); //工作开始
                    emit sigCameraOpened();
                }
                else
                {
                    emit sigCameraClosed();
                }
                //else if (status == QCamera::Status::ActiveStatus)
            });

    camera_->start();


    //获取摄像头支持的分辨率、帧率等参数
    QList<QSize> view_sets = camera_->supportedViewfinderResolutions();

    QSize max_view(0, 0);

    for (auto &view_set : view_sets)
    {
        qDebug() << "size_item" << view_set;
        if (max_view.width() < view_set.width() && max_view.height() < view_set.height())
        {
            max_view = view_set;
        }
    }

    qDebug() << "max_size" << max_view;

    //设置默认最大像素
    QCameraViewfinderSettings reset;
    reset.setResolution(max_view);
    camera_->setViewfinderSettings(reset);
}

void CameraWorker2::stopInternal()
{
    if (timer_capture_ != nullptr)
    {
        timer_capture_->stop();
    }

    if (camera_ != nullptr)
    {
        camera_->stop();
        camera_.release();
        camera_.reset();
    }

    if (image_capture_ != nullptr)
    {
        image_capture_.reset();
    }
}

void CameraWorker2::stop()
{
    stopInternal();
    emit sigWorkStop();
}

void CameraWorker2::capture()
{
    if (image_capture_ == nullptr)
        return;
    image_capture_->capture();
}

void CameraWorker2::captureNow()
{
    if (timer_capture_ != nullptr)
        timer_capture_->start();
    capture();
}
