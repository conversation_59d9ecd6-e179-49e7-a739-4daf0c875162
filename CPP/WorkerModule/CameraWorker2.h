﻿#ifndef CAMERAWORKER2_H
#define CAMERAWORKER2_H

#include <QCamera>
#include <QCameraImageCapture>
#include <QObject>
#include <QTimer>
#include <memory>

class CameraWorker2 : public QObject
{
    Q_OBJECT

    Q_PROPERTY(bool isNeed_face_detected_ READ getIsNeedFaceDetected WRITE setIsNeedFaceDetected NOTIFY sigIsNeedFaceDetected FINAL)
public:
    explicit CameraWorker2(QObject *parent = nullptr);

    void process(QString camera_name, int interval = 42); // 处理

    void stopInternal();
    void stop();

    void capture();

    void captureNow();
    // 是否需要开启人脸支付
    bool getIsNeedFaceDetected();
    void setIsNeedFaceDetected(bool is_need = false);

    std::unique_ptr<QTimer>              timer_capture_;
    std::unique_ptr<QCameraImageCapture> image_capture_;
    std::unique_ptr<QCamera>             camera_;
    bool isNeed_face_detected_ = false;

signals:
    void sendImg(QImage img); // 发送图片

    void sigWorkStart();      // 工作开始
    void sigWorkStop();       // 工作开始
    void sigCameraError();    // 相机错误


    void sigIsNeedFaceDetected();
    void sigCameraOpened();
    void sigCameraClosed();
private:

};

#endif // CAMERAWORKER2_H
