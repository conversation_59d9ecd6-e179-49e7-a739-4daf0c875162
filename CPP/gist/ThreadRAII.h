﻿#ifndef THREADRAII_H
#define THREADRAII_H


#include <iostream>
#include <thread>

class ThreadRAII {
private:
    std::thread &th;

public:
    explicit ThreadRAII(std::thread &t) : th(t) {
    };

    ~ThreadRAII() {
        if (th.joinable()) {
            th.join(); // join() in destructor
//            std::cout << "\n" << __PRETTY_FUNCTION__ << " Destroyed Object & Automatic JOIN" << std::endl;
        }
//        std::cout << "\n" << __PRETTY_FUNCTION__ << " Destruction accomplished" << std::endl;
    }

    // delete the copy constructor as thread cannot be copied or assigned
    // only can it be moved
    ThreadRAII(const ThreadRAII &) = delete;

    // delete the copy assignment operator
    ThreadRAII &operator=(const ThreadRAII &) = delete;
};

#endif // THREADRAII_H
