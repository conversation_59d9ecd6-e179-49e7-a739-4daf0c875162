﻿#ifndef SPLASHSCREEN_H
#define SPLASHSCREEN_H

#include <QList>
#include <QObject>
#include <QPixmap>
#include <QProgressBar>
#include <QSplashScreen>
#include <QThread>
#include <QTime>
#include <QTimer>
#include <QtGlobal>
#include <qDebug>
#include <windows.h>

class SplashScreen : public QObject
{
    Q_OBJECT
public:
    SplashScreen(const QPixmap &pixmap);
    ~SplashScreen();

private:
    QSplashScreen *splashScreen;
    // 随机数列表
    QList<int> numbersList;
    // 启动界面停留的时间
    int elapseTime;

    QThread *workThread;

    void generateAscendRandomNumber();

signals:
    void sendProgress();

public slots:
    void slotUpdateProgress();
    void setProgress();
    void finish(QWidget *w);
};

#endif // SPLASHSCREEN_H
