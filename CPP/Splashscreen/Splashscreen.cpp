﻿#include "Splashscreen.h"
#include <ostream>
#include "LogManager.h"

SplashScreen::SplashScreen(const QPixmap &pixmap)
{
    splashScreen = new QSplashScreen();
    generateAscendRandomNumber();
    splashScreen->setPixmap(pixmap);
    //    splashScreen->setWindowFlags(Qt::Tool |Qt::X11BypassWindowManagerHint | Qt::WindowStaysOnTopHint | Qt::FramelessWindowHint);
    splashScreen->setFont(QFont("微软雅黑", 20, QFont::Bold));
    splashScreen->showMessage("0%", 0x0084, Qt::white);

    splashScreen->show();
    // setProgress();
}

SplashScreen::~SplashScreen()
{
    delete splashScreen;
}

void SplashScreen::setProgress()
{
    int tempTime = elapseTime / 100;
    for (int i = 0; i < 99; i++)
    {
        slotUpdateProgress();
    }

    //    QTimer::singleShot(elapseTime, this, SLOT(close()));
}

void SplashScreen::finish(QWidget *w)
{
    splashScreen->finish(w);
    //    delete splashScreen;
    //    splashScreen = NULL;
}

void SplashScreen::generateAscendRandomNumber()
{
    int i;
    qsrand(QTime(0, 0, 0).secsTo(QTime::currentTime()));
    // 生成100个大小在[0,100]之间的随机数
    for (i = 0; i < 100; i++)
    {
        numbersList.append(qrand() % 99);
    }
    numbersList.append(99);
    
    // 递增排序
    qSort(numbersList.begin(), numbersList.end());
}
void SplashScreen::slotUpdateProgress()
{
    static int num = 0;
    if (num > 96)
    {
        splashScreen->showMessage("100%", 0x0084, Qt::white);
    }
    else
    {
        QString str = QString::number(numbersList[num]) + "%";
        splashScreen->showMessage(str, 0x0084, Qt::white);
        Sleep(40);
        num++;
    }
}
