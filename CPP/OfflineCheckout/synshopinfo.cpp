﻿#include "synshopinfo.h"
#include "../Utils/Utils.h"

SynShopInfo::SynShopInfo(QObject *parent) : QObject(parent)
{

}

void SynShopInfo::getSyncInfo(QList<ShopInfo> synShopInfoListTmp)
{
    syncInfoList = synShopInfoListTmp;
}

void SynShopInfo::handleResults()
{
    updateOrInsertDb(syncInfoList);
}

void SynShopInfo::updateOrInsertDb(QList<ShopInfo> syncInfoListTmp)
{
    QSqlDatabase db;
    QString connectionName = "ShopInfo";
    if (QSqlDatabase::contains(connectionName))
    {
        db = QSqlDatabase::database(connectionName);
    }
    else
    {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        //db.setPassword("Z!a@n#g$c%h^u&a*n(k)a-i=");
    }
    QString dataPath = Utils::getDataBasePath();
    db.setDatabaseName(dataPath + "buyhoo2.db");
    if (db.open() == false)
    {
        qCritical() << QStringLiteral("dishdb数据库打开失败7");
    }
    QSqlQuery   dbQuery(db);
    QStringList list = db.tables();

    if (!list.contains("shops"))
    {
        dbQuery.prepare("create table shops(shop_id integer primary key,"
                                "shop_unique int64, "
                                "shop_name varchar,"
                                "manager_unique varchar,"
                                "manager_account varchar,"
                                "manager_pwd varchar,"
                                "shop_alias varchar,"
                                "trade_area_unique varchar,"
                                "shop_latitude decimal,"
                                "shop_longitude decimal,"
                                "shop_seller_email varchar,"
                                "shop_address_detail varchar,"
                                "shop_phone varchar,"
                                "shop_remark varchar,"
                                "shop_image_path varchar,"
                                "shop_partner varchar,"
                                "shop_APP_ID varchar,"
                                "shop_MCH_ID varchar,"
                                "shop_API_KEY varchar,"
                                "area_dict_num varchar,"
                                "examinestatus integer,"
                                "power_supplier integer,"
                                "examinestatus_reason varchar,"
                                "shop_hours varchar,"
                                "shop_counter varchar,"
                                "channel_id varchar,"
                                "shop_source integer,"
                                "cashier_id varchar,"
                                "sameType integer,"
                                "pc_update_selling_price varchar,"
                                "pc_update_stock varchar,"
                                "pc_stock_goods_calss varchar,"
                                "pc_update_purchase_price varchar,"
                                "pc_update_goods_name varchar,"
                                "pc_goods_delete varchar,"
                                "pc_goods_add varchar)");
        if (!dbQuery.exec())
        {
            qCritical() << QStringLiteral("建店铺信息表失败:") << dbQuery.lastError().text();
        }
    }
    else
    {
        int sizeSyncInfoListTmp = syncInfoListTmp.size();
        int isExit = -1;
        QString selectSql;
        for (int i = 0; i < sizeSyncInfoListTmp; i++)
        {
            dbQuery.prepare("select * from shops where shop_id = ?");
            dbQuery.addBindValue(syncInfoListTmp.at(i).shop_id);
            dbQuery.exec();
//            qDebug() << "!!!!!111" << dbQuery.lastError().text();
            isExit = -1;
            while (dbQuery.next())
            {
                dbQuery.prepare("update shops set shop_unique = ?,"
                                "area_dict_num = ?,"
                                "shop_address_detail = ?,"
                                "shop_hours = ?,"
                                "manager_account = ?,"
                                "shop_image_path = ?,"
                                "shop_phone = ?,"
                                "shop_name = ?,"
                                "sameType = ? where shop_id = ?");
                        dbQuery.addBindValue(syncInfoListTmp.at(i).shop_unique);
                        dbQuery.addBindValue(syncInfoListTmp.at(i).area_dict_num);
                        dbQuery.addBindValue(syncInfoListTmp.at(i).shop_address_detail);
                        dbQuery.addBindValue(syncInfoListTmp.at(i).shop_hours);
                        dbQuery.addBindValue(syncInfoListTmp.at(i).manager_account);
                        dbQuery.addBindValue(syncInfoListTmp.at(i).shop_image_path);
                        dbQuery.addBindValue(syncInfoListTmp.at(i).shop_phone);
                        dbQuery.addBindValue(syncInfoListTmp.at(i).shop_name);
                        dbQuery.addBindValue(syncInfoListTmp.at(i).sameType);
                        dbQuery.addBindValue(syncInfoListTmp.at(i).shop_id);

                dbQuery.exec();
//                qDebug() << "!!!!!112" << selectSql;
                isExit = 1;
            }
            if (isExit == -1)
            {
                dbQuery.prepare("insert into shops (shop_id,"
                                "shop_unique,"
                                "area_dict_num,"
                                "shop_address_detail,"
                                "shop_hours,"
                                "manager_account,"
                                "shop_image_path,"
                                "shop_phone,"
                                "shop_name,"
                                "sameType) values (?,?,?,?,?,?,?,?,?,?)");
                dbQuery.addBindValue(syncInfoListTmp.at(i).shop_id);
                dbQuery.addBindValue(syncInfoListTmp.at(i).shop_unique);
                dbQuery.addBindValue(syncInfoListTmp.at(i).area_dict_num);
                dbQuery.addBindValue(syncInfoListTmp.at(i).shop_address_detail);
                dbQuery.addBindValue(syncInfoListTmp.at(i).shop_hours);
                dbQuery.addBindValue(syncInfoListTmp.at(i).manager_account);
                dbQuery.addBindValue(syncInfoListTmp.at(i).shop_image_path);
                dbQuery.addBindValue(syncInfoListTmp.at(i).shop_phone);
                dbQuery.addBindValue(syncInfoListTmp.at(i).shop_name);
                dbQuery.addBindValue(syncInfoListTmp.at(i).sameType);

                if (dbQuery.exec());
//                qDebug() << "!!!!!113";
            }
        }
    }
    db.close();
}

