﻿#ifndef SYNSHOPINFO_H
#define SYNSHOPINFO_H

#include <QObject>
#include <QDebug>
#include <QSqlQuery>
#include <QCoreApplication>
#include <QSqlError>
#include <QMutex>

class ShopInfo
{
public:
    QString     shop_id;
    QString     shop_unique;
    QString     area_dict_num;
    QString     shop_address_detail;
    QString     shop_hours;
    QString     manager_account;
    QString     shop_image_path;
    QString     shop_phone;
    QString     shop_name;
    QString     sameType;
    QString     manager_pwd;
};

class SynShopInfo : public QObject
{
    Q_OBJECT
public:
    explicit SynShopInfo(QObject *parent = 0);

    void getSyncInfo(QList<ShopInfo> synShopInfoListTmp);

signals:

public slots:

    void handleResults();

    void updateOrInsertDb(QList<ShopInfo> syncInfoListTmp);

private:
    QList<ShopInfo> syncInfoList;
};

#endif // SYNSHOPINFO_H
