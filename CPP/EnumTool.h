﻿#ifndef ENUMTOOL_H
#define ENUMTOOL_H

#include <QObject>
#include <QQmlEngine>


class EnumTool : public QObject
{
    Q_OBJECT
public:
    // NOTE 主页面枚举
    enum class PageEnum : int
    {
        PAGE_LOGIN = 0,  // 登录
        PAGE_PAY,        // 收银
        PAGE_QUERY,      // 查询
        PAGE_NET_ORDER, // 网单
        PAGE_SETTING,    // 设置
        PAGE_GODOWN,     // 入库
        PAGE_GODOWN_OLD, // 入库老版
        PAGE_MEMBERSHIP, // 会员
        PAGE_STATISTICS, // 统计
        PAGE_HANDOVER,   // 交班
        PAGE_WEIGHT,     // 称重
    };
    Q_ENUM(PageEnum)


    // NOTE 支付方式枚举
    enum class PayMethodEnum : int
    {
        PAY_METHOD__UNKNOW = 0, // 未知

        PAY_METHOD__CASH           = 1, // 现金
        PAY_METHOD__ALIPAY_OFFLINE = 2, // 支付宝 线下
        PAY_METHOD__WECHAT_OFFLINE = 3, // 微信 线下
        PAY_METHOD__BANK_CARD      = 4, // 银行卡
        PAY_METHOD__VIPCARD        = 5, // 储值卡
        //6-美团外卖7-饿了么外卖
        PAY_METHOD__COMBINED = 8, // 组合支付
        PAY_METHOD__JINQUAN  = 9, // 金圈平台
        //10-积分兑换 11-百货豆 12-拉卡拉
        PAY_METHOD__YI_TONG = 13, // 易通付款码支付
        //14-合利宝刷卡

        PAY_METHOD__ALIPAY_ONLINE, // 支付宝 线上
        PAY_METHOD__WECHAT_ONLINE, // 微信 线上

        PAY_METHOD__UNIONPAY,      // 银联 云闪付
        PAY_METHOD__FACE,          // 人脸支付
        PAY_METHOD__GOODS_MANAGER, // 商品管理
        PAY_METHOD__MINI_PROGRAM,  // 小程序
        PAY_METHOD__SCAN,          // 扫码支付
        PAY_METHOD__CHANGE,        // 存零
    };
    Q_ENUM(PayMethodEnum)

    enum class NetStatusEnum : int
    {
        NET_STATUS__SUCC    = 200, //成功
        NET_STATUS__FAIL    = 400, //失败
        NET_STATUS__UNUSUAL = 500, //异常
        NET_STATUS__INVALID = 401, //授权失效
    };
    Q_ENUM(NetStatusEnum)

    // NOTE 查询页面枚举
    enum class PageQueryEnum : int
    {
        QUERY_UNKNOW          = 0, // 未知
        QUERY_STOCK_RECORD    = 1, // 出入库记录
        QUERY_SALES_RECORD    = 2, // 销售记录
        QUERY_HANDOVER_RECORD = 3, // 交接班记录
        QUERY_GOODS_INFORMATION = 4, //商品信息列表
        QUERY_GOODS_SALESTATISTICS = 5, //商品销售统计

    };
    Q_ENUM(PageQueryEnum)


    // NOTE 网单页面枚举
    enum class PageNetOrderEnum : int
    {
        NET_ORDER_SWITCH = -1, // 切换

        NET_ORDER_UNKNOW = 0,          // 未知
        NET_ORDER_WAITING_SHIPMENT,    // 待发货
        NET_ORDER_WAITING_RECEIVING,   // 待收货
        NET_ORDER_WAITING_SELF_PICKUP, // 待自提
        NET_ORDER_COMPLETED,           // 已完成
        NET_ORDER_DELIVERY_EXCEPTION,  // 配送异常
        NET_ORDER_WAITING_VERIFY,      // 待确认
        NET_ORDER_WAITING_EVALUATED,   // 待评价
        NET_ORDER_ALL_ORDERS,          // 全部订单

        NET_ORDER_REFUND_WAITING_REVIEW, // 待审核
        NET_ORDER_REFUND_REFUNDED,       // 已退款
        NET_ORDER_REFUND_REJECTED,       // 已拒绝
        NET_ORDER_REFUND_ALL_ORDERS,     // 全部订单
    };
    Q_ENUM(PageNetOrderEnum)


    // NOTE 自定义商品分类
    enum class CusGoodsKindEnum : int
    {
        CUS_GOODS_KIND_PENDING_ORDER = 0, // 挂单
        CUS_GOODS_KIND_MEMBER_SHIP   = 1, // 会员
        CUS_GOODS_KIND_ALL_GOODS     = 2, // 全部商品
        CUS_GOODS_KIND_RECOGNITION   = 3, // 识别
    };
    Q_ENUM(CusGoodsKindEnum)


    enum class PopupCashPayEnum : int
    {
        POPUPCASHPAY_CASH = 0,        // 现金
        POPUPCASHPAY_MEMBERSHIP_CARD, // 储值卡
        POPUPCASHPAY_COMBINATION,     // 组合支付
    };
    Q_ENUM(PopupCashPayEnum)

    // 无码商品枚举
    enum class UidEnum : unsigned long long
    {
        ID_NO_CODE_WEIGHT_GOODS = 999999998, // 无码称重
        ID_NO_CODE_GOODS        = 999999999, // 无码商品

        ID_GOODS_KIND_COMMON      = 99990, // 虚拟常用分类
        ID_GOODS_KIND_COMMON_REAL = 99991, // 真实常用分类
    };
    Q_ENUM(UidEnum)

    // 入库 - 商品详情枚举
    enum class GoodsInfoTabEnum : unsigned long long
    {
        GOODS_INFO_TAB_BASIC_INFO  = 0, // 基础信息
        GOODS_INFO_TAB_SPEC_BASE   = 1, // 基础规格
        GOODS_INFO_TAB_SPEC_MIDDLE = 2, // 中间规格
        GOODS_INFO_TAB_SPEC_MAX    = 3  // 最大规格
    };
    Q_ENUM(GoodsInfoTabEnum)

    // 购物车枚举
    enum class ShopCartTabEnum : unsigned long long
    {
        SHOP_CART_TAB__MAIN_ORDER = 0, // 主单
        SHOP_CART_TAB__PENDING_ORDER   // 挂单
    };
    Q_ENUM(ShopCartTabEnum)

    // 会员枚举
    enum class MemberEnum : unsigned long long
    {
        PAY_PAGE__MEMBER__UNKNOW        = 0,
        PAY_PAGE__MEMBER__NULL          = 1 << 1, // 新增会员
        PAY_PAGE__MEMBER__NEW_MEMBER    = 1 << 2, // 新增会员
        PAY_PAGE__MEMBER__MANAGE_MEMBER = 1 << 3, // 会员管理
        PAY_PAGE__MEMBER__ORDER_LIST    = 1 << 4, // 订单记录
    };
    Q_ENUM(MemberEnum)

    // 支付状态枚举
    enum class PayStatusEnum : unsigned long long
    {
        PAY_STATUS__UNKNOW = 0, // 未知
        PAY_STATUS__PAYING,     // 支付中
        PAY_STATUS__SUCCESS,    // 成功
        PAY_STATUS__ERROR,      // 失败
        PAY_STATUS__CANCEL,     // 取消
        PAY_STATUS__PAID,       // 已付款过
        PAY_STATUS__TIME_OUT    // 超时
    };
    Q_ENUM(PayStatusEnum)

    // TOAST枚举
    enum class ToastLevelEnum : unsigned long long
    {
        TOAST_LEVEL__INFO = 0,
        TOAST_LEVEL__WARN,
        TOAST_LEVEL__ERROR,
    };
    Q_ENUM(ToastLevelEnum)

    enum class PermissionEnum : int
    {
        PERMISSION_PAGE_L1_PAY            = 75, // 收银
        PERMISSION_PAGE_L2_PAY__GOODS_MGR = 90, // 商品管理

        PERMISSION_PAGE_L1_QUERY                            = 76,  // 查询
        PERMISSION_PAGE_L2_QUERY__STOCK_CHANGE_LIST         = 81,  // 出入库记录
        PERMISSION_PAGE_L2_QUERY__SALE_LIST                 = 82,  // 销售记录
        PERMISSION_PAGE_L2_QUERY__HAND_OVER_LIST            = 83,  // 交接班记录
        PERMISSION_PAGE_L2_QUERY__GOODS_INFO_LIST           = 84,  // 商品信息列表
        PERMISSION_PAGE_L2_QUERY__GOODS_SALE_STATISTIC_LIST = 114, // 商品销售统计

        PERMISSION_PAGE_L1_NET_ORDER                        = 77, // 网单
        PERMISSION_PAGE_L2_NET_ORDER__DELIVERY_LIST         = 87, // 配送中
        PERMISSION_PAGE_L2_NET_ORDER__WAITING_SHIPMENT_LIST = 86, // 待发货
        PERMISSION_PAGE_L2_NET_ORDER__NEW_ORDER_LIST        = 85, // 新订单
        PERMISSION_PAGE_L2_NET_ORDER__CANCEL_LIST           = 89, // 已取消
        PERMISSION_PAGE_L2_NET_ORDER__FINISH_LIST           = 88, // 已完成

        PERMISSION_PAGE_L1_GODOWN    = 78,  // 入库
        PERMISSION_PAGE_L1_MEMBER    = 79,  // 会员
        PERMISSION_PAGE_L1_STATISTIC = 80,  // 统计
        PERMISSION_PAGE_L1_SETTING   = 793, // 设置

        PERMISSION_ACTION_MEMBER_RECHARGE     = 111, // 会员 - 充值
        PERMISSION_ACTION_MEMBER_EXCHANGE     = 112, // 会员 - 兑换
        PERMISSION_ACTION_MEMBER_UPDATE       = 113, // 会员 - 更改
        PERMISSION_ACTION_MEMBER_REGISTER     = 114, // 会员 - 注册
        PERMISSION_ACTION_MEMBER_POINT_UPDATE = 309, // 会员 - 积分增减
        PERMISSION_ACTION_MEMBER_DELETE       = 310, // 会员 - 删除

        PERMISSION_ACTION_GOODS_PRICE_UPDATE        = 115, // 售价修改
        PERMISSION_ACTION_GOODS_STOCK_UPDATE        = 116, // 库存修改
        PERMISSION_ACTION_GOODS_PRICE_IN_UPDATE     = 118, // 商品进价修改
        PERMISSION_ACTION_GOODS_NAME_UPDATE         = 119, // 商品名称修改
        PERMISSION_ACTION_GOODS_DELETE_UPDATE       = 120, // 商品删除
        PERMISSION_ACTION_GOODS_SAVE_UPDATE         = 121, // 商品保存
        PERMISSION_ACTION_GOODS_STOCK_IN_UPDATE     = 306, // 入库
        PERMISSION_ACTION_GOODS_STOCK_OUT_UPDATE    = 307, // 出库
        PERMISSION_ACTION_GOODS_MEMBER_PRICE_UPDATE = 308, // 商品会员价修改
    };
    Q_ENUM(PermissionEnum)


    enum class WipeZeroTypeEnum : int
    {
        WIPE_ZERO__OFF = 0,
        WIPE_ZERO__1_YUAN,
        WIPE_ZERO__5_JIAO,
        WIPE_ZERO__1_JIAO,
    };
    Q_ENUM(WipeZeroTypeEnum)


    enum class HttpHandleType : int
    {
        HTTP_HANDLE__ON_FINISHED = 0,
        HTTP_HANDLE__ON_ERROR,
        HTTP_HANDLE__ON_TIMEOUT,
    };
    Q_ENUM(HttpHandleType)


    // 人脸识别状态
    enum class FacePayStatus : int
    {
        FACE_PAY_STATUS__SUCC = 100,
        FACE_PAY_STATUS__REQ_ERROR,
        FACE_PAY_STATUS__BALANCE_INSUFFICIENT,
        FACE_PAY_STATUS__FACE_NOT_FOUND,
        FACE_PAY_STATUS__ORDER_DUPLICATE,
        FACE_PAY_STATUS__UNKNOW,
    };
    Q_ENUM(FacePayStatus)


    enum class NetworkStatus : int
    {
        NETWORK_STATUS__UNKNOW = 100,
        NETWORK_STATUS__CONNECTED,
        NETWORK_STATUS__DISCONNECTED,
    };
    Q_ENUM(NetworkStatus)
    enum class DataStatusEnum : int
    {
        DATA_STATUS__VALID   = 100, //有效
        DATA_STATUS__INVALID = 101, //无效
    };
    Q_ENUM(DataStatusEnum)
};


using PayMethodEnum = EnumTool::PayMethodEnum;

using WipeZeroTypeEnum = EnumTool::WipeZeroTypeEnum;

using HttpHandleType = EnumTool::HttpHandleType;

using FacePayStatus = EnumTool::FacePayStatus;

using UidEnum = EnumTool::UidEnum;

using NetworkStatus = EnumTool::NetworkStatus;

using DataStatusEnum     = EnumTool::DataStatusEnum;

using NetStatusEnum      = EnumTool::NetStatusEnum;


#endif // ENUMTOOL_H
