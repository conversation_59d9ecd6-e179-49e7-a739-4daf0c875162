﻿#pragma once

#include <QObject>
#include <QQmlEngine>

class ConfigToolEnum : public QObject
{
    Q_OBJECT
public:
    enum class WeighingScaleEnum : size_t
    {
        WEIGHTING_SCALE_DA_HUA = 0, // 大华
        WEIGHTING_SCALE_YOU_SHENG,  // 友声
        WEIGHTING_SCALE_TAI_HANG,   // 太航
        WEIGHTING_SCALE_TUO_LI_DUO, // 托利多
        WEIGHTING_SCALE_DING_JIAN,  // 顶尖
        WEIGHTING_SCALE_SHU_HENG,   // 数衡
        WEIGHTING_SCALE_BAI_LUN_SI  // 佰伦斯
    };
    Q_ENUM(WeighingScaleEnum)


    enum class TicketsSpecEnum : size_t
    {
        SPEC_58 = 0,
        SPEC_80
    };
    Q_ENUM(TicketsSpecEnum)


    enum class PrinterConnType : size_t
    {
        PRINTER_CONN_TYPE_SETTING_COM = 0,
        PRINTER_CONN_TYPE_SETTING_LPT,
        PRINTER_CONN_TYPE_SETTING_USB,
        PRINTER_CONN_TYPE_SETTING_NET_IP,
        PRINTER_CONN_TYPE_SETTING_NET_PORT,
    };
    Q_ENUM(PrinterConnType)


    enum class PriceTagTemplateEnum : size_t
    {
        PriceTagTemplate_40x30 = 1,
        PriceTagTemplate_95x38,
        PriceTagTemplate_60x35
    };
    Q_ENUM(PriceTagTemplateEnum)


    enum class WipeZeroEnum : size_t
    {
        WIPE_ZERO_YUAN = 0, // 抹零到元
        WIPE_ZERO_JIAO,     // 抹零到分
    };
    Q_ENUM(WipeZeroEnum)


    enum class InexistWeightGoodsSearchStatusEnum : size_t
    {
        INEXIST_WEIGHT_GOODS_SEARCH_STATUS__ADD = 0,    // 添加新商品
        INEXIST_WEIGHT_GOODS_SEARCH_STATUS__ADD_2_CART, // 添加到购物车
    };
    Q_ENUM(InexistWeightGoodsSearchStatusEnum)


    enum class ConfigEnum : size_t
    {
        // SETTING_SHOPINFO -------------------------------
        STORE_ID = 0,       // 店铺ID
        STORE_NAME,         // 店铺名称
        STORE_ADDRESS,      // 店铺位置
        STAFF_ID,           // 雇员编号
        MECHINE_ID,         // 机器编号
        STORE_PHONE_NUMBER, // 联系电话

        // SETTING_SHOPCONFIG -------------------------------
        IS_DAIKOU,                      // 微信代扣
        IS_MEMBERSHIP_FACE_PAYMENT,     // 会员人脸识别
        IS_NEED_PASSWORD,               // 需要储值密码
        IS_BARCODE_FUZZY_SEARCH,        // 条码模糊搜索
        IS_NOT_USE,                     // 未使用
        IS_MEMBERSHIP_VALIDITY,         // 会员有效期
        IS_PRICE_TAG_MODE,              // 价签模式
        IS_SETTLEMENT_HINT_WORK_NUMBER, // 结算提示工号
        IS_NEGATIVE_PROFIT_HINT,        // 负利润提醒
        TIME_DAIKOU,                    // 代扣等待时长

        // SETTING_PROFIT -------------------------------
        WIPE_ZERO_STATUS, // 抹零状态

        ROUND_DOWN_DIGIT,             // 四舍五入位数
        NOCODE_GOODS_PROFIT_RATIO,    // 无码商品利润比
        NORMAL_GOODS_PROFIT_RATIO,    // 普通商品利润比
        IS_POINT,                     // 积分
        IS_CHANGE_TO_YUAN,            // 存零到元
        IS_MEMBERSHIP_PRICE,          // 会员价
        IS_NOCODE_PAY,                // 无码收银
        IS_FACE_AUTO_PAYMENT,         // 人脸自动结算
        IS_ONE_STEP_PAYMENT_CONFIRM,  // 免密支付确认
        POINTS_RATIO,                 // 积分比例
        IS_MEMBERSHIP_PRICE_DISCOUNT, // 会员可打折

        // SETTING_MARKETING -------------------------------
        ORDER_PROMOTION_INFO,     //促销信息
        IS_BUNDLING_SALE,            // 捆绑销售
        IS_ORDER_MANJIANMANZENG,     // 订单满减满赠
        IS_GOODS_MANJIANMANZENG,     // 商品满减满赠
        IS_PRICE_CHANGE_NO_DISCOUNT, // 改价不参与打折

        // SETTING_CASHBOX -------------------------------
        CASHBOX_PORT,                // 钱箱端口
        IS_OPEN_CASH_BOX_BEFORE_PAY, // 支付前弹钱箱

        // SETTING_TICKETS -------------------------------
        PRINTER_IS_USE_FOOD_TICKET, // 是否使用餐饮小票
        PRINTER_FOOD_TICKET_INDEX,  // 餐饮小票 index

        PRINTER_NAME_TICKET,    // 小票打印机名
        PRINTER_NAME_ONLINE,    // 网单打印机名
        PRINTER_NAME_PRICE_TAG, // 价签打印机名

        PRINTER_NUM_TICKET,    // 小票打印机打印数量
        PRINTER_NUM_ONLINE,    // 网单打印机打印数量
        PRINTER_NUM_PRICE_TAG, // 价签打印机打印数量

        PRINTER_STATE_TICKET,    // 小票打印机状态
        PRINTER_STATE_ONLINE,    // 网单打印机状态
        PRINTER_STATE_PRICE_TAG, // 价签打印机状态

        PRINTER_IS_PRINT__TICKET,    // 小票打印机 是否打印
        PRINTER_IS_PRINT__ONLINE,    // 网单打印机 是否打印
        PRINTER_IS_PRINT__PRICE_TAG, // 价签打印机 是否打印

        PRINTER_IP,    // 打印机IP
        PRINTER_PORT,  // 打印机PORT
        PRINTER_STATE, // 打印机状态

        PRINTER_DEFAULT_PRINT_COUNT,    // 默认打印数量
        TICKETS_SPECIFICATION,          // 小票规格
        IS_FOOD_TICKETS,                // 餐饮小票
        IS_PENDING_ORDER_PRINT_TICKETS, // 挂单打印小票
        REMARK_INFO_TICKETS,            // 备注内容

        // SETTING_PRICETAG -------------------------------
        TAG_TEMUPLATE_TYPE, // 价签模板 类型

        // SETTING_WEIGHING -------------------------------
        IS_SERIAL_PORT_WEIGHING_SCALE,              // 是否使用串口秤 --
        SERIAL_PORT_WEIGHING_SCALE_BAUD,            // 串口秤波特率
        SERIAL_PORT_WEIGHING_SCALE_COM,             // 串口秤端口号
        SERIAL_PORT_WEIGHING_SCALE_DATA_PARSE_TYPE, // 串口秤数据类型
        SERIAL_PORT_SCALE_IS_KG,                    // 串口秤是否是KG

        IS_BAR_CODE_WEIGHING_SCALE, // 是否使用条码秤 --
        BAR_CODE_WEIGHING_TYPE,     // 条码秤类型 --
        BAR_CODE_WEIGHING_SCALE_IP, // 条码秤IP


        ///-------------------------------------------| 条码秤 |-------------------------------------------
        //
        BARCODE_LABEL_SCALE_INFO_JSON,
        //
        ///-------------------------------------------| 条码秤 |-------------------------------------------


        // SETTING_SECSCREEN -------------------------------
        IS_USE_SECOND_SCREEN, // 启用副屏


        // SETTING_OTHER -------------------------------
        IS_VOICE_REMINDERS, // 播报实付金额


        // SETTING_ONLINE -------------------------------
        IS_USE_ONLINE_STORE,           // 启用线上商城
        IS_NET_LIST,                   // 开启网单
        IS_AUTO_TAKE_ORDERS_AND_PRINT, // 自动接单打印
        IS_REFUND_ORDER_AND_PRINT,     // 自动打印退款单
        ONLINE_PRINT_COUNT,            // 线上小票打印数量
        ONLINE_TICKETS_SPEC,           // 线上小票规格
        IS_SEPARATE_ONLINE_REMARK,     // 使用独立的备注
        REMARK_INFO_ONLINE,            // 线上备注


        // SETTING_SYSTEM -------------------------------
        BAR_CODE_WEIGHING_PRE_TWO_CODE, // 称重商品的前两位
        BAR_CODE_NORMAL_PRE_TWO_CODE,   // 普通商品的前两位
        MEMBERSHIP_PRE_TWO_CODE,        // 会员的前两位
        DISPLAY_DIGIT_LIMIT,            // 显示位数限制
        GOODS_LIST_FONT_SIZE,           // 商品列表字体大小
        IS_ACCUMULATE_WEIGHING,         // 是否累计称重
        IS_PROGRAM_TOP,                 // 程序置顶
        IS_WEIGHTING_OTHER_IMAGE,       // 称重其他图片
        IS_WEIGHTING_ON_CLICKED_UPDATE, // 点击更新称重
        IS_WEIGHTING_ON_DOUBLE_CLICK,   // 称重双击
        IS_KEYPAD_HIDE,                 // 小键盘隐藏
        IS_PENDING_ORDER_NUMBER_SELECT, // 挂单号选择
        IS_PENDING_ORDER_SHOW_LIST,     // 显示挂单列表
        IS_SHOW_EXPIRATION_DATE,        // 显示保质期
        IS_BAR_CODE_USE_WEIGHTING,      // 条码按重量
        IS_DEFAULT_STORED_VALUE_CARD,   // 默认储值卡
        IS_SORT_BY_INDEX,               // 按索引
        IS_SORT_BY_ABC,                 // 按字母
        IS_MIANMI_PRINT,                // 免密打印
        IS_SHOW_NO_CODE_GOODS,          // 是否显示无码商品
        IS_SWAP_NO_CODE_GOODS,          // 是否交换无码称重和商品位置

        INEXIST_WEIGHT_GOODS_SEARCH_STATUS, // 不存在的称重商品 在搜索框输入后(accept)执行的操作

        ///-------------------------------------------| 账号信息 |-------------------------------------------
        //
        USER_ACCOUNT,  // 用户账号
        USER_PASSWORD, // 用户密码

        REQ_HOST_PREFIX,              // 网络请求 - 请求前缀
        REQ_HOST_PREFIX_MINI_PROGRAM, // 网络请求 - 小程序请求前缀
        REQ_HOST_PREFIX_FACE,         // 网络请求 - 人脸请求前缀
        REQ_HOST_PREFIX_TAKEAWAY,     // 网络请求 - 网单请求前缀

        //
        ///-------------------------------------------| 账号信息 |-------------------------------------------

        PRINTER_PRICE_TAG_40x30, //40x30价签配置
        PRINTER_PRICE_TAG_95x38, //95x38价签配置
        PRINTER_PRICE_TAG_60x35, //95x38价签配置

        ///-------------------------------------------| TTS |-------------------------------------------
        //

        TTS_ENGINE, // TTS 引擎
        TTS_LOCALE, // TTS 区域
        TTS_VOICE,  // TTS 声音
        ///-------------------------------------------| NINGYU |-------------------------------------------
        NINGYU_ADMIN_ACCOUNT,//宁宇管理账号
        NINGYU_TIME_PRINT,
        //
        ///-------------------------------------------| TTS |-------------------------------------------

        IS_VERIFY_EAN13,         // 是否需要验证 EAN13
        IS_USE_VIRTUAL_KEYBOARD, // 是否使用虚拟键盘

        REQ_GOODS_BEGIN_DATE_TIME, // 请求商品开始时间

        PAY_METHOD_CONFIG, // 支付方式配置

        PENDING_ORDERS, // 挂单
        IS_AUTO_RUN,    // 开机自启

        FONT_SCALE_RATIO, // 字体缩放比例

        kIsQuickCreateNocodeGoods, // 快速创建无码商品

        kIsShowStock,        // 显示库存?
        kIsPowerOffWhenExit, // 退出关机?

        kCamera4Face,  // 人脸摄像头
        kCamera4Goods, // 商品摄像头

        kIsUseJinWeightScale, // 串口秤转市斤

        kIsAutoAddTag, // 是否自动添加TAG

        kIsPlayKeyboardSound, //播放 键盘声
        kIsPlayPromptSound,   //播放 提示音

        kIsPlayCashTts,     //播放 现金 TTS
        kIsPlayOnlineTts,   //播放 在线支付 TTS
        kIsPlayMemberTts,   //播放 会员 TTS
        kIsPlayOfflineTTs,  //播放 线下 TTS
        kIsPlayMiniProgram, //播放 小程序 TTS
        kIsPlayMiniProgramRefund, //播放 小程序退款 TTS
        kIsPlayMiniProgramTakeOrder, //播放 小程序接单 TTS
        kClacDigitNum, //计算位数
        kClacType,     //计算方式 0:舍 1:四舍五入

        kDefaultGoodsKind, //默认商品分类

        kDefaultWipeZeroType, //默认抹零方式

        kRecognitionRatioP1,
        kRecognitionRatioP2,
        kRecognitionRatioP3,
        kRecognitionRatioP4,

        /**shopInfo**/
        USER_ACCOUNT_SHOPINFO,
        USER_PASSWORD_SHOPINFO,
        language_Selected, //国际语言

    };
    Q_ENUM(ConfigEnum)
    // 将当前类中枚举注册到QML
    static void declareQML()
    {
        qmlRegisterType<ConfigToolEnum>("SettingEnum", 1, 0, "SettingEnum");
    }
};

using ConfigEnum           = ConfigToolEnum::ConfigEnum;
using PriceTagTemplateEnum = ConfigToolEnum::PriceTagTemplateEnum;
