﻿#include "ConfigTool.h"
#include <QApplication>
#include <QFileInfo>
#include <QSettings>
#include <QtSerialPort/QSerialPortInfo>
#include <string>
#include "ControlModule/ControlManager.h"
#include "ControlModule/ShopCartControl.h"
#include "LogManager.h"
#include "Utils/Utils.h"
#include "Utils/Utils4Qml.h"
#include "json-qt.hpp"
#include "nameof.hpp"

std::unique_ptr<ConfigTool> ConfigTool::singleton_;
std::mutex                  ConfigTool::mutex_;

ConfigTool::ConfigTool(QObject *parent) : QObject{parent}
{
    auto config_path = Utils::getAppDirPath() + "/" + setting_config_name_;
    isFileExist(config_path) ? is_need_init_config_ = false : is_need_init_config_ = true;
    initValueMap();
    q_settings_.reset(new QSettings(config_path, QSettings::IniFormat));
    q_settings_->beginGroup(group_name_);
    q_shopInfo_.reset(new QSettings(config_path, QSettings::IniFormat));
    q_shopInfo_->beginGroup(group_name_shopInfo);

    if (is_need_init_config_)
    {
        initSettingConfig();
    }
    else
    {
        initByConfig();
    }

    is_use_jin_weight_scale = getSetting(ConfigEnum::kIsUseJinWeightScale).toBool();
    is_negative_profit_hint = getSetting(ConfigEnum::IS_NEGATIVE_PROFIT_HINT).toBool();
    is_auto_add_tag_        = getSetting(ConfigEnum::kIsAutoAddTag).toBool();

    is_play_keyboard_sound_  = getSetting(ConfigEnum::kIsPlayKeyboardSound).toBool();
    is_play_prompt_sound     = getSetting(ConfigEnum::kIsPlayPromptSound).toBool();
    is_play_cash_tts_        = getSetting(ConfigEnum::kIsPlayCashTts).toBool();
    is_play_online_tts       = getSetting(ConfigEnum::kIsPlayOnlineTts).toBool();
    is_play_member_tts       = getSetting(ConfigEnum::kIsPlayMemberTts).toBool();
    is_play_offline_tts      = getSetting(ConfigEnum::kIsPlayOfflineTTs).toBool();
    is_play_mini_program     = getSetting(ConfigEnum::kIsPlayMiniProgram).toBool();
    is_play_mini_program_refund = getSetting(ConfigEnum::kIsPlayMiniProgramRefund).toBool();
    is_play_mini_program_take_orders = getSetting(ConfigEnum::kIsPlayMiniProgramTakeOrder).toBool();
    clac_digit_num_          = getSetting(ConfigEnum::kClacDigitNum).toInt();
    points_ratio_            = getSetting(ConfigEnum::POINTS_RATIO).toInt();
    is_use_virtual_keyboard_ = getSetting(ConfigEnum::IS_USE_VIRTUAL_KEYBOARD).toBool();
    is_refundOrder_and_print = getSetting(ConfigEnum::IS_REFUND_ORDER_AND_PRINT).toBool();
    language_selected = getSetting(ConfigEnum::language_Selected).toString();
    normal_goods_profit_ratio_ = getSetting(ConfigEnum::NORMAL_GOODS_PROFIT_RATIO).toFloat();
}

ConfigTool::~ConfigTool()
{
    q_settings_->sync();
    q_settings_->endGroup();
    q_shopInfo_->sync();
    q_shopInfo_->endGroup();
}

ConfigTool *ConfigTool::getInstance()
{
    std::lock_guard<std::mutex> lock(mutex_);

    if (singleton_ == nullptr)
    {
        singleton_.reset(new ConfigTool());
    };
    return singleton_.get();
}

QVariant ConfigTool::getSetting(ConfigToolEnum::ConfigEnum setting_id)
{
    switch (setting_id)
    {
    case ConfigToolEnum::ConfigEnum::STAFF_ID:
        {
            auto shop_control = ControlManager::getInstance()->getShopControl();
            return shop_control->getPersonInfo().cashier_id;
        }

    case ConfigToolEnum::ConfigEnum::MECHINE_ID:
        return 1;

    case ConfigToolEnum::ConfigEnum::IS_AUTO_RUN:
        return Utils::System::isAutoRun(INTERNAL_NAME);

    default:
        auto value = q_settings_->value(value_map_.value(setting_id));

        if (value.toString() == "true")
            return true;
        if (value.toString() == "false")
            return false;
        return value;
    }
}
QVariant ConfigTool::getSettingShopInfo(ConfigToolEnum::ConfigEnum setting_id)
{
    switch (setting_id)
    {
    case ConfigToolEnum::ConfigEnum::STAFF_ID:
        {
            auto shop_control = ControlManager::getInstance()->getShopControl();
            return shop_control->getPersonInfo().cashier_id;
        }

    case ConfigToolEnum::ConfigEnum::MECHINE_ID:
        return 1;

    case ConfigToolEnum::ConfigEnum::IS_AUTO_RUN:
        return Utils::System::isAutoRun(INTERNAL_NAME);

    default:
        auto value = q_shopInfo_->value(value_map_.value(setting_id));

        if (value.toString() == "true")
            return true;
        if (value.toString() == "false")
            return false;
        return value;
    }
}

QVariant ConfigTool::getSettingShopInfo(int setting_id_i)
{
    auto setting_id = static_cast<ConfigToolEnum::ConfigEnum>(setting_id_i);
    return getSettingShopInfo(setting_id);
}
QVariant ConfigTool::getSetting(int setting_id_i)
{
    auto setting_id = static_cast<ConfigToolEnum::ConfigEnum>(setting_id_i);
    return getSetting(setting_id);
}
void ConfigTool::setSetting(ConfigToolEnum::ConfigEnum setting_id, QVariant value, bool init_only)
{
    if (!init_only)
    {
        if (value == getSetting(setting_id))
            return;
    }
    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_data_, "setSetting: write{} value:{}", value_map_.value(setting_id).toStdString(),
                       value.toString().toStdString());

    if (!init_only)
    {
        switch (setting_id)
        {
        case ConfigToolEnum::ConfigEnum::IS_USE_SECOND_SCREEN:
            Utils4Qml::getInstance()->changeSecondScreenVisibleState(value.toBool());
            break;
        case ConfigToolEnum::ConfigEnum::BAR_CODE_WEIGHING_PRE_TWO_CODE:
            ControlManager::getInstance()->getBarcodeLabelScale()->barcode_weight_prefix_ = value.toString();
            break;
        case ConfigToolEnum::ConfigEnum::IS_AUTO_RUN:
            auto setting_value = value_map_.value(setting_id);
            Utils::System::setAutoRun(INTERNAL_NAME, value.toBool());
            if(Utils::System::isAutoRun(INTERNAL_NAME)){
                LOG_EVT_INFO("当前状态自启动");
            }else{
                LOG_EVT_INFO("当前状态非自启动");
            }
            return ;
        }
    }
    LOG_EVT_INFO("当前状态非自启动:{}",value.toString().toStdString());
    auto setting_value = value_map_.value(setting_id);
    auto save_string = value.toString();
    q_settings_->setValue(setting_value, value);
    q_settings_->sync(); // 是否立即写入

    if (init_only)
        return;
}

void ConfigTool::setSetting(int setting_id_i, QVariant value, bool init_only)
{
    auto setting_id = static_cast<ConfigToolEnum::ConfigEnum>(setting_id_i);
    setSetting(setting_id, value, init_only);
}
void ConfigTool::changeLanguage(QString language)
{
    // 卸载当前翻译器
    qApp->removeTranslator(&translator);

    // 初始化加载翻译文件（默认载入中文）
    LOG_EVT_INFO("LANGUAGE:{}",language.toStdString());
    if(translator.load(language)){
        qApp->installTranslator(&translator);
        LOG_EVT_INFO("翻译文件加载成功");
    }else{
        LOG_EVT_INFO("翻译文件加载失败");
    }
    // 发出语言更改信号
    emit languageChanged();
}
void ConfigTool::setSettingShopInfo(ConfigToolEnum::ConfigEnum setting_id, QVariant value, bool init_only)
{
    if (!init_only)
    {
        if (value == getSetting(setting_id))
            return;
    }

    SPDLOG_LOGGER_INFO(LogMgr::getInst()->logger_data_, "setSetting: write{} value:{}", value_map_.value(setting_id).toStdString(),
                       value.toString().toStdString());

    if (!init_only)
    {
        switch (setting_id)
        {
        case ConfigToolEnum::ConfigEnum::IS_USE_SECOND_SCREEN:
            Utils4Qml::getInstance()->changeSecondScreenVisibleState(value.toBool());
            break;
        case ConfigToolEnum::ConfigEnum::BAR_CODE_WEIGHING_PRE_TWO_CODE:
            ControlManager::getInstance()->getBarcodeLabelScale()->barcode_weight_prefix_ = value.toString();
            break;
        case ConfigToolEnum::ConfigEnum::IS_AUTO_RUN:
            return Utils::System::setAutoRun(INTERNAL_NAME, value.toBool());
        }
    }

    auto setting_value = value_map_.value(setting_id);

    auto save_string = value.toString();

    q_shopInfo_->setValue(setting_value, value);
    q_shopInfo_->sync(); // 是否立即写入

    if (init_only)
        return;
}

void ConfigTool::setSettingShopInfo(int setting_id_i, QVariant value, bool init_only)
{
    auto setting_id = static_cast<ConfigToolEnum::ConfigEnum>(setting_id_i);
    setSetting(setting_id, value, init_only);
}
QStringList ConfigTool::getSerialNameList()
{
    auto serialPortInfoList = QSerialPortInfo::availablePorts();

    QStringList list;
    for (auto &serial : serialPortInfoList)
        list.append(serial.portName().toUpper());

    return list;
}

void ConfigTool::setCursorState(bool state)
{
    state ? QApplication::setOverrideCursor(Qt::ArrowCursor) : QApplication::setOverrideCursor(Qt::BlankCursor);
}
void ConfigTool::setFontRatio(float font_ratio)
{
    if (font_ratio < 1)
    {
        font_ratio = 1;
    }
    else if (font_ratio > 1.5)
    {
        font_ratio = 1.5;
    }
    font_ratio_ = font_ratio;
    setSetting(ConfigEnum::FONT_SCALE_RATIO, font_ratio);
    emit fontRatioChanged();
}
float ConfigTool::fontRatio()
{
    return font_ratio_;
}
float ConfigTool::fontRatio2()
{
    return 1 + (font_ratio_ - 1) * .3;
}
bool ConfigTool::isQuickCreateNoCodeGoods()
{
    return is_quick_create_nocode_goods_;
}
void ConfigTool::setIsQuickCreateNoCodeGoods(bool is_quick)
{
    is_quick_create_nocode_goods_ = is_quick;
    setSetting(ConfigEnum::kIsQuickCreateNocodeGoods, is_quick);
    emit isQuickCreateNoCodeGoodsChanged();
}
bool ConfigTool::isShowStock()
{
    return is_show_stock_;
}

void ConfigTool::setIsShowStock(bool is_show)
{
    is_show_stock_ = is_show;
    setSetting(ConfigEnum::kIsShowStock, is_show);
    emit isShowStockChanged();
}
bool ConfigTool::isPowerOffWhenExit()
{
    return is_power_off_when_exit_;
}
void ConfigTool::setIsPowerOffWhenExit(bool is_power_off)
{
    is_power_off_when_exit_ = is_power_off;
    setSetting(ConfigEnum::kIsPowerOffWhenExit, is_power_off);
    emit isPowerOffWhenExitChanged();
}

bool ConfigTool::isUseJinWeightScale()
{
    return is_use_jin_weight_scale;
}

void ConfigTool::setIsUseJinWeightScale(bool is_jin)
{
    is_use_jin_weight_scale = is_jin;
    setSetting(ConfigEnum::kIsUseJinWeightScale, is_jin);
    emit isUseJinWeightScaleChanged();
}

bool ConfigTool::isNegativeProfitHint()
{
    return is_negative_profit_hint;
}

void ConfigTool::setIsNegativeProfitHint(bool is_show)
{
    is_negative_profit_hint = is_show;
    setSetting(ConfigEnum::IS_NEGATIVE_PROFIT_HINT, is_show);
    emit isNegativeProfitHintChanged();
}

bool ConfigTool::isUseRecognition()
{
#ifdef _RECOGNITION_
    return true;
#else
    return false;
#endif
}

void ConfigTool::initValueMap()
{
    value_map_.insert(ConfigToolEnum::ConfigEnum::STORE_ID, "store_id");
    value_map_.insert(ConfigToolEnum::ConfigEnum::STORE_NAME, "store_name");
    value_map_.insert(ConfigToolEnum::ConfigEnum::STORE_ADDRESS, "store_address");
    value_map_.insert(ConfigToolEnum::ConfigEnum::STAFF_ID, "staff_id");
    value_map_.insert(ConfigToolEnum::ConfigEnum::MECHINE_ID, "mechine_id");
    value_map_.insert(ConfigToolEnum::ConfigEnum::STORE_PHONE_NUMBER, "store_phone_number"); //

    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_DAIKOU, "is_daikou");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_MEMBERSHIP_FACE_PAYMENT, "is_membership_face_payment");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_NEED_PASSWORD, "is_need_password");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_BARCODE_FUZZY_SEARCH, "is_barcode_fuzzy_search");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_NOT_USE, "is_not_use");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_MEMBERSHIP_VALIDITY, "is_membership_validity");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_PRICE_TAG_MODE, "is_price_tag_mode");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_SETTLEMENT_HINT_WORK_NUMBER, "is_settlement_hint_work_number");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_NEGATIVE_PROFIT_HINT, "is_negative_profit_hint");
    value_map_.insert(ConfigToolEnum::ConfigEnum::TIME_DAIKOU, "time_daikou"); //

    value_map_.insert(ConfigToolEnum::ConfigEnum::WIPE_ZERO_STATUS, "wipe_zero_status");
    value_map_.insert(ConfigToolEnum::ConfigEnum::ROUND_DOWN_DIGIT, "round_down_digit");
    value_map_.insert(ConfigToolEnum::ConfigEnum::NOCODE_GOODS_PROFIT_RATIO, "nocode_goods_profit_ratio");
    value_map_.insert(ConfigToolEnum::ConfigEnum::NORMAL_GOODS_PROFIT_RATIO, "normal_goods_profit_ratio");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_POINT, "is_point");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_CHANGE_TO_YUAN, "is_change_to_yuan");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_MEMBERSHIP_PRICE, "is_membership_price");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_NOCODE_PAY, "is_nocode_pay");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_FACE_AUTO_PAYMENT, "is_face_auto_payment");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_ONE_STEP_PAYMENT_CONFIRM, "is_one_step_payment_confirm");
    value_map_.insert(ConfigToolEnum::ConfigEnum::POINTS_RATIO, "points_ratio");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_MEMBERSHIP_PRICE_DISCOUNT, "is_membership_price_discount"); //

    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_BUNDLING_SALE, "is_bundling_sale");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_ORDER_MANJIANMANZENG, "is_order_manjianmanzeng");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_GOODS_MANJIANMANZENG, "is_goods_manjianmanzeng");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_PRICE_CHANGE_NO_DISCOUNT, "is_price_change_no_discount"); //

    value_map_.insert(ConfigToolEnum::ConfigEnum::CASHBOX_PORT, "cashbox_port");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_OPEN_CASH_BOX_BEFORE_PAY, "is_open_cash_box_before_pay"); //

    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_IS_USE_FOOD_TICKET, "PRINTER_IS_USE_FOOD_TICKET");
    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_FOOD_TICKET_INDEX, "PRINTER_FOOD_TICKET_INDEX");

    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_NAME_TICKET, "printer_name_ticket");
    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_NAME_ONLINE, "printer_name_online");
    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_NAME_PRICE_TAG, "printer_name_price_tag");

    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_NUM_TICKET, "printer_num_ticket");
    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_NUM_ONLINE, "printer_num_online");
    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_NUM_PRICE_TAG, "printer_num_price_tag");

    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_IS_PRINT__TICKET, "printer_is_print__ticket");
    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_IS_PRINT__ONLINE, "printer_is_print__online");
    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_IS_PRINT__PRICE_TAG, "printer_is_print__price_tag");

    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_IP, "printer_ip");
    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_PORT, "printer_port");

    value_map_.insert(ConfigToolEnum::ConfigEnum::PRINTER_DEFAULT_PRINT_COUNT, "printer_default_print_count");
    value_map_.insert(ConfigToolEnum::ConfigEnum::TICKETS_SPECIFICATION, "tickets_specification");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_FOOD_TICKETS, "is_food_tickets");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_PENDING_ORDER_PRINT_TICKETS, "is_pending_order_print_tickets");
    value_map_.insert(ConfigToolEnum::ConfigEnum::REMARK_INFO_TICKETS, "tickets_remark_info"); //

    value_map_.insert(ConfigToolEnum::ConfigEnum::TAG_TEMUPLATE_TYPE, "tag_temuplate_type"); //

    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_SERIAL_PORT_WEIGHING_SCALE, "is_serial_port_weighing_scale");
    value_map_.insert(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_BAUD, "serial_port_weighing_scale_baud");
    value_map_.insert(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_COM, "serial_port_weighing_scale_com");
    value_map_.insert(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_DATA_PARSE_TYPE, "serial_port_weighing_scale_data_parse_type");
    value_map_.insert(ConfigToolEnum::ConfigEnum::SERIAL_PORT_SCALE_IS_KG, "serial_port_scale_is_kg");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_BAR_CODE_WEIGHING_SCALE, "is_bar_code_weighing_scale");
    value_map_.insert(ConfigToolEnum::ConfigEnum::BAR_CODE_WEIGHING_TYPE, "bar_code_weighing_type");
    value_map_.insert(ConfigToolEnum::ConfigEnum::BAR_CODE_WEIGHING_SCALE_IP, "bar_code_weighing_scale_ip");

    value_map_.insert(ConfigToolEnum::ConfigEnum::BARCODE_LABEL_SCALE_INFO_JSON, "barcode_label_scale_info_json");


    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_USE_SECOND_SCREEN, "is_use_second_screen"); //

    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_VOICE_REMINDERS, "is_voice_reminders"); //

    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_USE_ONLINE_STORE, "is_use_online_store");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_NET_LIST, "is_net_list");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_AUTO_TAKE_ORDERS_AND_PRINT, "is_auto_take_orders_and_print");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_REFUND_ORDER_AND_PRINT, "is_refund_order_and_print");
    value_map_.insert(ConfigToolEnum::ConfigEnum::ONLINE_PRINT_COUNT, "online_print_count");
    value_map_.insert(ConfigToolEnum::ConfigEnum::ONLINE_TICKETS_SPEC, "online_tickets_spec");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_SEPARATE_ONLINE_REMARK, "is_separate_online_remark");
    value_map_.insert(ConfigToolEnum::ConfigEnum::REMARK_INFO_ONLINE, "online_remark_info");

    value_map_.insert(ConfigToolEnum::ConfigEnum::BAR_CODE_WEIGHING_PRE_TWO_CODE, "bar_code_weighing_pre_two_code");
    value_map_.insert(ConfigToolEnum::ConfigEnum::BAR_CODE_NORMAL_PRE_TWO_CODE, "BAR_CODE_NORMAL_PRE_TWO_CODE");
    value_map_.insert(ConfigToolEnum::ConfigEnum::MEMBERSHIP_PRE_TWO_CODE, "membership_pre_two_code");
    value_map_.insert(ConfigToolEnum::ConfigEnum::DISPLAY_DIGIT_LIMIT, "display_digit_limit");
    value_map_.insert(ConfigToolEnum::ConfigEnum::GOODS_LIST_FONT_SIZE, "goods_list_font_size");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_ACCUMULATE_WEIGHING, "is_accumulate_weighing");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_PROGRAM_TOP, "is_program_top");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_WEIGHTING_OTHER_IMAGE, "is_weighting_other_image");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_WEIGHTING_ON_CLICKED_UPDATE, "is_weighting_on_clicked_update");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_WEIGHTING_ON_DOUBLE_CLICK, "is_weighting_on_double_click");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_KEYPAD_HIDE, "is_keypad_hide");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_PENDING_ORDER_NUMBER_SELECT, "is_pending_order_number_select");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_PENDING_ORDER_SHOW_LIST, "is_pending_order_show_list");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_SHOW_EXPIRATION_DATE, "is_show_expiration_date");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_BAR_CODE_USE_WEIGHTING, "is_bar_code_use_weighting");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_DEFAULT_STORED_VALUE_CARD, "is_default_stored_value_card");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_SORT_BY_INDEX, "is_sort_by_index");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_SORT_BY_ABC, "is_sort_by_abc");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_MIANMI_PRINT, "is_mianmi_print");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_SHOW_NO_CODE_GOODS, "is_show_no_code_goods");
    value_map_.insert(ConfigToolEnum::ConfigEnum::IS_SWAP_NO_CODE_GOODS, "IS_SWAP_NO_CODE_GOODS");
    value_map_.insert(ConfigToolEnum::ConfigEnum::INEXIST_WEIGHT_GOODS_SEARCH_STATUS, "INEXIST_WEIGHT_GOODS_SEARCH_STATUS");

    value_map_.insert(ConfigToolEnum::ConfigEnum::USER_ACCOUNT, "user_account");
    value_map_.insert(ConfigToolEnum::ConfigEnum::USER_PASSWORD, "user_password");

    /**[shopInfo]**/
    value_map_.insert(ConfigToolEnum::ConfigEnum::USER_ACCOUNT_SHOPINFO, "account");
    //value_map_.insert(ConfigToolEnum::ConfigEnum::USER_PASSWORD_SHOPINFO, "");


    value_map_.insert(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX, "req_host_prefix");
    value_map_.insert(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX_MINI_PROGRAM, "req_host_prefix_mini_program");
    value_map_.insert(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX_FACE, "req_host_prefix_face");
    value_map_.insert(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX_TAKEAWAY, "req_host_prefix_takeaway");


    value_map_.insert(ConfigToolEnum::ConfigEnum::NINGYU_ADMIN_ACCOUNT, "ningyu_admin_account");
    value_map_.insert(ConfigToolEnum::ConfigEnum::NINGYU_TIME_PRINT, "ningyu_time_print");

    value_map_.insert(ConfigEnum::PRINTER_PRICE_TAG_40x30, "PRINTER_PRICE_TAG_40x30");
    value_map_.insert(ConfigEnum::PRINTER_PRICE_TAG_95x38, "PRINTER_PRICE_TAG_95x38");
    value_map_.insert(ConfigEnum::PRINTER_PRICE_TAG_60x35, "PRINTER_PRICE_TAG_60x35");

    value_map_.insert(ConfigEnum::TTS_ENGINE, "tts_engine");
    value_map_.insert(ConfigEnum::TTS_LOCALE, "tts_locale");
    value_map_.insert(ConfigEnum::TTS_VOICE, "tts_voice");
    value_map_.insert(ConfigEnum::IS_VERIFY_EAN13, "is_verify_ean13");
    value_map_.insert(ConfigEnum::IS_USE_VIRTUAL_KEYBOARD, "is_use_virtual_keyboard");
    value_map_.insert(ConfigEnum::REQ_GOODS_BEGIN_DATE_TIME, "req_goods_begin_date_time");
    value_map_.insert(ConfigEnum::PAY_METHOD_CONFIG, "PAY_METHOD_CONFIG");
    value_map_.insert(ConfigEnum::ORDER_PROMOTION_INFO, "orderPromotionInfo");
    value_map_.insert(ConfigEnum::PENDING_ORDERS, "PENDING_ORDERS");
    value_map_.insert(ConfigEnum::IS_AUTO_RUN, "IS_AUTO_RUN");

    value_map_.insert(ConfigEnum::FONT_SCALE_RATIO, "FONT_SCALE_RATIO");
    value_map_.insert(ConfigEnum::kIsQuickCreateNocodeGoods, "IsQuickCreateNocodeGoods");
    value_map_.insert(ConfigEnum::kIsShowStock, "IsShowStock");
    value_map_.insert(ConfigEnum::kIsPowerOffWhenExit, "IsPowerOffWhenExit");

    value_map_.insert(ConfigEnum::kCamera4Face, "kCamera4Face");
    value_map_.insert(ConfigEnum::kCamera4Goods, "kCamera4Goods");
    value_map_.insert(ConfigEnum::kIsUseJinWeightScale, "kIsUseJinWeightScale");
    value_map_.insert(ConfigEnum::kIsAutoAddTag, "kIsAutoAddTag");

    value_map_.insert(ConfigEnum::kIsPlayKeyboardSound, "kIsPlayKeyboardSound");
    value_map_.insert(ConfigEnum::kIsPlayPromptSound, "kIsPlayPromptSound");
    value_map_.insert(ConfigEnum::kIsPlayCashTts, "kIsPlayCashTts");
    value_map_.insert(ConfigEnum::kIsPlayOnlineTts, "kIsPlayOnlineTts");
    value_map_.insert(ConfigEnum::kIsPlayMemberTts, "kIsPlayMemberTts");
    value_map_.insert(ConfigEnum::kIsPlayOfflineTTs, "kIsPlayOfflineTTs");
    value_map_.insert(ConfigEnum::kIsPlayMiniProgram, "kIsPlayMiniProgram");
    value_map_.insert(ConfigEnum::kIsPlayMiniProgramRefund, "kIsPlayMiniProgramRefund");
    value_map_.insert(ConfigEnum::language_Selected, "language_Selected");
    value_map_.insert(ConfigEnum::kIsPlayMiniProgramTakeOrder, "kIsPlayMiniProgramTakeOrder");
    value_map_.insert(ConfigEnum::kClacDigitNum, "kClacDigitNum");
    value_map_.insert(ConfigEnum::kClacType, "kClacType");
    value_map_.insert(ConfigEnum::kDefaultGoodsKind, "kDefaultGoodsKind");
    value_map_.insert(ConfigEnum::kDefaultWipeZeroType, "kDefaultWipeZeroType");

    value_map_.insert(ConfigEnum::kRecognitionRatioP1, "kRecognitionRatioP1");
    value_map_.insert(ConfigEnum::kRecognitionRatioP2, "kRecognitionRatioP2");
    value_map_.insert(ConfigEnum::kRecognitionRatioP3, "kRecognitionRatioP3");
    value_map_.insert(ConfigEnum::kRecognitionRatioP4, "kRecognitionRatioP4");
    // value_map_.insert(ConfigEnum::kDefaultWipeZeroType, "kDefaultWipeZeroType");
}

void ConfigTool::initSettingConfig()
{
    setSetting(ConfigToolEnum::ConfigEnum::STORE_NAME, "", true);
    setSetting(ConfigToolEnum::ConfigEnum::STORE_ADDRESS, "", true);
    setSetting(ConfigToolEnum::ConfigEnum::STORE_PHONE_NUMBER, "", true);

    setSetting(ConfigToolEnum::ConfigEnum::IS_DAIKOU, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_MEMBERSHIP_FACE_PAYMENT, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_NEED_PASSWORD, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_BARCODE_FUZZY_SEARCH, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_NOT_USE, false);
    setSetting(ConfigToolEnum::ConfigEnum::IS_MEMBERSHIP_VALIDITY, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_PRICE_TAG_MODE, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_SETTLEMENT_HINT_WORK_NUMBER, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_NEGATIVE_PROFIT_HINT, true);
    setSetting(ConfigToolEnum::ConfigEnum::TIME_DAIKOU, 20);

    setSetting(ConfigToolEnum::ConfigEnum::WIPE_ZERO_STATUS, (int)ConfigToolEnum::WipeZeroEnum::WIPE_ZERO_YUAN);

    setSetting(ConfigToolEnum::ConfigEnum::ROUND_DOWN_DIGIT, 2);
    setSetting(ConfigToolEnum::ConfigEnum::NOCODE_GOODS_PROFIT_RATIO, .15);
    setSetting(ConfigToolEnum::ConfigEnum::NORMAL_GOODS_PROFIT_RATIO, .25);
    setSetting(ConfigToolEnum::ConfigEnum::IS_POINT, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_CHANGE_TO_YUAN, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_MEMBERSHIP_PRICE, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_NOCODE_PAY, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_FACE_AUTO_PAYMENT, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_ONE_STEP_PAYMENT_CONFIRM, true);
    setSetting(ConfigToolEnum::ConfigEnum::POINTS_RATIO, 1);
    setSetting(ConfigToolEnum::ConfigEnum::IS_MEMBERSHIP_PRICE_DISCOUNT, true);

    setSetting(ConfigToolEnum::ConfigEnum::IS_BUNDLING_SALE, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_ORDER_MANJIANMANZENG, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_GOODS_MANJIANMANZENG, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_PRICE_CHANGE_NO_DISCOUNT, true);

    setSetting(ConfigToolEnum::ConfigEnum::CASHBOX_PORT, "XP-58");
    setSetting(ConfigToolEnum::ConfigEnum::IS_OPEN_CASH_BOX_BEFORE_PAY, true);


    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_IS_USE_FOOD_TICKET, false);

    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NAME_TICKET, "Adobe PDF");
    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NAME_ONLINE, "Adobe PDF");
    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NAME_PRICE_TAG, "Adobe PDF");

    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NUM_TICKET, 1);
    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NUM_ONLINE, 1);
    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_NUM_PRICE_TAG, 1);

    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_IS_PRINT__TICKET, true);
    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_IS_PRINT__ONLINE, true);
    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_IS_PRINT__PRICE_TAG, true);


    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_IP, "*************");
    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_PORT, "9999");
    //    SetSetting(SettingToolEnum::SettingEnum::PRINTER_STATE,"INI_NO_DATA");
    setSetting(ConfigToolEnum::ConfigEnum::PRINTER_DEFAULT_PRINT_COUNT, 1);
    setSetting(ConfigToolEnum::ConfigEnum::IS_FOOD_TICKETS, true);
    setSetting(ConfigToolEnum::ConfigEnum::TICKETS_SPECIFICATION, static_cast<int>(ConfigToolEnum::TicketsSpecEnum::SPEC_58));
    setSetting(ConfigToolEnum::ConfigEnum::IS_PENDING_ORDER_PRINT_TICKETS, true);
    setSetting(ConfigToolEnum::ConfigEnum::REMARK_INFO_TICKETS, "");

    setSetting(ConfigToolEnum::ConfigEnum::TAG_TEMUPLATE_TYPE, static_cast<int>(ConfigToolEnum::PriceTagTemplateEnum::PriceTagTemplate_40x30), true);

    setSetting(ConfigToolEnum::ConfigEnum::IS_SERIAL_PORT_WEIGHING_SCALE, "INI_NO_DATA");
    setSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_BAUD, 9600);
    setSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_COM, "COM1");
    setSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_WEIGHING_SCALE_DATA_PARSE_TYPE, 0);
    setSetting(ConfigToolEnum::ConfigEnum::SERIAL_PORT_SCALE_IS_KG, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_BAR_CODE_WEIGHING_SCALE, "INI_NO_DATA");
    setSetting(ConfigToolEnum::ConfigEnum::BAR_CODE_WEIGHING_TYPE, static_cast<int>(ConfigToolEnum::WeighingScaleEnum::WEIGHTING_SCALE_DING_JIAN));
    setSetting(ConfigToolEnum::ConfigEnum::BAR_CODE_WEIGHING_SCALE_IP, "*************");

    setSetting(ConfigToolEnum::ConfigEnum::BARCODE_LABEL_SCALE_INFO_JSON, "");

    setSetting(ConfigToolEnum::ConfigEnum::IS_USE_SECOND_SCREEN, true);

    setSetting(ConfigToolEnum::ConfigEnum::IS_VOICE_REMINDERS, true);

    //    SetSetting(SettingToolEnum::SettingEnum::IS_USE_ONLINE_STORE,"INI_NO_DATA");
    setSetting(ConfigToolEnum::ConfigEnum::IS_NET_LIST, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_AUTO_TAKE_ORDERS_AND_PRINT, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_REFUND_ORDER_AND_PRINT, true);
    setSetting(ConfigToolEnum::ConfigEnum::ONLINE_PRINT_COUNT, 1);
    setSetting(ConfigToolEnum::ConfigEnum::ONLINE_TICKETS_SPEC, static_cast<int>(ConfigToolEnum::TicketsSpecEnum::SPEC_58));
    setSetting(ConfigToolEnum::ConfigEnum::IS_SEPARATE_ONLINE_REMARK, false);
    setSetting(ConfigToolEnum::ConfigEnum::REMARK_INFO_ONLINE, "");

    setSetting(ConfigToolEnum::ConfigEnum::BAR_CODE_WEIGHING_PRE_TWO_CODE, "21", true);
    setSetting(ConfigToolEnum::ConfigEnum::BAR_CODE_NORMAL_PRE_TWO_CODE, "31", true);
    setSetting(ConfigToolEnum::ConfigEnum::MEMBERSHIP_PRE_TWO_CODE, "", true);
    setSetting(ConfigToolEnum::ConfigEnum::DISPLAY_DIGIT_LIMIT, 2, true);
    setSetting(ConfigToolEnum::ConfigEnum::GOODS_LIST_FONT_SIZE, 17, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_ACCUMULATE_WEIGHING, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_PROGRAM_TOP, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_WEIGHTING_OTHER_IMAGE, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_WEIGHTING_ON_CLICKED_UPDATE, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_WEIGHTING_ON_DOUBLE_CLICK, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_KEYPAD_HIDE, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_PENDING_ORDER_NUMBER_SELECT, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_PENDING_ORDER_SHOW_LIST, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_SHOW_EXPIRATION_DATE, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_BAR_CODE_USE_WEIGHTING, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_DEFAULT_STORED_VALUE_CARD, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_SORT_BY_INDEX, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_SORT_BY_ABC, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_MIANMI_PRINT, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_SHOW_NO_CODE_GOODS, true);
    setSetting(ConfigToolEnum::ConfigEnum::IS_SWAP_NO_CODE_GOODS, false);
    setSetting(ConfigToolEnum::ConfigEnum::INEXIST_WEIGHT_GOODS_SEARCH_STATUS,
               static_cast<int>(ConfigToolEnum::InexistWeightGoodsSearchStatusEnum::INEXIST_WEIGHT_GOODS_SEARCH_STATUS__ADD), true);

    setSetting(ConfigToolEnum::ConfigEnum::USER_ACCOUNT, "");
    setSetting(ConfigToolEnum::ConfigEnum::USER_PASSWORD, "");

    /**[shopInfo]**/
    setSetting(ConfigToolEnum::ConfigEnum::USER_ACCOUNT_SHOPINFO, "");

    setSetting(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX, "http://buyhoo.cc/", true);
    setSetting(ConfigToolEnum::ConfigEnum::NINGYU_ADMIN_ACCOUNT, "");
    setSetting(ConfigToolEnum::ConfigEnum::NINGYU_TIME_PRINT, "");
    setSetting(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX_MINI_PROGRAM, "http://buyhoo.cc/", true);
    setSetting(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX_FACE, "http://face.buyhoo.cc/", true);
    setSetting(ConfigToolEnum::ConfigEnum::REQ_HOST_PREFIX_TAKEAWAY, "http://buyhoo.cc/shop/", true);

    setSetting(ConfigEnum::PRINTER_PRICE_TAG_40x30, "");
    setSetting(ConfigEnum::PRINTER_PRICE_TAG_95x38, "");
    setSetting(ConfigEnum::PRINTER_PRICE_TAG_60x35, "");

    setSetting(ConfigToolEnum::ConfigEnum::TTS_ENGINE, "", true);
    setSetting(ConfigToolEnum::ConfigEnum::TTS_LOCALE, "", true);
    setSetting(ConfigToolEnum::ConfigEnum::TTS_VOICE, "", true);

    setSetting(ConfigEnum::IS_VERIFY_EAN13, false);
    setSetting(ConfigEnum::IS_USE_VIRTUAL_KEYBOARD, true);
    setSetting(ConfigEnum::REQ_GOODS_BEGIN_DATE_TIME, "");
    setSetting(ConfigEnum::ORDER_PROMOTION_INFO, "");
    setSetting(ConfigEnum::PAY_METHOD_CONFIG, "");

    setSetting(ConfigEnum::PENDING_ORDERS, "");
    setSetting(ConfigEnum::PENDING_ORDERS, false);

    setSetting(ConfigEnum::IS_AUTO_RUN, true);

    setSetting(ConfigEnum::FONT_SCALE_RATIO, 1);

    setSetting(ConfigEnum::kIsQuickCreateNocodeGoods, true);
    setSetting(ConfigEnum::kIsShowStock, true);
    setSetting(ConfigEnum::kIsPowerOffWhenExit, true);

    setSetting(ConfigEnum::kCamera4Face, "");
    setSetting(ConfigEnum::kCamera4Goods, "");
    setSetting(ConfigEnum::kIsUseJinWeightScale, false);
    setSetting(ConfigEnum::kIsAutoAddTag, false);

    setSetting(ConfigEnum::kIsPlayKeyboardSound, true);
    setSetting(ConfigEnum::kIsPlayPromptSound, true);
    setSetting(ConfigEnum::kIsPlayCashTts, true);
    setSetting(ConfigEnum::kIsPlayOnlineTts, true);
    setSetting(ConfigEnum::kIsPlayMemberTts, true);
    setSetting(ConfigEnum::kIsPlayOfflineTTs, true);
    setSetting(ConfigEnum::kIsPlayMiniProgram, true);
    setSetting(ConfigEnum::kIsPlayMiniProgramRefund, true);
    setSetting(ConfigEnum::kIsPlayMiniProgramTakeOrder, true);
    setSetting(ConfigEnum::kClacDigitNum, 2);
    setSetting(ConfigEnum::kClacType, 0);
    setSetting(ConfigEnum::kDefaultGoodsKind, "");
    setSetting(ConfigEnum::language_Selected, "");
    setSetting(ConfigEnum::kRecognitionRatioP1, QPointF(0, 0));
    setSetting(ConfigEnum::kRecognitionRatioP2, QPointF(1, 0));
    setSetting(ConfigEnum::kRecognitionRatioP3, QPointF(1, 1));
    setSetting(ConfigEnum::kRecognitionRatioP4, QPointF(0, 1));
}

void ConfigTool::initByConfig()
{
    QVariant tmp;
    tmp = getSetting(ConfigEnum::FONT_SCALE_RATIO);
    setFontRatio(tmp.isNull() ? 1 : tmp.toFloat());

    tmp = getSetting(ConfigEnum::kIsQuickCreateNocodeGoods);
    setIsQuickCreateNoCodeGoods(tmp.isNull() ? true : tmp.toBool());

    tmp = getSetting(ConfigEnum::kIsPowerOffWhenExit);
    setIsPowerOffWhenExit(tmp.isNull() ? true : tmp.toBool());

    tmp = getSetting(ConfigEnum::kIsShowStock);
    setIsShowStock(tmp.isNull() ? true : tmp.toBool());

    tmp = getSetting(ConfigEnum::BAR_CODE_WEIGHING_PRE_TWO_CODE);
    setSetting(ConfigEnum::BAR_CODE_WEIGHING_PRE_TWO_CODE, tmp.isNull() ? "21" : tmp.toString(), true);

    tmp = getSetting(ConfigEnum::BAR_CODE_NORMAL_PRE_TWO_CODE);
    setSetting(ConfigEnum::BAR_CODE_NORMAL_PRE_TWO_CODE, tmp.isNull() ? "31" : tmp.toString(), true);

    tmp = getSetting(ConfigEnum::IS_NEGATIVE_PROFIT_HINT);
    setSetting(ConfigEnum::IS_NEGATIVE_PROFIT_HINT, tmp.isNull() ? true : tmp.toBool(), true);

    tmp = getSetting(ConfigEnum::kClacDigitNum);
    setSetting(ConfigEnum::kClacDigitNum, tmp.isNull() ? 2 : tmp.toUInt(), true);

    tmp = getSetting(ConfigEnum::kClacDigitNum);
    setSetting(ConfigEnum::kClacDigitNum, tmp.isNull() ? 2 : tmp.toUInt(), true);

    tmp = getSetting(ConfigEnum::kClacType);

    auto clac_type = tmp.toUInt();
    if (clac_type > 1)
        clac_type = 0;

    setSetting(ConfigEnum::kClacType, tmp.isNull() ? 0 : clac_type, true);

    tmp = getSetting(ConfigEnum::kDefaultWipeZeroType);
    setDefaultWipeZeroType(tmp.isNull() ? (int)WipeZeroTypeEnum::WIPE_ZERO__OFF : tmp.toInt());

    tmp = getSetting(ConfigEnum::kRecognitionRatioP1);
    if (tmp.isNull())
    {
        setRecognitionRatioP1(QPointF(0, 0));
    }
    else
    {
        setRecognitionRatioP1(tmp.toPointF());
    }


    tmp = getSetting(ConfigEnum::kRecognitionRatioP2);
    if (tmp.isNull())
    {
        setRecognitionRatioP2(QPointF(1, 0));
    }
    else
    {
        setRecognitionRatioP2(tmp.toPointF());
    }


    tmp = getSetting(ConfigEnum::kRecognitionRatioP3);
    if (tmp.isNull())
    {
        setRecognitionRatioP3(QPointF(1, 1));
    }
    else
    {
        setRecognitionRatioP3(tmp.toPointF());
    }


    tmp = getSetting(ConfigEnum::kRecognitionRatioP4);
    if (tmp.isNull())
    {
        setRecognitionRatioP4(QPointF(0, 1));
    }
    else
    {
        setRecognitionRatioP4(tmp.toPointF());
    }
}

bool ConfigTool::isFileExist(QString fullFilePath)
{
    QFileInfo fileInfo(fullFilePath);
    if (fileInfo.exists())
        return true;
    return false;
}

bool ConfigTool::isAutoAddTag()
{
    return is_auto_add_tag_;
}

void ConfigTool::setIsAutoAddTag(bool is_auto)
{
    is_auto_add_tag_ = is_auto;
    setSetting(ConfigEnum::kIsAutoAddTag, is_auto);
    emit isAutoAddTagChanged();
}

bool ConfigTool::isPlayKeyboardSound()
{
    return is_play_keyboard_sound_;
}

void ConfigTool::setIsPlayKeyboardSound(bool is_play)
{
    is_play_keyboard_sound_ = is_play;
    setSetting(ConfigEnum::kIsPlayKeyboardSound, is_play);
    emit isPlayKeyboardSoundChanged();
}

bool ConfigTool::isPlayPromptSound()
{
    return is_play_prompt_sound;
}

void ConfigTool::setIsPlayPromptSound(bool is_play)
{
    is_play_prompt_sound = is_play;
    setSetting(ConfigEnum::kIsPlayPromptSound, is_play);
    emit isPlayPromptSoundChanged();
}

bool ConfigTool::isPlayCashTts()
{
    return is_play_cash_tts_;
}
bool ConfigTool::isRefundOrderAndPrint()
{
    return is_refundOrder_and_print;
}
void ConfigTool::setIsPlayCashTts(bool is_play)
{
    is_play_cash_tts_ = is_play;
    setSetting(ConfigEnum::kIsPlayCashTts, is_play);
    emit isPlayCashTtsChanged();
}

bool ConfigTool::isPlayOnlineTts()
{
    return is_play_online_tts;
}

void ConfigTool::setIsPlayOnlineTts(bool is_play)
{
    is_play_online_tts = is_play;
    setSetting(ConfigEnum::kIsPlayOnlineTts, is_play);
    emit isPlayOnlineTtsChanged();
}

bool ConfigTool::isPlayMemberTts()
{
    return is_play_member_tts;
}

void ConfigTool::setIsPlayMemberTts(bool is_play)
{
    is_play_member_tts = is_play;
    setSetting(ConfigEnum::kIsPlayMemberTts, is_play);
    emit isPlayMemberTtsChanged();
}

bool ConfigTool::isPlayOfflineTTs()
{
    return is_play_offline_tts;
}

void ConfigTool::setIsPlayOfflineTTs(bool is_play)
{
    is_play_offline_tts = is_play;
    setSetting(ConfigEnum::kIsPlayOfflineTTs, is_play);
    emit isPlayOfflineTTsChanged();
}

bool ConfigTool::isPlayMiniProgram()
{
    return is_play_mini_program;
}

void ConfigTool::setIsPlayMiniProgram(bool is_play)
{
    is_play_mini_program = is_play;
    setSetting(ConfigEnum::kIsPlayMiniProgram, is_play);
    emit isPlayMiniProgramChanged();
}
bool ConfigTool::isPlayMiniProgramRefund()
{
    return is_play_mini_program_refund;
}
QString ConfigTool::languageSelected()
{
    return language_selected;
}
bool ConfigTool::isPlayMiniProgramTakeOrders()
{
    return is_play_mini_program_take_orders;
}
void ConfigTool::setisPlayMiniProgramRefund(bool is_play)
{
    is_play_mini_program_refund = is_play;
    setSetting(ConfigEnum::kIsPlayMiniProgramRefund, is_play);
    emit isPlayMiniProgramRefundChanged();
}
void ConfigTool::setlanguageSelected(QString language)
{
    language_selected = language;
    setSetting(ConfigEnum::language_Selected, language);
    emit languageSelectedChanged();
}
void ConfigTool::restartProcess()
{

    QString     program          = QApplication::applicationFilePath();
    QStringList arguments        = QApplication::arguments();
    QString     workingDirectory = QDir::currentPath();
    QProcess::startDetached(program, arguments, workingDirectory);
    QApplication::exit();
}
void ConfigTool::setIsPlayMiniProgramTakeOrders(bool is_play)
{
    is_play_mini_program_take_orders = is_play;
    setSetting(ConfigEnum::kIsPlayMiniProgramTakeOrder, is_play);
    emit isPlayMiniProgramTakeOrdersChanged();
}
int ConfigTool::clacDigitNum()
{
    return clac_digit_num_;
}

void ConfigTool::setClacDigitNum(int digit_num)
{
    clac_digit_num_ = digit_num;
    setSetting(ConfigEnum::kClacDigitNum, digit_num);
    emit clacDigitNumChanged();
}

int ConfigTool::calcType()
{
    return calc_type_;
}

void ConfigTool::setCalcType(int calc_type)
{
    calc_type_ = calc_type;
    emit calcTypeChanged();
}

bool ConfigTool::isCanRecognitionDebug()
{
#ifdef _RECOGNITION_DEBUG_
    return true;
#else
    return false;
#endif
}

bool ConfigTool::isRecognitionDebug()
{
    return is_recognition_debug_;
}

void ConfigTool::setIsRecognitionDebug(bool is_recognition)
{
    is_recognition_debug_ = is_recognition;
    emit isRecognitionDebugChanged();
}

int ConfigTool::pointsRatio()
{
    return points_ratio_;
}

void ConfigTool::setPointsRatio(int ratio)
{
    points_ratio_ = ratio;
    setSetting(ConfigEnum::POINTS_RATIO, ratio);
    emit pointsRatioChanged();
}

bool ConfigTool::isUseFloatKeyboard()
{
    return is_use_float_keyboard_;
}

void ConfigTool::setIsUseFloatKeyboard(bool is_use)
{
    is_use_float_keyboard_ = is_use;
    emit isUseFloatKeyboardChanged();
}

bool ConfigTool::isUseVirtualKeyboard()
{
    return is_use_virtual_keyboard_;
}

void ConfigTool::setIsUseVirtualKeyboard(bool is_use)
{
    is_use_virtual_keyboard_ = is_use;
    setSetting(ConfigEnum::IS_USE_VIRTUAL_KEYBOARD, is_use);
    emit isUseVirtualKeyboardChanged();
}

bool ConfigTool::isUseStaticKeyboard()
{
    return is_use_static_keyboard_;
}

void ConfigTool::setIsUseStaticKeyboard(bool is_use)
{
    is_use_static_keyboard_ = is_use;
    emit isUseStaticKeyboardChanged();
}

float ConfigTool::normalGoodsProfitRatio()
{
    return normal_goods_profit_ratio_;
}

void ConfigTool::setNormalGoodsProfitRatio(float profit_ratio)
{
    auto profit_ratio_str      = QString::number(profit_ratio, 'f', 2);
    normal_goods_profit_ratio_ = profit_ratio_str.toFloat();
    setSetting(ConfigEnum::NORMAL_GOODS_PROFIT_RATIO, profit_ratio_str);
    emit normalGoodsProfitRatioChanged();
}

int ConfigTool::defaultWipeZeroType()
{
    return default_wipe_zero_type_;
}

void ConfigTool::setDefaultWipeZeroType(int type)
{
    default_wipe_zero_type_ = type;
    setSetting(ConfigEnum::kDefaultWipeZeroType, type);
    emit defaultWipeZeroTypeChanged();
}
