﻿#ifndef CONFIGTOOL_H
#define CONFIGTOOL_H

#include <QMap>
#include <QObject>
#include <QQmlEngine>
#include <QSharedPointer>
#include <QString>
#include <iostream>
#include <memory>
#include <mutex>
#include "ConfModule/ConfigEnum.h"
#include "EnumTool.h"
#include <QTranslator>

class QSettings;
class ConfigTool : public QObject
{
    Q_OBJECT

    Q_PROPERTY(float fontRatio READ fontRatio WRITE setFontRatio NOTIFY fontRatioChanged FINAL)
    Q_PROPERTY(float fontRatio2 READ fontRatio2 NOTIFY fontRatioChanged FINAL)

    //积分比例
    Q_PROPERTY(int pointsRatio READ pointsRatio WRITE setPointsRatio NOTIFY pointsRatioChanged FINAL)

    Q_PROPERTY(bool isQuickCreateNocodeGoods READ isQuickCreateNoCodeGoods WRITE setIsQuickCreateNoCodeGoods NOTIFY isQuickCreateNoCodeGoodsChanged FINAL)
    Q_PROPERTY(bool isShowStock READ isShowStock WRITE setIsShowStock NOTIFY isShowStockChanged FINAL)

    Q_PROPERTY(bool isPowerOffWhenExit READ isPowerOffWhenExit WRITE setIsPowerOffWhenExit NOTIFY isPowerOffWhenExitChanged FINAL)

    Q_PROPERTY(bool isUseJinWeightScale READ isUseJinWeightScale WRITE setIsUseJinWeightScale NOTIFY isUseJinWeightScaleChanged FINAL)

    Q_PROPERTY(bool isNegativeProfitHint READ isNegativeProfitHint WRITE setIsNegativeProfitHint NOTIFY isNegativeProfitHintChanged FINAL)

    Q_PROPERTY(bool isUseRecognition READ isUseRecognition)

    Q_PROPERTY(bool isAutoAddTag READ isAutoAddTag WRITE setIsAutoAddTag NOTIFY isAutoAddTagChanged FINAL)

    Q_PROPERTY(bool isPlayKeyboardSound READ isPlayKeyboardSound WRITE setIsPlayKeyboardSound NOTIFY isPlayKeyboardSoundChanged FINAL)
    Q_PROPERTY(bool isPlayPromptSound READ isPlayPromptSound WRITE setIsPlayPromptSound NOTIFY isPlayPromptSoundChanged FINAL)

    Q_PROPERTY(bool isPlayCashTts READ isPlayCashTts WRITE setIsPlayCashTts NOTIFY isPlayCashTtsChanged FINAL)
    Q_PROPERTY(bool isPlayOnlineTts READ isPlayOnlineTts WRITE setIsPlayOnlineTts NOTIFY isPlayOnlineTtsChanged FINAL)

    Q_PROPERTY(bool isPlayMemberTts READ isPlayMemberTts WRITE setIsPlayMemberTts NOTIFY isPlayMemberTtsChanged FINAL)
    Q_PROPERTY(bool isPlayOfflineTTs READ isPlayOfflineTTs WRITE setIsPlayOfflineTTs NOTIFY isPlayOfflineTTsChanged FINAL)
    Q_PROPERTY(bool isPlayMiniProgram READ isPlayMiniProgram WRITE setIsPlayMiniProgram NOTIFY isPlayMiniProgramChanged FINAL)
    Q_PROPERTY(bool isPlayMiniProgram READ isPlayMiniProgram WRITE setIsPlayMiniProgram NOTIFY isPlayMiniProgramChanged FINAL)
    Q_PROPERTY(bool isPlayMiniProgramRefund READ isPlayMiniProgramRefund WRITE setisPlayMiniProgramRefund NOTIFY isPlayMiniProgramRefundChanged FINAL)
    Q_PROPERTY(QString languageSelected READ languageSelected WRITE setlanguageSelected NOTIFY languageSelectedChanged FINAL)
    Q_PROPERTY(bool isPlayMiniProgramTakeOrders READ isPlayMiniProgramTakeOrders WRITE setIsPlayMiniProgramTakeOrders NOTIFY isPlayMiniProgramTakeOrdersChanged FINAL)

    Q_PROPERTY(int clacDigitNum READ clacDigitNum WRITE setClacDigitNum NOTIFY clacDigitNumChanged FINAL)

    Q_PROPERTY(int calcType READ calcType WRITE setCalcType NOTIFY calcTypeChanged FINAL)

    Q_PROPERTY(bool isRecognitionDebug READ isRecognitionDebug WRITE setIsRecognitionDebug NOTIFY isRecognitionDebugChanged FINAL)

    Q_PROPERTY(bool isUseFloatKeyboard READ isUseFloatKeyboard WRITE setIsUseFloatKeyboard NOTIFY isUseFloatKeyboardChanged FINAL)
    Q_PROPERTY(bool isUseStaticKeyboard READ isUseStaticKeyboard WRITE setIsUseStaticKeyboard NOTIFY isUseStaticKeyboardChanged FINAL)
    Q_PROPERTY(bool isUseVirtualKeyboard READ isUseVirtualKeyboard WRITE setIsUseVirtualKeyboard NOTIFY isUseVirtualKeyboardChanged FINAL)

    Q_PROPERTY(float normalGoodsProfitRatio READ normalGoodsProfitRatio WRITE setNormalGoodsProfitRatio NOTIFY normalGoodsProfitRatioChanged FINAL)

    Q_PROPERTY(int defaultWipeZeroType READ defaultWipeZeroType WRITE setDefaultWipeZeroType NOTIFY defaultWipeZeroTypeChanged FINAL)

    Q_PROPERTY(QPointF recognitionRatioP1 READ recognitionRatioP1 WRITE setRecognitionRatioP1 NOTIFY recognitionRatioP1Changed FINAL)
    Q_PROPERTY(QPointF recognitionRatioP2 READ recognitionRatioP2 WRITE setRecognitionRatioP2 NOTIFY recognitionRatioP2Changed FINAL)
    Q_PROPERTY(QPointF recognitionRatioP3 READ recognitionRatioP3 WRITE setRecognitionRatioP3 NOTIFY recognitionRatioP3Changed FINAL)
    Q_PROPERTY(QPointF recognitionRatioP4 READ recognitionRatioP4 WRITE setRecognitionRatioP4 NOTIFY recognitionRatioP4Changed FINAL)

    /**shopInfo**/

private:
    static std::unique_ptr<ConfigTool> singleton_;
    static std::mutex                  mutex_;

    explicit ConfigTool(QObject *parent = nullptr);

public:
    ~ConfigTool() override;
    static ConfigTool *getInstance();

    //获取设置
    QVariant             getSetting(ConfigToolEnum::ConfigEnum setting_id);
    Q_INVOKABLE QVariant getSetting(int setting_id_i);

    //获取设置
    QVariant             getSettingShopInfo(ConfigToolEnum::ConfigEnum setting_id);
    Q_INVOKABLE QVariant getSettingShopInfo(int setting_id_i);

    //设置设置
    void             setSetting(ConfigToolEnum::ConfigEnum setting_id, QVariant value, bool init_only = false);
    Q_INVOKABLE void setSetting(int setting_id_i, QVariant value, bool init_only = false);
    //设置ShopInfo设置
    void             setSettingShopInfo(ConfigToolEnum::ConfigEnum setting_id, QVariant value, bool init_only = false);
    Q_INVOKABLE void setSettingShopInfo(int setting_id_i, QVariant value, bool init_only = false);
    //设置语言
    Q_INVOKABLE void changeLanguage(QString language);
    // 获取可用COM端口
    Q_INVOKABLE static QStringList getSerialNameList();

    Q_INVOKABLE static void setCursorState(bool state);

    Q_INVOKABLE void restartProcess();

    void  setFontRatio(float font_ratio);
    float fontRatio();
    float fontRatio2();

    bool isQuickCreateNoCodeGoods();
    void setIsQuickCreateNoCodeGoods(bool is_quick);

    bool isShowStock();
    void setIsShowStock(bool is_show);

    bool isPowerOffWhenExit();

    void setIsPowerOffWhenExit(bool is_power_off);

    bool isUseJinWeightScale();
    void setIsUseJinWeightScale(bool is_jin);

    bool isNegativeProfitHint();
    void setIsNegativeProfitHint(bool is_show);

    bool isUseRecognition();

    bool isAutoAddTag();
    void setIsAutoAddTag(bool is_auto);


    bool isPlayKeyboardSound();
    void setIsPlayKeyboardSound(bool is_play);

    bool isPlayPromptSound();
    void setIsPlayPromptSound(bool is_play);

    bool isPlayCashTts();
    bool isRefundOrderAndPrint();
    void setIsPlayCashTts(bool is_play);

    bool isPlayOnlineTts();
    void setIsPlayOnlineTts(bool is_play);

    bool isPlayMemberTts();
    void setIsPlayMemberTts(bool is_play);

    bool isPlayOfflineTTs();
    void setIsPlayOfflineTTs(bool is_play);

    bool isPlayMiniProgram();
    void setIsPlayMiniProgram(bool is_play);

    bool isPlayMiniProgramTakeOrders();
    void setIsPlayMiniProgramTakeOrders(bool is_play);

    bool isPlayMiniProgramRefund();
    void setisPlayMiniProgramRefund(bool is_play);

    QString languageSelected();
    void setlanguageSelected(QString language);

    int  clacDigitNum();
    void setClacDigitNum(int digit_num);

    int  calcType();
    void setCalcType(int calc_type);

    Q_INVOKABLE bool isCanRecognitionDebug();

    bool isRecognitionDebug();
    void setIsRecognitionDebug(bool is_recognition);

    int pointsRatio();
    void setPointsRatio(int ratio);

    bool isUseFloatKeyboard();
    void setIsUseFloatKeyboard(bool is_use);

    bool isUseVirtualKeyboard();
    void setIsUseVirtualKeyboard(bool is_use);

    bool isUseStaticKeyboard();
    void setIsUseStaticKeyboard(bool is_use);

    float normalGoodsProfitRatio();
    void  setNormalGoodsProfitRatio(float profit_ratio);

    int  defaultWipeZeroType();
    void setDefaultWipeZeroType(int type);

    QPointF recognitionRatioP1()
    {
        return recognition_ratio_p1_;
    };
    void setRecognitionRatioP1(QPointF point)
    {
        recognition_ratio_p1_ = point;
        setSetting(ConfigEnum::kRecognitionRatioP1, point);
        emit recognitionRatioP1Changed();
    };
    QPointF recognition_ratio_p1_ = QPointF(.0, .0);


    QPointF recognitionRatioP2()
    {
        return recognition_ratio_p2_;
    };
    void setRecognitionRatioP2(QPointF point)
    {
        recognition_ratio_p2_ = point;
        setSetting(ConfigEnum::kRecognitionRatioP2, point);
        emit recognitionRatioP2Changed();
    };
    QPointF recognition_ratio_p2_ = QPointF(1, 0);


    QPointF recognitionRatioP3()
    {
        return recognition_ratio_p3_;
    };
    void setRecognitionRatioP3(QPointF point)
    {
        recognition_ratio_p3_ = point;
        setSetting(ConfigEnum::kRecognitionRatioP3, point);
        emit recognitionRatioP3Changed();
    };
    QPointF recognition_ratio_p3_ = QPointF(1, 1);


    QPointF recognitionRatioP4()
    {
        return recognition_ratio_p4_;
    };
    void setRecognitionRatioP4(QPointF point)
    {
        recognition_ratio_p4_ = point;
        setSetting(ConfigEnum::kRecognitionRatioP4, point);
        emit recognitionRatioP4Changed();
    };
    QPointF recognition_ratio_p4_ = QPointF(.0, 1);

private:
    // 初始化与枚举对应的字符串列表
    void initValueMap();

    // 初始化配置(不存在)
    void initSettingConfig();

    // 根据配置文件初始化
    void initByConfig();

    bool isFileExist(QString fullFilePath);


    QString group_name_          = "settings";
    QString group_name_shopInfo  = "shopInfo";
    QString setting_config_name_ = "config.ini";
    bool    is_need_init_config_ = true; // 是否需要初始化设置

    QMap<ConfigToolEnum::ConfigEnum, QString> value_map_;
    QSharedPointer<QSettings>                 q_settings_;
    QSharedPointer<QSettings>                 q_shopInfo_;
    QTranslator translator;
    float font_ratio_                   = 1.0;
    bool  is_quick_create_nocode_goods_ = false;
    bool  is_show_stock_                = true;
    bool  is_power_off_when_exit_       = true;

    bool is_use_jin_weight_scale = false;
    bool is_negative_profit_hint = false;

    bool is_auto_add_tag_;

    bool is_play_keyboard_sound_ = false; //键盘
    bool is_play_prompt_sound    = false; //主页
    bool is_play_cash_tts_       = false; //现金
    bool is_play_online_tts      = false; //在线
    bool is_play_member_tts      = false; //会员
    bool is_play_offline_tts     = false; //离线
    bool is_play_mini_program    = false; //小程序
    bool is_refundOrder_and_print = false; //打印
    bool is_play_mini_program_refund = false; //小程序退款
    bool is_play_mini_program_take_orders = false; //小程序接单

    QString language_selected = ""; //国际化语言
    int clac_digit_num_ = 2;
    int calc_type_      = 0;

    int points_ratio_ = -1; //积分比例

    bool is_recognition_debug_ = false;

    bool is_use_float_keyboard_   = false;
    bool is_use_static_keyboard_  = false;
    bool is_use_virtual_keyboard_ = false;

    float normal_goods_profit_ratio_ = .0;

    int default_wipe_zero_type_ = (int)WipeZeroTypeEnum::WIPE_ZERO__OFF;

signals:
    void fontRatioChanged();
    void isQuickCreateNoCodeGoodsChanged();
    void isShowStockChanged();
    void isPowerOffWhenExitChanged();
    void isUseJinWeightScaleChanged();
    void isNegativeProfitHintChanged();
    void isAutoAddTagChanged();

    void isPlayKeyboardSoundChanged();
    void isPlayPromptSoundChanged();
    void isPlayCashTtsChanged();
    void isPlayOnlineTtsChanged();
    void isPlayMiniProgramTakeOrdersChanged();

    void isPlayMemberTtsChanged();
    void isPlayOfflineTTsChanged();
    void isPlayMiniProgramChanged();
    void isPlayMiniProgramRefundChanged();
    void languageSelectedChanged();

    void clacDigitNumChanged();
    void calcTypeChanged();

    void isRecognitionDebugChanged();

    void pointsRatioChanged();

    void isUseFloatKeyboardChanged();
    void isUseStaticKeyboardChanged();
    void isUseVirtualKeyboardChanged();

    void normalGoodsProfitRatioChanged();

    void defaultWipeZeroTypeChanged();

    void recognitionRatioP1Changed();
    void recognitionRatioP2Changed();
    void recognitionRatioP3Changed();
    void recognitionRatioP4Changed();

    void languageChanged();
};

#endif // CONFIGTOOL_H
