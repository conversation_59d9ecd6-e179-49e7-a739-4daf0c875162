﻿cmake_minimum_required(VERSION 3.14)

project(B<PERSON>YHOO VERSION 0.1 LANGUAGES CXX)

# if(CMAKE_CL_64)
#     set(CMAKE_PREFIX_PATH C:/Qt/5.15.2/msvc2019_64)
# else(CMAKE_CL_64)
#     set(CMAKE_PREFIX_PATH C:/Qt/5.15.2/msvc2019)
# endif(CMAKE_CL_64)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /utf-8")
set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "${CMAKE_CXX_FLAGS_RELWITHDEBINFO} /utf-8 /Od")
STRING(REPLACE "-O2" "-Od" CMAKE_CXX_FLAGS_RELWITHDEBINFO ${CMAKE_CXX_FLAGS_RELWITHDEBINFO})

set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /utf-8")
set(CMAKE_CXX_FLAGS_MINSIZEREL "${CMAKE_CXX_FLAGS_MINSIZEREL} /utf-8")

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Core LinguistTools QtGui Quick Widgets Mqtt PrintSupport Charts  TextToSpeech Sql Multimedia SerialPort)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Core LinguistTools Quick Widgets Mqtt PrintSupport Charts  TextToSpeech Sql Multimedia SerialPort)

add_definitions(-DUNICODE -D_UNICODE)

add_definitions(-DBUYHOO_DEBUG) #实时输出调试
add_definitions(-DBUYHOO_LOG) #是否输出调试
# add_definitions(-DDISABLE_TTS) #关闭TTS(不编译)
add_definitions(-DBUYHOO_PARSE_DEBUG)

include_directories(CPP)
include_directories(CPP/LogModule)
include_directories(Libraries/spdlog/include)
include_directories(Libraries/pugixml/include)
include_directories(Libraries/Json)
include_directories(Libraries/nameof/include)
include_directories(Libraries/zip_file/include)

IF(CMAKE_CL_64) # x64

add_definitions(-D_AMD64_)
####add_definitions(-D_FORCE_RECOGNITION_) #无称情况下使用识别
add_definitions(-D_RECOGNITION_)
add_definitions(-DNOMINMAX)

add_definitions(-D_RECOGNITION_DEBUG_)

include_directories(Libraries/zint-package-vs-x64/include)
link_directories(Libraries/zint-package-vs-x64/lib)

SET(CMAKE_INCLUDE_PATH ${CMAKE_INCLUDE_PATH} "Libraries/opencv_with_contrib-vs-x64/include")
SET(CMAKE_LIBRARY_PATH ${CMAKE_LIBRARY_PATH} "Libraries/opencv_with_contrib-vs-x64/x64/vc17/lib")
SET(OpenCV_DIR "Libraries/opencv_with_contrib-vs-x64/x64/vc17/lib")

FIND_PACKAGE(OpenCV REQUIRED)
include_directories(${OpenCV_INCLUDE_DIRS})

include_directories(Libraries/libtorch-vs-x64/include)
include_directories(Libraries/libtorch-vs-x64/include/torch/csrc/api/include)
link_directories(Libraries/libtorch-vs-x64/lib)

include_directories(Libraries/torchvision-vs-x64/csrc_include)
link_directories(Libraries/torchvision-vs-x64/Release_lib)

ELSE(CMAKE_CL_64) # x32

add_definitions(-D_X86_)

include_directories(Libraries/zint-package-vs-x32/include)
link_directories(Libraries/zint-package-vs-x32/lib)

SET(CMAKE_INCLUDE_PATH ${CMAKE_INCLUDE_PATH} "Libraries/opencv_with_contrib-vs-x32/include")
SET(CMAKE_LIBRARY_PATH ${CMAKE_LIBRARY_PATH} "Libraries/opencv_with_contrib-vs-x32/x86/vc17/lib")
SET(OpenCV_DIR "Libraries/opencv_with_contrib-vs-x32/x86/vc17/lib")
FIND_PACKAGE(OpenCV REQUIRED)
include_directories(${OpenCV_INCLUDE_DIRS})

ENDIF(CMAKE_CL_64)

add_subdirectory(SortFilterProxyModel)

add_definitions(-DCOMPILE_FOR_WINDOWS)
add_definitions(-DPUGIXML_HEADER_ONLY)
add_definitions(-DNAMEOF_ENUM_SUPPORTED)
add_definitions(-D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS)


set(PROJECT_SOURCES
    $<TARGET_OBJECTS:SortFilterProxyModel>
    
    CPP/AdapterModule/ImageProvider.h

    CPP/AdapterModule/Annotation.h
    CPP/AdapterModule/Annotation.cpp

    CPP/AdapterModule/BaseDraw.h
    CPP/AdapterModule/BaseDraw.cpp

    CPP/AdapterModule/EllipseDraw.h
    CPP/AdapterModule/EllipseDraw.cpp

    CPP/AdapterModule/MeasurementDraw.h
    CPP/AdapterModule/MeasurementDraw.cpp

    CPP/AdapterModule/PolygonDraw.h
    CPP/AdapterModule/PolygonDraw.cpp

    CPP/AdapterModule/RectangeDraw.h
    CPP/AdapterModule/RectangeDraw.cpp

    CPP/AdapterModule/SplineDraw.h
    CPP/AdapterModule/SplineDraw.cpp

    CPP/OfflineCheckout/synshopinfo.h
    CPP/OfflineCheckout/synshopinfo.cpp

    CPP/ConfModule/ConfigTool.h
    CPP/ControlModule/BarcodeLabelScale.h
    CPP/ControlModule/BusinessAlarmControl.h
    CPP/ControlModule/BarcodeLabelScaleStruct.h
    CPP/ControlModule/CameraControl.h
    CPP/ControlModule/ControlManager.h
    CPP/ControlModule/FileDownloadWorker.h
    CPP/ControlModule/GoodsControl.h
    CPP/ControlModule/GoodsDataModel.h
    CPP/ControlModule/LogCtrl.h
    CPP/ControlModule/MemberControl.h
    CPP/ControlModule/GoodsPromotionCtrl.h
    CPP/ControlModule/MqttControl.h
    CPP/ControlModule/NetOrderControl.h
    CPP/ControlModule/OrderControl.h
    CPP/ControlModule/PayMethodControl.h
    CPP/ControlModule/PermissionCtrl.h
    CPP/ControlModule/PrinterControl.h
    CPP/ControlModule/PrinterStruct.h
    CPP/ControlModule/ShopCartControl.h
    CPP/ControlModule/ShopCartModel.h
    CPP/ControlModule/ShopControl.h
    CPP/ControlModule/SoundCtrl.h
    CPP/ControlModule/SupplierControl.h
    CPP/ControlModule/TtsControl.h
    CPP/ControlModule/UpdateCtrl.h
    CPP/ControlModule/WeightingScaleControl.h
    CPP/ControlModule/ZintControl.h

    CPP/WorkerModule/Ping.cpp
    CPP/WorkerModule/Ping.h

    CPP/ControlModule/NetStatusCtrl.cpp
    CPP/ControlModule/NetStatusCtrl.h

    CPP/DataModule/DataManager.h
    CPP/DataModule/DataStruct.h
    CPP/DataModule/GoodsData.h
    CPP/DataModule/GoodsKindData.h
    CPP/DataModule/OrderData.h
    CPP/DataModule/VirtualGoodsKindData.h

    CPP/HelperModule/AreaDivisionHelper.h
    CPP/HelperModule/AreaDivisionHelper.cpp

    CPP/EnumTool.h
    CPP/GlobeData.h
    CPP/GlobeData.cpp
    CPP/KeyEvent/KeyEvent.h
    CPP/LogModule/LogManager.h
    CPP/LogModule/QtLog.h
    CPP/NetModule/HttpCallback.h
    CPP/NetModule/HttpClient.h
    CPP/NetModule/HttpWorker.h
    CPP/NetModule/HttpWorker2.h
    CPP/NetModule/NetGlobal.h
    CPP/Splashscreen/Splashscreen.h
    CPP/Utils/Utils.h
    CPP/Utils/Utils4Qml.h
    CPP/Version.h
    CPP/WorkerModule/CameraWorker.h
    CPP/gist/ThreadRAII.h
    CPP/json-qt.hpp
    CPP/json.hpp
    
    CPP/AdapterModule/ImageProvider.cpp

    CPP/ConfModule/ConfigTool.cpp
    CPP/ControlModule/BarcodeLabelScale.cpp
    CPP/ControlModule/BusinessAlarmControl.cpp
    CPP/ControlModule/CameraControl.cpp
    CPP/ControlModule/ControlManager.cpp
    CPP/ControlModule/FileDownloadWorker.cpp
    CPP/ControlModule/GoodsControl.cpp
    CPP/ControlModule/GoodsDataModel.cpp
    CPP/ControlModule/LogCtrl.cpp
    CPP/ControlModule/MemberControl.cpp
    CPP/ControlModule/MqttControl.cpp
    CPP/ControlModule/NetOrderControl.cpp
    CPP/ControlModule/OrderControl.cpp
    CPP/ControlModule/PayMethodControl.cpp
    CPP/ControlModule/PermissionCtrl.cpp
    CPP/ControlModule/PrinterControl.cpp
    CPP/ControlModule/PrinterStruct.cpp
    CPP/ControlModule/ShopCartControl.cpp
    CPP/ControlModule/ShopCartModel.cpp
    CPP/ControlModule/ShopControl.cpp
    CPP/ControlModule/SoundCtrl.cpp
    CPP/ControlModule/GoodsPromotionCtrl.cpp
    CPP/ControlModule/SupplierControl.cpp
    CPP/ControlModule/TtsControl.cpp
    CPP/ControlModule/UpdateCtrl.cpp
    CPP/ControlModule/WeightingScaleControl.cpp
    CPP/ControlModule/ZintControl.cpp
    CPP/DataModule/DataManager.cpp
    CPP/DataModule/GoodsData.cpp
    CPP/DataModule/GoodsKindData.cpp
    CPP/DataModule/OrderData.cpp
    CPP/DataModule/VirtualGoodsKindData.cpp
    CPP/KeyEvent/KeyEvent.cpp
    CPP/LogModule/LogManager.cpp
    CPP/LogModule/QtLog.cpp
    CPP/NetModule/HttpCallback.cpp
    CPP/NetModule/HttpWorker.cpp
    CPP/NetModule/HttpWorker2.cpp
    CPP/NetModule/NetGlobal.cpp
    CPP/Splashscreen/Splashscreen.cpp
    CPP/Utils/Utils.cpp
    CPP/Utils/Utils4Qml.cpp
    CPP/WorkerModule/CameraWorker.cpp

    CPP/main.cpp
    
    CPP/WorkerModule/CameraWorker2.h
    CPP/WorkerModule/CameraWorker2.cpp
    CPP/WorkerModule/FaceRecognizer.h
    CPP/WorkerModule/FaceRecognizer.cpp
    CPP/WorkerModule/LocalDataWorker.h
    CPP/WorkerModule/LocalDataWorker.cpp

    CPP/WorkerModule/NetStatusWorker.h
    CPP/WorkerModule/NetStatusWorker.cpp

    CPP/WorkerModule/PrinterWorker.h
    CPP/WorkerModule/PrinterWorker.cpp

    CPP/ModelModule/TagEditHelper.h
    CPP/ModelModule/TagEditHelper.cpp
    
    CPP/ConfModule/ConfigEnum.h

    CPP/Utils/Utils2.h
    CPP/Utils/Utils2.cpp

    CPP/CheckDataModule/CheckData.cpp
    CPP/CheckDataModule/CheckData.h

    qml.qrc
    image.qrc
    keyboard.qrc
    sound.qrc
    font.qrc
    Languages.qrc
    zx.rc
)

set(TS_DIR "${CMAKE_SOURCE_DIR}/translations")

file(GLOB_RECURSE QML_FILES ${CMAKE_SOURCE_DIR}/QML/*.qml)
set(TS_FILES
        ${CMAKE_SOURCE_DIR}/translations/lang_Chinese.ts
        ${CMAKE_SOURCE_DIR}/translations/lang_English.ts
        ${CMAKE_SOURCE_DIR}/translations/lang_Thailand.ts
        ${CMAKE_SOURCE_DIR}/translations/lang_Russia.ts
        )

find_package(Qt5LinguistTools)
foreach(_ts_file ${TS_FILES})
    if(NOT EXISTS ${_ts_file})
        qt5_create_translation(QM_FILES ${QML_FILES} ${TS_FILES} OPTIONS -source-language en_US -no-obsolete)
        message(STATUS "==Creating empty translation file: ${_ts_file}")
    else()
        message(STATUS "==Not empty translation file: ${_ts_file}")
    endif()
endforeach()
if(CMAKE_CL_64)
    message("CMAKE_CL_64")
    set(PROJECT_SOURCES_64
        CPP/WorkerModule/RecognitionWorker.h
        CPP/WorkerModule/RecognitionWorker.cpp
        CPP/ControlModule/RecognitionCtrl.h
        CPP/ControlModule/RecognitionCtrl.cpp

    )
else(CMAKE_CL_64)
    set(PROJECT_SOURCES_64)
endif(CMAKE_CL_64)


if (${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_executable(${PROJECT_NAME}
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}
        ${PROJECT_SOURCES_64}
        ${QML_FILES}
        ${QM_FILES}
    )

else ()
    add_executable(${PROJECT_NAME}
        ${PROJECT_SOURCES}
        ${PROJECT_SOURCES_64}
        ${QML_FILES}
        ${QM_FILES}
    )
endif ()


file(MAKE_DIRECTORY ${TS_DIR})
find_program(LUPDATE_EXECUTABLE lupdate)
find_program(LRELEASE_EXECUTABLE lrelease)


foreach(_ts_file ${TS_FILES})
    if(NOT EXISTS ${_ts_file})
        message(STATUS "Creating empty translation file: ${_ts_file}")
    endif()
    execute_process(
        COMMAND ${LRELEASE_EXECUTABLE} ${_ts_file})
endforeach()

if(CMAKE_CL_64)
    set(64_LIB
        torchvision
        asmjit
        c10
        clog
        cpuinfo
        dnnl
        fbgemm
        fbjni
        fmt
        kineto
        libprotobuf-lite
        libprotobuf
        libprotoc
        pthreadpool
        pytorch_jni
        torch_cpu
        torch
        XNNPACK
    )
else(CMAKE_CL_64)
    set(64_LIB)
endif(CMAKE_CL_64)

target_link_libraries(${PROJECT_NAME}
    PRIVATE Qt${QT_VERSION_MAJOR}::Core Qt${QT_VERSION_MAJOR}::Quick
    Qt${QT_VERSION_MAJOR}::Widgets Qt${QT_VERSION_MAJOR}::Mqtt
    Qt${QT_VERSION_MAJOR}::PrintSupport
    Qt${QT_VERSION_MAJOR}::TextToSpeech
    Qt${QT_VERSION_MAJOR}::Sql
    Qt${QT_VERSION_MAJOR}::Multimedia
    Qt${QT_VERSION_MAJOR}::SerialPort
    Qt${QT_VERSION_MAJOR}::Charts
    ${OpenCV_LIBS}
    ${64_LIB}
    QZint
    zint
    wsock32
    ws2_32
    Qt5::Core
    Qt5::Gui
)
set_target_properties(${PROJECT_NAME} PROPERTIES
    MACOSX_BUNDLE_GUI_IDENTIFIER my.example.com
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
    CXX_ISO_COMPLIANT ON
    C_ISO_COMPLIANT ON
)


install(TARGETS ${PROJECT_NAME}
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR})

if (QT_VERSION_MAJOR EQUAL 6)
    qt_import_qml_plugins(${PROJECT_NAME})
    qt_finalize_executable(${PROJECT_NAME})
endif ()
