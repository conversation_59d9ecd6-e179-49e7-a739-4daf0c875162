﻿IDI_ICON1	ICON	DISCARDABLE	"zx.ico"
#include <winver.h>
#include "Cpp/Version.h"

VS_VERSION_INFO VERSIONINFO

    FILEVERSION FILE_VERSION

    PRODUCTVERSION PRODUCT_VERSION

    FILEFLAGSMASK 0x3fL

#ifdef _DEBUG

    FILEFLAGS VS_FF_DEBUG

#else

    FILEFLAGS 0x0L

#endif

    FILEOS VOS__WINDOWS32

    FILETYPE VFT_DLL

    FILESUBTYPE 0x0L

    BEGIN

        BLOCK "StringFileInfo"

        BEGIN

            BLOCK "080404b0"

            BEGIN

                VALUE "CompanyName", COMPANY_NAME

                VALUE "FileDescription", FILE_DESCRIPTION

                VALUE "FileVersion", FILE_VERSION_STR

                VALUE "InternalName", INTERNAL_NAME

                VALUE "LegalCopyright", LEGAL_COPYRIGHT

                VALUE "OriginalFilename", ORIGINAL_FILE_NAME

                VALUE "ProductName", PRODUCT_NAME

                VALUE "ProductVersion", PRODUCT_VERSION_STR

            END

        END

        BLOCK "VarFileInfo"

        BEGIN

            VALUE "Translation", 0x804, 1200

        END

    END

